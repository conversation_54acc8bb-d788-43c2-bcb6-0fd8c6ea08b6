<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.aliyun.wormhole</groupId>
    <artifactId>qanat-aliyun-inc-com</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <name>qanat-aliyun-inc-com</name>

    <properties>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.source>1.8</maven.compiler.source>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <mockito-all.version>1.10.19</mockito-all.version>
        <spring-boot.version>1.5.20.RELEASE</spring-boot.version>
        <maven-antrun.version>1.8</maven-antrun.version>
        <pandora-boot.version>2020-04-release</pandora-boot.version>
        <pandora-boot-maven-plugin.version>2.1.14</pandora-boot-maven-plugin.version>
        <autoconfig-maven-plugin.version>1.2-fixcompress-SNAPSHOT</autoconfig-maven-plugin.version>
        <security.version>1.0.11</security.version>
        <velocity.starter.version>1.0.4.RELEASE</velocity.starter.version>
        <schedulerx2.starter.version>1.1.4</schedulerx2.starter.version>
    </properties>

    <modules>
        <module>qanat-aliyun-inc-com-service</module>
        <module>qanat-aliyun-inc-com-start</module>
        <module>qanat-service-api</module>
        <module>qanat-service-dao</module>
    	<module>qanat-openapi</module>
  </modules>

    <dependencyManagement>
        <dependencies>
	        <dependency>
	            <groupId>com.alibaba</groupId>
	            <artifactId>fastjson</artifactId>
	            <version>1.2.82</version>
	        </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.taobao.pandora</groupId>
                <artifactId>pandora-boot-starter-bom</artifactId>
                <version>${pandora-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.aliyun.wormhole</groupId>
                <artifactId>qanat-aliyun-inc-com-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.security</groupId>
                <artifactId>security-spring-boot-starter</artifactId>
                <version>${security.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-config</artifactId>
                <version>4.2.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-web</artifactId>
                <version>4.2.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring</artifactId>
                <version>999-not-exists-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>servlet-api</artifactId>
                <version>3.0</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>security-extras</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>velocity-spring-boot-starter</artifactId>
                <version>${velocity.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-all</artifactId>
                <version>${mockito-all.version}</version>
                <scope>test</scope>
            </dependency>
	        <!-- lombok -->
		    <dependency>
		        <groupId>org.projectlombok</groupId>
		        <artifactId>lombok</artifactId>
		        <version>1.18.8</version>
		    </dependency>
            <!-- schedulerx2 -->
	        <dependency>
			  <groupId>com.alibaba.schedulerx</groupId>
			  <artifactId>schedulerx2-spring-boot-starter</artifactId>
			  <version>${schedulerx2.starter.version}</version>
			</dependency>
	        <dependency>
	            <groupId>com.aliyun.odps</groupId>
	            <artifactId>odps-sdk-core-internal</artifactId>
	            <version>0.38.3</version>
	            <exclusions>
	                <exclusion>
	                    <groupId>org.codehaus.jackson</groupId>
	                    <artifactId>jackson-mapper-asl</artifactId>
	                </exclusion>
	                <exclusion>
	                    <groupId>org.aspectj</groupId>
	                    <artifactId>aspectjrt</artifactId>
	                </exclusion>
	                <exclusion>
	                    <groupId>com.alibaba</groupId>
	                    <artifactId>fastjson</artifactId>
	                </exclusion>
	            </exclusions>
	        </dependency>
	        <dependency>
	            <groupId>com.alibaba.boot</groupId>
	            <artifactId>notify-spring-boot-starter</artifactId>
	            <version>1.1.0</version>
	            <exclusions>
	                <exclusion>
	                    <groupId>com.taobao.eagleeye</groupId>
	                    <artifactId>eagleeye-core</artifactId>
	                </exclusion>
	                <exclusion>
	                    <groupId>com.alibaba.unit.rule</groupId>
	                    <artifactId>unitrouter</artifactId>
	                </exclusion>
	            </exclusions>
	        </dependency>
	        <dependency>
	            <groupId>com.aliyun.oss</groupId>
	            <artifactId>aliyun-sdk-oss</artifactId>
	            <version>2.8.2</version>
	        </dependency>
	        <dependency>
	            <groupId>com.alibaba.work.alipmc</groupId>
	            <artifactId>alipmc-api</artifactId>
	            <version>3.0.7</version>
	        </dependency>
	        <dependency>
	            <groupId>com.alibaba.fastsql</groupId>
	            <artifactId>fastsql</artifactId>
	            <version>2.0.0_preview_910</version>
	        </dependency>
	        <dependency>
	            <groupId>com.taobao.drc</groupId>
	            <artifactId>client</artifactId>
	            <version>********</version>
	        </dependency>
	        <dependency>
	            <groupId>com.alibaba.base</groupId>
	            <artifactId>base-meta-client</artifactId>
	            <version>2.0.0-SNAPSHOT</version>
	            <exclusions>
	                <exclusion>
	                    <groupId>com.alibaba</groupId>
	                    <artifactId>fastjson</artifactId>
	                </exclusion>
	                <exclusion>
	                    <groupId>com.taobao.eagleeye</groupId>
	                    <artifactId>eagleeye-core</artifactId>
	                </exclusion>
	                <exclusion>
	                    <groupId>javax.servlet</groupId>
	                    <artifactId>javax.servlet-api</artifactId>
	                </exclusion>
	                <exclusion>
	                    <groupId>org.springframework</groupId>
	                    <artifactId>spring-aop</artifactId>
	                </exclusion>
	            </exclusions>
	        </dependency>
	        <!-- Bayes SDK -->
	        <dependency>
	            <groupId>com.aliyun</groupId>
	            <artifactId>aliyun-java-sdk-core</artifactId>
	            <version>4.5.6</version>
	        </dependency>
	        <dependency>
	            <groupId>com.aliyun</groupId>
	            <artifactId>aliyun-java-sdk-foas</artifactId>
	            <version>2.8.0</version>
	        </dependency>
	        <dependency>
	            <groupId>com.aliexpress.boot</groupId>
	            <artifactId>spring-boot-starter-keycenter</artifactId>
	            <version>1.0.0-SNAPSHOT</version>
	            <exclusions>
	                <exclusion>
	                    <groupId>com.alibaba</groupId>
	                    <artifactId>fastjson</artifactId>
	                </exclusion>
	            </exclusions>
	        </dependency>
	        <dependency>
			  <groupId>com.alibaba.boot</groupId>
			  <artifactId>pandora-eagleeye-spring-boot-starter</artifactId>
			  <version>2020-04-release</version>
			</dependency>
            <dependency>
                <groupId>com.taobao.ateye</groupId>
                <artifactId>ateye-client</artifactId>
                <version>2.2.11</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.csp</groupId>
                <artifactId>sentinel</artifactId>
                <version>2.16.0</version>
            </dependency>
	        <dependency>
			    <groupId>org.postgresql</groupId>
			    <artifactId>postgresql</artifactId>
			    <version>42.2.18</version>
			</dependency>
			<dependency>
			   <groupId>com.aliyun</groupId>
			   <artifactId>aliyun-java-sdk-ververica</artifactId>
			   <version>1.0.8</version>
			</dependency>
			 <dependency>
			   <groupId>com.aliyun</groupId>
			   <artifactId>ververica-common</artifactId>
			   <version>1.0.25</version>
			 </dependency>
			 <dependency>
			  <groupId>com.aliyun.devata.customer</groupId>
			  <artifactId>customer-pool-shared</artifactId>
			  <version>1.0.14</version>
			</dependency>
			<dependency>
			  <groupId>com.aliyun.dataworks</groupId>
			  <artifactId>aliyun-java-sdk-dataworks</artifactId>
			  <version>1.1.6</version>
			</dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>3.10.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-pool2</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.11.1</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <version>999-not-exist-v3</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
            <version>999-not-exist-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.platform.shared</groupId>
            <artifactId>fasttext.all</artifactId>
            <version>1.4-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.external</groupId>
                    <artifactId>jakarta.commons.lang</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.external</groupId>
                    <artifactId>xml.xmlgraphics__batik-css-1.7.jar</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.external</groupId>
                    <artifactId>jakarta.commons.logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.external</groupId>
                    <artifactId>jakarta.commons.collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.external</groupId>
                    <artifactId>xml.apis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- ElasticSearch -->
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>6.3.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>elasticsearch</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>6.3.2</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.work.alipmc</groupId>
            <artifactId>alipmc-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.taobao.security</groupId>
                    <artifactId>security</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-acl-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-buc-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
		  <groupId>com.alibaba.platform.shared</groupId>
		  <artifactId>buc.acl.api</artifactId>
		  <version>1.1.9</version>
		</dependency>
            <dependency>
                <groupId>com.aliexpress.boot</groupId>
                <artifactId>spring-boot-starter-keycenter</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
			    <groupId>com.alibaba.boot</groupId>
			    <artifactId>pandora-eagleeye-spring-boot-starter</artifactId>
			</dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>${maven-antrun.version}</version>
                </plugin>
                <plugin>
                    <groupId>com.taobao.pandora</groupId>
                    <artifactId>pandora-boot-maven-plugin</artifactId>
                    <version>${pandora-boot-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>com.alibaba.citrus.tool</groupId>
                    <artifactId>autoconfig-maven-plugin</artifactId>
                    <version>${autoconfig-maven-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>http://repo.alibaba-inc.com/mvn/releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://repo.alibaba-inc.com/mvn/snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>