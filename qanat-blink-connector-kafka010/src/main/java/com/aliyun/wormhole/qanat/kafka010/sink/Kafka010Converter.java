/*
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 * 	http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.aliyun.wormhole.qanat.kafka010.sink;

import com.alibaba.blink.streaming.connectors.common.custom.TableBaseInfo;
import com.alibaba.blink.streaming.connectors.kafkabase.KafkaConverter;
import com.alibaba.blink.table.connectors.conf.BlinkOptions;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.taobao.eagleeye.EagleEye;

import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.table.util.TableProperties;
import org.apache.flink.types.Row;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;

public class Kafka010Converter extends TableBaseInfo implements KafkaConverter<Row> {
	
	private final static Logger log = LoggerFactory.getLogger(Kafka010Converter.class);
	
	private TableProperties properties;
	private String fieldDelimiter;
	
	public Kafka010Converter(TableProperties properties) {
		this.properties = properties;
	}

	@Override
	public ProducerRecord convert(Row row, String topic, int[] partitions) {
		log.info("row={}", JSON.toJSONString(rowTypeInfo));
		try {
			byte[] key = null;
			byte[] value = null;
			if (null != primaryKeys && primaryKeys.size() != 0) {
				key = row.getField(rowTypeInfo.getFieldIndex(primaryKeys.get(0))).toString().getBytes();
			}
			List<String> values = new ArrayList<>();
			for (String field : rowTypeInfo.getFieldNames()) {
				if ("__traceId__".equalsIgnoreCase(field) 
						|| (null != primaryKeys && primaryKeys.size() != 0 && primaryKeys.get(0).equalsIgnoreCase(field))) {
					continue;
				}
				values.add(row.getField(rowTypeInfo.getFieldIndex(field)).toString());
			}
			JSONObject json = new JSONObject();
			json.put("data", values.size() == 1 ? values.get(0) : StringUtils.join(values, fieldDelimiter));
			json.put("ts", System.currentTimeMillis());
			if (rowTypeInfo.hasField("__traceId__")) {
				json.put("traceId", row.getField(rowTypeInfo.getFieldIndex("__traceId__")).toString());
			} else {
				try {
					json.put("traceId", EagleEye.getTraceId() != null ? EagleEye.getTraceId() : EagleEye.generateTraceId(InetAddress.getLocalHost().getHostAddress()));
				} catch (UnknownHostException e) {}
			}
			log.info("key={},value={}", key, json.toJSONString());
			value = json.toJSONString().getBytes();
			return new ProducerRecord(topic, key, value);
		} catch(Exception e) {
			log.error("convert failed, error={}", e.getMessage(), e);
		}
		return null;
	}

	@Override
	public void open(RuntimeContext context) {
		fieldDelimiter = properties.getString(BlinkOptions.FIELD_DELIMITER);
	}

	@Override
	public void close() {

	}
}
