/*
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 * 	http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.aliyun.wormhole.qanat.kafka010;

import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.streaming.connectors.kafka.config.StartupMode;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.dataformat.GenericRow;
import org.apache.flink.table.factories.BatchCompatibleTableSinkFactory;
import org.apache.flink.table.factories.BatchTableSourceFactory;
import org.apache.flink.table.factories.StreamTableSinkFactory;
import org.apache.flink.table.factories.StreamTableSourceFactory;
import org.apache.flink.table.sinks.BatchCompatibleStreamTableSink;
import org.apache.flink.table.sinks.StreamTableSink;
import org.apache.flink.table.sources.BatchTableSource;
import org.apache.flink.table.sources.StreamTableSource;
import org.apache.flink.table.types.TypeConverters;
import org.apache.flink.table.util.TableProperties;
import org.apache.flink.types.Row;
import org.apache.flink.util.StringUtils;

import com.alibaba.blink.streaming.connectors.common.custom.TableBaseInfo;
import com.alibaba.blink.streaming.connectors.common.errcode.ConnectorErrors;
import com.alibaba.blink.streaming.connectors.common.exception.ErrorUtils;
import com.alibaba.blink.streaming.connectors.common.exception.InvalidParamException;
import com.alibaba.blink.streaming.connectors.common.util.BlinkStringUtil;
import com.alibaba.blink.streaming.connectors.common.util.DateUtil;
import com.alibaba.blink.streaming.connectors.kafkabase.KafkaConverter;
import com.alibaba.blink.table.connectors.conf.BlinkOptions;
import com.alibaba.blink.table.factories.BlinkTableFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.aliyun.wormhole.qanat.kafka010.input.Kafka010TableSource;
import com.aliyun.wormhole.qanat.kafka010.sink.Kafka010Converter;
import com.aliyun.wormhole.qanat.kafka010.sink.Kafka010OutputFormat;
import com.aliyun.wormhole.qanat.kafka10.sink.Kafka010TableSink;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import static com.alibaba.blink.streaming.connectors.common.source.SourceUtils.toRowTypeInfo;
import static org.apache.flink.configuration.ConfigOptions.key;
import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_PROPERTY_VERSION;
import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_TYPE;

public class Kafka010TableFactory extends BlinkTableFactory implements
		StreamTableSourceFactory<GenericRow>,
		StreamTableSinkFactory<Tuple2<Boolean, Row>>,
		BatchTableSourceFactory<GenericRow>,
		BatchCompatibleTableSinkFactory<Tuple2<Boolean, Row>> {
	
    public static final ConfigOption<String> DBNAME = key("dbName".toLowerCase()).noDefaultValue();

	private Kafka010TableSource createSource(Map<String, String> props) {
		TableProperties properties = new TableProperties();
		properties.putProperties(props);
		RichTableSchema schema = properties.readSchemaFromProperties(classLoader);
		String topicStr = properties.getString(BlinkOptions.KAFKA08.TOPIC);
		String topicPatternStr = properties.getString(BlinkOptions.KAFKA010.TOPIC_PATTERN);
		String dbName = properties.getString(DBNAME);
		if (BlinkStringUtil.isNotEmpty(dbName)) {
            String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
            JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
            if (dbMetaJson != null && BlinkStringUtil.isNotEmpty(dbMetaJson.getString("bootstrap.servers"))) {
            	properties.setString("bootstrap.servers", dbMetaJson.getString("bootstrap.servers"));
            }
		}
		Properties prop = getProperties(BlinkOptions.KAFKA010.ESSENTIAL_CONSUMER_KEYS,
										BlinkOptions.KAFKA010.OPTIONAL_CONSUMER_KEYS,
										properties);
		Long startTimeMs = properties.getLong(BlinkOptions.START_TIME_MILLS);
		String startDateTime = properties.getString(BlinkOptions.KAFKA.OPTIONAL_START_TIME);
		String timeZone = properties.getString(BlinkOptions.TIME_ZONE);
		long startInMs = startTimeMs;
		if (startInMs == -1 ) {
			if (StringUtils.isNullOrWhitespaceOnly(startDateTime)) {
				SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				startDateTime = formatter.format(new Date());
			}
			try {
				startInMs = DateUtil.parseDateString(BlinkOptions.DATE_FORMAT, startDateTime, timeZone);
			} catch (ParseException e) {
				throw new InvalidParamException(String.format(
						"Incorrect datetime format: %s, pls use ISO-8601 " +
						"complete date plus hours, minutes and seconds format:%s",
						startDateTime,
						BlinkOptions.DATE_FORMAT), e);
			}
		}

		String blinkEnvironmentType = properties.getString(BlinkOptions.BLINK_ENVIRONMENT_TYPE_KEY);
		boolean isBatchMode = BlinkOptions.BLINK_ENVIRONMENT_BATCH_VALUE
				.equalsIgnoreCase(blinkEnvironmentType) ? true : false;
		if (!StringUtils.isNullOrWhitespaceOnly(topicStr)) {
			List<String> topics = Arrays.asList(topicStr.split(","));
			return new Kafka010TableSource(topics, null, prop, getStartupMode(properties), startInMs, isBatchMode,
					TypeConverters.toBaseRowTypeInfo(schema.getResultType()), schema, properties);
		} else if (!StringUtils.isNullOrWhitespaceOnly(topicPatternStr)) {
			return new Kafka010TableSource(null, topicPatternStr, prop, getStartupMode(properties), startInMs, isBatchMode,
					TypeConverters.toBaseRowTypeInfo(schema.getResultType()), schema, properties);
		} else {
			throw ErrorUtils.getException(ConnectorErrors.INST.unSufficientArguments("Kafka10"));
		}
	}

	private Kafka010TableSink createSink(Map<String, String> props) {
		TableProperties properties = new TableProperties();
		properties.putProperties(props);
		RichTableSchema schema = properties.readSchemaFromProperties(classLoader);
		String topic = properties.getString(BlinkOptions.KAFKA.TOPIC);
		String dbName = properties.getString(DBNAME);
		if (BlinkStringUtil.isNotEmpty(dbName)) {
            String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
            JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
            if (dbMetaJson != null && BlinkStringUtil.isNotEmpty(dbMetaJson.getString("bootstrap.servers"))) {
            	properties.setString("bootstrap.servers", dbMetaJson.getString("bootstrap.servers"));
            }
		}
		Properties prop = getProperties(
				BlinkOptions.KAFKA010.ESSENTIAL_PRODUCER_KEYS,
				BlinkOptions.KAFKA010.OPTIONAL_PRODUCER_KEYS,
				properties);
		KafkaConverter kafkaConverter;
		String convertClassStr = properties.getString(BlinkOptions.KAFKA.OPTIONAL_CONVERTER_CLASS);
		if (null != convertClassStr && !convertClassStr.isEmpty()) {
			try {
				Class converterClass = classLoader.loadClass(convertClassStr);
				kafkaConverter = (KafkaConverter) converterClass.newInstance();
			} catch (Exception e) {
				throw ErrorUtils.getException("", e);
			}
		} else {
			kafkaConverter = new Kafka010Converter(properties);
		}
		if (kafkaConverter instanceof TableBaseInfo) {
			TableBaseInfo tableBaseInfo = (TableBaseInfo) kafkaConverter;
			tableBaseInfo.setHeaderFields(schema.getHeaderFields())
						.setRowTypeInfo(toRowTypeInfo(schema.getResultRowType()))
						.setPrimaryKeys(schema.getPrimaryKeys())
						.setUserParamsMap(properties.toMap());
		}
		Kafka010OutputFormat.Builder builder = new Kafka010OutputFormat.Builder();
		builder.setKafkaConverter(kafkaConverter)
			.setProperties(prop)
			.setTopic(topic)
			.setRowTypeInfo(toRowTypeInfo(schema.getResultRowType()));
		return new Kafka010TableSink(builder, schema);
	}

	@Override
	protected List<String> supportedSpecificProperties() {
		return mergeProperties(BlinkOptions.KAFKA010.ESSENTIAL_CONSUMER_KEYS,
				BlinkOptions.KAFKA010.ESSENTIAL_PRODUCER_KEYS,
				BlinkOptions.KAFKA010.OPTIONAL_CONSUMER_KEYS,
				BlinkOptions.KAFKA010.OPTIONAL_PRODUCER_KEYS,
				BlinkOptions.KAFKA.SUPPORTED_KEYS);
	}

	@Override
	protected Map<String, String> requiredContextSpecific() {
		Map<String, String> context = new HashMap<>();
		context.put(CONNECTOR_TYPE, "QANAT_KAFKA010"); // KAFKA010
		context.put(CONNECTOR_PROPERTY_VERSION, "1"); // backwards compatibility
		return context;
	}

	@Override
	public BatchCompatibleStreamTableSink<Tuple2<Boolean, Row>> createBatchCompatibleTableSink(Map<String, String> properties) {
		return createSink(properties);
	}

	@Override
	public BatchTableSource<GenericRow> createBatchTableSource(Map<String, String> properties) {
		return createSource(properties);
	}

	@Override
	public StreamTableSink<Tuple2<Boolean, Row>> createStreamTableSink(Map<String, String> properties) {
		return createSink(properties);
	}

	@Override
	public StreamTableSource<GenericRow> createStreamTableSource(Map<String, String> properties) {
		return createSource(properties);
	}

	protected Properties getProperties(
			Set<String> essentialKeys,
			Set<String> optionalKeys,
			TableProperties properties) {
		Properties prop = new Properties();

		Iterator<String> iterator = essentialKeys.iterator();
		while (iterator.hasNext()) {
			String key = iterator.next();
			if (!properties.containsKey(key)) {
				throw ErrorUtils.getException(ConnectorErrors.INST.unSufficientArguments("Kafka"));
			} else {
				prop.put(key, properties.getString(key, null));
			}
		}
		Iterator<String> iterator1 = optionalKeys.iterator();
		while (iterator1.hasNext()) {
			String key = iterator1.next();
			if (properties.containsKey(key)) {
				prop.put(key, properties.getString(key, null));
			}
		}
		String extraConfig = properties.getString(BlinkOptions.KAFKA.EXTRA_CONFIG);
		if (!StringUtils.isNullOrWhitespaceOnly(extraConfig)) {
			String[] configs = extraConfig.split(";");
			for (String config : configs) {
				String[] kv = config.split("=");
				if (null != kv && kv.length == 2) {
					prop.put(kv[0], kv[1]);
				}
			}
		}
		return prop;
	}

	protected StartupMode getStartupMode(TableProperties properties) {
		String blinkEnvironmentType = properties.getString(BlinkOptions.BLINK_ENVIRONMENT_TYPE_KEY);
		boolean isBatchMode = BlinkOptions.BLINK_ENVIRONMENT_BATCH_VALUE
				.equalsIgnoreCase(blinkEnvironmentType) ? true : false;

		StartupMode startupMode = isBatchMode ? StartupMode.EARLIEST : StartupMode.GROUP_OFFSETS;
		String startupModeStr = properties.getString(BlinkOptions.KAFKA.STARTUP_MODE);
		if (!StringUtils.isNullOrWhitespaceOnly(startupModeStr)) {
			if (startupModeStr.equalsIgnoreCase("EARLIEST")) {
				startupMode = StartupMode.EARLIEST;
			} else if (startupModeStr.equalsIgnoreCase("GROUP_OFFSETS")) {
				startupMode = StartupMode.GROUP_OFFSETS;
			} else if (startupModeStr.equalsIgnoreCase("LATEST")) {
				startupMode = StartupMode.LATEST;
			} else if (startupModeStr.equalsIgnoreCase("SPECIFIC_OFFSETS")) {
				startupMode = StartupMode.SPECIFIC_OFFSETS;
			} else if (startupModeStr.equalsIgnoreCase("TIMESTAMP")) {
				startupMode = StartupMode.TIMESTAMP;
			}
		}
		return startupMode;
	}
}
