/*
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 * 	http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.aliyun.wormhole.qanat.kafka010.input;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.util.serialization.KeyedDeserializationSchema;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.table.dataformat.GenericRow;
import org.apache.flink.table.typeutils.BaseRowTypeInfo;
import org.apache.flink.table.util.TableProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.blink.streaming.connector.hbase.utils.ByteSerializer;
import com.alibaba.blink.table.connectors.conf.BlinkOptions;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.RateLimiter;
import com.taobao.diamond.client.impl.DiamondUnitSite;
import com.taobao.diamond.manager.ManagerListener;
import com.taobao.eagleeye.EagleEye;
import com.alibaba.blink.streaming.connectors.common.errcode.ConnectorErrors;
import com.alibaba.blink.streaming.connectors.common.util.DateUtil;

import java.io.IOException;
import java.net.InetAddress;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

public class Kafka010MessageDeserialization implements KeyedDeserializationSchema<BaseRow> {

    private final static Logger log = LoggerFactory.getLogger(Kafka010MessageDeserialization.class);
    		
	private BaseRowTypeInfo baseRowTypeInfo;
	private RichTableSchema schema;
	private List<String> headerFields;
	private String fieldDelimiter;
	private String consumerId;
	
	public static Map<String, RateLimiter> limiterMap = null;
	public static Map<String, JSONObject> limiterFilterMap = null;

	public Kafka010MessageDeserialization(BaseRowTypeInfo baseRowTypeInfo, RichTableSchema schema, TableProperties tableProperties, String consumerId) {
		this.baseRowTypeInfo = baseRowTypeInfo;
		this.schema = schema;
		headerFields = schema.getHeaderFields();
		log.info("headerFields={}", JSON.toJSONString(headerFields));
		log.info("baseRowTypeInfo.getFieldNames()={}", JSON.toJSONString(baseRowTypeInfo.getFieldNames()));
		fieldDelimiter = tableProperties.getString(BlinkOptions.FIELD_DELIMITER);
		log.info("fieldDelimiter={}", fieldDelimiter);
		this.consumerId = consumerId;
	}

	@Override
	public GenericRow deserialize(byte[] messageKey, byte[] messageBody, String topic, int partition, long offset) throws IOException {
		log.info("deserialize({},{},{},{},{})", new String(messageKey, "UTF-8"), new String(messageBody, "UTF-8"), topic, partition, offset);
		GenericRow row = new GenericRow(this.baseRowTypeInfo.getTotalFields());
		try {
			long startTs = System.currentTimeMillis();
			if (limiterMap == null) {
				try {
					DiamondUnitSite.getDiamondUnitEnv("pre").addListeners(this.consumerId + "-flow", "DATATUBE-FLOW", Arrays.asList(new ManagerListener() {
						@Override
						public void receiveConfigInfo(String conf) {
							try {
								initLimitConf(conf);
							} catch (Exception e) {}
						}
						@Override
						public Executor getExecutor() {
							return null;
						}
					}));
					String limitConf = DiamondUnitSite.getDiamondUnitEnv("pre").getConfig(this.consumerId + "-flow", "DATATUBE-FLOW", 100000);
					log.info("limitConf={}", limitConf);
					initLimitConf(limitConf);
					log.info("init limitConf use:{}ms", System.currentTimeMillis() - startTs);
				} catch(Exception e) {
					log.error("get limit conf from diamond faild, error={}", e.getMessage(), e);
				}
			}
			
			String line = new String(messageBody, "UTF-8");
			log.info("line={}", line);
			JSONObject json = JSON.parseObject(line);
			String[] datas = json.getString("data").split(fieldDelimiter);
			String eagleEyeTraceId = json.getString("traceId");
			Long storeTs = json.getLong("ts");
			log.info("datas={}", JSON.toJSONString(datas));
			for (int idx = 0; idx < baseRowTypeInfo.getFieldNames().length; idx++) {
				if (headerFields.contains(baseRowTypeInfo.getFieldNames()[idx])) {
					if ("__traceId__".equalsIgnoreCase(schema.getColumnNames()[idx])) {
						row.update(idx, StringUtils.isNotBlank(eagleEyeTraceId) ? eagleEyeTraceId : (EagleEye.getTraceId() != null ? EagleEye.getTraceId() : EagleEye.generateTraceId(InetAddress.getLocalHost().getHostAddress())));
					} else if ("__key__".equalsIgnoreCase(baseRowTypeInfo.getFieldNames()[idx])) {
						row.update(idx, new String(messageKey, "UTF-8"));
					} else if ("__ts__".equalsIgnoreCase(baseRowTypeInfo.getFieldNames()[idx])) {
						row.update(idx, storeTs != null ? storeTs : System.currentTimeMillis());
					} else if ("__offset__".equalsIgnoreCase(baseRowTypeInfo.getFieldNames()[idx])) {
						row.update(idx, offset);
					} else if ("__partition__".equalsIgnoreCase(baseRowTypeInfo.getFieldNames()[idx])) {
						row.update(idx, partition);
					}
				} else {
					String data = datas[idx];
					Object value = null;
					ByteSerializer.ValueType colType = ByteSerializer.getTypeIndex(this.baseRowTypeInfo.getTypeAt(idx).getTypeClass());
					switch (colType) {
					case V_String:
						value = data;
						break;
					case V_Byte:
						value = messageBody;
						break;
					case V_ByteArray: // byte[]
						value = data.getBytes();
						break;
					case V_Short:
					case V_Integer:
						value = Integer.parseInt(data);
						break;
					case V_Long:
					case V_BigInteger:
						value = Long.parseLong(data);
						break;
					case V_BigDecimal:
					case V_Float:
					case V_Double:
						value = Double.parseDouble(data);
						break;
					case V_Boolean:
						value = Boolean.parseBoolean(data) == true ? '1' : '0';
						break;
					case V_Timestamp:
						value = DateUtil.timeStamp2String(Timestamp.valueOf(data), null, false);
						break;
					case V_Date:
						value = DateUtil.date2String(java.sql.Date.valueOf(data), null);
						break;
					case V_Time:
						value =  DateUtil.time2String(Time.valueOf(data), null);
						break;
					default:
						throw new IllegalArgumentException(ConnectorErrors.INST.dataTypeError(data));
					}
					try {
						JSONObject dataJson = new JSONObject();
						try{
							dataJson = JSON.parseObject(data);
						} catch(Exception e) {}
						while (true) {
							if (getLimiter(dataJson, new String(messageKey, "UTF-8")) == null) {
								log.info("topic[{}] messageKey:{} 不需限流", topic, new String(messageKey, "UTF-8"));
								break;
							}
							if (getLimiter(dataJson, new String(messageKey, "UTF-8")).tryAcquire(500, TimeUnit.MILLISECONDS)) {
								log.info("topic[{}] messageKey:{} 未限流", topic, new String(messageKey, "UTF-8"));
								break;
							} else {
								log.info("topic[{}] messageKey:{} 触发限流", topic, new String(messageKey, "UTF-8"));
								try {
				                    Thread.sleep(500);
				                } catch (InterruptedException ignore) {}
							}
						}
					} catch(Exception e) {
						log.error("topic[{}] messageKey:{} 不需限流", topic, new String(messageKey, "UTF-8"));
					}
					row.update(idx, value);
				}
			}
			return row;
		} catch (Exception ex) {
			log.error("topic[{}] messageKey:{} 处理异常：{}", topic, new String(messageKey, "UTF-8"), ex.getMessage());
		}
		return null;
	}

	private synchronized static void initLimitConf(String limitConf) {
		JSONObject limitConfJson = JSON.parseObject(limitConf);
		if (limitConfJson != null && limitConfJson.getJSONArray("rules") != null && limitConfJson.getJSONArray("rules").size() > 0) {
			JSONArray rulesJsonArray = limitConfJson.getJSONArray("rules");
			limiterMap = new ConcurrentHashMap<>();
			limiterFilterMap = new ConcurrentHashMap<>();
			for (int i=0; i < rulesJsonArray.size(); i++) {
				JSONObject ruleJson =  rulesJsonArray.getJSONObject(i);
				limiterMap.put(ruleJson.getString("name"), RateLimiter.create(ruleJson.getDoubleValue("limit")));
				if (ruleJson.getJSONObject("filter") != null && CollectionUtils.isNotEmpty(ruleJson.getJSONObject("filter").keySet())) {
					limiterFilterMap.put(ruleJson.getString("name"), ruleJson.getJSONObject("filter"));
				} else if (ruleJson.getJSONObject("filter") != null && CollectionUtils.isEmpty(ruleJson.getJSONObject("filter").keySet())) {
					limiterFilterMap.put(ruleJson.getString("name"), new JSONObject());
				}
			}
		}
	}

	public static RateLimiter getLimiter(JSONObject json, String pk) {
		if (limiterMap != null && limiterMap.keySet() != null) {
			for (String name : limiterMap.keySet()) {
				JSONObject filterJson = limiterFilterMap.get(name);
				if (filterJson != null && CollectionUtils.isNotEmpty(filterJson.keySet())) {
					boolean match = true;
					for (String key : filterJson.keySet()) {
						JSONArray valArray = filterJson.getJSONArray(key);
						if (valArray == null || valArray.size() == 0) {
							continue;
						}
						if (json.getJSONObject("fieldValues").getJSONObject(key) != null) {
							String refVal = json.getJSONObject("fieldValues").getJSONObject(key).getString("newValue") == null ? 
									json.getJSONObject("fieldValues").getJSONObject(key).getString("oldValue") : json.getJSONObject("fieldValues").getJSONObject(key).getString("newValue");
							for (int j = 0; j < valArray.size(); j++) {
								String val = valArray.getString(j);
								log.info("key:{} refVal:{}, val:{}", key, refVal, val);
								if (val.equalsIgnoreCase(refVal)) {
									continue;
								} else {
									match = false;
									break;
								}
							}
						} else {
							match = false;
						}
					}
					if (match) {
						log.info("pk:{} 命中规则:{}", name, pk);
						return limiterMap.get(name);
					}
				} else {
					log.info("pk:{} 空filter 命中规则:{}", pk, name);
					return limiterMap.get(name);
				}
			}
		}
		log.info("pk:{} 未命中规则", pk);
		return null;
	}

	@Override
	public boolean isEndOfStream(BaseRow kafkaMessage) {
		return false;
	}

	@Override
	public TypeInformation<BaseRow> getProducedType() {
		return baseRowTypeInfo;
	}
}
