/*
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 * 	http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.aliyun.wormhole.qanat.kafka010.input;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer010;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumerBase;
import org.apache.flink.streaming.connectors.kafka.config.StartupMode;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.dataformat.GenericRow;
import org.apache.flink.table.typeutils.BaseRowTypeInfo;
import org.apache.flink.table.util.TableProperties;
import org.apache.flink.util.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.ByteArraySerializer;

import com.alibaba.blink.streaming.connectors.kafkabase.KafkaBaseTableSource;

import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.regex.Pattern;

public class Kafka010TableSource extends KafkaBaseTableSource {
	
	private TableProperties tableProperties;
	private RichTableSchema schema;
	private String consumerId;
	public Kafka010TableSource(
			List<String> topic,
			String topicPattern,
			Properties properties,
			StartupMode startupMode,
			long startTimeStamp,
			boolean isFinite,
			BaseRowTypeInfo baseRowTypeInfo, RichTableSchema schema, TableProperties tableProperties) {
		super(topic, topicPattern, properties, startupMode, startTimeStamp, isFinite, baseRowTypeInfo);
		this.schema = schema;
		this.tableProperties = tableProperties;
		if (!StringUtils.isNullOrWhitespaceOnly(topicPattern)) {
			String [] topics = topicPattern.split("\\|");
			this.topic = Arrays.asList(topics);
		}
		consumerId = properties.getProperty("group.id");
	}
	
	@Override
	public FlinkKafkaConsumerBase createKafkaConsumer() {
		FlinkKafkaConsumerBase consumer;
		Kafka010MessageDeserialization kafkaMessageDeserialization = new Kafka010MessageDeserialization(baseRowTypeInfo, schema, tableProperties, consumerId);
		Pattern pattern;
		if (!StringUtils.isNullOrWhitespaceOnly(topicPattern)) {
			pattern = Pattern.compile(topicPattern);
			consumer = new FlinkKafkaConsumer010(pattern, kafkaMessageDeserialization, properties);
		} else {
			consumer = new FlinkKafkaConsumer010(topic, kafkaMessageDeserialization, properties);
		}
		if (startupMode == StartupMode.TIMESTAMP && startTimeStamp >= -1){
			((FlinkKafkaConsumer010)consumer).setStartFromTimestamp(startTimeStamp);
		}
		return consumer;
	}

	@Override
	public int getTopicPartitionSize() {
		Properties props = new Properties(properties);
		props.setProperty(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class.getName());
		props.setProperty(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class.getName());
		props.setProperty(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, properties.getProperty("bootstrap.servers"));
		KafkaProducer producer = new KafkaProducer(props);
		try {
			int size = 0;
			for (String t : topic) {
				size += producer.partitionsFor(t).size();
			}
			return size;
		} finally {
			producer.close();
		}
	}

	@Override
	public DataStream<GenericRow> getDataStream(StreamExecutionEnvironment execEnv) {
		DataStreamSource<GenericRow> boundedStreamSource = execEnv.addSource(getSourceFunction(),
				String.format("%s-%s", explainSource(), STREAM_TAG),
				getProducedType());
		int parallelism = getTopicPartitionSize();
		boundedStreamSource.setParallelism(parallelism);
		boundedStreamSource.getTransformation().setMaxParallelism(parallelism);
		return boundedStreamSource;

	}
}
