<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>blink-connectors</artifactId>
        <groupId>com.alibaba.blink</groupId>
        <version>blink-3.7-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>qanat-kafka010</artifactId>

    <dependencies>
		<dependency>
		  <groupId>com.google.guava</groupId>
		  <artifactId>guava</artifactId>
		  <version>30.1.1-jre</version>
		</dependency>
        <!-- blink table -->
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>blink-table</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>blink-connector-kafka-base</artifactId>
            <version>${blink.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-connector-kafka-0.10_${scala.binary.version}</artifactId>
            <version>${blink.version}</version>
        </dependency>
        <!-- flink dependencies -->
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-table_${scala.binary.version}</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-core</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
            <!-- Projects depending on this project,
            won't depend on flink-table. -->
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-java</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>jersey-client</artifactId>
                    <groupId>com.sun.jersey</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>flink-shaded-hadoop2</artifactId>
                    <groupId>com.alibaba.blink</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>flink-shaded-hadoop3</artifactId>
                    <groupId>com.alibaba.blink</groupId>
                </exclusion>
            </exclusions>
            <!-- Projects depending on this project,
            won't depend on flink-table. -->
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>blink-connector-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-streaming-java_${scala.binary.version}</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-table_${scala.binary.version}</artifactId>
            <version>${blink.version}</version>
            <classifier>tests</classifier>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-streaming-scala_${scala.binary.version}</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-metrics-dropwizard</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-hadoop-compatibility_${scala.binary.version}</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>jersey-core</artifactId>
                    <groupId>com.sun.jersey</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-test-utils-junit</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-test-utils_${scala.binary.version}</artifactId>
            <version>${blink.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- test dependencies -->
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_2.11</artifactId>
            <version>2.2.6</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>scala-xml_2.11</artifactId>
                    <groupId>org.scala-lang.modules</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
		  <groupId>com.taobao.eagleeye</groupId>  
		  <artifactId>eagleeye-core</artifactId>  
		  <version>1.5.2.3</version>  
        </dependency>
        <dependency>
          <groupId>com.taobao.diamond</groupId>  
		  <artifactId>diamond-client</artifactId>  
		  <version>3.8.11</version>
        </dependency>
        <dependency>
        	<groupId>com.aliyun.wormhole</groupId>
        	<artifactId>qanat-datasource</artifactId>
        	<version>1.0.0</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Scala Compiler -->
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <executions>
                    <!-- Run scala compiler in the process-resources phase, so that dependencies on
                        scala classes can be resolved later in the (Java) compile phase -->
                    <execution>
                        <id>scala-compile-first</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>add-source</goal>
                            <goal>compile</goal>
                        </goals>
                    </execution>

                    <!-- Run scala compiler in the process-test-resources phase, so that dependencies on
                         scala classes can be resolved later in the (Java) test-compile phase -->
                    <execution>
                        <id>scala-test-compile</id>
                        <phase>process-test-resources</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- Scala Code Style, most of the configuration done via plugin management -->
            <plugin>
                <groupId>org.scalastyle</groupId>
                <artifactId>scalastyle-maven-plugin</artifactId>
                <configuration>
                    <configLocation>${project.basedir}/../tools/maven/scalastyle-config.xml
                    </configLocation>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <executions>
                    <execution>
                        <id>jar-with-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <shadedArtifactAttached>true</shadedArtifactAttached>
                            <shadedClassifierName>jar-with-dependencies</shadedClassifierName>
                            <createDependencyReducedPom>true</createDependencyReducedPom>
                            <dependencyReducedPomLocation>${project.basedir}/target/dependency
                                -reduced-pom.xml
                            </dependencyReducedPomLocation>
                            <filters>
                                <!-- Globally exclude log4j.properties from our JAR files. -->
                                <filter>
                                    <artifact>*</artifact>
                                    <excludes>
                                        <exclude>log4j.properties</exclude>
                                        <exclude>log4j-test.properties</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                            <artifactSet>
                                <includes>
                                    <!-- Unfortunately, the next line is necessary for now to force the execution
                                    of the Shade plugin upon all sub modules. This will generate effective poms,
                                    i.e. poms which do not contain properties which are derived from this root pom.
                                    In particular, the Scala version properties are defined in the root pom and without
                                    shading, the root pom would have to be Scala suffixed and thereby all other modules.
                                    -->

                                    <include>*</include>
                                </includes>
                            </artifactSet>
                            <relocations>
                                <relocation>
                                    <pattern>org.objectweb.asm</pattern>
                                    <shadedPattern>org.apache.flink.shaded.org.objectweb.asm</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>org.apache.http</pattern>
                                    <shadedPattern>com.alibaba.blink.datahub.shade.org.apache.http
                                    </shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>org.glassfish.jersey</pattern>
                                    <shadedPattern>com.alibaba.blink.datahub.shade.org.glassfish.jersey</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>javax.ws.rs</pattern>
                                    <shadedPattern>com.alibaba.blink.datahub.shade.javax.ws.rs
                                    </shadedPattern>
                                </relocation>
                            </relocations>
                            <!--<finalName>${project.artifactId}-${project.version}-jar-with-dependencies</finalName>-->
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <id>create-jar</id>
                        <phase/>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <descriptorRefs>
                                <descriptorRef>jar-with-dependencies</descriptorRef>
                            </descriptorRefs>
                        </configuration>
                    </execution>
                </executions>
            </plugin>


        </plugins>
    </build>
    <groupId>com.aliyun.wormhole</groupId>
</project>