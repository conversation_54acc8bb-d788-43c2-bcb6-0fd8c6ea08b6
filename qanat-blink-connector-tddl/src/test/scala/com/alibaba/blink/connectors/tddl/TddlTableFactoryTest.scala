/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.blink.connectors.tddl

import org.apache.flink.api.common.typeinfo.{TypeInformation, Types}
import org.apache.flink.api.scala._
import org.apache.flink.streaming.api.scala.{DataStream, StreamExecutionEnvironment}
import org.apache.flink.table.api.{RichTableSchema, TableEnvironment}
import org.apache.flink.table.api.scala._
import org.apache.flink.table.types.{DataTypes, TypeConverters}
import org.apache.flink.table.typeutils.BaseRowTypeInfo
import org.apache.flink.table.util.TableProperties

import com.alibaba.blink.table.connectors.conf.BlinkOptions
import com.alibaba.blink.table.util.BlinkTableFactoryUtil
import org.junit.Assert.{assertEquals, assertTrue}
import org.junit.Test


class TddlTableFactoryTest{

  @Test
  def testCreateTddlTableSink(): Unit = {
    val schema = new RichTableSchema(
      Array("id", "len", "content"),
      Array(
        DataTypes.INT,
        DataTypes.INT,
        DataTypes.STRING))

    schema.setPrimaryKey("id")

    val kvParams: TableProperties = new TableProperties()
    kvParams.setString("appName", "BLINKRDSTEST_APP")
    kvParams.setString("tableName", "test")
    kvParams.setString("isSharding", "true")
    kvParams.setString("accessKey", "ak")
    kvParams.setString("secretKey", "sk")
    kvParams.putTableNameIntoProperties("t1")
    kvParams.putSchemaIntoProperties(schema)
    kvParams.setString(BlinkOptions.CONNECTOR_GROUP, BlinkOptions.CONNECTOR_GROUP_VALUE_BLINK)

    val data = List(
      (1, 2, "Hi"),
      (2, 5, "Hello"),
      (3, 6, "Hello!"))

    val env = StreamExecutionEnvironment.getExecutionEnvironment
    val tEnv = TableEnvironment.getTableEnvironment(env)
    val stream: DataStream[(Int, Int, String)] = env.fromCollection(data)
    val streamTable = stream.toTable(tEnv, 'id, 'len, 'content)
    tEnv.registerTable("source_t1", streamTable)

    tEnv.connect(BlinkTableFactoryUtil.getSimpleDescriptor("TDDL", kvParams))
      .registerTableSink("sink_t1")

    tEnv.sqlUpdate("insert into sink_t1 select id, len, content from source_t1")
  }

  @Test
  def testCreateTddlDimTableSource(): Unit = {
    val schema = new RichTableSchema(
      Array("id", "name"),
      Array(DataTypes.INT, DataTypes.STRING))
    schema.setPrimaryKey("id")

    val kvParams: TableProperties = new TableProperties()
    kvParams.setString("appName", "BLINKTEST")
    kvParams.setString("tableName", "test")
    kvParams.setString("accessKey", "xxxxx")
    kvParams.setString("secretKey", "xxxxx")
    kvParams.putTableNameIntoProperties("tddl")
    kvParams.putSchemaIntoProperties(schema)
    kvParams.setString(BlinkOptions.CONNECTOR_GROUP, BlinkOptions.CONNECTOR_GROUP_VALUE_BLINK)

    val env = StreamExecutionEnvironment.getExecutionEnvironment
    val tEnv = TableEnvironment.getTableEnvironment(env)
    val tddlDim = BlinkTableFactoryUtil.findAndCreateTableSource(tEnv,
      BlinkTableFactoryUtil.getSimpleDescriptor("TDDL", kvParams))
    val rowTypeInfo = new BaseRowTypeInfo(
      Array(Types.INT, Types.STRING).asInstanceOf[Array[TypeInformation[_]]],
      Array("id", "name"))

    assertTrue(tddlDim.isInstanceOf[TddlTableSource])
    assertEquals(rowTypeInfo,
      TypeConverters.createInternalTypeInfoFromDataType(tddlDim.getReturnType))
  }
}
