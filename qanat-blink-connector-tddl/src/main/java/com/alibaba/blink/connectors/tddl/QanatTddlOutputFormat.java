/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.blink.connectors.tddl;

import com.alibaba.blink.streaming.connectors.common.MetricUtils;
import com.alibaba.blink.streaming.connectors.common.errcode.ConnectorErrors;
import com.alibaba.blink.streaming.connectors.common.exception.BlinkRuntimeException;
import com.alibaba.blink.streaming.connectors.common.exception.ErrorUtils;
import com.alibaba.blink.streaming.connectors.common.output.HasRetryTimeout;
import com.alibaba.blink.streaming.connectors.common.output.Syncable;
import com.alibaba.blink.streaming.connectors.common.output.TupleRichOutputFormat;
import com.alibaba.blink.streaming.connectors.common.source.parse.DirtyDataStrategy;
import com.alibaba.blink.streaming.connectors.common.util.ConnectionPool;
import com.alibaba.blink.streaming.connectors.common.util.JdbcUtils;
import com.alibaba.blink.streaming.connectors.common.util.SQLExceptionSkipPolicy;
import com.alibaba.blink.streaming.connectors.common.util.TpsLimitUtils;
import com.taobao.diamond.identify.CredentialService;
import com.taobao.diamond.identify.Credentials;
import org.apache.flink.shaded.guava18.com.google.common.base.Joiner;
import com.taobao.tddl.client.jdbc.TDataSource;
import com.taobao.tddl.common.exception.TddlRuntimeException;
import com.taobao.tddl.common.model.lifecycle.Lifecycle;
import com.taobao.tddl.group.jdbc.TGroupDataSource;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.Meter;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

public class QanatTddlOutputFormat
		extends TupleRichOutputFormat
		implements Syncable, HasRetryTimeout {
	private static final Logger LOG = LoggerFactory.getLogger(QanatTddlOutputFormat.class);
	private String appName;
	private String tableName;
	private List<String> pkFields;
	private String ak;
	private String sk;
	private RowTypeInfo rowTypeInfo;
	private boolean isSharding = false;
	private String dbGroupKey;
	private int maxRetryTime = 3;
	private String unitName;
	private String dialect;

	private transient Timer flusher;
	private volatile long lastFlushTime = 0;

	private int batchSize = 50;
	private int bufferSize = 500;
	private List<String> exceptUpdateKeys = new ArrayList<>();
	private long flushIntervalMs = 5000;
	private long currentCount = 0;
	private boolean existsPrimaryKeys = true;
	private long maxSinkTps = -1;
	private int numTasks = 1;

	private final Map<String, Tuple2<Boolean,Row>> mapReduceBuffer = new HashMap<>();
	private List<Row> writeAddBuffer;
	private int curAddCount = 0;
	private List<Row> writeDelBuffer;
	private int curDelCount = 0;

	private transient DataSource dataSource;
	private static ConnectionPool<DataSource> dataSourcePool = new ConnectionPool<>();
	private transient Connection connection = null;

	private String escapedFieldNames = null;

	private static final String INSERT_OR_UPDATE_ON_DUPLICATE_SQL_TPL = "INSERT INTO %s (%s) VALUES (%s)\n" +
																		"  ON DUPLICATE KEY UPDATE %s";
	private static final String INSERT_SQL_TPL = "INSERT INTO %s (%s) VALUES (%s)";

	private final String DELETE_SQL_TPL = "DELETE FROM %s WHERE %s";
	private final String DELETE_WITH_KEY_SQL_TPL = "DELETE FROM %s WHERE %s LIMIT 1";

	private boolean skipDuplicateEntryError = true;

	private DirtyDataStrategy dirtyDataStrategy = DirtyDataStrategy.SKIP;

	private Meter outTps;
	private Meter outBps;
	private Counter sinkSkipCounter;
	private MetricUtils.LatencyGauge latencyGauge;
	private boolean ignoreDelete = false;

	private volatile transient Exception flushException = null;
	private volatile boolean flushError = false;

	public QanatTddlOutputFormat(
			String dialect,
			String appName,
			String tableName,
			String dbGroupKey,
			List<String> pkFields,
			RowTypeInfo rowTypeInfo, boolean isSharding, int maxRetryTime) {
		this.dialect = dialect;
		this.appName = appName;
		this.tableName = tableName;
		this.pkFields = pkFields;
		this.rowTypeInfo = rowTypeInfo;
		this.isSharding = isSharding;
		this.maxRetryTime = maxRetryTime;
		this.dbGroupKey = dbGroupKey;
		Joiner joinerOnComma = Joiner.on(",").useForNull("null");
		String[] fieldNamesStr = new String[rowTypeInfo.getArity()];
		for( int i = 0;i < fieldNamesStr.length; i ++){
			fieldNamesStr[i] = JdbcUtils.escapeSqlColumn(dialect, rowTypeInfo.getFieldNames()[i]);
		}
		this.escapedFieldNames = joinerOnComma.join(fieldNamesStr);
		if (null != pkFields && !pkFields.isEmpty()){
			existsPrimaryKeys = true;
		} else {
			existsPrimaryKeys = false;
		}
	}

	public QanatTddlOutputFormat(
			String dialect,
			String appName,
			String tableName,
			String dbGroupKey,
			List<String> pkFields,
			RowTypeInfo rowTypeInfo, boolean isSharding, int maxRetryTime, String ak, String sk) {
		this.dialect = dialect;
		this.dbGroupKey = dbGroupKey;
		this.appName = appName;
		this.tableName = tableName;
		this.pkFields = pkFields;
		this.rowTypeInfo = rowTypeInfo;
		this.isSharding = isSharding;
		this.maxRetryTime = maxRetryTime;
		Joiner joinerOnComma = Joiner.on(",").useForNull("null");
		String[] fieldNamesStr = new String[rowTypeInfo.getArity()];
		for( int i = 0;i < fieldNamesStr.length; i ++){
			fieldNamesStr[i] = JdbcUtils.escapeSqlColumn(dialect, rowTypeInfo.getFieldNames()[i]);
		}
		this.escapedFieldNames = joinerOnComma.join(fieldNamesStr);
		this.ak = ak;
		this.sk = sk;
		if (null != pkFields && !pkFields.isEmpty()){
			existsPrimaryKeys = true;
		} else {
			existsPrimaryKeys = false;
		}
	}

	public QanatTddlOutputFormat setMaxSinkTps(long maxSinkTps) {
		this.maxSinkTps = maxSinkTps;
		return this;
	}

	public QanatTddlOutputFormat setBatchSize(int batchSize) {
		this.batchSize = batchSize;
		return this;
	}

	public QanatTddlOutputFormat setBufferSize(int bufferSize) {
		this.bufferSize = bufferSize;
		return this;
	}

	public QanatTddlOutputFormat setExceptUpdateKeys(List<String> exceptUpdateKeys) {
		this.exceptUpdateKeys = exceptUpdateKeys;
		return this;
	}

	public QanatTddlOutputFormat setFlushIntervalMs(long flushIntervalMs) {
		this.flushIntervalMs = flushIntervalMs;
		return this;
	}

	public QanatTddlOutputFormat setIgnoreDelete(boolean ignoreDelete) {
		this.ignoreDelete = ignoreDelete;
		return this;
	}

	@Override
	public long getRetryTimeout() {
		return 0;
	}

	@Override
	public synchronized void sync() throws IOException {
		if(!existsPrimaryKeys){
			return;
		} else {
			//使用synchronized关键字保证flush线程执行的时候，不会同时更新mapReduceBuffer的内容
			synchronized (mapReduceBuffer) {
				List<Row> addBuffer = new ArrayList<>();
				List<Row> deleteBuffer = new ArrayList<>();
				for (Tuple2<Boolean, Row> rowTuple2 : mapReduceBuffer.values()) {
					if (rowTuple2.f0) {
						addBuffer.add(rowTuple2.f1);
					} else {
						deleteBuffer.add(rowTuple2.f1);
					}
				}
				batchWrite(addBuffer);
				batchDelete(deleteBuffer);
				mapReduceBuffer.clear();
			}
		}
		lastFlushTime = System.currentTimeMillis();
		currentCount = 0;
	}

	private void initConnection() {
		SQLException exception = null;
		for (int i=0; i < maxRetryTime; i++) {
			try {
				if (null == connection) {
					connection = dataSource.getConnection();
				}
				if (connection.isClosed()) {
					connection = dataSource.getConnection();
				}
				exception = null;
				break;
			} catch (SQLException e) {
				exception = e;
				LOG.warn("get connection failed, retryTimes=" + i, e);
			}
		}

		if (exception != null || connection == null) {
			ErrorUtils.throwException(ConnectorErrors.INST.rdsGetConnectionError("TDDL"));
		}
	}

	private void batchWrite(List<Row> buffers) {
		try {
			initConnection();
			for (Row row : buffers) {
				if (writeAddBuffer == null) {
					writeAddBuffer = new ArrayList<>();
					this.curAddCount = 0;
				}
				writeAddBuffer.add(row);
				this.curAddCount++;
				if (curAddCount >= batchSize) {
					execBatchAdd();
					this.writeAddBuffer = null;
					this.curAddCount = 0;
				}
			}
			execBatchAdd();
		} finally {
			if (null != connection) {
				try {
					connection.close();
				} catch (SQLException e) {
					LOG.error("", e);
				}
			}
		}
	}

	private void execBatchAdd() {
		if( null == writeAddBuffer || writeAddBuffer.isEmpty()) {
			return;
		}

		int count = writeAddBuffer.size();
		int retriedTimes = 0;
//		while (!writeAddBuffer.isEmpty() && retriedTimes++ < maxRetryTime) {
			long start = System.currentTimeMillis();
			String sql = JdbcUtils.getDuplicateUpdateSql(rowTypeInfo, exceptUpdateKeys, tableName);
			PreparedStatement preparedStatement = null;
			int sinkCount = 0;
			try {
				preparedStatement = connection.prepareStatement(sql);
				for (Row row : writeAddBuffer) {
					JdbcUtils.setUpdateStatement(preparedStatement, row, rowTypeInfo);
					preparedStatement.addBatch();
					sinkCount++;
				}

				LOG.info("BatchUpdateSize [{}]", count);
				long s1 = System.currentTimeMillis();
				preparedStatement.executeBatch();

				if (latencyGauge != null) {
					long s2 = System.currentTimeMillis();
					latencyGauge.report(s2 - s1, count);
				}
				if (outTps != null) {
					outTps.markEvent(count);
				}
				if (outBps != null) {
					outBps.markEvent(count * 1000);
				}
				TpsLimitUtils.limitTps(maxSinkTps, numTasks, start, sinkCount);

				lastFlushTime = System.currentTimeMillis();
				writeAddBuffer.clear();
//				break;
			} catch (SQLException e) {
				LOG.error("Batch write tddl error, retry times=" + retriedTimes, e);

//				if (retriedTimes >= maxRetryTime) {
//					BlinkRuntimeException blinkException =
//							ErrorUtils.getException(
//									ConnectorErrors.INST.rdsBatchWriteError("TDDL",sql, String.valueOf
//											(preparedStatement)), e);
//					if (SQLExceptionSkipPolicy.judge(dirtyDataStrategy, e.getErrorCode(), blinkException)) {
//						sinkSkipCounter.inc(count);
//						LOG.error(blinkException.getErrorMessage() + " sql:" + sql);
//					}
//				}
//
//				try {
//					Thread.sleep(1000 * retriedTimes);
//				} catch (Exception e1) {
//					LOG.error("Thread sleep exception", e1);
//				}
			} finally {
				if (null != preparedStatement) {
					try {
						preparedStatement.close();
					} catch (SQLException e) {
						LOG.error("preparedStatement close error", e);
					}
				}
			}
//		}
	}

	private void batchDelete(List<Row> buffers) {
		try {
			initConnection();
			for (Row row : buffers) {
				if (writeDelBuffer == null) {
					writeDelBuffer = new ArrayList<>();
					this.curDelCount = 0;
				}
				writeDelBuffer.add(row);
				this.curDelCount++;
				if (curDelCount >= batchSize) {
					execBatchDelete();
					this.writeDelBuffer = null;
					this.curDelCount = 0;
				}
			}
			execBatchDelete();
		} finally {
			if (null != connection) {
				try {
					connection.close();
				} catch (SQLException e) {
					LOG.error("", e);
				}
			}
		}
	}

	private void execBatchDelete() {
		if( null == writeDelBuffer || writeDelBuffer.isEmpty()) {
			return;
		}

		int count = writeDelBuffer.size();
		int retriedTimes = 0;
		while (!writeDelBuffer.isEmpty() && retriedTimes++ < maxRetryTime) {
			long start = System.currentTimeMillis();
			String sql = JdbcUtils.getDeleteSql(pkFields, tableName, true);
			PreparedStatement preparedStatement = null;
			int sinkCount = 0;
			try {
				preparedStatement = connection.prepareStatement(sql);
				for (Row row : writeDelBuffer) {
					JdbcUtils.setDeleteStatement(preparedStatement, row, rowTypeInfo, pkFields);
					preparedStatement.addBatch();
					sinkCount++;
				}

				LOG.info("BatchDeleteSize [{}]", count);
				long s1 = System.currentTimeMillis();
				preparedStatement.executeBatch();

				if (latencyGauge != null) {
					long s2 = System.currentTimeMillis();
					latencyGauge.report(s2 - s1, count);
				}
				if (outTps != null) {
					outTps.markEvent(count);
				}
				TpsLimitUtils.limitTps(maxSinkTps, numTasks, start, sinkCount);

				lastFlushTime = System.currentTimeMillis();
				writeDelBuffer.clear();
				break;
			} catch (SQLException e) {
				LOG.error("Batch delete tddl error, retry times=" + retriedTimes, e);
				if (connection != null) {
					try {
						LOG.warn("Transaction is being rolled back");
						connection.rollback();
					} catch (Exception ex) {
						LOG.warn("Rollback failed", ex);
					}
				}

				if (retriedTimes >= maxRetryTime) {
					BlinkRuntimeException blinkException =
							ErrorUtils.getException(
									ConnectorErrors.INST.rdsBatchDeleteError("TDDL",sql, String.valueOf
											(preparedStatement)), e);
					if (SQLExceptionSkipPolicy.judge(dirtyDataStrategy, e.getErrorCode(), blinkException)) {
						sinkSkipCounter.inc(count);
						LOG.error(blinkException.getErrorMessage() + " sql:" + sql);
					}
				}

				try {
					Thread.sleep(1000 * retriedTimes);
				} catch (Exception e1) {
					LOG.error("Thread sleep exception", e1);
				}
			} finally {
				if (null != preparedStatement) {
					try {
						preparedStatement.close();
					} catch (SQLException e) {
						LOG.error("preparedStatement close error", e);
					}
				}
			}
		}
	}

	@Override
	public void configure(Configuration configuration) {

	}

	@Override
	public void open(int taskNumber, int numTasks) throws IOException {
		this.numTasks = numTasks;
		super.open(taskNumber, numTasks);
		boolean hasDbGroupKey = !StringUtils.isEmpty(dbGroupKey);
		synchronized (QanatTddlOutputFormat.class) {
			System.setProperty("tddl.version.check", "false");
			if (dataSourcePool.contains(appName)){
				if (hasDbGroupKey) {
					dataSource = dataSourcePool.get(appName, dbGroupKey);
				} else {
					dataSource = dataSourcePool.get(appName);
				}
			} else {
				if (!StringUtils.isEmpty(ak) && !StringUtils.isEmpty(sk)) {
					CredentialService credentialService = CredentialService.getInstance();
					credentialService.setCredential(new Credentials(ak, sk));
				}

				if (hasDbGroupKey) {
					TGroupDataSource tdataSource = new TGroupDataSource();
					tdataSource.setAppName(appName);
					tdataSource.setDbGroupKey(dbGroupKey);
					if(!StringUtils.isEmpty(unitName)){
						tdataSource.setUnitName(unitName);
					}
					tdataSource.init();
					dataSource = tdataSource;
					dataSourcePool.put(appName, dbGroupKey, dataSource);
				} else {
					TDataSource tdataSource = new TDataSource();
					tdataSource.setAppName(appName);
					tdataSource.setSharding(isSharding);
					tdataSource.setDynamicRule(isSharding);
					if (null != unitName && !unitName.isEmpty()) {
						tdataSource.setUnitName(unitName);
					}
					tdataSource.init();
					dataSource = tdataSource;
					dataSourcePool.put(appName, dataSource);
				}
			}
		}
		if(existsPrimaryKeys) {
			scheduleFlusher();
			// 默认会把主键排除掉
			for(String pk:pkFields) {
				if(!exceptUpdateKeys.contains(pk)) {
					exceptUpdateKeys.add(pk);
				}
			}
		}
		outTps = MetricUtils.registerOutTps(getRuntimeContext());
		outBps = MetricUtils.registerOutBps(getRuntimeContext(), "tddl");
		latencyGauge = MetricUtils.registerOutLatency(getRuntimeContext());
		sinkSkipCounter = MetricUtils.registerSinkSkipCounter(getRuntimeContext(),getName());
	}
	/**
	 * Start flusher that will flush buffer automatically
	 */
	protected void scheduleFlusher() {
		flusher = new Timer("TddlOutputFormat.buffer.flusher");
		flusher.schedule(new TimerTask() {
			@Override
			public void run() {
				try {
					if(System.currentTimeMillis() - lastFlushTime >= flushIntervalMs){
						synchronized (this){
							sync();
						}
					}
				} catch (Exception e) {
					LOG.error("flush buffer to Tddl failed", e);
					flushException = e;
					flushError = true;
				}
			}
		}, flushIntervalMs, flushIntervalMs);
	}

	private void executeSql(String sql) throws SQLException{
		long start = System.currentTimeMillis();
		int retryTime = 0;
//		while (retryTime++ < maxRetryTime) {
			PreparedStatement ps = null;
			try {
				if (null == connection) {
					connection = dataSource.getConnection();
				}
				if (LOG.isDebugEnabled()) {
					LOG.debug(sql);
				}
				ps = connection.prepareStatement(sql);
				ps.execute(sql);
				TpsLimitUtils.limitTps(maxSinkTps, numTasks, start,1);
//				break;
			} catch (SQLException e) {
				LOG.error("Insert into db error,exception:", e);
//				if (retryTime == maxRetryTime) {
//					sinkSkipCounter.inc();
//					LOG.error("Exception: insert into db failed after retry " + maxRetryTime +
//							" times. "+ "sql: " + sql, e);
//				}
//				try {
//					// sleep according to retryTimes
//					Thread.sleep(1000 * retryTime);
//				} catch (Exception e1) {
//					LOG.error("Thread sleep exception", e1);
//				}
			} catch (TddlRuntimeException e){
//				if (retryTime == maxRetryTime) {
					LOG.error("Exception: insert into db failed after retry " + maxRetryTime +
							" times. "+ "sql: " + sql, e);
//				}
//				try {
//					// sleep according to retryTimes
//					Thread.sleep(1000 * retryTime);
//				} catch (Exception e1) {
//					LOG.error("Thread sleep exception", e1);
//				}
			} catch (Exception e) {
                LOG.error("Insert into db error,exception:", e);
//                if (retryTime == maxRetryTime) {
//                    sinkSkipCounter.inc();
//                    LOG.error("Exception: insert into db failed after retry " + maxRetryTime +
//                            " times. "+ "sql: " + sql, e);
//                }
//                try {
//                    // sleep according to retryTimes
//                    Thread.sleep(1000 * retryTime);
//                } catch (Exception e1) {
//                    LOG.error("Thread sleep exception", e1);
//                }
            } finally {
				if (null != ps) {
					ps.close();
				}
				if (null != connection) {
					connection.close();
					connection = null;
				}
			}
//		}

		// report metrics
		long end = System.currentTimeMillis();
		latencyGauge.report(end - start, 1);
		outTps.markEvent();
		// rough estimate
		outBps.markEvent(sql.length() * 2);
	}


	@Override
	public void writeAddRecord(Row row) throws IOException {
		if (flushError && null != flushException){
			throw new RuntimeException(flushException);
		}

		if (null == row) {
			return;
		}
		if(!existsPrimaryKeys) {
			String sql = null;
			try {
				Joiner joinerOnComma = Joiner.on(",").useForNull("null");
				String fieldValues = JdbcUtils.toMysqlField(row.getField(0));
				for (int i = 1; i < row.getArity(); i++) {
					fieldValues = fieldValues.concat("," + JdbcUtils.toMysqlField(row.getField(i)));
				}
				sql = String.format(INSERT_SQL_TPL, tableName, this.escapedFieldNames, fieldValues);
				executeSql(sql);
			} catch (SQLException e) {
				LOG.error("ERROR happens in TddlOutputFormat writeAddRecord method,Exception : ", e);
			}
		} else {
			currentCount ++;
			String dupKey = JdbcUtils.constructDupKey(row, rowTypeInfo, pkFields);
			synchronized (mapReduceBuffer) {
				mapReduceBuffer.put(dupKey, new Tuple2<>(true, row));
			}
			if(currentCount >= bufferSize){
				sync();
			}
		}
	}

	@Override
	public void writeDeleteRecord(Row row) throws IOException {
		if (flushError && null != flushException){
			throw new RuntimeException(flushException);
		}
		if(ignoreDelete){
			return;
		}
		if (!existsPrimaryKeys) {
			try {
				Joiner joinerOnComma = Joiner.on(" AND ").useForNull("null");
				List<String> sub = new ArrayList<>();
				for (int i = 0; i < row.getArity(); i++) {
					sub.add(" " + rowTypeInfo.getFieldNames()[i] + " = " +
							JdbcUtils.toMysqlField(row.getField(i)));
				}
				String sql = String.format(DELETE_SQL_TPL, tableName, joinerOnComma.join(sub));
				executeSql(sql);

			} catch (SQLException e) {
				LOG.error("", e);
			}
		}else {
			currentCount ++;
			String dupKey = JdbcUtils.constructDupKey(row, rowTypeInfo, pkFields);
			synchronized (mapReduceBuffer) {
				mapReduceBuffer.put(dupKey, new Tuple2<>(false, row));
			}
			if(currentCount >= bufferSize){
				sync();
			}
		}
	}

	@Override
	public String getName() {
		return "tddl";
	}



	@Override
	public void close() throws IOException {
		if (flusher != null) {
			flusher.cancel();
			flusher = null;
		}
		sync();
		synchronized (QanatTddlOutputFormat.class) {
			if (dataSourcePool.remove(appName) && null != dataSource) {
				((Lifecycle) dataSource).destroy();
				dataSource = null; //avoid potential connections leak
			}
		}
	}

	public void setDirtyDataStrategy(DirtyDataStrategy strategy){
		dirtyDataStrategy = strategy;
	}

	public QanatTddlOutputFormat setUnitName(String unitName) {
		this.unitName = unitName;
		return this;
	}

	public static class Builder {
		private String appName;
		private String tableName;
		private List<String> pkField;
		private RowTypeInfo rowTypeInfo;
		private boolean isSharding = false;
		private int maxRetryTime = 3;
		private String ak;
		private String sk;
		private DirtyDataStrategy dirtyDataStrategy = DirtyDataStrategy.EXCEPTION;
		private String unitName;
		private int batchSize = 50;
		private int bufferSize = 500;
		private List<String> exceptUpdateKeys = new ArrayList<>();
		private long flushIntervalMs = 5000;
		private long maxSinkTps = -1;
		private boolean ignoreDelete = false;
		private String dbGroupKey;
		private String dialect;

		public String getDbGroupKey() {
			return dbGroupKey;
		}

		public Builder setDbGroupKey(String dbGroupKey) {
			this.dbGroupKey = dbGroupKey;
			return this;
		}

		public Builder setIgnoreDelete(boolean ignoreDelete) {
			this.ignoreDelete = ignoreDelete;
			return this;
		}

		public Builder setMaxSinkTps(long maxSinkTps) {
			this.maxSinkTps = maxSinkTps;
			return this;
		}

		public Builder setBatchSize(int batchSize) {
			this.batchSize = batchSize;
			return this;
		}

		public Builder setBufferSize(int bufferSize) {
			this.bufferSize = bufferSize;
			return this;
		}

		public Builder setExceptUpdateKeys(List<String> exceptUpdateKeys) {
			this.exceptUpdateKeys = exceptUpdateKeys;
			return this;
		}

		public Builder setFlushIntervalMs(long flushIntervalMs) {
			this.flushIntervalMs = flushIntervalMs;
			return this;
		}

		public Builder setUnitName(String unitName) {
			this.unitName = unitName;
			return this;
		}

		public Builder setAppName(String appName) {
			this.appName = appName;
			return this;
		}

		public Builder setTableName(String tableName) {
			this.tableName = tableName;
			return this;
		}

		public String getTableName() {
			return  this.tableName;
		}

		public Builder setPkField(List<String> pkField) {
			this.pkField = pkField;
			return this;
		}

		public Builder setRowTypeInfo(RowTypeInfo rowTypeInfo) {
			this.rowTypeInfo = rowTypeInfo;
			return this;
		}

		public Builder setSharding(boolean sharding) {
			isSharding = sharding;
			return this;
		}

		public Builder setMaxRetryTime(int maxRetryTime) {
			this.maxRetryTime = maxRetryTime;
			return this;
		}

		public Builder setAk(String ak) {
			this.ak = ak;
			return this;
		}

		public Builder setSk(String sk) {
			this.sk = sk;
			return this;
		}

		public Builder setDirtyDataStrategy(DirtyDataStrategy strategy){
			dirtyDataStrategy = strategy;
			return this;
		}

		public Builder setDialect(String dialect) {
			this.dialect = dialect;
			return this;
		}

		public QanatTddlOutputFormat build() {
			QanatTddlOutputFormat outputFormat;
			if(null == ak || null == sk){
				outputFormat = new QanatTddlOutputFormat(dialect, appName, tableName, dbGroupKey, pkField, rowTypeInfo, isSharding, maxRetryTime);
			} else {
				outputFormat = new QanatTddlOutputFormat(dialect, appName, tableName, dbGroupKey, pkField, rowTypeInfo, isSharding, maxRetryTime, ak, sk);

			}
			outputFormat.setDirtyDataStrategy(dirtyDataStrategy);
			if (null != unitName && !unitName.isEmpty()){
				outputFormat.setUnitName(unitName);
			}
			outputFormat.setBatchSize(batchSize)
				.setBufferSize(bufferSize)
				.setFlushIntervalMs(flushIntervalMs)
				.setExceptUpdateKeys(exceptUpdateKeys)
				.setMaxSinkTps(maxSinkTps)
				.setIgnoreDelete(ignoreDelete);
			return outputFormat;
		}
	}
}
