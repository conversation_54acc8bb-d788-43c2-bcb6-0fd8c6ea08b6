/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.blink.connectors.tddl;

import com.alibaba.blink.table.cache.CacheConfig;
import com.alibaba.blink.table.cache.CacheStrategy;
import com.alibaba.blink.table.connectors.conf.BlinkOptions;
import com.alibaba.blink.streaming.connectors.common.errcode.ConnectorErrors;
import com.alibaba.blink.streaming.connectors.common.exception.InvalidParamException;
import com.alibaba.blink.streaming.connectors.common.exception.NotEnoughParamsException;
import com.alibaba.blink.table.factories.BlinkTableFactory;
import com.alibaba.blink.streaming.connectors.common.source.parse.DirtyDataStrategy;
import com.alibaba.blink.streaming.connectors.common.util.BlinkStringUtil;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.table.dataformat.GenericRow;
import org.apache.flink.table.factories.BatchCompatibleTableSinkFactory;
import org.apache.flink.table.factories.BatchTableSourceFactory;
import org.apache.flink.table.factories.StreamTableSinkFactory;
import org.apache.flink.table.factories.StreamTableSourceFactory;
import org.apache.flink.table.plan.stats.TableStats;
import org.apache.flink.table.sinks.BatchCompatibleStreamTableSink;
import org.apache.flink.table.sinks.StreamTableSink;
import org.apache.flink.table.sources.BatchTableSource;
import org.apache.flink.table.sources.StreamTableSource;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.util.TableProperties;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.types.Row;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_PROPERTY_VERSION;
import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_TYPE;

public class QanatTddlTableFactory extends BlinkTableFactory implements
		BatchTableSourceFactory<BaseRow>,
		StreamTableSourceFactory<BaseRow>,
		StreamTableSinkFactory<Tuple2<Boolean, Row>>,
		BatchCompatibleTableSinkFactory<Tuple2<Boolean, Row>> {

	@Override
	public StreamTableSource<BaseRow> createStreamTableSource(Map<String, String> props) {
		TableProperties properties = new TableProperties();
		properties.putProperties(props);
		RichTableSchema schema = properties.readSchemaFromProperties(classLoader);
		String sqlTableName = properties.readTableNameFromProperties();

		String appName = properties.getString(BlinkOptions.TDDL.APP_NAME);
		String tableName = properties.getString(BlinkOptions.TDDL.TABLE_NAME);
		String ak = properties.getString(BlinkOptions.TDDL.ACCESS_KEY);
		String sk = properties.getString(BlinkOptions.TDDL.SECRET_KEY);
		if (BlinkStringUtil.isEmpty(appName, tableName, ak, sk)) {
			throw new NotEnoughParamsException(BlinkOptions.TDDL.PARAMS_DIM_HELP_MSG);
		}

		if (schema.deduceAllIndexes().isEmpty()) {
			throw new InvalidParamException(ConnectorErrors.INST.dimTableNoIndexKeyError(sqlTableName));
		}

		boolean isSharding = properties.getBoolean(BlinkOptions.TDDL.OPTIONAL_IS_SHARDING);
		String unitName = properties.getString(BlinkOptions.TDDL.OPTIONAL_UNIT_NAME);
		int maxRetries = properties.getInteger(BlinkOptions.TDDL.OPTIONAL_MAX_RETRY_TIMES);
		String dbGroupKey = properties.getString(BlinkOptions.TDDL.OPTIONAL_DB_GROUP_KEY);
		int joinMaxRows = properties.getInteger(BlinkOptions.DIM.OPTIONAL_JOIN_MAX_ROWS);
		String dialect = properties.getString(BlinkOptions.TDDL.OPTIONAL_DIALECT);

		CacheStrategy cacheStrategy = BlinkOptions.TDDL.getCacheStrategy(properties);
		String cacheReloadTimeBlacklist = properties.getString(BlinkOptions.RDS.OPTIONAL_CACHE_RELOAD_TIME_BLACKLIST);
		List<Tuple2<Long, Long>> timeRangeBlacklist = BlinkOptions.RDS.parseTimeRangeBlacklist(cacheReloadTimeBlacklist);
		int scanLimit = properties.getInteger(BlinkOptions.RDS.OPTIONAL_CACHE_SCAN_LIMIT);
		CacheConfig cacheConfig = new CacheConfig(cacheStrategy, timeRangeBlacklist, scanLimit);

		TddlConnectionParam param = new TddlConnectionParam();
		param.setAppName(appName)
			.setTableName(tableName)
			.setDbGroupKey(dbGroupKey)
			.setSharding(isSharding)
			.setAccessKey(ak)
			.setSecretKey(sk)
			.setUnitName(unitName)
			.setDialect(dialect)
			.setMaxRetries(maxRetries)
			.setMaxFetchResult(joinMaxRows);

		return new TddlTableSource(
				sqlTableName,
				schema,
				param,
				cacheConfig);
	}

	private TddlTableSink createSink(Map<String, String> props) {
		TableProperties properties = new TableProperties();
		properties.putProperties(props);
		RichTableSchema schema = properties.readSchemaFromProperties(classLoader);

		String appName = properties.getString(BlinkOptions.TDDL.APP_NAME);
		String tableName = properties.getString(BlinkOptions.TDDL.TABLE_NAME);
		String ak = properties.getString(BlinkOptions.TDDL.ACCESS_KEY);
		String sk = properties.getString(BlinkOptions.TDDL.SECRET_KEY);
		if (BlinkStringUtil.isEmpty(appName, tableName, ak, sk)) {
			throw new NotEnoughParamsException(BlinkOptions.TDDL.PARAMS_WRITER_HELP_MSG);
		}

		String dbGroupKey = properties.getString(BlinkOptions.TDDL.OPTIONAL_DB_GROUP_KEY);
		boolean isSharding = properties.getBoolean(BlinkOptions.TDDL.OPTIONAL_IS_SHARDING);
		String unitName = properties.getString(BlinkOptions.TDDL.OPTIONAL_UNIT_NAME);
		int maxRetries = properties.getInteger(BlinkOptions.TDDL.OPTIONAL_MAX_RETRY_TIMES);
		int batchSize = properties.getInteger(BlinkOptions.RDS.OPTIONAL_BATCH_SIZE);
		int bufferSize = properties.getInteger(BlinkOptions.RDS.OPTIONAL_BUFFER_SIZE);
		int flushIntervalMs = properties.getInteger(BlinkOptions.RDS.OPTIONAL_FLUSH_INTERVAL_MS);
		String excludeUpdateColumnsStr = properties.getString(BlinkOptions.RDS.OPTIONAL_EXCLUDE_UPDATE_FIELDS);
		long maxSinkTps = properties.getLong(BlinkOptions.MAX_SINK_TPS);
		List<String> excludeUpdateColumns = new ArrayList<>();
		if (!BlinkStringUtil.isEmpty(excludeUpdateColumnsStr)) {
			for(String s1:excludeUpdateColumnsStr.split(",")){
				excludeUpdateColumns.add(s1);
			}
		}

		String actionOnInsertError = properties.getString(BlinkOptions.ACTION_ON_INSERT_ERROR);
		DirtyDataStrategy dirtyDataStrategy = DirtyDataStrategy.EXCEPTION;
		if(actionOnInsertError.equalsIgnoreCase("SKIP")){
			dirtyDataStrategy = DirtyDataStrategy.SKIP;
		}else if(actionOnInsertError.equalsIgnoreCase("SKIP_SINLENT")){
			dirtyDataStrategy = DirtyDataStrategy.SKIP_SILENT;
		}

		boolean ignoreDelete = properties.getBoolean(BlinkOptions.TDDL.OPTIONAL_IGNORE_DELETE);
		String partitionBy = properties.getString(BlinkOptions.PARTITION_BY);
		boolean shuffleEmptyKey = properties.getBoolean(BlinkOptions.SHUFFLE_EMPTY_KEY);
		String dialect = properties.getString(BlinkOptions.TDDL.OPTIONAL_DIALECT);

		QanatTddlOutputFormat.Builder builder = new QanatTddlOutputFormat.Builder();
		builder.setAppName(appName)
			.setTableName(tableName)
			.setDbGroupKey(dbGroupKey)
			.setSharding(isSharding)
			.setMaxRetryTime(maxRetries)
			.setPkField(schema.getPrimaryKeys())
			.setAk(ak)
			.setSk(sk)
			.setUnitName(unitName)
			.setBufferSize(bufferSize)
			.setBatchSize(batchSize)
			.setFlushIntervalMs(flushIntervalMs)
			.setExceptUpdateKeys(excludeUpdateColumns)
			.setMaxSinkTps(maxSinkTps)
			.setDirtyDataStrategy(dirtyDataStrategy)
			.setIgnoreDelete(ignoreDelete)
			.setDialect(dialect);

		TddlTableSink sink = new TddlTableSink(builder, schema);
		if (partitionBy != null && !partitionBy.isEmpty()) {
			sink.setPartitionedField(partitionBy);
			sink.setShuffleEmptyKey(shuffleEmptyKey);
		}
		return sink;
	}

	@Override
	protected Map<String, String> requiredContextSpecific() {
		Map<String, String> context = new HashMap<>();
		context.put(CONNECTOR_TYPE, "qanat_tddl"); // TDDL
		context.put(CONNECTOR_PROPERTY_VERSION, "1"); // backwards compatibility
		return context;
	}

	@Override
	protected List<String> supportedSpecificProperties() {
		return mergeProperties(BlinkOptions.DIM.SUPPORTED_KEYS,
				BlinkOptions.TDDL.SUPPORTED_KEYS,
				BlinkOptions.RDS.SUPPORTED_KEYS);
	}

	@Override
	public BatchCompatibleStreamTableSink<Tuple2<Boolean, Row>> createBatchCompatibleTableSink(Map<String, String> properties) {
		return createSink(properties);
	}

	@Override
	public StreamTableSink<Tuple2<Boolean, Row>> createStreamTableSink(Map<String, String> properties) {
		return createSink(properties);
	}

	@Override
	public BatchTableSource<BaseRow> createBatchTableSource(Map<String, String> properties) {
		return (BatchTableSource<BaseRow>) createStreamTableSource(properties);
	}
}
