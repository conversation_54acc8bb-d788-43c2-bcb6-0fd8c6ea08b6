/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.blink.connectors.tddl;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.api.functions.AsyncTableFunction;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.plan.schema.IndexKey;
import org.apache.flink.table.plan.stats.TableStats;
import org.apache.flink.table.sources.BatchTableSource;
import org.apache.flink.table.sources.LookupConfig;
import org.apache.flink.table.sources.LookupableTableSource;
import org.apache.flink.table.sources.StreamTableSource;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.TypeConverters;
import org.apache.flink.table.typeutils.BaseRowTypeInfo;

import com.alibaba.blink.connectors.tddl.dim.TddlCacheAllRowFetcher;
import com.alibaba.blink.connectors.tddl.dim.TddlRowFetcher;
import com.alibaba.blink.streaming.connectors.common.LookupFunctionWrapper;
import com.alibaba.blink.streaming.connectors.common.reload.CacheAllReloadConf;
import com.alibaba.blink.streaming.connectors.common.source.SourceUtils;
import com.alibaba.blink.table.cache.CacheConfig;
import com.alibaba.blink.table.cache.CacheStrategy;

public class TddlTableSource implements
		BatchTableSource<BaseRow>, StreamTableSource<BaseRow>, LookupableTableSource<BaseRow> {

	private final String sqlTableName;
	private final RichTableSchema richSchema;
	private final BaseRowTypeInfo rowTypeInfo;
	private final TddlConnectionParam param;
	private final CacheStrategy cacheStrategy;
	private final CacheConfig cacheConfig;

	public TddlTableSource(
			String sqlTableName,
			RichTableSchema schema,
			TddlConnectionParam param,
			CacheConfig cacheConfig) {

		this.richSchema = schema;
		this.sqlTableName = sqlTableName;
		this.rowTypeInfo = TypeConverters.toBaseRowTypeInfo(schema.getResultType());
		this.param = param;
		this.cacheStrategy = cacheConfig.getCacheStrategy();
		this.cacheConfig = cacheConfig;
	}

	@Override
	public TableFunction<BaseRow> getLookupFunction(int[] lookupKeys) {
		IndexKey indexKey = SourceUtils.getSelectedIndexKey(richSchema, lookupKeys);
		if (cacheStrategy.isAllCache()) {
			CacheAllReloadConf reloadConf = new CacheAllReloadConf(
					cacheConfig.getTimeRangeBlacklist(),
					cacheConfig.getCacheScanLimit(), cacheStrategy.getTtlMs());
			return new LookupFunctionWrapper(new TddlCacheAllRowFetcher(
					sqlTableName,
					indexKey,
					rowTypeInfo,
					reloadConf,
					param));
		} else {
			return new LookupFunctionWrapper(new TddlRowFetcher(
					sqlTableName,
					cacheStrategy,
					indexKey,
					rowTypeInfo,
					param));
		}
	}

	@Override
	public AsyncTableFunction<BaseRow> getAsyncLookupFunction(int[] lookupKeys) {
		throw new UnsupportedOperationException("TDDL does not support async join currently");
	}

	@Override
	public LookupConfig getLookupConfig() {
		return new LookupConfig();
	}

	@Override
	public DataStream<BaseRow> getDataStream(StreamExecutionEnvironment execEnv) {
		throw new UnsupportedOperationException("TDDL does not support getDataStream");
	}

	@Override
	public TableSchema getTableSchema() {
		return SourceUtils.toTableSchema(richSchema);
	}

	@Override
	public DataType getReturnType() {
		return SourceUtils.toDataType(richSchema);
	}

	@Override
	public String explainSource() {
		return "TDDL: [" + sqlTableName + "]";
	}

	@Override
	public TableStats getTableStats() {
		return null;
	}

	@Override
	public DataStream<BaseRow> getBoundedStream(StreamExecutionEnvironment streamEnv) {
		throw new UnsupportedOperationException("TDDL does not support getBoundedStream");
	}
}

