/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.blink.connectors.tddl.dim;

import com.alibaba.blink.connectors.mysql.dim.AbstractMySqlCacheAllRowFetcher;
import com.alibaba.blink.connectors.tddl.TddlConnectionParam;
import com.alibaba.blink.streaming.connectors.common.errcode.ConnectorErrors;
import com.alibaba.blink.streaming.connectors.common.reload.CacheAllReloadConf;
import com.alibaba.blink.streaming.connectors.common.util.ConnectionPool;

import org.apache.flink.table.plan.schema.IndexKey;
import org.apache.flink.table.typeutils.BaseRowTypeInfo;

import com.taobao.diamond.identify.CredentialService;
import com.taobao.diamond.identify.Credentials;

import com.taobao.tddl.client.jdbc.TDataSource;
import com.taobao.tddl.common.model.lifecycle.Lifecycle;
import com.taobao.tddl.jdbc.group.TGroupDataSource;

import org.apache.commons.lang3.StringUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;

import java.sql.Connection;

public class TddlCacheAllRowFetcher extends AbstractMySqlCacheAllRowFetcher {
	private static final long serialVersionUID = 3588469531667383107L;
	private static Logger LOG = LoggerFactory.getLogger(TddlCacheAllRowFetcher.class);

	private static final ConnectionPool<DataSource> dataSourcePool = new ConnectionPool<>();

	private final TddlConnectionParam param;
	private transient DataSource dataSource = null;


	public TddlCacheAllRowFetcher(
			String sqlTableName,
			IndexKey index,
			BaseRowTypeInfo rowTypeInfo,
			CacheAllReloadConf reloadConf,
			TddlConnectionParam param) {
		super(sqlTableName, param.getTableName(), index, reloadConf, rowTypeInfo, param.getMaxRetries(), param.getDialect());
		this.param = param;
		setMaxFetchResults(param.getMaxFetchResult());
	}


	protected Connection connectToTable() {
		try {
			synchronized (TddlRowFetcher.class) {
				System.setProperty("tddl.version.check", "false");
				if (dataSourcePool.contains(sqlTableName)) {
					dataSource = dataSourcePool.get(sqlTableName);
				} else {
					if (!StringUtils.isEmpty(param.getAccessKey()) && !StringUtils.isEmpty(param.getSecretKey())) {
						CredentialService credentialService = CredentialService.getInstance();
						credentialService.setCredential(new Credentials(param.getAccessKey(), param.getSecretKey()));
					}
					if (!StringUtils.isEmpty(param.getDbGroupKey())) {
						TGroupDataSource dataSource = new TGroupDataSource();
						dataSource.setDbGroupKey(param.getDbGroupKey());
						dataSource.setAppName(param.getAppName());
						if(!StringUtils.isEmpty(param.getUnitName())){
							dataSource.setUnitName(param.getUnitName());
						}
						dataSource.init();
						this.dataSource = dataSource;
					} else {
						TDataSource dataSource = new TDataSource();
						dataSource.setAppName(param.getAppName());
						dataSource.setSharding(param.isSharding());
						dataSource.setDynamicRule(param.isSharding());
						if (!StringUtils.isEmpty(param.getUnitName())) {
							dataSource.setUnitName(param.getUnitName());
						}
						dataSource.init();
						this.dataSource = dataSource;
					}
					dataSourcePool.put(sqlTableName, dataSource);
				}
				return dataSource.getConnection();
			}
		} catch (Exception e) {
			LOG.error("Exception while creating connection to TDDL.", e);
			throw new RuntimeException(ConnectorErrors.INST.rdsGetConnectionError("TDDL"), e);
		}
	}

	@Override
	protected void closeDataSource() {
		if (dataSourcePool.remove(sqlTableName) && dataSource != null) {
			((Lifecycle) dataSource).destroy();
			dataSource = null;
		}
	}
}
