package com.aliyun.wormhole.qanat.blink.udtf;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson.JSON;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.tuple.Tuple7;
import org.apache.flink.table.functions.TableFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatYidaToTagsV2UDTF extends TableFunction<Tuple7<String,String,String,String,String,String,String>> {
    
    private final static Logger LOG = LoggerFactory.getLogger(QanatYidaToTagsV2UDTF.class);
    
    private final static String [] COLUMNS = {"cbm_id","cbm_name","cid","main_app","y19_it_2019_cost","y19_yun_in_gaap","y19_yun_in_ecs_num","y19_yun_out_gaap","y19_yun_out_ecs_num","y19_yun_in_cdn_gaap","y19_yun_in_cdn_num","y19_yun_out_cdn_gaap","y19_yun_out_cdn_num","y19_yun_in_idc_gaap","y19_yun_in_idc_num","y19_yun_in_other_gaap","y19_yun_in_other_tips","y19_yun_total_tips","y20_it_2020_cost","y20_yun_in_gaap","y20_yun_in_ecs_num","y20_yun_out_gaap","y20_yun_out_ecs_num","y20_yun_in_cdn_gaap","y20_yun_in_cdn_num","y20_yun_out_cdn_gaap","y20_yun_out_cdn_num","y20_yun_in_idc_gaap","y20_yun_in_idc_num","y20_yun_in_other_gaap","y20_yun_in_other_tips","y20_yun_total_tips","y19_tecent_is_not","y19_tecent_it_2019_cost","y20_tecent_it_goal","y19_tecent_sales_name","y19_tecent_pdsa_name","y19_tecent_yun_in_gaap","y19_tecent_yun_in_ecs_num","y19_tecent_yun_out_gaap","y19_tecent_yun_out_ecs_num","y19_tecent_yun_in_cdn_gaap","y19_tecent_yun_in_cdn_num","y19_tecent_yun_out_cdn_gaap","y19_tecent_yun_out_cdn_num","y19_tecent_yun_in_heishi_gaap","y19_tecent_yun_in_heishi_num","y19_tecent_yun_in_other_gaap","y19_tecent_yun_in_other_tips","y19_tecent_yun_in_zhekou","y19_tecent_yun_in_cdn_gaap_m","y19_tecent_yun_out_gaap_unit","y19_tecent_yun_out_zhekou_info","y19_tecent_yun_total_tips","y19_other_is_not","y19_other_name","y19_other_gaap","y19_huawei_it_2019_cost","y20_huawei_it_goal","y19_huawei_sales_name","y19_huawei_pdsa_name","y19_huawei_yun_in_gaap","y19_huawei_yun_in_ecs_num","y19_huawei_yun_out_gaap","y19_huawei_yun_out_ecs_num","y19_huawei_yun_in_cdn_gaap","y19_huawei_yun_in_cdn_num","y19_huawei_yun_out_cdn_gaap","y19_huawei_yun_out_cdn_num","y19_huawei_yun_in_other_gaap","y19_huawei_yun_in_other_tips","y19_huawei_yun_in_zhekou","y19_huawei_yun_in_cdn_gaap_m","y19_huawei_yun_out_gaap_unit","y19_huawei_yun_out_zhekou_info","y19_huawei_yun_total_tips","y19_aws_it_2019_cost","y20_aws_it_goal","y19_aws_sales_name","y19_aws_pdsa_name","y19_aws_yun_in_gaap","y19_aws_yun_in_ecs_num","y19_aws_yun_out_gaap","y19_aws_yun_out_ecs_num","y19_aws_yun_in_cdn_gaap","y19_aws_yun_in_cdn_num","y19_aws_yun_out_cdn_gaap","y19_aws_yun_out_cdn_num","y19_aws_yun_in_other_gaap","y19_aws_yun_in_other_tips","y19_aws_yun_in_zhekou","y19_aws_yun_in_cdn_gaap_m","y19_aws_yun_out_gaap_unit","y19_aws_yun_out_zhekou_info","y19_aws_yun_total_tips","y19_ucloud_it_2019_cost","y20_ucloud_it_goal","y19_ucloud_sales_name","y19_ucloud_pdsa_name","y19_ucloud_yun_in_gaap","y19_ucloud_yun_in_ecs_num","y19_ucloud_yun_out_gaap","y19_ucloud_yun_out_ecs_num","y19_ucloud_yun_in_cdn_gaap","y19_ucloud_yun_in_cdn_num","y19_ucloud_yun_out_cdn_gaap","y19_ucloud_yun_out_cdn_num","y19_ucloud_yun_in_other_gaap","y19_ucloud_yun_in_other_tips","y19_ucloud_yun_in_zhekou","y19_ucloud_yun_in_cdn_gaap_m","y19_ucloud_yun_out_gaap_unit","y19_ucloud_yun_out_zhekou_info","y19_ucloud_yun_total_tips","y19_jinshan_it_2019_cost","y20_jinshan_it_goal","y19_jinshan_sales_name","y19_jinshan_pdsa_name","y19_jinshan_yun_in_gaap","y19_jinshan_yun_in_ecs_num","y19_jinshan_yun_out_gaap","y19_jinshan_yun_out_ecs_num","y19_jinshan_yun_in_cdn_gaap","y19_jinshan_yun_in_cdn_num","y19_jinshan_yun_out_cdn_gaap","y19_jinshan_yun_out_cdn_num","y19_jinshan_yun_in_other_gaap","y19_jinshan_yun_in_other_tips","y19_jinshan_yun_in_zhekou","y19_jinshan_yun_in_cdn_gaap_m","y19_jinshan_yun_out_gaap_unit","y19_jinshan_yun_out_zhekou_info","y19_jinshan_yun_total_tips","y20_out_it_country","y20_out_it_dongnanya","y20_out_it_rihan","y20_out_it_india","y20_out_it_northua","y20_out_it_southa","y20_out_it_ouzhou","y20_out_it_eluosi","y20_out_it_othercountry","y20_is_correct"};

    private final static Map<String, String> areaMap = new HashMap<String, String>() {
    	{
    		put("dongnanya", "SoutheastAsia");
    		put("rihan", "JapanAndKorea");
    		put("india", "India");
    		put("northua", "NorthAmerica");
    		put("southa", "SouthAmerica");
    		put("ouzhou", "Europe");
    		put("eluosi", "Russia");
    		put("other", "other");
    	}
    };
    
    public void eval(String operateType, String ... values) {
        LOG.info("eval({})", JSON.toJSONString(values));
        Map<String, String> data = new HashMap<>();
        if (COLUMNS.length != values.length) {
            return;
        } else {
            	for (int i=0; i < COLUMNS.length; i++) {
            		String colName = COLUMNS[i];
            		data.put(colName, values[i]);
            	}
        }
        try {
        	    String cid  = data.get("cid");
	        String tagDomainCode = "domain_model";
	        String objectDomainCode = "domain_model";

	        
	        //===================== model_out_it ============================
	        String objectCode = "model_out_it";
	        List<Tuple7<String,String,String,String,String,String,String>> tuple7List = new ArrayList<>();
	        
	        if (StringUtils.isNotBlank(data.get("y20_out_it_dongnanya"))) {
	            String country = "dongnanya";
		        String outItObjectId = "yida-" + "moi_" + country + "_" + cid;
		        tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, outItObjectId, data.get("cid"), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, outItObjectId, "2020", operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "it_area", objectDomainCode, objectCode, outItObjectId, areaMap.get(country), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "out_it_cost", objectDomainCode, objectCode, outItObjectId, formatNumber(data.get("y20_out_it_dongnanya")), operateType));
	        }
            
            if (StringUtils.isNotBlank(data.get("y20_out_it_rihan"))) {
                String country = "rihan";
                String outItObjectId = "yida-" + "moi_" + country + "_" + cid;
                tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, outItObjectId, data.get("cid"), operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, outItObjectId, "2020", operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "it_area", objectDomainCode, objectCode, outItObjectId, areaMap.get(country), operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "out_it_cost", objectDomainCode, objectCode, outItObjectId, formatNumber(data.get("y20_out_it_rihan")), operateType));
            }
            
            if (StringUtils.isNotBlank(data.get("y20_out_it_india"))) {
                String country = "india";
                String outItObjectId = "yida-" + "moi_" + country + "_" + cid;
                tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, outItObjectId, data.get("cid"), operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, outItObjectId, "2020", operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "it_area", objectDomainCode, objectCode, outItObjectId, areaMap.get(country), operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "out_it_cost", objectDomainCode, objectCode, outItObjectId, formatNumber(data.get("y20_out_it_india")), operateType));
            }
            
            if (StringUtils.isNotBlank(data.get("y20_out_it_northua"))) {
                String country = "northua";
                String outItObjectId = "yida-" + "moi_" + country + "_" + cid;
                tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, outItObjectId, data.get("cid"), operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, outItObjectId, "2020", operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "it_area", objectDomainCode, objectCode, outItObjectId, areaMap.get(country), operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "out_it_cost", objectDomainCode, objectCode, outItObjectId, formatNumber(data.get("y20_out_it_northua")), operateType));
            }
            
            if (StringUtils.isNotBlank(data.get("y20_out_it_southa"))) {
                String country = "southa";
                String outItObjectId = "yida-" + "moi_" + country + "_" + cid;
                tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, outItObjectId, data.get("cid"), operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, outItObjectId, "2020", operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "it_area", objectDomainCode, objectCode, outItObjectId, areaMap.get(country), operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "out_it_cost", objectDomainCode, objectCode, outItObjectId, formatNumber(data.get("y20_out_it_southa")), operateType));
            }
            
            if (StringUtils.isNotBlank(data.get("y20_out_it_ouzhou"))) {
                String country = "ouzhou";
                String outItObjectId = "yida-" + "moi_" + country + "_" + cid;
                tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, outItObjectId, data.get("cid"), operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, outItObjectId, "2020", operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "it_area", objectDomainCode, objectCode, outItObjectId, areaMap.get(country), operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "out_it_cost", objectDomainCode, objectCode, outItObjectId, formatNumber(data.get("y20_out_it_ouzhou")), operateType));
            }
            
            if (StringUtils.isNotBlank(data.get("y20_out_it_eluosi"))) {
                String country = "eluosi";
                String outItObjectId = "yida-" + "moi_" + country + "_" + cid;
                tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, outItObjectId, data.get("cid"), operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, outItObjectId, "2020", operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "it_area", objectDomainCode, objectCode, outItObjectId, areaMap.get(country), operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "out_it_cost", objectDomainCode, objectCode, outItObjectId, formatNumber(data.get("y20_out_it_eluosi")), operateType));
            }
            
            if (StringUtils.isNotBlank(data.get("y20_out_it_othercountry"))) {
                String country = "other";
                String outItObjectId = "yida-" + "moi_" + country + "_" + cid;
                tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, outItObjectId, data.get("cid"), operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, outItObjectId, "2020", operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "it_area", objectDomainCode, objectCode, outItObjectId, areaMap.get(country), operateType));
                tuple7List.add(Tuple7.of(tagDomainCode, "out_it_cost", objectDomainCode, objectCode, outItObjectId, formatNumber(data.get("y20_out_it_othercountry")), operateType));
            }
            
		    if (tuple7List.size() > 0) {
        	        for (Tuple7<String,String,String,String,String,String,String> tuple7 : tuple7List) {
        	            collect(tuple7);
        	        }
		    }
        } catch (Exception e) {
        	    LOG.error("QanatYidaToTagsUDTF failed", e);
        }
    }
    
    private static String formatNumber(String num) {
        	if (StringUtils.isBlank(num)) {
        		return num;
        	}
        	String reg = "^[0-9]\\d*(\\.0+)?$";
        	Pattern p = Pattern.compile(reg);
        	Matcher m = p.matcher(num);
        	while (m.find()) {
        		if (m.group(1) == null) {
        			break;
        		}
        		num = num.replace(m.group(1), "");
        		break;
        	}
        	return num;
    }
}