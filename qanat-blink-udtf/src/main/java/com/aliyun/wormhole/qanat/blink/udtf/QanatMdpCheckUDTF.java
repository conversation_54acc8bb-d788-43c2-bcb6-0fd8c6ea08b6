package com.aliyun.wormhole.qanat.blink.udtf;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.aliyun.wormhole.qanat.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.datasource.RdsConnectionParam;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.TableFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatMdpCheckUDTF extends TableFunction<String> {

    private final static Logger LOG = LoggerFactory.getLogger(QanatMdpCheckUDTF.class);

    public void eval(String tableName, String objectBizId, String tagCode, String tagValue, Long isDeleted) {
    	eval("devata_rtdw", tableName, objectBizId, tagCode, tagValue, isDeleted);
    }

    public void eval(String dbName, String tableName, String objectBizId, String tagCode, String tagValue, Long isDeleted) {
        LOG.info("eval({},{},{},{},{})", tableName, objectBizId, tagCode, tagValue, isDeleted);
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {

        	String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
            LOG.info("dbMetaStr={}", dbMetaStr);
            if (StringUtils.isBlank(dbMetaStr)) {
            	return;
            }
            JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
            RdsConnectionParam param = new RdsConnectionParam();
            param.setUrl(dbMetaJson.getString("jdbcUrl"))
    	        .setUserName(dbMetaJson.getString("username"))
    	        .setPassword(dbMetaJson.getString("password"));
        	conn = QanatDatasourceHandler.connectToTable(param);
            String sql = String.format("SELECT %s FROM %s WHERE __pk__='%s'", tagCode, tableName, objectBizId);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            if (rs.next()) {
                Map<String, Object> data = new HashMap<>();
                int columnCount = rs.getMetaData().getColumnCount();
                for (int i = 1; i <= columnCount; i++) {
                    Object value = rs.getObject(i);
                    if (value == null) {
                        if (StringUtils.isBlank(tagValue)) {
                            collect("");
                        } else {
                            if (isDeleted != 0L) {
                                collect("");
                            } else {
                                collect(tagCode + "`" + tagValue + "`" + value);
                            }
                        }
                    } else {
                        if (value.equals(tagValue)) {
                            collect("");
                        } else {
                            collect(tagCode + "`" + tagValue + "`" + value);
                        }
                    }
                }
            } else {
                if (isDeleted != 0L) {
                    collect("");
                } else {
                    collect(tagCode + "`" + tagValue + "`MISS");
                }
            }
        } catch (Exception e) {
            LOG.error("sql exec failed:{}", e.getMessage(), e);
        } finally {
        	if (ps != null) {
        		try {
        			ps.close();
        		} catch(Exception e) {}
        		ps = null;
        	}
        	if (conn != null) {
        		try {
        			conn.close();
        		} catch(Exception e) {}
        		conn = null;
        	}
        }
    }
}
