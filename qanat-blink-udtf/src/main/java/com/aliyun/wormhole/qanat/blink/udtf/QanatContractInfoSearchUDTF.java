package com.aliyun.wormhole.qanat.blink.udtf;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.TableFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatContractInfoSearchUDTF extends TableFunction<String> {
    
    private final static Logger LOG = LoggerFactory.getLogger(QanatContractInfoSearchUDTF.class);

    public void eval(String content) {
        LOG.info("eval({})", content);
        if (StringUtils.isBlank(content)) {
            return;
        }
        try {
            String [] contracts = content.split(",");
            for (String contract : contracts) {
                Map<String, String> data = new HashMap<>();
                String [] effTimeTokens = contract.split("\\$");
                data.put("contract_effect_time", effTimeTokens[1]);
                String [] statusTokens = effTimeTokens[0].split("\\@");
                data.put("contract_status", statusTokens[1]);
                String [] typeTokens = statusTokens[0].split("\\|");
                data.put("contract_type", typeTokens[1]);
                data.put("contract_id", typeTokens[0]);
                String json = JSON.toJSONString(data);
                LOG.info("json={}", json);
                collect(json);
            }
        } catch (Throwable e) {
            LOG.info("parse error:" + e);
        }
    }
    
    public static void main(String [] args) {
        QanatContractInfoSearchUDTF udtf = new QanatContractInfoSearchUDTF();
        udtf.eval("21786|1@8$1563292800,21905|1@1$1563724800,建业新生活数字化转型咨询项目|2@3$1558627200");
    }
}
