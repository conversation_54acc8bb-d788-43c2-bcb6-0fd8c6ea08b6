package com.aliyun.wormhole.qanat.blink.udtf;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;

import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.flink.table.functions.TableFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatPplGaapBuildUDTF extends TableFunction<String> {

    private final static Logger log = LoggerFactory.getLogger(QanatPplGaapBuildUDTF.class);
    //gaap所有月份
    public static String[] months = {"04", "05", "06", "07", "08", "09", "10", "11", "12", "01", "02", "03"};

    public void eval(Long pid, String incomeType, String fy, Long m1, <PERSON> m2, <PERSON> m3, <PERSON> m4, <PERSON> m5, 
        <PERSON> m6, <PERSON> m7, <PERSON> m8, <PERSON> m9, <PERSON> m10, <PERSON> m11, Long m12, Long sum) {
        Long[] gaaps = new Long[] {m1, m2, m3, m4, m5, m6, m7, m8, m9, m10, m11, m12};
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
            Date dateFy;
            try {
                dateFy = sdf.parse(fy);
            } catch (ParseException e) {
                log.error(" fy 转换时间失败 gaap:{},{}", fy, e);
                return;
            }
            Date preFy = DateUtils.addYears(dateFy, -1);
            String fyStr = DateFormatUtils.format(dateFy, "yyyy");
            String preFyStr = DateFormatUtils.format(preFy, "yyyy");
            incomeType = incomeType == null ? "all" : incomeType;
            for (int i = 1; i <= 12; i++) {
                String month = (i <= 9) ? (preFyStr + "-" + months[i - 1]) : (fyStr + "-" + months[i - 1]);
                Long gaapValue = gaaps[i - 1] == null ? 0 : gaaps[i - 1];
                Map<String, Object> data = new HashMap<>();
                data.put("fy", fyStr);
                data.put("month", month);
                data.put("project_id", pid);
                data.put("income_type", incomeType);
                data.put("gaap", gaapValue);
                String dataStr = JSON.toJSONString(data);
                collect(dataStr);
            }
            Long gaapValue = sum == null ? 0 : sum;

            Map<String, Object> data = new HashMap<>();
            data.put("fy", fyStr);
            data.put("month", fyStr);
            data.put("project_id", pid);
            data.put("income_type", incomeType);
            data.put("gaap", gaapValue);
            String dataStr = JSON.toJSONString(data);
            collect(dataStr);
        } catch (Exception e) {
            log.error("gaap transform failed:{}", e.getMessage(), e);
        }
    }
}
