package com.aliyun.wormhole.qanat.blink.udtf;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.TableFunction;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.apache.http.nio.entity.NStringEntity;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatQueryEsUDTF extends TableFunction<String> {
    
    private final static Logger LOG = LoggerFactory.getLogger(QanatQueryEsUDTF.class);
    RestHighLevelClient client = null;

    private synchronized void init(String traceId, String dbName) {
    	if (client != null) {
    		return;
    	}
    	int retries = 3;
    	while (retries > 0) {
	        try {
	        	
	            String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
	            LOG.info("dbMetaStr={}", dbMetaStr);
	            if (StringUtils.isBlank(dbMetaStr)) {
	            	continue;
	            }
	            JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
	            HttpHost httpHost = new HttpHost(dbMetaJson.getString("host"), dbMetaJson.getInteger("port"));
	            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
	            credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(dbMetaJson.getString("username"), dbMetaJson.getString("password")));
	            RestClientBuilder builder = RestClient.builder(httpHost);
	            builder.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
	                @Override
	                public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
	                    return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
	                }
	            }).build();
	            client = new RestHighLevelClient(builder);
	            LOG.info("{} dbName={} RestHighLevelClient inited", traceId, dbName);
	            break;
	        } catch (Exception e) {
	            LOG.error("{} RestHighLevelClient init failed {} times due to {}", traceId, retries, e.getMessage(), e);
	            retries--;
	            try {
					Thread.sleep(200);
				} catch (InterruptedException e1) {
				}
	        }
    	}
    	if (client == null) {
    		throw new RuntimeException("es connection init failed");
    	}
    }

    public void eval(String traceId, String dbName, String index, String type, String dsl) {
        LOG.info("eval({},{},{},{},{})", traceId, dbName, index, type, dsl);
        init(traceId, dbName);
        List<Map<String, Object>> datas = query(traceId, index, type, dsl);
        if (CollectionUtils.isNotEmpty(datas)) {
	        for (Map<String, Object> data : datas) {
	            String dataStr = JSON.toJSONString(data);
	            LOG.info("{} collect data:{}", traceId, dataStr);
	            collect(dataStr);
	        }
        }
    }

    private List<Map<String, Object>> query(String traceId, String index, String type, String dsl) {
        List<Map<String, Object>> datas = new ArrayList<>();
        try {
            HttpEntity entity = new NStringEntity(dsl, ContentType.APPLICATION_JSON);
            Response resp = client.getLowLevelClient().performRequest("GET", "/" + index + "/" + type + "/_search", Collections.emptyMap(), entity);
            String content = EntityUtils.toString(resp.getEntity());
            LOG.info("{} content={}", traceId, content);
            JSONObject json = JSON.parseObject(content);
            JSONObject hitsObject = json.getJSONObject("hits");
            if (hitsObject != null) {
                JSONArray hitsArray = hitsObject.getJSONArray("hits");
                for (int i = 0; i < hitsArray.size(); i++) {
                    JSONObject hitObject = hitsArray.getJSONObject(i);
                    datas.add(hitObject);
                }
            }
        } catch (Exception e) {
            LOG.error("{} performRequest failed", traceId, e);
        }
        return datas;
    }
}