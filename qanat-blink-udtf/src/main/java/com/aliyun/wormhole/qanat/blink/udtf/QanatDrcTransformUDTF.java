package com.aliyun.wormhole.qanat.blink.udtf;

import com.alibaba.fastjson.JSON;
import com.aliyun.wormhole.qanat.drc.event.DataMessage;
import com.aliyun.wormhole.qanat.drc.event.DbEventInfo;
import com.aliyun.wormhole.qanat.drc.event.DrcMessageParser;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.TableFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatDrcTransformUDTF extends TableFunction<String> {
    
    private final static Logger LOG = LoggerFactory.getLogger(QanatDrcTransformUDTF.class);

    public void eval(String msg, String tableName) {
        LOG.info("eval({},{})", msg, tableName);
        if (StringUtils.isBlank(msg) || (StringUtils.isBlank(tableName))) {
            return;
        }
        DataMessage.Record record = JSON.parseObject(msg, DataMessage.Record.class);
        String currType = record.getOpt().toString();
        DbEventInfo dbEventInfo = null;
        try {
            if (tableName.equalsIgnoreCase(getLogicName(record.getTablename()))) {
                dbEventInfo = DrcMessageParser.parse(record, currType);
                if(dbEventInfo != null) {
                    String json = JSON.toJSONString(dbEventInfo);
                    LOG.info("json={}", json);
                    collect(json);
                }
            }
        } catch (Throwable e) {
            LOG.info("parse error:" + e);
        }
    }
    
    public void eval(String msg) {
        LOG.info("eval({})", msg);
        if (StringUtils.isBlank(msg)) {
            return;
        }
        DataMessage.Record record = JSON.parseObject(msg, DataMessage.Record.class);
        String currType = record.getOpt().toString();
        DbEventInfo dbEventInfo = null;
        try {
            dbEventInfo = DrcMessageParser.parse(record, currType);
            if(dbEventInfo != null) {
                String json = JSON.toJSONString(dbEventInfo);
                LOG.info("json={}", json);
                collect(json);
            }
        } catch (Throwable e) {
            LOG.info("parse error:" + e);
        }
    }

    private String getLogicName(String realName) {
        String[] tokens = realName.split("_");
        String[] targetTokens = new String[tokens.length - 1];
        System.arraycopy(tokens, 0, targetTokens, 0, tokens.length - 1);
        return StringUtils.join(targetTokens, "_");
    }
    
    public static void main(String [] args) {
        String msg = "{\"attributes\":{\"checkpoint\":\"4584003@1\",\"instance\":\"11.8.110.16-3306\",\"fields_enc\":\",utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,,,,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,utf8mb4,,utf8mb4\",\"source_category\":\"full_recorded\",\"source_type\":\"mysql\",\"usec\":\"0\",\"logevent\":\"1\",\"server_id\":\"8168786938172072524\",\"table_name\":\"devata_organization_odps_0195\",\"record_type\":\"insert\",\"prev_id\":\"72057594047955417\",\"threadid\":\"453264\",\"record_id\":\"72057594047955420\",\"record_encoding\":\"utf8mb4\",\"prev_server_id\":\"8168786938172072524\",\"db\":\"aliyun_devata_customer_tddl_0003\",\"timestamp\":\"1575870562\",\"primary\":\"id\"},\"checkpoint\":\"4584003@1\",\"dbType\":\"MYSQL\",\"dbname\":\"aliyun_devata_customer_tddl_0003\",\"fieldCount\":59,\"fieldList\":[{\"changeValue\":true,\"encoding\":\"\",\"fieldname\":\"id\",\"length\":5,\"name\":\"id\",\"primary\":true,\"primaryKey\":true,\"type\":\"INT64\",\"value\":{\"bytes\":\"MzAzMDg=\",\"len\":5,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"org_name\",\"length\":36,\"name\":\"org_name\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"5LiK5rW36aKG5rGH5L+h5oGv5oqA5pyv5pyJ6ZmQ5YWs5Y+4\",\"len\":36,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"org_name_eng\",\"length\":-1,\"name\":\"org_name_eng\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"social_credit_code\",\"length\":18,\"name\":\"social_credit_code\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"OTEzMTAxMDg3ODM2MDU3MzQy\",\"len\":18,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"org_type\",\"length\":44,\"name\":\"org_type\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"5pyJ6ZmQ6LSj5Lu75YWs5Y+4KOiHqueEtuS6uuaKlei1hOaIluaOp+iCoSk=\",\"len\":44,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"license_number\",\"length\":15,\"name\":\"license_number\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"MzEwMTA4MDAwMzc4NDQy\",\"len\":15,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"\",\"fieldname\":\"stored_time\",\"length\":19,\"name\":\"stored_time\",\"primary\":false,\"primaryKey\":false,\"type\":\"DATETIME\",\"value\":{\"bytes\":\"MjAxOS0xMC0wOCAyMDoxMjo0MQ==\",\"len\":19,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"\",\"fieldname\":\"gmt_create\",\"length\":19,\"name\":\"gmt_create\",\"primary\":false,\"primaryKey\":false,\"type\":\"DATETIME\",\"value\":{\"bytes\":\"MjAxOS0xMC0wNyAwMDowMDowMA==\",\"len\":19,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"\",\"fieldname\":\"gmt_modified\",\"length\":19,\"name\":\"gmt_modified\",\"primary\":false,\"primaryKey\":false,\"type\":\"DATETIME\",\"value\":{\"bytes\":\"MjAxOS0xMC0wNyAwMDowMDowMA==\",\"len\":19,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"reg_cap_cur\",\"length\":9,\"name\":\"reg_cap_cur\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"5Lq65rCR5biB\",\"len\":9,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"reg_cap\",\"length\":9,\"name\":\"reg_cap\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"NTAuMDAwMDAw\",\"len\":9,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"rec_cap\",\"length\":-1,\"name\":\"rec_cap\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"legal_name\",\"length\":6,\"name\":\"legal_name\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"5byg5re8\",\"len\":6,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"es_date\",\"length\":10,\"name\":\"es_date\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"MjAwNS0xMi0wOA==\",\"len\":10,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"op_from\",\"length\":10,\"name\":\"op_from\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"MjAwNS0xMi0wOA==\",\"len\":10,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"op_to\",\"length\":10,\"name\":\"op_to\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"MjAyNS0xMi0wNw==\",\"len\":10,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"ent_status\",\"length\":6,\"name\":\"ent_status\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"5Zyo6JCl\",\"len\":6,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"chg_date\",\"length\":19,\"name\":\"chg_date\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"MjAxOS0wMy0yNiAxNjowMDowMA==\",\"len\":19,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"can_date\",\"length\":-1,\"name\":\"can_date\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"rev_date\",\"length\":-1,\"name\":\"rev_date\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"allow_busi_item\",\"length\":549,\"name\":\"allow_busi_item\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"5Zyo6K6h566X5py644CB6YCa6K6v44CB572R57uc5LiT5Lia6aKG5Z+f5YaF5LuO5LqL5oqA5pyv5byA5Y+R44CB5oqA5pyv6L2s6K6p44CB5oqA5pyv5pyN5Yqh44CB5oqA5pyv5ZKo6K+i77yM572R57uc5bel56iL77yM6KeG6aKR57O757uf5bel56iL77yM57yd57qr6K6+5aSH5a6J6KOF5pyN5Yqh77yb6K6+6K6h44CB5Yi25L2c44CB5Luj55CG44CB5Y+R5biD5ZCE57G75bm/5ZGK77yM5Zu+5paH6K6+6K6h44CB5Yi25L2c77yM5ZWG5Yqh5L+h5oGv5ZKo6K+i77yM5LyB5Lia5b2i6LGh562W5YiS77yM5biC5Zy66JCl6ZSA562W5YiS77yM5bGV6KeI5bGV56S65pyN5Yqh77yb6K6h566X5py66L2v56Gs5Lu277yM5Luq5Zmo5Luq6KGo77yM55S15a2Q5Lqn5ZOB77yM6YCa6K6v5Lqn5ZOB5Y+K6K6+5aSH77yM5Yqe5YWs55So5ZOB77yM55S16ISR6ICX5p2Q77yM55S157q/55S157yG55qE5om55Y+R6Zu25ZSu44CC44CQ5L6d5rOV6aG757uP5om55YeG55qE6aG555uu77yM57uP55u45YWz6YOo6Zeo5om55YeG5ZCO5pa55Y+v5byA5bGV57uP6JCl5rS75Yqo44CR\",\"len\":549,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"comm_busi_item\",\"length\":549,\"name\":\"comm_busi_item\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"5Zyo6K6h566X5py644CB6YCa6K6v44CB572R57uc5LiT5Lia6aKG5Z+f5YaF5LuO5LqL5oqA5pyv5byA5Y+R44CB5oqA5pyv6L2s6K6p44CB5oqA5pyv5pyN5Yqh44CB5oqA5pyv5ZKo6K+i77yM572R57uc5bel56iL77yM6KeG6aKR57O757uf5bel56iL77yM57yd57qr6K6+5aSH5a6J6KOF5pyN5Yqh77yb6K6+6K6h44CB5Yi25L2c44CB5Luj55CG44CB5Y+R5biD5ZCE57G75bm/5ZGK77yM5Zu+5paH6K6+6K6h44CB5Yi25L2c77yM5ZWG5Yqh5L+h5oGv5ZKo6K+i77yM5LyB5Lia5b2i6LGh562W5YiS77yM5biC5Zy66JCl6ZSA562W5YiS77yM5bGV6KeI5bGV56S65pyN5Yqh77yb6K6h566X5py66L2v56Gs5Lu277yM5Luq5Zmo5Luq6KGo77yM55S15a2Q5Lqn5ZOB77yM6YCa6K6v5Lqn5ZOB5Y+K6K6+5aSH77yM5Yqe5YWs55So5ZOB77yM55S16ISR6ICX5p2Q77yM55S157q/55S157yG55qE5om55Y+R6Zu25ZSu44CC44CQ5L6d5rOV6aG757uP5om55YeG55qE6aG555uu77yM57uP55u45YWz6YOo6Zeo5om55YeG5ZCO5pa55Y+v5byA5bGV57uP6JCl5rS75Yqo44CR\",\"len\":549,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"op_scope\",\"length\":549,\"name\":\"op_scope\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"5Zyo6K6h566X5py644CB6YCa6K6v44CB572R57uc5LiT5Lia6aKG5Z+f5YaF5LuO5LqL5oqA5pyv5byA5Y+R44CB5oqA5pyv6L2s6K6p44CB5oqA5pyv5pyN5Yqh44CB5oqA5pyv5ZKo6K+i77yM572R57uc5bel56iL77yM6KeG6aKR57O757uf5bel56iL77yM57yd57qr6K6+5aSH5a6J6KOF5pyN5Yqh77yb6K6+6K6h44CB5Yi25L2c44CB5Luj55CG44CB5Y+R5biD5ZCE57G75bm/5ZGK77yM5Zu+5paH6K6+6K6h44CB5Yi25L2c77yM5ZWG5Yqh5L+h5oGv5ZKo6K+i77yM5LyB5Lia5b2i6LGh562W5YiS77yM5biC5Zy66JCl6ZSA562W5YiS77yM5bGV6KeI5bGV56S65pyN5Yqh77yb6K6h566X5py66L2v56Gs5Lu277yM5Luq5Zmo5Luq6KGo77yM55S15a2Q5Lqn5ZOB77yM6YCa6K6v5Lqn5ZOB5Y+K6K6+5aSH77yM5Yqe5YWs55So5ZOB77yM55S16ISR6ICX5p2Q77yM55S157q/55S157yG55qE5om55Y+R6Zu25ZSu44CC44CQ5L6d5rOV6aG757uP5om55YeG55qE6aG555uu77yM57uP55u45YWz6YOo6Zeo5om55YeG5ZCO5pa55Y+v5byA5bGV57uP6JCl5rS75Yqo44CR\",\"len\":549,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"op_scope_and_form\",\"length\":549,\"name\":\"op_scope_and_form\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"5Zyo6K6h566X5py644CB6YCa6K6v44CB572R57uc5LiT5Lia6aKG5Z+f5YaF5LuO5LqL5oqA5pyv5byA5Y+R44CB5oqA5pyv6L2s6K6p44CB5oqA5pyv5pyN5Yqh44CB5oqA5pyv5ZKo6K+i77yM572R57uc5bel56iL77yM6KeG6aKR57O757uf5bel56iL77yM57yd57qr6K6+5aSH5a6J6KOF5pyN5Yqh77yb6K6+6K6h44CB5Yi25L2c44CB5Luj55CG44CB5Y+R5biD5ZCE57G75bm/5ZGK77yM5Zu+5paH6K6+6K6h44CB5Yi25L2c77yM5ZWG5Yqh5L+h5oGv5ZKo6K+i77yM5LyB5Lia5b2i6LGh562W5YiS77yM5biC5Zy66JCl6ZSA562W5YiS77yM5bGV6KeI5bGV56S65pyN5Yqh77yb6K6h566X5py66L2v56Gs5Lu277yM5Luq5Zmo5Luq6KGo77yM55S15a2Q5Lqn5ZOB77yM6YCa6K6v5Lqn5ZOB5Y+K6K6+5aSH77yM5Yqe5YWs55So5ZOB77yM55S16ISR6ICX5p2Q77yM55S157q/55S157yG55qE5om55Y+R6Zu25ZSu44CC44CQ5L6d5rOV6aG757uP5om55YeG55qE6aG555uu77yM57uP55u45YWz6YOo6Zeo5om55YeG5ZCO5pa55Y+v5byA5bGV57uP6JCl5rS75Yqo44CR\",\"len\":549,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"ent_address\",\"length\":26,\"name\":\"ent_address\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"5rKq5aSq6LevMTEyOOWPt0M177yNNDDlrqQ=\",\"len\":26,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"op_loc\",\"length\":-1,\"name\":\"op_loc\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"reg_org\",\"length\":6,\"name\":\"reg_org\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"MzEwMTA2\",\"len\":6,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"reg_org_code\",\"length\":-1,\"name\":\"reg_org_code\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"reg_org_prov\",\"length\":9,\"name\":\"reg_org_prov\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"5LiK5rW35biC\",\"len\":9,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"reg_org_prov_num\",\"length\":6,\"name\":\"reg_org_prov_num\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"MzEwMDAw\",\"len\":6,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"reg_org_city\",\"length\":18,\"name\":\"reg_org_city\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"5LiK5rW35biC5biC6L6W5Yy6\",\"len\":18,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"reg_org_city_num\",\"length\":6,\"name\":\"reg_org_city_num\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"MzEwMTAw\",\"len\":6,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"reg_org_district\",\"length\":9,\"name\":\"reg_org_district\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"6Z2Z5a6J5Yy6\",\"len\":9,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"reg_org_district_num\",\"length\":8,\"name\":\"reg_org_district_num\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"MzEwMTA2MDA=\",\"len\":8,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"anche_year\",\"length\":-1,\"name\":\"anche_year\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"anche_date\",\"length\":-1,\"name\":\"anche_date\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"industry_name_lv1\",\"length\":30,\"name\":\"industry_name_lv1\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"56eR5a2m56CU56m25ZKM5oqA5pyv5pyN5Yqh5Lia\",\"len\":30,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"industry_code_lv1\",\"length\":1,\"name\":\"industry_code_lv1\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"TQ==\",\"len\":1,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"industry_name_lv2\",\"length\":21,\"name\":\"industry_name_lv2\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"5LiT5Lia5oqA5pyv5pyN5Yqh5Lia\",\"len\":21,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"industry_code_lv2\",\"length\":2,\"name\":\"industry_code_lv2\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"NzQ=\",\"len\":2,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"industry_name_lv3\",\"length\":27,\"name\":\"industry_name_lv3\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"5YW25LuW5LiT5Lia5oqA5pyv5pyN5Yqh5Lia\",\"len\":27,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"industry_code_lv3\",\"length\":3,\"name\":\"industry_code_lv3\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"NzQ5\",\"len\":3,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"industry_name_lv4\",\"length\":36,\"name\":\"industry_name_lv4\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"5YW25LuW5pyq5YiX5piO5LiT5Lia5oqA5pyv5pyN5Yqh5Lia\",\"len\":36,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"industry_code_lv4\",\"length\":4,\"name\":\"industry_code_lv4\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"NzQ5OQ==\",\"len\":4,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"baike_industry\",\"length\":24,\"name\":\"baike_industry\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"6K6h566X5py66L2v5Lu25ZKM5pyN5Yqh\",\"len\":24,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"ent_district\",\"length\":-1,\"name\":\"ent_district\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"ent_email\",\"length\":-1,\"name\":\"ent_email\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"ent_introduce\",\"length\":-1,\"name\":\"ent_introduce\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"ent_phone\",\"length\":-1,\"name\":\"ent_phone\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"ent_site\",\"length\":-1,\"name\":\"ent_site\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"isp_record_time\",\"length\":-1,\"name\":\"isp_record_time\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"organization_no\",\"length\":9,\"name\":\"organization_no\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"NzgzNjA1NzM0\",\"len\":9,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"ori_license_number\",\"length\":-1,\"name\":\"ori_license_number\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"p_algorithm_id\",\"length\":-1,\"name\":\"p_algorithm_id\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"baike_industry_code\",\"length\":4,\"name\":\"baike_industry_code\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"MDQwNA==\",\"len\":4,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"revenue\",\"length\":-1,\"name\":\"revenue\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"profits\",\"length\":-1,\"name\":\"profits\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\"},{\"changeValue\":true,\"encoding\":\"\",\"fieldname\":\"is_del\",\"length\":1,\"name\":\"is_del\",\"primary\":false,\"primaryKey\":false,\"type\":\"INT64\",\"value\":{\"bytes\":\"MA==\",\"len\":1,\"offset\":0}},{\"changeValue\":true,\"encoding\":\"utf8\",\"fieldname\":\"eid\",\"length\":19,\"name\":\"eid\",\"primary\":false,\"primaryKey\":false,\"type\":\"STRING\",\"value\":{\"bytes\":\"MTE4MDcxNjAxMzU3MDU0NDc1MA==\",\"len\":19,\"offset\":0}}],\"firstInLogevent\":true,\"id\":\"72057594047955420\",\"opt\":\"INSERT\",\"prevId\":\"72057594047955417\",\"prevServerSeq\":\"8168786938172072524\",\"primaryKeys\":\"id\",\"queryBack\":false,\"regionId\":\"ylxb\",\"safeTimestamp\":\"1575870562\",\"serverId\":\"11.8.110.16-3306\",\"serverSeq\":\"8168786938172072524\",\"tablename\":\"devata_organization_odps_0195\",\"threadId\":\"453264\",\"timestamp\":\"1575870562\",\"traceInfo\":\"\"}";
        QanatDrcTransformUDTF udtf = new QanatDrcTransformUDTF();
        udtf.eval(msg, "devata_organization_odps");
    }
}
