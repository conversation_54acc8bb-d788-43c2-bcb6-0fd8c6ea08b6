package com.aliyun.wormhole.qanat.blink.udtf;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.flink.table.functions.TableFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatNaviBuildUDTF extends TableFunction<String> {

    private final static Logger log = LoggerFactory.getLogger(QanatNaviBuildUDTF.class);

    public void eval(String content) {
        if (StringUtils.isBlank(content)) {
            return;
        }
        try {
            JSONObject json = JSON.parseObject(content);
            if (json == null) {
                return; 
             }
            JSONObject dashboardInfoJson = json.getJSONObject("dashboardInfo");
            if (dashboardInfoJson == null) {
                return; 
             }
            JSONArray dataJsonArray = dashboardInfoJson.getJSONArray("data");
            if (dataJsonArray == null) {
                return; 
             }
            
            log.info("dataJsonArray.size={}", dataJsonArray.size());
            Set<String> uniqSet = new HashSet<>();
            for (int i = 0; i < dataJsonArray.size(); i++) {
                JSONObject dataJson = dataJsonArray.getJSONObject(i);
                if ("slider".equalsIgnoreCase(dataJson.getString("name"))) {
                    continue;
                }
                JSONArray lvl1ChildrenJsonArray = dataJson.getJSONArray("children");
                if (lvl1ChildrenJsonArray == null) {
                   continue; 
                }
                for (int j = 0; j < lvl1ChildrenJsonArray.size(); j++) {
                    JSONObject lvl1ChildJson = lvl1ChildrenJsonArray.getJSONObject(j);
                    String lvl1Title = lvl1ChildJson.getString("title");
                    JSONArray lvl2ChildrenJsonArray = lvl1ChildJson.getJSONArray("children");
                    if (lvl2ChildrenJsonArray == null) {
                        continue; 
                     }
                    for (int k = 0; k < lvl2ChildrenJsonArray.size(); k++) {
                        JSONObject lvl2ChildJson = lvl2ChildrenJsonArray.getJSONObject(k);
                        String lvl2Title = lvl2ChildJson.getString("title");
                        JSONArray naviJsonArray = lvl2ChildJson.getJSONArray("data");
                        if (naviJsonArray == null) {
                            continue; 
                         }
                        for (int l = 0; l < naviJsonArray.size(); l++) {
                            JSONObject naviJson = naviJsonArray.getJSONObject(l);
                            String title = naviJson.getString("text");
                            String link = naviJson.getString("link");
                            log.info("title={},link={}", title, link);
                            if (StringUtils.isBlank(link)
                                || uniqSet.contains(link)) {
                                continue;
                            }
                            uniqSet.add(link);
                            Map<String, String> data = new HashMap<>();
                            data.put("id", UUID.randomUUID().toString().replaceAll("-", ""));
                            data.put("name_zh", title);
                            data.put("name_zh_suggest", title);
                            data.put("root_name_zh", lvl1Title);
                            data.put("parent_name_zh", lvl2Title);
                            data.put("keyword_zh", title);
                            data.put("intro_zh", title);
                            data.put("link", link);
                            data.put("source", "bizWorkflow");
                            data.put("is_deleted", "0");
                            String dataStr = JSON.toJSONString(data);
                            collect(dataStr);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("navi build failed:{}", e.getMessage(), e);
        }
    }
}