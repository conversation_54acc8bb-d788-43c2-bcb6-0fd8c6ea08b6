package com.aliyun.wormhole.qanat.blink.udtf;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.aliyun.wormhole.qanat.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.datasource.RdsConnectionParam;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.TableFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatTeamNamePathBuildUDTF extends TableFunction<String> {

    private final static Logger log = LoggerFactory.getLogger(QanatTeamNamePathBuildUDTF.class);

    public void eval(String teamPath) {
    	eval("devata_rtdw", teamPath);
    }

    public void eval(String dbName, String teamPath) {
        log.info("teamPath={}", teamPath);
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
        	String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
            log.info("dbMetaStr={}", dbMetaStr);
            if (StringUtils.isBlank(dbMetaStr)) {
            	return;
            }
            JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
            RdsConnectionParam param = new RdsConnectionParam();
            param.setUrl(dbMetaJson.getString("jdbcUrl"))
    	        .setUserName(dbMetaJson.getString("username"))
    	        .setPassword(dbMetaJson.getString("password"));
        	conn = QanatDatasourceHandler.connectToTable(param);
            String teamIds = teamPath.replaceAll("\\.", ",").substring(0, teamPath.length() - 1);
            String sql = "select id,team_cname from sales_team_info where id in (" + teamIds + ") and is_deleted=0";
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            Map<String, String> data = new HashMap<>();
            while (rs.next()) {
                data.put(rs.getLong("id") + "", rs.getString("team_cname"));
            }
            String teamNamePath = null;
            String [] teamIdArray = teamPath.split("\\.");
            String [] teamNameArray = new String[teamIdArray.length];
            for (int i=0; i < teamIdArray.length; i++) {
                if ("0".equals(teamIdArray[i])) {
                    teamNameArray[i] = "0";
                } else {
                    teamNameArray[i] = data.get(teamIdArray[i]);
                }
            }
            teamNamePath = StringUtils.join(teamNameArray, ".");
            log.info("teamNamePath={}", teamNamePath);
            collect(teamNamePath);
        } catch (Exception e) {
            log.error("sql exec failed:{}", e.getMessage(), e);
        } finally {
        	if (ps != null) {
        		try {
        			ps.close();
        		} catch(Exception e) {}
        		ps = null;
        	}
        	if (conn != null) {
        		try {
        			conn.close();
        		} catch(Exception e) {}
        		conn = null;
        	}
        }
    }
}