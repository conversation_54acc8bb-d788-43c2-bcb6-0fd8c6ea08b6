package com.aliyun.wormhole.qanat.blink.udtf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;

import org.apache.commons.collections.CollectionUtils;
import org.apache.flink.table.functions.TableFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatJSONArrayUDTF extends TableFunction<String> {

    private final static Logger log = LoggerFactory.getLogger(QanatJSONArrayUDTF.class);

    public void eval(String jsonStr) {
        log.info("eval({})", jsonStr);
        try {
        	JSONArray jsonArray = JSON.parseArray(jsonStr);
        	if (CollectionUtils.isNotEmpty(jsonArray)) {
	        	for (int i = 0; i < jsonArray.size(); i++) {
	        		collect(JSON.toJSONString(jsonArray.getJSONObject(i)));
	        	}
        	}
        } catch (Exception e) {
            log.error("QanatJSONArrayUDTF failed", e);
        }
    }
}