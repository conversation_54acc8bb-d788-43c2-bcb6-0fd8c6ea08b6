package com.aliyun.wormhole.qanat.blink.udtf;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson.JSON;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.TableFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatYidaToJsonUDTF extends TableFunction<String> {
    
    private final static Logger LOG = LoggerFactory.getLogger(QanatYidaToJsonUDTF.class);
    
    private final static String [] COLUMNS = {"cbm_id","cbm_name","cid","main_app","y19_it_2019_cost","y19_yun_in_gaap","y19_yun_in_ecs_num","y19_yun_out_gaap","y19_yun_out_ecs_num","y19_yun_in_cdn_gaap","y19_yun_in_cdn_num","y19_yun_out_cdn_gaap","y19_yun_out_cdn_num","y19_yun_in_idc_gaap","y19_yun_in_idc_num","y19_yun_in_other_gaap","y19_yun_in_other_tips","y19_yun_total_tips","y20_it_2020_cost","y20_yun_in_gaap","y20_yun_in_ecs_num","y20_yun_out_gaap","y20_yun_out_ecs_num","y20_yun_in_cdn_gaap","y20_yun_in_cdn_num","y20_yun_out_cdn_gaap","y20_yun_out_cdn_num","y20_yun_in_idc_gaap","y20_yun_in_idc_num","y20_yun_in_other_gaap","y20_yun_in_other_tips","y20_yun_total_tips","y19_tecent_is_not","y19_tecent_it_2019_cost","y20_tecent_it_goal","y19_tecent_sales_name","y19_tecent_pdsa_name","y19_tecent_yun_in_gaap","y19_tecent_yun_in_ecs_num","y19_tecent_yun_out_gaap","y19_tecent_yun_out_ecs_num","y19_tecent_yun_in_cdn_gaap","y19_tecent_yun_in_cdn_num","y19_tecent_yun_out_cdn_gaap","y19_tecent_yun_out_cdn_num","y19_tecent_yun_in_heishi_gaap","y19_tecent_yun_in_heishi_num","y19_tecent_yun_in_other_gaap","y19_tecent_yun_in_other_tips","y19_tecent_yun_in_zhekou","y19_tecent_yun_in_cdn_gaap_m","y19_tecent_yun_out_gaap_unit","y19_tecent_yun_out_zhekou_info","y19_tecent_yun_total_tips","y19_other_is_not","y19_other_name","y19_other_gaap","y19_huawei_it_2019_cost","y20_huawei_it_goal","y19_huawei_sales_name","y19_huawei_pdsa_name","y19_huawei_yun_in_gaap","y19_huawei_yun_in_ecs_num","y19_huawei_yun_out_gaap","y19_huawei_yun_out_ecs_num","y19_huawei_yun_in_cdn_gaap","y19_huawei_yun_in_cdn_num","y19_huawei_yun_out_cdn_gaap","y19_huawei_yun_out_cdn_num","y19_huawei_yun_in_other_gaap","y19_huawei_yun_in_other_tips","y19_huawei_yun_in_zhekou","y19_huawei_yun_in_cdn_gaap_m","y19_huawei_yun_out_gaap_unit","y19_huawei_yun_out_zhekou_info","y19_huawei_yun_total_tips","y19_aws_it_2019_cost","y20_aws_it_goal","y19_aws_sales_name","y19_aws_pdsa_name","y19_aws_yun_in_gaap","y19_aws_yun_in_ecs_num","y19_aws_yun_out_gaap","y19_aws_yun_out_ecs_num","y19_aws_yun_in_cdn_gaap","y19_aws_yun_in_cdn_num","y19_aws_yun_out_cdn_gaap","y19_aws_yun_out_cdn_num","y19_aws_yun_in_other_gaap","y19_aws_yun_in_other_tips","y19_aws_yun_in_zhekou","y19_aws_yun_in_cdn_gaap_m","y19_aws_yun_out_gaap_unit","y19_aws_yun_out_zhekou_info","y19_aws_yun_total_tips","y19_ucloud_it_2019_cost","y20_ucloud_it_goal","y19_ucloud_sales_name","y19_ucloud_pdsa_name","y19_ucloud_yun_in_gaap","y19_ucloud_yun_in_ecs_num","y19_ucloud_yun_out_gaap","y19_ucloud_yun_out_ecs_num","y19_ucloud_yun_in_cdn_gaap","y19_ucloud_yun_in_cdn_num","y19_ucloud_yun_out_cdn_gaap","y19_ucloud_yun_out_cdn_num","y19_ucloud_yun_in_other_gaap","y19_ucloud_yun_in_other_tips","y19_ucloud_yun_in_zhekou","y19_ucloud_yun_in_cdn_gaap_m","y19_ucloud_yun_out_gaap_unit","y19_ucloud_yun_out_zhekou_info","y19_ucloud_yun_total_tips","y19_jinshan_it_2019_cost","y20_jinshan_it_goal","y19_jinshan_sales_name","y19_jinshan_pdsa_name","y19_jinshan_yun_in_gaap","y19_jinshan_yun_in_ecs_num","y19_jinshan_yun_out_gaap","y19_jinshan_yun_out_ecs_num","y19_jinshan_yun_in_cdn_gaap","y19_jinshan_yun_in_cdn_num","y19_jinshan_yun_out_cdn_gaap","y19_jinshan_yun_out_cdn_num","y19_jinshan_yun_in_other_gaap","y19_jinshan_yun_in_other_tips","y19_jinshan_yun_in_zhekou","y19_jinshan_yun_in_cdn_gaap_m","y19_jinshan_yun_out_gaap_unit","y19_jinshan_yun_out_zhekou_info","y19_jinshan_yun_total_tips","y20_out_it_country","y20_out_it_dongnanya","y20_out_it_rihan","y20_out_it_india","y20_out_it_northua","y20_out_it_southa","y20_out_it_ouzhou","y20_out_it_eluosi","y20_out_it_othercountry","y20_is_correct"};

    private final static Map<String, String> areaMap = new HashMap<String, String>() {
    	{
    		put("东南亚", "SoutheastAsia");
    		put("日韩", "JapanAndKorea");
    		put("印度亚", "India");
    		put("北美", "NorthAmerica");
    		put("南美", "SouthAmerica");
    		put("欧洲", "Europe");
    		put("俄罗斯", "Russia");
    		put("其他国家和地区", "other");
    	}
    };
    
    private final static Map<String, String> areaItOutMap = new HashMap<String, String>() {
    	{
    		put("东南亚", "y20_out_it_dongnanya");
    		put("日韩", "y20_out_it_rihan");
    		put("印度亚", "y20_out_it_india");
    		put("北美", "y20_out_it_northua");
    		put("南美", "y20_out_it_southa");
    		put("欧洲", "y20_out_it_ouzhou");
    		put("俄罗斯", "y20_out_it_eluosi");
    		put("其他国家和地区", "y20_out_it_othercountry");
    	}
    };
    
    public void eval(String ... values) {
        LOG.info("eval({})", JSON.toJSONString(values));
        Map<String, String> data = new HashMap<>();
        if (COLUMNS.length != values.length) {
            LOG.info("not eq");
        	return;
        } else {
        	for (int i=0; i < COLUMNS.length; i++) {
        		String colName = COLUMNS[i];
        		data.put(colName, values[i]);
        	}
        }
        try {
	        Map<String, Object> resultMap = new HashMap<>();
        	String cid  = data.get("cid");

	        //===================== model_customer ============================
	        String objectCode = "model_customer";
	        String customerObjectId = "yida-" + "mc_" + cid;
	        
	        Map<String, String> customer = new HashMap<>();
	        resultMap.put("customer", customer);
	        customer.put("domainCode", objectCode);
	        customer.put("id", customerObjectId);
	        customer.put("cid", dataGet(data, "cid"));
	        customer.put("year", "2020");
	        customer.put("empId", dataGet(data, "cbm_id"));
	        customer.put("empName", dataGet(data, "cbm_name"));
	        customer.put("app", dataGet(data, "main_app"));

	        resultMap.put("cid", dataGet(data, "cid"));
	        resultMap.put("year", "2020");
	        resultMap.put("empId", dataGet(data, "cbm_id"));
	        resultMap.put("empName", dataGet(data, "cbm_name"));

	        //===================== model_it ============================
	        objectCode = "model_it";
	        String it2019ObjectId = "yida-" + "mi_19_" + cid;
	        
	        Map<String, Object> itLastYear = new HashMap<>();
	        resultMap.put("itLastYear", itLastYear);
	        itLastYear.put("domainCode", objectCode);
	        itLastYear.put("id", it2019ObjectId);
	        itLastYear.put("cid", dataGet(data, "cid"));
	        itLastYear.put("year", "2020");
	        Map<String, Object> fields = new HashMap<>();
	        itLastYear.put("fields", fields);
	        
	        fields.put("cost", dataGet(data, "y19_it_2019_cost"));
	        fields.put("yun_in_gaap", dataGet(data, "y19_yun_in_gaap"));
	        fields.put("yun_in_ecs_num", dataGet(data, "y19_yun_in_ecs_num"));
	        fields.put("yun_out_gaap", dataGet(data, "y19_yun_out_gaap"));
	        fields.put("yun_out_ecs_num", dataGet(data, "y19_yun_out_ecs_num"));
	        fields.put("yun_in_cdn_gaap", dataGet(data, "y19_yun_in_cdn_gaap"));
	        fields.put("yun_in_cdn_num", dataGet(data, "y19_yun_in_cdn_num"));
	        fields.put("yun_out_cdn_gaap", dataGet(data, "y19_yun_out_cdn_gaap"));
	        fields.put("yun_out_cdn_num", dataGet(data, "y19_yun_out_cdn_num"));
	        fields.put("yun_in_idc_gaap", dataGet(data, "y19_yun_in_idc_gaap"));
	        fields.put("yun_in_idc_num", dataGet(data, "y19_yun_in_idc_num"));
	        fields.put("yun_in_other_gaap", dataGet(data, "y19_yun_in_other_gaap"));
	        fields.put("yun_in_other_tips", dataGet(data, "y19_yun_in_other_tips"));
	        fields.put("yun_total_tips", dataGet(data, "y19_yun_total_tips"));
	        
	        String it2020ObjectId = "yida-" + "mi_20_" + cid;
	        
	        Map<String, Object> itThisYear = new HashMap<>();
	        resultMap.put("itThisYear", itThisYear);
	        itThisYear.put("domainCode", objectCode);
	        itThisYear.put("id", it2020ObjectId);
	        itThisYear.put("cid", dataGet(data, "cid"));
	        itThisYear.put("year", "2020");
	        fields = new HashMap<>();
	        itThisYear.put("fields", fields);
	        
	        fields.put("cost", dataGet(data, "y20_it_2020_cost"));
	        fields.put("yun_in_gaap", dataGet(data, "y20_yun_in_gaap"));
	        fields.put("yun_in_ecs_num", dataGet(data, "y20_yun_in_ecs_num"));
	        fields.put("yun_out_gaap", dataGet(data, "y20_yun_out_gaap"));
	        fields.put("yun_out_ecs_num", dataGet(data, "y20_yun_out_ecs_num"));
	        fields.put("yun_in_cdn_gaap", dataGet(data, "y20_yun_in_cdn_gaap"));
	        fields.put("yun_in_cdn_num", dataGet(data, "y20_yun_in_cdn_num"));
	        fields.put("yun_out_cdn_gaap", dataGet(data, "y20_yun_out_cdn_gaap"));
	        fields.put("yun_out_cdn_num", dataGet(data, "y20_yun_out_cdn_num"));
	        fields.put("yun_in_idc_gaap", dataGet(data, "y20_yun_in_idc_gaap"));
	        fields.put("yun_in_idc_num", dataGet(data, "y20_yun_in_idc_num"));
	        fields.put("yun_in_other_gaap", dataGet(data, "y20_yun_in_other_gaap"));
	        fields.put("yun_in_other_tips", dataGet(data, "y20_yun_in_other_tips"));
	        fields.put("yun_total_tips", dataGet(data, "y20_yun_total_tips"));
	        
	        //===================== model_opponent ============================
	        objectCode = "model_opponent";
	        List<Map<String, Object>> opponentList = new ArrayList<>();
	        resultMap.put("opponent", opponentList);
	        
	        String tencent = data.get("y19_tecent_it_2019_cost");
	        if (StringUtils.isNotBlank(tencent)) {
    	        String tencentObjectId = "yida-" + "mo_tx_" + cid;
    	        
    	        Map<String, Object> opponent = new HashMap<>();
    	        opponentList.add(opponent);
    	        opponent.put("domainCode", objectCode);
    	        opponent.put("id", tencentObjectId);
    	        opponent.put("cid", dataGet(data, "cid"));
    	        opponent.put("year", "2020");
    	        fields = new HashMap<>();
    	        opponent.put("fields", fields);
    	        
		        fields.put("opponent", "Tencent");
		        fields.put("lastyear_opponent_it_cost", dataGet(data, "y19_tecent_it_2019_cost"));
		        fields.put("it_goal", dataGet(data, "y20_tecent_it_goal"));
		        fields.put("sales_name", dataGet(data, "y19_tecent_sales_name"));
		        fields.put("pdsa_name", dataGet(data, "y19_tecent_pdsa_name"));
		        fields.put("yun_in_gaap", dataGet(data, "y19_tecent_yun_in_gaap"));
		        fields.put("yun_in_ecs_num", dataGet(data, "y19_tecent_yun_in_ecs_num"));
		        fields.put("yun_out_gaap", dataGet(data, "y19_tecent_yun_out_gaap"));
		        fields.put("yun_out_ecs_num", dataGet(data, "y19_tecent_yun_out_ecs_num"));
		        fields.put("yun_in_cdn_gaap", dataGet(data, "y19_tecent_yun_in_cdn_gaap"));
		        fields.put("yun_in_cdn_num", dataGet(data, "y19_tecent_yun_in_cdn_num"));
		        fields.put("yun_out_cdn_gaap", dataGet(data, "y19_tecent_yun_out_cdn_gaap"));
		        fields.put("yun_out_cdn_num", dataGet(data, "y19_tecent_yun_out_cdn_num"));
		        fields.put("tecent_yun_in_heishi_gaap", dataGet(data, "y19_tecent_yun_in_heishi_gaap"));
		        fields.put("tecent_yun_in_heishi_num", dataGet(data, "y19_tecent_yun_in_heishi_num"));
		        fields.put("yun_in_other_gaap", dataGet(data, "y19_tecent_yun_in_other_gaap"));
		        fields.put("yun_in_other_tips", dataGet(data, "y19_tecent_yun_in_other_tips"));
		        fields.put("yun_in_zhekou", dataGet(data, "y19_tecent_yun_in_zhekou"));
//		        fields.put("yun_in_cdn_gaap_m", dataGet(data, "y19_tecent_yun_in_cdn_gaap_m"));
		        fields.put("yun_out_gaap_unit", dataGet(data, "y19_tecent_yun_out_gaap_unit"));
		        fields.put("yun_out_zhekou_info", dataGet(data, "y19_tecent_yun_out_zhekou_info"));
		        fields.put("yun_total_tips", dataGet(data, "y19_tecent_yun_total_tips"));
	        }
	        
	        String huawei = data.get("y19_huawei_it_2019_cost");
	        if (StringUtils.isNotBlank(huawei)) {
    	        String huaweiObjectId = "yida-" + "mo_hw_" + cid;
    	        
    	        Map<String, Object> opponent = new HashMap<>();
    	        opponentList.add(opponent);
    	        opponent.put("domainCode", objectCode);
    	        opponent.put("id", huaweiObjectId);
    	        opponent.put("cid", dataGet(data, "cid"));
    	        opponent.put("year", "2020");
    	        fields = new HashMap<>();
    	        opponent.put("fields", fields);

		        fields.put("opponent", "Huawei");
		        fields.put("lastyear_opponent_it_cost", dataGet(data, "y19_huawei_it_2019_cost"));
		        fields.put("it_goal", dataGet(data, "y20_huawei_it_goal"));
		        fields.put("sales_name", dataGet(data, "y19_huawei_sales_name"));
		        fields.put("pdsa_name", dataGet(data, "y19_huawei_pdsa_name"));
		        fields.put("yun_in_gaap", dataGet(data, "y19_huawei_yun_in_gaap"));
		        fields.put("yun_in_ecs_num", dataGet(data, "y19_huawei_yun_in_ecs_num"));
		        fields.put("yun_out_gaap", dataGet(data, "y19_huawei_yun_out_gaap"));
		        fields.put("yun_out_ecs_num", dataGet(data, "y19_huawei_yun_out_ecs_num"));
		        fields.put("yun_in_cdn_gaap", dataGet(data, "y19_huawei_yun_in_cdn_gaap"));
		        fields.put("yun_in_cdn_num", dataGet(data, "y19_huawei_yun_in_cdn_num"));
		        fields.put("yun_out_cdn_gaap", dataGet(data, "y19_huawei_yun_out_cdn_gaap"));
		        fields.put("yun_out_cdn_num", dataGet(data, "y19_huawei_yun_out_cdn_num"));
		        fields.put("yun_in_other_gaap", dataGet(data, "y19_huawei_yun_in_other_gaap"));
		        fields.put("yun_in_other_tips", dataGet(data, "y19_huawei_yun_in_other_tips"));
		        fields.put("yun_in_zhekou", dataGet(data, "y19_huawei_yun_in_zhekou"));
//		        fields.put("yun_in_cdn_gaap_m", dataGet(data, "y19_huawei_yun_in_cdn_gaap_m"));
		        fields.put("yun_out_gaap_unit", dataGet(data, "y19_huawei_yun_out_gaap_unit"));
		        fields.put("yun_out_zhekou_info", dataGet(data, "y19_huawei_yun_out_zhekou_info"));
		        fields.put("yun_total_tips", dataGet(data, "y19_huawei_yun_total_tips"));
	        }
	        
	        String aws = data.get("y19_aws_it_2019_cost");
	        if (StringUtils.isNotBlank(aws)) {
    	        String awsObjectId = "yida-" + "mo_aws_" + cid;
    	        
    	        Map<String, Object> opponent = new HashMap<>();
    	        opponentList.add(opponent);
    	        opponent.put("domainCode", objectCode);
    	        opponent.put("id", awsObjectId);
    	        opponent.put("cid", dataGet(data, "cid"));
    	        opponent.put("year", "2020");
    	        fields = new HashMap<>();
    	        opponent.put("fields", fields);
    	        
		        fields.put("opponent", "AWS");
		        fields.put("lastyear_opponent_it_cost", dataGet(data, "y19_aws_it_2019_cost"));
		        fields.put("it_goal", dataGet(data, "y20_aws_it_goal"));
		        fields.put("sales_name", dataGet(data, "y19_aws_sales_name"));
		        fields.put("pdsa_name", dataGet(data, "y19_aws_pdsa_name"));
		        fields.put("yun_in_gaap", dataGet(data, "y19_aws_yun_in_gaap"));
		        fields.put("yun_in_ecs_num", dataGet(data, "y19_aws_yun_in_ecs_num"));
		        fields.put("yun_out_gaap", dataGet(data, "y19_aws_yun_out_gaap"));
		        fields.put("yun_out_ecs_num", dataGet(data, "y19_aws_yun_out_ecs_num"));
		        fields.put("yun_in_cdn_gaap", dataGet(data, "y19_aws_yun_in_cdn_gaap"));
		        fields.put("yun_in_cdn_num", dataGet(data, "y19_aws_yun_in_cdn_num"));
		        fields.put("yun_out_cdn_gaap", dataGet(data, "y19_aws_yun_out_cdn_gaap"));
		        fields.put("yun_out_cdn_num", dataGet(data, "y19_aws_yun_out_cdn_num"));
		        fields.put("yun_in_other_gaap", dataGet(data, "y19_aws_yun_in_other_gaap"));
		        fields.put("yun_in_other_tips", dataGet(data, "y19_aws_yun_in_other_tips"));
		        fields.put("yun_in_zhekou", dataGet(data, "y19_aws_yun_in_zhekou"));
//		        fields.put("yun_in_cdn_gaap_m", dataGet(data, "y19_aws_yun_in_cdn_gaap_m"));
		        fields.put("yun_out_gaap_unit", dataGet(data, "y19_aws_yun_out_gaap_unit"));
		        fields.put("yun_out_zhekou_info", dataGet(data, "y19_aws_yun_out_zhekou_info"));
		        fields.put("yun_total_tips", dataGet(data, "y19_aws_yun_total_tips"));
	        }
	        
	        String ucloud = data.get("y19_ucloud_it_2019_cost");
	        if (StringUtils.isNotBlank(ucloud)) {
    	        String ucloudObjectId = "yida-" + "mo_uc_" + cid;
    	        
    	        Map<String, Object> opponent = new HashMap<>();
    	        opponentList.add(opponent);
    	        opponent.put("domainCode", objectCode);
    	        opponent.put("id", ucloudObjectId);
    	        opponent.put("cid", dataGet(data, "cid"));
    	        opponent.put("year", "2020");
    	        fields = new HashMap<>();
    	        opponent.put("fields", fields);
    	        
		        fields.put("opponent", "UCloud");
		        fields.put("lastyear_opponent_it_cost", dataGet(data, "y19_ucloud_it_2019_cost"));
		        fields.put("it_goal", dataGet(data, "y20_ucloud_it_goal"));
		        fields.put("sales_name", dataGet(data, "y19_ucloud_sales_name"));
		        fields.put("pdsa_name", dataGet(data, "y19_ucloud_pdsa_name"));
		        fields.put("yun_in_gaap", dataGet(data, "y19_ucloud_yun_in_gaap"));
		        fields.put("yun_in_ecs_num", dataGet(data, "y19_ucloud_yun_in_ecs_num"));
		        fields.put("yun_out_gaap", dataGet(data, "y19_ucloud_yun_out_gaap"));
		        fields.put("yun_out_ecs_num", dataGet(data, "y19_ucloud_yun_out_ecs_num"));
		        fields.put("yun_in_cdn_gaap", dataGet(data, "y19_ucloud_yun_in_cdn_gaap"));
		        fields.put("yun_in_cdn_num", dataGet(data, "y19_ucloud_yun_in_cdn_num"));
		        fields.put("yun_out_cdn_gaap", dataGet(data, "y19_ucloud_yun_out_cdn_gaap"));
		        fields.put("yun_out_cdn_num", dataGet(data, "y19_ucloud_yun_out_cdn_num"));
		        fields.put("yun_in_other_gaap", dataGet(data, "y19_ucloud_yun_in_other_gaap"));
		        fields.put("yun_in_other_tips", dataGet(data, "y19_ucloud_yun_in_other_tips"));
		        fields.put("yun_in_zhekou", dataGet(data, "y19_ucloud_yun_in_zhekou"));
//		        fields.put("yun_in_cdn_gaap_m", dataGet(data, "y19_ucloud_yun_in_cdn_gaap_m"));
		        fields.put("yun_out_gaap_unit", dataGet(data, "y19_ucloud_yun_out_gaap_unit"));
		        fields.put("yun_out_zhekou_info", dataGet(data, "y19_ucloud_yun_out_zhekou_info"));
		        fields.put("yun_total_tips", dataGet(data, "y19_ucloud_yun_total_tips"));
	        }
	        
	        String jinshan = data.get("y19_jinshan_it_2019_cost");
	        if (StringUtils.isNotBlank(jinshan)) {
    	        String jinshanObjectId = "yida-" + "mo_js_" + cid;
    	        
    	        Map<String, Object> opponent = new HashMap<>();
    	        opponentList.add(opponent);
    	        opponent.put("domainCode", objectCode);
    	        opponent.put("id", jinshanObjectId);
    	        opponent.put("cid", dataGet(data, "cid"));
    	        opponent.put("year", "2020");
    	        fields = new HashMap<>();
    	        opponent.put("fields", fields);
    	        
		        fields.put("opponent", "Kingsoft");
		        fields.put("lastyear_opponent_it_cost", dataGet(data, "y19_jinshan_it_2019_cost"));
		        fields.put("it_goal", dataGet(data, "y20_jinshan_it_goal"));
		        fields.put("sales_name", dataGet(data, "y19_jinshan_sales_name"));
		        fields.put("pdsa_name", dataGet(data, "y19_jinshan_pdsa_name"));
		        fields.put("yun_in_gaap", dataGet(data, "y19_jinshan_yun_in_gaap"));
		        fields.put("yun_in_ecs_num", dataGet(data, "y19_jinshan_yun_in_ecs_num"));
		        fields.put("yun_out_gaap", dataGet(data, "y19_jinshan_yun_out_gaap"));
		        fields.put("yun_out_ecs_num", dataGet(data, "y19_jinshan_yun_out_ecs_num"));
		        fields.put("yun_in_cdn_gaap", dataGet(data, "y19_jinshan_yun_in_cdn_gaap"));
		        fields.put("yun_in_cdn_num", dataGet(data, "y19_jinshan_yun_in_cdn_num"));
		        fields.put("yun_out_cdn_gaap", dataGet(data, "y19_jinshan_yun_out_cdn_gaap"));
		        fields.put("yun_out_cdn_num", dataGet(data, "y19_jinshan_yun_out_cdn_num"));
		        fields.put("yun_in_other_gaap", dataGet(data, "y19_jinshan_yun_in_other_gaap"));
		        fields.put("yun_in_other_tips", dataGet(data, "y19_jinshan_yun_in_other_tips"));
		        fields.put("yun_in_zhekou", dataGet(data, "y19_jinshan_yun_in_zhekou"));
//		        fields.put("yun_in_cdn_gaap_m", dataGet(data, "y19_jinshan_yun_in_cdn_gaap_m"));
		        fields.put("yun_out_gaap_unit", dataGet(data, "y19_jinshan_yun_out_gaap_unit"));
		        fields.put("yun_out_zhekou_info", dataGet(data, "y19_jinshan_yun_out_zhekou_info"));
		        fields.put("yun_total_tips", dataGet(data, "y19_jinshan_yun_total_tips"));
	        }
	        
	        
	        String other = data.get("y19_other_is_not");
	        if (StringUtils.isNotBlank(other)) {
    	        String otherObjectId = "yida-" + "mo_other_" + cid;
    	        
    	        Map<String, Object> opponent = new HashMap<>();
    	        opponentList.add(opponent);
    	        opponent.put("domainCode", objectCode);
    	        opponent.put("id", otherObjectId);
    	        opponent.put("cid", dataGet(data, "cid"));
    	        opponent.put("year", "2020");
    	        fields = new HashMap<>();
    	        opponent.put("fields", fields);
    	        
    	        fields.put("opponent", "other");
    	        fields.put("opponent_other_name", dataGet(data, "y19_other_name"));
    	        fields.put("opponent_other_gaap", dataGet(data, "y19_other_gaap"));
	        }
	        
	        //===================== model_out_it ============================
	        objectCode = "model_out_it";
	        List<Map<String, Object>> overseasList = new ArrayList<>();
	        resultMap.put("overseas", overseasList);
	        String outItCountry = data.get("y20_out_it_country");
	        if (StringUtils.isNotBlank(outItCountry)) {
		        String[] countries = outItCountry.split(",");
		        for (String country : countries) {
			        String outItObjectId = "yida-" + "moi_" + country + "_" + cid;
	    	        
	    	        Map<String, Object> overseas = new HashMap<>();
	    	        overseasList.add(overseas);
	    	        overseas.put("domainCode", objectCode);
	    	        overseas.put("id", outItObjectId);
	    	        overseas.put("cid", dataGet(data, "cid"));
	    	        overseas.put("year", "2020");
	    	        fields = new HashMap<>();
	    	        overseas.put("fields", fields);
	    	        
			        fields.put("it_area", areaMap.get(country));
			        fields.put("out_it_cost", dataGet(data, areaItOutMap.get(country)));
		        }
	        }

	        //===================== model_is_correct ============================
	        objectCode = "model_is_correct";
	        String isCorrectObjectId = "yida-" + "mic_" + cid;
	        
	        Map<String, Object> confirm = new HashMap<>();
	        resultMap.put("confirm", confirm);
	        confirm.put("domainCode", objectCode);
	        confirm.put("id", isCorrectObjectId);
	        confirm.put("cid", dataGet(data, "cid"));
	        confirm.put("year", "2020");
	        fields = new HashMap<>();
	        confirm.put("fields", fields);
	        if (StringUtils.isNotBlank(data.get("y20_is_correct"))) {
	        	String y20IsCorrect = data.get("y20_is_correct");
	        	String isCorrect = null;
	        	if (y20IsCorrect.indexOf("达到80") > 0) {
	        		isCorrect = "PerfectlyClear_above_80";
	        	} else if (y20IsCorrect.indexOf("达到60") > 0) {
	        		isCorrect = "Clear_above_60";
	        	} else if (y20IsCorrect.indexOf("不足60") > 0) {
	        		isCorrect = "Clear_below_60";
	        	}
	        	fields.put("is_correct", isCorrect);
	        }
	        
	        String json = JSON.toJSONString(resultMap);
            LOG.info("json={}", json);
	        collect(json);
        } catch (Exception e) {
        	LOG.error("QanatYidaToJsonUDTF failed", e);
        }
    }
    
    private static String dataGet(Map<String, String> data, String key) {
    	return formatNumber(data.get(key));
    }
    
    private static String formatNumber(String num) {
    	if (StringUtils.isBlank(num)) {
    		return num;
    	}
    	String reg = "^[0-9]\\d*(\\.0+)?$";
    	Pattern p = Pattern.compile(reg);
    	Matcher m = p.matcher(num);
    	while (m.find()) {
    		if (m.group(1) == null) {
    			break;
    		}
    		num = num.replace(m.group(1), "");
    		break;
    	}
    	return num;
    }
}