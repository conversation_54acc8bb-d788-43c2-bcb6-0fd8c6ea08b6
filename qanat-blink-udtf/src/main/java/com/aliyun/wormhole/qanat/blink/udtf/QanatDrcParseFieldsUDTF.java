package com.aliyun.wormhole.qanat.blink.udtf;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.TableFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatDrcParseFieldsUDTF extends TableFunction<String> {
    
    private final static Logger LOG = LoggerFactory.getLogger(QanatDrcParseFieldsUDTF.class);

    public void eval(String msg, String ... colNames) {
        LOG.info("eval({},{},{})", msg, colNames);
        try {
	        if (StringUtils.isBlank(msg)
	            || (colNames == null || colNames.length == 0)) {
	            return;
	        }
	        JSONObject msgJson = JSON.parseObject(msg);
	        JSONObject fieldsJson =  msgJson.getJSONObject("fieldValues");
	        if (fieldsJson != null) {
	            Map<String, Object> data = new HashMap<>();
	            data.put("eventType", msgJson.getInteger("eventType"));
	            data.put("dbName", msgJson.getString("dbName"));
	            data.put("tableName", msgJson.getString("tableName"));
	            for (String colName : colNames) {
	                JSONObject fieldJson = fieldsJson.getJSONObject(colName);
	                if (fieldJson != null) {
	                    String value = fieldJson.getString("newValue");
	                    data.put(colName, value);
	                    String oldValue = fieldJson.getString("oldValue");
	                    if (oldValue != null) {
	                        data.put(colName + "_old", oldValue);
	                    }
	                }
	            }
	            if (CollectionUtils.isNotEmpty(data.keySet())) {
	                String json = JSON.toJSONString(data);
	                LOG.info("json={}", json);
	                collect(json);
	            }
	        }
        } catch (Exception e) {
        	LOG.error("QanatDrcParseFieldsUDTF failed", e);
        }
    }
}
