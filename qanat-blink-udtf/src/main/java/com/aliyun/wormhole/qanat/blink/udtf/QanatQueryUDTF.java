package com.aliyun.wormhole.qanat.blink.udtf;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.aliyun.wormhole.qanat.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.datasource.QanatTddlDatasourceHandler;
import com.aliyun.wormhole.qanat.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.datasource.TddlConnectionParam;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.TableFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatQueryUDTF extends TableFunction<String> {

    private final static Logger log = LoggerFactory.getLogger(QanatQueryUDTF.class);

    public void eval(String traceDbName, String sql, Object ... values) {
        String requestId = "req_" + UUID.randomUUID().toString().replace("-", "");
        log.info("{} eval({},{},{})", requestId, traceDbName, sql, values);
        String[] tokens = traceDbName.split("\\|");
        String traceId = null;
        String dbName = null;
        if (tokens.length == 1) {
        	traceId = requestId;
        	dbName = tokens[0];
        } else {
        	traceId = tokens[0];
        	dbName = tokens[1];
        }
        log.info("{} {} dbName:{}, sql:{}, values:{}", requestId, traceId, dbName, sql, values);
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        try {
            String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
            log.info("{} {} dbMetaStr={}", requestId, traceId, dbMetaStr);
            if (StringUtils.isBlank(dbMetaStr)) {
                log.error("{} {} failed to get dbMeta", requestId, traceId);
            	return;
            }
            JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
            String hint = "";
            if (dbMetaJson.containsKey("appName")) {
            	TddlConnectionParam param = new TddlConnectionParam();
            	param.setAppName(dbMetaJson.getString("appName"))
            		.setAccessKey("accessKey")
            		.setSecretKey("secretKey");
            	conn = QanatTddlDatasourceHandler.connectToTable(param);
	        	hint = "/*+TDDL({\"extra\":{\"ALLOW_FULL_TABLE_SCAN\":\"TRUE\"}})*//*+TDDL_GROUP({groupIndex:1})*//*+TDDL({'extra':{'SOCKET_TIMEOUT':'10000'}})*/";
            } else {
	            RdsConnectionParam param = new RdsConnectionParam();
	            param.setUrl(dbMetaJson.getString("jdbcUrl"))
	    	        .setUserName(dbMetaJson.getString("username"))
	    	        .setPassword(dbMetaJson.getString("password"));
	        	conn = QanatDatasourceHandler.connectToTable(param);
	        	
	        	if (dbMetaJson.getString("jdbcUrl").contains("ads.aliyuncs.com")) {
	        		hint = "/*+query_timeout=30000*/";
	        	}
            }
            sql = hint + sql;
        	ps = conn.prepareStatement(sql);
            if (values != null && values.length > 0) {
                for (int i = 0; i < values.length; i++) {
                	ps.setObject(i + 1, values[i]);
                }
            }
            long startTs = System.currentTimeMillis();
            rs = ps.executeQuery();
            log.info("{} {} dbName:{} sql:{} cost:{}", requestId, traceId, dbName, sql, System.currentTimeMillis() - startTs);
            boolean result = false;
            while (rs.next()) {
                result = true;
                Map<String, Object> data = new HashMap<>();
                int columnCount = rs.getMetaData().getColumnCount();
                for (int i = 1; i <= columnCount; i++) {
                    data.put(rs.getMetaData().getColumnLabel(i), rs.getObject(i));
                }
                String dataStr = JSON.toJSONString(data);
                log.info("{} {} keyword:{} collect data:{}", requestId, traceId, values, dataStr);
                collect(dataStr);
            }
            if (!result) {
                Map<String, Object> data = new HashMap<>();
                String dataStr = JSON.toJSONString(data);
                log.info("{} {} keyword:{} collect data:{}", requestId, traceId, values, dataStr);
                collect(dataStr);
            }
        } catch (Exception e) {
            log.error("{} {} QanatQueryUDTF failed due to {}", requestId, traceId, e.getMessage(), e);
        } finally {
            if (rs != null) {
                try {
                	rs.close();
                } catch (SQLException e) {
                } finally {
                	rs = null;
                }
            }
            if (ps != null) {
                try {
                	ps.close();
                } catch (SQLException e) {
                } finally {
                	ps = null;
                }
            }
            if (conn != null) {
                try {
                	conn.close();
                } catch (SQLException e) {
                } finally {
                	conn = null;
                }
            }
        }
    }
}
