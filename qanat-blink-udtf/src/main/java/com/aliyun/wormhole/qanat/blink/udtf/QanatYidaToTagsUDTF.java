package com.aliyun.wormhole.qanat.blink.udtf;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson.JSON;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.tuple.Tuple7;
import org.apache.flink.table.functions.TableFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatYidaToTagsUDTF extends TableFunction<Tuple7<String,String,String,String,String,String,String>> {
    
    private final static Logger LOG = LoggerFactory.getLogger(QanatYidaToTagsUDTF.class);
    
    private final static String [] COLUMNS = {"cbm_id","cbm_name","cid","main_app","y19_it_2019_cost","y19_yun_in_gaap","y19_yun_in_ecs_num","y19_yun_out_gaap","y19_yun_out_ecs_num","y19_yun_in_cdn_gaap","y19_yun_in_cdn_num","y19_yun_out_cdn_gaap","y19_yun_out_cdn_num","y19_yun_in_idc_gaap","y19_yun_in_idc_num","y19_yun_in_other_gaap","y19_yun_in_other_tips","y19_yun_total_tips","y20_it_2020_cost","y20_yun_in_gaap","y20_yun_in_ecs_num","y20_yun_out_gaap","y20_yun_out_ecs_num","y20_yun_in_cdn_gaap","y20_yun_in_cdn_num","y20_yun_out_cdn_gaap","y20_yun_out_cdn_num","y20_yun_in_idc_gaap","y20_yun_in_idc_num","y20_yun_in_other_gaap","y20_yun_in_other_tips","y20_yun_total_tips","y19_tecent_is_not","y19_tecent_it_2019_cost","y20_tecent_it_goal","y19_tecent_sales_name","y19_tecent_pdsa_name","y19_tecent_yun_in_gaap","y19_tecent_yun_in_ecs_num","y19_tecent_yun_out_gaap","y19_tecent_yun_out_ecs_num","y19_tecent_yun_in_cdn_gaap","y19_tecent_yun_in_cdn_num","y19_tecent_yun_out_cdn_gaap","y19_tecent_yun_out_cdn_num","y19_tecent_yun_in_heishi_gaap","y19_tecent_yun_in_heishi_num","y19_tecent_yun_in_other_gaap","y19_tecent_yun_in_other_tips","y19_tecent_yun_in_zhekou","y19_tecent_yun_in_cdn_gaap_m","y19_tecent_yun_out_gaap_unit","y19_tecent_yun_out_zhekou_info","y19_tecent_yun_total_tips","y19_other_is_not","y19_other_name","y19_other_gaap","y19_huawei_it_2019_cost","y20_huawei_it_goal","y19_huawei_sales_name","y19_huawei_pdsa_name","y19_huawei_yun_in_gaap","y19_huawei_yun_in_ecs_num","y19_huawei_yun_out_gaap","y19_huawei_yun_out_ecs_num","y19_huawei_yun_in_cdn_gaap","y19_huawei_yun_in_cdn_num","y19_huawei_yun_out_cdn_gaap","y19_huawei_yun_out_cdn_num","y19_huawei_yun_in_other_gaap","y19_huawei_yun_in_other_tips","y19_huawei_yun_in_zhekou","y19_huawei_yun_in_cdn_gaap_m","y19_huawei_yun_out_gaap_unit","y19_huawei_yun_out_zhekou_info","y19_huawei_yun_total_tips","y19_aws_it_2019_cost","y20_aws_it_goal","y19_aws_sales_name","y19_aws_pdsa_name","y19_aws_yun_in_gaap","y19_aws_yun_in_ecs_num","y19_aws_yun_out_gaap","y19_aws_yun_out_ecs_num","y19_aws_yun_in_cdn_gaap","y19_aws_yun_in_cdn_num","y19_aws_yun_out_cdn_gaap","y19_aws_yun_out_cdn_num","y19_aws_yun_in_other_gaap","y19_aws_yun_in_other_tips","y19_aws_yun_in_zhekou","y19_aws_yun_in_cdn_gaap_m","y19_aws_yun_out_gaap_unit","y19_aws_yun_out_zhekou_info","y19_aws_yun_total_tips","y19_ucloud_it_2019_cost","y20_ucloud_it_goal","y19_ucloud_sales_name","y19_ucloud_pdsa_name","y19_ucloud_yun_in_gaap","y19_ucloud_yun_in_ecs_num","y19_ucloud_yun_out_gaap","y19_ucloud_yun_out_ecs_num","y19_ucloud_yun_in_cdn_gaap","y19_ucloud_yun_in_cdn_num","y19_ucloud_yun_out_cdn_gaap","y19_ucloud_yun_out_cdn_num","y19_ucloud_yun_in_other_gaap","y19_ucloud_yun_in_other_tips","y19_ucloud_yun_in_zhekou","y19_ucloud_yun_in_cdn_gaap_m","y19_ucloud_yun_out_gaap_unit","y19_ucloud_yun_out_zhekou_info","y19_ucloud_yun_total_tips","y19_jinshan_it_2019_cost","y20_jinshan_it_goal","y19_jinshan_sales_name","y19_jinshan_pdsa_name","y19_jinshan_yun_in_gaap","y19_jinshan_yun_in_ecs_num","y19_jinshan_yun_out_gaap","y19_jinshan_yun_out_ecs_num","y19_jinshan_yun_in_cdn_gaap","y19_jinshan_yun_in_cdn_num","y19_jinshan_yun_out_cdn_gaap","y19_jinshan_yun_out_cdn_num","y19_jinshan_yun_in_other_gaap","y19_jinshan_yun_in_other_tips","y19_jinshan_yun_in_zhekou","y19_jinshan_yun_in_cdn_gaap_m","y19_jinshan_yun_out_gaap_unit","y19_jinshan_yun_out_zhekou_info","y19_jinshan_yun_total_tips","y20_out_it_country","y20_out_it_dongnanya","y20_out_it_rihan","y20_out_it_india","y20_out_it_northua","y20_out_it_southa","y20_out_it_ouzhou","y20_out_it_eluosi","y20_out_it_othercountry","y20_is_correct"};

    private final static Map<String, String> areaMap = new HashMap<String, String>() {
    	{
    		put("东南亚", "SoutheastAsia");
    		put("日韩", "JapanAndKorea");
    		put("印度亚", "India");
    		put("北美", "NorthAmerica");
    		put("南美", "SouthAmerica");
    		put("欧洲", "Europe");
    		put("俄罗斯", "Russia");
    		put("其他国家和地区", "other");
    	}
    };
    
    private final static Map<String, String> areaItOutMap = new HashMap<String, String>() {
    	{
    		put("东南亚", "y20_out_it_dongnanya");
    		put("日韩", "y20_out_it_rihan");
    		put("印度亚", "y20_out_it_india");
    		put("北美", "y20_out_it_northua");
    		put("南美", "y20_out_it_southa");
    		put("欧洲", "y20_out_it_ouzhou");
    		put("俄罗斯", "y20_out_it_eluosi");
    		put("其他国家和地区", "y20_out_it_othercountry");
    	}
    };
    
    public void eval(String operateType, String ... values) {
        LOG.info("eval({})", JSON.toJSONString(values));
        Map<String, String> data = new HashMap<>();
        if (COLUMNS.length != values.length) {
        	return;
        } else {
        	for (int i=0; i < COLUMNS.length; i++) {
        		String colName = COLUMNS[i];
        		data.put(colName, values[i]);
        	}
        }
        try {
        	String cid  = data.get("cid");
	        String tagDomainCode = "domain_model";
	        String objectDomainCode = "domain_model";

	        //===================== model_customer ============================
	        String objectCode = "model_customer";
	        String customerObjectId = "yida-" + "mc_" + cid;
	        List<Tuple7<String,String,String,String,String,String,String>> tuple7List = new ArrayList<>();
	        tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, customerObjectId, data.get("cid"), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, customerObjectId, "2020", operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "cbm_id", objectDomainCode, objectCode, customerObjectId, data.get("cbm_id"), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "cbm_name", objectDomainCode, objectCode, customerObjectId, data.get("cbm_name"), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "main_app", objectDomainCode, objectCode, customerObjectId, data.get("main_app"), operateType));
  
	        for (Tuple7<String,String,String,String,String,String,String> tuple7 : tuple7List) {
	            collect(tuple7);
	        }


	        //===================== model_it ============================
	        objectCode = "model_it";
	        tuple7List = new ArrayList<>();
	        String it2019ObjectId = "yida-" + "mi_19_" + cid;
	        tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, it2019ObjectId, data.get("cid"), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, it2019ObjectId, "2020", operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "year_type", objectDomainCode, objectCode, it2019ObjectId, "last_year", operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "cost", objectDomainCode, objectCode, it2019ObjectId, formatNumber(data.get("y19_it_2019_cost")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_gaap", objectDomainCode, objectCode, it2019ObjectId, formatNumber(data.get("y19_yun_in_gaap")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_ecs_num", objectDomainCode, objectCode, it2019ObjectId, formatNumber(data.get("y19_yun_in_ecs_num")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_gaap", objectDomainCode, objectCode, it2019ObjectId, formatNumber(data.get("y19_yun_out_gaap")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_ecs_num", objectDomainCode, objectCode, it2019ObjectId, formatNumber(data.get("y19_yun_out_ecs_num")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_gaap", objectDomainCode, objectCode, it2019ObjectId, formatNumber(data.get("y19_yun_in_cdn_gaap")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_num", objectDomainCode, objectCode, it2019ObjectId, formatNumber(data.get("y19_yun_in_cdn_num")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_cdn_gaap", objectDomainCode, objectCode, it2019ObjectId, formatNumber(data.get("y19_yun_out_cdn_gaap")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_cdn_num", objectDomainCode, objectCode, it2019ObjectId, formatNumber(data.get("y19_yun_out_cdn_num")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_idc_gaap", objectDomainCode, objectCode, it2019ObjectId, formatNumber(data.get("y19_yun_in_idc_gaap")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_idc_num", objectDomainCode, objectCode, it2019ObjectId, formatNumber(data.get("y19_yun_in_idc_num")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_other_gaap", objectDomainCode, objectCode, it2019ObjectId, formatNumber(data.get("y19_yun_in_other_gaap")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_other_tips", objectDomainCode, objectCode, it2019ObjectId, formatNumber(data.get("y19_yun_in_other_tips")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_total_tips", objectDomainCode, objectCode, it2019ObjectId, formatNumber(data.get("y19_yun_total_tips")), operateType));
	        
	        String it2020ObjectId = "yida-" + "mi_20_" + cid;
	        tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, it2020ObjectId, data.get("cid"), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, it2020ObjectId, "2020", operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "year_type", objectDomainCode, objectCode, it2020ObjectId, "this_year", operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "cost", objectDomainCode, objectCode, it2020ObjectId, formatNumber(data.get("y20_it_2020_cost")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_gaap", objectDomainCode, objectCode, it2020ObjectId, formatNumber(data.get("y20_yun_in_gaap")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_ecs_num", objectDomainCode, objectCode, it2020ObjectId, formatNumber(data.get("y20_yun_in_ecs_num")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_gaap", objectDomainCode, objectCode, it2020ObjectId, formatNumber(data.get("y20_yun_out_gaap")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_ecs_num", objectDomainCode, objectCode, it2020ObjectId, formatNumber(data.get("y20_yun_out_ecs_num")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_gaap", objectDomainCode, objectCode, it2020ObjectId, formatNumber(data.get("y20_yun_in_cdn_gaap")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_num", objectDomainCode, objectCode, it2020ObjectId, formatNumber(data.get("y20_yun_in_cdn_num")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_cdn_gaap", objectDomainCode, objectCode, it2020ObjectId, formatNumber(data.get("y20_yun_out_cdn_gaap")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_cdn_num", objectDomainCode, objectCode, it2020ObjectId, formatNumber(data.get("y20_yun_out_cdn_num")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_idc_gaap", objectDomainCode, objectCode, it2020ObjectId, formatNumber(data.get("y20_yun_in_idc_gaap")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_idc_num", objectDomainCode, objectCode, it2020ObjectId, formatNumber(data.get("y20_yun_in_idc_num")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_other_gaap", objectDomainCode, objectCode, it2020ObjectId, formatNumber(data.get("y20_yun_in_other_gaap")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_other_tips", objectDomainCode, objectCode, it2020ObjectId, formatNumber(data.get("y20_yun_in_other_tips")), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "yun_total_tips", objectDomainCode, objectCode, it2020ObjectId, formatNumber(data.get("y20_yun_total_tips")), operateType));
  
	        for (Tuple7<String,String,String,String,String,String,String> tuple7 : tuple7List) {
	            collect(tuple7);
	        }
	        
	        //===================== model_opponent ============================
	        objectCode = "model_opponent";
	        tuple7List = new ArrayList<>();
	        
	        String tencent = data.get("y19_tecent_it_2019_cost");
	        if (StringUtils.isNotBlank(tencent)) {
    	        String tencentObjectId = "yida-" + "mo_tx_" + cid;
		        tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, tencentObjectId, data.get("cid"), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, tencentObjectId, "2020", operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "opponent", objectDomainCode, objectCode, tencentObjectId, "Tencent", operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "lastyear_opponent_it_cost", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_it_2019_cost")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "it_goal", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y20_tecent_it_goal")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "sales_name", objectDomainCode, objectCode, tencentObjectId, data.get("y19_tecent_sales_name"), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "pdsa_name", objectDomainCode, objectCode, tencentObjectId, data.get("y19_tecent_pdsa_name"), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_gaap", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_yun_in_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_ecs_num", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_yun_in_ecs_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_gaap", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_yun_out_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_ecs_num", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_yun_out_ecs_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_gaap", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_yun_in_cdn_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_num", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_yun_in_cdn_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_cdn_gaap", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_yun_out_cdn_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_cdn_num", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_yun_out_cdn_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "tecent_yun_in_heishi_gaap", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_yun_in_heishi_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "tecent_yun_in_heishi_num", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_yun_in_heishi_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_other_gaap", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_yun_in_other_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_other_tips", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_yun_in_other_tips")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_zhekou", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_yun_in_zhekou")), operateType));
//		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_gaap_m", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_yun_in_cdn_gaap_m")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_gaap_unit", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_yun_out_gaap_unit")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_zhekou_info", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_yun_out_zhekou_info")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_total_tips", objectDomainCode, objectCode, tencentObjectId, formatNumber(data.get("y19_tecent_yun_total_tips")), operateType));
	        }
	        
	        String huawei = data.get("y19_huawei_it_2019_cost");
	        if (StringUtils.isNotBlank(huawei)) {
    	        String huaweiObjectId = "yida-" + "mo_hw_" + cid;
		        tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, huaweiObjectId, data.get("cid"), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, huaweiObjectId, "2020", operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "opponent", objectDomainCode, objectCode, huaweiObjectId, "Huawei", operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "lastyear_opponent_it_cost", objectDomainCode, objectCode, huaweiObjectId, formatNumber(data.get("y19_huawei_it_2019_cost")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "it_goal", objectDomainCode, objectCode, huaweiObjectId, formatNumber(data.get("y20_huawei_it_goal")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "sales_name", objectDomainCode, objectCode, huaweiObjectId, data.get("y19_huawei_sales_name"), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "pdsa_name", objectDomainCode, objectCode, huaweiObjectId, data.get("y19_huawei_pdsa_name"), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_gaap", objectDomainCode, objectCode, huaweiObjectId, formatNumber(data.get("y19_huawei_yun_in_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_ecs_num", objectDomainCode, objectCode, huaweiObjectId, formatNumber(data.get("y19_huawei_yun_in_ecs_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_gaap", objectDomainCode, objectCode, huaweiObjectId, formatNumber(data.get("y19_huawei_yun_out_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_ecs_num", objectDomainCode, objectCode, huaweiObjectId, formatNumber(data.get("y19_huawei_yun_out_ecs_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_gaap", objectDomainCode, objectCode, huaweiObjectId, formatNumber(data.get("y19_huawei_yun_in_cdn_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_num", objectDomainCode, objectCode, huaweiObjectId, formatNumber(data.get("y19_huawei_yun_in_cdn_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_cdn_gaap", objectDomainCode, objectCode, huaweiObjectId, formatNumber(data.get("y19_huawei_yun_out_cdn_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_cdn_num", objectDomainCode, objectCode, huaweiObjectId, formatNumber(data.get("y19_huawei_yun_out_cdn_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_other_gaap", objectDomainCode, objectCode, huaweiObjectId, formatNumber(data.get("y19_huawei_yun_in_other_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_other_tips", objectDomainCode, objectCode, huaweiObjectId, formatNumber(data.get("y19_huawei_yun_in_other_tips")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_zhekou", objectDomainCode, objectCode, huaweiObjectId, formatNumber(data.get("y19_huawei_yun_in_zhekou")), operateType));
//		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_gaap_m", objectDomainCode, objectCode, huaweiObjectId, formatNumber(data.get("y19_huawei_yun_in_cdn_gaap_m")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_gaap_unit", objectDomainCode, objectCode, huaweiObjectId, formatNumber(data.get("y19_huawei_yun_out_gaap_unit")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_zhekou_info", objectDomainCode, objectCode, huaweiObjectId, formatNumber(data.get("y19_huawei_yun_out_zhekou_info")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_total_tips", objectDomainCode, objectCode, huaweiObjectId, formatNumber(data.get("y19_huawei_yun_total_tips")), operateType));
	        }
	        
	        String aws = data.get("y19_aws_it_2019_cost");
	        if (StringUtils.isNotBlank(aws)) {
    	        String awsObjectId = "yida-" + "mo_aws_" + cid;
		        tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, awsObjectId, data.get("cid"), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, awsObjectId, "2020", operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "opponent", objectDomainCode, objectCode, awsObjectId, "AWS", operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "lastyear_opponent_it_cost", objectDomainCode, objectCode, awsObjectId, formatNumber(data.get("y19_aws_it_2019_cost")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "it_goal", objectDomainCode, objectCode, awsObjectId, formatNumber(data.get("y20_aws_it_goal")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "sales_name", objectDomainCode, objectCode, awsObjectId, data.get("y19_aws_sales_name"), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "pdsa_name", objectDomainCode, objectCode, awsObjectId, data.get("y19_aws_pdsa_name"), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_gaap", objectDomainCode, objectCode, awsObjectId, formatNumber(data.get("y19_aws_yun_in_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_ecs_num", objectDomainCode, objectCode, awsObjectId, formatNumber(data.get("y19_aws_yun_in_ecs_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_gaap", objectDomainCode, objectCode, awsObjectId, formatNumber(data.get("y19_aws_yun_out_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_ecs_num", objectDomainCode, objectCode, awsObjectId, formatNumber(data.get("y19_aws_yun_out_ecs_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_gaap", objectDomainCode, objectCode, awsObjectId, formatNumber(data.get("y19_aws_yun_in_cdn_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_num", objectDomainCode, objectCode, awsObjectId, formatNumber(data.get("y19_aws_yun_in_cdn_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_cdn_gaap", objectDomainCode, objectCode, awsObjectId, formatNumber(data.get("y19_aws_yun_out_cdn_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_cdn_num", objectDomainCode, objectCode, awsObjectId, formatNumber(data.get("y19_aws_yun_out_cdn_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_other_gaap", objectDomainCode, objectCode, awsObjectId, formatNumber(data.get("y19_aws_yun_in_other_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_other_tips", objectDomainCode, objectCode, awsObjectId, formatNumber(data.get("y19_aws_yun_in_other_tips")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_zhekou", objectDomainCode, objectCode, awsObjectId, formatNumber(data.get("y19_aws_yun_in_zhekou")), operateType));
//		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_gaap_m", objectDomainCode, objectCode, awsObjectId, formatNumber(data.get("y19_aws_yun_in_cdn_gaap_m")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_gaap_unit", objectDomainCode, objectCode, awsObjectId, formatNumber(data.get("y19_aws_yun_out_gaap_unit")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_zhekou_info", objectDomainCode, objectCode, awsObjectId, formatNumber(data.get("y19_aws_yun_out_zhekou_info")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_total_tips", objectDomainCode, objectCode, awsObjectId, formatNumber(data.get("y19_aws_yun_total_tips")), operateType));
	        }
	        
	        String ucloud = data.get("y19_ucloud_it_2019_cost");
	        if (StringUtils.isNotBlank(ucloud)) {
    	        String ucloudObjectId = "yida-" + "mo_uc_" + cid;
		        tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, ucloudObjectId, data.get("cid"), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, ucloudObjectId, "2020", operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "opponent", objectDomainCode, objectCode, ucloudObjectId, "UCloud", operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "lastyear_opponent_it_cost", objectDomainCode, objectCode, ucloudObjectId, formatNumber(data.get("y19_ucloud_it_2019_cost")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "it_goal", objectDomainCode, objectCode, ucloudObjectId, formatNumber(data.get("y20_ucloud_it_goal")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "sales_name", objectDomainCode, objectCode, ucloudObjectId, data.get("y19_ucloud_sales_name"), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "pdsa_name", objectDomainCode, objectCode, ucloudObjectId, data.get("y19_ucloud_pdsa_name"), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_gaap", objectDomainCode, objectCode, ucloudObjectId, formatNumber(data.get("y19_ucloud_yun_in_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_ecs_num", objectDomainCode, objectCode, ucloudObjectId, formatNumber(data.get("y19_ucloud_yun_in_ecs_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_gaap", objectDomainCode, objectCode, ucloudObjectId, formatNumber(data.get("y19_ucloud_yun_out_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_ecs_num", objectDomainCode, objectCode, ucloudObjectId, formatNumber(data.get("y19_ucloud_yun_out_ecs_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_gaap", objectDomainCode, objectCode, ucloudObjectId, formatNumber(data.get("y19_ucloud_yun_in_cdn_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_num", objectDomainCode, objectCode, ucloudObjectId, formatNumber(data.get("y19_ucloud_yun_in_cdn_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_cdn_gaap", objectDomainCode, objectCode, ucloudObjectId, formatNumber(data.get("y19_ucloud_yun_out_cdn_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_cdn_num", objectDomainCode, objectCode, ucloudObjectId, formatNumber(data.get("y19_ucloud_yun_out_cdn_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_other_gaap", objectDomainCode, objectCode, ucloudObjectId, formatNumber(data.get("y19_ucloud_yun_in_other_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_other_tips", objectDomainCode, objectCode, ucloudObjectId, formatNumber(data.get("y19_ucloud_yun_in_other_tips")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_zhekou", objectDomainCode, objectCode, ucloudObjectId, formatNumber(data.get("y19_ucloud_yun_in_zhekou")), operateType));
//		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_gaap_m", objectDomainCode, objectCode, ucloudObjectId, formatNumber(data.get("y19_ucloud_yun_in_cdn_gaap_m")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_gaap_unit", objectDomainCode, objectCode, ucloudObjectId, formatNumber(data.get("y19_ucloud_yun_out_gaap_unit")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_zhekou_info", objectDomainCode, objectCode, ucloudObjectId, formatNumber(data.get("y19_ucloud_yun_out_zhekou_info")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_total_tips", objectDomainCode, objectCode, ucloudObjectId, formatNumber(data.get("y19_ucloud_yun_total_tips")), operateType));
	        }
	        
	        String jinshan = data.get("y19_jinshan_it_2019_cost");
	        if (StringUtils.isNotBlank(jinshan)) {
    	        String jinshanObjectId = "yida-" + "mo_js_" + cid;
		        tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, jinshanObjectId, data.get("cid"), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, jinshanObjectId, "2020", operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "opponent", objectDomainCode, objectCode, jinshanObjectId, "Kingsoft", operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "lastyear_opponent_it_cost", objectDomainCode, objectCode, jinshanObjectId, formatNumber(data.get("y19_jinshan_it_2019_cost")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "it_goal", objectDomainCode, objectCode, jinshanObjectId, formatNumber(data.get("y20_jinshan_it_goal")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "sales_name", objectDomainCode, objectCode, jinshanObjectId, data.get("y19_jinshan_sales_name"), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "pdsa_name", objectDomainCode, objectCode, jinshanObjectId, data.get("y19_jinshan_pdsa_name"), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_gaap", objectDomainCode, objectCode, jinshanObjectId, formatNumber(data.get("y19_jinshan_yun_in_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_ecs_num", objectDomainCode, objectCode, jinshanObjectId, formatNumber(data.get("y19_jinshan_yun_in_ecs_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_gaap", objectDomainCode, objectCode, jinshanObjectId, formatNumber(data.get("y19_jinshan_yun_out_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_ecs_num", objectDomainCode, objectCode, jinshanObjectId, formatNumber(data.get("y19_jinshan_yun_out_ecs_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_gaap", objectDomainCode, objectCode, jinshanObjectId, formatNumber(data.get("y19_jinshan_yun_in_cdn_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_num", objectDomainCode, objectCode, jinshanObjectId, formatNumber(data.get("y19_jinshan_yun_in_cdn_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_cdn_gaap", objectDomainCode, objectCode, jinshanObjectId, formatNumber(data.get("y19_jinshan_yun_out_cdn_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_cdn_num", objectDomainCode, objectCode, jinshanObjectId, formatNumber(data.get("y19_jinshan_yun_out_cdn_num")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_other_gaap", objectDomainCode, objectCode, jinshanObjectId, formatNumber(data.get("y19_jinshan_yun_in_other_gaap")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_other_tips", objectDomainCode, objectCode, jinshanObjectId, formatNumber(data.get("y19_jinshan_yun_in_other_tips")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_zhekou", objectDomainCode, objectCode, jinshanObjectId, formatNumber(data.get("y19_jinshan_yun_in_zhekou")), operateType));
//		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_in_cdn_gaap_m", objectDomainCode, objectCode, jinshanObjectId, formatNumber(data.get("y19_jinshan_yun_in_cdn_gaap_m")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_gaap_unit", objectDomainCode, objectCode, jinshanObjectId, formatNumber(data.get("y19_jinshan_yun_out_gaap_unit")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_out_zhekou_info", objectDomainCode, objectCode, jinshanObjectId, formatNumber(data.get("y19_jinshan_yun_out_zhekou_info")), operateType));
		        tuple7List.add(Tuple7.of(tagDomainCode, "yun_total_tips", objectDomainCode, objectCode, jinshanObjectId, formatNumber(data.get("y19_jinshan_yun_total_tips")), operateType));
	        }
	        
	        
	        String other = data.get("y19_other_is_not");
	        if (StringUtils.isNotBlank(other)) {
    	        String otherObjectId = "yida-" + "mo_other_" + cid;
    	        tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, otherObjectId, data.get("cid"), operateType));
    	        tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, otherObjectId, "2020", operateType));
    	        tuple7List.add(Tuple7.of(tagDomainCode, "opponent", objectDomainCode, objectCode, otherObjectId, "other", operateType));
    	        tuple7List.add(Tuple7.of(tagDomainCode, "opponent_other_name", objectDomainCode, objectCode, otherObjectId, data.get("y19_other_name"), operateType));
    	        tuple7List.add(Tuple7.of(tagDomainCode, "opponent_other_gaap", objectDomainCode, objectCode, otherObjectId, formatNumber(data.get("y19_other_gaap")), operateType));
	        }
    	
	    	for (Tuple7<String,String,String,String,String,String,String> tuple7 : tuple7List) {
	            collect(tuple7);
	        }

	        
	        //===================== model_out_it ============================
	        objectCode = "model_out_it";
	        tuple7List = new ArrayList<>();
	        String outItCountry = data.get("y20_out_it_country");
	        if (StringUtils.isNotBlank(outItCountry)) {
		        String[] countries = outItCountry.split(",");
		        for (String country : countries) {
			        String outItObjectId = "yida-" + "moi_" + country + "_" + cid;
			        tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, outItObjectId, data.get("cid"), operateType));
			        tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, outItObjectId, "2020", operateType));
			        tuple7List.add(Tuple7.of(tagDomainCode, "it_area", objectDomainCode, objectCode, outItObjectId, areaMap.get(country), operateType));
			        tuple7List.add(Tuple7.of(tagDomainCode, "out_it_cost", objectDomainCode, objectCode, outItObjectId, formatNumber(data.get(areaItOutMap.get(country))), operateType));
		        }
		        
		        for (Tuple7<String,String,String,String,String,String,String> tuple7 : tuple7List) {
		            collect(tuple7);
		        }
	        }

	        //===================== model_is_correct ============================
	        objectCode = "model_is_correct";
	        tuple7List = new ArrayList<>();
	        String isCorrectObjectId = "yida-" + "mic_" + cid;
	        tuple7List.add(Tuple7.of(tagDomainCode, "cid", objectDomainCode, objectCode, isCorrectObjectId, data.get("cid"), operateType));
	        tuple7List.add(Tuple7.of(tagDomainCode, "year", objectDomainCode, objectCode, isCorrectObjectId, "2020", operateType));
	        if (StringUtils.isNotBlank(data.get("y20_is_correct"))) {
	        	String y20IsCorrect = data.get("y20_is_correct");
	        	String isCorrect = null;
	        	if (y20IsCorrect.indexOf("达到80") > 0) {
	        		isCorrect = "PerfectlyClear_above_80";
	        	} else if (y20IsCorrect.indexOf("达到60") > 0) {
	        		isCorrect = "Clear_above_60";
	        	} else if (y20IsCorrect.indexOf("不足60") > 0) {
	        		isCorrect = "Clear_below_60";
	        	}
	        	tuple7List.add(Tuple7.of(tagDomainCode, "is_correct", objectDomainCode, objectCode, isCorrectObjectId, isCorrect, operateType));
	        }
	        
	        for (Tuple7<String,String,String,String,String,String,String> tuple7 : tuple7List) {
	            collect(tuple7);
	        }
	        
        } catch (Exception e) {
        	LOG.error("QanatYidaToTagsUDTF failed", e);
        }
    }
    
    private static String formatNumber(String num) {
    	if (StringUtils.isBlank(num)) {
    		return num;
    	}
    	String reg = "^[0-9]\\d*(\\.0+)?$";
    	Pattern p = Pattern.compile(reg);
    	Matcher m = p.matcher(num);
    	while (m.find()) {
    		if (m.group(1) == null) {
    			break;
    		}
    		num = num.replace(m.group(1), "");
    		break;
    	}
    	return num;
    }
    
    public static void main(String [] args) {
    	String str = "0";
    	System.out.println(formatNumber(str));
    }
}