package com.aliyun.wormhole.qanat.blink.udtf;

import com.alibaba.fastjson.JSON;
import com.aliyun.wormhole.qanat.drc.event.DataMessage;
import com.aliyun.wormhole.qanat.drc.event.DbEventInfo;
import com.aliyun.wormhole.qanat.drc.event.DrcMessageParser;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.TableFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatDrcTransform4RdsUDTF extends TableFunction<String> {
    
    private final static Logger LOG = LoggerFactory.getLogger(QanatDrcTransform4RdsUDTF.class);

    public void eval(String msg, String tableName) {
        LOG.info("eval({},{})", msg, tableName);
        if (StringUtils.isBlank(msg) || (StringUtils.isBlank(tableName))) {
            return;
        }
        DataMessage.Record record = JSON.parseObject(msg, DataMessage.Record.class);
        String currType = record.getOpt().toString();
        DbEventInfo dbEventInfo = null;
        try {
            if (tableName.equalsIgnoreCase(record.getTablename())) {
                dbEventInfo = DrcMessageParser.parse(record, currType);
                if(dbEventInfo != null) {
                    String json = JSON.toJSONString(dbEventInfo);
                    LOG.info("json={}", json);
                    collect(json);
                }
            }
        } catch (Throwable e) {
            LOG.info("parse error:" + e);
        }
    }

    public void eval(String msg) {
        LOG.info("eval({})", msg);
        if (StringUtils.isBlank(msg)) {
            return;
        }
        DataMessage.Record record = JSON.parseObject(msg, DataMessage.Record.class);
        String currType = record.getOpt().toString();
        DbEventInfo dbEventInfo = null;
        try {
            dbEventInfo = DrcMessageParser.parse(record, currType);
            if(dbEventInfo != null) {
                String json = JSON.toJSONString(dbEventInfo);
                LOG.info("json={}", json);
                collect(json);
            }
        } catch (Throwable e) {
            LOG.info("parse error:" + e);
        }
    }
}
