package com.aliyun.wormhole.qanat.blink.udtf;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.aliyun.wormhole.qanat.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.datasource.QanatTddlDatasourceHandler;
import com.aliyun.wormhole.qanat.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.datasource.TddlConnectionParam;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.TableFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatGroupConcatUDTF extends TableFunction<String> {

    private final static Logger log = LoggerFactory.getLogger(QanatGroupConcatUDTF.class);

    public void eval(String dbName, String sql, String tokenizer, String aggField, String groupField, Object ... values) {
        log.info("eval({},{},{},{},{},{})", dbName, sql, tokenizer, aggField, groupField, JSON.toJSONString(values));
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        try {
            String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
            log.info("dbMetaStr={}", dbMetaStr);
            if (StringUtils.isBlank(dbMetaStr)) {
            	return;
            }
            JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
            if (dbMetaJson.containsKey("appName")) {
            	TddlConnectionParam param = new TddlConnectionParam();
            	param.setAppName(dbMetaJson.getString("appName"))
            		.setAccessKey("accessKey")
            		.setSecretKey("secretKey");
            	conn = QanatTddlDatasourceHandler.connectToTable(param);
            } else {
	            RdsConnectionParam param = new RdsConnectionParam();
	            param.setUrl(dbMetaJson.getString("jdbcUrl"))
	    	        .setUserName(dbMetaJson.getString("username"))
	    	        .setPassword(dbMetaJson.getString("password"));
	        	conn = QanatDatasourceHandler.connectToTable(param);
            }
        	ps = conn.prepareStatement(sql);
            if (values != null && values.length > 0) {
                for (int i = 0; i < values.length; i++) {
                	ps.setObject(i + 1, values[i]);
                }
            }
            rs = ps.executeQuery();
            List<Map<String, Object>> dataList = new ArrayList<>();
            while (rs.next()) {
                Map<String, Object> data = new HashMap<>();
                int columnCount = rs.getMetaData().getColumnCount();
                for (int i = 1; i <= columnCount; i++) {
                    data.put(rs.getMetaData().getColumnLabel(i), rs.getObject(i));
                }
                dataList.add(data);
            }
            if (CollectionUtils.isNotEmpty(dataList)) {
            	Map<Object,Set<Object>> groupValMap = new HashMap<>();
            	for (Map<String, Object> data : dataList) {
            		if (groupValMap.get(data.get(groupField)) != null) {
            			groupValMap.get(data.get(groupField)).add(data.get(aggField));
            		} else {
                        Set<Object> valueSet = new HashSet<>();
                        valueSet.add(data.get(aggField));
                        groupValMap.put(data.get(groupField), valueSet);
            		}
            	}
            	for (Object key : groupValMap.keySet()) {
            		collect(StringUtils.join(groupValMap.get(key), tokenizer));
            	}
            } else {
            	collect(" ");
            }
        } catch (Exception e) {
            log.error("QanatGroupConcatUDTF failed", e);
        } finally {
            if (ps != null) {
                try {
                	ps.close();
                } catch (SQLException e) {
                } finally {
                	ps = null;
                }
            }
            if (conn != null) {
                try {
                	conn.close();
                } catch (SQLException e) {
                } finally {
                	conn = null;
                }
            }
        }
    }
}
