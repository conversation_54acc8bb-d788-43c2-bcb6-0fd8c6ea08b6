package com.aliyun.wormhole.qanat.datasource;

import java.sql.Connection;

import javax.sql.DataSource;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.taobao.diamond.identify.CredentialService;
import com.taobao.diamond.identify.Credentials;
import com.taobao.tddl.client.jdbc.TDataSource;
import com.taobao.tddl.common.model.lifecycle.Lifecycle;
import com.taobao.tddl.jdbc.group.TGroupDataSource;

public class QanatTddlDatasourceHandler {
	private static Logger LOG = LoggerFactory.getLogger(QanatTddlDatasourceHandler.class);

    private static final ConnectionPool<DataSource> dataSourcePool = new ConnectionPool<>();
	private static DataSource tddlDataSource = null;
    
    public static Connection connectToTable(TddlConnectionParam param) {
		try {
			synchronized (QanatTddlDatasourceHandler.class) {
				if (dataSourcePool.contains(param.getAppName())) {
					tddlDataSource = dataSourcePool.get(param.getAppName());
				} else {
					if (!StringUtils.isEmpty(param.getAccessKey()) && !StringUtils.isEmpty(param.getSecretKey())) {
						CredentialService credentialService = CredentialService.getInstance();
						credentialService.setCredential(new Credentials(param.getAccessKey(), param.getSecretKey()));
					}
					if (!StringUtils.isEmpty(param.getDbGroupKey())) {
						TGroupDataSource dataSource = new TGroupDataSource();
						dataSource.setDbGroupKey(param.getDbGroupKey());
						dataSource.setAppName(param.getAppName());
						if(!StringUtils.isEmpty(param.getUnitName())){
							dataSource.setUnitName(param.getUnitName());
						}
						dataSource.init();
						tddlDataSource = dataSource;
					} else {
						TDataSource dataSource = new TDataSource();
						dataSource.setAppName(param.getAppName());
						dataSource.setSharding(param.isSharding());
						dataSource.setDynamicRule(param.isSharding());
						if (!StringUtils.isEmpty(param.getUnitName())) {
							dataSource.setUnitName(param.getUnitName());
						}
						dataSource.init();
						tddlDataSource = dataSource;
					}
					dataSourcePool.put(param.getAppName(), tddlDataSource);
				}
				return tddlDataSource.getConnection();
			}
		} catch (Exception e) {
			LOG.error("Exception while creating connection to TDDL.", e);
			throw new RuntimeException("GET TDDL CONNECTION FAILED", e);
		}
	}

	public static void closeDataSource(TddlConnectionParam param) {
		if (dataSourcePool.remove(param.getAppName()) && tddlDataSource != null) {
			((Lifecycle) tddlDataSource).destroy();
			tddlDataSource = null;
		}
	}
}