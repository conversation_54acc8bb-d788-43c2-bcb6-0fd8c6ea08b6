package com.aliyun.wormhole.qanat.datasource;

import java.sql.Connection;

import com.alibaba.druid.pool.DruidDataSource;

public class QanatDatasourceHandler {

    private static final ConnectionPool<DruidDataSource> dataSourcePool = new ConnectionPool<>();
    
    public static Connection connectToTable(RdsConnectionParam param) {
        try {
            synchronized (QanatDatasourceHandler.class) {
                DruidDataSource dataSource = null;
                if (dataSourcePool.contains(param.getUrl())) {
                    dataSource = dataSourcePool.get(param.getUrl());
                    try {
                    	return dataSource.getConnection();
                    } catch(Exception e) {
                        dataSource = param.buildDataSource();
                        dataSourcePool.put(param.getUrl(), dataSource);
                    	return dataSource.getConnection();
                    }
                } else {
                    dataSource = param.buildDataSource();
                    dataSourcePool.put(param.getUrl(), dataSource);
                	return dataSource.getConnection();
                }
            }
        } catch (Exception e) {
        	e.printStackTrace();
            throw new RuntimeException("failed to get connection for db:" + param.getUrl(), e);
        }
    }

    public static void closeDataSource(RdsConnectionParam param) {
        if (dataSourcePool.get(param.getUrl()) != null) {
            dataSourcePool.get(param.getUrl()).close();
            dataSourcePool.remove(param.getUrl());
        }
    }
}