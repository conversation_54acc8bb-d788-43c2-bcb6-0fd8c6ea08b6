package com.aliyun.wormhole.qanat.datasource;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

import com.alibaba.druid.pool.DruidDataSourceFactory;
import com.alibaba.fastjson.JSON;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class QanatDataSourceUtils {
    
    private static Map<String, String> dbMetaMap = new ConcurrentHashMap<>();
    private static Map<String, Connection> connectionMap = new ConcurrentHashMap<>();

    /**
     * 得到连接对象
     */
    public static Connection getConnection(Properties info) {
        try {
        	if (connectionMap.get(info.getProperty("url")) != null) {
        		try {
	        		if (!connectionMap.get(info.getProperty("url")).isClosed()) { 
	        			return connectionMap.get(info.getProperty("url"));
	        		}
        		} catch (Exception e) {
        		}
        	}
        	Connection conn = DruidDataSourceFactory.createDataSource(info).getConnection();
        	connectionMap.put(info.getProperty("url"), conn);	
        	return conn;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 释放资源
     */
    public static void close(Connection conn, Statement stmt, ResultSet rs) {
        if (rs!=null) {
            try {
                rs.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        if (stmt!=null) {
            try {
                stmt.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        if (conn!=null) {
            try {
                conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    public static void close(Connection conn, Statement stmt) {
        close(conn, stmt, null);
    }
    
    public static String getDbMeta(String dbName, ClassLoader loader) {
		if (dbMetaMap.get(dbName) != null) {
			return dbMetaMap.get(dbName);
		}
		log.info("reload dbMeta");
		Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {

            Properties info = new Properties();
            info.load(loader.getResourceAsStream("qanat.properties"));
            conn = QanatDataSourceUtils.getConnection(info);
            ps = conn.prepareStatement("select meta from db_info where db_name=? and is_deleted=0");
            ps.setObject(1, dbName);
            rs = ps.executeQuery();
            while (rs.next()) {
            	String dbMeta = rs.getString("meta");
            	dbMetaMap.put(dbName, dbMeta);
            	return dbMeta;
            }
        } catch (Exception e) {
            log.error("sql exec failed:{}", e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
        	QanatDataSourceUtils.close(conn, ps, rs);
        }
        return null;
	}
    
    public static String getDsInfo(String dsName, ClassLoader loader) {
		log.info("reload dbMeta");
		Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {

            Properties info = new Properties();
            info.load(loader.getResourceAsStream("qanat.properties"));
            conn = QanatDataSourceUtils.getConnection(info);
            ps = conn.prepareStatement("select * from datasource where ds_name=? and is_deleted=0");
            ps.setObject(1, dsName);
            rs = ps.executeQuery();
            while (rs.next()) {
            	int fieldCnt = rs.getMetaData().getColumnCount();
            	Map<String, Object> data = new HashMap<>();
            	for (int i = 0; i < fieldCnt; i++) {
                    String fieldName = rs.getMetaData().getColumnLabel(i + 1);
                    Object value = rs.getObject(fieldName);
                    data.put(fieldName, value);
            	}
            	return JSON.toJSONString(data);
            }
        } catch (Exception e) {
            log.error("sql exec failed:{}", e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
        	QanatDataSourceUtils.close(conn, ps, rs);
        }
        return null;
	}
    
    public static String getExtensionScriptByCode(String tenantId, String code, ClassLoader loader) {
		log.info("getExtensionByCode({})", code);
		Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            Properties info = new Properties();
            info.load(loader.getResourceAsStream("qanat.properties"));
            Class.forName("com.mysql.jdbc.Driver");
            conn = DriverManager.getConnection(info.getProperty("url"), info.getProperty("username"), info.getProperty("password"));
            ps = conn.prepareStatement("select script from extension where tenant_id=? and code=? and is_deleted=0", ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ps.setObject(1, tenantId);
            ps.setObject(2, code);
            rs = ps.executeQuery();
            while (rs.next()) {
            	return rs.getString("script");
            }
        } catch (Exception e) {
            log.error("getExtensionScriptByCode sql exec failed:{}", e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
        	if (rs != null) {
                try {
                    rs.close();
                } catch (SQLException e) {
                    rs = null;
                }
            }
            if (ps != null) {
                try {
                    ps.close();
                } catch (SQLException e) {
                    ps = null;
                }
            }
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    conn = null;
                }
            }
        }
        return null;
	}
}