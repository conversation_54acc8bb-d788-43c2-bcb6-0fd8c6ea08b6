/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.aliyun.wormhole.qanat.datasource;

import java.io.Serializable;

public class TddlConnectionParam implements Serializable {

	private static final long serialVersionUID = -764510949279683113L;

	private String appName;
	private String tableName;
	private String dbGroupKey;
	private boolean isSharding = false;
	private String accessKey;
	private String secretKey;
	private String unitName;
	private String dialect;
	private int maxRetries = 3;
	private int maxFetchResult = 1024;

	public String getAppName() {
		return appName;
	}

	public TddlConnectionParam setAppName(String appName) {
		this.appName = appName;
		return this;
	}

	public String getDbGroupKey() {
		return dbGroupKey;
	}

	public TddlConnectionParam setDbGroupKey(String dbGroupKey) {
		this.dbGroupKey = dbGroupKey;
		return this;
	}

	public String getTableName() {
		return tableName;
	}

	public TddlConnectionParam setTableName(String tableName) {
		this.tableName = tableName;
		return this;
	}

	public boolean isSharding() {
		return isSharding;
	}

	public TddlConnectionParam setSharding(boolean sharding) {
		isSharding = sharding;
		return this;
	}

	public String getAccessKey() {
		return accessKey;
	}

	public TddlConnectionParam setAccessKey(String accessKey) {
		this.accessKey = accessKey;
		return this;
	}

	public String getSecretKey() {
		return secretKey;
	}

	public TddlConnectionParam setSecretKey(String secretKey) {
		this.secretKey = secretKey;
		return this;
	}

	public String getUnitName() {
		return unitName;
	}

	public TddlConnectionParam setUnitName(String unitName) {
		this.unitName = unitName;
		return this;
	}

	public String getDialect() {
		return dialect;
	}

	public TddlConnectionParam setDialect(String dialect) {
		this.dialect = dialect;
		return this;
	}

	public int getMaxRetries() {
		return maxRetries;
	}

	public TddlConnectionParam setMaxRetries(int maxRetries) {
		this.maxRetries = maxRetries;
		return this;
	}

	public int getMaxFetchResult() {
		return maxFetchResult;
	}

	public TddlConnectionParam setMaxFetchResult(int maxFetchResult) {
		this.maxFetchResult = maxFetchResult;
		return this;
	}
}
