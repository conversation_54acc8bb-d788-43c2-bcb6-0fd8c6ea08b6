/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.odps.source;

import com.alibaba.blink.table.cache.CacheConfig;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.TableEnvironment;
import org.apache.flink.table.sources.TableSource;
import org.apache.flink.table.types.DataTypes;
import org.apache.flink.table.types.InternalType;
import org.apache.flink.types.Row;
import org.junit.Ignore;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertTrue;

public class ODPSTableSourceITCase extends BaseODPSTableSourceITCase {

	@Override
	public TableEnvironment createTableEnvironment() {
		return batchTableEnv;
	}

	@Override
	public void registerStream(String tableName, DataStream<Row> stream, String fields) {
		batchTableEnv.registerBoundedStream(tableName, stream, fields);
	}

	@Override
	public DataStream<Row> table2DataStream(Table t) {
		return batchTableEnv.toBoundedStream(t, Row.class);
	}

	@Override
	public TableSource createOdpsTableSource(
			String tableName,
			String odpsTableName,
			RichTableSchema richTableSchema,
			List<String> userSpecificPartitions,
			CacheConfig cacheConfig) {
		ODPSTableSource source = ODPSTableSource.builder()
												.setTableName(tableName)
												.setTable(odpsTableName)
												.setAccessId(accessId)
												.setAccessKey(accessKey)
												.setEndpoint(endpoint)
												.setProject(project)
												.setRichSchema(richTableSchema)
												.setUserSpecificPartitions(userSpecificPartitions)
												.setCacheConfig(cacheConfig)
												.build();
		return source;
	}


	@Test
	public void testExplainWithFilter() {
		RichTableSchema schema = new RichTableSchema(
				new String[]{"cookie", "ip", "filter"},
				new InternalType[]{ DataTypes.STRING, DataTypes.STRING, DataTypes.STRING});
		ODPSTableSource s = (ODPSTableSource) createOdpsTableSource(
				"t", partitionedTableName, schema, new ArrayList<>(), cacheConfig);
		String before = s.explainSource();
		s = (ODPSTableSource) s.applyPrunedPartitionsAndPredicate(true,  new ArrayList<>(), new ArrayList<>());
		String after = s.explainSource();
		assertNotEquals(before, after);
		assertTrue(after.contains("isFilterPushedDown: true"));
	}

	@Test
	public void testProjectEmptyFields() {
		RichTableSchema schema = new RichTableSchema(
				new String[]{"cookie", "ip", "filter"},
				new InternalType[]{ DataTypes.STRING, DataTypes.STRING, DataTypes.STRING});
		ODPSTableSource s = (ODPSTableSource) createOdpsTableSource(
				"t", partitionedTableName, schema, new ArrayList<>(), cacheConfig);
		// Get first field if input project fields is empty
		TableSource projectedSource = s.projectFields(new int[]{});
		assertTrue(projectedSource.getTableSchema().getFieldCount() != 0);
	}
}
