/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.odps.util;

import com.aliyun.odps.OdpsException;
import com.aliyun.odps.Partition;
import com.aliyun.odps.PartitionSpec;
import com.aliyun.wormhole.qanat.odps.util.ODPSUtil;
import com.aliyun.wormhole.qanat.odps.util.PartitionConditionParser;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;
import java.util.ArrayList;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class PartitionConditionParserTest {

	@Test
	public void testMaxPtEmptyResultWithoutSpecifiedPartColumn() throws OdpsException {
		List<String> partitionStrs = new ArrayList<>();
		List<Partition> partitions = getPartitions(partitionStrs);
		List<String> conditions = new ArrayList<>();
		conditions.add("max_pt()");
		assertTrue(PartitionConditionParser.filter(partitions, conditions).isEmpty());
	}

	@Test
	public void testMaxPtOnePartitionFieldWithoutSpecifiedPartColumn() throws OdpsException {
		List<String> partitionStrs = new ArrayList<>();
		partitionStrs.add("p=1");
		partitionStrs.add("p=5");
		partitionStrs.add("p=3");
		partitionStrs.add("p=2");
		List<Partition> partitions = getPartitions(partitionStrs);
		List<String> conditions = new ArrayList<>();
		conditions.add("max_pt()");
		PartitionConditionParser.filter(partitions, conditions);
		List<Partition> matchedPartitions = PartitionConditionParser.filter(partitions, conditions);
		assertTrue(matchedPartitions.size() == 1);
		assertEquals("p=5", ODPSUtil.partitionSpecToString(matchedPartitions.get(0).getPartitionSpec()));
	}

	@Test
	public void testMaxPtWithDoneOnePartitionFieldWithoutSpecifiedPartColumn() throws OdpsException {
		List<String> partitionStrs = new ArrayList<>();
		partitionStrs.add("p=1");
		partitionStrs.add("p=5");
		partitionStrs.add("p=3");
		partitionStrs.add("p=3.done");
		partitionStrs.add("p=2");
		List<Partition> partitions = getPartitions(partitionStrs);
		List<String> conditions = new ArrayList<>();
		conditions.add("max_pt_with_done()");
		PartitionConditionParser.filter(partitions, conditions);
		List<Partition> matchedPartitions = PartitionConditionParser.filter(partitions, conditions);
		assertTrue(matchedPartitions.size() == 1);
		assertEquals("p=3", ODPSUtil.partitionSpecToString(matchedPartitions.get(0).getPartitionSpec()));
	}

	@Test
	public void tesMaxPtTwoPartitionFieldWithoutSpecifiedPartColumn() throws OdpsException {
		List<String> partitionStrs = new ArrayList<>();
		partitionStrs.add("p=1,q=a");
		partitionStrs.add("p=5,q=b");
		partitionStrs.add("p=3,q=c");
		partitionStrs.add("p=2,q=d");
		List<Partition> partitions = getPartitions(partitionStrs);
		List<String> conditions = new ArrayList<>();
		conditions.add("max_pt()");
		PartitionConditionParser.filter(partitions, conditions);
		List<Partition> matchedPartitions = PartitionConditionParser.filter(partitions, conditions);
		assertTrue(matchedPartitions.size() == 1);
		assertEquals("p=5,q=b", ODPSUtil.partitionSpecToString(matchedPartitions.get(0).getPartitionSpec()));
	}

	@Test
	public void tesMaxPtWithDoneTwoPartitionFieldWithoutSpecifiedPartColumn() throws OdpsException {
		List<String> partitionStrs = new ArrayList<>();
		partitionStrs.add("p=1,q=a");
		partitionStrs.add("p=1,q=a.done");
		partitionStrs.add("p=5,q=b");
		partitionStrs.add("p=3,q=c");
		partitionStrs.add("p=2,q=d");
		List<Partition> partitions = getPartitions(partitionStrs);
		List<String> conditions = new ArrayList<>();
		conditions.add("max_pt_with_done()");
		PartitionConditionParser.filter(partitions, conditions);
		List<Partition> matchedPartitions = PartitionConditionParser.filter(partitions, conditions);
		assertTrue(matchedPartitions.size() == 1);
		assertEquals("p=1,q=a", ODPSUtil.partitionSpecToString(matchedPartitions.get(0).getPartitionSpec()));
	}

	@Test
	public void testMaxPtOnePartitionFieldWithSpecifiedPartColumn() throws OdpsException {
		List<String> partitionStrs = new ArrayList<>();
		partitionStrs.add("p=1");
		partitionStrs.add("p=5");
		partitionStrs.add("p=3");
		partitionStrs.add("p=2");
		List<Partition> partitions = getPartitions(partitionStrs);
		List<String> conditions = new ArrayList<>();
		conditions.add("p=max_pt()");
		PartitionConditionParser.filter(partitions, conditions);
		List<Partition> matchedPartitions = PartitionConditionParser.filter(partitions, conditions);
		assertTrue(matchedPartitions.size() == 1);
		assertEquals("p=5", ODPSUtil.partitionSpecToString(matchedPartitions.get(0).getPartitionSpec()));
	}

	@Test
	public void testMaxPtWithDoneOnePartitionFieldWithSpecifiedPartColumn() throws OdpsException {
		List<String> partitionStrs = new ArrayList<>();
		partitionStrs.add("p=1.done");
		partitionStrs.add("p=1");
		partitionStrs.add("p=5");
		partitionStrs.add("p=3");
		partitionStrs.add("p=3");
		partitionStrs.add("p=2");
		List<Partition> partitions = getPartitions(partitionStrs);
		List<String> conditions = new ArrayList<>();
		conditions.add("p=max_pt_with_done()");
		PartitionConditionParser.filter(partitions, conditions);
		List<Partition> matchedPartitions = PartitionConditionParser.filter(partitions, conditions);
		assertTrue(matchedPartitions.size() == 1);
		assertEquals("p=1", ODPSUtil.partitionSpecToString(matchedPartitions.get(0).getPartitionSpec()));
	}

	@Test
	public void tesMaxPtTwoPartitionFieldWithSpecifiedPartColumn() throws OdpsException {
		List<String> partitionStrs = new ArrayList<>();
		partitionStrs.add("p=2,q=a");
		partitionStrs.add("p=1,q=a");
		partitionStrs.add("p=5,q=b");
		partitionStrs.add("p=3,q=c");
		partitionStrs.add("p=3#,q=c");
		partitionStrs.add("p=2,q=c");
		List<Partition> partitions = getPartitions(partitionStrs);
		List<String> conditions = new ArrayList<>();
		conditions.add("q=c,p=max_pt()");
		PartitionConditionParser.filter(partitions, conditions);
		List<Partition> matchedPartitions = PartitionConditionParser.filter(partitions, conditions);
		assertTrue(matchedPartitions.size() == 1);
		assertEquals("p=3#,q=c", ODPSUtil.partitionSpecToString(matchedPartitions.get(0).getPartitionSpec()));
	}

	@Test
	public void tesMaxPtWithDoneTwoPartitionFieldWithSpecifiedPartColumn() throws OdpsException {
		List<String> partitionStrs = new ArrayList<>();
		partitionStrs.add("p=2,q=a");
		partitionStrs.add("p=1,q=a");
		partitionStrs.add("p=5,q=b");
		partitionStrs.add("p=3,q=c");
		partitionStrs.add("p=3#,q=c");
		partitionStrs.add("p=3,q=c.done");
		partitionStrs.add("p=2,q=c");
		List<Partition> partitions = getPartitions(partitionStrs);
		List<String> conditions = new ArrayList<>();
		conditions.add("q=c,p=max_pt_with_done()");
		PartitionConditionParser.filter(partitions, conditions);
		List<Partition> matchedPartitions = PartitionConditionParser.filter(partitions, conditions);
		assertTrue(matchedPartitions.size() == 1);
		assertEquals("p=3,q=c", ODPSUtil.partitionSpecToString(matchedPartitions.get(0).getPartitionSpec()));
	}

	@Test
	public void testMaxPtEmptyResultWithSpecifiedPartColumn() throws OdpsException {
		List<String> partitionStrs = new ArrayList<>();
		partitionStrs.add("p=2,q=a");
		partitionStrs.add("p=1,q=a");
		partitionStrs.add("p=5,q=b");
		partitionStrs.add("p=3,q=c");
		partitionStrs.add("p=3#,q=c");
		partitionStrs.add("p=2,q=c");
		List<Partition> partitions = getPartitions(partitionStrs);
		List<String> conditions = new ArrayList<>();
		conditions.add("q=abc,p=max_pt()");
		PartitionConditionParser.filter(partitions, conditions);
		List<Partition> matchedPartitions = PartitionConditionParser.filter(partitions, conditions);
		assertTrue(matchedPartitions.size() == 0);
	}

	@Test
	public void testMaxPtWithDoneEmptyResultWithSpecifiedPartColumn() throws OdpsException {
		List<String> partitionStrs = new ArrayList<>();
		partitionStrs.add("p=2,q=a");
		partitionStrs.add("p=1,q=a");
		partitionStrs.add("p=5,q=b");
		partitionStrs.add("p=3,q=c");
		partitionStrs.add("p=3#,q=c");
		partitionStrs.add("p=2,q=c");
		List<Partition> partitions = getPartitions(partitionStrs);
		List<String> conditions = new ArrayList<>();
		conditions.add("q=abc,p=max_pt_with_done()");
		PartitionConditionParser.filter(partitions, conditions);
		List<Partition> matchedPartitions = PartitionConditionParser.filter(partitions, conditions);
		assertTrue(matchedPartitions.size() == 0);
	}

	@Test
	public void testFilter() throws Exception {
		List<String> partitionStrs = new ArrayList<>();
		partitionStrs.add("ds=20170303,i=0");
		partitionStrs.add("ds=20170303,i=1/odd");
		partitionStrs.add("ds=20170303,i=2");
		partitionStrs.add("ds=20170303,i=3/odd");
		partitionStrs.add("ds=20170303,i=4");
		List<Partition> partitions = getPartitions(partitionStrs);

		List<String> conditions = new ArrayList<>();
		conditions.add("regex:ds=201703*odd");
		conditions.add("*i=2*");
		conditions.add("ds=20170303,i=0");

		List<Partition> matchedPartitions = PartitionConditionParser.filter(partitions, conditions);
		assertEquals(4, matchedPartitions.size());
		assertEquals("ds=20170303,i=0", ODPSUtil.partitionSpecToString(matchedPartitions.get(0).getPartitionSpec()));
		assertEquals("ds=20170303,i=1/odd", ODPSUtil.partitionSpecToString(matchedPartitions.get(1).getPartitionSpec()));
		assertEquals("ds=20170303,i=2", ODPSUtil.partitionSpecToString(matchedPartitions.get(2).getPartitionSpec()));
		assertEquals("ds=20170303,i=3/odd", ODPSUtil.partitionSpecToString(matchedPartitions.get(3).getPartitionSpec()));
	}

	@Test
	public void testFilterWithComplexConditions() throws Exception {
		List<String> partitionStrs = new ArrayList<>();
		partitionStrs.add("ds=20170303,i=0");
		partitionStrs.add("ds=20170303,i=1/odd");
		partitionStrs.add("ds=20170303,i=2");
		partitionStrs.add("ds=20170303,i=3/odd");
		partitionStrs.add("ds=20170303,i=4.done");
		partitionStrs.add("ds=20170303,i=4");
		partitionStrs.add("ds=20170303,i=5");
		List<Partition> partitions = getPartitions(partitionStrs);

		List<String> conditions = new ArrayList<>();
		conditions.add("regex:ds=201703*odd");
		conditions.add("*i=2*");
		conditions.add("ds=20170303,i=max_pt()");
		conditions.add("ds=20170303,i=max_pt_with_done()");

		List<Partition> matchedPartitions = PartitionConditionParser.filter(partitions, conditions);
		assertEquals(5, matchedPartitions.size());
		assertEquals("ds=20170303,i=1/odd", ODPSUtil.partitionSpecToString(matchedPartitions.get(0).getPartitionSpec()));
		assertEquals("ds=20170303,i=2", ODPSUtil.partitionSpecToString(matchedPartitions.get(1).getPartitionSpec()));
		assertEquals("ds=20170303,i=3/odd", ODPSUtil.partitionSpecToString(matchedPartitions.get(2).getPartitionSpec()));
		assertEquals("ds=20170303,i=4", ODPSUtil.partitionSpecToString(matchedPartitions.get(3).getPartitionSpec()));
		assertEquals("ds=20170303,i=5", ODPSUtil.partitionSpecToString(matchedPartitions.get(4).getPartitionSpec()));
	}

	private List<Partition> getPartitions(List<String> partitions) {
		List<Partition> partitionList = new ArrayList<>();
		for (String partition: partitions) {
			Partition p = mock(Partition.class);
			when(p.getPartitionSpec()).thenReturn(new PartitionSpec(partition));
			partitionList.add(p);
		}
		return partitionList;
	}
}
