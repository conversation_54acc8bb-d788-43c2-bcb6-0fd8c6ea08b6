/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.blink.connectors.odps.outputformat;

import com.alibaba.blink.streaming.connectors.common.util.RowConverter;
import com.aliyun.odps.Column;
import com.aliyun.odps.OdpsType;
import com.aliyun.odps.data.ArrayRecord;
import com.aliyun.odps.data.Record;
import com.aliyun.wormhole.qanat.odps.outputformat.ODPSRecordConverter;
import org.apache.flink.types.Row;
import org.junit.Assert;
import org.junit.Test;


public class ODPSRecordConverterTest {
	@Test
	public void convertRecord() throws Exception {
		RowConverter<Row, Record> converter = new ODPSRecordConverter();
		Row row = new Row(1);
		Column[] columns = new Column[1];
		Record record;

		columns[0] = new Column("id", OdpsType.BIGINT, "");
		record = new ArrayRecord(columns);
		row.setField(0, null);
		converter.convert(row, record);

		columns[0] = new Column("id", OdpsType.BIGINT, "");
		record = new ArrayRecord(columns);
		row.setField(0, (byte) 1);
		converter.convert(row, record);
		row.setField(0, (short) 1);
		converter.convert(row, record);
		row.setField(0, 1);
		converter.convert(row, record);
		row.setField(0, 1L);
		converter.convert(row, record);
		row.setField(0, 'a');
		converter.convert(row, record);

		columns[0] = new Column("id", OdpsType.DOUBLE, "");
		record = new ArrayRecord(columns);
		row.setField(0, (byte) 1);
		converter.convert(row, record);
		row.setField(0, (short) 1);
		converter.convert(row, record);
		row.setField(0, 1);
		converter.convert(row, record);
		row.setField(0, 1L);
		converter.convert(row, record);
		row.setField(0, 1.0f);
		converter.convert(row, record);
		row.setField(0, 1.0);
		converter.convert(row, record);
		row.setField(0, true);
		converter.convert(row, record);
		row.setField(0, 'a');
		converter.convert(row, record);

		columns[0] = new Column("id", OdpsType.FLOAT, "");
		record = new ArrayRecord(columns);
		row.setField(0, (byte) 1);
		converter.convert(row, record);
		row.setField(0, (short) 1);
		converter.convert(row, record);
		row.setField(0, 1);
		converter.convert(row, record);
		row.setField(0, 1L);
		converter.convert(row, record);
		row.setField(0, 1.0f);
		converter.convert(row, record);
		row.setField(0, true);
		converter.convert(row, record);
		row.setField(0, 'a');
		converter.convert(row, record);

		columns[0] = new Column("id", OdpsType.BOOLEAN, "");
		record = new ArrayRecord(columns);
		row.setField(0, (byte) 1);
		converter.convert(row, record);
		row.setField(0, (short) 1);
		converter.convert(row, record);
		row.setField(0, 1);
		converter.convert(row, record);
		row.setField(0, 1L);
		converter.convert(row, record);
		row.setField(0, 1.0f);
		converter.convert(row, record);
		row.setField(0, 1.0);
		converter.convert(row, record);
		row.setField(0, true);
		converter.convert(row, record);
		row.setField(0, 'a');
		converter.convert(row, record);

		columns[0] = new Column("id", OdpsType.INT, "");
		record = new ArrayRecord(columns);
		row.setField(0, (byte) 1);
		converter.convert(row, record);
		row.setField(0, (short) 1);
		converter.convert(row, record);
		row.setField(0, 1);
		converter.convert(row, record);
		row.setField(0, true);
		converter.convert(row, record);
		row.setField(0, 'a');
		converter.convert(row, record);

		columns[0] = new Column("id", OdpsType.SMALLINT, "");
		record = new ArrayRecord(columns);
		row.setField(0, (byte) 1);
		converter.convert(row, record);
		row.setField(0, (short) 1);
		converter.convert(row, record);
		row.setField(0, true);
		converter.convert(row, record);
		row.setField(0, 'a');
		converter.convert(row, record);

		columns[0] = new Column("id", OdpsType.TINYINT, "");
		record = new ArrayRecord(columns);
		row.setField(0, (byte) 1);
		converter.convert(row, record);
		row.setField(0, true);
		converter.convert(row, record);
		row.setField(0, 'a');
		converter.convert(row, record);

		columns[0] = new Column("id", OdpsType.STRING, "");
		record = new ArrayRecord(columns);
		row.setField(0, (byte) 1);
		converter.convert(row, record);
		row.setField(0, (short) 1);
		converter.convert(row, record);
		row.setField(0, 1);
		converter.convert(row, record);
		row.setField(0, 1L);
		converter.convert(row, record);
		row.setField(0, 1.0f);
		converter.convert(row, record);
		row.setField(0, 1.0);
		converter.convert(row, record);
		row.setField(0, true);
		converter.convert(row, record);
		row.setField(0, 'a');
		converter.convert(row, record);
		row.setField(0, 1111111111111111111111.0);
		converter.convert(row, record);
		Assert.assertTrue(record.get(0).toString().equalsIgnoreCase("1.1111111111111111e21"));
		row.setField(0, 0.000001);
		converter.convert(row, record);
		Assert.assertEquals("0.000001", record.get(0));
		row.setField(0, 0.0000001);
		converter.convert(row, record);
		Assert.assertTrue(record.get(0).toString().equalsIgnoreCase("1.0e-7"));
		row.setField(0, 111111111111111111111.0);
		converter.convert(row, record);
		Assert.assertEquals("111111111111111110000.0", record.get(0));
		row.setField(0, 100000000000000000000.0);
		converter.convert(row, record);
		Assert.assertEquals("100000000000000000000.0", record.get(0));
	}

}
