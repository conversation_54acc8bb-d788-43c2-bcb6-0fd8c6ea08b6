/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.odps.source;

import com.alibaba.blink.connectors.odps.schema.ODPSColumn;
import com.alibaba.blink.connectors.odps.split.ODPSInputSplit;
import com.alibaba.blink.connectors.odps.split.ODPSPartitionSegmentDownloadDesc;

import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.util.TestLogger;

import com.aliyun.odps.PartitionSpec;
import com.aliyun.odps.OdpsType;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.tunnel.TableTunnel;
import com.aliyun.odps.tunnel.io.TunnelRecordReader;
import com.aliyun.wormhole.qanat.odps.conf.ODPSConf;

import java.util.List;

import org.junit.Test;
import org.mockito.ArgumentCaptor;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyBoolean;
import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.times;

public class ODPSRecordReaderTest extends TestLogger {
	@Test
	public void testRead() throws Exception {
		final TableTunnel tableTunnel = mock(TableTunnel.class);
		final TableTunnel.DownloadSession downloadSession = mock(TableTunnel.DownloadSession.class);
		final TunnelRecordReader tunnelRecordReader1 = mock(TunnelRecordReader.class);
		final TunnelRecordReader tunnelRecordReader2 = mock(TunnelRecordReader.class);

		when(tableTunnel.getDownloadSession(anyString(), anyString(), any(PartitionSpec.class), anyString()))
			.thenReturn(downloadSession);
		when(downloadSession.openRecordReader(anyLong(), anyLong(), anyBoolean(), anyList()))
			.thenReturn(tunnelRecordReader1, tunnelRecordReader2);

		final Record record1 = mock(Record.class);
		final Record record2 = mock(Record.class);
		final Record record3 = mock(Record.class);
		when(tunnelRecordReader1.read()).thenReturn(record1, record2, null);
		when(tunnelRecordReader2.read()).thenReturn(record3, null);

		ODPSColumn column = new ODPSColumn("partition", OdpsType.STRING, true);
		ODPSColumn[] columns = new ODPSColumn[]{column};
		final ODPSRecordReader recordReader = new ODPSRecordReader(
			tableTunnel,
			new ODPSConf("AccessID", "AccessKey", "Endpoint", "Project", "TunnelEndpoint"),
			"TableName",
			columns);
		final ODPSInputSplit split = new ODPSInputSplit(
			1,
			new ODPSPartitionSegmentDownloadDesc(
				new PartitionSpec("partition=1").toString(), 0, 2, "downloadSession1"),
			new ODPSPartitionSegmentDownloadDesc(
				new PartitionSpec("partition=2").toString(), 1024, 1, "downloadSession2"));
		recordReader.open(split, mock(RuntimeContext.class));

		Tuple2<String, Long> progress = recordReader.getProgress();
		assertEquals("partition=1", progress.f0.replaceAll("\'", ""));
		assertEquals(0L, progress.f1.longValue());

		assertTrue(recordReader.next());
		assertEquals("1", recordReader.getMessage().getString(0));

		progress = recordReader.getProgress();
		assertEquals("partition=1", progress.f0.replaceAll("\'", ""));
		assertEquals(1L, progress.f1.longValue());

		assertTrue(recordReader.next());

		progress = recordReader.getProgress();
		assertEquals("partition=1", progress.f0.replaceAll("\'", ""));
		assertEquals(2L, progress.f1.longValue());

		assertEquals("1", recordReader.getMessage().getString(0));

		assertTrue(recordReader.next());
		assertEquals("2", recordReader.getMessage().getString(0));
		assertFalse(recordReader.next());

		progress = recordReader.getProgress();
		assertEquals("partition=2", progress.f0.replaceAll("\'", ""));
		assertEquals(1L, progress.f1.longValue());
	}

	@Test
	public void testSeek() throws Exception {
		final TableTunnel tableTunnel = mock(TableTunnel.class);
		final TableTunnel.DownloadSession downloadSession = mock(TableTunnel.DownloadSession.class);
		final TunnelRecordReader tunnelRecordReader1 = mock(TunnelRecordReader.class);
		final TunnelRecordReader tunnelRecordReader2 = mock(TunnelRecordReader.class);

		when(tableTunnel.getDownloadSession(anyString(), anyString(), any(PartitionSpec.class), anyString()))
			.thenReturn(downloadSession);
		when(downloadSession.openRecordReader(anyLong(), anyLong(), anyBoolean(), any(List.class)))
			.thenReturn(tunnelRecordReader1, tunnelRecordReader2);

		ODPSColumn column = new ODPSColumn("partition", OdpsType.STRING, true);
		ODPSColumn[] columns = new ODPSColumn[]{column};
		final ODPSRecordReader recordReader = new ODPSRecordReader(
			tableTunnel,
			new ODPSConf("AccessID", "AccessKey", "Endpoint", "Project", "TunnelEndpoint"),
			"TableName",
			columns);
		final ODPSInputSplit split = new ODPSInputSplit(
			1,
			new ODPSPartitionSegmentDownloadDesc(
				new PartitionSpec("partition=1").toString(), 0, 1024, "downloadSession1"),
			new ODPSPartitionSegmentDownloadDesc(
				new PartitionSpec("partition=2").toString(), 1024, 9527, "downloadSession2"));
		recordReader.open(split, mock(RuntimeContext.class));

		recordReader.seek(new Tuple2<>(new PartitionSpec("partition=2").toString(), 1024L));

		final ArgumentCaptor<String> downloadSessionCaptor = ArgumentCaptor.forClass(String.class);
		verify(tableTunnel, times(2)).getDownloadSession(
			anyString(), anyString(), any(PartitionSpec.class), downloadSessionCaptor.capture());
		final List<String> downloadSessionList = downloadSessionCaptor.getAllValues();
		assertEquals(2, downloadSessionList.size());
		assertEquals("downloadSession1", downloadSessionList.get(0));
		assertEquals("downloadSession2", downloadSessionList.get(1));

		final ArgumentCaptor<Long> startCaptor = ArgumentCaptor.forClass(Long.class);
		final ArgumentCaptor<Long> countCaptor = ArgumentCaptor.forClass(Long.class);
		verify(downloadSession, times(2))
			.openRecordReader(startCaptor.capture(), countCaptor.capture(), anyBoolean(), anyList());
		final List<Long> startList = startCaptor.getAllValues();
		final List<Long> countList = countCaptor.getAllValues();

		assertEquals(2, startList.size());
		assertEquals(0L, startList.get(0).longValue());
		assertEquals(1024L + 1024L, startList.get(1).longValue());

		assertEquals(2, countList.size());
		assertEquals(1024L, countList.get(0).longValue());
		assertEquals(9527L - 1024L, countList.get(1).longValue());
	}
}
