/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * 	 http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.odps.source;

import com.alibaba.blink.table.cache.CacheConfig;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.TableEnvironment;
import org.apache.flink.table.api.java.StreamTableEnvironment;
import org.apache.flink.table.sources.TableSource;
import org.apache.flink.types.Row;
import org.junit.Ignore;

import java.util.List;

@Ignore
public class ODPSStreamTableSourceITCase extends BaseODPSTableSourceITCase {

	@Override
	public TableEnvironment createTableEnvironment() {
		return TableEnvironment.getTableEnvironment(env);
	}

	@Override
	public void registerStream(String tableName, DataStream<Row> stream, String fields) {
		((StreamTableEnvironment) tEnv).registerDataStream(tableName, stream, fields + ", proc.proctime");
	}

	@Override
	public DataStream<Row> table2DataStream(Table t) {
		return ((StreamTableEnvironment) tEnv).toAppendStream(t, Row.class);
	}

	@Override
	public TableSource createOdpsTableSource(
			String tableName,
			String odpsTableName,
			RichTableSchema richTableSchema,
			List<String> userSpecificPartitions,
			CacheConfig cacheConfig) {
		return ODPSStreamTableSource.builder()
									.setTableName(tableName)
									.setTable(odpsTableName)
									.setAccessId(accessId)
									.setAccessKey(accessKey)
									.setEndpoint(endpoint)
									.setProject(project)
									.setRichSchema(richTableSchema)
									.setUserSpecificPartitions(userSpecificPartitions)
									.setExitAfterReadFinish(true)
									.setCacheConfig(cacheConfig)
									.build();
	}

}
