/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.odps.source;

import com.alibaba.blink.connectors.odps.split.ODPSInputSplit;
import com.aliyun.odps.Table;
import com.aliyun.odps.tunnel.TableTunnel;
import org.apache.flink.util.TestLogger;
import org.mockito.InjectMocks;
import org.mockito.Mock;

public class ODPSContinuousMonitoringRecordReaderTest extends TestLogger {
	@Mock
	private ODPSRecordReader currentReader;

	@Mock
	private ODPSInputSplit currentSubSplit;

	@Mock
	private TableTunnel tableTunnel;

	@Mock
	private Table table;

	@InjectMocks
	private ODPSContinuousMonitoringRecordReader recordReader;
/*
	@Before
	public void setUp() {
		recordReader = new ODPSContinuousMonitoringRecordReader(
			tableTunnel, new ODPSConf("AccessID", "AccessKey", "Endpoint", "Project"), "TableName", null,null,false);
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void testSubscribeNewPartitions() throws Exception {
		Tuple3<String[], String, Long> cursor = new Tuple3<>(
			new String[]{
				new PartitionSpec("ds=20170309,hh=00").toString(),
				new PartitionSpec("ds=20170309,hh=01").toString()
			},
			new PartitionSpec("ds=20170309,hh=02").toString(),
			64L
		);
		recordReader.open(new GenericInputSplit(2, 16), null);
		recordReader.seek(cursor);
		TableTunnel.DownloadSession downloadSession = mock(TableTunnel.DownloadSession.class);
		when(tableTunnel.createDownloadSession(anyString(), anyString(), any(PartitionSpec.class)))
			.thenReturn(downloadSession);
		when(downloadSession.getId()).thenReturn("download_session_id");
		when(downloadSession.getRecordCount()).thenReturn(2048L);

		List<Partition> partitions = new LinkedList<>();
		Partition partition1 = mock(Partition.class);
		when(partition1.getPartitionSpec()).thenReturn(new PartitionSpec("ds=20170309,hh=01"));

		Partition partition2 = mock(Partition.class);
		when(partition2.getPartitionSpec()).thenReturn(new PartitionSpec("ds=20170309,hh=02"));

		Partition partition3 = mock(Partition.class);
		when(partition3.getPartitionSpec()).thenReturn(new PartitionSpec("ds=20170309,hh=03"));

		partitions.add(partition1);
		partitions.add(partition2);
		partitions.add(partition3);
		when(table.getPartitions()).thenReturn(partitions);

		ODPSInputSplit subscribedPartitions = recordReader.subscribeNewPartitions();
		assertEquals(2, subscribedPartitions.getPartitions().length);
		assertEquals(2, subscribedPartitions.getSplitNumber());

		ODPSPartitionSegmentDownloadDesc partitionSegmentDownloadDesc1 = subscribedPartitions.getPartitions()[0];
		assertEquals(new PartitionSpec("ds=20170309,hh=02").toString(), partitionSegmentDownloadDesc1.getPartition());
		assertEquals(2048 / 16 * 2 + 64, partitionSegmentDownloadDesc1.getStart());
		assertEquals(2048 / 16 - 64, partitionSegmentDownloadDesc1.getCount());

		ODPSPartitionSegmentDownloadDesc partitionSegmentDownloadDesc2 = subscribedPartitions.getPartitions()[1];
		assertEquals(new PartitionSpec("ds=20170309,hh=03").toString(), partitionSegmentDownloadDesc2.getPartition());
		assertEquals(2048 / 16 * 2, partitionSegmentDownloadDesc2.getStart());
		assertEquals(2048 / 16, partitionSegmentDownloadDesc2.getCount());
	}

	@Test
	public void testGetProgress() throws Exception {
		recordReader.open(new GenericInputSplit(0, 8), null);

		when(currentReader.getProgress())
			.thenReturn(new Tuple2<>(new PartitionSpec("ds=20170309,hh=03").toString(), 96L));
		when(currentSubSplit.getPartitions()).thenReturn(new ODPSPartitionSegmentDownloadDesc[]{
			new ODPSPartitionSegmentDownloadDesc(new PartitionSpec("ds=20170309,hh=01").toString(), 0, 128, "id"),
			new ODPSPartitionSegmentDownloadDesc(new PartitionSpec("ds=20170309,hh=02").toString(), 0, 128, "id"),
			new ODPSPartitionSegmentDownloadDesc(new PartitionSpec("ds=20170309,hh=03").toString(), 0, 128, "id"),
			new ODPSPartitionSegmentDownloadDesc(new PartitionSpec("ds=20170309,hh=04").toString(), 0, 128, "id"),
		});
		Tuple3<String[], String, Long> progressTuple = recordReader.getProgress();
		assertEquals(2, progressTuple.f0.length);
		assertEquals(new PartitionSpec("ds=20170309,hh=03").toString(), progressTuple.f1);
		assertEquals(96, progressTuple.f2.longValue());
	}
*/
}
