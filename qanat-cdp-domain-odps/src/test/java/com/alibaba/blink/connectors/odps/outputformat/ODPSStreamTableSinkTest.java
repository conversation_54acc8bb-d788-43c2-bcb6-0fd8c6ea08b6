/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *	 http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.odps.outputformat;

import com.alibaba.blink.connectors.odps.util.AccessKeyUtil;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.api.java.StreamTableEnvironment;
import org.apache.flink.table.sinks.TableSink;
import org.apache.flink.table.types.DataTypes;
import org.apache.flink.table.types.InternalType;
import org.apache.flink.types.Row;

import com.alibaba.blink.streaming.connectors.common.util.RowConverter;
import com.aliyun.odps.Odps;
import com.aliyun.odps.data.Record;
import com.aliyun.wormhole.qanat.odps.OdpsTableSink;
import com.aliyun.wormhole.qanat.odps.conf.ODPSConf;
import com.aliyun.wormhole.qanat.odps.outputformat.ODPSRecordConverter;
import com.aliyun.wormhole.qanat.odps.util.ODPSUtil;
import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by zw144119 on 2017/8/21.
 */
@Ignore
public class ODPSStreamTableSinkTest {

    private String accessKey = AccessKeyUtil.getAccessKey();
    private String accessId = AccessKeyUtil.getAccessId();
    private String endpoint = AccessKeyUtil.getEndpoint();
    private String project = AccessKeyUtil.getProject();
    private String table;

    @Before
    public void setUp() throws Exception {

    }

    @After
    public void tearDown() throws Exception {

    }

    @Test
    public void testUnpartitionTable() throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        StreamTableEnvironment tEnv = org.apache.flink.table.api.TableEnvironment.getTableEnvironment(env);
        org.apache.flink.table.api.Table in = tEnv.fromDataStream(getSmall2TupleDataSet(env), "a,b");
        org.apache.flink.table.api.Table res = in.select("a,b");

        ODPSConf conf = new ODPSConf(accessId, accessKey, endpoint, project, null);
        RichTableSchema flinkSchema =
                new RichTableSchema(
                new String[]{"owner_kp", "project_name"},
                new InternalType[]{
                        DataTypes.STRING,
                        DataTypes.STRING});
        OdpsTableSink odpsStreamTableSink =
                new OdpsTableSink(conf, table, flinkSchema, null, 100, 10000);
        odpsStreamTableSink.setCustomConverter(new TestConverter());
        res.writeToSink(odpsStreamTableSink);
        env.execute();
    }

    @Test
    public void testPartitionTable() throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        StreamTableEnvironment tEnv = org.apache.flink.table.api.TableEnvironment.getTableEnvironment(env);
		env.setParallelism(1);
        org.apache.flink.table.api.Table in = tEnv.fromDataStream(getSmall3TupleDataSet(env), "a,b,c");
        org.apache.flink.table.api.Table res = in.select("a,b,c");

        ODPSConf conf = new ODPSConf(accessId,accessKey,endpoint,project, null);
        Odps odps = ODPSUtil.initOdps(conf);
        String tableName = "xhbtest";
        String partition = "filter=qwe";
        ODPSUtil.clearData(conf, tableName, partition);
        RichTableSchema flinkSchema = new RichTableSchema(
                        new String[]{"cookie", "name", "ip"},
                        new InternalType[]{
                                DataTypes.STRING,
                                DataTypes.STRING,
                                DataTypes.STRING});
        TableSink simpleSink = new OdpsTableSink(conf, tableName, flinkSchema, partition, 100, 10000);

        res.writeToSink(simpleSink);
        env.execute();
    }

    public static DataStream<Tuple2<String, String>> getSmall2TupleDataSet(StreamExecutionEnvironment env) {
        List<Tuple2<String, String>> data = new ArrayList();
        data.add(new Tuple2<>("hi", "Hi"));
        data.add(new Tuple2<>("hello", "Hello"));
        data.add(new Tuple2<>("hello world", "Hello world!"));
        return env.fromCollection(data);
    }

    public static DataStream<Tuple3<String, String, String>> getSmall3TupleDataSet(StreamExecutionEnvironment env) {
        List<Tuple3<String, String, String>> data = new ArrayList();
        data.add(new Tuple3<>("hi", "hi", "Hi"));
        data.add(new Tuple3<>("hello", "hello", "Hello"));
        data.add(new Tuple3<>("hello world!", "hello world!", "Hello world!"));
        return env.fromCollection(data);
    }

    public static class TestConverter extends ODPSRecordConverter  implements RowConverter<Row, Record> {
    	int i = 0;
    	int j = 0;

        @Override
        public void open(RuntimeContext context) {
        	i = context.getIndexOfThisSubtask();
        	j = context.getNumberOfParallelSubtasks();
            System.out.println("TestConverter open~:" + i + "/" + j);
            super.open(context);
        }

        @Override
        public Record convert(Row row, Record reuse) {
            System.out.println("TestConverter convert:"+ i + "/" + j + "-" + row);
            return super.convert(row, reuse);
        }

        @Override
        public void close() {
            System.out.println("TestConverter close:"+ i + "/" + j);
            super.close();
        }
    }
}
