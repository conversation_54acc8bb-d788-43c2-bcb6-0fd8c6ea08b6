/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.odps.source;

import com.alibaba.blink.connectors.odps.util.AccessKeyUtil;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.TableEnvironment;
import org.apache.flink.table.api.TableException;
import org.apache.flink.table.api.java.BatchTableEnvironment;
import org.apache.flink.table.sources.TableSource;
import org.apache.flink.table.types.DataTypes;
import org.apache.flink.table.types.InternalType;
import org.apache.flink.types.Row;

import com.alibaba.blink.connectors.odps.schema.ODPSTableSchema;
import com.alibaba.blink.table.cache.CacheConfig;
import com.alibaba.blink.table.cache.CacheStrategy;
import com.aliyun.odps.OdpsType;
import com.aliyun.wormhole.qanat.odps.OdpsTableSink;
import com.aliyun.wormhole.qanat.odps.conf.ODPSConf;
import com.aliyun.wormhole.qanat.odps.util.ODPSUtil;
import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.junit.Assert.*;

public abstract class BaseODPSTableSourceITCase {
	protected StreamExecutionEnvironment env;
	protected TableEnvironment tEnv;
	protected BatchTableEnvironment batchTableEnv;

	protected static String accessKey = AccessKeyUtil.getAccessKey();
	protected static String accessId = AccessKeyUtil.getAccessId();
	protected static String endpoint = AccessKeyUtil.getEndpoint();
	protected static String project = AccessKeyUtil.getProject();

	protected String unpartitionedTableName;
	protected String partitionedTableName;
	private static ODPSConf conf = new ODPSConf(accessId, accessKey, endpoint, project, null);
	protected CacheConfig cacheConfig = new CacheConfig(CacheStrategy.all(1 * 1000), new ArrayList<>(), 100);

	public abstract TableEnvironment createTableEnvironment();

	public abstract void registerStream(String tableName, DataStream<Row> stream, String fields);

	public abstract DataStream<Row> table2DataStream(Table t);

	public abstract TableSource createOdpsTableSource(
			String tableName,
			String odpsTableName,
			RichTableSchema richTableSchema,
			List<String> userSpecificPartitions,
			CacheConfig cacheConfig);

	@Before
	public void prepareODPSTable() {
		env = StreamExecutionEnvironment.getExecutionEnvironment();
		batchTableEnv = TableEnvironment.getBatchTableEnvironment(env);
		tEnv = createTableEnvironment();
		unpartitionedTableName = createUniqueOdpsTableName();
		partitionedTableName = createUniqueOdpsTableName();
		createUnPartitionedTable();
		createPartitionedTable();
		List<Row> data = new ArrayList<>();
		data.add(Row.of("1", "Hi"));
		data.add(Row.of("2", "Hello"));
		data.add(Row.of("5", "Hello word"));
		data.add(Row.of("6", "Narotu"));
		data.add(Row.of("7", "N/A"));

		DataStream<Row> input = env.fromCollection(data).returns(new RowTypeInfo(
				new TypeInformation[]{
						org.apache.flink.api.common.typeinfo.Types.STRING, org.apache.flink.api.common.typeinfo.Types.STRING},
				new String[]{"name", "ip"}
		));

		registerStream("MyTable", input, "name, ip");
		StringSink.result.clear();
	}

	@Test(expected = TableException.class)
	public void testUnknownColumn() {
		RichTableSchema schema = new RichTableSchema(
				new String[]{"error_field", "project_name"},
				new InternalType[]{DataTypes.STRING, DataTypes.STRING});
		schema.setPrimaryKey("project_name");
		createOdpsTableSource("t", unpartitionedTableName, schema, new ArrayList<>(), cacheConfig);
	}

	// Ignore because it occasionally fails with timeout
	@Ignore
	@Test
	public void testPartitionedTableAsScanTable() throws Exception {
		RichTableSchema schema = new RichTableSchema(
				new String[]{"cookie", "ip", "filter"},
				new InternalType[]{DataTypes.STRING, DataTypes.STRING, DataTypes.STRING});
		String sourceName = "t";
		TableSource source = createOdpsTableSource(sourceName, partitionedTableName, schema, new ArrayList<>(), cacheConfig);
		tEnv.registerTableSource(sourceName, source);
		Table result = tEnv.sqlQuery("SELECT ip, `filter` FROM " + sourceName + " where `filter`='qwe'");
		DataStream<Row> res = table2DataStream(result);
		res.addSink(new StringSink());
		env.execute();

		List<String> expectedResult = new ArrayList<>();
		expectedResult.add("Hi,qwe");
		expectedResult.add("Hello,qwe");
		expectedResult.add("Hello word,qwe");
		Collections.sort(expectedResult);
		Collections.sort(StringSink.result);

		assertEquals(expectedResult, StringSink.result);
	}

	// Ignore because it occasionally fails with timeout
	@Ignore
	@Test
	public void testPartitionedTableAsScanTable2() throws Exception {
		RichTableSchema schema = new RichTableSchema(
				new String[]{"cookie", "ip", "filter"},
				new InternalType[]{DataTypes.STRING, DataTypes.STRING, DataTypes.STRING});
		List<String> userSpecificPartitions = new ArrayList<>();
		userSpecificPartitions.add("filter=max_pt()");
		String sourceName = "t";
		TableSource source = createOdpsTableSource(sourceName, partitionedTableName, schema, userSpecificPartitions, cacheConfig);
		tEnv.registerTableSource(sourceName, source);
		Table result = tEnv.sqlQuery("SELECT ip, `filter` FROM " + sourceName);
		DataStream<Row> res = table2DataStream(result);
		res.addSink(new StringSink());
		env.execute();

		List<String> expectedResult = new ArrayList<>();
		expectedResult.add("Hi,qwf");
		expectedResult.add("Hello,qwf");
		Collections.sort(expectedResult);
		Collections.sort(StringSink.result);

		assertEquals(expectedResult, StringSink.result);
	}

	@Ignore
	@Test
	public void testLeftJoinPartitionedTableAsDimTable() throws Exception {
		RichTableSchema schema = new RichTableSchema(
				new String[]{"name", "ip"},
				new InternalType[]{DataTypes.STRING, DataTypes.STRING});
		schema.setPrimaryKey("name");
		List<String> indexKeys = new ArrayList<>();
		indexKeys.add("ip");
		RichTableSchema.Index index = new RichTableSchema.Index(true, indexKeys);
		List<RichTableSchema.Index> indice = new ArrayList<>();
		indice.add(index);
		schema.setIndexes(indice);

		List<String> userSpecificPartitions = new ArrayList<>();
		userSpecificPartitions.add("filter=max_pt()");
		String sourceName = "dimT";
		TableSource partitionDim = createOdpsTableSource(sourceName, partitionedTableName, schema, userSpecificPartitions, cacheConfig);
		tEnv.registerTableSource(sourceName, partitionDim);
		Table result = tEnv.sqlQuery("SELECT T.name, T.ip, H.ip FROM MyTable AS T LEFT JOIN " +
									 sourceName + " FOR SYSTEM_TIME AS OF PROCTIME() " +
									 "AS H ON T.name = H.name and T.ip = H.ip");

		DataStream<Row> res = table2DataStream(result);
		res.addSink(new StringSink());

		env.execute();

		List<String> expectedResult = new ArrayList<>();
		expectedResult.add("1,Hi,Hi");
		expectedResult.add("2,Hello,Hello");
		expectedResult.add("5,Hello word,null");
		expectedResult.add("6,Narotu,null");
		expectedResult.add("7,N/A,null");

		Collections.sort(expectedResult);
		Collections.sort(StringSink.result);

		assertEquals(expectedResult, StringSink.result);
	}

	@Ignore
	@Test
	public void testJoinPartitionedTableAsDimTable() throws Exception {
		RichTableSchema schema = new RichTableSchema(
				new String[]{"name", "ip"},
				new InternalType[]{DataTypes.STRING, DataTypes.STRING});
		schema.setPrimaryKey("name");
		List<String> indexKeys = new ArrayList<>();
		indexKeys.add("ip");
		RichTableSchema.Index index = new RichTableSchema.Index(true, indexKeys);
		List<RichTableSchema.Index> indice = new ArrayList<>();
		indice.add(index);
		schema.setIndexes(indice);

		List<String> userSpecificPartitions = new ArrayList<>();
		userSpecificPartitions.add("filter=qwe");
		String sourceName = "dimT";
		TableSource partitionDim = createOdpsTableSource(sourceName, partitionedTableName, schema, userSpecificPartitions, cacheConfig);
		tEnv.registerTableSource(sourceName, partitionDim);
		Table result = tEnv.sqlQuery("SELECT T.name, T.ip, H.ip FROM MyTable AS T JOIN " +
									 sourceName + " FOR SYSTEM_TIME AS OF PROCTIME() " +
									 "AS H ON T.name = H.name and T.ip = H.ip");

		DataStream<Row> res = table2DataStream(result);
		res.addSink(new StringSink());

		env.execute();

		List<String> expectedResult = new ArrayList<>();
		expectedResult.add("1,Hi,Hi");
		expectedResult.add("2,Hello,Hello");
		expectedResult.add("5,Hello word,Hello word");

		Collections.sort(expectedResult);
		Collections.sort(StringSink.result);

		assertEquals(expectedResult, StringSink.result);
	}

	@Ignore
	@Test
	public void testJoinUnPartitionedTableAsDimTable() throws Exception {
		RichTableSchema schema = new RichTableSchema(
				new String[]{"owner_kp", "project_name"},
				new InternalType[]{DataTypes.STRING, DataTypes.STRING});
		schema.setPrimaryKey("project_name");

		String sourceName = "dimT";
		TableSource unpartitionedDim = createOdpsTableSource(sourceName, unpartitionedTableName, schema, new ArrayList<>(), cacheConfig);
		tEnv.registerTableSource(sourceName, unpartitionedDim);
		Table result = tEnv.sqlQuery("SELECT T.name, T.ip, H.owner_kp FROM MyTable AS T JOIN " +
									 sourceName + " FOR SYSTEM_TIME AS OF PROCTIME() AS H ON T.ip = H.project_name");

		DataStream<Row> res = table2DataStream(result);
		res.addSink(new StringSink());

		env.execute();

		List<String> expectedResult = new ArrayList<>();
		expectedResult.add("1,Hi,1");
		expectedResult.add("2,Hello,2");
		expectedResult.add("5,Hello word,5");

		Collections.sort(expectedResult);
		Collections.sort(StringSink.result);

		assertEquals(expectedResult, StringSink.result);
	}

	private void createUnPartitionedTable() {
		List<com.aliyun.odps.Column> normalColumns = new ArrayList<>();
		com.aliyun.odps.Column colOwnerKP = new com.aliyun.odps.Column("owner_kp", OdpsType.STRING);
		normalColumns.add(colOwnerKP);
		com.aliyun.odps.Column colProjectName = new com.aliyun.odps.Column("project_name", OdpsType.STRING);
		normalColumns.add(colProjectName);
		ODPSTableSchema schema = new ODPSTableSchema(normalColumns, null, false);
		ODPSUtil.createTable(conf, project, unpartitionedTableName, schema, "Blink ODPS Test", true);
		// insert data
		List<Row> data = new ArrayList<>();
		data.add(Row.of("1", "Hi"));
		data.add(Row.of("2", "Hello"));
		data.add(Row.of("5", "Hello word"));
		RowTypeInfo type = new RowTypeInfo(BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO);
		batchTableEnv.registerCollection("source1", data, type, "a,b");
		RichTableSchema richTableSchema = new RichTableSchema(
				new String[] {"owner_kp", "project_name"},
				new InternalType[]{DataTypes.STRING, DataTypes.STRING});
		OdpsTableSink sink = new OdpsTableSink(conf, unpartitionedTableName, richTableSchema, null, 100);
		batchTableEnv.registerTableSink("output1", sink.getFieldNames(), sink.getFieldTypes(), sink);
		batchTableEnv.sqlQuery("select a,b from source1").writeToSink(sink);
		batchTableEnv.execute();
	}

	private void createPartitionedTable() {
		List<com.aliyun.odps.Column> normalColumns = new ArrayList<>();
		com.aliyun.odps.Column colCookie = new com.aliyun.odps.Column("cookie", OdpsType.STRING);
		normalColumns.add(colCookie);
		com.aliyun.odps.Column colName = new com.aliyun.odps.Column("name", OdpsType.STRING);
		normalColumns.add(colName);
		com.aliyun.odps.Column colIp = new com.aliyun.odps.Column("ip", OdpsType.STRING);
		normalColumns.add(colIp);
		List<com.aliyun.odps.Column> partitionColumns = new ArrayList<>();
		com.aliyun.odps.Column colFilter = new com.aliyun.odps.Column("filter", OdpsType.STRING);
		partitionColumns.add(colFilter);
		// create a partition table
		ODPSTableSchema schema = new ODPSTableSchema(normalColumns, partitionColumns, false);
		ODPSUtil.createTable(conf, project, partitionedTableName, schema, "Blink ODPS Test", true);
		// insert data into partition filter=qwe
		List<Row> data = new ArrayList<>();
		data.add(Row.of("1", "1", "Hi"));
		data.add(Row.of("4", "2", "Hello"));
		data.add(Row.of("3", "5", "Hello word"));
		RowTypeInfo type = new RowTypeInfo(BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO);
		batchTableEnv.registerCollection("source", data, type, "a,b,c");
		RichTableSchema richTableSchema = new RichTableSchema(
				new String[] {"cookie", "name", "ip"},
				new InternalType[]{DataTypes.STRING, DataTypes.STRING, DataTypes.STRING});
		OdpsTableSink sink = new OdpsTableSink(conf, partitionedTableName, richTableSchema, "filter=qwe", 100);
		batchTableEnv.registerTableSink("output", sink.getFieldNames(), sink.getFieldTypes(), sink);
		batchTableEnv.sqlQuery("select a,b,c from source").writeToSink(sink);
		batchTableEnv.execute();
		// insert data into partition filter=qwf
		List<Row> data2 = new ArrayList<>();
		data2.add(Row.of("1", "1", "Hi"));
		data2.add(Row.of("4", "2", "Hello"));
		batchTableEnv.registerCollection("source2", data2, type, "a,b,c");
		OdpsTableSink sink2 = new OdpsTableSink(conf, partitionedTableName, richTableSchema, "filter=qwf", 100);
		batchTableEnv.registerTableSink("output2", sink2.getFieldNames(), sink2.getFieldTypes(), sink2);
		batchTableEnv.sqlQuery("select a,b,c from source2").writeToSink(sink2);
		batchTableEnv.execute();
	}

	@After
	public void clean() {
		ODPSUtil.deleteTable(conf, project, unpartitionedTableName, true);
		ODPSUtil.deleteTable(conf, project, partitionedTableName, true);
	}

	private String createUniqueOdpsTableName() {
		return "tmp_" + UUID.randomUUID().toString().replace("-", "");
	}

	public static class StringSink extends RichSinkFunction<Row> {

		static final List<String> result = new ArrayList<>();
		private static final long serialVersionUID = -5846017721299903963L;

		@Override
		public void invoke(Row row) throws Exception {
			synchronized (result) {
				result.add(row.toString());
			}

		}
	}
}
