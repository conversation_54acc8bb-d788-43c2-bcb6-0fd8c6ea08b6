/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.odps.source;

import com.aliyun.wormhole.qanat.odps.conf.ODPSConf;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.core.io.InputSplit;
import org.apache.flink.util.TestLogger;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class ODPSContinuousMonitoringSourceTest extends TestLogger {
	@Mock
	private RuntimeContext runtimeContext;

	@InjectMocks
	private ODPSContinuousMonitoringSource odpsSource;
	/*
	@Test
	public void createInputSplits() throws Exception {
		odpsSource = new ODPSContinuousMonitoringSource(new ODPSConf("AccessID", "AccessKey", "Endpoint", "Project"), "TableName",null,false);

		MockitoAnnotations.initMocks(this);
		when(runtimeContext.getMaxNumberOfParallelSubtasks()).thenReturn(16);

		InputSplit[] inputSplits = odpsSource.createInputSplitsForCurrentSubTask(8, 0);
		assertEquals(2, inputSplits.length);

		assertEquals(0, inputSplits[0].getSplitNumber());
		assertEquals(8, inputSplits[1].getSplitNumber());
	}
	*/
}
