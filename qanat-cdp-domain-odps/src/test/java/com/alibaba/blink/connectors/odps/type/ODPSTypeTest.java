package com.alibaba.blink.connectors.odps.type;

import org.apache.flink.table.dataformat.Decimal;
import org.apache.flink.table.dataformat.GenericRow;
import org.apache.flink.table.types.DecimalType;

import com.aliyun.odps.data.ArrayRecord;
import com.aliyun.odps.data.Record;
import org.junit.Test;

import java.math.BigDecimal;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.powermock.api.mockito.PowerMockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;

public class ODPSTypeTest {

	@Test
	public void testNullDecimal() throws Exception {
		GenericRow row = new GenericRow(1);
		Record record = mock(Record.class);
		when(record, "getDecimal", "d").thenReturn(null);
		ODPSType.DECIMAL.setRowField(row, 0, record, "d");
		assertTrue(row.isNullAt(0));
	}

	@Test
	public void testTooBigDecimal() throws Exception {
		GenericRow row = new GenericRow(1);
		Record record = mock(Record.class);
		when(record, "getDecimal", "d").thenReturn(new BigDecimal("1.23E50"));
		ODPSType.DECIMAL.setRowField(row, 0, record, "d");
		assertTrue(row.isNullAt(0));
	}

	@Test
	public void testDecimal() throws Exception {
		GenericRow row = new GenericRow(1);
		Record record = mock(Record.class);
		when(record, "getDecimal", "d").thenReturn(new BigDecimal("1.23E10"));
		ODPSType.DECIMAL.setRowField(row, 0, record, "d");
		assertEquals(row.getDecimal(0, Decimal.MAX_PS, 18), Decimal.castFrom("1.23E10",
				DecimalType.MAX_PRECISION, DecimalType.MAX_SCALE));
	}

	@Test
	public void testNullTimestamp() throws Exception {
		GenericRow row = new GenericRow(1);
		Record record = mock(ArrayRecord.class);
		when(record, "getTimestamp", "d").thenReturn(null);
		ODPSType.TIMESTAMP.setRowField(row, 0, record, "d");
		assertTrue(row.isNullAt(0));
	}

	@Test
	public void testNullDateTime() throws Exception {
		GenericRow row = new GenericRow(1);
		Record record = mock(ArrayRecord.class);
		when(record, "getDatetime", "d").thenReturn(null);
		ODPSType.DATETIME.setRowField(row, 0, record, "d");
		assertTrue(row.isNullAt(0));
	}

	@Test
	public void testNullBinary() throws Exception {
		GenericRow row = new GenericRow(1);
		Record record = mock(ArrayRecord.class);
		when(record, "getBytes", "d").thenReturn(null);
		ODPSType.BINARY.setRowField(row, 0, record, "d");
		assertTrue(row.isNullAt(0));
	}
}
