/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.odps.util;

public class AccessKeyUtil {

    private static String endpoint = "http://service-corp.odps.aliyun-inc.com/api";

    // We use byte arrays here to protect Code Search Engines from finding us!!!
    private static byte[] akBytes = {
            73, 51, 113, 90, 121, 81, 90, 118, 78, 103,
            74, 100, 113, 77, 98, 121, 66, 83, 104,
            72, 110, 70, 102, 80, 110, 72, 104, 48, 120,
            78};
    private static byte[] aidBytes = {
            76, 84, 65, 73, 48, 99, 103, 52, 107, 48,
            104, 97, 69, 51, 105, 97};
    private static byte[] projectBytes = {
            97, 117, 116, 111, 116, 101, 115, 116, 95, 100,
            101, 118};

    public static String getAccessKey() {
        return new String(akBytes);
    }

    public static String getAccessId() {
        return new String(aidBytes);
    }

    public static String getProject() {
        return new String(projectBytes);
    }

    public static String getEndpoint() {
        return endpoint;
    }
}
