/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.blink.connectors.odps

import com.alibaba.blink.connectors.odps.conf.ODPSConf
import com.alibaba.blink.connectors.odps.schema.{ODPSColumn, ODPSTableSchema}
import com.alibaba.blink.connectors.odps.util.{AccessKeyUtil, ODPSUtil, OdpsMetadataProvider}
import com.aliyun.odps.{Column, OdpsType, PartitionSpec}
import org.apache.flink.table.api.{RichTableSchema, TableConfig, TableEnvironment}
import org.apache.flink.api.common.typeinfo.BasicTypeInfo.{INT_TYPE_INFO, STRING_TYPE_INFO}
import org.apache.flink.api.java.typeutils.RowTypeInfo
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment
import org.apache.flink.table.api.java.{BatchTableEnvironment, StreamTableEnvironment}
import org.apache.flink.table.types.DataTypes
import org.apache.flink.table.runtime.batch.sql.BatchTestBase.row
import org.apache.flink.types.Row
import org.junit.{Assert, Before, Ignore, Test}
import java.util.ArrayList

import com.alibaba.blink.connectors.odps.source.ODPSRecordReader
import com.alibaba.blink.connectors.odps.split.{ODPSInputSplit, ODPSPartitionSegmentDownloadDesc}

import scala.collection.JavaConversions._

class OdpsTableSinkTest {

  private val accessKey = AccessKeyUtil.getAccessKey
  private val accessId = AccessKeyUtil.getAccessId
  private val project = AccessKeyUtil.getProject
  private val endpoint = AccessKeyUtil.getEndpoint
  private val conf = new ODPSConf(accessId, accessKey, endpoint, project, null)

  def getBatchExecData(tEnv: BatchTableEnvironment): Unit = {
    val smallData3 = Seq(
      row(1, 1, "Hiii"),
      row(4, 2, "Hello"),
      row(3, 5, "Hello world"),
      row(30, 20, "Hello world"),
      row(30, 20, "my world")
    )
    val type3 = new RowTypeInfo(INT_TYPE_INFO, INT_TYPE_INFO, STRING_TYPE_INFO)
    tEnv.registerCollection("sourceTable", smallData3, type3, "a,b,c")
  }

  def getBatchExecDataForDynamicPartition(tEnv: BatchTableEnvironment): Unit = {
    val smallData4 = Seq(
      row(1, 1, "Hi", "qwe"),
      row(4, 2, "Hello", "qwe"),
      row(3, 5, "Hello world", "qwe"),
      row(30, 20, "Hello world", "qwf"),
      row(30, 21, "my world", "qwf")
    )
    val type4 = new RowTypeInfo(INT_TYPE_INFO, INT_TYPE_INFO, STRING_TYPE_INFO, STRING_TYPE_INFO)
    tEnv.registerCollection("sourceTableDyn", smallData4, type4, "a,b,c,filter")
  }

  def getStreamDataForDynamicPartitioin(
        tEnv: StreamTableEnvironment,
        env: StreamExecutionEnvironment): Unit = {
    val data: java.util.List[Row] = new ArrayList[Row]
    data.add(Row.of(new Integer(1), new Integer(1), "Hi", "qwe"))
    data.add(Row.of(new Integer(4), new Integer(2), "Hello", "qwe"))
    data.add(Row.of(new Integer(3), new Integer(5), "Hello world", "qwe"))
    data.add(Row.of(new Integer(30), new Integer(20), "Hello world", "qwf"))
    data.add(Row.of(new Integer(30), new Integer(21), "my world", "qwf"))
    val type4 = new RowTypeInfo(INT_TYPE_INFO, INT_TYPE_INFO, STRING_TYPE_INFO, STRING_TYPE_INFO)
    val stream = env.fromCollection(data).returns(type4)
    tEnv.registerDataStream("sourceTableDyn", stream, "a,b,c,filter")
  }

  def getBatchTableEnvironment(): BatchTableEnvironment = {
    val tableConf = new TableConfig
    val env: StreamExecutionEnvironment = StreamExecutionEnvironment.getExecutionEnvironment
    TableEnvironment.getBatchTableEnvironment(env, tableConf)
  }

  def getStreamTableEnvironment(): StreamTableEnvironment = {
    val tableConf = new TableConfig
    val env: StreamExecutionEnvironment = StreamExecutionEnvironment.getExecutionEnvironment
    TableEnvironment.getTableEnvironment(env, tableConf)
  }

  @Before
  def prepareODPSTable(): Unit = {
    val normalColumns = new ArrayList[Column]()
    val colOwnerKP = new Column("owner_kp", OdpsType.STRING)
    normalColumns.add(colOwnerKP)
    val colProjectName = new Column("project_name", OdpsType.STRING)
    normalColumns.add(colProjectName)
    val schema = new ODPSTableSchema(normalColumns, null, false)
    ODPSUtil.createTable(conf, project, "tmp",  schema, "Blink ODPS Test", true)
  }

  @Test
  def testUnpartitionTable(): Unit = {
    val tEnv: BatchTableEnvironment = getBatchTableEnvironment()
    getBatchExecData(tEnv)

    val flinkSchema = new RichTableSchema(
      Array[String]("owner_kp", "project_name"),
      Array(DataTypes.STRING, DataTypes.STRING))

    val sink = new OdpsTableSink(conf, "tmp", flinkSchema, null)
    tEnv.registerTableSink(
      "dest",
      sink.getFieldNames,
      sink.getFieldTypes,
      sink)

    val res = tEnv.sqlQuery("select c, c from sourceTable")
    res.writeToSink(sink)
    tEnv.execute("testUnpartitionTableOdps")
  }

  @Test
  def testPartitionTable(): Unit = {
    createEmptyTable(conf, "xhbtest")
    val tEnv: BatchTableEnvironment = getBatchTableEnvironment()
    getBatchExecData(tEnv)

    val flinkSchema = new RichTableSchema(
      Array("cookie", "name", "ip"),
      Array(
        DataTypes.STRING,
        DataTypes.STRING,
        DataTypes.STRING))

    val simpleSink = new OdpsTableSink(conf, "xhbtest", flinkSchema, "filter=qwe")

    tEnv.registerTableSink(
      "destOdps",
      simpleSink.getFieldNames,
      simpleSink.getFieldTypes,
      simpleSink)

    val res = tEnv.sqlQuery("select c, c, c from sourceTable")
    res.writeToSink(simpleSink)
    tEnv.execute("testPartitionTableOdps")
  }

  /**
    * First, Create table if not exists
    * @param odpsConf
    */
  def createEmptyTable(odpsConf: ODPSConf, tableName: String): Unit = {
    ODPSUtil.deleteTable(odpsConf, project, tableName, true)
    val normalColumns = new ArrayList[Column]()
    val cookie = new Column("cookie", OdpsType.STRING)
    normalColumns.add(cookie)
    val name = new Column("name", OdpsType.STRING)
    normalColumns.add(name)
    val ip = new Column("ip", OdpsType.STRING)
    normalColumns.add(ip)
    val partColumns = new ArrayList[Column]()
    val filter = new Column("filter", OdpsType.STRING)
    partColumns.add(filter)
    val schema = new ODPSTableSchema(normalColumns, partColumns, false)
    ODPSUtil.createTable(odpsConf, project, tableName,  schema, "Blink ODPS Test", true)
  }

  /**
    * Second, Generate by dynamic partitions
    * @param tEnv
    */
  def testAndVerify(tEnv: TableEnvironment, tableName: String): Unit = {
    val flinkSchema = new RichTableSchema(
      Array("cookie", "name", "ip", "filter"),
      Array(
        DataTypes.STRING,
        DataTypes.STRING,
        DataTypes.STRING,
        DataTypes.STRING))

    val simpleSink = new OdpsTableSink(conf, tableName, flinkSchema, "filter")

    tEnv.registerTableSink(
      "destOdps",
      simpleSink.getFieldNames,
      simpleSink.getFieldTypes,
      simpleSink)

    val res = tEnv.sqlQuery("select c, c, c,`filter` from sourceTableDyn")
    res.writeToSink(simpleSink)
    tEnv.execute("testPartitionDynTableOdps")

    // Verify two partitions
    val newParts = ODPSUtil.getAllPartitions(conf, tableName)
    // The two partitions are newly generated
    Assert.assertEquals(2, newParts.size)

    val tunnel = ODPSUtil.createTableTunnel(conf)
    var partSpec = new PartitionSpec("filter='qwe'")
    var downloadSession =
      OdpsMetadataProvider.createDownloadSession(tunnel, conf.getProject(), tableName, partSpec)
    var sessionId = downloadSession.getId
    val columns = new Array[ODPSColumn](4)
    columns(0) = new ODPSColumn("cookie", OdpsType.STRING)
    columns(1) = new ODPSColumn("name", OdpsType.STRING)
    columns(2) = new ODPSColumn("ip", OdpsType.STRING)
    columns(3) = new ODPSColumn("filter", OdpsType.STRING, true)
    val recordReader = new ODPSRecordReader(
      tunnel,
      conf,
      tableName,
      columns)
    var split = new ODPSInputSplit(
      1,
      new ODPSPartitionSegmentDownloadDesc(
        partSpec.toString, 0, 3, sessionId))
    recordReader.open(split, null)

    val part1 = scala.collection.mutable.Set[String]()
    // Row Number 1
    Assert.assertTrue(recordReader.next())
    part1.add(recordReader.getMessage.getString(0))
    part1.add(recordReader.getMessage.getString(1))
    part1.add(recordReader.getMessage.getString(2))
    // Row Number 2
    Assert.assertTrue(recordReader.next())
    part1.add(recordReader.getMessage.getString(0))
    part1.add(recordReader.getMessage.getString(1))
    part1.add(recordReader.getMessage.getString(2))
    // Row Number 3
    Assert.assertTrue(recordReader.next())
    part1.add(recordReader.getMessage.getString(0))
    part1.add(recordReader.getMessage.getString(1))
    part1.add(recordReader.getMessage.getString(2))
    // Make sure there is only three rows
    Assert.assertFalse(recordReader.next())
    recordReader.close()
    Assert.assertEquals(3, part1.size)
    Assert.assertTrue(part1.contains("Hi"))
    Assert.assertTrue(part1.contains("Hello"))
    Assert.assertTrue(part1.contains("Hello world"))

    // Test another partition
    partSpec = new PartitionSpec("filter='qwf'")
    downloadSession =
      OdpsMetadataProvider.createDownloadSession(tunnel, conf.getProject(), tableName, partSpec)
    sessionId = downloadSession.getId
    split = new ODPSInputSplit(
      2,
      new ODPSPartitionSegmentDownloadDesc(
        partSpec.toString, 0, 2, sessionId))
    recordReader.open(split, null)

    val part2 = scala.collection.mutable.Set[String]()
    // Row Number 1
    Assert.assertTrue(recordReader.next())
    part2.add(recordReader.getMessage.getString(0))
    part2.add(recordReader.getMessage.getString(1))
    part2.add(recordReader.getMessage.getString(2))
    // Row Number 2
    Assert.assertTrue(recordReader.next())
    part2.add(recordReader.getMessage.getString(0))
    part2.add(recordReader.getMessage.getString(1))
    part2.add(recordReader.getMessage.getString(2))
    // Make sure there is only two rows
    Assert.assertFalse(recordReader.next())
    recordReader.close()
    Assert.assertEquals(2, part2.size)
    Assert.assertTrue(part2.contains("Hello world"))
    Assert.assertTrue(part2.contains("my world"))
  }

  @Ignore
  @Test
  def testDynamicPartitionTableForStream(): Unit = {
    // TODO This test case passes while testing offline, but fails with timeout when testing with
    // Aone. Please try to figure out the reason.
    // We use different table name to prevent a bug of Tunnel
    createEmptyTable(conf, "xhbtest1")
    val tableConf = new TableConfig
    val env: StreamExecutionEnvironment = StreamExecutionEnvironment.getExecutionEnvironment
    val tEnv = TableEnvironment.getTableEnvironment(env, tableConf)

    getStreamDataForDynamicPartitioin(tEnv, env)
    testAndVerify(tEnv, "xhbtest1")
  }

  @Test
  def testDynamicPartitionTableForBatch(): Unit = {
    // We use different table name to prevent a bug of Tunnel
    createEmptyTable(conf, "xhbtest2")
    val tEnv = getBatchTableEnvironment()
    getBatchExecDataForDynamicPartition(tEnv)
    testAndVerify(tEnv, "xhbtest2")
  }
}
