/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.odps

import org.apache.flink.api.java.tuple.{Tuple2 => JTuple2}
import org.apache.flink.streaming.api.datastream.{DataStream, DataStreamSink}
import org.apache.flink.table.api.RichTableSchema
import org.apache.flink.table.connector.DefinedDistribution
import org.apache.flink.table.sinks.{BatchCompatibleStreamTableSink, TableSinkBase, UpsertStreamTableSink}
import org.apache.flink.table.types.{DataType, DataTypes}
import org.apache.flink.types.Row

import com.aliyun.wormhole.qanat.odps.conf.ODPSConf
import com.aliyun.wormhole.qanat.odps.outputformat.ODPSStreamOutputFormat
import com.aliyun.wormhole.qanat.odps.outputformat.YamlUtil
import com.aliyun.wormhole.qanat.odps.util.ODPSUtil
import com.alibaba.blink.streaming.connectors.common.output.TupleOutputFormatAdapterSink
import com.alibaba.blink.streaming.connectors.common.source.SourceUtils
import com.alibaba.blink.streaming.connectors.common.util.RowConverter
import com.aliyun.odps.data.Record
import com.aliyun.odps.{PartitionSpec, Table}

import java.lang.{Boolean => JBool}

/**
  * A OdpsTableSink for odps
  *
  * @param odpsConf     odps configuration
  * @param table        odps table name
  * @param schema       odps table schema
  * @param partition    partition which to write,
  *                       table is partitioned, then partition cannot be null; else could be null
  * @param flushIntervalMs  flush data to odps interrval time in ms
  * @param batchCount    batch count
  * @param retryTimes    failed retryTimes
  * @param sleepTime     when failed, should sleep time
  * @param customConverter  user custom coverter
  */
class OdpsTableSink (
                      odpsConf: ODPSConf,
                      table: String,
                      schema: RichTableSchema = null,
                      partition: String = null,
                      dynamicPartitionLimit: Int = 100,
                      flushIntervalMs: Long = 30000,
                      var batchCount: Long = Long.MaxValue,
                      var retryTimes: Int = 6,
                      var sleepTime: Long = 4000,
                      var customConverter: RowConverter[Row, Record] = null)
  extends TableSinkBase[JTuple2[JBool, Row]]
  with UpsertStreamTableSink[Row]
  with DefinedDistribution
  with BatchCompatibleStreamTableSink[JTuple2[JBool, Row]] {

  def this(odpsConf: ODPSConf,
           table: String,
           schema: RichTableSchema,
           partition: String,
           dynamicPartitionLimit: Int,
           flushIntervalMs: Long){
    this(odpsConf, table, schema,partition, dynamicPartitionLimit, flushIntervalMs,
      Long.MaxValue, 6, 4000)
  }

  def this(odpsConf: ODPSConf,
           table: String,
           schema: RichTableSchema,
           partition: String,
           dynamicPartitionLimit: Int) {
    this(odpsConf, table, schema, partition, dynamicPartitionLimit, 30000, Long.MaxValue, 6, 4000)
  }

  private val odpsTable: Table = {
    val odps = ODPSUtil.initOdps(odpsConf)
    val project = odpsConf.getProject
    var dataModel = YamlUtil.getDataModel(odpsConf.getDataModelYaml())
    var metaRef = dataModel.getDomain().getMetaRef()
    var tableName = "ods_cdp_domain_" + metaRef.split("__")(1) + "_df"
    val t = odps.tables.get(project, tableName)
    require(!t.isVirtualView, "View is not supported yet! ")
    t
  }

  private val isPartitionedTable: Boolean = {
    val isPartitioned = odpsTable.isPartitioned
    if (isPartitioned) {
      require(partition != null, "partition cannot be null for partition table! ")
    }
    isPartitioned
  }

  createPartitionIfNeeded()

  override def emitDataStream(
    dataStream: DataStream[JTuple2[JBool, Row]]): DataStreamSink[JTuple2[JBool, Row]] = {
    val outputFormat: ODPSStreamOutputFormat = new ODPSStreamOutputFormat(
      odpsConf,
      table,
      SourceUtils.toRowTypeInfo(getRecordType),
      partition,
      isPartitionedTable,
      flushIntervalMs,
      dynamicPartitionLimit)
    outputFormat.setBatchCount(batchCount)
    outputFormat.setRetryTimes(retryTimes)
    outputFormat.setSleepTime(sleepTime)
    if (customConverter != null) {
      outputFormat.setCustomConverter(customConverter)
    }
    val sink = new TupleOutputFormatAdapterSink[Row](outputFormat)
    dataStream.addSink(sink).name(sink.toString)
  }

  override def emitBoundedStream(
      boundedStream: DataStream[JTuple2[JBool, Row]]): DataStreamSink[JTuple2[JBool, Row]] = {
    val outputFormat = new ODPSStreamOutputFormat(
      odpsConf,
      table,
      SourceUtils.toRowTypeInfo(getRecordType),
      partition,
      isPartitionedTable,
      flushIntervalMs,
      dynamicPartitionLimit)
    outputFormat.setBatchCount(batchCount)
    outputFormat.setRetryTimes(retryTimes)
    outputFormat.setSleepTime(sleepTime)

    if (customConverter != null) {
      outputFormat.setCustomConverter(customConverter)
    }

    boundedStream.writeUsingOutputFormat(outputFormat)
      .name(String.format("%s-%s", toString,
      com.alibaba.blink.streaming.connectors.common.Constants.BATCH_TAG))
  }

  override protected def copy: TableSinkBase[JTuple2[JBool, Row]] ={
    val sink = new OdpsTableSink(
      odpsConf,
      table,
      schema,
      partition,
      dynamicPartitionLimit,
      flushIntervalMs,
      batchCount,
      retryTimes,
      sleepTime,
      customConverter)
    sink.partitionedField = this.partitionedField
    sink._shuffleEmptyKey = this._shuffleEmptyKey
    sink
  }

  private var partitionedField: String = null

  private var _shuffleEmptyKey: Boolean = true

  def setPartitionedField(partitionedField: String): Unit = {
    this.partitionedField = partitionedField
  }

  def setShuffleEmptyKey(shuffleEmptyKey: Boolean): Unit = {
    this._shuffleEmptyKey = shuffleEmptyKey
  }

  override def getPartitionFields(): Array[String] = {
    if (this.partitionedField == null) {
      null
    } else {
      Array[String](partitionedField)
    }
  }

  override def shuffleEmptyKey() = _shuffleEmptyKey

  override def getRecordType: DataType = DataTypes.createRowTypeV2(getFieldTypes, getFieldNames)

  override def setKeyFields(keys: Array[String]): Unit = {}

  override def getFieldNames: Array[String] = {
    if (null == schema) {
      super.getFieldNames
    } else {
      schema.getColumnNames
    }
  }

  override def getFieldTypes: Array[DataType] = {
    if (null == schema) {
      super.getFieldTypes
    } else {
      schema.getColumnTypes.asInstanceOf[Array[DataType]]
    }
  }

  // create new partition if partition not exist yet
  private def createPartitionIfNeeded(): Unit = {
    if (isPartitionedTable) {
      if (!partition.contains("=")) {
        // Dynamic Partition
        return
      }
      val partitionSpec = new PartitionSpec(partition)
      odpsTable.createPartition(partitionSpec, true)
    }
  }

  override def setIsAppendOnly(isAppendOnly: JBool): Unit = {}

  def setBatchCount(customedBatchCount: Long): OdpsTableSink = {
    batchCount = customedBatchCount
    this
  }

  def setRetryTimes(customedRetryTimes: Int): OdpsTableSink = {
    retryTimes = customedRetryTimes
    this
  }

  def setRetryIntervalMs(customedRetryIntervalMs: Long): OdpsTableSink = {
    sleepTime = customedRetryIntervalMs
    this
  }

  def setCustomConverter(userConverter: RowConverter[Row, Record]): OdpsTableSink = {
    customConverter = userConverter
    this
  }

  override def toString: String = s"odpsTableSink-".concat(if(table==null) "" else table)
}
