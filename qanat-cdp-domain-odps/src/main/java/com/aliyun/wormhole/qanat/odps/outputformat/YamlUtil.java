package com.aliyun.wormhole.qanat.odps.outputformat;

//import org.yaml.snakeyaml.Yaml;

public class YamlUtil {

    public static DataModel getDataModel(String dataModelYaml) {
//        try {
//            Yaml yaml = new Yaml();
//            return yaml.loadAs(dataModelYaml, DataModel.class);
//        } catch(Exception e) {
//            e.printStackTrace();
//        }
        return null;
    }
}
