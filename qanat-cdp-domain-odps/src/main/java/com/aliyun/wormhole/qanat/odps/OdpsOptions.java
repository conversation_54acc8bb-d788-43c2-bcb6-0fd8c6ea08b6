/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.odps;

import org.apache.flink.configuration.ConfigOption;


import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.apache.flink.configuration.ConfigOptions.key;

/**
 * Deprecated from 3.x version Blink, please use
 * {@link com.alibaba.blink.table.connectors.conf.BlinkOptions} instead.
 */
public class OdpsOptions {

  /** ODPS options. **/
  public static class ODPS extends com.alibaba.blink.table.connectors.conf.BlinkOptions.DIM {

    public static final ConfigOption<String> DATA_MODEL_YAML = key("dataModelYaml".toLowerCase())
        .noDefaultValue();
  }
}
