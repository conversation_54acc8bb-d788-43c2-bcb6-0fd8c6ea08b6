/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.odps.util;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.table.api.TableException;
import org.apache.flink.table.typeutils.BaseRowTypeInfo;

import com.alibaba.blink.connectors.odps.schema.ODPSColumn;
import com.alibaba.blink.connectors.odps.schema.ODPSTableSchema;
import com.alibaba.blink.connectors.odps.type.ODPSType;
import com.aliyun.odps.Column;
import com.aliyun.odps.Odps;
import com.aliyun.odps.OdpsException;
import com.aliyun.odps.Partition;
import com.aliyun.odps.PartitionSpec;
import com.aliyun.odps.Table;
import com.aliyun.odps.TableSchema;
import com.aliyun.odps.account.Account;
import com.aliyun.odps.account.AliyunAccount;
import com.aliyun.odps.tunnel.TableTunnel;
import com.aliyun.wormhole.qanat.odps.conf.ODPSConf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * utility for ODPS
 */
public class ODPSUtil {
	private static final Logger LOGGER = LoggerFactory.getLogger(ODPSUtil.class);
	public static final String DONE_FLAG = ".done";

	/**
	 * Creates odps instance using odps configuration.
	 *
	 * @param odpsConf odps configuration
	 * @return odps instance
	 */
	public static Odps initOdps(ODPSConf odpsConf) {
		return initOdps(
				odpsConf.getAccessId(),
				odpsConf.getAccessKey(),
				odpsConf.getEndpoint(),
				odpsConf.getProject());
	}

	/**
	 * Creates odps instance.
	 *
	 * @param accessId access id
	 * @param accessKey access key
	 * @param endpoint endopint
	 * @param defaultProject default project
	 * @return new created odps instance
	 */
	private static Odps initOdps(String accessId, String accessKey, String endpoint,
			String defaultProject) {
		Account account = new AliyunAccount(accessId, accessKey);
		Odps odps = new Odps(account);
		odps.setEndpoint(endpoint);
		if (defaultProject != null) {
			odps.setDefaultProject(defaultProject);
		}
		return odps;
	}

	/**
	 * Fetches table schema of a specific odps table.
	 *
	 * @param odpsConf odps configuration
	 * @param table odps table name
	 * @return odps table schema
	 */
	public static ODPSTableSchema getODPSTableSchema(ODPSConf odpsConf, String table) {
		Odps odps = initOdps(odpsConf);
		try {
			Table t = OdpsMetadataProvider.getTable(odps, odpsConf.getProject(), table);
			TableSchema schema = t.getSchema();
			boolean isView = t.isVirtualView();
			return new ODPSTableSchema(schema.getColumns(), schema.getPartitionColumns(), isView);
		} catch (OdpsException e) {
			LOGGER.error("Fail to get table schema of table {} !", table, e);
			throw new TableException("Failed to get table schema !", e);
		}
	}

	/**
	 * Fetches size of an odps table.
	 *
	 * @param odpsConf odps configuration
	 * @param table odps table name
	 * @return total size of an odps table
	 */
	public static long getTableSize(ODPSConf odpsConf, String table) {
		Odps odps = initOdps(odpsConf);
		try {
			Table t = OdpsMetadataProvider.getTable(odps, odpsConf.getProject(), table);
			return t.getSize();
		} catch (OdpsException e) {
			LOGGER.error("Fail to get table size of table {} !", table, e);
			throw new TableException("Fail to get table size of table !", e);
		}
	}

	/**
	 * Fetches size of an odps table partition.
	 *
	 * @param odpsConf odps configuration
	 * @param table odps table name
	 * @param partition a specified odps table partition, e.g pt='1',ds='2'
	 * @return total size of an odps table partition
	 */
	public static long getPartitionSize(ODPSConf odpsConf, String table, String partition) {
		Odps odps = initOdps(odpsConf);
		try {
			PartitionSpec spec = new PartitionSpec(partition);
			Partition p = OdpsMetadataProvider.getPartition(odps, odpsConf.getProject(), table, spec);
			return p.getSize();
		} catch (OdpsException e) {
			LOGGER.error("Fail to get partition size of table {}, partition {} !", table, partition, e);
			throw new TableException("Fail to get partition size !", e);
		}
	}

	/**
	 * Fetches last modify time of an odps table.
	 *
	 * @param odpsConf odps configuration
	 * @param table odps table name
	 * @return last modify time of an odps table
	 */
	public static long getTableLastModifyTime(ODPSConf odpsConf, String table) {
		Odps odps = initOdps(odpsConf);
		try {
			Table t = OdpsMetadataProvider.getTable(odps, odpsConf.getProject(), table);
			return t.getLastDataModifiedTime().getTime();
		} catch (OdpsException e) {
			LOGGER.error("Fail to get last modify time of table {} !", table, e);
			throw new TableException("Fail to get last modify time of table !", e);
		}
	}

	/**
	 * Fetches last modify time of an odps table partition.
	 *
	 * @param odpsConf odps configuration
	 * @param table odps table name
	 * @param partition a specified odps table partition, e.g pt='1',ds='2'
	 * @return last modify time of an odps table partition
	 */
	public static long getPartitionLastModifyTime(ODPSConf odpsConf, String table, String partition) {
		Odps odps = initOdps(odpsConf);
		PartitionSpec spec = new PartitionSpec(partition);
		try {
			Partition p = OdpsMetadataProvider.getPartition(odps, odpsConf.getProject(), table, spec);
			return p.getLastDataModifiedTime().getTime();
		} catch (OdpsException e) {
			LOGGER.error("Fail to get partition last modify time of table {}, partition {} !", table, partition, e);
			throw new TableException("Fail to get partition last modify time of table !", e);
		}
	}

	/**
	 * Fetches all partitions of an odps table.
	 *
	 * @param odpsConf odps configuration
	 * @param table odps table name
	 * @return all partitions of an odps table
	 */
	public static List<Partition> getAllPartitions(ODPSConf odpsConf, String table) {
		Odps odps = initOdps(odpsConf);
		try {
			return OdpsMetadataProvider.getAllPartitions(odps, odpsConf.getProject(), table);
		} catch (OdpsException e) {
			LOGGER.error("Fail to get all partitions of table {} !", table, e);
			throw new TableException("Fail to get all partitions of table !", e);
		}
	}

	/**
	 * Creates odps table.
	 *
	 * @param odpsConf odps configuration
	 * @param project project name of target table
	 * @param table table name of target table
	 * @param schema schema of target table
	 * @param comment comment of target table
	 * @param ifNotExists if false and table exists: throw Exception
	 * if true:
	 * return success, and schema of table(if exists) should not be modified
	 * @throws RuntimeException
	 */
	public static void createTable(ODPSConf odpsConf, String project, String table,
			ODPSTableSchema schema, String comment, boolean ifNotExists) {
		Odps odps = initOdps(odpsConf);
		TableSchema theSchema = new TableSchema();
		List<ODPSColumn> columns = schema.getColumns();
		for (ODPSColumn column : columns) {
			Column theColumn = new Column(column.getName(), column.getType());
			if (column.isPartition()) {
				theSchema.addPartitionColumn(theColumn);
			} else {
				theSchema.addColumn(theColumn);
			}
		}

		try {
			odps.tables().create(project, table, theSchema, comment, ifNotExists);
		} catch (OdpsException e) {
			LOGGER.error("Fail to create table {} in project {} !", table, project, e);
			throw new TableException("Fail to create table !", e);
		}
	}

	/**
	 * Clears data of a non-partitioned table or a specified partition of a partitioned table.
	 *
	 * @param odpsConf odps configuration
	 * @param table table name of target table
	 * @param partition a specified odps table partition, e.g pt='1',ds='2'
	 */
	public static void clearData(ODPSConf odpsConf, String table, String partition) {
		Odps odps = ODPSUtil.initOdps(odpsConf);
		Table t = odps.tables().get(odpsConf.getProject(), table);
		try {
			boolean isPartitioned = t.isPartitioned();
			if (isPartitioned) {
				if (partition == null) {
					LOGGER.error("Partition cannot be null for a partitioned table {} !", table);
					throw new TableException("partition cannot be null for a partitioned table !");
				}
				PartitionSpec partitionSpec = new PartitionSpec(partition);
				t.deletePartition(partitionSpec, true);
			} else {
				t.truncate();
			}
		} catch (OdpsException e) {
			LOGGER.error("Fail to clear data of table {}, partition {} !", table, partition, e);
			throw new TableException("Fail to clear data of table !", e);
		}
	}

	/**
	 * Deletes odps table.
	 *
	 * @param odpsConf odps configuration
	 * @param project project name of target table
	 * @param table table name of target table
	 * @param ifExists if false and table not exists: throw Exception
	 * if true: return success
	 * @throws RuntimeException
	 */
	public static void deleteTable(ODPSConf odpsConf, String project, String table, boolean ifExists) {
		Odps odps = initOdps(odpsConf);
		try {
			odps.tables().delete(project, table, ifExists);
		} catch (OdpsException e) {
			LOGGER.error("Fail to delete table {} in project {} !", table, project, e);
			throw new TableException("Fail to delete table !", e);
		}
	}

	/**
	 * Converts a {@link PartitionSpec} to string.
	 *
	 * @param spec PartitionSpec to convert
	 * @return string converted from PartitionSpec. e.g. pt=2014,ds=03
	 */
	static String partitionSpecToString(PartitionSpec spec) {
		StringBuilder sb = new StringBuilder();
		boolean first = true;
		for (String key : spec.keys()) {
			if (first) {
				first = false;
			} else {
				sb.append(',');
			}
			sb.append(key).append("=").append(spec.get(key));
		}
		return sb.toString();
	}

	/**
	 * Checks whether a {@link Partition} is .done flag partition.
	 *
	 * @param partition Partition to check
	 * @return true if the partition is .done flag partition
	 */
	static boolean isDoneFlagPartition(Partition partition) {
		PartitionSpec spec = partition.getPartitionSpec();
		for (String key : spec.keys()) {
			if (spec.get(key).endsWith(DONE_FLAG)) {
				return true;
			}
		}
		return false;
	}

	public static BaseRowTypeInfo deriveRowType(ODPSColumn[] columns) {
		int columnsNum = columns.length;
		String[] fieldNames = new String[columnsNum];
		TypeInformation<?>[] fieldTypes = new TypeInformation<?>[columnsNum];
		for (int idx = 0; idx < columnsNum; idx++) {
			ODPSColumn column = columns[idx];
			fieldNames[idx] = column.getName();
			fieldTypes[idx] = ODPSType.valueOf(column.getType().name()).toFlinkType();
		}
		return new BaseRowTypeInfo(fieldTypes, fieldNames);
	}

	public static TableTunnel createTableTunnel(ODPSConf odpsConf) {
		Odps odps = initOdps(odpsConf);
		return createTableTunnel(odps, odpsConf);
	}

	public static TableTunnel createTableTunnel(Odps odps, ODPSConf odpsConf) {
		TableTunnel tunnel = new TableTunnel(odps);
		// set tunnel endpoint if user specify the value
		if (odpsConf.getTunnelEndpoint() != null) {
			tunnel.setEndpoint(odpsConf.getTunnelEndpoint());
		}
		return tunnel;
	}

	/**
	 * Deprecates default constructor
	 */
	private ODPSUtil() {

	}

}
