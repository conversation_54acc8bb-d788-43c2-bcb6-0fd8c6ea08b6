/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *	 http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.odps.outputformat;

import com.aliyun.odps.PartitionSpec;
import com.aliyun.odps.data.RecordWriter;
import com.aliyun.odps.tunnel.TableTunnel;
import com.aliyun.odps.tunnel.TunnelException;
import com.aliyun.odps.tunnel.io.TunnelBufferedWriter;
import com.aliyun.wormhole.qanat.odps.conf.ODPSConf;
import com.aliyun.wormhole.qanat.odps.util.ODPSUtil;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicInteger;

import static java.lang.Thread.sleep;

public class StaticPartitionODPSWriter extends ODPSWriter {

	private static final Logger LOGGER = LoggerFactory.getLogger(StaticPartitionODPSWriter.class);

	private final String table;
	private final String partition;
	private final boolean isPartitioned;

	private TableTunnel.UploadSession uploadSession;
	private RecordWriter recordWriter;
	private transient AtomicInteger currentCachedCount = new AtomicInteger(0);

	public StaticPartitionODPSWriter(
			long flushIntervalMs,
			ODPSConf odpsConf,
			String table,
			String partition,
			boolean isPartitioned) {
		super(flushIntervalMs, odpsConf);
		this.table = table;
		this.partition = partition;
		this.isPartitioned = isPartitioned;
	}

	private TableTunnel.UploadSession createSharedUploadSession() {
		try {
		    DataModel dataModel = YamlUtil.getDataModel(odpsConf.getDataModelYaml());
		    String metaRef = dataModel.getDomain().getMetaRef();
		    String tableName = "ods_cdp_domain_" + metaRef.split("__")[1] + "_df";
		    LOGGER.info("tableName={}", tableName);
			String project = odpsConf.getProject();
			if (!isPartitioned) {
				// Managed Table
				return tableTunnel.createUploadSession(project, tableName);
			} else {
				// Partition Table
				return tableTunnel.createUploadSession(project, tableName, new PartitionSpec(partition));
			}
		} catch (TunnelException e) {
			LOGGER.error("Fail to create uploadSession! ", e);
			throw new RuntimeException("Fail to create uploadSession! ", e);
		}
	}

	@Override
	public void open() throws IOException {
		if (null == uploadSession) {
			try {
				this.tableTunnel = ODPSUtil.createTableTunnel(odpsConf);
				uploadSession = createSharedUploadSession();
				LOGGER.info("Created upload session id: " + uploadSession.getId());
			} catch (Throwable e) {
				LOGGER.error("Fail to create odps upload session!", e);
				throw new RuntimeException(e);
			}
		}
		try {
			if (this.recordWriter == null) {
				this.recordWriter = createRecordWriter(uploadSession);
				currentCachedCount = new AtomicInteger(0);
				if (flushIntervalMs > 0) {
					scheduleFlusher();
				}
			}
		} catch (Throwable e) {
			LOGGER.warn("Fail to create odps writer! ", e);
			throw new IOException("Fail to create odps writer! ", e);
		}
	}

	@Override
	public TableTunnel.UploadSession getUploadSession(String staticPartKey) {
		return uploadSession;
	}

	@Override
	public RecordWriter getRecordWriter(String staticPartKey) {
		return recordWriter;
	}

	@Override
	public String getPartitionKey(Row row) {
		return partition;
	}

	@Override
	public synchronized void flush(boolean commit) throws IOException {
		if (this.recordWriter != null) {
			((TunnelBufferedWriter) recordWriter).getTotalBytes(); //trigger flush
			currentCachedCount.set(0);
			lastFlushTimeMs = System.currentTimeMillis();
		}
		if (commit) {
			try {
				LOGGER.info("commit to odps && sessionId:" + uploadSession.getId());
				uploadSession.commit();
				uploadSession = null;
				recordWriter = null;

				// rebuild session and recordWriter
				uploadSession = createSharedUploadSession();
				this.recordWriter = createRecordWriter(uploadSession);
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		}
	}

	@Override
	public void close() throws IOException {
		if (flusher != null) {
			flusher.shutdownNow();
			while (!flusher.isTerminated()) {
				try {
					sleep(10);
				} catch (Throwable t) {
					LOGGER.error("Exception Happened In Sleep Method", t);
					//ignore
				}
			}
			flusher = null;
		}

		synchronized (this) {
			if (this.recordWriter != null) {
				this.recordWriter.close();
			}
			if (null != uploadSession) {
				try {
					uploadSession.commit();
					uploadSession = null;
				} catch (TunnelException e) {
					LOGGER.error("Fail to commit odps upload session!", e);
					throw new RuntimeException(e);
				}
			}
		}
	}

	@Override
	public synchronized void checkAndFlush(String staticPartKey) throws IOException {
		currentCachedCount.incrementAndGet();

		long currentTime = System.currentTimeMillis();
		if (this.recordWriter instanceof TunnelBufferedWriter && currentTime - lastFlushTimeMs > flushIntervalMs
				|| currentCachedCount.get() > batchCount) {
			flush(false);
		}
	}

}
