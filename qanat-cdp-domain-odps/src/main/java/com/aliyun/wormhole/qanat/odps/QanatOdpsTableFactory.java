/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.aliyun.wormhole.qanat.odps;

import com.alibaba.blink.streaming.connectors.common.exception.InvalidParamException;
import com.alibaba.blink.streaming.connectors.common.exception.NotEnoughParamsException;
import com.alibaba.blink.streaming.connectors.common.exception.UnsupportedTableException;
import com.alibaba.blink.streaming.connectors.common.util.BlinkStringUtil;
import com.alibaba.blink.table.cache.CacheConfig;
import com.alibaba.blink.table.cache.CacheStrategy;
import com.alibaba.blink.table.connectors.conf.BlinkOptions;
import com.alibaba.blink.table.factories.BlinkTableFactory;

import com.aliyun.wormhole.qanat.odps.OdpsTableSink;
import com.aliyun.wormhole.qanat.odps.conf.ODPSConf;
import com.aliyun.wormhole.qanat.odps.util.ODPSUtil;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.dataformat.GenericRow;
import org.apache.flink.table.factories.BatchTableSourceFactory;
import org.apache.flink.table.factories.StreamTableSourceFactory;
import org.apache.flink.table.factories.StreamTableSinkFactory;
import org.apache.flink.table.factories.BatchCompatibleTableSinkFactory;
import org.apache.flink.table.sinks.BatchCompatibleStreamTableSink;
import org.apache.flink.table.sinks.StreamTableSink;
import org.apache.flink.table.sources.BatchTableSource;
import org.apache.flink.table.sources.StreamTableSource;
import org.apache.flink.table.sources.TableSource;
import org.apache.flink.table.util.TableProperties;
import org.apache.flink.types.Row;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_PROPERTY_VERSION;
import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_TYPE;

public class QanatOdpsTableFactory extends BlinkTableFactory implements
		StreamTableSinkFactory<Tuple2<Boolean, Row>>,
		BatchCompatibleTableSinkFactory<Tuple2<Boolean, Row>> {

	

	private OdpsTableSink createTableSink(Map<String, String> properties, boolean isStream) {
		TableProperties tableProperties = new TableProperties();
		tableProperties.putProperties(properties);
		RichTableSchema richTableSchema = tableProperties.readSchemaFromProperties(classLoader);

		String endPoint = tableProperties.getString(BlinkOptions.ODPS.END_POINT);
		String tunnelEndPoint = tableProperties.getString(BlinkOptions.ODPS.TUNNEL_END_POINT);
		String tableName = tableProperties.getString(BlinkOptions.ODPS.TABLE_NAME);
		String accessID = tableProperties.getString(BlinkOptions.ODPS.ACCESS_ID);
		String accessKey = tableProperties.getString(BlinkOptions.ODPS.ACCESS_KEY);
		String project = tableProperties.getString(BlinkOptions.ODPS.PROJECT_NAME);
		int dynamicPartitionLimit = tableProperties.getInteger(BlinkOptions.ODPS.DYNAMIC_PART_LIMIT);
        String dataModelYaml = tableProperties.getString(OdpsOptions.ODPS.DATA_MODEL_YAML);

		if (BlinkStringUtil.isEmpty(endPoint, accessID, accessKey, project)) {
			throw new NotEnoughParamsException(BlinkOptions.ODPS.PARAMS_WRITER_HELP_MSG);
		}
		String partition = tableProperties.getString(BlinkOptions.ODPS.PARTITION);
		boolean dynamicPart = partition != null && !partition.contains("=");
		ODPSConf odpsConf = new ODPSConf(accessID, accessKey, endPoint, project, tunnelEndPoint, dataModelYaml);
		boolean isOverwrite = tableProperties.getBoolean(BlinkOptions.ODPS.OPTIONAL_IS_OVERWRITE);
		if (isOverwrite && isStream) {
			throw new UnsupportedTableException("Only batch mode support overwrite.");
		}
		if (isOverwrite && dynamicPart) {
			throw new UnsupportedTableException("Dynamic partition mode does not support overwrite.");
		}
		if (isOverwrite) {
			// clear data in batch mode
			ODPSUtil.clearData(odpsConf, tableName, partition);
		}
		String partitionBy = tableProperties.getString(BlinkOptions.PARTITION_BY);
		boolean shuffleEmptyKey = tableProperties.getBoolean(BlinkOptions.SHUFFLE_EMPTY_KEY);
		OdpsTableSink sink = new OdpsTableSink(
		        odpsConf, tableName, richTableSchema, partition, dynamicPartitionLimit);
		if (partitionBy != null && !partitionBy.isEmpty()) {
			sink.setPartitionedField(partitionBy);
			sink.setShuffleEmptyKey(shuffleEmptyKey);
		}

		return sink;
	}

	@Override
	protected Map<String, String> requiredContextSpecific() {
		Map<String, String> context = new HashMap<>();
		context.put(CONNECTOR_TYPE, "QANAT_ODPS"); // ODPS
		context.put(CONNECTOR_PROPERTY_VERSION, "1"); // backwards compatibility
		return context;
	}

	@Override
	protected List<String> supportedSpecificProperties() {
		return mergeProperties(BlinkOptions.ODPS.SUPPORTED_KEYS, BlinkOptions.DIM.SUPPORTED_KEYS);
	}

	@Override
	public BatchCompatibleStreamTableSink<Tuple2<Boolean, Row>> createBatchCompatibleTableSink(Map<String, String> properties) {
		return createTableSink(properties, false);
	}

	@Override
	public StreamTableSink<Tuple2<Boolean, Row>> createStreamTableSink(Map<String, String> properties) {
		return createTableSink(properties, true);
	}
}
