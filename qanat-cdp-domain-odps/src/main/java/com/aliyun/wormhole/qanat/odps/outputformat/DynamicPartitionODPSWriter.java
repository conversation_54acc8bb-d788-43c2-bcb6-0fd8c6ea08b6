/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *	 http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.odps.outputformat;

import com.aliyun.odps.Odps;
import com.aliyun.odps.OdpsException;
import com.aliyun.odps.PartitionSpec;
import com.aliyun.odps.Table;
import com.aliyun.odps.data.RecordWriter;
import com.aliyun.odps.tunnel.TableTunnel;
import com.aliyun.odps.tunnel.TunnelException;
import com.aliyun.odps.tunnel.io.TunnelBufferedWriter;
import com.aliyun.wormhole.qanat.odps.conf.ODPSConf;
import com.aliyun.wormhole.qanat.odps.util.ODPSUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

public class DynamicPartitionODPSWriter extends ODPSWriter {

	private static final Logger LOGGER = LoggerFactory.getLogger(DynamicPartitionODPSWriter.class);
	private static final String NULL_PARTITION = "NULL";

	private final List<Integer> dynamicCols;
	private final String[] fieldNames;
	private final int dynamicPartitionLimit;
	private final String table;
	private final Map<String, TableTunnel.UploadSession> sessionMap = new HashMap<>();
	private final Map<String, RecordWriter> writerMap = new HashMap<>();
	private transient Map<String, AtomicInteger> cachedCountMap = null;
	private transient boolean dynamicScheduled = false;
	private transient Table tableClient;

	public DynamicPartitionODPSWriter(
			String[] fieldNames,
			int dynamicPartitionLimit,
			long flushIntervalMs,
			ODPSConf odpsConf,
			String table,
			String partition) {
		super(flushIntervalMs, odpsConf);
		this.fieldNames = fieldNames;
		dynamicCols = new ArrayList<>();
		String[] partNames = partition.split(",");
		for (int i = 0; i < fieldNames.length; i++) {
			for (String partName : partNames) {
				if (partName.trim().equalsIgnoreCase(fieldNames[i])) {
					dynamicCols.add(i);
				}
			}
		}
		this.dynamicPartitionLimit = dynamicPartitionLimit;
		this.table = table;
	}

	public String getPartitionKey(Row row) {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < dynamicCols.size(); i++) {
			sb.append(fieldNames[dynamicCols.get(i)]);
			sb.append("=");
			sb.append(row.getField(dynamicCols.get(i)));
			if (i < dynamicCols.size() - 1) {
				sb.append(",");
			}
		}
		return sb.toString();
	}

	private String getNullPartitionKey() {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < dynamicCols.size(); i++) {
			sb.append(fieldNames[dynamicCols.get(i)]);
			sb.append("=");
			sb.append(NULL_PARTITION);
			if (i < dynamicCols.size() - 1) {
				sb.append(",");
			}
		}
		return sb.toString();
	}

	@Override
	public synchronized void flush(boolean commit) throws IOException {
		for (Map.Entry<String, RecordWriter> recordWriterEntry : writerMap.entrySet()) {
			((TunnelBufferedWriter) recordWriterEntry.getValue()).getTotalBytes(); //trigger flush
			cachedCountMap.get(recordWriterEntry.getKey()).set(0);
		}
		lastFlushTimeMs = System.currentTimeMillis();
		if (commit) {
			try {
				LOGGER.info("commit to odps with dynamic partition, size: " + sessionMap.size());
				for (Map.Entry<String, TableTunnel.UploadSession> e : sessionMap.entrySet()) {
					e.getValue().commit();
					LOGGER.info("commit to odps && sessionId:" + e.getValue().getId()
							+ " dynamic partition key: " + e.getKey());
				}
				sessionMap.clear();
				writerMap.clear();
				cachedCountMap.clear();
			} catch (Throwable t) {
				throw new RuntimeException(t);
			}
		}
	}

	@Override
	public void close() throws IOException {
		if (flusher != null) {
			flusher.shutdownNow();
			while (!flusher.isTerminated()) {
				try {
					Thread.sleep(10);
				} catch (Throwable t) {
					LOGGER.error("Exception Happened In Sleep Method", t);
					//ignore
				}
			}
			flusher = null;
		}

		synchronized (this) {
			for (Map.Entry<String, RecordWriter> e : writerMap.entrySet()) {
				e.getValue().close();
			}

			for (Map.Entry<String, TableTunnel.UploadSession> e : sessionMap.entrySet()) {
				try {
					e.getValue().commit();
				} catch (TunnelException exception) {
					LOGGER.error("Fail to commit odps upload session!", e);
					throw new RuntimeException(exception);
				}
			}

			writerMap.clear();
			sessionMap.clear();
		}
	}

	@Override
	public synchronized void checkAndFlush(String dynamicPartKey) throws IOException {
		cachedCountMap.get(dynamicPartKey).incrementAndGet();

		long currentTime = System.currentTimeMillis();
		if (writerMap.get(dynamicPartKey) instanceof TunnelBufferedWriter && currentTime - lastFlushTimeMs > flushIntervalMs
				|| cachedCountMap.get(dynamicPartKey).get() > batchCount) {
			flush(false);
		}
	}


	@Override
	public void open() {
		this.tableTunnel = ODPSUtil.createTableTunnel(odpsConf);
		this.cachedCountMap = new HashMap<>();
		Odps odps = ODPSUtil.initOdps(odpsConf);
		String project = odpsConf.getProject();
		this.tableClient = odps.tables().get(project, table);
	}

	@Override
	public TableTunnel.UploadSession getUploadSession(String dynamicPartKey) throws TunnelException {
		if (StringUtils.isEmpty(dynamicPartKey)) {
			dynamicPartKey = getNullPartitionKey();
		}
		if (sessionMap.containsKey(dynamicPartKey)) {
			return sessionMap.get(dynamicPartKey);
		} else {
			if (sessionMap.size() >= dynamicPartitionLimit) {
				throw new RuntimeException("Too many dynamic partitions: "
						+ sessionMap.size()
						+ ", which exceeds the size limit: " + dynamicPartitionLimit);
			}
			String project = odpsConf.getProject();

			PartitionSpec partSpec = new PartitionSpec(dynamicPartKey);
			try {
				synchronized (this) {
					if (!tableClient.hasPartition(partSpec)) {
						tableClient.createPartition(partSpec, true);
					}
				}
			} catch (OdpsException e) {
				throw new TunnelException("ODPS Exception: ", e);
			}

			TableTunnel.UploadSession uploadSession = tableTunnel.createUploadSession(
					project, table, partSpec);
			sessionMap.put(dynamicPartKey, uploadSession);
			return uploadSession;
		}
	}

	@Override
	public RecordWriter getRecordWriter(String dynamicPartKey) throws TunnelException {

		if (StringUtils.isEmpty(dynamicPartKey)) {
			dynamicPartKey = getNullPartitionKey();
		}
		RecordWriter recordWriter;
		if (writerMap.containsKey(dynamicPartKey)) {
			recordWriter = writerMap.get(dynamicPartKey);
		} else {
			if (writerMap.size() >= dynamicPartitionLimit) {
				throw new RuntimeException("Too many dynamic partitions: "
						+ sessionMap.size()
						+ ", which exceeds the size limit: " + dynamicPartitionLimit);
			}
			TableTunnel.UploadSession uploadSession = getUploadSession(dynamicPartKey);
			recordWriter = createRecordWriter(uploadSession);
			writerMap.put(dynamicPartKey, recordWriter);
			LOGGER.info("Created upload session id: " + uploadSession.getId()
					+ " record writer: " + recordWriter + " partition key: " + dynamicPartKey);

			AtomicInteger currentCachedCount = new AtomicInteger(0);
			cachedCountMap.put(dynamicPartKey, currentCachedCount);
		}
		if (flushIntervalMs > 0 && !dynamicScheduled) {
			scheduleFlusher();
			dynamicScheduled = true;
		}
		return recordWriter;
	}

}
