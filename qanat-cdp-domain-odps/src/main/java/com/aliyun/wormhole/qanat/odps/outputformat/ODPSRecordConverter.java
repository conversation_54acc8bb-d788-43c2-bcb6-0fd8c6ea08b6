/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.odps.outputformat;

import com.alibaba.blink.streaming.connectors.common.util.RowConverter;
import com.aliyun.odps.Column;
import com.aliyun.odps.OdpsType;
import com.aliyun.odps.data.Record;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.types.Row;

import java.text.NumberFormat;


class ODPSRecordConverter implements RowConverter<Row, Record> {

	private static NumberFormat nf = NumberFormat.getInstance();
	static {
		// From odps code double-conversion.cc
		//
		// static int flags = DoubleToStringConverter::EMIT_TRAILING_DECIMAL_POINT |
		//     DoubleToStringConverter::EMIT_TRAILING_ZERO_AFTER_POINT;
		// static DoubleToStringConverter dc3(flags,"inf", "nan", 'e', -6, 20, 0, 0);
		//
		// We use Java to simulate the behavior
		nf.setGroupingUsed(false);
		nf.setMinimumIntegerDigits(1);
		nf.setMinimumFractionDigits(1);
		nf.setMaximumFractionDigits(6);
	}

	private static Object convertField(Object field, OdpsType odpsType) {
		if (null == field) {
			return null;
		}
		switch (odpsType) {
			case BIGINT:
				return toLong(field);
			case INT:
				return toInt(field);
			case SMALLINT:
				return toShort(field);
			case TINYINT:
				return toByte(field);
			case DOUBLE:
				return toDouble(field);
			case FLOAT:
				return toFloat(field);
			case BOOLEAN:
				return toBoolean(field);
			case STRING:
			case VARCHAR:
				return toString(field);
			default:
				return field;
		}
	}

	private static long toLong(Object field) {
		if (field instanceof Byte) {
			return (Byte) field;
		} else if (field instanceof Short) {
			return (Short) field;
		} else if (field instanceof Integer) {
			return (Integer) field;
		} else if (field instanceof Long) {
			return (Long) field;
		} else if (field instanceof Float) {
			throw new ClassCastException("Float value: " + field + " can not be cast to Long");
		} else if (field instanceof Double) {
			throw new ClassCastException("Double value: " + field + " can not be cast to Long");
		} else if (field instanceof Boolean) {
			return ((Boolean) field) ? 1L : 0L;
		} else if (field instanceof Character) {
			return (Character) field;
		}
		return (long) field;
	}

	private static int toInt(Object field) {
		if (field instanceof Byte) {
			return (Byte) field;
		} else if (field instanceof Short) {
			return (Short) field;
		} else if (field instanceof Integer) {
			return (Integer) field;
		} else if (field instanceof Long) {
			throw new ClassCastException("Long value: " + field + " can not be cast to Integer");
		} else if (field instanceof Float) {
			throw new ClassCastException("Float value: " + field + " can not be cast to Integer");
		} else if (field instanceof Double) {
			throw new ClassCastException("Double value: " + field + " can not be cast to Integer");
		} else if (field instanceof Boolean) {
			return ((Boolean) field) ? 1 : 0;
		} else if (field instanceof Character) {
			return (Character) field;
		}
		return (int) field;
	}

	private static short toShort(Object field) {
		if (field instanceof Byte) {
			return (Byte) field;
		} else if (field instanceof Short) {
			return (Short) field;
		} else if (field instanceof Integer) {
			throw new ClassCastException("Integer value: " + field + " can not be cast to Short");
		} else if (field instanceof Long) {
			throw new ClassCastException("Long value: " + field + " can not be cast to Short");
		} else if (field instanceof Float) {
			throw new ClassCastException("Float value: " + field + " can not be cast to Short");
		} else if (field instanceof Double) {
			throw new ClassCastException("Double value: " + field + " can not be cast to Short");
		} else if (field instanceof Boolean) {
			return (short) (((Boolean) field) ? 1 : 0);
		} else if (field instanceof Character) {
			return ((short) ((Character) field).charValue());
		}
		return (short) field;
	}

	private static byte toByte(Object field) {
		if (field instanceof Byte) {
			return (Byte) field;
		} else if (field instanceof Short) {
			throw new ClassCastException("Short value: " + field + " can not be cast to Byte");
		} else if (field instanceof Integer) {
			throw new ClassCastException("Integer value: " + field + " can not be cast to Byte");
		} else if (field instanceof Long) {
			throw new ClassCastException("Long value: " + field + " can not be cast to Byte");
		} else if (field instanceof Float) {
			throw new ClassCastException("Float value: " + field + " can not be cast to Byte");
		} else if (field instanceof Double) {
			throw new ClassCastException("Double value: " + field + " can not be cast to Byte");
		} else if (field instanceof Boolean) {
			return (byte) (((Boolean) field) ? 1 : 0);
		} else if (field instanceof Character) {
			return ((byte) ((Character) field).charValue());
		}
		return (byte) field;
	}

	private static double toDouble(Object field) {
		if (field instanceof Byte) {
			return (Byte) field;
		} else if (field instanceof Short) {
			return (Short) field;
		} else if (field instanceof Integer) {
			return (Integer) field;
		} else if (field instanceof Long) {
			return (Long) field;
		} else if (field instanceof Float) {
			return (Float) field;
		} else if (field instanceof Double) {
			return (Double) field;
		} else if (field instanceof Boolean) {
			return ((Boolean) field) ? 1d : 0d;
		} else if (field instanceof Character) {
			return (Character) field;
		}
		return (double) field;
	}

	private static float toFloat(Object field) {
		if (field instanceof Byte) {
			return (Byte) field;
		} else if (field instanceof Short) {
			return (Short) field;
		} else if (field instanceof Integer) {
			return (Integer) field;
		} else if (field instanceof Long) {
			return (Long) field;
		} else if (field instanceof Float) {
			return (Float) field;
		} else if (field instanceof Double) {
			throw new ClassCastException("Double value: " + field + " can not be cast to Float");
		} else if (field instanceof Boolean) {
			return ((Boolean) field) ? 1.0f : 0f;
		} else if (field instanceof Character) {
			return (Character) field;
		}
		return (float) field;
	}

	private static boolean toBoolean(Object field) {
		if (field instanceof Byte) {
			return (Byte) field != 0;
		} else if (field instanceof Short) {
			return (Short) field != 0;
		} else if (field instanceof Integer) {
			return (Integer) field != 0;
		} else if (field instanceof Long) {
			return (Long) field != 0;
		} else if (field instanceof Float) {
			return (Float) field != 0;
		} else if (field instanceof Double) {
			return (Double) field != 0;
		} else if (field instanceof Boolean) {
			return ((Boolean) field);
		} else if (field instanceof Character) {
			return (Character) field != 0;
		}
		return (boolean) field;
	}

	private static String toString(Object field) {
		double d = 0.0;
		if (field instanceof Float) {
			d = ((Float) field).doubleValue();
		} else if (field instanceof Double) {
			d = (Double) field;
		}
		if (d >= 1e-6 && d < 1e21) {
			return nf.format(d);
		}
		return field.toString();
	}

	@Override
	public void open(RuntimeContext context) {

	}

	@Override
	public Record convert(Row row, Record record) {
		// set record data
		Column[] columns = record.getColumns();
		assert record.getColumnCount()  == row.getArity();

		for (int i = 0; i < row.getArity(); i++) {
			OdpsType odpsType = columns[i].getType();
			Object field = convertField(row.getField(i), odpsType);
			record.set(i, field);
		}
		return record;
	}

	@Override
	public void close() {

	}
}
