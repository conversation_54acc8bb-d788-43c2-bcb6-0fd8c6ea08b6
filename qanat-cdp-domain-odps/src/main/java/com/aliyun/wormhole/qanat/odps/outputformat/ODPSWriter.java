/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *	 http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.odps.outputformat;

import com.aliyun.odps.commons.util.RetryStrategy;
import com.aliyun.odps.data.RecordWriter;
import com.aliyun.odps.tunnel.TableTunnel;
import com.aliyun.odps.tunnel.TunnelException;
import com.aliyun.odps.tunnel.io.TunnelBufferedWriter;
import com.aliyun.wormhole.qanat.odps.conf.ODPSConf;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.Serializable;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * This class is to create an ODPS writer for both dynamic partition and static partition.
 */
public abstract class ODPSWriter implements Serializable {

	private static final Logger LOGGER = LoggerFactory.getLogger(ODPSWriter.class);

	protected long batchCount = Long.MAX_VALUE;
	protected int retryTimes = 6;
	protected long sleepTime = 4000;
	protected long lastFlushTimeMs = 0;
	protected final long flushIntervalMs;
	protected final ODPSConf odpsConf;
	protected transient TableTunnel tableTunnel;
	protected transient ScheduledExecutorService flusher;

	private volatile transient Throwable flushException = null;
	private volatile boolean flushError = false;

	public ODPSWriter(long flushIntervalMs, ODPSConf odpsConf) {
		this.flushIntervalMs = flushIntervalMs;
		this.odpsConf = odpsConf;
	}

	/**
	 * Open the session on the server side.
	 */
	abstract void open() throws IOException;

	/**
	 * Get the upload session of the ODPS tunnel
	 * @param partitionKey Partition Key
	 * @return Tunnel Upload Session.
	 * @throws TunnelException
	 */
	abstract TableTunnel.UploadSession getUploadSession(String partitionKey) throws TunnelException;

	abstract RecordWriter getRecordWriter(String partitionKey) throws TunnelException;

	/**
	 * Return the dynamic partition key, the key can be null
	 * @param row Current row
	 * @return partition key eg "pt='20190101'" or null
	 */
	abstract String getPartitionKey(Row row);

	/**
	 * Flush the data in buffer to the ODPS tunnel.
	 * @param commit If commit is true, trigger flush, commit and then rebuild the writer
	 *               and upload session. If commit is false, trigger flush only.
	 * @throws IOException
	 */
	abstract void flush(boolean commit) throws IOException;

	abstract void close() throws IOException;

	abstract void checkAndFlush(String partitionKey) throws IOException;

	public void setBatchCount(long batchCount) {
		this.batchCount = batchCount;
	}

	public void setRetryTimes(int retryTimes) {
		this.retryTimes = retryTimes;
	}

	public void setSleepTime(long sleepTime) {
		this.sleepTime = sleepTime;
	}

	public long getRetryTimeout() {
		return retryTimes * sleepTime;
	}

	public boolean flushError() {
		return flushError && null != flushException;
	}

	public Throwable getFlushException() {
		return flushException;
	}

	/**
	 * Start flusher that will flush buffer automatically
	 */
	protected void scheduleFlusher() {
		flusher = new ScheduledThreadPoolExecutor(
				1,
				new BasicThreadFactory.Builder().namingPattern("ODPSStreamOutputFormat.buffer.flusher")
						.daemon(true)
						.build());
		flusher.scheduleAtFixedRate(new Runnable() {
			@Override
			public void run() {
				try {
					flush(false);
				} catch (Throwable t) {
					LOGGER.error("Sync sink buffer to ODPS failed", t);
					flushException = t;
					flushError = true;
				}
			}
		}, flushIntervalMs, flushIntervalMs, TimeUnit.MILLISECONDS);
	}


	protected RecordWriter createRecordWriter(TableTunnel.UploadSession uploadSession) throws TunnelException {
		RecordWriter recordWriter = uploadSession.openBufferedWriter(true);
		RetryStrategy retryStrategy = new RetryStrategy(retryTimes, (int) (sleepTime / 1000),
				RetryStrategy.BackoffStrategy.EXPONENTIAL_BACKOFF);
		((TunnelBufferedWriter) recordWriter).setRetryStrategy(retryStrategy);
		return recordWriter;
	}
}
