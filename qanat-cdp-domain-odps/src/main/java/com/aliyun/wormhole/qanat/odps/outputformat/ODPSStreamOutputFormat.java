/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *	 http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.odps.outputformat;

import com.alibaba.blink.streaming.connectors.common.MetricUtils;
import com.alibaba.blink.streaming.connectors.common.output.HasRetryTimeout;
import com.alibaba.blink.streaming.connectors.common.output.Syncable;
import com.alibaba.blink.streaming.connectors.common.util.RowConverter;
import com.aliyun.odps.Column;
import com.aliyun.odps.OdpsType;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.data.RecordWriter;
import com.aliyun.odps.tunnel.TableTunnel;
import com.aliyun.wormhole.qanat.odps.conf.ODPSConf;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.io.RichOutputFormat;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Meter;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nullable;
import java.io.IOException;
import java.text.NumberFormat;

import static org.apache.flink.util.Preconditions.checkArgument;
import static org.apache.flink.util.Preconditions.checkNotNull;

import org.apache.flink.api.java.tuple.Tuple2;

public class ODPSStreamOutputFormat
		extends RichOutputFormat<Tuple2<Boolean, Row>>
		implements Syncable, HasRetryTimeout {

	private static final Logger LOGGER = LoggerFactory.getLogger(ODPSStreamOutputFormat.class);
	private final RowTypeInfo rowTypeInfo;
	private final ODPSWriter odpsWriter;
	private RowConverter<Row, Record> customConverter = null;

	private Meter outTps;
	private Meter outBps;

	public ODPSStreamOutputFormat(
			ODPSConf odpsConf,
			String table,
			RowTypeInfo rowTypeInfo,
			@Nullable String partition,
			boolean isPartitioned,
			long flushIntervalMs,
			int dynamicPartitionLimit) {

		checkNotNull(odpsConf, "ODPS configuration can not be null.");
//		checkArgument(StringUtils.isNotBlank(table), "Table name can not be null or blank.");
		this.odpsWriter = ODPSWriterFactory.createODPSWriter(
				rowTypeInfo.getFieldNames(),
				dynamicPartitionLimit,
				flushIntervalMs,
				odpsConf,
				table,
				partition,
				isPartitioned);
		this.rowTypeInfo = rowTypeInfo;
	}

	public ODPSStreamOutputFormat setBatchCount(long batchCount) {
		odpsWriter.setBatchCount(batchCount);
		return this;
	}

	public ODPSStreamOutputFormat setRetryTimes(int retryTimes) {
		odpsWriter.setRetryTimes(retryTimes);
		return this;
	}

	public ODPSStreamOutputFormat setSleepTime(long sleepTime) {
		odpsWriter.setSleepTime(sleepTime);
		return this;
	}

	public ODPSStreamOutputFormat setCustomConverter(RowConverter<Row, Record> customConverter) {
		this.customConverter = customConverter;
		return this;
	}

	/**
	 * Write Add record
	 *
	 * @param tupleRow
	 * @throws IOException
	 */
	@Override
	public void writeRecord(Tuple2<Boolean, Row> tupleRow) throws IOException {
		if (odpsWriter.flushError()){
			throw new RuntimeException(odpsWriter.getFlushException());
		}
		// add synchronized keyword to keep consistency
		synchronized (odpsWriter) {
			if (tupleRow.f0) {
				Row row = tupleRow.f1;
				try {
					String partitionKey = odpsWriter.getPartitionKey(row);
					TableTunnel.UploadSession uploadSession = odpsWriter.getUploadSession(partitionKey);
					Record newEmptyRecord = uploadSession.newRecord();
					if (null != outTps) {
						outTps.markEvent();
					}
					Record fullfillRecord;
					if (customConverter == null) {
						fullfillRecord = ODPSRecordConverter.convert(row, newEmptyRecord, this.rowTypeInfo);
					} else {
						fullfillRecord = customConverter.convert(row, newEmptyRecord);
					}
					if (null != outBps) {
						// rough estimate each column only has 50 bytes
						outBps.markEvent(fullfillRecord.getColumnCount() * 50);
					}

					RecordWriter recordWriter = odpsWriter.getRecordWriter(partitionKey);
					recordWriter.write(fullfillRecord);
					odpsWriter.checkAndFlush(partitionKey);

				} catch (Throwable e) {
					LOGGER.warn("Fail to write record into odps tunnel! ", e);
					throw new IOException("Fail to write record into odps tunnel! ", e);
				}
			}
		}
	}

	@Override
	public void configure(Configuration configuration) {

	}

	@Override
	public void open(int taskNumber, int numTasks) throws IOException {
		LOGGER.info("Open odps outputFormat:" + taskNumber + "/" + numTasks);
		try {
			if (customConverter != null) {
				customConverter.open(getRuntimeContext());
			}
			odpsWriter.open();
		} catch (Throwable e) {
			LOGGER.warn("Fail to create odps writer! ", e);
			throw new IOException("Fail to create odps writer! ", e);
		}
		outTps = MetricUtils.registerOutTps(getRuntimeContext());
		outBps = MetricUtils.registerOutBps(getRuntimeContext(), "odps");
	}

	@Override
	public void close() throws IOException {
		odpsWriter.close();

		if (customConverter != null) {
			customConverter.close();
		}
	}

	@Override
	public long getRetryTimeout() {
		return odpsWriter.getRetryTimeout();
	}

	@Override
	public synchronized void sync() throws IOException {
		odpsWriter.flush(true);
	}

	private static class ODPSRecordConverter {

		private static NumberFormat nf = NumberFormat.getInstance();

		static {
			// From odps code double-conversion.cc
			//
			// static int flags = DoubleToStringConverter::EMIT_TRAILING_DECIMAL_POINT |
			//	 DoubleToStringConverter::EMIT_TRAILING_ZERO_AFTER_POINT;
			// static DoubleToStringConverter dc3(flags,"inf", "nan", 'e', -6, 20, 0, 0);
			//
			// We use Java to simulate the behavior
			nf.setGroupingUsed(false);
			nf.setMinimumIntegerDigits(1);
			nf.setMinimumFractionDigits(1);
			nf.setMaximumFractionDigits(6);
		}

		private static Object convertField(Object field, OdpsType odpsType) {
			if (null == field) {
				return null;
			}
			switch (odpsType) {
				case BIGINT:
					return toLong(field);
				case INT:
					return toInt(field);
				case SMALLINT:
					return toShort(field);
				case TINYINT:
					return toByte(field);
				case DOUBLE:
					return toDouble(field);
				case FLOAT:
					return toFloat(field);
				case BOOLEAN:
					return toBoolean(field);
				case STRING:
				case VARCHAR:
					return toString(field);
				default:
					return field;
			}
		}

		private static long toLong(Object field) {
			if (field instanceof Byte) {
				return (Byte) field;
			} else if (field instanceof Short) {
				return (Short) field;
			} else if (field instanceof Integer) {
				return (Integer) field;
			} else if (field instanceof Long) {
				return (Long) field;
			} else if (field instanceof Float) {
				throw new ClassCastException("Float value: " + field + " can not be cast to Long");
			} else if (field instanceof Double) {
				throw new ClassCastException("Double value: " + field + " can not be cast to Long");
			} else if (field instanceof Boolean) {
				return ((Boolean) field) ? 1L : 0L;
			} else if (field instanceof Character) {
				return (Character) field;
			}
			return (long) field;
		}

		private static int toInt(Object field) {
			if (field instanceof Byte) {
				return (Byte) field;
			} else if (field instanceof Short) {
				return (Short) field;
			} else if (field instanceof Integer) {
				return (Integer) field;
			} else if (field instanceof Long) {
				throw new ClassCastException("Long value: " + field + " can not be cast to Integer");
			} else if (field instanceof Float) {
				throw new ClassCastException("Float value: " + field + " can not be cast to Integer");
			} else if (field instanceof Double) {
				throw new ClassCastException("Double value: " + field + " can not be cast to Integer");
			} else if (field instanceof Boolean) {
				return ((Boolean) field) ? 1 : 0;
			} else if (field instanceof Character) {
				return (Character) field;
			}
			return (int) field;
		}

		private static short toShort(Object field) {
			if (field instanceof Byte) {
				return (Byte) field;
			} else if (field instanceof Short) {
				return (Short) field;
			} else if (field instanceof Integer) {
				throw new ClassCastException("Integer value: " + field + " can not be cast to Short");
			} else if (field instanceof Long) {
				throw new ClassCastException("Long value: " + field + " can not be cast to Short");
			} else if (field instanceof Float) {
				throw new ClassCastException("Float value: " + field + " can not be cast to Short");
			} else if (field instanceof Double) {
				throw new ClassCastException("Double value: " + field + " can not be cast to Short");
			} else if (field instanceof Boolean) {
				return (short) (((Boolean) field) ? 1 : 0);
			} else if (field instanceof Character) {
				return ((short) ((Character) field).charValue());
			}
			return (short) field;
		}

		private static byte toByte(Object field) {
			if (field instanceof Byte) {
				return (Byte) field;
			} else if (field instanceof Short) {
				throw new ClassCastException("Short value: " + field + " can not be cast to Byte");
			} else if (field instanceof Integer) {
				throw new ClassCastException("Integer value: " + field + " can not be cast to Byte");
			} else if (field instanceof Long) {
				throw new ClassCastException("Long value: " + field + " can not be cast to Byte");
			} else if (field instanceof Float) {
				throw new ClassCastException("Float value: " + field + " can not be cast to Byte");
			} else if (field instanceof Double) {
				throw new ClassCastException("Double value: " + field + " can not be cast to Byte");
			} else if (field instanceof Boolean) {
				return (byte) (((Boolean) field) ? 1 : 0);
			} else if (field instanceof Character) {
				return ((byte) ((Character) field).charValue());
			}
			return (byte) field;
		}

		private static double toDouble(Object field) {
			if (field instanceof Byte) {
				return (Byte) field;
			} else if (field instanceof Short) {
				return (Short) field;
			} else if (field instanceof Integer) {
				return (Integer) field;
			} else if (field instanceof Long) {
				return (Long) field;
			} else if (field instanceof Float) {
				return (Float) field;
			} else if (field instanceof Double) {
				return (Double) field;
			} else if (field instanceof Boolean) {
				return ((Boolean) field) ? 1d : 0d;
			} else if (field instanceof Character) {
				return (Character) field;
			}
			return (double) field;
		}

		private static float toFloat(Object field) {
			if (field instanceof Byte) {
				return (Byte) field;
			} else if (field instanceof Short) {
				return (Short) field;
			} else if (field instanceof Integer) {
				return (Integer) field;
			} else if (field instanceof Long) {
				return (Long) field;
			} else if (field instanceof Float) {
				return (Float) field;
			} else if (field instanceof Double) {
				throw new ClassCastException("Double value: " + field + " can not be cast to Float");
			} else if (field instanceof Boolean) {
				return ((Boolean) field) ? 1.0f : 0f;
			} else if (field instanceof Character) {
				return (Character) field;
			}
			return (float) field;
		}

		private static boolean toBoolean(Object field) {
			if (field instanceof Byte) {
				return (Byte) field != 0;
			} else if (field instanceof Short) {
				return (Short) field != 0;
			} else if (field instanceof Integer) {
				return (Integer) field != 0;
			} else if (field instanceof Long) {
				return (Long) field != 0;
			} else if (field instanceof Float) {
				return (Float) field != 0;
			} else if (field instanceof Double) {
				return (Double) field != 0;
			} else if (field instanceof Boolean) {
				return ((Boolean) field);
			} else if (field instanceof Character) {
				return (Character) field != 0;
			}
			return (boolean) field;
		}

		private static String toString(Object field) {
			double d = 0.0;
			if (field instanceof Float) {
				d = ((Float) field).doubleValue();
			} else if (field instanceof Double) {
				d = (Double) field;
			}
			if (d >= 1e-6 && d < 1e21) {
				return nf.format(d);
			}
			return field.toString();
		}

		public static Record convert(
				Row row,
				Record record,
				RowTypeInfo rowTypeInfo) {
			// set record data
			Column[] columns = record.getColumns();
			String[] fieldNames = rowTypeInfo.getFieldNames();
			// For example, Row is 4 columns, rowTypeInfo is 4 columns, Record is 3 columns
			int rn = 0;
			String bizObjectId = (String)row.getField(0);
            String val = (String)row.getField(1);
            String [] tokens = val.split("\\|");
            for (String token : tokens) {
                String[] kvs = token.split("`");
                String fieldName = "domain_model__" + kvs[0];
                if (kvs.length == 2) {
                    String fieldValue = kvs[1];
                    record.setString(fieldName, fieldValue);
                }
            }
//			for (int i = 0; i < row.getArity(); i++) {
//				if (rn >= columns.length || !fieldNames[i].equalsIgnoreCase(columns[rn].getName())) {
//					continue;
//				}
//				OdpsType odpsType = columns[rn].getType();
//				Object field = convertField(row.getField(i), odpsType);
//				record.set(rn, field);
//				rn++;
//			}

			return record;
		}
	}
}
