/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.odps.schema;

import com.aliyun.odps.Column;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.apache.flink.util.Preconditions.checkArgument;

/**
 * ODPS table schema information including column information and isPartition table
 */
public class ODPSTableSchema implements Serializable {
	private static final long serialVersionUID = -6327923765714170499L;

	private final List<ODPSColumn> columns;

	private final boolean isPartition;

	private final boolean isView;

	private transient Map<String, ODPSColumn> columnMap;

	public ODPSTableSchema(
			List<Column> normalColumns,
			List<Column> partitionColumns,
			boolean isView) {

		checkArgument(
				normalColumns != null && !normalColumns.isEmpty(),
				"input normal columns cannot be null or empty!");

		List<ODPSColumn> columnList = new ArrayList<>();
		for(Column column: normalColumns){
			columnList.add(new ODPSColumn(column.getName(), column.getType()));
		}
		this.isView = isView;

		boolean hasPartitionCols = partitionColumns != null && !partitionColumns.isEmpty();

		if (hasPartitionCols) {
			List<ODPSColumn> partitionColumnList = new ArrayList<>();
			for(Column column: partitionColumns){
				partitionColumnList.add(new ODPSColumn(column.getName(), column.getType(), true));
			}
			columnList.addAll(partitionColumnList);
		}
		isPartition = !isView && hasPartitionCols;
		this.columns = columnList;
		rebuildColumnMap();
	}

	public List<ODPSColumn> getColumns() {
		return columns;
	}

	public boolean isPartition() {
		return isPartition;
	}

	public boolean isView() {
		return isView;
	}

	public ODPSColumn getColumn(String name) {
		return columnMap.get(name);
	}

	public boolean isPartitionColumn(String name) {
		ODPSColumn column = columnMap.get(name);
		if (column != null) {
			return column.isPartition();
		} else {
			throw new IllegalArgumentException("unknown column " + name);
		}
	}

	private void readObject(ObjectInputStream inputStream) throws IOException,
			ClassNotFoundException
	{
		inputStream.defaultReadObject();
		rebuildColumnMap();
	}

	private void rebuildColumnMap() {
		Map<String, ODPSColumn> tmpMap = new HashMap<>();
		for(ODPSColumn column:columns){
			if(column != null){
				tmpMap.put(column.getName(), column);
			}
		}
		this.columnMap = tmpMap;
	}
}

