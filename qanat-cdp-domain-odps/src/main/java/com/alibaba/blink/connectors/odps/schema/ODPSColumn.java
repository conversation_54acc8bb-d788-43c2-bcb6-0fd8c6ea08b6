/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.odps.schema;

import com.aliyun.odps.OdpsType;

import java.io.Serializable;

/**
 * column information including name, type, isPartition trait
 */
public class ODPSColumn implements Serializable {
	private static final long serialVersionUID = 3385061492593160182L;
	private final String name;
	private final OdpsType type;
	private final boolean isPartition;

	public ODPSColumn(String name, OdpsType type) {
		this(name, type, false);
	}

	public ODPSColumn(String name, OdpsType type, boolean isPartition) {
		this.name = name;
		this.type = type;
		this.isPartition = isPartition;
	}

	public String getName() {
		return name;
	}

	public OdpsType getType() {
		return type;
	}

	public boolean isPartition() {
		return isPartition;
	}

	@Override
	public String toString() {
		return "ODPSColumn{" +
				"name='" + name + '\'' +
				", type=" + type +
				", isPartition=" + isPartition +
				'}';
	}
}
