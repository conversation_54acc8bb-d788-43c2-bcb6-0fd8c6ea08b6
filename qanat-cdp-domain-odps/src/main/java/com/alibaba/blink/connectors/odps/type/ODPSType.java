/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.odps.type;

import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.PrimitiveArrayTypeInfo;
import org.apache.flink.api.common.typeinfo.SqlTimeTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.table.dataformat.BinaryString;
import org.apache.flink.table.dataformat.Decimal;
import org.apache.flink.table.dataformat.GenericRow;
import org.apache.flink.table.types.DecimalType;

import com.aliyun.odps.data.ArrayRecord;
import com.aliyun.odps.data.Record;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/**
 * Supported odps column type
 */
public enum ODPSType {

	/**
	 * Type of a 1-byte signed integer with values from -128 to 127.
	 */
	TINYINT {
		public TypeInformation<?> toFlinkType() {
			return BasicTypeInfo.BYTE_TYPE_INFO;
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, String sourceValueStr) {
			Byte value = sourceValueStr == null ? null : Byte.valueOf(sourceValueStr);
			targetRow.update(targetFieldIdx, value);
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, Record sourceRecord,
				String sourceColumnName) {
			targetRow.update(targetFieldIdx, sourceRecord.get(sourceColumnName));
		}
	},

	/**
	 * Type of a 2-byte signed integer with values from -32,768 to 32,767.
	 */
	SMALLINT {
		public TypeInformation<?> toFlinkType() {
			return BasicTypeInfo.SHORT_TYPE_INFO;
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, String sourceValueStr) {
			Short value = sourceValueStr == null ? null : Short.valueOf(sourceValueStr);
			targetRow.update(targetFieldIdx, value);
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, Record sourceRecord,
				String sourceColumnName) {
			targetRow.update(targetFieldIdx, sourceRecord.get(sourceColumnName));
		}
	},

	/**
	 * Type of a 4-byte signed integer with values from -2,147,483,648 to 2,147,483,647.
	 */
	INT {
		public TypeInformation<?> toFlinkType() {
			return BasicTypeInfo.INT_TYPE_INFO;
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, String sourceValueStr) {
			Integer value = sourceValueStr == null ? null : Integer.valueOf(sourceValueStr);
			targetRow.update(targetFieldIdx, value);
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, Record sourceRecord,
				String sourceColumnName) {
			targetRow.update(targetFieldIdx, sourceRecord.get(sourceColumnName));
		}
	},

	/**
	 * Type of an 8-byte signed integer with values from -9,223,372,036,854,775,808 to
	 * * 9,223,372,036,854,775,807.
	 */
	BIGINT {
		public TypeInformation<?> toFlinkType() {
			return BasicTypeInfo.LONG_TYPE_INFO;
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, String sourceValueStr) {
			Long value = sourceValueStr == null ? null : Long.parseLong(sourceValueStr);
			targetRow.update(targetFieldIdx, value);
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, Record sourceRecord,
				String sourceColumnName) {
			targetRow.update(targetFieldIdx, sourceRecord.getBigint(sourceColumnName));
		}
	},

	/**
	 * Type of a 4-byte single precision floating point number.
	 */
	FLOAT {
		public TypeInformation<?> toFlinkType() {
			return BasicTypeInfo.FLOAT_TYPE_INFO;
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, String sourceValueStr) {
			Float value = sourceValueStr == null ? null : Float.parseFloat(sourceValueStr);
			targetRow.update(targetFieldIdx, value);
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, Record sourceRecord,
				String sourceColumnName) {
			targetRow.update(targetFieldIdx, sourceRecord.get(sourceColumnName));
		}
	},


	/**
	 * Type of an 8-byte double precision floating point number.
	 */
	DOUBLE {
		public TypeInformation<?> toFlinkType() {
			return BasicTypeInfo.DOUBLE_TYPE_INFO;
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, String sourceValueStr) {
			Double value = sourceValueStr == null ? null : Double.parseDouble(sourceValueStr);
			targetRow.update(targetFieldIdx, value);
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, Record sourceRecord,
				String sourceColumnName) {
			targetRow.update(targetFieldIdx, sourceRecord.getDouble(sourceColumnName));
		}
	},

	/**
	 * Type of a boolean with a (possibly) two-valued logic of {@code TRUE, FALSE}.
	 */
	BOOLEAN {
		public TypeInformation<?> toFlinkType() {
			return BasicTypeInfo.BOOLEAN_TYPE_INFO;
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, String sourceValueStr) {
			Boolean value = sourceValueStr == null ? null : Boolean.parseBoolean(sourceValueStr);
			targetRow.update(targetFieldIdx, value);
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, Record sourceRecord,
				String sourceColumnName) {
			targetRow.update(targetFieldIdx, sourceRecord.getBoolean(sourceColumnName));
		}

	},

	/**
	 * Type of a timestamp WITH GMT time zone consisting of {@code year-month-day hour:minute:second[.fractional]}
	 * with up to millisecond precision and values ranging from {@code 0000-01-01 00:00:00.000} to
	 * {@code 9999-12-31 23:59:59.999}.
	 * TODO take TimeZone into consideration.
	 */
	DATETIME {
		public TypeInformation<?> toFlinkType() {
			return SqlTimeTypeInfo.TIMESTAMP;
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, String sourceValueStr) {
			throw new UnsupportedOperationException("Partition column cannot be DATETIME type!");
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, Record sourceRecord,
				String sourceColumnName) {
			Date date = sourceRecord.getDatetime(sourceColumnName);
			Long result = null;
			if (date != null) {
				// TODO take TimeZone into consideration.
				result = date.getTime();
			}
			targetRow.update(targetFieldIdx, result);
		}

	},

	/**
	 * Type of a timestamp WITHOUT time zone consisting of {@code year-month-day hour:minute:second[.fractional]}
	 * with up to nanosecond precision and values ranging from {@code 0000-01-01 00:00:00.000000000} to
	 * {@code 9999-12-31 23:59:59.999999999}.
	 *
	 * Note: Timestamp in Blink only support millisecond precision.
	 */
	TIMESTAMP {
		public TypeInformation<?> toFlinkType() {
			return SqlTimeTypeInfo.TIMESTAMP;
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, String sourceValueStr) {
			throw new UnsupportedOperationException("Partition column cannot be TIMESTAMP type!");
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, Record sourceRecord,
				String sourceColumnName) {
			Timestamp timestamp = ((ArrayRecord) sourceRecord).getTimestamp(sourceColumnName);
			Long millis = null;
			if (timestamp != null) {
				millis = timestamp.getTime();
			}
			targetRow.update(targetFieldIdx, millis);
		}
	},

	/**
	 * Type of a variable-length character string. length from 1 to 65535.
	 */
	VARCHAR {
		public TypeInformation<?> toFlinkType() {
			return BasicTypeInfo.STRING_TYPE_INFO;
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, String sourceValueStr) {
			targetRow.update(targetFieldIdx, BinaryString.fromString(sourceValueStr));
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, Record sourceRecord,
				String sourceColumnName) {
			targetRow.update(targetFieldIdx, BinaryString.fromString(sourceRecord.getString(sourceColumnName)));
		}
	},

	/**
	 * Type of string，Upper limitation is 8M.
	 */
	STRING {
		public TypeInformation<?> toFlinkType() {
			return BasicTypeInfo.STRING_TYPE_INFO;
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, String sourceValueStr) {
			targetRow.update(targetFieldIdx, BinaryString.fromString(sourceValueStr));
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, Record sourceRecord,
				String sourceColumnName) {
			targetRow.update(targetFieldIdx, BinaryString.fromString(sourceRecord.getString(sourceColumnName)));
		}

	},

	/**
	 * Type of a decimal number with fixed precision and scale.
	 * NOTE: ODPS decimal type could support max precision 54, while Blink could only support 38.
	 */
	DECIMAL {

		public TypeInformation<?> toFlinkType() {
			return BasicTypeInfo.BIG_DEC_TYPE_INFO;
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, String sourceValueStr) {
			throw new UnsupportedOperationException("Partition column cannot be Decimal type!");
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, Record sourceRecord,
				String sourceColumnName) {
			BigDecimal field = sourceRecord.getDecimal(sourceColumnName);
			Decimal result = null;
			if (field != null) {
				result = Decimal.fromBigDecimal(field, DecimalType.MAX_PRECISION, DecimalType.MAX_SCALE);
			}
			targetRow.update(targetFieldIdx, result);
		}
	},

	/**
	 * Type of a fixed-length binary string (=a sequence of bytes).
	 */
	BINARY {
		public TypeInformation<?> toFlinkType() {
			return PrimitiveArrayTypeInfo.BYTE_PRIMITIVE_ARRAY_TYPE_INFO;
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, String sourceValueStr) {
			throw new UnsupportedOperationException("Partition column cannot be Binary type!");
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, Record sourceRecord,
				String sourceColumnName) {
			targetRow.update(targetFieldIdx, sourceRecord.getBytes(sourceColumnName));
		}
	},

	/**
	 * Type of an associative array that maps keys (including {@code NULL}) to values (including
	 * {@code NULL}).
	 */
	MAP {
		public TypeInformation<?> toFlinkType() {
			// TODO support Map later
			throw new UnsupportedOperationException("Map type is not supported now!");
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, String sourceValueStr) {
			throw new UnsupportedOperationException("Partition column cannot be Map type!");
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, Record sourceRecord,
				String sourceColumnName) {
			throw new UnsupportedOperationException("Map type is not supported now!");
		}

	},

	/**
	 * Type of an array of elements with same subtype.
	 */
	ARRAY {
		// TODO support Array later
		public TypeInformation<?> toFlinkType() {
			throw new UnsupportedOperationException("Array type is not supported now!");
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, String sourceValueStr) {
			throw new UnsupportedOperationException("Partition column cannot be Array type!");
		}

		public void setRowField(GenericRow targetRow, int targetFieldIdx, Record sourceRecord,
				String sourceColumnName) {
			throw new UnsupportedOperationException("Array type is not supported now!");
		}

	};

	/**
	 * Mapping from odps type to flink type information
	 *
	 * @return related flink type information
	 */
	public abstract TypeInformation<?> toFlinkType();

	/**
	 * Translates the value str to the correct type value, sets the translated value to specified
	 * location of target row.
	 *
	 * Note: Only used to translate partition field.
	 *
	 * @param targetRow target row to hold translated value
	 * @param targetFieldIdx index of field in row to hold translated value
	 * @param sourceValueStr the string value to translate
	 */
	public abstract void setRowField(
			GenericRow targetRow,
			int targetFieldIdx,
			String sourceValueStr);

	/**
	 * Gets the odps column value from Record, sets the value to specified location of row.
	 *
	 * @param targetRow target row holds translated value
	 * @param targetFieldIdx index of field in row holds translated value
	 * @param sourceRecord source record holds values to translate
	 * @param sourceColumnName column name identifies the column in record to translate
	 */
	public abstract void setRowField(
			GenericRow targetRow,
			int targetFieldIdx,
			Record sourceRecord,
			String sourceColumnName);

}
