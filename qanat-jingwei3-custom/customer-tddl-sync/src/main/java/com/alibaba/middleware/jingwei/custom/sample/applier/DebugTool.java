package com.alibaba.middleware.jingwei.custom.sample.applier;

import com.alibaba.middleware.jingwei.common.logger.JwLoggerFactoryV3;
import com.alibaba.middleware.jingwei.core.kernel.JingweiTaskContext;
import com.alibaba.middleware.jingwei.core.loader.TaskLoaderDebug;

/**
 * <pre>
 *
 *     tar包本地调试可以用
 * <AUTHOR>
 * Date: 2019-10-17
 * Time: 16:12
 * <pre>
 **/
public class DebugTool {
    public static void main(String[] args) throws Exception {
        startNormalTask();

    }

    public static void startNormalTask() {
        String taskName = "D_T_DC_184223";// D_T_DD_169634
        JwLoggerFactoryV3.taskInit(taskName);
        JingweiTaskContext.setTaskName(taskName);
        JingweiTaskContext.setConsoleDomain("http://jingwei.alibaba.net/jingwei3");
        System.setProperty("jingwei.task.name", taskName); // 如果是上传tar包的任务，需要此参数（logback使用）

        //把自己的tar包代码写这里
        new TaskLoaderDebug(taskName,new CustomerChangeApplier());

    }
}
