package com.alibaba.middleware.jingwei.custom.sample.applier;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.middleware.jingwei.externalApi.applier.ApplyResult;
import com.alibaba.middleware.jingwei.externalApi.applier.BatchCustomApplier;
import com.alibaba.middleware.jingwei.externalApi.applier.CustomContext;
import com.alibaba.middleware.jingwei.externalApi.exception.ApplierException;
import com.alibaba.middleware.jingwei.externalApi.message.DataMessage;
import com.alibaba.rocketmq.client.producer.MessageQueueSelector;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.common.message.Message;
import com.alibaba.rocketmq.common.message.MessageQueue;

import com.taobao.metaq.client.MetaProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;

/**
 * 精卫tar包自主消费的示例，使用上传tar包常见问题： 找不到类、方法：一般由于冲突引起，可以参考下dependency.info查看精卫已有的依赖，避免冲突
 * 尤其是一些asm相关的，比如aspectjweaver等，此外注意不要使用jdk8的stream语法，暂时无法反编译出来
 */
public class CustomerChangeApplier implements BatchCustomApplier {

    private static Logger logger = LoggerFactory.getLogger(CustomerChangeApplier.class);
    private ApplicationContext applicationContext;
    private MetaProducer producer;

    @Override
    public void init(CustomContext context) {
        producer = new MetaProducer("SOP_TASK_ES_JINGWEI3_PRODUCER");
        try {
            producer.start();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 实现CustomApplier单条处理的时候， 使用该apply方法，单条处理性能较差，不推荐
     *
     * @param message
     * @return
     * @throws ApplierException
     */
    @Override
    public ApplyResult apply(DataMessage message) throws ApplierException {
        logger.warn("receive message without batch : " + message);
        return ApplyResult.COMMIT;
    }

    @Override
    public void destroy() {
    }

    /**
     * 实现BatchCustomApplier的时候为批处理，使用该apply方法。List的大小不固定，源端拉取一般默认ring buffter size为8192,List size取决于流量大小 PS:
     * 常见代码bug为异步处理失败，但是又没有准确捕获异常，导致丢数据，用户编写自己的消费逻辑时务必注意，确保处理失败能抛出异常，精卫侧发现异常会不断重试，触发报警阈值10分钟之后会触发报警 PS: 如果出现Failed to
     * visit entry xxx的异常，请去掉jdk8 stream语法相关的代码段后重试
     * 
     * @param message
     * @param hasMerge
     * @return
     * @throws ApplierException
     */
    @Override
    public ApplyResult apply(List<DataMessage> message, boolean hasMerge) throws ApplierException {
        for (DataMessage dataMessage : message) {
            // 针对dataMessage做一些业务处理，注意处理失败的情况不要吞异常，有异常直接上抛，精卫侧会尝试重试的
            // 针对update event注意下rowChangeData和rowData的区别，一个是变更后的值，一个是变更前的值，切勿取错
            logger.info("message={}", JSON.toJSONString(dataMessage));
            List<Map<String,Serializable>> dataList = dataMessage.getRowDataMaps();
            String tableName = dataMessage.getTableName();
            for (Map<String,Serializable> data : dataList) {
                Serializable pk = data.get("id");
                
                Message msg = new Message("SOP_TASK_ES", tableName, pk + "", JSON.toJSONString(data).getBytes());
                SendResult sendResult;
                try {
                    sendResult = producer.send(msg, new MessageQueueSelector() {
                        @Override
                        public MessageQueue select(List<MessageQueue> mqs, Message msg, Object arg) {
                            Integer id = (Integer)arg;
                            int index = id % mqs.size();
                            return mqs.get(index);
                        }
                    }, Integer.valueOf(pk+""));
                    logger.info(JSON.toJSONString(sendResult));
                } catch (Exception e) {
                    logger.error("msg table[{}] pk[{}] pub failed", tableName, pk, e);
                }
            }
        }
        return ApplyResult.COMMIT;
    }

    /**
     * applier
     * 参数默认现在都是开启batch=true，启用批处理的时候是否要开启合并，开启合并后性能会更好，符合最终一致性。例如相同pk上的多次update，最后只会保留最后一条，之前的会丢弃，用户根据自己业务需要选择是否开启
     * 
     * @return
     */
    @Override
    public boolean isMerge() {
        return false;
    }
}
