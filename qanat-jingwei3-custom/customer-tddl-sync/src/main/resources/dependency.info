[INFO] Scanning for projects...
[INFO] 
[INFO] ------------------------------------------------------------------------
[INFO] Building jingwei project web module 3.2.19-SNAPSHOT 3.2.19-SNAPSHOT
[INFO] ------------------------------------------------------------------------
[WARNING] The POM for com.aliyun:aliyun-java-sdk-core:jar:3.0.8 is missing, no dependency information available
[INFO] 
[INFO] --- maven-dependency-plugin:2.8:tree (default-cli) @ jingwei-web ---
[INFO] com.alibaba.middleware.jingwei:jingwei-web:war:3.2.19-SNAPSHOT
[INFO] +- com.dingtalk.api:dingtalk-sdk-java:jar:20181115:compile
[INFO] +- com.alibaba.middleware.jingwei:jingwei-client:jar:3.2.19-SNAPSHOT:compile
[INFO] |  +- com.alibaba.middleware.jingwei:jingwei-common:jar:3.2.19-SNAPSHOT:compile
[INFO] |  |  +- com.taobao.hsf.hessian:hessian:jar:4.0.7.bugfix10:compile
[INFO] |  |  +- com.alibaba.middleware.jingwei:jingwei-external-api:jar:3.2.19-SNAPSHOT:compile
[INFO] |  |  +- com.taobao.metaq.final:metaq-trace-core:jar:4.1.9.Final:compile
[INFO] |  |  +- com.alibaba.rocketmq:rocketmq-client:jar:4.2.2:compile
[INFO] |  |  |  +- com.alibaba.rocketmq:rocketmq-common:jar:4.2.2:compile
[INFO] |  |  |  +- com.alibaba.rocketmq:rocketmq-remoting:jar:4.2.2:compile
[INFO] |  |  |  |  +- io.netty:netty-all:jar:4.0.33.Final:compile
[INFO] |  |  |  |  \- io.netty:netty-tcnative:jar:linux-x86_64:1.1.33.Fork22:compile
[INFO] |  |  |  \- com.alibaba.rocketmq:rocketmq-logging:jar:4.2.2:compile
[INFO] |  |  +- com.alibaba.middleware:metrics-core-api:jar:1.7.2:compile
[INFO] |  |  +- com.taobao.diamond:diamond-client:jar:3.8.8:compile
[INFO] |  |  |  \- com.alibaba.middleware:tls-sslsocketfactory:jar:1.0.4:compile
[INFO] |  |  |     \- com.alibaba.middleware:tls-common:jar:1.0.3:compile
[INFO] |  |  +- org.apache.thrift:libthrift:jar:0.8.0:compile
[INFO] |  |  +- org.apache.commons:commons-collections4:jar:4.1:compile
[INFO] |  |  +- org.apache.zookeeper:zookeeper:jar:3.4.8:compile
[INFO] |  |  |  \- jline:jline:jar:0.9.94:compile
[INFO] |  |  +- org.apache.curator:curator-recipes:jar:2.9.1:compile
[INFO] |  |  |  \- org.apache.curator:curator-framework:jar:2.9.1:compile
[INFO] |  |  |     \- org.apache.curator:curator-client:jar:2.9.1:compile
[INFO] |  |  +- io.netty:netty:jar:3.10.6.Final:compile
[INFO] |  |  +- org.apache.httpcomponents:httpclient:jar:4.5.1:compile
[INFO] |  |  +- com.jcabi:jcabi-manifests:jar:1.1:compile
[INFO] |  |  |  \- com.jcabi:jcabi-log:jar:0.14:compile
[INFO] |  |  +- com.oracle:ojdbc14:jar:********.0:compile
[INFO] |  |  +- org.postgresql:postgresql:jar:9.4.1212:compile
[INFO] |  |  \- org.ini4j:ini4j:jar:0.5.2:compile
[INFO] |  +- com.taobao.metaq.final:metaq-client:jar:4.1.9.Final:compile
[INFO] |  |  +- com.alibaba.middleware.platform:platform-bom:pom:2.0.5-SNAPSHOT:import
[INFO] |  |  +- com.aliyun.openservices:ons-auth4client:jar:1.2.9:compile
[INFO] |  |  \- com.taobao.metaq.final:metaq-grayenv:jar:4.1.9.Final:compile
[INFO] |  \- com.taobao.eagleeye:eagleeye-core:jar:1.6.3:compile
[INFO] |     +- com.alibaba.middleware:metrics-core-impl:jar:1.6.3:compile
[INFO] |     +- com.alibaba.middleware:metrics-common:jar:1.6.3:compile
[INFO] |     +- com.alibaba.middleware:metrics-reporter:jar:1.6.3:compile
[INFO] |     +- com.alibaba.middleware:metrics-bin:jar:1.6.3:compile
[INFO] |     +- com.alibaba.middleware:metrics-integrate:jar:1.6.3:compile
[INFO] |     |  +- com.alibaba.middleware:metrics-jvm:jar:1.6.3:compile
[INFO] |     |  +- com.alibaba.middleware:metrics-os:jar:1.6.3:compile
[INFO] |     |  +- com.alibaba.middleware:metrics-tomcat:jar:1.6.3:compile
[INFO] |     |  \- com.alibaba.middleware:metrics-nginx:jar:1.6.3:compile
[INFO] |     \- com.alibaba.middleware:metrics-pandolet:jar:1.6.3:compile
[INFO] +- com.taobao.dauth.sso:dauth-sso-client:jar:1.2.8.1:compile
[INFO] |  +- javax.servlet:javax.servlet-api:jar:3.0.1:compile
[INFO] |  +- com.taobao.dauth.sso:dauth-sso-common:jar:0.1.8:compile
[INFO] |  \- com.taobao.spas.sdk:spas-sdk-client:jar:1.2.8:compile
[INFO] |     \- com.taobao.spas.sdk:spas-sdk-common:jar:1.2.7:compile
[INFO] +- com.alibaba.sharing.platform:dauth-wrapper:jar:0.1.0-SNAPSHOT:compile
[INFO] |  +- com.taobao.spas.sdk:spas-sdk-service:jar:1.2.4-SNAPSHOT:compile
[INFO] |  |  \- com.taobao.spas.sdk:spas-sdk-svcbase:jar:1.2.4-SNAPSHOT:compile
[INFO] |  |     \- com.taobao.dauth.sdk:dauth-data-client:jar:0.0.1-SNAPSHOT:compile
[INFO] |  +- com.taobao.spas.sdk:spas-sdk-plugin:jar:1.2.4-SNAPSHOT:compile
[INFO] |  |  +- com.aliyun.shared:ak.sdk:jar:1.0.20:compile
[INFO] |  |  +- com.aliyun.ram:ram-auth-client:jar:1.0.3:compile
[INFO] |  |  |  +- com.google.code.gson:gson:jar:2.3.1:compile
[INFO] |  |  |  \- com.google.protobuf:protobuf-java:jar:2.4.1:compile
[INFO] |  |  +- com.aliyun:aliyun-java-sdk-ubsms-inner:jar:2.0.4:compile
[INFO] |  |  +- com.aliyun:aliyun-java-sdk-oam:jar:1.0.5-SNAPSHOT:compile
[INFO] |  |  +- com.aliyun:aliyun-java-sdk-core:jar:3.0.8:compile
[INFO] |  |  \- com.taobao.top:top-api-sdk-dev:jar:top-api-SNAPSHOT:compile
[INFO] |  +- com.alibaba.sharing.platform:dauth-client:jar:0.1.1-SNAPSHOT:compile
[INFO] |  +- com.aliyun:aliyun-java-sdk-ram:jar:2.0.7:compile
[INFO] |  \- com.alibaba.aliyunid:aliyunid.client.java:jar:1.0.3:compile
[INFO] +- com.alibaba.citrus:citrus-webx-all:jar:3.2.4-s:compile
[INFO] |  +- org.apache.commons:commons-jexl:jar:2.1.1:compile
[INFO] |  +- cglib:cglib-nodep:jar:3.1:compile
[INFO] |  +- ecs:ecs:jar:1.4.2:compile
[INFO] |  +- commons-fileupload:commons-fileupload:jar:1.3.1:compile
[INFO] |  +- org.codehaus.groovy:groovy-all:jar:2.1.9:compile
[INFO] |  +- com.alibaba:fastjson:jar:1.2.48:compile
[INFO] |  +- org.springframework:spring-core:jar:3.2.7.RELEASE:compile
[INFO] |  +- org.springframework:spring-beans:jar:3.2.7.RELEASE:compile
[INFO] |  +- org.springframework:spring-aop:jar:3.2.7.RELEASE:compile
[INFO] |  +- aopalliance:aopalliance:jar:1.0:compile
[INFO] |  +- org.springframework:spring-context:jar:3.2.7.RELEASE:compile
[INFO] |  +- org.springframework:spring-expression:jar:3.2.7.RELEASE:compile
[INFO] |  +- org.springframework:spring-web:jar:3.2.7.RELEASE:compile
[INFO] |  +- org.springframework:spring-context-support:jar:3.2.7.RELEASE:compile
[INFO] |  +- org.springframework:spring-tx:jar:3.2.7.RELEASE:compile
[INFO] |  +- org.springframework:spring-******************************
[INFO] |  +- org.springframework:spring-orm:jar:3.2.7.RELEASE:compile
[INFO] |  +- org.springframework:spring-webmvc:jar:3.2.7.RELEASE:compile
[INFO] |  \- org.slf4j:jcl-over-slf4j:jar:1.7.5:compile
[INFO] +- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- ch.qos.logback:logback-core:jar:1.1.7:compile
[INFO] +- ch.qos.logback:logback-classic:jar:1.1.7:compile
[INFO] +- com.taobao.tddl:tddl-client:jar:5.2.7-2-jingwei-2:compile
[INFO] |  +- com.taobao.tddl:tddl-matrix:jar:5.2.7-2-jingwei-2:compile
[INFO] |  |  +- com.taobao.tddl:tddl-common:jar:5.2.7-2-jingwei-2:compile
[INFO] |  |  |  \- org.jdom:jdom:jar:2.0.2:compile
[INFO] |  |  +- com.taobao.tddl:tddl-optimizer:jar:5.2.7-2-jingwei-2:compile
[INFO] |  |  |  +- com.taobao.tddl:tddl-rule:jar:5.2.7-2-jingwei-2:compile
[INFO] |  |  |  \- com.taobao.tddl:tddl-parser:jar:5.2.7-2-jingwei-2:compile
[INFO] |  |  +- com.taobao.tddl:tddl-executor:jar:5.2.7-2-jingwei-2:compile
[INFO] |  |  |  +- com.taobao.tddl:tddl-net:jar:5.2.7-2-jingwei-2:compile
[INFO] |  |  |  +- com.sleepycat:je:jar:5.0.73:compile
[INFO] |  |  |  +- org.fusesource:sigar:jar:1.6.4:compile
[INFO] |  |  |  \- com.taobao.h2database:h2:jar:1.0.0.ali.fix1:compile
[INFO] |  |  |     +- org.osgi:org.osgi.core:jar:4.2.0:compile
[INFO] |  |  |     +- org.osgi:org.osgi.enterprise:jar:4.2.0:compile
[INFO] |  |  |     \- com.vividsolutions:jts:jar:1.13:compile
[INFO] |  |  +- com.taobao.tddl:tddl-config:jar:5.2.7-2-jingwei-2:compile
[INFO] |  |  +- com.taobao.tddl:tddl-monitor:jar:5.2.7-2-jingwei-2:compile
[INFO] |  |  +- com.taobao.tddl:tddl-repo-mysql:jar:5.2.7-2-jingwei-2:compile
[INFO] |  |  |  \- com.taobao.tddl:tddl-group:jar:5.2.7-2-jingwei-2:compile
[INFO] |  |  |     +- com.taobao.tddl:tddl-atom:jar:5.2.7-2-jingwei-2:compile
[INFO] |  |  |     \- com.taobao.tddl:tddl-memcached:jar:5.2.7-2-jingwei-2:compile
[INFO] |  |  |        \- com.taobao.tddl:tmemcached:jar:2.2.6:compile
[INFO] |  |  +- com.taobao.tddl:tddl-repo-bdb:jar:5.2.7-2-jingwei-2:compile
[INFO] |  |  +- com.taobao.tddl:tddl-repo-demo:jar:5.2.7-2-jingwei-2:compile
[INFO] |  |  \- com.taobao.tddl:tddl-sequence:jar:5.2.7-2-jingwei-2:compile
[INFO] |  +- com.taobao.tddl:tddl-config-diamond:jar:5.2.7-2-jingwei-2:compile
[INFO] |  +- com.alibaba.unit.rule:unitrouter:jar:1.1.6:compile
[INFO] |  +- com.alibaba.unit:unitrouter-ext:jar:1.0.3:compile
[INFO] |  |  \- org.apache.commons:commons-lang3:jar:3.4:compile
[INFO] |  \- com.taobao.common:fulllinkstresstesting:jar:0.9.9.3:compile
[INFO] +- org.apache.ibatis:ibatis-sqlmap:jar:2.3.4.726:compile
[INFO] +- com.alibaba.platform.shared:buc.sso.client:jar:0.6.6:compile
[INFO] |  +- com.alibaba.platform.shared:buc.sso.common:jar:0.6.6:compile
[INFO] |  +- com.alibaba.platform.shared:buc.sso.api:jar:0.6.6:compile
[INFO] |  +- commons-lang:commons-lang:jar:2.6:compile
[INFO] |  \- log4j:log4j:jar:1.2.17:compile
[INFO] +- org.slf4j:slf4j-api:jar:1.7.7:compile
[INFO] +- org.slf4j:log4j-over-slf4j:jar:1.7.25:compile
[INFO] +- javabase64:javabase64:jar:1.3:compile
[INFO] +- commons-io:commons-io:jar:2.4:compile
[INFO] +- mysql:mysql-connector-java:jar:5.1.40:compile
[INFO] +- com.taobao.jm.alert:jm-alert:jar:1.0.4:compile
[INFO] |  +- com.alibaba-inc.mon:noticentersdk:jar:2.0.2:compile
[INFO] |  \- com.alibaba.toolkit.common.lang:lang:jar:1.0:compile
[INFO] +- dom4j:dom4j:jar:1.6.1:compile
[INFO] |  \- xml-apis:xml-apis:jar:1.0.b2:compile
[INFO] +- org.freemarker:freemarker:jar:2.3.21:compile
[INFO] +- com.taobao.security:security:jar:2.0-WEBX:compile
[INFO] |  +- com.alibaba.platform.shared:fasttext.all:jar:1.3-SNAPSHOT:compile
[INFO] |  |  +- com.alibaba.platform.shared:fasttext-css:jar:1.3.149:compile
[INFO] |  |  |  +- com.alibaba.external:jakarta.commons.logging:jar:0.0.0:compile
[INFO] |  |  |  +- com.alibaba.external:xml.xmlgraphics:jar:1.7:compile
[INFO] |  |  |  |  +- com.alibaba.external:xml.xmlgraphics__batik-css-1.7.jar:jar:1.7:compile
[INFO] |  |  |  |  +- com.alibaba.external:xml.xmlgraphics__batik-util-1.6-1.jar:jar:1.7:compile
[INFO] |  |  |  |  \- com.alibaba.external:xml.apis.css:jar:0.0.0:compile
[INFO] |  |  |  \- com.alibaba.external:jakarta.commons.lang:jar:2.4:compile
[INFO] |  |  +- com.alibaba.platform.shared:fasttext-psoriasis:jar:1.3.149:compile
[INFO] |  |  +- com.alibaba.platform.shared:fasttext-sec:jar:1.3.149:compile
[INFO] |  |  |  +- com.alibaba.external:misc.htmlparser:jar:0.0.0:compile
[INFO] |  |  |  +- com.alibaba.external:xml.nekohtml:jar:1.9.9:compile
[INFO] |  |  |  |  \- com.alibaba.external:xml.apis:jar:0.0.0:compile
[INFO] |  |  |  \- com.alibaba.external:xml.xerces:jar:2.8.1:compile
[INFO] |  |  |     \- com.alibaba.external:xml.commons-resolver:jar:0.0.0:compile
[INFO] |  |  +- com.alibaba.platform.shared:fasttext-segment:jar:1.3.149:compile
[INFO] |  |  \- com.alibaba.platform.shared:fasttext-utils:jar:1.3.149:compile
[INFO] |  |     \- com.alibaba.external:jakarta.oro:jar:0.0.0:compile
[INFO] |  +- commons-logging:commons-logging:jar:1.2:compile
[INFO] |  +- org.htmlparser:htmlparser:jar:1.6:compile
[INFO] |  +- oro:oro:jar:2.0.8:compile
[INFO] |  +- net.sourceforge.nekohtml:nekohtml:jar:1.9.9:compile
[INFO] |  |  \- xerces:xercesImpl:jar:2.8.1:compile
[INFO] |  +- org.apache.xmlgraphics:batik-css:jar:1.7:compile
[INFO] |  |  +- org.apache.xmlgraphics:batik-ext:jar:1.7:compile
[INFO] |  |  \- xml-apis:xml-apis-ext:jar:1.3.04:compile
[INFO] |  +- org.apache.xmlgraphics:batik-util:jar:1.7:compile
[INFO] |  +- org.w3c.css:sac:jar:1.3:compile
[INFO] |  +- com.alibaba.toolkit:toolkit-webx-request:jar:2.0:compile
[INFO] |  +- com.alibaba.common.logging:toolkit-common-logging:jar:1.0:compile
[INFO] |  +- com.alibaba.common.lang:toolkit-common-lang:jar:1.0:compile
[INFO] |  +- com.alibaba.toolkit:toolkit-service-framework:jar:1.0:compile
[INFO] |  +- commons-configuration:commons-configuration:jar:1.6:compile
[INFO] |  |  +- commons-digester:commons-digester:jar:1.8:compile
[INFO] |  |  \- commons-beanutils:commons-beanutils-core:jar:1.8.0:compile
[INFO] |  +- com.alibaba.toolkit:toolkit-common-expression:jar:1.0:compile
[INFO] |  +- com.alibaba.toolkit:toolkit-service-form:jar:1.0:compile
[INFO] |  +- com.alibaba.toolkit:toolkit-service-threadcontext:jar:1.0:compile
[INFO] |  +- com.alibaba.toolkit:toolkit-webx-turbine:jar:2.0:compile
[INFO] |  +- com.alibaba.toolkit:toolkit-common-configuration:jar:1.0:compile
[INFO] |  +- commons-jelly:commons-jelly:jar:1.0-RC1:compile
[INFO] |  |  +- commons-cli:commons-cli:jar:1.3.1:compile
[INFO] |  |  +- commons-discovery:commons-discovery:jar:20030211.213356:compile
[INFO] |  |  +- forehead:forehead:jar:1.0-beta-5:compile
[INFO] |  |  \- javax.servlet:jstl:jar:1.0.6:compile
[INFO] |  +- com.alibaba.toolkit:toolkit-service-rundata:jar:1.0:compile
[INFO] |  +- com.alibaba.toolkit:toolkit-webx-framework:jar:2.0:compile
[INFO] |  +- com.alibaba.toolkit:toolkit-service-pipeline:jar:1.0:compile
[INFO] |  \- com.alibaba.toolkit:toolkit-service-pull:jar:1.0:compile
[INFO] +- com.alibaba:druid:jar:1.0.25.2018030201:compile
[INFO] |  +- com.alibaba:jconsole:jar:1.8.0:system
[INFO] |  \- com.alibaba:tools:jar:1.8.0:system
[INFO] +- org.apache.velocity:velocity:jar:1.7:compile
[INFO] |  \- commons-collections:commons-collections:jar:3.2.2:compile
[INFO] +- com.alibaba.platform.app:buc.keycenter.shared.client:jar:1.0.8:compile
[INFO] |  \- com.alibaba.platform.app:buc.keycenter.shared.common:jar:1.0.8:compile
[INFO] |     +- joda-time:joda-time:jar:2.3:compile
[INFO] |     \- org.apache.httpcomponents:httpmime:jar:4.2.2:compile
[INFO] +- com.taobao.tbdatasource:tbdatasource:jar:2.0.4:compile
[INFO] |  +- jboss.common:jboss-common:jar:1.2.1.GA:compile
[INFO] |  +- jboss:jboss-j2ee:jar:4.2.2.GA:compile
[INFO] |  \- xom:xom:jar:1.0b3:compile
[INFO] |     +- xerces:xmlParserAPIs:jar:2.6.1:compile
[INFO] |     +- com.ibm.icu:icu4j:jar:3.4.4:compile
[INFO] |     +- xalan:xalan:jar:2.6.0:compile
[INFO] |     \- org.ccil.cowan.tagsoup:tagsoup:jar:0.9.7:compile
[INFO] +- com.taobao.vipserver:vipserver-client:jar:4.6.4:compile
[INFO] |  +- com.taobao.middleware:logger.api:jar:0.2.2:compile
[INFO] |  \- com.alibaba.middleware:ushura:jar:1.0-SNAPSHOT:compile
[INFO] +- com.alibaba.aops:aops-common:jar:1.1:compile
[INFO] |  +- commons-net:commons-net:jar:3.1:compile
[INFO] |  +- commons-beanutils:commons-beanutils:jar:1.8.2:compile
[INFO] |  +- commons-httpclient:commons-httpclient:jar:3.1:compile
[INFO] |  +- org.quartz-scheduler:quartz-jobs:jar:2.2.1:compile
[INFO] |  +- org.apache.poi:poi-scratchpad:jar:3.8:compile
[INFO] |  |  \- org.apache.poi:poi:jar:3.8:compile
[INFO] |  +- org.apache.poi:poi-ooxml-schemas:jar:3.8:compile
[INFO] |  |  \- org.apache.xmlbeans:xmlbeans:jar:2.3.0:compile
[INFO] |  |     \- stax:stax-api:jar:1.0.1:compile
[INFO] |  \- org.apache.poi:poi-ooxml:jar:3.8:compile
[INFO] +- com.doctusoft:json-schema-java7:jar:1.4.1:compile
[INFO] |  +- org.json:json:jar:20160810:compile
[INFO] |  +- com.google.guava:guava:jar:18.0:compile
[INFO] |  +- org.threeten:threetenbp:jar:1.3.3:compile
[INFO] |  \- commons-validator:commons-validator:jar:1.5.1:compile
[INFO] +- junit:junit:jar:4.11:test
[INFO] |  \- org.hamcrest:hamcrest-core:jar:1.3:compile
[INFO] +- org.springframework:spring-test:jar:3.2.7.RELEASE:test
[INFO] +- com.alibaba.middleware:healthcheck-api:jar:1.0-SNAPSHOT:compile
[INFO] +- com.alibaba.middleware.jingwei:jingwei-butler:jar:3.2.19-SNAPSHOT:compile
[INFO] |  \- com.alibaba:butler-api-sdk:jar:2.0.1-SNAPSHOT:compile
[INFO] |     +- com.alibaba:liberate-common:jar:1.0.2:compile
[INFO] |     |  +- net.sf.dozer:dozer:jar:5.5.1:compile
[INFO] |     |  +- javax.el:javax.el-api:jar:2.2.4:compile
[INFO] |     |  \- org.hibernate:hibernate-validator:jar:5.1.3.Final:compile
[INFO] |     |     +- javax.validation:validation-api:jar:1.1.0.Final:compile
[INFO] |     |     +- org.jboss.logging:jboss-logging:jar:3.1.3.GA:compile
[INFO] |     |     \- com.fasterxml:classmate:jar:1.0.0:compile
[INFO] |     +- com.alibaba:liberate-rest-client:jar:1.0.2:compile
[INFO] |     +- com.alibaba:butler-common:jar:2.0.1-SNAPSHOT:compile
[INFO] |     |  +- com.alibaba:liberate-rest:jar:1.0.2:compile
[INFO] |     |  +- org.jodd:jodd:jar:3.3.8:compile
[INFO] |     |  +- com.sun.mail:javax.mail:jar:1.5.0:compile
[INFO] |     |  |  \- javax.activation:activation:jar:1.1:compile
[INFO] |     |  +- org.springframework:spring-aspects:jar:4.1.4.RELEASE:compile
[INFO] |     |  |  \- org.aspectj:aspectjweaver:jar:1.8.4:compile
[INFO] |     |  \- ch.ethz.ganymed:ganymed-ssh2:jar:262:compile
[INFO] |     \- com.github.docker-java:docker-java:jar:3.0.14:compile
[INFO] |        +- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:jar:2.6.4:compile
[INFO] |        |  +- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:jar:2.6.4:compile
[INFO] |        |  +- com.fasterxml.jackson.core:jackson-core:jar:2.6.4:compile
[INFO] |        |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.6.4:compile
[INFO] |        |  |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.6.0:compile
[INFO] |        |  \- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:jar:2.6.4:compile
[INFO] |        +- org.glassfish.jersey.connectors:jersey-apache-connector:jar:2.23.1:compile
[INFO] |        |  +- org.glassfish.jersey.core:jersey-common:jar:2.23.1:compile
[INFO] |        |  |  +- javax.annotation:javax.annotation-api:jar:1.2:compile
[INFO] |        |  |  +- org.glassfish.jersey.bundles.repackaged:jersey-guava:jar:2.23.1:compile
[INFO] |        |  |  \- org.glassfish.hk2:osgi-resource-locator:jar:1.0.1:compile
[INFO] |        |  \- javax.ws.rs:javax.ws.rs-api:jar:2.0.1:compile
[INFO] |        +- org.glassfish.jersey.core:jersey-client:jar:2.23.1:compile
[INFO] |        |  +- org.glassfish.hk2:hk2-api:jar:2.4.0-b34:compile
[INFO] |        |  |  +- org.glassfish.hk2:hk2-utils:jar:2.4.0-b34:compile
[INFO] |        |  |  \- org.glassfish.hk2.external:aopalliance-repackaged:jar:2.4.0-b34:compile
[INFO] |        |  +- org.glassfish.hk2.external:javax.inject:jar:2.4.0-b34:compile
[INFO] |        |  \- org.glassfish.hk2:hk2-locator:jar:2.4.0-b34:compile
[INFO] |        |     \- org.javassist:javassist:jar:3.18.1-GA:compile
[INFO] |        +- com.kohlschutter.junixsocket:junixsocket-common:jar:2.0.4:compile
[INFO] |        +- com.kohlschutter.junixsocket:junixsocket-native-common:jar:2.0.4:compile
[INFO] |        |  \- org.scijava:native-lib-loader:jar:2.0.2:compile
[INFO] |        +- org.bouncycastle:bcpkix-jdk15on:jar:1.54:compile
[INFO] |        |  \- org.bouncycastle:bcprov-jdk15on:jar:1.54:compile
[INFO] |        +- io.netty:netty-codec-http:jar:4.1.11.Final:compile
[INFO] |        |  \- io.netty:netty-codec:jar:4.1.11.Final:compile
[INFO] |        +- io.netty:netty-handler:jar:4.1.11.Final:compile
[INFO] |        |  +- io.netty:netty-buffer:jar:4.1.11.Final:compile
[INFO] |        |  \- io.netty:netty-transport:jar:4.1.11.Final:compile
[INFO] |        |     \- io.netty:netty-resolver:jar:4.1.11.Final:compile
[INFO] |        +- io.netty:netty-handler-proxy:jar:4.1.11.Final:compile
[INFO] |        |  \- io.netty:netty-codec-socks:jar:4.1.11.Final:compile
[INFO] |        +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.11.Final:compile
[INFO] |        |  +- io.netty:netty-common:jar:4.1.11.Final:compile
[INFO] |        |  \- io.netty:netty-transport-native-unix-common:jar:4.1.11.Final:compile
[INFO] |        \- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.11.Final:compile
[INFO] +- org.apache.commons:commons-compress:jar:1.15:compile
[INFO] |  \- org.objenesis:objenesis:jar:2.6:compile
[INFO] +- org.quartz-scheduler:quartz:jar:2.2.1:compile
[INFO] |  \- c3p0:c3p0:jar:0.9.1.1:compile
[INFO] +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] +- com.alibaba.goc:goc-basic-service-client:jar:1.0-SNAPSHOT:compile
[INFO] |  +- com.alibaba.goc:goc-basic-service-common:jar:1.0-SNAPSHOT:compile
[INFO] |  |  \- com.alibaba:hr.masterdata.client:jar:2.0.2:compile
[INFO] |  +- javax.persistence:persistence-api:jar:1.0.2:compile
[INFO] |  +- com.alibaba.goc:goc-common:jar:0.3-SNAPSHOT:compile
[INFO] |  +- com.alibaba.goc:goc-app-pojo:jar:0.3-SNAPSHOT:compile
[INFO] |  +- com.alibaba.goc:goc-organization-pojo:jar:1.0.0-SNAPSHOT:compile
[INFO] |  \- org.logback-extensions:logback-ext-spring:jar:0.1.5:compile
[INFO] +- com.taobao.diamond:diamond-sdk:jar:2.4.11-SNAPSHOT:compile
[INFO] |  +- com.taobao.diamond:diamond-utils:jar:3.2.7-SNAPSHOT:compile
[INFO] |  |  \- org.codehaus.jackson:jackson-mapper-lgpl:jar:1.9.6:compile
[INFO] |  |     \- org.codehaus.jackson:jackson-core-lgpl:jar:1.9.6:compile
[INFO] |  \- org.apache.httpcomponents:httpasyncclient:jar:4.1.3:compile
[INFO] |     +- org.apache.httpcomponents:httpcore:jar:4.4.4:compile
[INFO] |     \- org.apache.httpcomponents:httpcore-nio:jar:4.4.6:compile
[INFO] +- com.aliyun.opensearch:aliyun-sdk-opensearch:jar:3.1.5:compile
[INFO] \- org.projectlombok:lombok:jar:1.18.8:compile
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time: 1.150 s
[INFO] Finished at: 2019-06-24T11:37:12+08:00
[INFO] Final Memory: 26M/619M
[INFO] ------------------------------------------------------------------------
