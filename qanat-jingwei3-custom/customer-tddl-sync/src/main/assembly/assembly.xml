<?xml version="1.0" encoding="UTF-8"?>
<assembly>
    <formats>
        <format>tar.gz</format>
    </formats>
    <fileSets>
        <fileSet>
            <directory>src/main/resources</directory>
            <outputDirectory>/conf</outputDirectory>
            <includes>
                <include>**/*.xml</include>
                <include>**/*.properties</include>
                <include>**/*.MC</include>
                <include>**/*.ini</include>
            </includes>
            <filtered>true</filtered>
        </fileSet>
    </fileSets>
    <dependencySets>
        <dependencySet>
            <unpack>false</unpack>
            <excludes>
                <exclude>com.alibaba.middleware.jingwei:jingwei-core</exclude>
                <exclude>com.alibaba.middleware.jingwei:jingwei-tddl3</exclude>
            </excludes>
            <outputDirectory>/lib</outputDirectory>
            <useProjectArtifact>false</useProjectArtifact><!-- lib里面不包含当前project打包成的jar -->
            <useTransitiveFiltering>true</useTransitiveFiltering><!-- 只打包直接依赖的jar -->
        </dependencySet>
        <dependencySet>
            <unpack>false</unpack>
            <includes>
                <include>
                    <!-- 对应pom.xml里 groupId:artifactId，pom.xml修改了，这里也要同步修改 -->
                    com.aliyun.wormhole:customer-tddl-sync
                </include>
            </includes>
            <outputDirectory></outputDirectory>
        </dependencySet>
    </dependencySets>
</assembly>