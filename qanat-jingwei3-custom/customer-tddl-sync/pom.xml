<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.aliyun.wormhole</groupId>
        <!--建议使用表示您业务的名称-->
        <artifactId>qanat-jingwei3-custom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <!--一个tar包绑定一个服务或者任务，建议使用服务（任务）名当做artifactId-->
    <artifactId>customer-tddl-sync</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.60</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.middleware.jingwei</groupId>
            <artifactId>jingwei-external-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.middleware.jingwei</groupId>
            <artifactId>jingwei-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.middleware.jingwei</groupId>
            <artifactId>jingwei-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.middleware.jingwei</groupId>
            <artifactId>jingwei-common</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.diamond</groupId>
            <artifactId>diamond-client</artifactId>

        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
	    <dependency>
		    <groupId>com.taobao.metaq.final</groupId>
		    <artifactId>metaq-client</artifactId>
		    <version>4.1.6.Final</version>
		</dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <descriptors>
                                <descriptor>src/main/assembly/assembly.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>