<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.aliyun.wormhole</groupId>
	<artifactId>qanat-jingwei3-custom</artifactId>
	<packaging>pom</packaging>
	<version>1.0.0-SNAPSHOT</version>
	<modules>
		<module>customer-tddl-sync</module>
	</modules>
	<properties>
		<jingwei.version>3.2.18</jingwei.version>
		<logback.version>1.1.7</logback.version>
		<logback.version>1.1.7</logback.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.alibaba.middleware.jingwei</groupId>
				<artifactId>jingwei-external-api</artifactId>
				<version>${jingwei.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.middleware.jingwei</groupId>
				<artifactId>jingwei-core</artifactId>
				<version>${jingwei.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.middleware.jingwei</groupId>
				<artifactId>jingwei-client</artifactId>
				<version>${jingwei.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.middleware.jingwei</groupId>
				<artifactId>jingwei-common</artifactId>
				<version>${jingwei.version}</version>
			</dependency>
			<dependency>
				<groupId>ch.qos.logback</groupId>
				<artifactId>logback-core</artifactId>
				<version>${logback.version}</version>
			</dependency>
			<dependency>
				<groupId>com.taobao.diamond</groupId>
				<artifactId>diamond-client</artifactId>
				<version>3.8.4</version>
			</dependency>
			<dependency>
				<groupId>ch.qos.logback</groupId>
				<artifactId>logback-core</artifactId>
				<version>${logback.version}</version>
			</dependency>
			<dependency>
				<groupId>ch.qos.logback</groupId>
				<artifactId>logback-classic</artifactId>
				<version>${logback.version}</version>
			</dependency>
		</dependencies>

	</dependencyManagement>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>2.3.2</version>
				<configuration>
					<source>1.7</source>
					<target>1.7</target>
					<encoding>utf-8</encoding>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>