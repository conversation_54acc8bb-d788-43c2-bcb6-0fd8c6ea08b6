<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.aliyun.wormhole</groupId>
    <artifactId>qanat-aliyun-inc-com</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <groupId>com.aliyun.wormhole</groupId>
  <artifactId>qanat-openapi</artifactId>
  <version>1.0.7</version>
  <name>qanat-openapi</name>
  <url>http://maven.apache.org</url>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
        <groupId>com.github.pagehelper</groupId>
        <artifactId>pagehelper</artifactId>
        <version>5.0.0</version>
    </dependency>
  </dependencies>
  <distributionManagement>
	<repository>
		<id>releases</id>
		<url>http://mvnrepo.alibaba-inc.com/mvn/releases</url>
	</repository>
	<snapshotRepository>
		<id>snapshots</id>
		<url>http://mvnrepo.alibaba-inc.com/mvn/snapshots</url>
	</snapshotRepository>
</distributionManagement>
</project>
