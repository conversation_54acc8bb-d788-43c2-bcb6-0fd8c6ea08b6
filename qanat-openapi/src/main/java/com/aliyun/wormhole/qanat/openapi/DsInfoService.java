package com.aliyun.wormhole.qanat.openapi;

import com.aliyun.wormhole.qanat.openapi.model.ApiResult;
import com.aliyun.wormhole.qanat.openapi.model.FieldInfoDTO;
import com.aliyun.wormhole.qanat.openapi.model.FieldInfoQeuryRequest;
import com.aliyun.wormhole.qanat.openapi.model.DsInfoDTO;
import com.aliyun.wormhole.qanat.openapi.model.DsInfoQeuryRequest;
import com.github.pagehelper.PageInfo;

public interface DsInfoService {

	ApiResult<PageInfo<DsInfoDTO>> list4Page(DsInfoQeuryRequest request);

	ApiResult<DsInfoDTO> getTableNameByObject(DsInfoQeuryRequest request);

	ApiResult<PageInfo<FieldInfoDTO>> listDsFields4Page(FieldInfoQeuryRequest request);
}
