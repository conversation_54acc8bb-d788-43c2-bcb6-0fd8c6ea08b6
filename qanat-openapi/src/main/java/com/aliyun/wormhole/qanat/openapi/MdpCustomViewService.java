package com.aliyun.wormhole.qanat.openapi;

import com.aliyun.wormhole.qanat.openapi.model.CustomViewRequest;
import com.aliyun.wormhole.qanat.openapi.model.ApiResult;
import com.aliyun.wormhole.qanat.openapi.model.DomainViewRequest;

public interface MdpCustomViewService {

    ApiResult<String> buildCustomOdpsView(CustomViewRequest request);

    ApiResult<String> buildDomainOdpsView(DomainViewRequest request);

    ApiResult<String> getYamlFromDomain(DomainViewRequest request);
}
