/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.adb30;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.types.Row;

import com.alibaba.blink.streaming.connector.hbase.utils.ByteSerializer;
import com.alibaba.blink.streaming.connectors.common.datatype.DataType;
import com.alibaba.blink.streaming.connectors.common.errcode.ConnectorErrors;
import com.alibaba.blink.streaming.connectors.common.exception.BlinkRuntimeException;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class JdbcUtils {

    public static String getDbName(String spec) throws MalformedURLException {
        String[] arr = spec.split("//", 2);
        URL url = new URL("http://" + arr[1]);
        String[] tmpUrls = url.getPath().split("/");
        return tmpUrls[tmpUrls.length - 1];
    }

    public static String getInsertSql(RowTypeInfo columns, String table) {
        return getInsertLikeSqlWithPrefix("INSERT", columns, table);
    }

    public static String getUpsertSql(RowTypeInfo columns, String table) {
        return getInsertLikeSqlWithPrefix("UPSERT", columns, table);
    }

    public static String getReplaceSql(RowTypeInfo columns, String table) {
        return getInsertLikeSqlWithPrefix("REPLACE", columns, table);
    }

    private static String getInsertLikeSqlWithPrefix(String prefix, RowTypeInfo columns, String table) {
        String[] columnList = columns.getFieldNames();
        String[] questionMark;
        questionMark = new String[columnList.length];
        for (int j = 0; j < columnList.length; j++) {
            questionMark[j] = "?";
        }
        StringBuilder sb = new StringBuilder();
        sb.append(prefix).append(" INTO ").append(table).append(" (");
        sb.append(StringUtils.join(columnList, ","));
        sb.append(") values (");
        sb.append(StringUtils.join(questionMark, ","));
        sb.append(")");
        return sb.toString();
    }

    public static String getDeleteSql(List<String> primaryKeys, String table) {
        return getDeleteSql(primaryKeys, table, false);
    }

    public static String getPGDeleteSql(List<String> primaryKeys, String table) {
        String[] condition = new String[primaryKeys.size()];
        StringBuilder sb = new StringBuilder();
        sb.append("DELETE FROM ").append(table).append(" WHERE");
        for (int i = 0; i < condition.length; i++) {
            condition[i] = " \"" + primaryKeys.get(i) + "\" = ? ";
        }
        sb.append(StringUtils.join(condition, "AND"));
        return sb.toString();
    }

    public static String getSqlServerDeleteSql(List<String> primaryKeys, String table) {
        String[] condition = new String[primaryKeys.size()];
        StringBuilder sb = new StringBuilder();
        sb.append("DELETE FROM ").append(table).append(" WHERE");
        for (int i = 0; i < condition.length; i++) {
            condition[i] = " [" + primaryKeys.get(i) + "] = ? ";
        }
        sb.append(StringUtils.join(condition, "AND"));
        return sb.toString();
    }

    public static String getDeleteSql(List<String> primaryKeys, String table, boolean includeBackQuote) {
        String[] condition = new String[primaryKeys.size()];
        StringBuilder sb = new StringBuilder();
        sb.append("DELETE FROM ").append(table).append(" WHERE");
        for (int i = 0; i < condition.length; i++) {
            if (!includeBackQuote) {
                condition[i] = " " + primaryKeys.get(i) + " = ? ";
            } else {
                condition[i] = " `" + primaryKeys.get(i) + "` = ? ";
            }
        }
        sb.append(StringUtils.join(condition, "AND"));
        //		sb.append(" LIMIT 1");
        return sb.toString();
    }

    public static String getPGDuplicateUpdateSql(
        RowTypeInfo columns, List<String> excludeKeys, String table,
        List<String> pkFields) {

        String[] columnList = columns.getFieldNames();
        String[] colnames;
        String[] questionMark;
        String[] equalsExpression;
        String[] pkFieldWithDQM = new String[pkFields.size()]; // DQM means double quotation marks (“ ”)
        colnames = new String[columnList.length];
        questionMark = new String[columnList.length];
        if (null != excludeKeys && excludeKeys.size() != 0) {
            equalsExpression = new String[columnList.length - excludeKeys.size()];
        } else {
            equalsExpression = new String[columnList.length];
        }

        int i = 0;
        for (int j = 0; j < columnList.length; j++) {
            colnames[j] = "\"" + columnList[j] + "\"";
            questionMark[j] = "?";
            if (null == excludeKeys || !excludeKeys.contains(columnList[j])) {
                equalsExpression[i] = colnames[j] + "= EXCLUDED." + colnames[j] + "";
                i++;
            }
        }
        int k = 0;
        for (String pkField : pkFields) {
            pkFieldWithDQM[k++] = "\"" + pkField + "\"";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("INSERT INTO \"").append(table).append("\" (");
        sb.append(StringUtils.join(colnames, ","));
        sb.append(") values (");
        sb.append(StringUtils.join(questionMark, ","));
        sb.append(") ON CONFLICT (");
        sb.append(StringUtils.join(pkFieldWithDQM, ","));
        sb.append(") DO UPDATE SET ");
        sb.append(StringUtils.join(equalsExpression, ","));
        return sb.toString();
    }

    public static String getSqlServerDuplicateUpdateSql(
        RowTypeInfo columns,
        List<String> excludeKeys,
        String table,
        List<String> pkFields) {
        String[] columnList = columns.getFieldNames();
        String[] colnames = new String[columnList.length];
        String[] questionMark = new String[columnList.length];
        String[] mergeCondition = new String[pkFields.size()];
        String[] equalsExpression = new String[columnList.length - pkFields.size()];

        int j = 0, k = 0;
        for (int i = 0; i < columnList.length; i++) {
            colnames[i] = "[" + columnList[i] + "]";
            questionMark[i] = "?";
            if (!excludeKeys.contains(columnList[i])) {
                equalsExpression[j++] = "target." + colnames[i] + " = source." + colnames[i];
            }
            if (pkFields.contains(columnList[i])) {
                mergeCondition[k++] = "target." + colnames[i] + " = source." + colnames[i];
            }
        }

        StringBuilder sb = new StringBuilder();
        sb.append("MERGE INTO ").append(table).append(" WITH (PAGLOCK,UPDLOCK) AS target ");
        sb.append("USING (VALUES (");
        sb.append(StringUtils.join(questionMark, ","));
        sb.append(")) AS source (").append(StringUtils.join(colnames, ","));
        sb.append(") ON (").append(StringUtils.join(mergeCondition, " AND "));
        sb.append(") WHEN MATCHED THEN UPDATE SET ");
        sb.append(StringUtils.join(equalsExpression, ","));
        sb.append(" WHEN NOT MATCHED THEN INSERT ");
        sb.append("VALUES (").append(StringUtils.join(colnames, ",")).append(");");
        return sb.toString();
    }

    public static String getDuplicateUpdateSql(RowTypeInfo columns, List<String> excludeKeys, String table) {
        return getDuplicateUpdateSql(columns, excludeKeys, table, true);
    }

    public static String getDuplicateUpdateSql(
        RowTypeInfo columns, List<String> excludeKeys, String table, Boolean
        includeBackQuote) {
        String[] columnList = columns.getFieldNames();
        String[] colnames;
        String[] questionMark;
        String[] equalsExpression;
        colnames = new String[columnList.length];
        questionMark = new String[columnList.length];
        if (null != excludeKeys && excludeKeys.size() != 0) {
            equalsExpression = new String[columnList.length - excludeKeys.size()];
        } else {
            equalsExpression = new String[columnList.length];
        }

        int i = 0;
        for (int j = 0; j < columnList.length; j++) {
            colnames[j] = includeBackQuote ? "`" + columnList[j] + "`" : "" + columnList[j] + "";
            questionMark[j] = "?";
            if (null == excludeKeys || !excludeKeys.contains(columnList[j])) {
                equalsExpression[i] = colnames[j] + "= values(" + colnames[j] + ")";
                i++;
            }
        }
        StringBuilder sb = new StringBuilder();
        if (includeBackQuote) {
            sb.append("INSERT INTO `").append(table).append("` (");
        } else {
            sb.append("INSERT INTO ").append(table).append(" (");
        }
        sb.append(StringUtils.join(colnames, ","));
        sb.append(") values (");
        sb.append(StringUtils.join(questionMark, ","));
        sb.append(") ON DUPLICATE KEY UPDATE ");
        sb.append(StringUtils.join(equalsExpression, ","));
        return sb.toString();
    }

    public static void setStatementData(PreparedStatement statement, int index, Object obj) throws SQLException {
        if (obj == null) {
            statement.setObject(index, null);
        } else if (obj instanceof Integer) {
            statement.setInt(index, (Integer)obj);
        } else if (obj instanceof BigDecimal) {
            statement.setBigDecimal(index, (BigDecimal)obj);
        } else if (obj instanceof String) {
            statement.setString(index, obj.toString());
        } else if (obj instanceof Boolean) {
            statement.setBoolean(index, (Boolean)obj);
        } else if (obj instanceof Double) {
            statement.setDouble(index, (Double)obj);
        } else if (obj instanceof Float) {
            statement.setFloat(index, (Float)obj);
        } else if (obj instanceof Long) {
            statement.setLong(index, (Long)obj);
        } else if (obj instanceof Short) {
            statement.setShort(index, (Short)obj);
        } else if (obj instanceof Date) {
            statement.setDate(index, (Date)obj);
        } else if (obj instanceof Time) {
            statement.setTime(index, (Time)obj);
        } else if (obj instanceof Timestamp) {
            statement.setTimestamp(index, (Timestamp)obj);
        } else if (obj instanceof Byte) {
            statement.setByte(index, (Byte)obj);
        } else if (obj instanceof byte[]) {
            statement.setBytes(index, (byte[])obj);
        } else {
            throw new BlinkRuntimeException(
                ConnectorErrors.INST
                    .jdbcUtilsDatatypeUncompatiableError(Integer.toString(index), obj.getClass().toString()));
        }
    }

    public static void setReplaceStatement(
        PreparedStatement statement,
        Row row,
        RowTypeInfo columns) throws SQLException {
        if (row != null) {
            if (row.getArity() == columns.getArity()) {
                int index = 1;
                for (int i = 0; i < row.getArity(); i++) {
                    Object obj = row.getField(i);
                    setStatementData(statement, index, obj);
                    index++;
                }
            } else {
                throw new BlinkRuntimeException(ConnectorErrors.INST.jdbcUtilsColumnsMismatchError(
                    Integer.toString(row.getArity()), Integer.toString(columns.getArity())));
            }
        }
    }

    public static void setUpdateStatement(
        PreparedStatement statement,
        Row row,
        RowTypeInfo columns) throws SQLException {
        setReplaceStatement(statement, row, columns);
    }

    public static void setDeleteStatement(
        PreparedStatement statement,
        Row row,
        RowTypeInfo columns,
        List<String> primaryKeys) throws SQLException {
        if (row != null) {
            if (row.getArity() == columns.getArity()) {
                for (int i = 0; i < primaryKeys.size(); i++) {
                    Object obj = row.getField(columns.getFieldIndex(primaryKeys.get(i)));
                    int index = i + 1;
                    setStatementData(statement, index, obj);
                }
            } else {
                throw new BlinkRuntimeException(ConnectorErrors.INST.jdbcUtilsColumnsMismatchError(
                    Integer.toString(row.getArity()), Integer.toString(columns.getArity())));
            }
        }
    }

    public static String toMysqlField(Object o) {
        if (null == o) {
            return "null";
        }
        String str = o.toString();
        if (str.indexOf("\\") >= 0) {
            str = str.replaceAll("\\\\", "\\\\\\\\");
        }
        if (str.indexOf("'") >= 0) {
            str = str.replaceAll("'", "\\\\'");
        }
        if (str.indexOf("\"") >= 0) {
            str = str.replaceAll("\"", "\\\\\"");
        }
        return "'" + str + "'";
    }

    public static String toPostgreSqlField(Object o) {
        if (null == o) {
            return "null";
        }
        String str = o.toString();
        if (str.indexOf("\\") >= 0) {
            str = str.replaceAll("\\\\", "\\\\\\\\");
        }
        if (str.indexOf("'") >= 0) {
            str = str.replaceAll("'", "''");
        }
        if (str.indexOf("\"") >= 0) {
            str = str.replaceAll("\"", "\\\\\"");
        }
        return "'" + str + "'";
    }

    public static String[] writeFormat(RowTypeInfo rowTypeInfo, Row row, boolean reserveMs) {
        return writeFormat(rowTypeInfo, row, null, reserveMs, null);
    }

    public static String[] writeFormat(RowTypeInfo rowTypeInfo, Row row, String timeZone, boolean reserveMs, String dbType) {
        List<String> output = new ArrayList<>();
        String[] fieldNames = rowTypeInfo.getFieldNames();
        TypeInformation[] typeInformations = rowTypeInfo.getFieldTypes();
        for (int i = 0; i < fieldNames.length; i++) {
            if (fieldNames[i].equalsIgnoreCase("__trace_id__")) {
                continue;
            }
            Object value = row.getField(rowTypeInfo.getFieldIndex(fieldNames[i]));
            ByteSerializer.ValueType colType = ByteSerializer.getTypeIndex(
                rowTypeInfo.getTypeAt(i).getTypeClass());
            try {
                if (value == null) {
                    output.add("null");
                    continue;
                } else {
                    switch (colType) {
                        case V_String:
                            value = "hologres".equalsIgnoreCase(dbType) ? toPostgreSqlField(value) : toMysqlField(value);
                            break;
                        case V_Byte:
                            value = "" + ((Byte)value).intValue();
                            break;
                        case V_ByteArray: // byte[]
                            value = new String((byte[])value);
                            break;
                        case V_Short:
                        case V_Integer:
                        case V_Long:
                        case V_BigInteger:
                            value = Long.parseLong("" + value);
                            break;
                        case V_BigDecimal:
                        case V_Float:
                        case V_Double:
                            value = Double.parseDouble("" + value);
                            break;
                        case V_Boolean:
                            value = (Boolean)value ? '1' : '0';
                            break;
                        case V_Timestamp:
                            value = "'" + DateUtil.timeStamp2String((Timestamp)value, timeZone, reserveMs) + "'";
                            break;
                        case V_Date:
                            value = "'" + DateUtil.date2String((Date)value, timeZone) + "'";
                            break;
                        case V_Time:
                            value = "'" + DateUtil.time2String((Time)value, timeZone) + "'";
                            break;
                        default:
                            throw new IllegalArgumentException(
                                ConnectorErrors.INST.dataTypeError(String.valueOf(colType)));
                    }
                }
                output.add("" + value);
            } catch (Exception e) {
                throw new RuntimeException("", e);
            }
        }
        String [] outputs = new String[output.size()];
        return output.toArray(outputs);
    }

    public static Map<String, String> getFieldValueMap(RowTypeInfo rowTypeInfo, Row row, String timeZone, boolean reserveMs) {
        Map<String, String> output = new HashMap<>();
        String[] fieldNames = rowTypeInfo.getFieldNames();
        TypeInformation[] typeInformations = rowTypeInfo.getFieldTypes();
        for (int i = 0; i < fieldNames.length; i++) {
            if (fieldNames[i].equalsIgnoreCase("__trace_id__")) {
                continue;
            }
            Object value = row.getField(rowTypeInfo.getFieldIndex(fieldNames[i]));
            ByteSerializer.ValueType colType = ByteSerializer.getTypeIndex(
                rowTypeInfo.getTypeAt(i).getTypeClass());
            try {
                if (value == null) {
                	output.put(fieldNames[i], "null");
                    continue;
                } else {
                    switch (colType) {
                        case V_String:
                            value = toMysqlField(value);
                            break;
                        case V_Byte:
                            value = "" + ((Byte)value).intValue();
                            break;
                        case V_ByteArray: // byte[]
                            value = new String((byte[])value);
                            break;
                        case V_Short:
                        case V_Integer:
                        case V_Long:
                        case V_BigInteger:
                            value = Long.parseLong("" + value);
                            break;
                        case V_BigDecimal:
                        case V_Float:
                        case V_Double:
                            value = Double.parseDouble("" + value);
                            break;
                        case V_Boolean:
                            value = (Boolean)value ? '1' : '0';
                            break;
                        case V_Timestamp:
                            value = "'" + DateUtil.timeStamp2String((Timestamp)value, timeZone, reserveMs) + "'";
                            break;
                        case V_Date:
                            value = "'" + DateUtil.date2String((Date)value, timeZone) + "'";
                            break;
                        case V_Time:
                            value = "'" + DateUtil.time2String((Time)value, timeZone) + "'";
                            break;
                        default:
                            throw new IllegalArgumentException(
                                ConnectorErrors.INST.dataTypeError(String.valueOf(colType)));
                    }
                }
                output.put(fieldNames[i], "" + value);
            } catch (Exception e) {
                throw new RuntimeException("", e);
            }
        }
        return output;
    }

    public static Object[] deleteFormat(RowTypeInfo rowTypeInfo, Row row, Set<String> primaryKeys, boolean reserveMs) {
        return deleteFormat(rowTypeInfo, row, primaryKeys, null, reserveMs);
    }

    public static Object[] deleteFormat(RowTypeInfo rowTypeInfo, Row row, Set<String> primaryKeys, String timeZone,
                                        boolean reserveMs) {
        Object[] output = new Object[primaryKeys.size()];
        String[] fieldNames = rowTypeInfo.getFieldNames();
        int keyIndex = 0;
        for (int i = 0; i < row.getArity(); i++) {
            String colName = fieldNames[i];
            if (!primaryKeys.contains(colName)) {
                continue;
            }

            Object value = row.getField(i);
            ByteSerializer.ValueType colType = ByteSerializer.getTypeIndex(
                rowTypeInfo.getTypeAt(i).getTypeClass());

            try {
                if (value == null) {
                    throw new RuntimeException(ConnectorErrors.INST.primaryKeyIsNullError(colName));
                } else {
                    switch (colType) {
                        case V_String:
                            value = toMysqlField(value);
                            break;
                        case V_Byte:
                            value = "" + ((Byte)value).intValue();
                            break;
                        case V_ByteArray: // byte[]
                            value = new String((byte[])value);
                            break;
                        case V_Short:
                        case V_Integer:
                        case V_Long:
                        case V_BigInteger:
                            value = Long.parseLong("" + value);
                            break;
                        case V_BigDecimal:
                        case V_Float:
                        case V_Double:
                            value = Double.parseDouble("" + value);
                            break;
                        case V_Boolean:
                            value = (Boolean)value == true ? '1' : '0';
                            break;
                        case V_Timestamp:
                            value = "'" + DateUtil.timeStamp2String((Timestamp)value, timeZone, reserveMs) + "'";
                            break;
                        case V_Date:
                            value = "'" + DateUtil.date2String((Date)value, timeZone) + "'";
                            break;
                        case V_Time:
                            value = "'" + DateUtil.time2String((Time)value, timeZone) + "'";
                            break;
                        default:
                            throw new IllegalArgumentException(ConnectorErrors.INST.dataTypeError(String.valueOf
                                (colType)));
                    }
                }
                output[keyIndex] = colName + " = " + value;
                keyIndex++;
            } catch (Exception e) {
                throw new RuntimeException("", e);
            }
        }

        if (keyIndex != primaryKeys.size()) {
            throw new RuntimeException(
                ConnectorErrors.INST.primaryKeyMatchError(String.valueOf(keyIndex), String.valueOf
                    (primaryKeys.size())));
        }

        return output;
    }

    public static Object[] updateWhereFormat(RowTypeInfo rowTypeInfo, Row row, Set<String> primaryKeys, String timeZone,
                                        boolean reserveMs) {
        Object[] output = new Object[primaryKeys.size()];
        String[] fieldNames = rowTypeInfo.getFieldNames();
        int keyIndex = 0;
        for (int i = 0; i < row.getArity(); i++) {
            String colName = fieldNames[i];
            if (!primaryKeys.contains(colName)) {
                continue;
            }

            Object value = row.getField(i);
            ByteSerializer.ValueType colType = ByteSerializer.getTypeIndex(
                rowTypeInfo.getTypeAt(i).getTypeClass());

            try {
                if (value == null) {
                    throw new RuntimeException(ConnectorErrors.INST.primaryKeyIsNullError(colName));
                } else {
                    switch (colType) {
                        case V_String:
                            value = toMysqlField(value);
                            break;
                        case V_Byte:
                            value = "" + ((Byte)value).intValue();
                            break;
                        case V_ByteArray: // byte[]
                            value = new String((byte[])value);
                            break;
                        case V_Short:
                        case V_Integer:
                        case V_Long:
                        case V_BigInteger:
                            value = Long.parseLong("" + value);
                            break;
                        case V_BigDecimal:
                        case V_Float:
                        case V_Double:
                            value = Double.parseDouble("" + value);
                            break;
                        case V_Boolean:
                            value = (Boolean)value == true ? '1' : '0';
                            break;
                        case V_Timestamp:
                            value = "'" + DateUtil.timeStamp2String((Timestamp)value, timeZone, reserveMs) + "'";
                            break;
                        case V_Date:
                            value = "'" + DateUtil.date2String((Date)value, timeZone) + "'";
                            break;
                        case V_Time:
                            value = "'" + DateUtil.time2String((Time)value, timeZone) + "'";
                            break;
                        default:
                            throw new IllegalArgumentException(ConnectorErrors.INST.dataTypeError(String.valueOf
                                (colType)));
                    }
                }
                output[keyIndex] = colName + " = " + value;
                keyIndex++;
            } catch (Exception e) {
                throw new RuntimeException("", e);
            }
        }

        if (keyIndex != primaryKeys.size()) {
            throw new RuntimeException(
                ConnectorErrors.INST.primaryKeyMatchError(String.valueOf(keyIndex), String.valueOf
                    (primaryKeys.size())));
        }

        return output;
    }

    public static Object[] updateSetFormat(RowTypeInfo rowTypeInfo, Row row, Set<String> primaryKeys, String timeZone,
                                        boolean reserveMs) {
        List<String> output = new ArrayList<>();
        String[] fieldNames = rowTypeInfo.getFieldNames();
        int keyIndex = 0;
        for (int i = 0; i < row.getArity(); i++) {
            String colName = fieldNames[i];
            if (primaryKeys.contains(colName) || "__trace_id__".equalsIgnoreCase(colName)) {
                continue;
            }

            Object value = row.getField(i);
            ByteSerializer.ValueType colType = ByteSerializer.getTypeIndex(
                rowTypeInfo.getTypeAt(i).getTypeClass());

            try {
                if (value == null) {
                } else {
                    switch (colType) {
                        case V_String:
                            value = toMysqlField(value);
                            break;
                        case V_Byte:
                            value = "" + ((Byte)value).intValue();
                            break;
                        case V_ByteArray: // byte[]
                            value = new String((byte[])value);
                            break;
                        case V_Short:
                        case V_Integer:
                        case V_Long:
                        case V_BigInteger:
                            value = Long.parseLong("" + value);
                            break;
                        case V_BigDecimal:
                        case V_Float:
                        case V_Double:
                            value = Double.parseDouble("" + value);
                            break;
                        case V_Boolean:
                            value = (Boolean)value == true ? '1' : '0';
                            break;
                        case V_Timestamp:
                            value = "'" + DateUtil.timeStamp2String((Timestamp)value, timeZone, reserveMs) + "'";
                            break;
                        case V_Date:
                            value = "'" + DateUtil.date2String((Date)value, timeZone) + "'";
                            break;
                        case V_Time:
                            value = "'" + DateUtil.time2String((Time)value, timeZone) + "'";
                            break;
                        default:
                            throw new IllegalArgumentException(ConnectorErrors.INST.dataTypeError(String.valueOf
                                (colType)));
                    }
                }
                output.add(colName + " = " + value);
                keyIndex++;
            } catch (Exception e) {
            	e.printStackTrace();
                throw new RuntimeException(e.getMessage(), e);
            }
        }
        String [] outputs = new String[output.size()];
        return output.toArray(outputs);
    }

    public static String constructDupKey(Row row, RowTypeInfo rowTypeInfo, List<String> pkFields) {
        String dupKey = "";
        for (String filed : pkFields) {
            dupKey += row.getField(rowTypeInfo.getFieldIndex(filed)) == null ? "null" :
                row.getField(rowTypeInfo.getFieldIndex(filed)).toString();
            dupKey = dupKey + "#"; // add # to avoid (a,c,ba) (ac,b,a) as a same key to deduplicate
        }
        return dupKey;
    }

    public static String escapeOracleColumns(String[] columns) {
        StringBuilder sb = new StringBuilder();
        for (String columnName : columns) {
            sb.append(escapeOracleColumn(columnName)).append(",");
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }

        return sb.toString();
    }

    public static String escapeOracleColumn(String column) {
        if (!column.startsWith("\"") && ORACLE_RESERVED_WORDS_SET_10G.contains(column.toUpperCase())) {
            return "\"" + column + "\"";
        }
        return column;
    }

    /**
     * Escape MySQL columns using backtick "`" (not double quote "\"" in ANSI mode).
     */
    public static String escapeMySQLColumn(String column) {
        if (!column.startsWith("`")) {
            return "`" + column + "`";
        }
        return column;
    }

    public static String resultSetToString(DataType type, ResultSet resultSet, int fieldCount, String timeZone,
                                           boolean reserveMs) {
        try {
            switch (type) {
                case ByteArray:
                    return new String(resultSet.getBytes(fieldCount));
                case String:
                    return resultSet.getString(fieldCount);
                case Byte:
                    return String.valueOf(resultSet.getByte(fieldCount));
                case Short:
                    return String.valueOf(resultSet.getShort(fieldCount));
                case Integer:
                    return String.valueOf(resultSet.getInt(fieldCount));
                case Long:
                case BigInteger:
                    return String.valueOf(resultSet.getLong(fieldCount));
                case Float:
                    return String.valueOf(resultSet.getFloat(fieldCount));
                case Double:
                    return String.valueOf(resultSet.getDouble(fieldCount));
                case Boolean:
                    return Boolean.toString(resultSet.getBoolean(fieldCount));
                case Timestamp:
                    return String.valueOf(
                        DateUtil.timeStamp2String(resultSet.getTimestamp(fieldCount), timeZone, reserveMs));
                case Time:
                    return String.valueOf(DateUtil.time2String(resultSet.getTime(fieldCount), timeZone));
                case Date:
                    return String.valueOf(DateUtil.date2String(resultSet.getDate(fieldCount), timeZone));
                case BigDecimal:
                    return String.valueOf(resultSet.getBigDecimal(fieldCount));
                default:
                    return String.valueOf(resultSet.getString(fieldCount));
            }
        } catch (Exception e) {
            throw new RuntimeException(String.format("Parse recordSet failed, DataType=%s, fieldCount=%s, timeZone=%s",
                type.toString(), fieldCount, timeZone), e);
        }
    }

    public static Object resultSetToObject(DataType type, ResultSet resultSet, int fieldCount, String timeZone) {
        try {
            switch (type) {
                case ByteArray:
                    return new String(resultSet.getBytes(fieldCount));
                case String:
                    return resultSet.getString(fieldCount);
                case Byte:
                    return String.valueOf(resultSet.getByte(fieldCount));
                case Short:
                    return resultSet.getShort(fieldCount);
                case Integer:
                    return resultSet.getInt(fieldCount);
                case Long:
                case BigInteger:
                    return resultSet.getLong(fieldCount);
                case Float:
                    return resultSet.getFloat(fieldCount);
                case Double:
                    return resultSet.getDouble(fieldCount);
                case Boolean:
                    return Boolean.toString(resultSet.getBoolean(fieldCount));
                case Timestamp:
                    return resultSet.getTimestamp(fieldCount);
                case Time:
                    return resultSet.getTime(fieldCount);
                case Date:
                    return resultSet.getDate(fieldCount);
                case BigDecimal:
                    return resultSet.getBigDecimal(fieldCount);
                default:
                    return String.valueOf(resultSet.getString(fieldCount));
            }
        } catch (Exception e) {
            throw new RuntimeException(String.format("Parse recordSet failed, DataType=%s, fieldCount=%s, timeZone=%s",
                type.toString(), fieldCount, timeZone), e);
        }
    }

    public static String escapeSqlColumn(String column) {
        return escapeSqlColumn(DEFAULT_DIALECT, column);
    }

    public static String escapeSqlColumn(String dialect, String column) {
        // Simply divide the dialect into oracle and non-oracle for now.
        // We can extend these escaping when encountering new issues.
        if (ORACLE_DIALECT.equalsIgnoreCase(dialect)) {
            return escapeOracleColumn(column);
        } else {
            return escapeMySQLColumn(column);
        }
    }

    public static final String DEFAULT_DIALECT = "mysql";
    public static final String ORACLE_DIALECT = "oracle";
    public static final Set<String> ORACLE_RESERVED_WORDS_SET_10G = new HashSet<>();

    /**
     * @see <a href="https://docs.oracle.com/cd/B19306_01/server.102/b14200/ap_keywd.htm">Reference</a>
     */
    public static final String[] ORACLE_RESERVED_WORDS_10G = {
        "ACCESS", "ADD", "ALL", "ALTER", "AND", "ANY", "AS", "ASC", "AUDIT", "BETWEEN", "BY", "CHAR", "CHECK",
        "CLUSTER", "COLUMN", "COMMENT", "COMPRESS",
        "CONNECT", "CREATE", "CURRENT", "DATE", "DECIMAL", "DEFAULT", "DELETE", "DESC", "DISTINCT", "DROP", "ELSE",
        "EXCLUSIVE", "EXISTS", "FILE", "FLOAT",
        "FOR", "FROM", "GRANT", "GROUP", "HAVING", "IDENTIFIED", "IMMEDIATE", "IN", "INCREMENT", "INDEX", "INITIAL",
        "INSERT", "INTEGER", "INTERSECT",
        "INTO", "IS", "LEVEL", "LIKE", "LOCK", "LONG", "MAXEXTENTS", "MINUS", "MLSLABEL", "MODE", "MODIFY", "NOAUDIT",
        "NOCOMPRESS", "NOT", "NOWAIT",
        "NULL", "NUMBER", "OF", "OFFLINE", "ON", "ONLINE", "OPTION", "OR", "ORDER", "PCTFREE", "PRIOR", "PRIVILEGES",
        "PUBLIC", "RAW", "RENAME", "RESOURCE",
        "REVOKE", "ROW", "ROWID", "ROWNUM", "ROWS", "SELECT", "SESSION", "SET", "SHARE", "SIZE", "SMALLINT", "START",
        "SUCCESSFUL", "SYNONYM", "SYSDATE",
        "TABLE", "THEN", "TO", "TRIGGER", "UID", "UNION", "UNIQUE", "UPDATE", "USER", "VALIDATE", "VALUES", "VARCHAR",
        "VARCHAR2", "VIEW", "WHENEVER",
        "WHERE", "WITH"};

    static {
        for (String keword : ORACLE_RESERVED_WORDS_10G) {
            ORACLE_RESERVED_WORDS_SET_10G.add(keword);
        }
    }
}
