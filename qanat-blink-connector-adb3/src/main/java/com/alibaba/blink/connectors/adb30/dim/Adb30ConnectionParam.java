/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.blink.connectors.adb30.dim;

import org.apache.commons.dbcp.BasicDataSource;

import java.io.Serializable;

import static org.apache.flink.util.Preconditions.checkNotNull;

public class Adb30ConnectionParam implements Serializable {

	private static final long serialVersionUID = -764510949279683113L;

	private String url;
	private String tableName;
	private String userName;
	private String password;

	private int maxRetryTime = 3;

	private String driverClassName = "com.mysql.jdbc.Driver";
	private int connectionMaxActive = 30;
	private int connectionInitialSize = 1;
	private int connectionMinIdle = 0;
	private boolean connectionTestWhileIdle = false;
	private int maxWait = 15000;
	private int removeAbandonedTimeout = 60 * 10;
	private int maxFetchResult = 1024;

	public Adb30ConnectionParam setMaxWait(int maxWait) {
		this.maxWait = maxWait;
		return this;
	}

	public Adb30ConnectionParam setRemoveAbandonedTimeout(int removeAbandonedTimeout) {
		this.removeAbandonedTimeout = removeAbandonedTimeout;
		return this;
	}

	public Adb30ConnectionParam setUrl(String url) {
		this.url = url;
		return this;
	}

	public Adb30ConnectionParam setTableName(String tableName) {
		this.tableName = tableName;
		return this;
	}

	public Adb30ConnectionParam setUserName(String userName) {
		this.userName = userName;
		return this;
	}

	public Adb30ConnectionParam setPassword(String password) {
		this.password = password;
		return this;
	}

	public Adb30ConnectionParam setMaxRetryTime(int maxRetryTime) {
		this.maxRetryTime = maxRetryTime;
		return this;
	}

	public int getMaxRetryTime() {
		return maxRetryTime;
	}

	public Adb30ConnectionParam setDriverClassName(String driverClassName) {
		this.driverClassName = driverClassName;
		return this;
	}

	public Adb30ConnectionParam setConnectionMaxActive(int connectionMaxActive) {
		this.connectionMaxActive = connectionMaxActive;
		return this;
	}

	public Adb30ConnectionParam setConnectionInitialSize(int connectionInitialSize) {
		this.connectionInitialSize = connectionInitialSize;
		return this;
	}

	public Adb30ConnectionParam setConnectionMinIdle(int connectionMinIdle) {
		this.connectionMinIdle = connectionMinIdle;
		return this;
	}

	public Adb30ConnectionParam setConnectionTestWhileIdle(boolean connectionTestWhileIdle) {
		this.connectionTestWhileIdle = connectionTestWhileIdle;
		return this;
	}

	public int getMaxFetchResult() {
		return maxFetchResult;
	}

	public Adb30ConnectionParam setMaxFetchResult(int maxFetchResult) {
		this.maxFetchResult = maxFetchResult;
		return this;
	}

	public String getTableName() {
		return tableName;
	}

	public BasicDataSource buildDataSource() {
		BasicDataSource ds = new BasicDataSource();
		ds.setUrl(checkNotNull(url, "url must not be null"));
		ds.setUsername(checkNotNull(userName, "username must not be null"));
		ds.setPassword(checkNotNull(password, "password must not be null"));
		ds.setDriverClassName(checkNotNull(driverClassName, "driverClassName must not be null"));
		ds.setMaxActive(connectionMaxActive);
		ds.setInitialSize(connectionInitialSize);
		ds.setMaxWait(maxWait);//默认为15s
		ds.setMinIdle(connectionMinIdle);
		ds.setTestWhileIdle(connectionTestWhileIdle);
		ds.setRemoveAbandonedTimeout(removeAbandonedTimeout); //对于可能存在连接泄露的情况，10min强制回收一次空闲的连接
		return ds;
	}
}
