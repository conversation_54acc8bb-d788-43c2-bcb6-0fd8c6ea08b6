/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.adb30;


import com.alibaba.blink.streaming.connector.hbase.utils.ByteSerializer;
import com.alibaba.blink.streaming.connectors.common.MetricUtils;
import com.alibaba.blink.streaming.connectors.common.errcode.ConnectorErrors;
import com.alibaba.blink.streaming.connectors.common.exception.BlinkRuntimeException;
import com.alibaba.blink.streaming.connectors.common.exception.ErrorUtils;
import com.alibaba.blink.streaming.connectors.common.output.HasRetryTimeout;
import com.alibaba.blink.streaming.connectors.common.output.Syncable;
import com.alibaba.blink.streaming.connectors.common.output.TupleRichOutputFormat;
import com.alibaba.blink.streaming.connectors.common.source.parse.DirtyDataStrategy;
import com.alibaba.blink.streaming.connectors.common.util.ConnectionPool;
import com.alibaba.blink.streaming.connectors.common.util.SQLExceptionSkipPolicy;
import com.alibaba.blink.streaming.connectors.common.util.TpsLimitUtils;
import com.aliyun.wormhole.qanat.stream.event.StreamEvent;
import com.aliyun.wormhole.qanat.stream.event.StreamEventField;
import com.aliyun.wormhole.qanat.stream.event.export.EventExporter;
import com.aliyun.wormhole.qanat.stream.event.export.KafkaEventExporter;

import org.apache.commons.dbcp.BasicDataSource;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.Meter;
import org.apache.flink.shaded.guava18.com.google.common.base.Joiner;
import org.apache.flink.types.Row;
import org.apache.flink.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;

public class QanatAdb30OutputFormat
		extends TupleRichOutputFormat
		implements Syncable, HasRetryTimeout {
	private transient static final Logger log = LoggerFactory.getLogger(QanatAdb30OutputFormat.class);

	private String url;
	private String tableName;
	private String userName;
	private String password;
	private RowTypeInfo rowTypeInfo;

	private List<String> pkFields = null;
	private int maxRetryTime = 3;

	private final String DELETE_WITH_KEY_SQL_TPL = "DELETE FROM %s WHERE %s ";

	private transient ScheduledExecutorService flusher;
	private volatile long lastFlushTime = 0;

	private String driverClassName = "com.mysql.jdbc.Driver";
	private String dbType = "adb3";
	private int connectionMaxActive = 5;
	private int connectionInitialSize = 1;
	private int connectionMinIdle = 0;
	private boolean connectionTestWhileIdle = true;
	private int maxWait = 60000;
	private int batchSize = 50;
	private int bufferSize = 500;
	private List<String> exceptUpdateKeys = new ArrayList<>();
	private long flushIntervalMs = 5000;
	private long currentCount = 0;
	private long maxSinkTps = -1;
	private int numTasks = 1;
	private Map<String, Tuple2<Boolean, Row>> mapReduceBuffer = new HashMap<>();
	private List<Tuple2<Boolean, Row>> mapReduceBufferWithoutPk = new ArrayList<>();

	private transient BasicDataSource dataSource;
	private static ConnectionPool<BasicDataSource> dataSourcePool = new ConnectionPool<>();
	private String dataSourceKey = "";

	private String fieldNames = null;

	private boolean existsPrimaryKeys = true;

	private DirtyDataStrategy dirtyDataStrategy = DirtyDataStrategy.EXCEPTION;

	private Meter outTps;
	private Meter outBps;
	private Counter sinkSkipCounter;
	private MetricUtils.LatencyGauge latencyGauge;

	private transient Connection connection;
	private transient Statement statement;
	private boolean ignoreDelete = false;
	private long lastPrintTime = 0L;
	private boolean reserveMs = false;

	private volatile transient Exception flushException = null;
	private volatile boolean flushError = false;
	private String replaceMode = "replace";
	private String writeMode;
	/**
	 * 非replaceMode模式下用于拼接insert into on duplicate key update + sqlUpdateColumnsStr
 	 */
	private String sqlUpdateColumnsStr = null;
    private String streamEvent = null;
    private EventExporter exporter;
    private String eventUnit;
    private String eventTopic;
    private String eventGroup;
    private String eventTag;
    private String streamType;
    private String eventServer;
	private List<String> fieldNamesExPk = new ArrayList<>();
	private List<String> fieldNameList = new ArrayList<>();
	private int sqlTimeoutMs;
	private String dbName;

	private QanatAdb30OutputFormat(
			String url,
			String tableName,
			String userName,
			String password,
			RowTypeInfo rowTypeInfo,
			List<String> pkFields,
			int maxRetryTime,
			String driverClassName,
			int connectionMaxActive,
			int connectionInitialSize,
			int connectionMinIdle,
			boolean connectionTestWhileIdle,
			int maxWait,
			int removeAbandonedTimeout) {
		this.url = url;
		this.tableName = tableName;
		this.userName = userName;
		this.password = password;
		this.rowTypeInfo = rowTypeInfo;
		this.pkFields = pkFields;
		this.maxRetryTime = maxRetryTime;
		this.driverClassName = driverClassName;
		this.connectionMaxActive = connectionMaxActive;
		this.connectionInitialSize = connectionInitialSize;
		this.connectionMinIdle = connectionMinIdle;
		this.connectionTestWhileIdle = connectionTestWhileIdle;
		Joiner joinerOnComma = Joiner.on(",").useForNull("null");
		List<String> fieldNamesStr = new ArrayList<>();
		for (int i = 0; i < rowTypeInfo.getArity(); i++) {
		    if (rowTypeInfo.getFieldNames()[i].equalsIgnoreCase("__trace_id__")) {
		        continue;
		    }
		    fieldNamesStr.add("`" + rowTypeInfo.getFieldNames()[i] + "`");
		}
		this.fieldNames = joinerOnComma.join(fieldNamesStr);
		this.maxWait = maxWait;
		if (null != pkFields && !pkFields.isEmpty()) {
			existsPrimaryKeys = true;
		} else {
			existsPrimaryKeys = false;
		}

		for (int i = 0; i < rowTypeInfo.getArity(); i++) {
		    if (rowTypeInfo.getFieldNames()[i].equalsIgnoreCase("__trace_id__")) {
		        continue;
		    }
		    fieldNameList.add(rowTypeInfo.getFieldNames()[i]);
		    if (this.pkFields.contains(rowTypeInfo.getFieldNames()[i])) {
		        continue;
		    }
		    fieldNamesExPk.add(rowTypeInfo.getFieldNames()[i] + "=excluded." + rowTypeInfo.getFieldNames()[i]);
		}

		if (url.startsWith("jdbc:postgresql")) {
			this.driverClassName = "org.postgresql.Driver";
			this.dbType = "hologres";
		} else {
			this.driverClassName = "com.mysql.jdbc.Driver";
			this.dbType = "adb3";
		}
	}

	public QanatAdb30OutputFormat setDbName(String dbName) {
		this.dbName = dbName;
		return this;
	}

	public QanatAdb30OutputFormat setSqlTimeoutMs(int sqlTimeoutMs) {
		this.sqlTimeoutMs = sqlTimeoutMs;
		return this;
	}

	public QanatAdb30OutputFormat setReserveMs(boolean reserveMs) {
		this.reserveMs = reserveMs;
		return this;
	}

	public QanatAdb30OutputFormat setIgnoreDelete(boolean ignoreDelete) {
		this.ignoreDelete = ignoreDelete;
		return this;
	}

	public QanatAdb30OutputFormat setBatchSize(int batchSize) {
		this.batchSize = batchSize;
		return this;
	}

	public QanatAdb30OutputFormat setBufferSize(int bufferSize) {
		this.bufferSize = bufferSize;
		return this;
	}

	public QanatAdb30OutputFormat setExceptUpdateKeys(List<String> exceptUpdateKeys) {
		this.exceptUpdateKeys = exceptUpdateKeys;
		return this;
	}

	public QanatAdb30OutputFormat setFlushIntervalMs(long flushIntervalMs) {
		this.flushIntervalMs = flushIntervalMs;
		return this;
	}

	public QanatAdb30OutputFormat setMaxSinkTps(long maxSinkTps) {
		this.maxSinkTps = maxSinkTps;
		return this;
	}

	public QanatAdb30OutputFormat setReplaceMode(String replaceMode) {
		this.replaceMode = replaceMode;
		return this;
	}

	public QanatAdb30OutputFormat setWriteMode(String writeMode) {
		this.writeMode = writeMode;
		return this;
	}

    public QanatAdb30OutputFormat setEventUnit(String eventUnit) {
        this.eventUnit = eventUnit;
        return this;
    }

    public QanatAdb30OutputFormat setSteamEvent(String streamEvent) {
        this.streamEvent = streamEvent;
        return this;
    }

    public QanatAdb30OutputFormat setEventGroup(String eventGroup) {
        this.eventGroup = eventGroup;
        return this;
    }

    public QanatAdb30OutputFormat setEventTag(String eventTag) {
        this.eventTag = eventTag;
        return this;
    }

    public QanatAdb30OutputFormat setEventTopic(String eventTopic) {
        this.eventTopic = eventTopic;
        return this;
    }

    public QanatAdb30OutputFormat setStreamType(String streamType) {
        this.streamType = streamType;
        return this;
    }

    public QanatAdb30OutputFormat setEventServer(String eventServer) {
        this.eventServer = eventServer;
        return this;
    }

	/**
	 * Start flusher that will flush buffer automatically
	 */
	protected void scheduleFlusher() {
		flusher = new ScheduledThreadPoolExecutor(1,
			    new BasicThreadFactory.Builder().namingPattern("Adb30OutputFormat.buffer.flusher-%d").daemon(true).build());
		
		flusher.scheduleAtFixedRate(new Runnable(){
			public void run() {
				try {
					if (System.currentTimeMillis() - lastFlushTime >= flushIntervalMs) {
						synchronized (this) {
							sync();
						}
					}
				} catch (Exception e) {
					log.error("flush buffer to Adb-3.0 failed", e);
					flushException = e;
					flushError = true;
				}
			}
		}, flushIntervalMs, flushIntervalMs, java.util.concurrent.TimeUnit.MILLISECONDS);
	}

	@Override
	public long getRetryTimeout() {
		return 0;
	}


	@Override
	public void configure(Configuration configuration) {

	}

	@Override
	public void open(int taskNumber, int numTasks) throws IOException {
		this.numTasks = numTasks;
		super.open(taskNumber, numTasks);
		synchronized (QanatAdb30OutputFormat.class) {
			dataSourceKey = url + userName + password + tableName;
			if (dataSourcePool.contains(dataSourceKey)){
				dataSource = dataSourcePool.get(dataSourceKey);
			} else {
				dataSource = new BasicDataSource();
				dataSource.setUrl(url);
				dataSource.setUsername(userName);
				dataSource.setPassword(password);
				dataSource.setDriverClassName(driverClassName);
				dataSource.setMaxActive(connectionMaxActive);
				dataSource.setInitialSize(connectionInitialSize);
				dataSource.setMaxWait(maxWait);
				dataSource.setMinIdle(connectionMinIdle);
				dataSource.setTestWhileIdle(connectionTestWhileIdle);
				dataSource.setRemoveAbandoned(true);
				dataSource.setMaxIdle(connectionMaxActive);
				dataSource.setPoolPreparedStatements(false);

				dataSource.setValidationQuery("select 1");
				dataSource.setTestWhileIdle(true);
				dataSource.setTestOnBorrow(true);
				dataSource.setTestOnReturn(true);
				dataSource.setTimeBetweenEvictionRunsMillis(180000);
				dataSource.setMinEvictableIdleTimeMillis(3600000);
				dataSource.setNumTestsPerEvictionRun(10);

				dataSource.setRemoveAbandoned(true);
				dataSource.setRemoveAbandonedTimeout(300);

				dataSourcePool.put(dataSourceKey, dataSource);
			}

			try {
				long start = System.currentTimeMillis();
				connection = dataSource.getConnection();
				long end = System.currentTimeMillis();
				log.info("Get Connection in Open Method " + (end - start));
				statement = connection.createStatement();
			} catch (Exception e) {
				log.error("Error When Init DataSource", e);
				ErrorUtils.throwException(ConnectorErrors.INST.rdsGetConnectionError("Adb-3.0"),e);
			}
		}
		if (existsPrimaryKeys) {
			scheduleFlusher();
			// 默认会把主键排除掉
			for (String pk : pkFields) {
				if (!exceptUpdateKeys.contains(pk)) {
					exceptUpdateKeys.add(pk);
				}
			}
		}
		List<String> updateColumnEqualStrs = new ArrayList<>();
		for (String field: rowTypeInfo.getFieldNames()) {
			if (!exceptUpdateKeys.contains(field)) {
				updateColumnEqualStrs.add("`" + field + "`=values(`" + field + "`)");
			}
		}
		sqlUpdateColumnsStr = org.apache.commons.lang3.StringUtils.join(updateColumnEqualStrs, ",");

		outTps = MetricUtils.registerOutTps(getRuntimeContext());
		outBps = MetricUtils.registerOutBps(getRuntimeContext(), "com/alibaba/blink/connectors/adb30");
		latencyGauge = MetricUtils.registerOutLatency(getRuntimeContext());
		sinkSkipCounter = MetricUtils.registerSinkSkipCounter(getRuntimeContext(), getName());
		
		//初始化MetaQ Producer
        streamEvent = org.apache.commons.lang3.StringUtils.isBlank(streamEvent) ? "enable" : streamEvent;
        if ("enable".equalsIgnoreCase(streamEvent)) {
	        exporter = new KafkaEventExporter(eventTopic, eventServer);
	        exporter.init();
	        log.info("KafkaEventExporter {}:{} inited", eventTopic, eventServer);
        }
	}

	@Override
	public void writeAddRecord(Row row) throws IOException {
		log.info("writeMode={}", writeMode);
		if (!"batch".equalsIgnoreCase(writeMode)) {
			singleRowWrite(row);
			return;
		}
		if (flushError && null != flushException){
			throw new RuntimeException(flushException);
		}

		currentCount++;
		if (existsPrimaryKeys) {
			synchronized (mapReduceBuffer) {
				String dupKey = JdbcUtils.constructDupKey(row, rowTypeInfo, pkFields);
				mapReduceBuffer.put(dupKey, new Tuple2<>(true, row));
			}
		} else {
			synchronized (mapReduceBufferWithoutPk) {
				mapReduceBufferWithoutPk.add(new Tuple2<>(true, row));
			}
		}

		if (currentCount >= bufferSize) {
			sync();
		}
	}

	@Override
	public void writeDeleteRecord(Row row) throws IOException {
//		if (flushError && null != flushException){
//			throw new RuntimeException(flushException);
//		}
//		if (ignoreDelete) {
//			sinkSkipCounter.inc(1);
//			return;
//		}
//
//		currentCount++;
//		if (existsPrimaryKeys) {
//			synchronized (mapReduceBuffer) {
//				String dupKey = JdbcUtils.constructDupKey(row, rowTypeInfo, pkFields);
//				mapReduceBuffer.put(dupKey, new Tuple2<>(false, row));
//			}
//		} else {
//			synchronized (mapReduceBufferWithoutPk) {
//				mapReduceBufferWithoutPk.add(new Tuple2<>(false, row));
//			}
//		}
//
//		if (currentCount >= bufferSize) {
//			sync();
//		}
	}
	
	private void executeSql(String traceId, String sql){
		executeSql(traceId, sql, true);
	}

	private void executeSql(String traceId, String sql,Boolean reportMetric) {
		long start = System.currentTimeMillis();
		int retryTime = 0;
		while (retryTime++ < maxRetryTime) {
			try {
				if (log.isDebugEnabled()) {
					log.debug(sql);
				}
				if (connection.isClosed()) {
					long start1 = System.currentTimeMillis();
					connection = dataSource.getConnection();
					long end = System.currentTimeMillis();
					// 每10分钟打印一次getConnection耗时
					if( end - lastPrintTime >= 10 * 60 * 1000) {
						lastPrintTime = end;
						log.info("Get Connection in executeSql " + (end - start1));
					}
					statement = connection.createStatement();
				}
				int upd0Retry = 0;
				int maxUpd0Retries = 3;
				while (upd0Retry++ < maxUpd0Retries) {
					statement.execute(sql);
					int updCnt = statement.getUpdateCount();
					log.info("{} dbname={},upd0Retry={},updCnt={}",traceId,dbName,upd0Retry,updCnt);
					if (updCnt > 0) {
						log.info("{} aftersql={},dbname={},result={},cost={}",traceId,sql,dbName,updCnt,System.currentTimeMillis()-start);
						log.info("datatube_stream_trace {} {} {} {} {}", traceId, null, System.currentTimeMillis(), null, "dw_save");
						TpsLimitUtils.limitTps(maxSinkTps, numTasks, start, 1);
						break;
					}
					if (upd0Retry < maxUpd0Retries) {
						try {
							// sleep according to retryTimes
							Thread.sleep(500 * upd0Retry);
						} catch (Exception e1) {
							//ignore
						}
					}
				}
				break;
			} catch (SQLException e) {
				log.error("Insert into db error,sql" + String.valueOf(sql));
				log.error("Insert into db error,exception:", e);
				if (retryTime == maxRetryTime) {
					BlinkRuntimeException blinkException = ErrorUtils.getException(ConnectorErrors.INST.rdsWriteError
							("Adb-3.0", sql), e);
					if (SQLExceptionSkipPolicy.judge(dirtyDataStrategy, e.getErrorCode(), blinkException)) {
						sinkSkipCounter.inc();
						log.error(blinkException.getErrorMessage() + " sql:" + sql);
					}
				}
				try {
					// sleep according to retryTimes
					Thread.sleep(1000 * retryTime);
				} catch (Exception e1) {
					//ignore
				}
			} finally {
				try {
					statement.close();
				} catch (Exception e) {
					log.error("", e);
				}
				try {
					connection.close();
				} catch (Exception e) {
					log.error("", e);
				}
			}
		}


		// report metrics
		long end = System.currentTimeMillis();
		if(reportMetric) {
			latencyGauge.report(end - start, 1);
			outTps.markEvent();
			// rough estimate bytes
			outBps.markEvent(sql.length() * 2);
		}
	}

	@Override
	public String getName() {
		return "Adb-3.0";
	}

	@Override
	public synchronized void sync() throws IOException {
		//使用synchronized关键字保证flush线程执行的时候，不会同时更新mapReduceBuffer的内容
		synchronized (existsPrimaryKeys ? mapReduceBuffer : mapReduceBufferWithoutPk) {
			List<Row> addBuffer = new ArrayList<>();
			List<Row> deleteBuffer = new ArrayList<>();
			Collection<Tuple2<Boolean, Row>> buffer = existsPrimaryKeys ? mapReduceBuffer.values() : mapReduceBufferWithoutPk;
			for (Tuple2<Boolean, Row> rowTuple2 : buffer) {
				if (rowTuple2.f0) {
					addBuffer.add(rowTuple2.f1);
				} else {
					deleteBuffer.add(rowTuple2.f1);
				}
			}
			if ("replace".equalsIgnoreCase(replaceMode)
					|| "update".equalsIgnoreCase(replaceMode)) {
				batchWrite(addBuffer);
			} else if ("delete".equalsIgnoreCase(replaceMode)) {
				if (existsPrimaryKeys) {
					batchDelete(addBuffer);
				} else {
					batchDeleteWithoutPk(addBuffer);
				}
			}
			if (existsPrimaryKeys) {
				batchDelete(deleteBuffer);
			} else {
				batchDeleteWithoutPk(deleteBuffer);
			}
			mapReduceBuffer.clear();
			mapReduceBufferWithoutPk.clear();
		}

		lastFlushTime = System.currentTimeMillis();
		currentCount = 0;
	}

	private void singleRowWrite(Row row) {
		String traceId = rowTypeInfo.getFieldIndex("__trace_id__") < 0 ? UUID.randomUUID().toString() : row.getField(rowTypeInfo.getFieldIndex("__trace_id__")) + "";
		long s1 = System.currentTimeMillis();
		log.info("{} pk={}", traceId, row.getField(rowTypeInfo.getFieldIndex(this.pkFields.get(0))));
		String[] fields = JdbcUtils.writeFormat(rowTypeInfo, row, null, reserveMs, dbType);
		String values = org.apache.commons.lang3.StringUtils.join(fields, ",");
		Map<String, String> fieldValueMap = JdbcUtils.getFieldValueMap(rowTypeInfo, row, null, reserveMs);
		
		String sql = null;
		if ("hologres".equalsIgnoreCase(dbType)) {
			sql = genSingleWriteSql4Holo(values, fieldValueMap, row, traceId);
		} else if ("adb3".equalsIgnoreCase(dbType)) {
			sql = genSingleWriteSql(values, fieldValueMap, row);
		}
		log.info("{} dbName={} sql={}", traceId, dbName, sql);
		executeSql(traceId, sql);
		
		//MetqQ export
		if ("enable".equalsIgnoreCase(streamEvent)) {
            //export event
            String[] fieldNames = rowTypeInfo.getFieldNames();
            String url = this.dataSourceKey.split("\\?")[0];
            String[] tokens = url.split("/");
            String dbName = tokens[tokens.length - 1];
            String tag = null;
            if (org.apache.commons.lang3.StringUtils.isBlank(eventTag)) {
                tag = dbName + "__" + tableName;
            } else {
                tag = eventTag;
            }
            
            StreamEvent streamEvent = new StreamEvent();
            Map<String, StreamEventField> fieldValues = new HashMap<>();
            streamEvent.setFieldValues(fieldValues);
            streamEvent.setPkField(this.pkFields);
            streamEvent.setTableName(tableName);
            streamEvent.setEventType(1);
            streamEvent.setDbName(dbName);
            streamEvent.setTs(System.currentTimeMillis());
            for (int i = 0; i < row.getArity(); i++) {
                ByteSerializer.ValueType colType = ByteSerializer.getTypeIndex(rowTypeInfo.getTypeAt(i).getTypeClass());
                String fieldName = fieldNames[i];
                if (fieldName.equalsIgnoreCase("__trace_id__")) {
                    continue;
                }
                StreamEventField eventField = new StreamEventField();
                eventField.setFieldName(fieldName);
                eventField.setNewValue(row.getField(i) + "");
                eventField.setFieldType(FieldTypeUtil.parseBlinkType(colType));
                fieldValues.put(fieldName, eventField);
            }
            try {
                streamEvent.setTraceId(traceId);
            } catch(Exception e) {}
            exporter.export(tag, streamEvent);
        }
		
		if (latencyGauge != null) {
			long s2 = System.currentTimeMillis();
			latencyGauge.report(s2 - s1, 1);
		}
		if (outTps != null) {
			outTps.markEvent(1);
		}
	}

	private void batchWrite(List<Row> buffers) {
		String traceId = UUID.randomUUID().toString().replace("-", "");
		if (null == buffers || buffers.isEmpty()) {
			return;
		}
		long s1 = System.currentTimeMillis();

		List<String> valueList = new ArrayList<String>();
		List<String[]> fieldValueList = new ArrayList<String[]>();
		String[] fields;
		for (Row row : buffers) {
			fields = JdbcUtils.writeFormat(rowTypeInfo, row, reserveMs);
			valueList.add("(" + org.apache.commons.lang3.StringUtils.join(fields, ",") + ")");
			fieldValueList.add(fields);
		}
		int startRowIndex = 0;
		int endRowIndex = batchSize;
		while(endRowIndex < valueList.size()){
			String sql = genBatchWriteSql(valueList.subList(startRowIndex,endRowIndex), fieldValueList.subList(startRowIndex,endRowIndex));
			log.info("dbName={} sql={}", dbName, sql);
			executeSql(traceId, sql);
			startRowIndex = endRowIndex;
			endRowIndex += batchSize;
		}

		if (startRowIndex != valueList.size()) {
			String sql = genBatchWriteSql(valueList.subList(startRowIndex, valueList.size()), fieldValueList.subList(startRowIndex, valueList.size()));
			log.info("dbName={} sql={}", dbName, sql);
			executeSql(traceId, sql, false);
		}
		
		//MetqQ export
		if ("enable".equalsIgnoreCase(streamEvent)) {
            //export event
            String[] fieldNames = rowTypeInfo.getFieldNames();
            String url = this.dataSourceKey.split("\\?")[0];
            String[] tokens = url.split("/");
            String dbName = tokens[tokens.length - 1];
            String tag = null;
            if (org.apache.commons.lang3.StringUtils.isBlank(eventTag)) {
                tag = dbName + "__" + tableName;
            } else {
                tag = eventTag;
            }
            for (Row row : buffers) {
                StreamEvent streamEvent = new StreamEvent();
                Map<String, StreamEventField> fieldValues = new HashMap<>();
                streamEvent.setFieldValues(fieldValues);
                streamEvent.setPkField(this.pkFields);
                streamEvent.setTableName(tableName);
                streamEvent.setEventType(1);
                streamEvent.setDbName(dbName);
                streamEvent.setTs(System.currentTimeMillis());
                for (int i = 0; i < row.getArity(); i++) {
                    ByteSerializer.ValueType colType = ByteSerializer.getTypeIndex(rowTypeInfo.getTypeAt(i).getTypeClass());
                    String fieldName = fieldNames[i];
                    if (fieldName.equalsIgnoreCase("__trace_id__")) {
                        continue;
                    }
                    StreamEventField eventField = new StreamEventField();
                    eventField.setFieldName(fieldName);
                    eventField.setNewValue(row.getField(i) + "");
                    eventField.setFieldType(FieldTypeUtil.parseBlinkType(colType));
                    fieldValues.put(fieldName, eventField);
                }
                try {
                    streamEvent.setTraceId("" + row.getField(rowTypeInfo.getFieldIndex("__trace_id__")));
                } catch(Exception e) {}
                exporter.export(tag, streamEvent);
            }
        }
		
		if (latencyGauge != null) {
			long s2 = System.currentTimeMillis();
			latencyGauge.report(s2 - s1, buffers.size());
		}
		if (outTps != null) {
			outTps.markEvent(buffers.size());
		}
	}

	private String genBatchWriteSql(List<String> valueList, List<String[]> fieldValueList) {
		StringBuilder sb = new StringBuilder();
		if ("hologres".equalsIgnoreCase(dbType)) {
			if ("replace".equalsIgnoreCase(replaceMode)) {
	        	return sb.append("INSERT INTO ").append(tableName).append(" (" + fieldNames.replaceAll("`", "") + " ) values ").append(org.apache.commons.lang3.StringUtils.join(valueList, ",")).toString();
			} else {
				return sb.toString();
			}
		} else {
	        if ("replace".equalsIgnoreCase(replaceMode)) {
	            sb.append("REPLACE INTO ").append(tableName).append(" (" + fieldNames + " ) values");
	            return sb.append(org.apache.commons.lang3.StringUtils.join(valueList, ",")).toString();
	        } else if ("update".equalsIgnoreCase(replaceMode)) {
				sb.append("UPDATE INTO ").append(tableName).append(" (" + fieldNames + " ) values");
				return sb.append(org.apache.commons.lang3.StringUtils.join(valueList, ",")).toString();
	        } else {
				return sb.toString();
			}
		}
	}

	private String genSingleWriteSql(String values, Map<String, String> fieldValueMap, Row row) {
		StringBuilder sb = new StringBuilder();
        if ("replace".equalsIgnoreCase(replaceMode)) {
    		return sb.append("REPLACE INTO ").append(tableName).append(" (" + fieldNames + " ) values (").append(values).append(")").toString();
        } else if ("update".equalsIgnoreCase(replaceMode)) {
    		return sb.append("UPDATE INTO ").append(tableName).append(" (" + fieldNames + " ) values (").append(values).append(")").toString();
        } else if ("delete_by_pk".equalsIgnoreCase(replaceMode)) {
			return sb.append("DELETE FROM ").append(tableName).append(" WHERE ").append(this.pkFields.get(0)).append("=").append(fieldValueMap.get(this.pkFields.get(0))).toString();
        } else if ("update_by_query".equalsIgnoreCase(replaceMode)) {
        	sb.append("/*+query_timeout=" + sqlTimeoutMs + "*/");
			sb.append("UPDATE ").append(tableName).append(" SET ");
			Object[] setOutput = JdbcUtils.updateSetFormat(rowTypeInfo, row, new HashSet<>(pkFields), null, reserveMs);
			Object[] whereOutput = JdbcUtils.updateWhereFormat(rowTypeInfo, row, new HashSet<>(pkFields), null, reserveMs);
			sb.append(org.apache.commons.lang3.StringUtils.join(setOutput, ","));
			sb.append(" WHERE ");
			sb.append(org.apache.commons.lang3.StringUtils.join(whereOutput, " AND "));
			return sb.toString();
		} else if ("upsert".equalsIgnoreCase(replaceMode)) {
			Object[] setOutput = JdbcUtils.updateSetFormat(rowTypeInfo, row, new HashSet<>(pkFields), null, reserveMs);
        	return sb.append("INSERT INTO ").append(tableName).append(" (" + fieldNames + " ) values (").append(values).append(") ON DUPLICATE KEY UPDATE ").append(org.apache.commons.lang3.StringUtils.join(setOutput, ",")).toString();
        } else {
        	return null;
        }
	}

	private String genSingleWriteSql4Holo(String values, Map<String, String> fieldValueMap, Row row, String traceId) {
		StringBuilder sb = new StringBuilder();
        if ("delete_by_pk".equalsIgnoreCase(replaceMode)) {
        	List<String> whereConds = new ArrayList<>();
        	for (String pkField : this.pkFields) {
        		whereConds.add(pkField + "=" + fieldValueMap.get(pkField));
        	}
			return sb.append("DELETE FROM ").append(tableName).append(" WHERE ").append(org.apache.commons.lang3.StringUtils.join(whereConds, " AND ")).toString();
        } else if ("update_by_query".equalsIgnoreCase(replaceMode)
        		|| "update".equalsIgnoreCase(replaceMode)) {
			sb.append("UPDATE ").append(tableName).append(" SET ");
			Object[] setOutput = JdbcUtils.updateSetFormat(rowTypeInfo, row, new HashSet<>(pkFields), null, reserveMs);
			List<Object> setOutputList = new ArrayList<>();
			setOutputList.addAll(Arrays.asList(setOutput));
			setOutputList.add("__trace_id__='" + traceId + "'");
			setOutputList.add("__gmt_modified__='" + DateUtil.timeStamp2String(new Timestamp(System.currentTimeMillis()), null, reserveMs) + "'");
			Object[] whereOutput = JdbcUtils.updateWhereFormat(rowTypeInfo, row, new HashSet<>(pkFields), null, reserveMs);
			sb.append(org.apache.commons.lang3.StringUtils.join(setOutputList, ","));
			sb.append(" WHERE ");
			sb.append(org.apache.commons.lang3.StringUtils.join(whereOutput, " AND "));
			return sb.toString();
		} else if ("upsert".equalsIgnoreCase(replaceMode)
				|| "replace".equalsIgnoreCase(replaceMode)
				|| "insert".equalsIgnoreCase(replaceMode)) {
			List<String> insertFieldList = new ArrayList<>();
			insertFieldList.addAll(fieldNameList);
			insertFieldList.add("__trace_id__");
			insertFieldList.add("__gmt_modified__");
			List<String> doUpdFieldList = new ArrayList<>();
			doUpdFieldList.addAll(fieldNamesExPk);
			doUpdFieldList.add("__trace_id__=excluded.__trace_id__");
			doUpdFieldList.add("__gmt_modified__=excluded.__gmt_modified__");
			String insertValues = values + (",'" + traceId + "','" + DateUtil.timeStamp2String(new Timestamp(System.currentTimeMillis()), null, reserveMs) + "'");
        	return sb.append("INSERT INTO ").append(tableName).append(" (" + org.apache.commons.lang3.StringUtils.join(insertFieldList, ",") + " ) values (").append(insertValues).append(") on conflict(" + org.apache.commons.lang3.StringUtils.join(this.pkFields, ",") + ") do update set ").append(org.apache.commons.lang3.StringUtils.join(doUpdFieldList, ",")).toString();
        } else {
        	return null;
        }
	}

	private void batchDelete(List<Row> buffers) {
		String traceId = UUID.randomUUID().toString().replace("-", "");
		for (Row row : buffers) {
			StringBuilder sb = new StringBuilder();
			sb.append("DELETE FROM ").append(tableName).append(" where ");
			Object[] output = JdbcUtils.deleteFormat(rowTypeInfo, row, new HashSet<>(pkFields), reserveMs);
			sb.append(org.apache.commons.lang3.StringUtils.join(output, " and "));
			String sql = sb.toString();
			executeSql(traceId, sql);
		}
		sinkSkipCounter.inc(buffers.size());
	}

	private void batchDeleteWithoutPk(List<Row> buffers) {
		String traceId = UUID.randomUUID().toString().replace("-", "");
		for (Row row : buffers) {
			Joiner joinerOnComma = Joiner.on(" AND ").useForNull("null");
			List<String> sub = new ArrayList<>();
			for (int i = 0; i < row.getArity(); i++) {
				sub.add(" " + rowTypeInfo.getFieldNames()[i] + " = " +
						JdbcUtils.toMysqlField(row.getField(i)));
			}
			String sql = String.format(DELETE_WITH_KEY_SQL_TPL, tableName, joinerOnComma.join(sub));
			executeSql(traceId, sql);
		}
		sinkSkipCounter.inc(buffers.size());
	}

	@Override
	public void close() throws IOException {
		if (flusher != null) {
			flusher.shutdown();
			flusher = null;
		}
		sync();
		try {
			if (null != connection && !connection.isClosed()) {
				connection.close();
			}
		} catch (Exception e) {
			// ignore this exception
		}
		synchronized (QanatAdb30OutputFormat.class) {
			if (dataSourcePool.remove(dataSourceKey) && !dataSource.isClosed()) {
				try {
					dataSource.close();
				}catch (SQLException e){
					log.error("",e);
				}
				dataSource = null;
			}
		}
        if (exporter != null) {
            exporter.close();
        }
	}

	public QanatAdb30OutputFormat setDirtyDataStrategy(DirtyDataStrategy strategy) {
		dirtyDataStrategy = strategy;
		return this;
	}

	public static class Builder {
		private String url;
		private String tableName;
		private String userName;
		private String password;
		private RowTypeInfo rowTypeInfo;
		private List<String> pkField;

		private int maxRetryTime = 3;

		private String driverClassName = "com.mysql.jdbc.Driver";
		private int connectionMaxActive = 40;
		private int connectionInitialSize = 1;
		private int connectionMinIdle = 0;
		private boolean connectionTestWhileIdle = true;
		private int maxWait = 15000;
		private int removeAbandonedTimeout = 60 * 10;
		private int batchSize = 50;
		private int bufferSize = 500;
		private long flushIntervalMs = 5000;
		private List<String> exceptUpdateKeys = new ArrayList<>();
		private long maxSinkTps = -1;
		private boolean ignoreDelete = false;
		private String replaceMode = "replace";
		private String writeMode;
		private boolean reserveMs = false;

	    private String streamEvent;
	    private String eventUnit;
	    private String eventTopic;
	    private String eventGroup;
	    private String eventTag;
	    private String streamType;
	    private String eventServer;
	    private int sqlTimeoutMs = 30000;
	    private String dbName;

		public Builder setDbName(String dbName) {
			this.dbName = dbName;
			return this;
		}

		public Builder setSqlTimeoutMs(int sqlTimeoutMs) {
			this.sqlTimeoutMs = sqlTimeoutMs;
			return this;
		}

		public Builder setReserveMs(boolean reserveMs) {
			this.reserveMs = reserveMs;
			return this;
		}

		public Builder setReplaceMode(String replaceMode) {
			this.replaceMode = replaceMode;
			return this;
		}

		public Builder setWriteMode(String writeMode) {
			this.writeMode = writeMode;
			return this;
		}

        public Builder setStreamEvent(String streamEvent) {
            this.streamEvent = streamEvent;
            return this;
        }

        public Builder setEventGroup(String eventGroup) {
            this.eventGroup = eventGroup;
            return this;
        }

        public Builder setEventTag(String eventTag) {
            this.eventTag = eventTag;
            return this;
        }

        public Builder setEventTopic(String eventTopic) {
            this.eventTopic = eventTopic;
            return this;
        }

        public Builder setEventServer(String eventServer) {
            this.eventServer = eventServer;
            return this;
        }

        public Builder setStreamType(String streamType) {
            this.streamType = streamType;
            return this;
        }

        public Builder setEventUnit(String eventUnit) {
            this.eventUnit = eventUnit;
            return this;
        }

		public Builder setIgnoreDelete(boolean ignoreDelete) {
			this.ignoreDelete = ignoreDelete;
			return this;
		}

		public Builder setMaxSinkTps(long maxSinkTps) {
			this.maxSinkTps = maxSinkTps;
			return this;
		}

		public Builder setBatchSize(int batchSize) {
			this.batchSize = batchSize;
			return this;
		}

		public Builder setBufferSize(int bufferSize) {
			this.bufferSize = bufferSize;
			return this;
		}

		public Builder setExceptUpdateKeys(List<String> exceptUpdateKeys) {
			if (null != exceptUpdateKeys) {
				this.exceptUpdateKeys = exceptUpdateKeys;
			}
			return this;
		}

		public Builder setFlushIntervalMs(long flushIntervalMs) {
			this.flushIntervalMs = flushIntervalMs;
			return this;
		}

		private DirtyDataStrategy dirtyDataStrategy = DirtyDataStrategy.EXCEPTION;

		void checkUserParameter(String target, String key) {
			if (StringUtils.isNullOrWhitespaceOnly(target)) {
				ErrorUtils.throwException(ConnectorErrors.INST.tableDDLConfigError(tableName,key));
			}
		}

		void checkIntervalParameter(String target, String key) {
			if (StringUtils.isNullOrWhitespaceOnly(target)) {
				ErrorUtils.throwException(ConnectorErrors.INST.tableDDLConfigError(tableName,key));
			}
		}

		void checkIntervalParameter(int target, String key) {
			if (target < 0) {
				ErrorUtils.throwException(ConnectorErrors.INST.tableDDLConfigError(tableName,key));
			}
		}

		public Builder setMaxWait(int maxWait) {
			checkIntervalParameter(maxWait, "maxWait");
			this.maxWait = maxWait;
			return this;
		}

		public Builder setRemoveAbandonedTimeout(int removeAbandonedTimeout) {
			checkIntervalParameter(removeAbandonedTimeout, "removeAbandonedTimeout");
			this.removeAbandonedTimeout = removeAbandonedTimeout;
			return this;
		}

		public Builder setUrl(String url) {
			checkUserParameter(url, "url");
			this.url = url;
			return this;
		}

		public Builder setTableName(String tableName) {
			checkUserParameter(tableName, "tableName");
			this.tableName = tableName;
			return this;
		}

		public Builder setUserName(String userName) {
			checkUserParameter(userName, "userName");
			this.userName = userName;
			return this;
		}

		public Builder setPassword(String password) {
			checkUserParameter(password, "password");
			this.password = password;
			return this;
		}

		public Builder setRowTypeInfo(RowTypeInfo rowTypeInfo) {
			this.rowTypeInfo = rowTypeInfo;
			return this;
		}

		public Builder setDriverClassName(String driverClassName) {
			checkIntervalParameter(driverClassName, "driverClassName");
			this.driverClassName = driverClassName;
			return this;
		}

		public Builder setMaxRetryTime(int maxRetryTime) {
			checkIntervalParameter(maxRetryTime, "maxRetryTime");
			this.maxRetryTime = maxRetryTime;
			return this;
		}

		public Builder setPkField(List<String> pkField) {
			this.pkField = pkField;
			return this;
		}

		public Builder setConnectionMaxActive(int connectionMaxActive) {
			checkIntervalParameter(connectionMaxActive, "connectionMaxActive");
			this.connectionMaxActive = connectionMaxActive;
			return this;
		}

		public Builder setConnectionInitialSize(int connectionInitialSize) {
			checkIntervalParameter(connectionInitialSize, "connectionInitialSize");
			this.connectionInitialSize = connectionInitialSize;
			return this;
		}

		public Builder setConnectionMinIdle(int connectionMinIdle) {
			checkIntervalParameter(connectionMinIdle, "connectionMinIdle");
			this.connectionMinIdle = connectionMinIdle;
			return this;
		}

		public Builder setConnectionTestWhileIdle(boolean connectionTestWhileIdle) {
			this.connectionTestWhileIdle = connectionTestWhileIdle;
			return this;
		}

		public Builder setDirtyDataStrategy(DirtyDataStrategy strategy) {
			this.dirtyDataStrategy = strategy;
			return this;
		}

		public QanatAdb30OutputFormat build() {
			QanatAdb30OutputFormat outputFormat = new QanatAdb30OutputFormat(
					url,
					tableName,
					userName,
					password,
					rowTypeInfo,
					pkField,
					maxRetryTime,
					driverClassName,
					connectionMaxActive,
					connectionInitialSize,
					connectionMinIdle,
					connectionTestWhileIdle,
					maxWait,
					removeAbandonedTimeout);
			outputFormat.setDirtyDataStrategy(dirtyDataStrategy)
						.setBatchSize(batchSize)
						.setBufferSize(bufferSize)
						.setFlushIntervalMs(flushIntervalMs)
						.setExceptUpdateKeys(exceptUpdateKeys)
						.setMaxSinkTps(maxSinkTps)
						.setReserveMs(reserveMs)
						.setIgnoreDelete(ignoreDelete)
						.setReplaceMode(replaceMode)
						.setWriteMode(writeMode)
						.setSteamEvent(streamEvent)
						.setEventGroup(eventGroup)
						.setEventTag(eventTag)
						.setEventTopic(eventTopic)
						.setEventUnit(eventUnit)
						.setStreamType(streamType)
						.setEventServer(eventServer)
						.setSqlTimeoutMs(sqlTimeoutMs)
						.setDbName(dbName);
			return outputFormat;
		}
	}
}
