package com.alibaba.blink.connectors.adb30;

import com.alibaba.blink.table.connectors.conf.BlinkOptions;
import org.apache.flink.configuration.ConfigOption;

import java.util.Arrays;
import java.util.List;

import static org.apache.flink.configuration.ConfigOptions.key;

public class QaantAdb30Options extends BlinkOptions {
    /** ADB-3.0 options. **/
    public static class ADB30 extends DIM {

        public static final ConfigOption<String> URL = key("url".toLowerCase())
                .noDefaultValue();

        public static final ConfigOption<String> TABLE_NAME = key("tableName".toLowerCase())
                .noDefaultValue();

        public static final ConfigOption<String> USER_NAME = key("userName".toLowerCase())
                .noDefaultValue();

        public static final ConfigOption<String> PASSWORD = key("password".toLowerCase())
                .noDefaultValue();

        public static final ConfigOption<String> DB_NAME = key("dbName".toLowerCase())
                .noDefaultValue();

        public static final ConfigOption<Integer> OPTIONAL_MAX_RETRY_TIMES = key("maxRetryTimes".toLowerCase())
                .defaultValue(10);

        public static final ConfigOption<Integer> OPTIONAL_BUFFER_SIZE = key("bufferSize".toLowerCase())
                .defaultValue(1000);

        public static final ConfigOption<Integer> OPTIONAL_BATCH_SIZE = key("batchSize".toLowerCase())
                .defaultValue(1000);


        public static final ConfigOption<Integer> OPTIONAL_FLUSH_INTERVAL_MS = key("flushIntervalMs".toLowerCase())
                .defaultValue(3000);

        public static final ConfigOption<Boolean> OPTIONAL_IGNORE_DELETE = key("ignoreDelete".toLowerCase())
                .defaultValue(false);
        public static final ConfigOption<String> OPTIONAL_EXCLUDE_UPDATE_FIELDS = key("excludeUpdateColumns".toLowerCase())
                .noDefaultValue();
        public static final ConfigOption<String> OPTIONAL_REPLACE_MODE = key("replaceMode".toLowerCase())
                .defaultValue("replace");
        public static final ConfigOption<String> OPTIONAL_WRITE_MODE = key("writeMode".toLowerCase())
                .defaultValue("batch");
        
        public static final ConfigOption<String> OPTIONAL_STREAM_EVENT = key("streamEvent".toLowerCase()).defaultValue("enable");
        public static final ConfigOption<String> OPTIONAL_STREAM_EVENT_UNIT = key("eventUnit".toLowerCase())
            .noDefaultValue();
        public static final ConfigOption<String> OPTIONAL_STREAM_EVENT_TOPIC= key("eventTopic".toLowerCase())
            .noDefaultValue();
        public static final ConfigOption<String> OPTIONAL_STREAM_EVENT_GROUP = key("eventGroup".toLowerCase())
            .noDefaultValue();
        public static final ConfigOption<String> OPTIONAL_STREAM_EVENT_TAG = key("eventTag".toLowerCase())
            .noDefaultValue();
        public static final ConfigOption<String> OPTIONAL_STREAM_TYPE = key("streamType".toLowerCase())
                .noDefaultValue();
        public static final ConfigOption<String> OPTIONAL_STREAM_EVENT_SERVER = key("eventServer".toLowerCase())
                .noDefaultValue();
        public static final ConfigOption<Integer> OPTIONAL_SQL_TIMEOUT_MS = key("sqlTimeoutMs".toLowerCase())
                .defaultValue(30000);

        public static final List<String> SUPPORTED_KEYS = Arrays.asList(URL.key(), TABLE_NAME.key(), USER_NAME.key(),
                PASSWORD.key(), OPTIONAL_MAX_RETRY_TIMES.key(), OPTIONAL_BUFFER_SIZE.key(),
                OPTIONAL_BATCH_SIZE.key(), OPTIONAL_FLUSH_INTERVAL_MS.key(), OPTIONAL_IGNORE_DELETE.key(),
                OPTIONAL_EXCLUDE_UPDATE_FIELDS.key(), OPTIONAL_REPLACE_MODE.key(), OPTIONAL_SQL_TIMEOUT_MS.key());


        public static final String PARAMS_DIM_HELP_MSG = String.format(
                "required params:%s,%s,%s,%s \n\toptional " +
                        "params:%s,%s,%s,%s",
                URL.key(),
                TABLE_NAME.key(),
                USER_NAME.key(),
                PASSWORD.key(),
                DB_NAME.key(),
                OPTIONAL_MAX_RETRY_TIMES.key(),
                OPTIONAL_BUFFER_SIZE.key(),
                OPTIONAL_BATCH_SIZE.key(),
                OPTIONAL_FLUSH_INTERVAL_MS.key());


        public static final String PARAMS_WRITER_HELP_MSG = String.format(
                "required params:%s,%s,%s \n\toptional " +
                        "params:%s,%s,%s",
                URL.key(),
                TABLE_NAME.key(),
                USER_NAME.key(),
                PASSWORD.key(),
                OPTIONAL_MAX_RETRY_TIMES.key(),
                OPTIONAL_IGNORE_DELETE.key());
    }
}
