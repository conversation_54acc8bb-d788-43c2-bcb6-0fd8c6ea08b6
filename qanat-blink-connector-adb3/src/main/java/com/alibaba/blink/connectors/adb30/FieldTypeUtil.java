package com.alibaba.blink.connectors.adb30;

import com.alibaba.blink.streaming.connector.hbase.utils.ByteSerializer;

import com.aliyun.wormhole.qanat.stream.event.FieldType;

public class FieldTypeUtil {

	public static int parseBlinkType(ByteSerializer.ValueType type) {
		switch (type) {
        case V_Short:
        case V_Integer:
        case V_Long:
        case V_BigInteger:
			return FieldType.TYPE_LONG;
        case V_BigDecimal:
        case V_Float:
        case V_Double:
			return FieldType.TYPE_DOUBLE;
		case V_Timestamp:
		case V_Time:
			return FieldType.TYPE_DATE;
		case V_String:
        case V_Byte:
			return FieldType.TYPE_STRING;
		default:
			return FieldType.TYPE_NOT_SUPPORT;
		}
	}
}
