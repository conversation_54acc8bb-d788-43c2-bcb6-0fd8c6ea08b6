/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.blink.connectors.adb30;

import com.alibaba.blink.connectors.adb30.dim.Adb30ConnectionParam;
import com.alibaba.blink.streaming.connectors.common.errcode.ConnectorErrors;
import com.alibaba.blink.streaming.connectors.common.exception.ErrorUtils;
import com.alibaba.blink.streaming.connectors.common.exception.InvalidParamException;
import com.alibaba.blink.streaming.connectors.common.exception.NotEnoughParamsException;
import com.alibaba.blink.streaming.connectors.common.source.parse.DirtyDataStrategy;
import com.alibaba.blink.streaming.connectors.common.util.BlinkStringUtil;
import com.alibaba.blink.table.cache.CacheConfig;
import com.alibaba.blink.table.cache.CacheStrategy;
import com.alibaba.blink.table.factories.BlinkTableFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;

import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.table.factories.BatchCompatibleTableSinkFactory;
import org.apache.flink.table.factories.BatchTableSourceFactory;
import org.apache.flink.table.factories.StreamTableSinkFactory;
import org.apache.flink.table.factories.StreamTableSourceFactory;
import org.apache.flink.table.sinks.BatchCompatibleStreamTableSink;
import org.apache.flink.table.sinks.StreamTableSink;
import org.apache.flink.table.sources.BatchTableSource;
import org.apache.flink.table.sources.StreamTableSource;
import org.apache.flink.table.util.TableProperties;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_PROPERTY_VERSION;
import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_TYPE;

public class QanatAdb30TableFactory extends BlinkTableFactory implements
		BatchTableSourceFactory<BaseRow>,
		StreamTableSourceFactory<BaseRow>,
		StreamTableSinkFactory<Tuple2<Boolean, Row>>,
		BatchCompatibleTableSinkFactory<Tuple2<Boolean, Row>> {

	@Override
	public StreamTableSource<BaseRow> createStreamTableSource(Map<String, String> props) {
		TableProperties properties = new TableProperties();
		properties.putProperties(props);
		RichTableSchema schema = properties.readSchemaFromProperties(classLoader);
		String s = properties.readTableNameFromProperties();
		String url = properties.getString(QaantAdb30Options.ADB30.URL);
		String tableName = properties.getString(QaantAdb30Options.ADB30.TABLE_NAME);
		String userName = properties.getString(QaantAdb30Options.ADB30.USER_NAME);
		String password = properties.getString(QaantAdb30Options.ADB30.PASSWORD);
		String dbName = properties.getString(QaantAdb30Options.ADB30.DB_NAME);
		
		if (BlinkStringUtil.isNotEmpty(dbName)) {
            String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
            JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
            url = dbMetaJson.getString("jdbcUrl");
            userName = dbMetaJson.getString("username");
            password = dbMetaJson.getString("password");
		}

		if (BlinkStringUtil.isEmpty(url, tableName, userName, password)) {
			throw new NotEnoughParamsException(QaantAdb30Options.ADB30.PARAMS_DIM_HELP_MSG);
		}

//		if (!url.startsWith("jdbc:mysql://")) {
//			throw new InvalidParamException("ADB-3.0 url must starts with jdbc:mysql:// format,but actual url is " + url);
//		}

		if (schema.deduceAllIndexes().isEmpty()) {
			// TODO remove this when support scannable dim table source
			ErrorUtils.throwException(ConnectorErrors.INST.dimTableNoIndexKeyError(s));
		}

		int joinMaxRows = properties.getInteger(QaantAdb30Options.DIM.OPTIONAL_JOIN_MAX_ROWS);
		int maxRetryTimes = properties.getInteger(QaantAdb30Options.ADB30.OPTIONAL_MAX_RETRY_TIMES);

		CacheStrategy cacheStrategy = QaantAdb30Options.ADB30.getCacheStrategy(properties);
		String cacheReloadTimeBlacklist = properties.getString(QaantAdb30Options.ADB30.OPTIONAL_CACHE_RELOAD_TIME_BLACKLIST);
		List<Tuple2<Long, Long>> timeRangeBlacklist = QaantAdb30Options.ADB30.parseTimeRangeBlacklist(cacheReloadTimeBlacklist);
		int scanLimit = properties.getInteger(QaantAdb30Options.ADB30.OPTIONAL_CACHE_SCAN_LIMIT);
		CacheConfig cacheConfig = new CacheConfig(cacheStrategy, timeRangeBlacklist, scanLimit);

		Adb30ConnectionParam param = new Adb30ConnectionParam();
		param.setUrl(url)
			.setTableName(tableName)
			.setUserName(userName)
			.setPassword(password)
			.setMaxRetryTime(maxRetryTimes)
			.setMaxFetchResult(joinMaxRows);

		return new Adb30TableSource(
				s,
				schema,
				param,
				cacheConfig);
	}

	private Adb30TableSink createSink(Map<String, String> props) {
		TableProperties properties = new TableProperties();
		properties.putProperties(props);
		RichTableSchema schema = properties.readSchemaFromProperties(classLoader);
		String url = properties.getString(QaantAdb30Options.ADB30.URL);
		String tableName = properties.getString(QaantAdb30Options.ADB30.TABLE_NAME);
		String userName = properties.getString(QaantAdb30Options.ADB30.USER_NAME);
		String password = properties.getString(QaantAdb30Options.ADB30.PASSWORD);
		// Timestamp type in adb-3.0 reserves fractional_seconds default.
		//boolean reserveMs = properties.getBoolean(QaantAdb30Options.TIMESTAMP_RESERVE_MILLISECOND, true);
		boolean reserveMs = false;
		String dbName = properties.getString(QaantAdb30Options.ADB30.DB_NAME);
		
		if (BlinkStringUtil.isNotEmpty(dbName)) {
            String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
            JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
            if (dbMetaJson == null) {
                url = "************************************************************************************************************************************";
                userName = "username";
                password = "password";
            } else {
	            url = dbMetaJson.getString("jdbcUrl");
	            userName = dbMetaJson.getString("username");
	            password = dbMetaJson.getString("password");
            }
		}

		if (BlinkStringUtil.isEmpty(url, tableName, userName, password)) {
			throw new NotEnoughParamsException(QaantAdb30Options.ADB30.PARAMS_WRITER_HELP_MSG);
		}

//		if (!url.startsWith("jdbc:mysql://")) {
//			throw new InvalidParamException("ADB-3.0 url must starts with jdbc:mysql:// format,but actual url is " + url);
//		}

		int maxRetryTimes = properties.getInteger(QaantAdb30Options.ADB30.OPTIONAL_MAX_RETRY_TIMES);
		int batchSize = properties.getInteger(QaantAdb30Options.ADB30.OPTIONAL_BATCH_SIZE);
		int bufferSize = properties.getInteger(QaantAdb30Options.ADB30.OPTIONAL_BUFFER_SIZE);
		int flushIntervalMs = properties.getInteger(QaantAdb30Options.ADB30.OPTIONAL_FLUSH_INTERVAL_MS);
		long maxSinkTps = properties.getLong(QaantAdb30Options.MAX_SINK_TPS);
		String excludeUpdateColumnsStr = properties.getString(QaantAdb30Options.ADB30.OPTIONAL_EXCLUDE_UPDATE_FIELDS);
		String replaceMode = properties.getString(QaantAdb30Options.ADB30.OPTIONAL_REPLACE_MODE);
		String writeMode = properties.getString(QaantAdb30Options.ADB30.OPTIONAL_WRITE_MODE);
		int sqlTimeoutMs = properties.getInteger(QaantAdb30Options.ADB30.OPTIONAL_SQL_TIMEOUT_MS);
		List<String> excludeUpdateColumns = new ArrayList<>();
		if (!BlinkStringUtil.isEmpty(excludeUpdateColumnsStr)) {
			for(String s1:excludeUpdateColumnsStr.split(",")){
				excludeUpdateColumns.add(s1);
			}
		}

		String actionOnInsertError = properties.getString(QaantAdb30Options.ACTION_ON_INSERT_ERROR);
		DirtyDataStrategy dirtyDataStrategy = DirtyDataStrategy.EXCEPTION;
		if(actionOnInsertError.equalsIgnoreCase("SKIP")){
			dirtyDataStrategy = DirtyDataStrategy.SKIP;
		}else if(actionOnInsertError.equalsIgnoreCase("SKIP_SINLENT")){
			dirtyDataStrategy = DirtyDataStrategy.SKIP_SILENT;
		}

		boolean ignoreDelete = properties.getBoolean(QaantAdb30Options.ADB30.OPTIONAL_IGNORE_DELETE);
		String partitionBy = properties.getString(QaantAdb30Options.PARTITION_BY);
		boolean shuffleEmptyKey = properties.getBoolean(QaantAdb30Options.SHUFFLE_EMPTY_KEY);
		
		String streamEvent = properties.getString(QaantAdb30Options.ADB30.OPTIONAL_STREAM_EVENT);
        String eventGroup = properties.getString(QaantAdb30Options.ADB30.OPTIONAL_STREAM_EVENT_GROUP);
        String eventTag = properties.getString(QaantAdb30Options.ADB30.OPTIONAL_STREAM_EVENT_TAG);
        String eventTopic = properties.getString(QaantAdb30Options.ADB30.OPTIONAL_STREAM_EVENT_TOPIC);
        String eventUnit = properties.getString(QaantAdb30Options.ADB30.OPTIONAL_STREAM_EVENT_UNIT);
        String eventServer = properties.getString(QaantAdb30Options.ADB30.OPTIONAL_STREAM_EVENT_SERVER);
		if (BlinkStringUtil.isNotEmpty(eventServer)) {
            String dbMetaStr = QanatDataSourceUtils.getDbMeta(eventServer, Thread.currentThread().getContextClassLoader());
            JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
            if (dbMetaJson != null && BlinkStringUtil.isNotEmpty(dbMetaJson.getString("bootstrap.servers"))) {
            	eventServer = dbMetaJson.getString("bootstrap.servers");
            }
		}
        String streamType = properties.getString(QaantAdb30Options.ADB30.OPTIONAL_STREAM_TYPE);

		QanatAdb30OutputFormat.Builder builder = new QanatAdb30OutputFormat.Builder();
		builder.setUrl(url)
				.setTableName(tableName)
				.setUserName(userName)
				.setPassword(password)
				.setPkField(schema.getPrimaryKeys())
				.setMaxRetryTime(maxRetryTimes)
				.setBufferSize(bufferSize)
				.setBatchSize(batchSize)
				.setFlushIntervalMs(flushIntervalMs)
				.setExceptUpdateKeys(excludeUpdateColumns)
				.setMaxSinkTps(maxSinkTps)
				.setDirtyDataStrategy(dirtyDataStrategy)
				.setIgnoreDelete(ignoreDelete)
				.setReserveMs(reserveMs)
				.setReplaceMode(replaceMode)
				.setWriteMode(writeMode)
				.setStreamEvent(streamEvent)
                .setEventGroup(eventGroup)
                .setEventTag(eventTag)
                .setEventTopic(eventTopic)
                .setEventUnit(eventUnit)
                .setEventServer(eventServer)
                .setStreamType(streamType)
                .setSqlTimeoutMs(sqlTimeoutMs)
                .setDbName(dbName);
        Adb30TableSink sink =  new Adb30TableSink(builder, schema);
		if (partitionBy != null && !partitionBy.isEmpty()) {
			sink.setPartitionedField(partitionBy);
			sink.setShuffleEmptyKey(shuffleEmptyKey);
		}
		return sink;
	}

	@Override
	protected List<String> supportedSpecificProperties() {
		return mergeProperties(QaantAdb30Options.ADB30.SUPPORTED_KEYS, QaantAdb30Options.DIM.SUPPORTED_KEYS);
	}

	@Override
	protected Map<String, String> requiredContextSpecific() {
		Map<String, String> context = new HashMap<>();
		context.put(CONNECTOR_TYPE, "QANAT_ADB30"); // adb-3.0
		context.put(CONNECTOR_PROPERTY_VERSION, "1"); // backwards compatibility
		return context;
	}

	@Override
	public BatchCompatibleStreamTableSink<Tuple2<Boolean, Row>> createBatchCompatibleTableSink(Map<String, String> properties) {
		return createSink(properties);
	}

	@Override
	public StreamTableSink<Tuple2<Boolean, Row>> createStreamTableSink(Map<String, String> properties) {
		return createSink(properties);
	}

	@Override
	public BatchTableSource<BaseRow> createBatchTableSource(Map<String, String> properties) {
		return (BatchTableSource<BaseRow>) createStreamTableSource(properties);
	}
}
