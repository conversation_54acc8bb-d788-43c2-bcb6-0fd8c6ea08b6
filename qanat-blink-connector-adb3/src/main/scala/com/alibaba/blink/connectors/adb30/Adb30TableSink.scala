/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.adb30

import java.lang.{Boolean => JBool}

import com.alibaba.blink.streaming.connectors.common.output.TupleOutputFormatAdapterSink
import com.alibaba.blink.streaming.connectors.common.source.SourceUtils
import org.apache.flink.api.java.tuple.{Tuple2 => JTuple2}
import org.apache.flink.streaming.api.datastream.DataStream
import org.apache.flink.table.api.RichTableSchema
import org.apache.flink.table.connector.DefinedDistribution
import org.apache.flink.table.sinks.{BatchCompatibleStreamTableSink, TableSinkBase, UpsertStreamTableSink}
import org.apache.flink.table.types.{DataType, DataTypes}
import org.apache.flink.types.Row

class Adb30TableSink(
                         outputFormatBuilder: QanatAdb30OutputFormat.Builder,
                         schema: RichTableSchema = null)
  extends TableSinkBase[JTuple2[JBool, Row]]
  with UpsertStreamTableSink[Row]
  with DefinedDistribution
  with BatchCompatibleStreamTableSink[JTuple2[JBool, Row]] {

  override protected def copy: TableSinkBase[JTuple2[JBool, Row]] = {
    val sink = new Adb30TableSink(outputFormatBuilder, schema)
    sink.partitionedField = this.partitionedField
    sink._shuffleEmptyKey = this._shuffleEmptyKey
    sink
  }

  private var partitionedField: String = null

  private var _shuffleEmptyKey: Boolean = true

  def setPartitionedField(partitionedField: String): Unit = {
    this.partitionedField = partitionedField
  }

    def setShuffleEmptyKey(shuffleEmptyKey: Boolean): Unit = {
    this._shuffleEmptyKey = shuffleEmptyKey
  }

  override def getPartitionFields(): Array[String] = {
    if (this.partitionedField == null) null else Array[String](partitionedField)
  }

  override def shuffleEmptyKey() = _shuffleEmptyKey

  override def setIsAppendOnly(isAppendOnly: JBool): Unit = {}

  override def setKeyFields(keys: Array[String]): Unit = {}

  override def emitDataStream(dataStream: DataStream[JTuple2[JBool, Row]]) = {
    outputFormatBuilder.setRowTypeInfo(SourceUtils.toRowTypeInfo(getRecordType))
    val sink = new TupleOutputFormatAdapterSink[Row](outputFormatBuilder.build())
    dataStream.addSink(sink).name(sink.toString)

  }

  override def emitBoundedStream(boundedStream: DataStream[JTuple2[JBool, Row]]) = {
    outputFormatBuilder.setRowTypeInfo(SourceUtils.toRowTypeInfo(getRecordType))
    val sink = new TupleOutputFormatAdapterSink[Row](outputFormatBuilder.build())
    boundedStream.addSink(sink).name(sink.toString)
  }

  override def getRecordType: DataType = DataTypes.createRowTypeV2(getFieldTypes, getFieldNames)

  override def getFieldNames: Array[String] = {
    if (null == schema) {
      super.getFieldNames
    } else {
      schema.getColumnNames
    }
  }

  override def getFieldTypes: Array[DataType] = {
    if (null == schema) {
      super.getFieldTypes
    } else {
      schema.getColumnTypes.asInstanceOf[Array[DataType]]
    }
  }
}
