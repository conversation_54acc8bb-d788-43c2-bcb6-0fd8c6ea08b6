/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.tddl

import org.apache.flink.api.java.typeutils.RowTypeInfo
import org.apache.flink.api.scala._
import org.apache.flink.streaming.api.scala.{DataStream, StreamExecutionEnvironment}
import org.apache.flink.table.api.scala.{BatchTableEnvironment, _}
import org.apache.flink.table.api.{RichTableSchema, TableConfig, TableEnvironment, Types}
import org.apache.flink.table.runtime.batch.sql.BatchTestBase.row
import org.apache.flink.table.types.DataTypes

import com.alibaba.blink.streaming.connectors.common.source.parse.DirtyDataStrategy
import org.junit.Test


import scala.collection.Seq
import scala.language.implicitConversions

class TddlTableSinkTest {

  /**
    * get batch source data
    *
    * @param tEnv
    */
  def getBatchData(tEnv: BatchTableEnvironment): Unit = {
    val smallData3 = Seq(
      row(1, 1, "Hi"),
      row(4, 2, "Hello"),
      row(3, 5, "Hello world"),
      row(30, 20, "Hello world"),
      row(30, 20, "my world")
    )
    val type3 = new RowTypeInfo(Types.INT, Types.INT, Types.STRING)
    tEnv.registerCollection("sourceTable", smallData3, type3, 'a, 'b, 'c)
  }

  @Test
  def testTddlTableSink(): Unit = {
    val env = StreamExecutionEnvironment.getExecutionEnvironment
    env.setParallelism(1)
    val tEnv = TableEnvironment.getTableEnvironment(env)

    val data = List(
      ("1", 2, "Hi"),
      ("2", 5, "Hello"),
      ("4", 5, "Hello "),
      ("8", 11, "Hello world"),
      ("9", 12, "Hello world!"))

    val stream: DataStream[(String, Int, String)] = env.fromCollection(data)
    val srcTab = stream.toTable(tEnv, 'a, 'b, 'c)
    tEnv.registerTable("src", srcTab)

    val schema = new RichTableSchema(
      Array("id", "len", "content"),
      Array(
        DataTypes.INT,
        DataTypes.STRING,
        DataTypes.STRING))

    var pkFields = new java.util.ArrayList[String]
    pkFields.add("id")

    val rdsOutputFormatBuilder = new TddlOutputFormat.Builder()
    rdsOutputFormatBuilder.setAppName("BLINKRDSTEST_APP")
      .setPkField(pkFields)
      .setSharding(false)
      .setTableName("test")
      .setMaxRetryTime(3)
      .setUnitName("daily")
      .setDirtyDataStrategy(DirtyDataStrategy.EXCEPTION)
    val sink = new TddlTableSink(rdsOutputFormatBuilder, schema)
    tEnv.registerTableSink(
      "dest",
      sink.getFieldNames,
      sink.getFieldTypes,
      sink
    )

    tEnv.sqlUpdate("insert into dest select b, a, c from src")
    tEnv.compile()

    env.execute()
  }

  @Test
  def testTddlTableSinkWithDbGroupKey(): Unit = {
    val env = StreamExecutionEnvironment.getExecutionEnvironment
    env.setParallelism(1)
    val tEnv = TableEnvironment.getTableEnvironment(env)

    val data = List(
      (1, 2, "Hi"),
      (1, 5, "Hello"),
      (1, 5, "Hello2"),
      (9, 11, "Hello world"),
      (9, 12, "Hello world!"))

    val stream: DataStream[(Int, Int, String)] = env.fromCollection(data)
    val srcTab = stream.toTable(tEnv, 'a, 'b, 'c)
    tEnv.registerTable("src", srcTab)

    val schema = new RichTableSchema(
      Array("rowkey", "cnt", "name"),
      Array(
        DataTypes.INT,
        DataTypes.INT,
        DataTypes.STRING))

    val pkFields = new java.util.ArrayList[String]
    pkFields.add("rowkey")

    val tddlOutputFormatBuilder = new TddlOutputFormat.Builder()
     tddlOutputFormatBuilder
      .setAppName("BLINK_CONNECTOR_TEST_APP")
      .setDbGroupKey("BLINK_CONNECTOR_TEST_0000_GROUP")
      .setPkField(pkFields)
      .setSharding(false)
      .setTableName("tddl_sink_table_1")
      .setMaxRetryTime(3)
      .setUnitName("daily")
      .setDirtyDataStrategy(DirtyDataStrategy.EXCEPTION)
    val sink = new TddlTableSink( tddlOutputFormatBuilder, schema)
    tEnv.registerTableSink(
      "dest",
      sink.getFieldNames,
      sink.getFieldTypes,
      sink
    )

    tEnv.sqlUpdate("insert into dest select a, b, c from src")
    tEnv.compile()
    env.execute()
  }

  @Test
  def testTddlTableBatchSink(): Unit = {
    val conf = new TableConfig
    val env: StreamExecutionEnvironment = StreamExecutionEnvironment.getExecutionEnvironment
    val tEnv: BatchTableEnvironment = TableEnvironment.getBatchTableEnvironment(env, conf)

    getBatchData(tEnv)

    val schema = new RichTableSchema(
      Array("id", "len", "content"),
      Array(
        DataTypes.INT,
        DataTypes.INT,
        DataTypes.STRING))

    var pkFields = new java.util.ArrayList[String]
    pkFields.add("id")

    val rdsOutputFormatBuilder = new TddlOutputFormat.Builder()
    rdsOutputFormatBuilder.setAppName("BLINKRDSTEST_APP")
      .setPkField(pkFields)
      .setSharding(false)
      .setTableName("test")
      .setMaxRetryTime(3)
      .setUnitName("daily")
      .setDirtyDataStrategy(DirtyDataStrategy.EXCEPTION)
    val sink = new TddlTableSink(rdsOutputFormatBuilder, schema)
    tEnv.registerTableSink(
      "dest",
      sink.getFieldNames,
      sink.getFieldTypes,
      sink
    )

    val res = tEnv.sqlQuery("select a, b, c from sourceTable")
    res.writeToSink(sink)
    tEnv.execute("testTddlOutput")
  }
}
