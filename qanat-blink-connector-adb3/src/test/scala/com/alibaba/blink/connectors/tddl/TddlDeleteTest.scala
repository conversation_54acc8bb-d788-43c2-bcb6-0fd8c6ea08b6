/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.tddl

import org.apache.flink.api.scala._
import org.apache.flink.streaming.api.scala.StreamExecutionEnvironment
import org.apache.flink.table.api.{RichTableSchema, TableEnvironment}
import org.apache.flink.table.api.scala._
import org.apache.flink.table.types.DataTypes

import org.junit.{Ignore, Test}

class TddlDeleteTest {
  // input data
  val data = List(
    ("Hello", 1),
    ("Hello", 1),
    ("Hello", 1)
  )

  @Ignore
  @Test
  def testTddlDelete(): Unit = {
    val env = StreamExecutionEnvironment.getExecutionEnvironment
    val tEnv = TableEnvironment.getTableEnvironment(env)
    val stream = env.fromCollection(data)
    val table = stream.toTable(tEnv, 'word, 'num)
    val resultTable = table
      .groupBy('word)
      .select('num.sum as 'cnt)
      .groupBy('cnt)
      .select('cnt as 'id, 'cnt.count as 'len)

    val schema = new RichTableSchema(
      Array("id", "len"),
      Array(
        DataTypes.INT,
        DataTypes.LONG))
    var pkFields = new java.util.ArrayList[String]
    pkFields.add("id")

    val tddlOutputFormatBuilder = new TddlOutputFormat.Builder()
    tddlOutputFormatBuilder.setAppName("BLINKRDSTEST_APP")
      .setPkField(pkFields)
      .setSharding(true)
      .setTableName("test")
      .setMaxRetryTime(3)
    val sink = new TddlTableSink(tddlOutputFormatBuilder, schema)
    tEnv.registerTableSink(
      "dest",
      sink.getFieldNames,
      sink.getFieldTypes,
      sink
    )

    resultTable.writeToSink(sink)

    env.execute()
  }
}
