/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.blink.connectors.tddl

import org.apache.flink.api.scala._
import org.apache.flink.streaming.api.scala.StreamExecutionEnvironment
import org.apache.flink.table.api.{RichTableSchema, TableEnvironment}
import org.apache.flink.table.api.scala.{StreamTableEnvironment, _}
import org.apache.flink.table.runtime.utils.TestingAppendSink
import org.apache.flink.table.types.DataTypes
import org.apache.flink.types.Row

import com.alibaba.blink.connectors.tddl.dim.TddlRowFetcher
import com.alibaba.blink.streaming.connectors.common.util.ConnectionPool
import com.alibaba.blink.table.cache.{CacheConfig, CacheStrategy}
import org.junit.Assert.assertEquals
import org.junit.{Before, Ignore, Test}

import java.util.Collections

@Ignore
class TddlDimTableTest{
  var env: StreamExecutionEnvironment = _
  var tEnv: StreamTableEnvironment = _

  @Before
  def start(): Unit = {
    env = StreamExecutionEnvironment.getExecutionEnvironment
    tEnv = TableEnvironment.getTableEnvironment(env)


    val param = new TddlConnectionParam()
      .setAppName("BLINKRDSTEST_APP")
      .setTableName("white_list")

    val schema = new RichTableSchema(Array("id", "name", "age"),
      Array(DataTypes.INT, DataTypes.STRING, DataTypes.INT))
    schema.setPrimaryKey("id")

    // 1  Jark  11
    // 2  Julian  22
    // 3  Lisa  33
    // 4  Jim  44
    // 5  Fabian  55
    // 7  Xi  77
    val tddl = new TddlTableSource(
      "tddl",
      schema,
      param,
      new CacheConfig(CacheStrategy.neverExpired(10, false)))
    val tddlNoCache = new TddlTableSource(
      "tddl",
      schema,
      param,
      new CacheConfig(CacheStrategy.none()))
    val tddl_cache_all = new TddlTableSource(
      "tddl_cache_all",
      schema,
      param,
      new CacheConfig(CacheStrategy.all(), Collections.emptyList(), 1))

    val schema2 = new RichTableSchema(Array("id", "name", "age"),
      Array(DataTypes.INT, DataTypes.STRING, DataTypes.INT))
    schema2.setPrimaryKey("name")
    val tddlStringKey = new TddlTableSource(
      "tddl",
      schema2,
      param,
      new CacheConfig(CacheStrategy.neverExpired(10, false)))

    tEnv.registerTableSource("tddl", tddl)
    tEnv.registerTableSource("tddl_no_cache", tddlNoCache)
    tEnv.registerTableSource("tddl_str_key", tddlStringKey)
    tEnv.registerTableSource("tddl_cache_all", tddl_cache_all)

    val data = List(
      (1, "Jark"),
      (2, "Julian"),
      (3, "Lisa"),
      (4, "Jim"),
      (5, "Timo"),
      (6, "Deng"))

    val ds = env.fromCollection(data)
    tEnv.registerDataStream("MyTable", ds, 'id, 'content)
  }

  @Test
  def testTddlDimTable(): Unit = {
    val sql =
      """
        |SELECT T.id, T.content, R.name, R.age
        |FROM MyTable AS T
        |LEFT JOIN tddl FOR SYSTEM_TIME AS OF PROCTIME() AS R
        |ON T.id = R.id AND T.content = R.name
      """.stripMargin

    val result = tEnv.sqlQuery(sql)
    val sink = new TestingAppendSink
    tEnv.toAppendStream[Row](result).addSink(sink)

    env.execute()

    val expected = List(
      "1,Jark,Jark,11",
      "2,Julian,Julian,22",
      "3,Lisa,Lisa,33",
      "4,Jim,Jim,44",
      "5,Timo,null,null",
      "6,Deng,null,null")

    assertEquals(expected.sorted, sink.getAppendResults.sorted)
    val field = classOf[TddlRowFetcher].getDeclaredField("dataSourcePool")
    field.setAccessible(true)
    val dataSourcePool = field
      .get(null)
      .asInstanceOf[ConnectionPool[_]]

    assertEquals(0, dataSourcePool.size())
  }

  @Test
  def testJoinTddlDimTableNoCache(): Unit = {
    val sql =
      """
        |SELECT T.id, T.content, R.name, R.age
        |FROM MyTable AS T
        |LEFT JOIN tddl_no_cache FOR SYSTEM_TIME AS OF PROCTIME() AS R
        |ON T.id = R.id AND T.content = R.name
      """.stripMargin

    val result = tEnv.sqlQuery(sql)
    val sink = new TestingAppendSink
    tEnv.toAppendStream[Row](result).addSink(sink)

    env.execute()

    val expected = List(
      "1,Jark,Jark,11",
      "2,Julian,Julian,22",
      "3,Lisa,Lisa,33",
      "4,Jim,Jim,44",
      "5,Timo,null,null",
      "6,Deng,null,null")

    assertEquals(expected.sorted, sink.getAppendResults.sorted)
    val field = classOf[TddlRowFetcher].getDeclaredField("dataSourcePool")
    field.setAccessible(true)
    val dataSourcePool = field
      .get(null)
      .asInstanceOf[ConnectionPool[_]]

    assertEquals(0, dataSourcePool.size())
  }

  @Test
  def testJoinTddlDimTableOnStrKey(): Unit = {
    val sql =
      """
        |SELECT T.id, T.content, R.name, R.age
        |FROM MyTable AS T
        |LEFT JOIN tddl_str_key FOR SYSTEM_TIME AS OF PROCTIME() AS R
        |ON T.content = R.name
      """.stripMargin

    val result = tEnv.sqlQuery(sql)
    val sink = new TestingAppendSink
    tEnv.toAppendStream[Row](result).addSink(sink)

    env.execute()

    val expected = List(
      "1,Jark,Jark,11",
      "2,Julian,Julian,22",
      "3,Lisa,Lisa,33",
      "4,Jim,Jim,44",
      "5,Timo,null,null",
      "6,Deng,null,null")

    assertEquals(expected.sorted, sink.getAppendResults.sorted)
    val field = classOf[TddlRowFetcher].getDeclaredField("dataSourcePool")
    field.setAccessible(true)
    val dataSourcePool = field
      .get(null)
      .asInstanceOf[ConnectionPool[_]]

    assertEquals(0, dataSourcePool.size())
  }

  @Test
  def testJoinTddlDimTableOnStrKeyAndOtherColumn(): Unit = {
    val sql =
      """
        |SELECT T.id, T.content, R.name, R.age
        |FROM MyTable AS T
        |LEFT JOIN tddl_str_key FOR SYSTEM_TIME AS OF PROCTIME() AS R
        |ON T.id = R.id AND T.content = R.name
      """.stripMargin

    val result = tEnv.sqlQuery(sql)
    val sink = new TestingAppendSink
    tEnv.toAppendStream[Row](result).addSink(sink)

    env.execute()

    val expected = List(
      "1,Jark,Jark,11",
      "2,Julian,Julian,22",
      "3,Lisa,Lisa,33",
      "4,Jim,Jim,44",
      "5,Timo,null,null",
      "6,Deng,null,null")

    assertEquals(expected.sorted, sink.getAppendResults.sorted)
    val field = classOf[TddlRowFetcher].getDeclaredField("dataSourcePool")
    field.setAccessible(true)
    val dataSourcePool = field
      .get(null)
      .asInstanceOf[ConnectionPool[_]]

    assertEquals(0, dataSourcePool.size())
  }

  @Test
  def testTddlDimTableWithCacheAll(): Unit = {
    val sql =
      """
        |SELECT T.id, T.content, R.name, R.age
        |FROM MyTable AS T
        |LEFT JOIN tddl_cache_all FOR SYSTEM_TIME AS OF PROCTIME() AS R
        |ON T.id = R.id AND T.content = R.name
      """.stripMargin

    val result = tEnv.sqlQuery(sql)
    val sink = new TestingAppendSink
    tEnv.toAppendStream[Row](result).addSink(sink)

    env.execute()

    val expected = List(
      "1,Jark,Jark,11",
      "2,Julian,Julian,22",
      "3,Lisa,Lisa,33",
      "4,Jim,Jim,44",
      "5,Timo,null,null",
      "6,Deng,null,null")

    assertEquals(expected.sorted, sink.getAppendResults.sorted)
    val field = classOf[TddlRowFetcher].getDeclaredField("dataSourcePool")
    field.setAccessible(true)
    val dataSourcePool = field
      .get(null)
      .asInstanceOf[ConnectionPool[_]]

    assertEquals(0, dataSourcePool.size())
  }

}
