<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.aliyun.wormhole</groupId>
	<artifactId>qanat-blink-hsf-udf</artifactId>
	<version>1.0.2</version>
	
	<properties>
        <maven.build.timestamp.format>yyyyMMdd_hhmm</maven.build.timestamp.format>
    </properties>

	<dependencies>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.68.noneautotype</version>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-core</artifactId>
            <version>blink-2.0-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table_2.11</artifactId>
            <version>blink-2.0-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-streaming-java_2.11</artifactId>
            <version>blink-2.0-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
        <!-- hsf调用需要的最少依赖,非spring方式 -->
        <dependency>
            <groupId>com.taobao.hsf</groupId>
            <artifactId>hsf-minimal</artifactId>
            <version>2.2.8.8</version>
        </dependency>
        <dependency>
        	<groupId>com.aliyun.wormhole</groupId>
        	<artifactId>qanat-datasource</artifactId>
        	<version>1.0.0</version>
        </dependency>
	</dependencies>
    
	<profiles>
	    <profile>
	        <!-- 测试环境 -->
	        <id>daily</id>
	        <properties>
	            <profiles.active>daily</profiles.active>
	        </properties>
	    </profile>
	    <profile>
	        <!-- 生产环境(弹内) -->
	        <id>pre</id>
	        <properties>
	            <profiles.active>pre</profiles.active>
	        </properties>
	    </profile>
	    <profile>
	        <!-- 生产环境(弹内) -->
	        <id>production</id>
	        <properties>
	            <profiles.active>production</profiles.active>
	        </properties>
	        <activation>
	            <activeByDefault>true</activeByDefault>
	        </activation>
	    </profile>
        <profile>
            <!-- 生产环境(新加坡) -->
            <id>sgp</id>
            <properties>
                <profiles.active>sgp</profiles.active>
            </properties>
        </profile>
	</profiles>

    <build>
        <plugins>
			<plugin>
			    <groupId>org.apache.maven.plugins</groupId>
			    <artifactId>maven-resources-plugin</artifactId>
			    <version>2.4.3</version>
			    <configuration>
			        <encoding>${project.build.sourceEncoding}</encoding>
			    </configuration>
			    <executions>
			        <execution>
			            <id>copy-spring-boot-resources</id>
			            <!-- here the phase you need -->
			            <phase>validate</phase>
			            <goals>
			                <goal>copy-resources</goal>
			            </goals>
			            <configuration>
			                <encoding>utf-8</encoding>
			                <outputDirectory>${basedir}/src/main/resources</outputDirectory>
			                <resources>
			                    <resource>
			                        <directory>${basedir}/src/main/resources/${profiles.active}</directory>
			                        <includes>
			                            <include>qanat.properties</include>
			                        </includes>
			                        <filtering>true</filtering>
			                    </resource>
			                </resources>
			            </configuration>
			        </execution>
			    </executions>
			</plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.5</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <version>2.3.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.5</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
							<shadedArtifactAttached>true</shadedArtifactAttached>
							<shadedClassifierName>${profiles.active}_${maven.build.timestamp}</shadedClassifierName>
                            <transformers>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                    <resource>internal_hsf_config.properties</resource>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <filters>
                        <filter>
                            <artifact>*:*</artifact>
                            <excludes>
                                <exclude>META-INF/*.SF</exclude>
                                <exclude>META-INF/*.DSA</exclude>
                                <exclude>META-INF/*.RSA</exclude>
                            </excludes>
                        </filter>
                    </filters>
                    <relocations>
                        <relocation>
                            <pattern>com.google</pattern>
                            <shadedPattern>shade.com.google</shadedPattern>
                        </relocation>
                        <relocation>
                            <pattern>com.alibaba.druid</pattern>
                            <shadedPattern>com.alibaba.blink.shaded.tddl.com.alibaba.druid</shadedPattern>
                        </relocation>
                    </relocations>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
