package com.aliyun.wormhole.qanat.blink.udf;

import java.util.Date;

import com.alibaba.fastjson.JSON;

import com.taobao.hsf.app.api.util.HSFApiConsumerBean;
import com.taobao.hsf.model.metadata.MethodSpecial;
import com.taobao.hsf.remoting.service.GenericService;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatRestartBlinkJobUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatRestartBlinkJobUdf.class);
    
    private GenericService genericService;
    
    @Override
    public void open(FunctionContext context) {
        try {
            HSFApiConsumerBean consumerBean = new HSFApiConsumerBean();
            consumerBean.setInterfaceName("com.aliyun.wormhole.qanat.api.service.BlinkService");
            consumerBean.setGeneric("true");
            consumerBean.setGroup("HSF");
            consumerBean.setVersion("1.0.0");
    
            MethodSpecial methodSpecial = new MethodSpecial();
            methodSpecial.setClientTimeout(60000);//客户端超时时间设置
            methodSpecial.setMethodName("rebuildBlinkJob");
            consumerBean.setMethodSpecials(new MethodSpecial[]{methodSpecial});
            
            // 设置同步等待 ConfigServer 推送地址的时间（单位：毫秒），从而避免因地址还未推送到就发起服务调用造成的
            // HSFAddressNotFoundException 问题。
            // 一般建议设置为 5000 毫秒，即可满足推送等待时间。
            consumerBean.init(5000);
            genericService = (GenericService) consumerBean.getObject();
        } catch(Exception e) {
            log.error("init hsf failed:{}", e.getMessage(), e);
        }
    }

    public Boolean eval(String tenantId, String appName, String jobName, Date startTime) {
        log.info("eval({},{},{},{})", tenantId, appName, jobName, startTime);
        try {
            Object result = genericService.$invoke("rebuildBlinkJob", 
            		new String[] {"java.lang.String", "java.lang.String", "java.lang.String", "java.util.Date"}, 
            		new Object[] {tenantId, appName, jobName, startTime});
            log.info("generic with method '{}' with param  ({},{},{},{}) return result: {}", "rebuildBlinkJob", tenantId, appName, jobName, startTime, JSON.toJSONString(result));
            if (result == null) {
                return false;
            }
            return (Boolean)result;
        } catch (Throwable ex) {
            log.error("eval failed:{}", ex.getMessage(), ex);
        }
        return false;
    }

    @Override
    public void close() {}
}