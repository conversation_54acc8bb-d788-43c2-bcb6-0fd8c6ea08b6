package com.aliyun.wormhole.qanat.blink.udf;

import java.util.Arrays;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.taobao.hsf.app.api.util.HSFApiConsumerBean;
import com.taobao.hsf.model.metadata.MethodSpecial;
import com.taobao.hsf.remoting.service.GenericService;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatHsfFunctionUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatHsfFunctionUdf.class);
    
    private GenericService genericService;
    
    @Override
    public void open(FunctionContext context) {}

    public void initService(JSONObject json) {
        try {
            HSFApiConsumerBean consumerBean = new HSFApiConsumerBean();
            consumerBean.setInterfaceName(json.getString("interfaceName"));
            consumerBean.setGeneric("true");
            consumerBean.setGroup(json.getString("group"));
            consumerBean.setVersion(json.getString("version"));
    
            MethodSpecial methodSpecial = new MethodSpecial();
            methodSpecial.setClientTimeout(json.getInteger("clientTimeout"));//客户端超时时间设置
            methodSpecial.setMethodName(json.getString("methodName"));
            consumerBean.setMethodSpecials(new MethodSpecial[]{methodSpecial});
            
            // 设置同步等待 ConfigServer 推送地址的时间（单位：毫秒），从而避免因地址还未推送到就发起服务调用造成的
            // HSFAddressNotFoundException 问题。
            // 一般建议设置为 5000 毫秒，即可满足推送等待时间。
            consumerBean.init(5000);
            genericService = (GenericService) consumerBean.getObject();
        } catch(Exception e) {
            log.error("init hsf failed:{}", e.getMessage(), e);
        }
    }

    public String eval(String tenantId, String funcCode, Object ... params) {
        log.info("eval({},{},{})", tenantId, funcCode, JSON.toJSONString(params));
        try {
        	String jsonStr = QanatDataSourceUtils.getExtensionScriptByCode(tenantId, funcCode, Thread.currentThread().getContextClassLoader());
        	JSONObject json = JSON.parseObject(jsonStr);
        	if (genericService == null) {
        		initService(json);
        	}
        	String[] paramTypes = new String[params.length];
        	paramTypes = Arrays.asList(params).stream().map(e->e.getClass().toString()).collect(Collectors.toList()).toArray(paramTypes);
            Object result = genericService.$invoke(json.getString("methodName"), paramTypes, params);
            log.info("generic with method '{}' with param  ({},{},{}) return result: {}", json.getString("methodName"), tenantId, funcCode, JSON.toJSONString(params), JSON.toJSONString(result));
            return JSON.toJSONString(result);
        } catch (Throwable ex) {
            log.error("eval failed:{}", ex.getMessage(), ex);
        }
        return null;
    }

    @Override
    public void close() {}
}