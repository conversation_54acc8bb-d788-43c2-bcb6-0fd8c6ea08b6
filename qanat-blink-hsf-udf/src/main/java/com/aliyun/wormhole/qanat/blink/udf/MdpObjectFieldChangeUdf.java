package com.aliyun.wormhole.qanat.blink.udf;

import java.util.Arrays;
import java.util.Map;
import java.util.Properties;

import com.alibaba.fastjson.JSON;

import com.taobao.hsf.app.api.util.HSFApiConsumerBean;
import com.taobao.hsf.model.metadata.MethodSpecial;
import com.taobao.hsf.remoting.service.GenericService;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MdpObjectFieldChangeUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(MdpObjectFieldChangeUdf.class);
    
    private GenericService genericService;
    
    @Override
    public void open(FunctionContext context) {
        try {
        	Properties prop = new Properties();
        	try {
        		prop.load(this.getClass().getClassLoader().getResourceAsStream("qanat.properties"));
        	} catch(Exception e) {
        		log.error("load props failed,e={}", e.getMessage(), e);
        	}
        	log.info("prop={}", JSON.toJSONString(prop));
        	
            HSFApiConsumerBean consumerBean = new HSFApiConsumerBean();
            consumerBean.setInterfaceName("com.aliyun.wormhole.qanat.api.service.RtdwViewModelTaskService");
            consumerBean.setGeneric("true");
            consumerBean.setGroup("HSF");
            if (StringUtils.isNotBlank(prop.getProperty("MdpObjectFieldChangeUdf.hsf.version"))) {
            	consumerBean.setVersion(prop.getProperty("MdpObjectFieldChangeUdf.hsf.version"));
            } else {
            	consumerBean.setVersion("1.0.0");
            }
        	if (StringUtils.isNotBlank(prop.getProperty("env"))) {
        		consumerBean.setConfigserverCenter(Arrays.asList(prop.getProperty("env")));
        	}
    
            MethodSpecial methodSpecial = new MethodSpecial();
            methodSpecial.setClientTimeout(60000);//客户端超时时间设置
            methodSpecial.setMethodName("reflectObjectFieldChange");
            consumerBean.setMethodSpecials(new MethodSpecial[]{methodSpecial});
            
            // 设置同步等待 ConfigServer 推送地址的时间（单位：毫秒），从而避免因地址还未推送到就发起服务调用造成的
            // HSFAddressNotFoundException 问题。
            // 一般建议设置为 5000 毫秒，即可满足推送等待时间。
            consumerBean.init(5000);
            genericService = (GenericService) consumerBean.getObject();
        } catch(Exception e) {
            log.error("init hsf failed:{}", e.getMessage(), e);
        }
    }

    public Boolean eval(String tenantId, String objectUniqueCode, String tagUniqueCode, String isRef, String operateType) {
        log.info("eval({},{},{},{})", tenantId, objectUniqueCode, tagUniqueCode, operateType);
        try {
            Object result = genericService.$invoke("reflectObjectFieldChange", 
            		new String[] {"java.lang.String", "java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.String"}, 
            		new Object[] {tenantId, objectUniqueCode, tagUniqueCode, isRef, operateType});
            log.info("generic with method '{}' with param  ({},{},{},{},{}) return result: {}", "MdpObjectFieldChangeUdf", tenantId, objectUniqueCode, tagUniqueCode, isRef, operateType, JSON.toJSONString(result));
            if (result == null) {
                return false;
            }
            Map<String, Object> resultMap = (Map<String, Object>)result;
            return (Boolean)resultMap.get("success");
        } catch (Throwable ex) {
            log.error("eval failed:{}", ex.getMessage(), ex);
        }
        return false;
    }

    @Override
    public void close() {}
}