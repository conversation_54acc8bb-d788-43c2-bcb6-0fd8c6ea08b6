package com.aliyun.wormhole.qanat.blink.udf;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.alibaba.fastjson.JSON;

import com.taobao.hsf.app.api.util.HSFApiConsumerBean;
import com.taobao.hsf.model.metadata.MethodSpecial;
import com.taobao.hsf.remoting.service.GenericService;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatMarkTagUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatMarkTagUdf.class);
    
    private GenericService genericService;
    
    @Override
    public void open(FunctionContext context) {
        try {
            HSFApiConsumerBean consumerBean = new HSFApiConsumerBean();
            consumerBean.setInterfaceName("com.aliyun.tag.api.service.ITagService");
            consumerBean.setGeneric("true");
            consumerBean.setGroup("HSF");
            consumerBean.setVersion("1.0.0");
    
            MethodSpecial methodSpecial = new MethodSpecial();
            methodSpecial.setClientTimeout(30000);
            methodSpecial.setMethodName("markTagByObject");//客户端超时时间设置
            consumerBean.setMethodSpecials(new MethodSpecial[]{methodSpecial});
            
            // 设置同步等待 ConfigServer 推送地址的时间（单位：毫秒），从而避免因地址还未推送到就发起服务调用造成的
            // HSFAddressNotFoundException 问题。
            // 一般建议设置为 5000 毫秒，即可满足推送等待时间。
            consumerBean.init(5000);
            genericService = (GenericService) consumerBean.getObject();
        } catch(Exception e) {
            log.error("init hsf failed:{}", e.getMessage(), e);
        }
    }

    public Boolean eval(String objectBizId, String tagCode, String tagValue, String operateDomainCode, String objectCode, String objectDomainCode, String tagDomainCode, String domainAK, String operateEmpid) {
        log.info("eval({},{},{},{},{},{},{},{},{})", objectBizId, tagCode, tagValue, operateDomainCode, objectCode, objectDomainCode, tagDomainCode, domainAK, operateEmpid);
        try {
            Map<String, Object> reqMap = new HashMap<>();
            reqMap.put("domainCode", operateDomainCode);
            reqMap.put("objectCode", objectCode);
            reqMap.put("requestId", UUID.randomUUID().toString());
            reqMap.put("objectBizId", objectBizId);
            reqMap.put("objectDomainCode", objectDomainCode);
            reqMap.put("domainAK", domainAK);
            List<Map<String, Object>> tagValueList = new ArrayList<>();
            reqMap.put("tagValueVOList", tagValueList);
            Map<String, Object> tagValueMap = new HashMap<>();
            tagValueList.add(tagValueMap);
            tagValueMap.put("tagDomainCode", tagDomainCode);
            tagValueMap.put("tagCode", tagCode);
            tagValueMap.put("tagValue", tagValue);
            tagValueMap.put("expireDate", null);
            tagValueMap.put("bizOperator", operateEmpid);

            Object result = genericService.$invoke("markTagByObject", new String[] {"com.aliyun.tag.api.request.tag.MarkTagByObjectRequest"}, new Object[] {reqMap});
            log.info("generic with method '{}' with param  {} return result: {}", "markTagByObject", JSON.toJSONString(reqMap), JSON.toJSONString(result));
            if (result == null) {
                return false;
            }
            Map<String, Object> resultMap = (Map<String, Object>)result;
            return (Boolean)resultMap.get("success");
        } catch (Throwable ex) {
            log.error("eval failed:{}", ex.getMessage(), ex);
        }
        return false;
    }

    @Override
    public void close() {}
}