package com.aliyun.wormhole.qanat.blink.udf;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.taobao.hsf.app.api.util.HSFApiConsumerBean;
import com.taobao.hsf.model.metadata.MethodSpecial;
import com.taobao.hsf.remoting.service.GenericService;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatDfaasFunctionUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatDfaasFunctionUdf.class);
    
    private GenericService genericService;
    
    @Override
    public void open(FunctionContext context) {
        try {
            HSFApiConsumerBean consumerBean = new HSFApiConsumerBean();
            consumerBean.setInterfaceName("com.alibaba.dt.oneness.common.hsf.OnenessHSFService");
            consumerBean.setGeneric("true");
            consumerBean.setGroup("HSF");
            consumerBean.setVersion("devata_dfaas_1.0");
    
            MethodSpecial methodSpecial = new MethodSpecial();
            methodSpecial.setClientTimeout(60000);//客户端超时时间设置
            methodSpecial.setMethodName("call");
            consumerBean.setMethodSpecials(new MethodSpecial[]{methodSpecial});
            
            // 设置同步等待 ConfigServer 推送地址的时间（单位：毫秒），从而避免因地址还未推送到就发起服务调用造成的
            // HSFAddressNotFoundException 问题。
            // 一般建议设置为 5000 毫秒，即可满足推送等待时间。
            consumerBean.init(5000);
            genericService = (GenericService) consumerBean.getObject();
        } catch(Exception e) {
            log.error("init hsf failed:{}", e.getMessage(), e);
        }
    }

    public String eval(String tenantId, String funcCode, Object ... params) {
        log.info("eval({},{},{})", tenantId, funcCode, JSON.toJSONString(params));
        try {
        	String jsonStr = QanatDataSourceUtils.getExtensionScriptByCode(tenantId, funcCode, Thread.currentThread().getContextClassLoader());
        	JSONObject json = JSON.parseObject(jsonStr);
        	Map<String, String> paramMap = new HashMap<>();
            for (int i = 0; i < json.getJSONArray("params").size(); i++) {
            	paramMap.put(json.getJSONArray("params").getString(i), params[i] == null ? null : params[i].toString());
            }
            Map<String,Object> result = (Map<String,Object>)genericService.$invoke("call", 
            										new String[] {String.class.getName(), Map.class.getName()}, 
            										new Object[] {json.getString("serviceName"), paramMap});
            log.info("generic with method '{}' with param  ({},{}) return result: {}", "call", json.getString("serviceName"), JSON.toJSONString(paramMap), JSON.toJSONString(result));
            if (result == null || result.get("data") == null) {
            	return null;
            }
            return JSON.toJSONString(result.get("data"));
        } catch (Throwable ex) {
            log.error("eval failed:{}", ex.getMessage(), ex);
        }
        return null;
    }

    @Override
    public void close() {}
}