package com.aliyun.wormhole.qanat.blink.udf;

import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections.CollectionUtils;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.RateLimiter;
import com.taobao.diamond.client.impl.DiamondUnitSite;
import com.taobao.diamond.manager.ManagerListener;

public class QanatFlowControlUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatFlowControlUdf.class);
	
	public Map<String, RateLimiter> limiterMap = null;
	public Map<String, JSONObject> limiterFilterMap = null;
	private int numberOfParallelSubtasks;
    
    @Override
    public void open(FunctionContext context) {
    	log.info("open({},{})", context.getIndexOfThisSubtask(), context.getNumberOfParallelSubtasks());
    	numberOfParallelSubtasks = context.getNumberOfParallelSubtasks();
    }

	private synchronized void initLimitConf(String limitConf) {
		JSONObject limitConfJson = JSON.parseObject(limitConf);
		if (limitConfJson != null && limitConfJson.getJSONArray("rules") != null && limitConfJson.getJSONArray("rules").size() > 0) {
			JSONArray rulesJsonArray = limitConfJson.getJSONArray("rules");
			limiterMap = new ConcurrentHashMap<>();
			limiterFilterMap = new ConcurrentHashMap<>();
			for (int i=0; i < rulesJsonArray.size(); i++) {
				JSONObject ruleJson =  rulesJsonArray.getJSONObject(i);
				limiterMap.put(ruleJson.getString("name"), RateLimiter.create(ruleJson.getDoubleValue("limit")/numberOfParallelSubtasks));
				if (ruleJson.getJSONObject("filter") != null && CollectionUtils.isNotEmpty(ruleJson.getJSONObject("filter").keySet())) {
					limiterFilterMap.put(ruleJson.getString("name"), ruleJson.getJSONObject("filter"));
				} else if (ruleJson.getJSONObject("filter") != null && CollectionUtils.isEmpty(ruleJson.getJSONObject("filter").keySet())) {
					limiterFilterMap.put(ruleJson.getString("name"), new JSONObject());
				}
			}
		}
	}

	private RateLimiter getLimiter(JSONObject json, Object pk) {
		if (limiterMap != null && limiterMap.keySet() != null) {
			for (String name : limiterMap.keySet()) {
				JSONObject filterJson = limiterFilterMap.get(name);
				if (filterJson != null && CollectionUtils.isNotEmpty(filterJson.keySet())) {
					boolean match = true;
					for (String key : filterJson.keySet()) {
						JSONArray valArray = filterJson.getJSONArray(key);
						if (valArray == null || valArray.size() == 0) {
							continue;
						}
						if (json.getJSONObject("fieldValues").getJSONObject(key) != null) {
							String refVal = json.getJSONObject("fieldValues").getJSONObject(key).getString("newValue") == null ? 
									json.getJSONObject("fieldValues").getJSONObject(key).getString("oldValue") : json.getJSONObject("fieldValues").getJSONObject(key).getString("newValue");
							for (int j = 0; j < valArray.size(); j++) {
								String val = valArray.getString(j);
								log.info("key:{} refVal:{}, val:{}", key, refVal, val);
								if (val.equalsIgnoreCase(refVal)) {
									continue;
								} else {
									match = false;
									break;
								}
							}
						} else {
							match = false;
						}
					}
					if (match) {
						log.info("key:{} 命中规则:{}", pk, name);
						return limiterMap.get(name);
					}
				} else {
					log.info("key:{} 空filter 命中规则:{}", pk, name);
					return limiterMap.get(name);
				}
			}
		}
		log.info("pk:{} 未命中规则", pk);
		return null;
	}

    public String eval(String traceId, String limiterId, Object key) {
    	return this.eval(traceId, limiterId, key, null);
    }

    public String eval(String traceId, String limiterId, Object key, String data) {
    	log.info("{} eval({},{},{})", traceId, limiterId, key, data);
		long startTs = System.currentTimeMillis();
    	if (limiterMap == null) {
			try {
				DiamondUnitSite.getDiamondUnitEnv("pre").addListeners(limiterId, "DATATUBE-FLOW-V2", Arrays.asList(new ManagerListener() {
					@Override
					public void receiveConfigInfo(String conf) {
						try {
							initLimitConf(conf);
						} catch (Exception e) {}
					}
					@Override
					public Executor getExecutor() {
						return null;
					}
				}));
				String limitConf = DiamondUnitSite.getDiamondUnitEnv("pre").getConfig(limiterId, "DATATUBE-FLOW-V2", 100000);
				log.info("{} limitConf={}", traceId, limitConf);
				initLimitConf(limitConf);
				log.info("{} init limitConf use:{}ms", traceId, System.currentTimeMillis() - startTs);
			} catch(Exception e) {
				log.error("{} get limit conf from diamond faild, error={}", traceId, e.getMessage(), e);
			}
		}
    	try {
			JSONObject dataJson = null;
			try{
				dataJson = JSON.parseObject(data);
			} catch(Exception e) {}
			if (dataJson == null) {
				dataJson = new JSONObject();
			}
			while (true) {
				if (getLimiter(dataJson, key) == null) {
					log.info("{} limiterId[{}] key:{} ts:{} 不需限流", traceId, limiterId, key, System.currentTimeMillis());
					break;
				}
				if (getLimiter(dataJson, key).tryAcquire(500, TimeUnit.MILLISECONDS)) {
					log.info("{} limiterId[{}] key:{} ts:{} 未限流", traceId, limiterId, key, System.currentTimeMillis());
					break;
				} else {
					log.info("{} limiterId[{}] key:{} ts:{} 触发限流", traceId, limiterId, key, System.currentTimeMillis());
					try {
	                    Thread.sleep(500);
	                } catch (InterruptedException ignore) {}
				}
			}
	    	log.info("datatube_stream_trace {} {} {} {} {}", traceId, key, System.currentTimeMillis(), limiterId, "flowcontrol");
		} catch(Exception e) {
			log.error("{} limiterId[{}] key:{} ts:{} 获取限流配置异常:{},不需限流", traceId, limiterId, key, System.currentTimeMillis(), e.getMessage(), e);
		}
    	return traceId;
    }

    @Override
    public void close() {}
}