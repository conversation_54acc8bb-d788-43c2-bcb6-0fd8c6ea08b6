package com.aliyun.wormhole.qanat.blink.udf;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatProjectRiskTransformUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatProjectRiskTransformUdf.class);
    
    @Override
    public void open(FunctionContext context) {}

    public String eval(String value) {
        log.info("eval({})", value);
        if (StringUtils.isNotBlank(value)) {
            String [] tokens = value.split(",");
            List<Map<String, Object>> riskList = new ArrayList<>();
            for (String risk : tokens) {
                Map<String, Object> riskMap = new HashMap<>();
                riskList.add(riskMap);
                riskMap.put("risk", transform(risk));
                riskMap.put("note", "");
            }
            return JSON.toJSONString(riskList);
        }
        return null;
    }

    private Object transform(String risk) {
        switch(risk) {
            case "CustomerDecision":
                return "WaitingForCustomerDecision";
            case "Compete":
                return "Competition";
            case "other":
                return "Others";
            default:
                return risk;
        }
    }

    @Override
    public void close() {}
}