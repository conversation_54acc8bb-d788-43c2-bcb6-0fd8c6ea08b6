package com.aliyun.wormhole.qanat.blink.udf;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;

public class QanatBuildPplGaapInrcUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatBuildPplGaapInrcUdf.class);
    
    @Override
    public void open(FunctionContext context) {}

    public String eval(Long project_id, String fy, String expected_binding_time, Long m1, Long m2, Long m3, Long m4, Long m5, Long m6, Long m7, Long m8, Long m9, Long m10, Long m11, Long m12, Long pre_fy_m12) {
        log.info("eval({},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{})", project_id,fy,expected_binding_time,m1,m2,m3,m4,m5,m6,m7,m8,m9,m10,m11,m12,pre_fy_m12);
        try {
            Map<String, Object> data = new HashMap<>();
            data.put("fy", fy);
            Map<String, Long> quarterMap = new HashMap<>();
            data.put("quarter", quarterMap);
            Map<String, Long> monthMap = new HashMap<>();
            data.put("month", monthMap);
            
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date gaapStartDate = sdf.parse(expected_binding_time);
            sdf = new SimpleDateFormat("yyyy-MM");
            String gaapStartYm = sdf.format(gaapStartDate);
            String year = gaapStartYm.split("-")[0];
            String month = gaapStartYm.split("-")[1];
            Integer gaapStartYmInt = Integer.valueOf(year + month);
            log.info("gaapStartYmInt={}", gaapStartYmInt);
            
            Map<Integer, Long> initGaapMonthMap = new HashMap<>();
            initGaapMonthMap.put(Integer.valueOf(fy + "04") - 100, m1);
            initGaapMonthMap.put(Integer.valueOf(fy + "05") - 100, m2);
            initGaapMonthMap.put(Integer.valueOf(fy + "06") - 100, m3);
            initGaapMonthMap.put(Integer.valueOf(fy + "07") - 100, m4);
            initGaapMonthMap.put(Integer.valueOf(fy + "08") - 100, m5);
            initGaapMonthMap.put(Integer.valueOf(fy + "09") - 100, m6);
            initGaapMonthMap.put(Integer.valueOf(fy + "10") - 100, m7);
            initGaapMonthMap.put(Integer.valueOf(fy + "11") - 100, m8);
            initGaapMonthMap.put(Integer.valueOf(fy + "12") - 100, m9);
            initGaapMonthMap.put(Integer.valueOf(fy + "01"), m10);
            initGaapMonthMap.put(Integer.valueOf(fy + "02"), m11);
            initGaapMonthMap.put(Integer.valueOf(fy + "03"), m12);
            log.info("initGaapMonthMap={}", JSON.toJSONString(initGaapMonthMap));


            Map<Integer, Long> adjustGaapMonthMap = new HashMap<>();
            for (Integer ym : initGaapMonthMap.keySet()) {
            	if (ym < gaapStartYmInt) {
            		adjustGaapMonthMap.put(ym, 0L);
            	} else {
            		adjustGaapMonthMap.put(ym, initGaapMonthMap.get(ym));
            	}
            }
            log.info("adjustGaapMonthMap={}", JSON.toJSONString(adjustGaapMonthMap));

            Map<String, Long> computeMonthGaapIncrMap = new HashMap<>();
        	computeMonthGaapIncrMap.put("m0", ((Integer.valueOf(fy + "03")  - 100) < gaapStartYmInt) ? 0 : pre_fy_m12);
            for (Integer ym : adjustGaapMonthMap.keySet()) {
            	String ymStr = ym + "";
            	String mStr = ymStr.substring(4);
            	Integer mInt = Integer.valueOf(mStr);
            	if (mInt > 3) {
            		mInt = mInt - 3;
            	} else {
            		mInt = mInt + 9;
            	}
            	computeMonthGaapIncrMap.put("m" + mInt, adjustGaapMonthMap.get(ym));
            }
            log.info("computeMonthGaapIncrMap={}", JSON.toJSONString(computeMonthGaapIncrMap));
            
            monthMap.put("m1", computeMonthGaapIncrMap.get("m1") - computeMonthGaapIncrMap.get("m0"));
            monthMap.put("m2", computeMonthGaapIncrMap.get("m2") - computeMonthGaapIncrMap.get("m1"));
            monthMap.put("m3", computeMonthGaapIncrMap.get("m3") - computeMonthGaapIncrMap.get("m2"));
            monthMap.put("m4", computeMonthGaapIncrMap.get("m4") - computeMonthGaapIncrMap.get("m3"));
            monthMap.put("m5", computeMonthGaapIncrMap.get("m5") - computeMonthGaapIncrMap.get("m4"));
            monthMap.put("m6", computeMonthGaapIncrMap.get("m6") - computeMonthGaapIncrMap.get("m5"));
            monthMap.put("m7", computeMonthGaapIncrMap.get("m7") - computeMonthGaapIncrMap.get("m6"));
            monthMap.put("m8", computeMonthGaapIncrMap.get("m8") - computeMonthGaapIncrMap.get("m7"));
            monthMap.put("m9", computeMonthGaapIncrMap.get("m9") - computeMonthGaapIncrMap.get("m8"));
            monthMap.put("m10", computeMonthGaapIncrMap.get("m10") - computeMonthGaapIncrMap.get("m9"));
            monthMap.put("m11", computeMonthGaapIncrMap.get("m11") - computeMonthGaapIncrMap.get("m10"));
            monthMap.put("m12", computeMonthGaapIncrMap.get("m12") - computeMonthGaapIncrMap.get("m11"));
            
            quarterMap.put("q1", monthMap.get("m1")*3 + monthMap.get("m2")*2 + monthMap.get("m3"));
            quarterMap.put("q2", monthMap.get("m4")*3 + monthMap.get("m5")*2 + monthMap.get("m6"));
            quarterMap.put("q3", monthMap.get("m7")*3 + monthMap.get("m8")*2 + monthMap.get("m9"));
            quarterMap.put("q4", monthMap.get("m10")*3 + monthMap.get("m11")*2 + monthMap.get("m12"));
                  
            String result = JSON.toJSONString(data);
            log.info("result={}", result);
            return result;
        } catch(Exception e) {
            log.error("eval failed, error={}", e.getMessage(), e);
        }
        return null;
    }

	@Override
    public void close() {}
	
	public static void main(String[] args) {
		QanatBuildPplGaapInrcUdf udf = new QanatBuildPplGaapInrcUdf();
		System.out.println(udf.eval(20200900001L, "2020", "2020-02-09 12:12:12", 1L, 2L, 4L, 8L, 16L, 32L, 64L, 128L, 256L, 512L, 1024L, 2048L, 9L));
	}
}
