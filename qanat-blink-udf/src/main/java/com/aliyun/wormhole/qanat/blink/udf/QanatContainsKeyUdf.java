package com.aliyun.wormhole.qanat.blink.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import scala.tools.jline_embedded.internal.Log;

import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatContainsKeyUdf extends ScalarFunction {
	
	private final static Logger log = LoggerFactory.getLogger(QanatContainsKeyUdf.class);
    
    @Override
    public void open(FunctionContext context) {}

    public boolean eval(String type, String jsonStr, String key) {
    	String traceId = UUID.randomUUID().toString();
    	log.info("{} eval({},{},{})", traceId, type, jsonStr, key);
    	try {
	    	if (StringUtils.isBlank(jsonStr) || StringUtils.isBlank(key)) {
	    		return false;
	    	}
	        JSONObject json = JSON.parseObject(jsonStr);
	        if (json == null) {
	        	return false;
	        }
	        if ("drc".equalsIgnoreCase(type)) {
	        	boolean result = json.getJSONObject("fieldValues").containsKey(key);
	        	log.info("{} result={}", traceId, result);
	        	return result;
	        } else if ("obj".equalsIgnoreCase(type)) {
	        	boolean result = json.getJSONObject("extParam").getJSONObject("objectInstanceVO").getJSONObject("fieldMap").containsKey(key);
	        	log.info("{} result={}", traceId, result);
	        	return result;
	        } else {
	        	boolean result = json.containsKey(key);
	        	log.info("{} result={}", traceId, result);
	        	return result;
	        }
    	} catch (Exception e) {
    		Log.error("{} eval failed", traceId, e.getMessage(), e);
    		return false;
    	}
    }

    @Override
    public void close() {}
}
