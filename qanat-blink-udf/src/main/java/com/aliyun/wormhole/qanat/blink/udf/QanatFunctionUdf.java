package com.aliyun.wormhole.qanat.blink.udf;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;

public class QanatFunctionUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatFunctionUdf.class);

    @Override
    public void open(FunctionContext context) {}

    public String eval(String tenantId, String funcCode, Object ... params) {
    	log.info("eval({},{})", funcCode, JSON.toJSONString(params));
        try {
        	String script = QanatDataSourceUtils.getExtensionScriptByCode(tenantId, funcCode, Thread.currentThread().getContextClassLoader());
        	log.info("script={} with params({},{})", script, funcCode, JSON.toJSONString(params));
        	if (StringUtils.isBlank(script)) {
        		return null;
        	}
        	ScriptEngineManager factory = new ScriptEngineManager();
            ScriptEngine engine = factory.getEngineByName("groovy");
            engine.eval(script);
            Invocable inv = (Invocable) engine;
            Object result = inv.invokeFunction("execute", params);
        	log.info("result=[{}] with params({},{})", JSON.toJSONString(result), funcCode, JSON.toJSONString(params));
        	return result.toString();
        } catch(Exception e) {
            log.error("eval failed with params({},{})", funcCode, JSON.toJSONString(params), e);
        }
        return null;
    }

    @Override
    public void close() {}
    
//    public static void main(String[] args) throws ScriptException, NoSuchMethodException, ParseException {
//    	String script = "import com.alibaba.fastjson.JSON;\n" + 
//    			"import com.alibaba.fastjson.JSONObject;\n" + 
//    			"import com.alibaba.fastjson.JSONPath;\n" + 
//    			"\n" + 
//    			"def execute(String jsonStr, String path) {\n" + 
//    			"	if(path == null) return null;\n" + 
//    			"	JSONObject json = JSON.parseObject(jsonStr);\n" + 
//    			"	return JSONPath.eval(json, path);\n" + 
//    			"}";
//    	ScriptEngineManager factory = new ScriptEngineManager();
//        ScriptEngine engine = factory.getEngineByName("groovy");
//        engine.eval(script);
//        Invocable inv = (Invocable) engine;
//        
//        String jsonStr = "{\"entityInstanceId\":\"1312\",\"type\":2,\"amount\":234.55,\"data\":{\"city\":\"beijing\",\"age\":[10,30]}}";
//        String key = "$.data.age[1]";
//        Object result = inv.invokeFunction("execute", new Object[] {jsonStr, key});
//        System.out.println(JSON.toJSONString(result));
//    }
}