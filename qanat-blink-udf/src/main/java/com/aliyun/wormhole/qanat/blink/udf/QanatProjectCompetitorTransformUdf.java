package com.aliyun.wormhole.qanat.blink.udf;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatProjectCompetitorTransformUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatProjectCompetitorTransformUdf.class);
    
    @Override
    public void open(FunctionContext context) {}

    public String eval(String value) {
        
        if (StringUtils.isNotBlank(value)) {
            log.info("eval({})", value);
            if ("--".equals(value)) {
                return null;
            }
            List<Map<String, Object>> dataList = new ArrayList<>();
            String [] competitors = value.split("\\|");
            for (String competitor : competitors) {
                Map<String, Object> data = new HashMap<>();
                String[] tokens = StringUtils.splitByWholeSeparatorPreserveAllTokens(competitor, "-");
                data.put("competitor", tokens[0]);
                data.put("ecs", "".equals(tokens[1]) ? null : Integer.parseInt(tokens[1]));
                data.put("CDN", "".equals(tokens[2]) ? null : Integer.parseInt(tokens[2]));
                dataList.add(data);
            }
            return JSON.toJSONString(dataList);
        }
        return null;
    }

    @Override
    public void close() {}
}