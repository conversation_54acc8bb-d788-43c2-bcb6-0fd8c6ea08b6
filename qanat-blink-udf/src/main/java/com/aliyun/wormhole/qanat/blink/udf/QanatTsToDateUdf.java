package com.aliyun.wormhole.qanat.blink.udf;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatTsToDateUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatTsToDateUdf.class);
    
    @Override
    public void open(FunctionContext context) {}

    public String eval(String ts) {
    	log.info("eval({})", ts);
        try {
        	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        	if (isNumberic(ts)) {
        		return sdf.format(new Date(Long.parseLong(ts)));
        	}
        } catch(Exception e) {
            log.error("eval failed", e);
        }
        return "";
    }

    public String eval(Long ts) {
    	log.info("eval({})", ts);
        try {
        	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        	return sdf.format(new Date(ts));
        } catch(Exception e) {
            log.error("eval failed", e);
        }
        return "";
    }

    @Override
    public void close() {}
    
    private static boolean isNumberic(String num) {
        try {
            Double.parseDouble(num);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}