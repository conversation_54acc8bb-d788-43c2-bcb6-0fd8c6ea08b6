package com.aliyun.wormhole.qanat.blink.udf;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatProjectSyncToPplUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatProjectSyncToPplUdf.class);
    
    @Override
    public void open(FunctionContext context) {
    }

    public String eval(String env, String isTestProject, String cid, String projectId, String projectName,
        String projectChangeTime, String projectGaap, String winRate, String cbmId, String cbmName, String battleFromType) {
        log.info("eval({},{},{},{},{},{},{},{},{},{})", env, isTestProject, cid, projectId, projectName,
            projectChangeTime, projectGaap, winRate, cbmId, cbmName, battleFromType);
        try {
            StringBuffer buffer = new StringBuffer();
            if ("pre".equalsIgnoreCase(env)) {
                buffer.append("https://pre-dos-apps.alibaba-inc.com");
            } else if ("daily".equalsIgnoreCase(env)) {
            	buffer.append("http://daily-dos-apps.alibaba.net");
            } else {
                buffer.append("https://bos.aliyun-inc.com");
            }
            buffer.append("/devata/ppl/syncProjectV3.json?")
            .append("cid=" + cid)
            .append("&cbmId=" + cbmId)
            .append("&cbmName=" + cbmName)
            .append("&projectId=" + projectId)
            .append("&projectName=" + projectName)
            .append("&projectChangeTime=" + projectChangeTime)
            .append("&projectGaap=" + projectGaap)
            .append("&winRate=" + winRate)
            .append("&isTestProject=" + isTestProject)
            .append("&battleFromType=" + battleFromType);
            String result = doHttpInvoke(buffer.toString());
            log.info("result={}", result);
            return result;
        } catch(Exception e) {
            log.error("eval failed, error={}", e.getMessage(), e);
        }
        return null;
    }

    private String doHttpInvoke(String url) {
        log.info("doHttpInvoke({})", url);
        String resp = null;
        Request request =  new Request.Builder()
                .url(url)
                .get()
                .build();
        
        OkHttpClient okHttpClient = new OkHttpClient();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                resp = response.body().string();  
                log.info("resp={}", resp);
            }
        } catch (Exception e) {
            log.error("http request failed", e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return resp;
    }

    @Override
    public void close() {}
}
