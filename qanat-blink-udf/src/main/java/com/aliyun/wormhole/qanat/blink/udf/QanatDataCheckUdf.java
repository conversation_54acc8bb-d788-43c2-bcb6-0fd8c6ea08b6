package com.aliyun.wormhole.qanat.blink.udf;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatDataCheckUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatDataCheckUdf.class);
    
    @Override
    public void open(FunctionContext context) {}

    public String eval(String fields, String srcValues, String dstValues) {
    	log.info("eval({},{},{})", fields, srcValues, dstValues);
        try {
            String[] fieldArray = fields.split(",");
            String[] srcValueArray = srcValues.split("\\|", -1);
            String[] dstValueArray = dstValues.split("\\|", -1);
            if (srcValueArray.length != dstValueArray.length) {
            	log.info("not equal:{})", "ERR_FIELD_NUMS" + "|l=" + srcValues + "|r=" + dstValues);
                return "ERR_FIELD_NUMS" + "|l=" + srcValues + "|r=" + dstValues;
            }
            for (int i=0; i < fieldArray.length; i++) {
                String field = fieldArray[i];
                String srcValue = srcValueArray[i];
                String dstValue = dstValueArray[i];
                if (!srcValue.equals(dstValue)) {
                	if (srcValue.contains(",") && dstValue.contains(",")) {
                		List<String> list1 = Arrays.asList(srcValue.split(","));
                        List<String> list2 = Arrays.asList(srcValue.split(","));
                        if (list1.containsAll(list2) && list2.containsAll(list1)) {
                    		continue;
                        }
                	}
                	if (isNumberic(srcValue) && isNumberic(dstValue) && Double.parseDouble(srcValue) == Double.parseDouble(dstValue)) {
                		continue;
                	}
                	if (compareAsDate(srcValue, dstValue)) {
                		continue;
                	}
                	log.info("not equal:{})", field + "|l=" + srcValue + "|r=" + dstValue);
                    return field + "|l=" + srcValue + "|r=" + dstValue;
                }
            }
        } catch(Exception e) {
            log.error("eval failed", e);
        }
        return "";
    }

    public String eval(String delimiter, String fields, String srcValues, String dstValues) {
    	log.info("eval({},{},{},{})", delimiter, fields, srcValues, dstValues);
        try {
            String[] fieldArray = fields.split(",");
            String[] srcValueArray = srcValues.split(delimiter);
            String[] dstValueArray = dstValues.split(delimiter);
            if (srcValueArray.length != dstValueArray.length) {
            	log.info("not equal:{})", "ERR_FIELD_NUMS" + "|l=" + srcValues + "|r=" + dstValues);
                return "ERR_FIELD_NUMS" + "|l=" + srcValues + "|r=" + dstValues;
            }
            for (int i=0; i < fieldArray.length; i++) {
                String field = fieldArray[i];
                String srcValue = srcValueArray[i];
                String dstValue = dstValueArray[i];
                if (!srcValue.equals(dstValue)) {
                	if (srcValue.contains(",") && dstValue.contains(",")) {
                		List<String> list1 = Arrays.asList(srcValue.split(","));
                        List<String> list2 = Arrays.asList(srcValue.split(","));
                        if (list1.containsAll(list2) && list2.containsAll(list1)) {
                    		continue;
                        }
                	}
                	if (isNumberic(srcValue) && isNumberic(dstValue) && Double.parseDouble(srcValue) == Double.parseDouble(dstValue)) {
                		continue;
                	}
                	if (compareAsDate(srcValue, dstValue)) {
                		continue;
                	}
                	log.info("not equal:{})", field + "|l=" + srcValue + "|r=" + dstValue);
                    return field + "|l=" + srcValue + "|r=" + dstValue;
                }
            }
        } catch(Exception e) {
            log.error("eval failed", e);
        }
        return "";
    }
    

    @Override
    public void close() {}
    
    private static boolean isNumberic(String num) {
        try {
            Double.parseDouble(num);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    private static Date getDateFromTs(String str) {
        try {
        	return new Date(Long.parseLong(str));
        } catch (Exception e) {
            return null;
        }
    }
    
    private static Date getDateFromStr(String str) {
    	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
        	return sdf.parse(str);
        } catch (Exception e) {
            return getDateFromTs(str);
        }
    }
    
    private static boolean compareAsDate(String l, String r) {
        try {
        	if (getDateFromStr(l) == null || getDateFromStr(r) == null) {
        		return false;
        	} else {
        		return (getDateFromStr(l).compareTo(getDateFromStr(r)) == 0);
        	}
        } catch (Exception e) {
            return false;
        }
    }
    
    public static void main(String[] args) {
    	String txt = "<EMAIL>|||||||||||||||||||||||||||||||||||";
    	System.out.println(txt.split("\\|").length);
    	System.out.println(StringUtils.splitPreserveAllTokens(txt, "\\|").length);
    }
}