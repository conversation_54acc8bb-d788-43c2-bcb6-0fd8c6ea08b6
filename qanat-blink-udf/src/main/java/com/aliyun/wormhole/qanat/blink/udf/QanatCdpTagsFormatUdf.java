package com.aliyun.wormhole.qanat.blink.udf;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatCdpTagsFormatUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatCdpTagsFormatUdf.class);
    
    @Override
    public void open(FunctionContext context) {}

    public String eval(String tags) {
        try {
            if (StringUtils.isNotBlank(tags)) {
                String [] tagCodeValueArray = tags.split(",");
                List<String> tagCodeValueList = new ArrayList<>();
                for (String tagCodeValue : tagCodeValueArray) {
                    String[] tagTokenArray = tagCodeValue.split("-", 2);
                    String tagCode = tagTokenArray[0];
                    String tagValues = tagTokenArray[1];
                    String[] tagValueArray = tagValues.split("\\.");
                    for (String tagValue : tagValueArray) {
                        tagCodeValueList.add(tagCode + "-" + tagValue);
                    }
                }
                return StringUtils.join(tagCodeValueList, ",");
            }
        } catch(Exception e) {
            log.error("eval failed", e);
        }
        return "";
    }

    @Override
    public void close() {}
}