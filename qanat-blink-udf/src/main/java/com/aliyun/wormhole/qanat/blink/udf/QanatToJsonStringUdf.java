package com.aliyun.wormhole.qanat.blink.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

public class QanatToJsonStringUdf extends ScalarFunction {
    
    @Override
    public void open(FunctionContext context) {}

    public String eval(String schema, String ... values) {
        JSONObject json = JSON.parseObject(schema);
        for (String key : json.keySet()) {
            String value = json.getString(key);
            int idx = Integer.valueOf(value.replace("$", ""));
            json.replace(key, values[idx]);
        }
        return JSON.toJSONString(json);
    }

    @Override
    public void close() {}
}
