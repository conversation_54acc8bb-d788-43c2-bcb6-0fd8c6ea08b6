<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<!-- <parent>
		<groupId>com.aliyun.wormhole</groupId>
		<artifactId>qanat-aliyun-inc-com</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</parent> -->
	<groupId>com.aliyun.wormhole</groupId>
	<artifactId>qanat-blink-udf</artifactId>
	<version>1.0.7</version>
	<name>qanat-blink-udf</name>
	<url>http://maven.apache.org</url>
	<properties>
		<table.version>blink-1.4-SNAPSHOT</table.version>
		<java_runtime.version>blink-1.4-SNAPSHOT</java_runtime.version>
		<scala.version>2.11.11</scala.version>
		<scala.binary.version>2.11</scala.binary.version>
		<runtime.version>blink-1.4-SNAPSHOT</runtime.version>
		<connectors.version>blink-1.4-SNAPSHOT</connectors.version>
		<statebackends.version>blink-1.4-SNAPSHOT</statebackends.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<!-- Internal property to reduce build times on TravisCi -->
		<flink-fast-tests-pattern>never-match-me</flink-fast-tests-pattern>
		<hadoop.version>2.8.0-adp2.1.0-SNAPSHOT</hadoop.version>
		<!-- Need to use a user property here because the surefire forkCount is 
			not exposed as a property. With this we can set it on the "mvn" commandline 
			in travis. -->
		<flink.forkCount>1C</flink.forkCount>
		<flink.reuseForks>true</flink.reuseForks>
		<log4j.configuration>log4j-test.properties</log4j.configuration>
		<guava.version>18.0</guava.version>
		<akka.version>2.3-custom</akka.version>
		<java.version>1.7</java.version>
		<scala.macros.version>2.1.0</scala.macros.version>
		<!-- Default scala versions, may be overwritten by build profiles -->
		<scala.version>2.11.11</scala.version>
		<scala.binary.version>2.11</scala.binary.version>
		<chill.version>0.8.1</chill.version>
		<asm.version>5.0.4</asm.version>
		<zookeeper.version>3.4.6</zookeeper.version>
		<curator.version>2.8.0</curator.version>
		<jackson.version>2.7.4</jackson.version>
		<metrics.version>3.1.0</metrics.version>
		<junit.version>4.12</junit.version>
		<mockito.version>1.10.19</mockito.version>
		<powermock.version>1.6.5</powermock.version>
		<blink_project_version>blink-3.3-SNAPSHOT</blink_project_version>
		<statebackends.version>blink-1.4-SNAPSHOT</statebackends.version>
		<tables.version>blink-3.3-SNAPSHOT</tables.version>
        <maven.build.timestamp.format>yyyyMMdd_hhmm</maven.build.timestamp.format>
	</properties>

	<dependencies>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.68.noneautotype</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.14.2</version>
        </dependency>
		<dependency>
			<groupId>com.alibaba.blink</groupId>
			<artifactId>blink-statebackend-rocksdb</artifactId>
			<version>${statebackends.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.alibaba.blink</groupId>
			<artifactId>blink-statebackend-niagara</artifactId>
			<version>${statebackends.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>${runtime.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-streaming-java_${scala.binary.version}</artifactId>
			<version>${runtime.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-streaming-scala_${scala.binary.version}</artifactId>
			<version>${runtime.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-java</artifactId>
			<version>${runtime.version}</version>
			<scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
            </exclusions>
		</dependency>
		<dependency>
			<groupId>com.alibaba.blink</groupId>
			<artifactId>flink-table_${scala.binary.version}</artifactId>
			<version>${blink_project_version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.alibaba.blink</groupId>
			<artifactId>flink-table_${scala.binary.version}</artifactId>
			<version>${blink_project_version}</version>
			<classifier>tests</classifier>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.scalatest</groupId>
			<artifactId>scalatest_2.11</artifactId>
			<version>2.2.6</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-streaming-java_2.11</artifactId>
			<version>${java_runtime.version}</version>
			<scope>provided</scope>
			<exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.7</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>14.0.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.alibaba.blink</groupId>
			<version>blink-table-1.2-SNAPSHOT</version>
			<artifactId>blink-table</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-test-utils_${scala.binary.version}</artifactId>
			<version>${runtime.version}</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>com.google.guava</groupId>
					<artifactId>guava</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.hadoop</groupId>
					<artifactId>hadoop-minikdc</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-tests_${scala.binary.version}</artifactId>
			<version>${runtime.version}</version>
			<type>test-jar</type>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>com.google.guava</groupId>
					<artifactId>guava</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.alibaba.blink</groupId>
			<artifactId>blink-connector-custom</artifactId>
			<version>${tables.version}</version>
			<scope>provided</scope>
		</dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>blink-connector-common</artifactId>
            <version>${tables.version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>blink-table</artifactId>
            <version>${tables.version}</version>
            <scope>provided</scope>
        </dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-streaming-scala_${scala.binary.version}</artifactId>
			<version>${runtime.version}</version>
			<scope>provided</scope>
		</dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.8</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>2.1.9</version>
        </dependency>
        <dependency>
        	<groupId>com.aliyun.wormhole</groupId>
        	<artifactId>qanat-datasource</artifactId>
        	<version>1.0.0</version>
        </dependency>
	</dependencies>
	
	<profiles>
	    <profile>
	        <!-- 测试环境 -->
	        <id>daily</id>
	        <properties>
	            <profiles.active>daily</profiles.active>
	        </properties>
	    </profile>
	    <profile>
	        <!-- 生产环境(弹内) -->
	        <id>production</id>
	        <properties>
	            <profiles.active>production</profiles.active>
	        </properties>
	        <activation>
	            <activeByDefault>true</activeByDefault>
	        </activation>
	    </profile>
        <profile>
            <!-- 生产环境(新加坡) -->
            <id>sgp</id>
            <properties>
                <profiles.active>sgp</profiles.active>
            </properties>
        </profile>
	</profiles>
	
	<build>
		<plugins>
            <!-- Scala Compiler -->
            <!-- <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <executions>
                    Run scala compiler in the process-resources phase, so that dependencies on
                        scala classes can be resolved later in the (Java) compile phase
                    <execution>
                        <id>scala-compile-first</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>add-source</goal>
                            <goal>compile</goal>
                        </goals>
                    </execution>

                    Run scala compiler in the process-test-resources phase, so that dependencies on
                         scala classes can be resolved later in the (Java) test-compile phase
                    <execution>
                        <id>scala-test-compile</id>
                        <phase>process-test-resources</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            Scala Code Style, most of the configuration done via plugin management
            <plugin>
                <groupId>org.scalastyle</groupId>
                <artifactId>scalastyle-maven-plugin</artifactId>
                <configuration>
                    <configLocation>${project.basedir}/../tools/maven/scalastyle-config.xml</configLocation>
                </configuration>
            </plugin> -->
			<!-- <plugin> <groupId>net.alchim31.maven</groupId> <artifactId>scala-maven-plugin</artifactId> 
				<executions> Run scala compiler in the process-resources phase, so that dependencies 
				on scala classes can be resolved later in the (Java) compile phase <execution> 
				<id>scala-compile-first</id> <phase>process-resources</phase> <goals> <goal>add-source</goal> 
				<goal>compile</goal> </goals> </execution> Run scala compiler in the process-test-resources 
				phase, so that dependencies on scala classes can be resolved later in the 
				(Java) test-compile phase <execution> <id>scala-test-compile</id> <phase>process-test-resources</phase> 
				<goals> <goal>testCompile</goal> </goals> </execution> </executions> </plugin> -->
			<plugin>
			    <groupId>org.apache.maven.plugins</groupId>
			    <artifactId>maven-resources-plugin</artifactId>
			    <version>2.4.3</version>
			    <configuration>
			        <encoding>${project.build.sourceEncoding}</encoding>
			    </configuration>
			    <executions>
			        <execution>
			            <id>copy-spring-boot-resources</id>
			            <!-- here the phase you need -->
			            <phase>validate</phase>
			            <goals>
			                <goal>copy-resources</goal>
			            </goals>
			            <configuration>
			                <encoding>utf-8</encoding>
			                <outputDirectory>${basedir}/src/main/resources</outputDirectory>
			                <resources>
			                    <resource>
			                        <directory>${basedir}/src/main/resources/${profiles.active}</directory>
			                        <includes>
			                            <include>qanat.properties</include>
			                        </includes>
			                        <filtering>true</filtering>
			                    </resource>
			                </resources>
			            </configuration>
			        </execution>
			    </executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.3</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<verbose>true</verbose>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
                <executions>
                    <execution>
                        <id>jar-with-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <shadedArtifactAttached>true</shadedArtifactAttached>
                            <shadedClassifierName>${profiles.active}_${maven.build.timestamp}</shadedClassifierName>
                            <createDependencyReducedPom>true</createDependencyReducedPom>
                            <dependencyReducedPomLocation>${project.basedir}/target/dependency-reduced-pom.xml
                            </dependencyReducedPomLocation>
                            <filters>
                                <!-- Globally exclude log4j.properties from our JAR files. -->
                                <filter>
                                    <artifact>*</artifact>
                                    <excludes>
                                        <exclude>log4j.properties</exclude>
                                        <exclude>log4j-test.properties</exclude>
                                        <exclude>META-INF/*.SF</exclude>  
                                        <exclude>META-INF/*.DSA</exclude>  
                                        <exclude>META-INF/*.RSA</exclude>
                                        <exclude>online/*.properties</exclude>
                                        <exclude>test/*.properties</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                            <artifactSet>
                                <includes>
                                    <!-- Unfortunately, the next line is necessary for now to force 
                                        the execution of the Shade plugin upon all sub modules. This will generate 
                                        effective poms, i.e. poms which do not contain properties which are derived 
                                        from this root pom. In particular, the Scala version properties are defined 
                                        in the root pom and without shading, the root pom would have to be Scala 
                                        suffixed and thereby all other modules. -->

                                    <include>*</include>
                                </includes>
                            </artifactSet>
                            <relocations>
                                <relocation>
	                                <pattern>okhttp3</pattern>
	                                <shadedPattern>com.aliyun.qanat.shade.okhttp3</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>okio</pattern>
                                    <shadedPattern>com.aliyun.qanat.shade.okio</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>com.alibaba.druid</pattern>
                                    <shadedPattern>com.alibaba.blink.shaded.tddl.com.alibaba.druid</shadedPattern>
                                </relocation>
                            </relocations>
                            <!--<finalName>${project.artifactId}-${project.version}-jar-with-dependencies</finalName> -->
                        </configuration>
                    </execution>
                </executions>
			</plugin>

		</plugins>
	</build>
</project>
