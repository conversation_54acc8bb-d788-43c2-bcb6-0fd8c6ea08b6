/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.mongodb;

import com.aliyun.qanat.blink.connectors.mongodb.MongodbTableFactory;
import com.aliyun.qanat.blink.connectors.mongodb.sink.MongodbSinkFunc;
import com.aliyun.qanat.blink.connectors.mongodb.sink.SinkConf;
import org.apache.flink.table.dataformat.GenericRow;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.DataTypes;

import com.mongodb.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import de.flapdoodle.embed.mongo.MongodExecutable;
import de.flapdoodle.embed.mongo.MongodProcess;
import de.flapdoodle.embed.mongo.MongodStarter;
import de.flapdoodle.embed.mongo.config.IMongodConfig;
import de.flapdoodle.embed.mongo.config.MongoCmdOptionsBuilder;
import de.flapdoodle.embed.mongo.config.MongodConfigBuilder;
import de.flapdoodle.embed.mongo.config.Net;
import de.flapdoodle.embed.mongo.config.Storage;
import de.flapdoodle.embed.mongo.distribution.Version;
import de.flapdoodle.embed.process.io.file.Files;
import de.flapdoodle.embed.process.runtime.Network;
import org.junit.After;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.Serializable;
import java.net.ServerSocket;

import static org.junit.Assert.assertEquals;

@Ignore
public class MongodbTest implements Serializable {

    private static final Logger LOG = LoggerFactory.getLogger(MongodbTest.class);

    private static final String MONGODB_LOCATION = "target/mongodb";

    private static final MongodStarter mongodStarter = MongodStarter.getDefaultInstance();

    private transient MongodExecutable mongodExecutable;
    private transient MongodProcess mongodProcess;

    private static int port;

    /** Looking for an available network port. */
    @BeforeClass
    public static void availablePort() throws Exception {
        try (ServerSocket serverSocket = new ServerSocket(0)) {
            port = serverSocket.getLocalPort();
        }
    }

    @Before
    public void setup() throws Exception {
        LOG.info("Starting MongoDB embedded instance on {}", port);
        try {
            Files.forceDelete(new File(MONGODB_LOCATION));
        } catch (Exception e) {

        }
        new File(MONGODB_LOCATION).mkdirs();
        IMongodConfig mongodConfig =
                new MongodConfigBuilder()
                        .version(Version.Main.PRODUCTION)
                        .configServer(false)
                        .replication(new Storage(MONGODB_LOCATION, null, 0))
                        .net(new Net("localhost", port, Network.localhostIsIPv6()))
                        .cmdOptions(
                                new MongoCmdOptionsBuilder()
                                        .syncDelay(10)
                                        .useNoPrealloc(true)
                                        .useSmallFiles(true)
                                        .useNoJournal(true)
                                        .build())
                        .build();
        mongodExecutable = mongodStarter.prepare(mongodConfig);
        mongodProcess = mongodExecutable.start();
    }

    @After
    public void stop() throws Exception {
        LOG.info("Stopping MongoDB instance");
        mongodProcess.stop();
        mongodExecutable.stop();
    }

    @Test
    public void testWrite() throws Exception {

        SinkConf conf = new SinkConf("test", "test", "mongodb://localhost:" + port,
                MongodbTableFactory.KEEP_ALIVE_KEY.defaultValue(),
                MongodbTableFactory.MAX_CONNECTION_IDLE_TIME_KEY.defaultValue(),
                MongodbTableFactory.BATCH_SIZE_KEY.defaultValue());
        MongodbSinkFunc func = new MongodbSinkFunc(
                conf, new String[] {"scientist"}, new DataType[] {DataTypes.STRING});
        func.open(null);

        for (int i = 0; i < 10000; i++) {
            func.invoke(GenericRow.of("Test " + i), null);
        }

        func.close();

        MongoClient client = new MongoClient("localhost", port);
        MongoDatabase database = client.getDatabase("test");
        MongoCollection collection = database.getCollection("test");

        MongoCursor cursor = collection.find().iterator();

        int count = 0;
        while (cursor.hasNext()) {
            count = count + 1;
            cursor.next();
        }

        assertEquals(10000, count);
    }
}
