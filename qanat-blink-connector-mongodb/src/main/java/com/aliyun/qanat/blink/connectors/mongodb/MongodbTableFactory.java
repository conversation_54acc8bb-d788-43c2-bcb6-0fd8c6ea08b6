/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.aliyun.qanat.blink.connectors.mongodb;

import com.alibaba.blink.streaming.connectors.common.source.SourceCollectorTableFunction;
import com.alibaba.blink.streaming.connectors.common.source.SourceUtils;
import com.alibaba.blink.table.connectors.conf.BlinkOptions;
import com.alibaba.blink.table.factories.BlinkTableFactory;

import com.aliyun.qanat.blink.connectors.mongodb.sink.MongodbSink;
import com.aliyun.qanat.blink.connectors.mongodb.sink.SinkConf;
import com.aliyun.qanat.blink.connectors.mongodb.source.MongoTableSourceParser;
import com.aliyun.qanat.blink.connectors.mongodb.source.MongodbSourceConf;
import com.aliyun.qanat.blink.connectors.mongodb.source.MongodbTableSource;
import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.api.TableSourceParser;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.table.factories.BatchCompatibleTableSinkFactory;
import org.apache.flink.table.factories.BatchTableSourceFactory;
import org.apache.flink.table.factories.StreamTableSinkFactory;

import org.apache.flink.table.factories.TableSourceParserFactory;
import org.apache.flink.table.sinks.BatchCompatibleStreamTableSink;
import org.apache.flink.table.sinks.StreamTableSink;
import org.apache.flink.table.sources.BatchTableSource;
import org.apache.flink.table.util.TableProperties;
import org.bson.Document;

import java.util.*;

import static org.apache.flink.configuration.ConfigOptions.key;
import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_PROPERTY_VERSION;
import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_TYPE;

public class MongodbTableFactory extends BlinkTableFactory
        implements StreamTableSinkFactory<BaseRow>,
        BatchTableSourceFactory<Document>,
        TableSourceParserFactory,
        BatchCompatibleTableSinkFactory<BaseRow> {

    private MongodbSink createSink(Map<String, String> props) {
        TableProperties tableProperties = new TableProperties();
        tableProperties.putProperties(props);
        String partitionBy = tableProperties.getString(BlinkOptions.PARTITION_BY);
        boolean shuffleEmptyKey = tableProperties.getBoolean(BlinkOptions.SHUFFLE_EMPTY_KEY);

        SinkConf conf = new SinkConf(
                tableProperties.getString(DATABASE_KEY),
                tableProperties.getString(COLLECTION_KEY),
                tableProperties.getString(URI_KEY),
                tableProperties.getBoolean(KEEP_ALIVE_KEY),
                tableProperties.getInteger(MAX_CONNECTION_IDLE_TIME_KEY),
                tableProperties.getInteger(BATCH_SIZE_KEY));
        MongodbSink mongoSink = new MongodbSink(conf);
        if (partitionBy != null && !partitionBy.isEmpty()) {
            mongoSink.setPartitionedField(partitionBy);
            mongoSink.setShuffleEmptyKey(shuffleEmptyKey);
        }
        return mongoSink;
    }

    private static final ConfigOption<String> DATABASE_KEY = key("database".toLowerCase()).noDefaultValue();
    private static final ConfigOption<String> COLLECTION_KEY = key("collection".toLowerCase()).noDefaultValue();
    private static final ConfigOption<String> URI_KEY = key("uri".toLowerCase()).noDefaultValue();
    static final ConfigOption<Boolean> KEEP_ALIVE_KEY = key("keepAlive".toLowerCase()).defaultValue(true);
    static final ConfigOption<Integer> MAX_CONNECTION_IDLE_TIME_KEY = key("maxConnectionIdleTime".toLowerCase()).defaultValue(60000);
    static final ConfigOption<Integer> BATCH_SIZE_KEY = key("batchSize".toLowerCase()).defaultValue(1024);

    @Override
    protected Map<String, String> requiredContextSpecific() {
        Map<String, String> context = new HashMap<>();
        context.put(CONNECTOR_TYPE, "QANAT_MONGODB"); // MONGODB
        context.put(CONNECTOR_PROPERTY_VERSION, "1"); // backwards compatibility
        return context;
    }

    @Override
    protected List<String> supportedSpecificProperties() {
        return Arrays.asList(DATABASE_KEY.key(), COLLECTION_KEY.key(), URI_KEY.key(),
                KEEP_ALIVE_KEY.key(), MAX_CONNECTION_IDLE_TIME_KEY.key(), BATCH_SIZE_KEY.key(),
                BlinkOptions.PARTITION_BY.key(), BlinkOptions.SHUFFLE_EMPTY_KEY.key());
    }

    @Override
    public BatchCompatibleStreamTableSink<BaseRow> createBatchCompatibleTableSink(Map<String, String> map) {
        return createSink(map);
    }

    @Override
    public StreamTableSink<BaseRow> createStreamTableSink(Map<String, String> map) {
        return createSink(map);
    }

    @Override
    public BatchTableSource<Document> createBatchTableSource(Map<String, String> map) {
        TableProperties tableProperties = new TableProperties();
        tableProperties.putProperties(map);

        MongodbSourceConf conf = new MongodbSourceConf(
                tableProperties.getString(DATABASE_KEY),
                tableProperties.getString(COLLECTION_KEY),
                tableProperties.getString(URI_KEY),
                tableProperties.getBoolean(KEEP_ALIVE_KEY),
                tableProperties.getInteger(MAX_CONNECTION_IDLE_TIME_KEY));

        return new MongodbTableSource(conf);
    }

    @Override
    public TableSourceParser createParser(String s,
            RichTableSchema tableSchema,
            TableProperties properties) {
        TableSourceParser tableSourceParser = SourceUtils.createParserFromDDL(tableSchema, properties, classLoader);
        if (tableSourceParser == null &&
                !properties.getBoolean(BlinkOptions.DISABLE_DEFAULT_PARSER)) {
            MongoTableSourceParser parser = new MongoTableSourceParser(tableSchema, properties);
            tableSourceParser = new TableSourceParser(
                    new SourceCollectorTableFunction<>(parser),
                    Collections.singletonList("f0"));
        }
        return  tableSourceParser;
    }
}

