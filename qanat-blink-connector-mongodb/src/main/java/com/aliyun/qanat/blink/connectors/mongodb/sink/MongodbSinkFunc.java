/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.qanat.blink.connectors.mongodb.sink;

import com.alibaba.blink.streaming.connectors.common.MetricUtils;
import com.alibaba.fastjson.JSON;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Meter;
import org.apache.flink.streaming.api.checkpoint.ListCheckpointed;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.table.runtime.conversion.DataStructureConverters;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.DataTypes;
import org.apache.flink.types.Row;

import com.mongodb.MongoClient;
import com.mongodb.MongoClientOptions;
import com.mongodb.MongoClientURI;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.UpdateOneModel;
import com.mongodb.client.model.UpdateOptions;
import com.mongodb.client.model.WriteModel;
import org.bson.Document;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import scala.Function1;

public class MongodbSinkFunc extends RichSinkFunction<BaseRow> implements ListCheckpointed<byte[]> {

    private final SinkConf conf;
    private final String[] fieldNames;
    private final DataType[] fieldTypes;

    private transient MongoClient client;
    private transient List<Document> batch;
    private transient Function1<BaseRow, Row> converter;

    private transient Meter outTps;

    public MongodbSinkFunc(SinkConf conf, String[] fieldNames, DataType[] fieldTypes) {
        this.conf = conf;
        this.fieldNames = fieldNames;
        this.fieldTypes = fieldTypes;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        client = new MongoClient(new MongoClientURI(conf.getUri(), getOptions(conf.getKeepAlive(), conf.getMaxConnectionIdleTime())));
        batch = new ArrayList<>();
        converter = (Function1) DataStructureConverters.createToExternalConverter(DataTypes.createRowType(fieldTypes));

        outTps = MetricUtils.registerOutTps(getRuntimeContext());
    }

    @Override
    public void close() throws Exception {
        flush();
        super.close();
        client.close();
        client = null;
    }

    @Override
    public void invoke(BaseRow value, Context context) throws Exception {
        outTps.markEvent();
        Row row = converter.apply(value);
        Map<String, Object> map = new HashMap<>();
        for (int i = 0; i < fieldNames.length; i++) {
            if ("_id".equalsIgnoreCase(fieldNames[i])) {
                map.put(fieldNames[i], row.getField(i));
            } else {
                if (row.getField(i) == null) {
                    map.put(fieldNames[i], null);
                } else {
                    try {
                        map.put(fieldNames[i], JSON.parse(row.getField(i).toString()));
                    } catch (Exception e) {
                        map.put(fieldNames[i], row.getField(i));
                    }
                }
            }
        }
        batch.add(new Document(map));
        if (batch.size() >= conf.getBatchSize()) {
            flush();
        }
    }

    private static MongoClientOptions.Builder getOptions(
            boolean keepAlive,
            int maxConnectionIdleTime) {
        MongoClientOptions.Builder optionsBuilder = new MongoClientOptions.Builder();
        optionsBuilder.socketKeepAlive(keepAlive).maxConnectionIdleTime(maxConnectionIdleTime);
        return optionsBuilder;
    }

    private void flush() {
        if (batch.isEmpty()) {
            return;
        }
        MongoDatabase mongoDatabase = client.getDatabase(conf.getDatabase());
        MongoCollection<Document> mongoCollection = mongoDatabase.getCollection(conf.getCollection());
        //mongoCollection.insertMany(batch);
        List<WriteModel<Document>> writes = new ArrayList<>();
        for (Document doc : batch) {
            writes.add(
                new UpdateOneModel<Document>(
                    new Document("_id", doc.get("_id")), new Document("$set", doc), new UpdateOptions().upsert(true)
                )
            );
        }
        mongoCollection.bulkWrite(writes);
        
        batch.clear();
    }

    @Override
    public List<byte[]> snapshotState(long l, long l1) throws Exception {
        try {
            flush();
        } catch (Exception e) {
            e.printStackTrace();
            throw new IOException(e.getMessage());
        }
        return null;
    }

    @Override
    public void restoreState(List<byte[]> list) throws Exception {
    }
    
    public static void main(String [] args) {

        SinkConf conf = new SinkConf("sales_pipeline_mongo_db","PublicCloud", "mongodb://root:<EMAIL>:3717", true,
            3, 1);
        MongoClient client = new MongoClient(new MongoClientURI(conf.getUri(), getOptions(conf.getKeepAlive(), conf.getMaxConnectionIdleTime())));
        List<Document> batch = new ArrayList<>();
        
        String[] fieldNames = {"_id", ""};

        Map<String, Object> map = new HashMap<>();
        map.put("_id", new Long("201804021961"));
        map.put("projectRisk", JSON.parse("[{\"riskFactor\":[\"CustomerDecision\",\"other\"]}]"));
        map.put("competitorAndBusinessVolume", JSON.parse("[{\"competitor\":\"Kingsoft\",\"ecs\":500,\"CDN\":0}]"));
        batch.add(new Document(map));
        
        MongoDatabase mongoDatabase = client.getDatabase(conf.getDatabase());
        MongoCollection<Document> mongoCollection = mongoDatabase.getCollection(conf.getCollection());
        //mongoCollection.insertMany(batch);
        List<WriteModel<Document>> writes = new ArrayList<>();
        for (Document doc : batch) {
            writes.add(
                new UpdateOneModel<Document>(
                    new Document("_id", doc.get("_id")), new Document("$set", doc), new UpdateOptions().upsert(true)
                )
            );
        }
        mongoCollection.bulkWrite(writes);
    }
}

