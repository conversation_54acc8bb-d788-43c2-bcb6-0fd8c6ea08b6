/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.qanat.blink.connectors.mongodb.source;

import com.alibaba.blink.streaming.connectors.common.MetricUtils;
import com.alibaba.blink.table.InputPartitionSource;
import com.mongodb.MongoClient;
import com.mongodb.MongoClientOptions;
import com.mongodb.MongoClientURI;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Meter;
import org.apache.flink.streaming.api.functions.source.RichParallelSourceFunction;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class MongodbSourceFunc extends RichParallelSourceFunction<Document>
        implements Serializable, InputPartitionSource {

    private static final Logger LOG = LoggerFactory.getLogger(MongodbSourceFunc.class);

    private static final long serialVersionUID = 6289824294498842748L;

    private MongodbSourceConf sourceConf;

    private transient MongoClient client;

    private transient Meter outTps;

    private int index = -1;

    private boolean isRunning = false;

    public MongodbSourceFunc(MongodbSourceConf sourceConf) {
        this.sourceConf = sourceConf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        if (getRuntimeContext().getNumberOfParallelSubtasks() > 1) {
            LOG.warn("mongo source subtask number is more than 1");
        }
        index = getRuntimeContext().getIndexOfThisSubtask();
        client = new MongoClient(new MongoClientURI(
                sourceConf.getUri(), getOptions(sourceConf.getKeepAlive(),
                sourceConf.getMaxConnectionIdleTime())));

        isRunning = true;

        outTps = MetricUtils.registerOutTps(getRuntimeContext());
    }

    @Override
    public void run(SourceContext<Document> sourceContext) throws Exception {
        if (index == 0) {

            MongoCollection<Document> collection = client.getDatabase(sourceConf.getDatabase())
                    .getCollection(sourceConf.getCollection());

            MongoCursor<Document> cursor = collection.find().iterator();
            while (isRunning && cursor.hasNext()) {

                Document document = cursor.next();
                outTps.markEvent();
                sourceContext.collect(document);
            }
        }
    }

    @Override
    public void close() throws Exception {
        client.close();
    }

    @Override
    public void cancel() {
        isRunning = false;
    }

    private static MongoClientOptions.Builder getOptions(
            boolean keepAlive,
            int maxConnectionIdleTime) {
        MongoClientOptions.Builder optionsBuilder = new MongoClientOptions.Builder();
        optionsBuilder.socketKeepAlive(keepAlive).maxConnectionIdleTime(maxConnectionIdleTime);
        return optionsBuilder;
    }

    @Override
    public List<String> getPartitionList() throws Exception {
        List<String> partitionList = new ArrayList<>();
        partitionList.add("1");
        return partitionList;
    }
}
