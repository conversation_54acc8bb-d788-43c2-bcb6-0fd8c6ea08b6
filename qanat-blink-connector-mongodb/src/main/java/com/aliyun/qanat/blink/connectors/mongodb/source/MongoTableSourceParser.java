/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.qanat.blink.connectors.mongodb.source;

import com.alibaba.blink.streaming.connectors.common.Explainable;
import com.alibaba.blink.streaming.connectors.common.RawMessage;
import com.alibaba.blink.streaming.connectors.common.source.BytesMessage;
import com.alibaba.blink.streaming.connectors.common.source.SourceCollector;
import com.alibaba.blink.streaming.connectors.common.source.SourceUtils;
import com.alibaba.blink.streaming.connectors.common.source.parse.DefaultSourceCollector;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.util.TableProperties;
import org.apache.flink.util.Collector;
import org.bson.Document;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MongoTableSourceParser implements SourceCollector<Document, BaseRow>, Explainable {

    private DefaultSourceCollector sourceCollector;

    private transient List<RawMessage> rawInput;

    public MongoTableSourceParser(
            RichTableSchema richTableSchema,
            TableProperties tableProperties) {

        sourceCollector = SourceUtils.createDefaultSourceCollector(richTableSchema, tableProperties);
    }

    @Override
    public void open(FunctionContext context) {
        sourceCollector.open(context);
        rawInput = new ArrayList<>(1);
    }

    @Override
    public void parseAndCollect(Document input, Collector<BaseRow> collector) {
        if (rawInput.size() == 1) {
            rawInput.set(0, extractBytesMessage(input));
        } else {
            rawInput.clear();
            rawInput.add(extractBytesMessage(input));
        }
        sourceCollector.parseAndCollect(rawInput, collector);
    }

    @Override
    public void close() {
        rawInput.clear();
        sourceCollector.close();
    }

    private BytesMessage extractBytesMessage(Document doc) {
        BytesMessage message = new BytesMessage();

        message.setData(doc.toJson().getBytes());

        Map<String, String> props = new HashMap<>();
//        String timestamp = doc.;
//        props.put("timestamp", timestamp);
        message.setProperties(props);
        return message;
    }

    @Override
    public String explain() {
        return this.getClass().getSimpleName();
    }

    @Override
    public TypeInformation<BaseRow> getProducedType() {
        return sourceCollector.getProducedType();
    }
}
