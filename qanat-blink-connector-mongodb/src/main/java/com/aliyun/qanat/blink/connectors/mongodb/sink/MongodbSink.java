/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.qanat.blink.connectors.mongodb.sink;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSink;
import org.apache.flink.table.connector.DefinedDistribution;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.table.sinks.BaseUpsertStreamTableSink;
import org.apache.flink.table.sinks.BatchCompatibleStreamTableSink;
import org.apache.flink.table.sinks.TableSink;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.DataTypes;
import org.apache.flink.table.types.InternalType;

import java.util.Arrays;

public class MongodbSink implements BaseUpsertStreamTableSink<BaseRow>,
        BatchCompatibleStreamTableSink<BaseRow>, DefinedDistribution {

    private final SinkConf conf;

    private String partitionedField = null;
    private boolean shuffleEmptyKey = true;

    private String[] fieldNames;
    private DataType[] fieldTypes;

    public MongodbSink(SinkConf conf) {
        this.conf = conf;
    }

    private MongodbSink copy() {
        MongodbSink sink = new MongodbSink(conf);
        sink.partitionedField = this.partitionedField;
        sink.shuffleEmptyKey = this.shuffleEmptyKey;
        return sink;
    }

    @Override
    public TableSink<BaseRow> configure(String[] fieldNames, DataType[] fieldTypes) {
        MongodbSink sink = copy();
        sink.fieldNames = fieldNames;
        sink.fieldTypes = fieldTypes;
        return sink;
    }

    public void setPartitionedField(String partitionedField) {
        this.partitionedField = partitionedField;
    }

    public void setShuffleEmptyKey(boolean shuffleEmptyKey) {
        this.shuffleEmptyKey = shuffleEmptyKey;
    }

    @Override
    public String[] getPartitionFields() {
        if (this.partitionedField == null) {
            return null;
        }
        return new String[]{this.partitionedField};
    }

    @Override
    public boolean sortLocalPartition() {
        return false;
    }

    @Override
    public boolean shuffleEmptyKey() {
        return shuffleEmptyKey;
    }

    @Override
    public void setKeyFields(String[] keys) {
    }

    @Override
    public void setIsAppendOnly(Boolean isAppendOnly) {
    }

    @Override
    public DataStreamSink<?> emitDataStream(DataStream<BaseRow> dataStream) {
        return emitBoundedStream(dataStream);
    }

    @Override
    public DataStreamSink<?> emitBoundedStream(DataStream<BaseRow> boundedStream) {
        MongodbSinkFunc sink = new MongodbSinkFunc(conf, fieldNames, fieldTypes);
        return boundedStream.addSink(sink).name(sink.toString());
    }

    @Override
    public DataType getOutputType() {
        return DataTypes.createRowType(
                Arrays.stream(fieldTypes).map(DataType::toInternalType).toArray(InternalType[]::new),
                fieldNames);
    }

    @Override
    public String[] getFieldNames() {
        return fieldNames;
    }

    @Override
    public DataType[] getFieldTypes() {
        return fieldTypes;
    }
}

