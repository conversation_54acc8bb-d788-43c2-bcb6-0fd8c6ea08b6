/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.qanat.blink.connectors.mongodb.source;

import com.alibaba.blink.streaming.connectors.common.source.SourceFunctionTableSource;
import com.alibaba.blink.streaming.connectors.common.source.UnsupportedSourceReqeustTypeException;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.bson.Document;

public class MongodbTableSource extends SourceFunctionTableSource<Document> {

    private static final long serialVersionUID = -46658498996747735L;

    private MongodbSourceFunc mongodbSourceFunc;

    private MongodbSourceConf sourceConf;

    public MongodbTableSource(MongodbSourceConf sourceConf) {
        this.sourceConf = sourceConf;
        this.mongodbSourceFunc = new MongodbSourceFunc(sourceConf);
    }

    @Override
    public String explainSource() {
        return String.format("mongo table source,db:%s,collection:%s",
                sourceConf.getDatabase(), sourceConf.getCollection());
    }

    @Override
    public SourceFunction<Document> getSourceFunction() throws UnsupportedSourceReqeustTypeException {
        return this.mongodbSourceFunc;
    }
}
