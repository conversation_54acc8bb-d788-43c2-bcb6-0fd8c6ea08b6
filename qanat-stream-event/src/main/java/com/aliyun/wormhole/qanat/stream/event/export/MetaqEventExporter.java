package com.aliyun.wormhole.qanat.stream.event.export;

import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.producer.MessageQueueSelector;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.common.message.Message;
import com.alibaba.rocketmq.common.message.MessageQueue;

import com.aliyun.wormhole.qanat.stream.event.StreamEvent;
import com.taobao.metaq.client.MetaProducer;
import com.taobao.metaq.trace.core.common.MetaQTraceConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class MetaqEventExporter implements EventExporter {

    private MetaProducer producer;
    private String topic;
    private String producerGroup;
    private String unitName;

	public MetaqEventExporter(String topic, String producerGroup, String unitName) {
		this.topic = topic;
        this.producerGroup = producerGroup;
        this.unitName = unitName;
	}
	
	@Override
	public void init() {
	    boolean success = false;
	    for (int i = 0; i < 10; i++) {
            producer = new MetaProducer(producerGroup);
            try {
                if (StringUtils.isNoneBlank(unitName)) {
                    log.info("metaq unitName:{}", unitName);
                    producer.setUnitName(unitName);
                }
                producer.start();
                log.info("metaqProducer inited");
                success = true;
                break;
            } catch (Exception e) {
                log.error("metaqProducer init failed", e);
            }
        }
	    if (!success) {
            throw new RuntimeException("metaqProducer init failed");
	    }
	}

	@Override
    public void export(StreamEvent event) {
        this.export(null, event);
    }

	@Override
	public void export(String tag, StreamEvent event) {
		export(tag, event, null);
	}

	@Override
	public void export(String tag, StreamEvent event, String shardKey) {
        log.info("export({},{},{})", tag, JSON.toJSONString(event), shardKey);
		try {
		    String pk = null;
		    if (StringUtils.isNotBlank(shardKey)) {
		    	pk = shardKey;
		    } else if (event.getPkField() != null && event.getPkField().size() > 0) {
		        List<String> pkValues = new ArrayList<>();
		        for (int i=0; i<event.getPkField().size(); i++) {
		            String pkValue = event.getFieldValues().get(event.getPkField().get(i)).getNewValue();
	                if (StringUtils.isBlank(pkValue)) {
	                    pkValue = event.getFieldValues().get(event.getPkField().get(i)).getOldValue();
	                }
	                pkValues.add(pkValue);
		        }
		        pk = StringUtils.join(pkValues, "|");
            }
            String eventJson = JSON.toJSONString(event);
            Message msg = new Message(topic, tag, pk, eventJson.getBytes());
            if (event.getTraceId() != null) {
                msg.putUserProperty(MetaQTraceConstants.TRACE_ID_KEY, event.getTraceId());
            }
            SendResult sendResult = null;
            if (StringUtils.isNotBlank(pk) && StringUtils.isNumeric(pk)) {
                sendResult = producer.send(msg, new MessageQueueSelector() {
                    @Override
                    public MessageQueue select(List<MessageQueue> mqs, Message msg, Object arg) {
                        Long id = (Long)arg;
                        Long index = id % mqs.size();
                        return mqs.get(index.intValue());
                    }
                }, Long.valueOf(pk));
            } else {
                sendResult = producer.send(msg);
            }
            log.info("SendStatus={}, sendMq={}", sendResult.getSendStatus(), eventJson);
        } catch (Exception e) {
            log.error("sendMq failed", e);
        }
	}

	@Override
	public void close() {
	    if (producer != null) {
	        producer.shutdown();
	    }
	}

	@Override
	public void export(StreamEvent event, String shardKey) {
        this.export(null, event, shardKey);
	}
}