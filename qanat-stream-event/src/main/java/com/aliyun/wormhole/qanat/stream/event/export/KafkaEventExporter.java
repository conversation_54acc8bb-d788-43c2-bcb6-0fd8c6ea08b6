package com.aliyun.wormhole.qanat.stream.event.export;

import com.alibaba.fastjson.JSON;

import com.aliyun.wormhole.qanat.stream.event.StreamEvent;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.Future;

@Slf4j
public class KafkaEventExporter implements EventExporter {

    private KafkaProducer<String, String> producer;
    private String topic;
    private String bootstrapServers;

	public KafkaEventExporter(String topic, String bootstrapServers) {
		this.topic = topic;
		this.bootstrapServers = bootstrapServers;
	}
	
	public void init() {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 30 * 1000);
        producer = new KafkaProducer<String, String>(props);
	}

    public void export(StreamEvent event) {
    	export(event, null);
    }

    public void export(StreamEvent event, String shardKey) {
        log.info("export({},{})", JSON.toJSONString(event), shardKey);
        try {
		    String pk = null;
		    if (StringUtils.isNotBlank(shardKey)) {
		    	pk = shardKey;
		    } else if (event.getPkField() != null && event.getPkField().size() > 0) {
		        List<String> pkValues = new ArrayList<>();
		        for (int i=0; i<event.getPkField().size(); i++) {
		            String pkValue = event.getFieldValues().get(event.getPkField().get(i)).getNewValue();
	                if (StringUtils.isBlank(pkValue)) {
	                    pkValue = event.getFieldValues().get(event.getPkField().get(i)).getOldValue();
	                }
	                pkValues.add(pkValue);
		        }
		        pk = StringUtils.join(pkValues, "|");
            }
		    Map<String, Object> message = new HashMap<>();
		    message.put("traceId", event.getTraceId());
		    message.put("data", JSON.toJSONString(event));
		    message.put("ts", System.currentTimeMillis());
            ProducerRecord<String, String>  kafkaMessage =  new ProducerRecord<String, String>(topic, pk, JSON.toJSONString(message));
            Future<RecordMetadata> metadataFuture = producer.send(kafkaMessage);
            RecordMetadata recordMetadata = metadataFuture.get();
            log.info("export topic:{} msg[{}] with result[{}]", this.topic, JSON.toJSONString(event), recordMetadata.toString());
        } catch (Exception e) {
            log.error("export failed", e);
        }
    }

	public void export(String tag, StreamEvent event) {
		export(event);
	}

	public void export(String tag, StreamEvent event, String shardKey) {
		export(event, shardKey);
	}
	
	public void close() {
		if (producer != null) {
			producer.close();
		}
	}
	
	public static void main(String[] args) {
		KafkaEventExporter exporter = new KafkaEventExporter("topic_test_1", "33.9.194.37:9092,33.9.194.36:9092,33.9.194.38:9092");
		exporter.init();
		StreamEvent event = new StreamEvent();
		event.setDbName("est");
		exporter.export(event);
	}
}