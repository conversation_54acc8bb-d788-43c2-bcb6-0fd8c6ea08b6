import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 简化的FlinkSqlBuilder测试
 * 验证TDD实现的核心逻辑
 */
public class FlinkSqlBuilderSimpleTest {
    
    private FlinkSqlBuilderSimple flinkSqlBuilder;
    
    @BeforeEach
    void setUp() {
        flinkSqlBuilder = new FlinkSqlBuilderSimple();
    }
    
    @Test
    void testMapToFlinkType_ShouldMapCorrectly() {
        // Given & When & Then
        assertEquals("STRING", flinkSqlBuilder.mapToFlinkType("varchar"));
        assertEquals("INT", flinkSqlBuilder.mapToFlinkType("int"));
        assertEquals("BIGINT", flinkSqlBuilder.mapToFlinkType("bigint"));
        assertEquals("TIMESTAMP(3)", flinkSqlBuilder.mapToFlinkType("datetime"));
        assertEquals("DECIMAL(18,2)", flinkSqlBuilder.mapToFlinkType("decimal"));
        assertEquals("STRING", flinkSqlBuilder.mapToFlinkType("unknown"));
    }
    
    @Test
    void testAdaptToFlinkSyntax_ShouldAdaptDateFormat() {
        // Given
        String originalSql = "SELECT DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') FROM test_table";
        
        // When
        String result = flinkSqlBuilder.adaptToFlinkSyntax(originalSql);
        
        // Then
        assertTrue(result.contains("DATE_FORMAT(create_time, 'yyyy-MM-dd HH:mm:ss')"));
    }
    
    @Test
    void testAdaptToFlinkSyntax_ShouldAddProcTime() {
        // Given
        String originalSql = "SELECT id, name FROM test_table";
        
        // When
        String result = flinkSqlBuilder.adaptToFlinkSyntax(originalSql);
        
        // Then
        assertTrue(result.contains("PROCTIME() as proc_time"));
    }
    
    @Test
    void testGenerateHologresBinlogSource_ShouldContainRequiredElements() {
        // Given
        String tableName = "test_table";
        String physicalTableName = "physical_test_table";
        String endpoint = "test_endpoint";
        
        // When
        String result = flinkSqlBuilder.generateHologresBinlogSource(tableName, physicalTableName, endpoint);
        
        // Then
        assertTrue(result.contains("CREATE TABLE " + tableName));
        assertTrue(result.contains("'connector' = 'hologres'"));
        assertTrue(result.contains("'binlog' = 'true'"));
        assertTrue(result.contains("'tablename' = '" + physicalTableName + "'"));
        assertTrue(result.contains("'endpoint' = '" + endpoint + "'"));
    }
    
    @Test
    void testGenerateFlinkInsertSql_ShouldGenerateCorrectFormat() {
        // Given
        String tableName = "target_table";
        String selectSql = "SELECT id, name FROM source_table";
        
        // When
        String result = flinkSqlBuilder.generateFlinkInsertSql(tableName, selectSql);
        
        // Then
        assertTrue(result.startsWith("INSERT INTO " + tableName));
        assertTrue(result.contains(selectSql));
    }
    
    @Test
    void testGenerateSqlHeader_ShouldContainMetadata() {
        // Given
        String jobName = "test_job";
        String modelCode = "test_model";
        
        // When
        String result = flinkSqlBuilder.generateSqlHeader(jobName, modelCode);
        
        // Then
        assertTrue(result.contains("--Author: Datatube Flink Generator"));
        assertTrue(result.contains("--JobName: " + jobName));
        assertTrue(result.contains("--ModelCode: " + modelCode));
        assertTrue(result.contains("--Architecture: Flink + Hologres"));
    }
    
    @Test
    void testGenerateUdfDefinitions_ShouldContainCommonUdfs() {
        // When
        String result = flinkSqlBuilder.generateUdfDefinitions();
        
        // Then
        assertTrue(result.contains("qanatConcat"));
        assertTrue(result.contains("qanatNvl"));
        assertTrue(result.contains("qanatDateFormat"));
        assertTrue(result.contains("CREATE TEMPORARY FUNCTION"));
    }
}

/**
 * 简化的FlinkSqlBuilder实现
 * 用于验证TDD的核心逻辑
 */
class FlinkSqlBuilderSimple {
    
    public String mapToFlinkType(String originalType) {
        switch (originalType.toLowerCase()) {
            case "varchar":
            case "string":
                return "STRING";
            case "int":
            case "integer":
                return "INT";
            case "bigint":
            case "long":
                return "BIGINT";
            case "decimal":
                return "DECIMAL(18,2)";
            case "datetime":
            case "timestamp":
                return "TIMESTAMP(3)";
            case "date":
                return "DATE";
            case "boolean":
                return "BOOLEAN";
            case "double":
                return "DOUBLE";
            case "float":
                return "FLOAT";
            default:
                return "STRING";
        }
    }
    
    public String adaptToFlinkSyntax(String originalSql) {
        String adaptedSql = originalSql;
        
        // 处理时间函数
        adaptedSql = adaptedSql.replaceAll("DATE_FORMAT\\(([^,]+),'%Y-%m-%d %H:%i:%s'\\)", 
                                          "DATE_FORMAT($1, 'yyyy-MM-dd HH:mm:ss')");
        
        // 添加处理时间字段
        if (!adaptedSql.contains("PROCTIME()")) {
            adaptedSql = adaptedSql.replaceFirst("SELECT", "SELECT PROCTIME() as proc_time,");
        }
        
        return adaptedSql;
    }
    
    public String generateHologresBinlogSource(String tableName, String physicalTableName, String endpoint) {
        return String.format(
            "CREATE TABLE %s (\n" +
            "    id BIGINT,\n" +
            "    name STRING,\n" +
            "    PRIMARY KEY (id) NOT ENFORCED\n" +
            ") WITH (\n" +
            "    'connector' = 'hologres',\n" +
            "    'tablename' = '%s',\n" +
            "    'endpoint' = '%s',\n" +
            "    'binlog' = 'true',\n" +
            "    'binlog.scan.startup.mode' = 'latest'\n" +
            ");\n",
            tableName, physicalTableName, endpoint
        );
    }
    
    public String generateFlinkInsertSql(String tableName, String selectSql) {
        return String.format("INSERT INTO %s\n%s", tableName, selectSql);
    }
    
    public String generateSqlHeader(String jobName, String modelCode) {
        return String.format(
            "--SQL\n" +
            "--********************************************************************--\n" +
            "--Author: Datatube Flink Generator\n" +
            "--CreateTime: %s\n" +
            "--JobName: %s\n" +
            "--ModelCode: %s\n" +
            "--Architecture: Flink + Hologres\n" +
            "--********************************************************************--\n\n",
            new java.util.Date().toString(), jobName, modelCode
        );
    }
    
    public String generateUdfDefinitions() {
        return "-- UDF函数定义\n" +
               "CREATE TEMPORARY FUNCTION IF NOT EXISTS qanatConcat AS 'com.aliyun.wormhole.qanat.flink.udf.QanatConcatUdf';\n" +
               "CREATE TEMPORARY FUNCTION IF NOT EXISTS qanatNvl AS 'com.aliyun.wormhole.qanat.flink.udf.QanatNvlUdf';\n" +
               "CREATE TEMPORARY FUNCTION IF NOT EXISTS qanatDateFormat AS 'com.aliyun.wormhole.qanat.flink.udf.QanatDateFormatUdf';\n\n";
    }
}
