<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~    http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!-- NOTE: This was taken and adapted from Apache Spark. -->

<!-- If you wish to turn off checking for a section of code, you can put a comment in the source
 before and after the section, with the following syntax: -->
<!-- // scalastyle:off -->
<!-- ... -->
<!-- // naughty stuff -->
<!-- ... -->
<!-- // scalastyle:on -->

<scalastyle>
 <name>Scalastyle standard configuration</name>
 <check level="error" class="org.scalastyle.file.FileTabChecker" enabled="true"></check>
 <!-- <check level="error" class="org.scalastyle.file.FileLengthChecker" enabled="true"> -->
 <!--  <parameters> -->
 <!--   <parameter name="maxFileLength"><![CDATA[800]]></parameter> -->
 <!--  </parameters> -->
 <!-- </check> -->
 <check level="error" class="org.scalastyle.file.HeaderMatchesChecker" enabled="true">
  <parameters>
      <parameter name="header"><![CDATA[/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */]]></parameter>
  </parameters>
 </check>
 <check level="error" class="org.scalastyle.scalariform.SpacesAfterPlusChecker" enabled="true"></check>
 <check level="error" class="org.scalastyle.file.WhitespaceEndOfLineChecker" enabled="false"></check>
 <check level="error" class="org.scalastyle.scalariform.SpacesBeforePlusChecker" enabled="true"></check>
 <check level="error" class="org.scalastyle.file.FileLineLengthChecker" enabled="true">
  <parameters>
   <parameter name="maxLineLength"><![CDATA[100]]></parameter>
   <parameter name="tabSize"><![CDATA[2]]></parameter>
   <parameter name="ignoreImports">true</parameter>
  </parameters>
 </check>
 <check level="error" class="org.scalastyle.scalariform.ClassNamesChecker" enabled="true">
  <parameters>
   <parameter name="regex"><![CDATA[[A-Z][A-Za-z]*]]></parameter>
  </parameters>
 </check>
 <check level="error" class="org.scalastyle.scalariform.ObjectNamesChecker" enabled="true">
  <parameters>
   <parameter name="regex"><![CDATA[[A-Z][A-Za-z]*]]></parameter>
  </parameters>
 </check>
 <check level="error" class="org.scalastyle.scalariform.PackageObjectNamesChecker" enabled="true">
  <parameters>
   <parameter name="regex"><![CDATA[^[a-z][A-Za-z]*$]]></parameter>
  </parameters>
 </check>
 <check level="error" class="org.scalastyle.scalariform.EqualsHashCodeChecker" enabled="false"></check>
 <!-- <check level="error" class="org.scalastyle.scalariform.IllegalImportsChecker" enabled="true"> -->
 <!--  <parameters> -->
 <!--   <parameter name="illegalImports"><![CDATA[sun._,java.awt._]]></parameter> -->
 <!--  </parameters> -->
 <!-- </check> -->
 <check level="error" class="org.scalastyle.scalariform.ParameterNumberChecker" enabled="true">
  <parameters>
   <parameter name="maxParameters"><![CDATA[15]]></parameter>
  </parameters>
 </check>
 <!-- <check level="error" class="org.scalastyle.scalariform.MagicNumberChecker" enabled="true"> -->
 <!--  <parameters> -->
 <!--   <parameter name="ignore"><![CDATA[-1,0,1,2,3]]></parameter> -->
 <!--  </parameters> -->
 <!-- </check> -->
 <check level="error" class="org.scalastyle.scalariform.NoWhitespaceBeforeLeftBracketChecker" enabled="false"></check>
 <check level="error" class="org.scalastyle.scalariform.NoWhitespaceAfterLeftBracketChecker" enabled="false"></check>
 <!-- <check level="error" class="org.scalastyle.scalariform.ReturnChecker" enabled="true"></check> -->
 <!-- <check level="error" class="org.scalastyle.scalariform.NullChecker" enabled="true"></check> -->
 <!-- <check level="error" class="org.scalastyle.scalariform.NoCloneChecker" enabled="true"></check> -->
 <!-- <check level="error" class="org.scalastyle.scalariform.NoFinalizeChecker" enabled="true"></check> -->
 <!-- <check level="error" class="org.scalastyle.scalariform.CovariantEqualsChecker" enabled="true"></check> -->
 <!-- <check level="error" class="org.scalastyle.scalariform.StructuralTypeChecker" enabled="true"></check> -->
 <!-- <check level="error" class="org.scalastyle.file.RegexChecker" enabled="true"> -->
 <!--  <parameters> -->
 <!--   <parameter name="regex"><![CDATA[println]]></parameter> -->
 <!--  </parameters> -->
 <!-- </check> -->
 <!-- <check level="error" class="org.scalastyle.scalariform.NumberOfTypesChecker" enabled="true"> -->
 <!--  <parameters> -->
 <!--   <parameter name="maxTypes"><![CDATA[30]]></parameter> -->
 <!--  </parameters> -->
 <!-- </check> -->
 <!-- <check level="error" class="org.scalastyle.scalariform.CyclomaticComplexityChecker" enabled="true"> -->
 <!--  <parameters> -->
 <!--   <parameter name="maximum"><![CDATA[10]]></parameter> -->
 <!--  </parameters> -->
 <!-- </check> -->
 <check level="error" class="org.scalastyle.scalariform.UppercaseLChecker" enabled="true"></check>
 <check level="error" class="org.scalastyle.scalariform.SimplifyBooleanExpressionChecker" enabled="false"></check>
 <check level="error" class="org.scalastyle.scalariform.IfBraceChecker" enabled="true">
  <parameters>
   <parameter name="singleLineAllowed"><![CDATA[true]]></parameter>
   <parameter name="doubleLineAllowed"><![CDATA[true]]></parameter>
  </parameters>
 </check>
 <!-- <check level="error" class="org.scalastyle.scalariform.MethodLengthChecker" enabled="true"> -->
 <!--  <parameters> -->
 <!--   <parameter name="maxLength"><![CDATA[50]]></parameter> -->
 <!--  </parameters> -->
 <!-- </check> -->
 <!-- <check level="error" class="org.scalastyle.scalariform.MethodNamesChecker" enabled="true"> -->
 <!--  <parameters> -->
 <!--   <parameter name="regex"><![CDATA[^[a-z][A-Za-z0-9]*$]]></parameter> -->
 <!--  </parameters> -->
 <!-- </check> -->
 <!-- <check level="error" class="org.scalastyle.scalariform.NumberOfMethodsInTypeChecker" enabled="true"> -->
 <!--  <parameters> -->
 <!--   <parameter name="maxMethods"><![CDATA[30]]></parameter> -->
 <!--  </parameters> -->
 <!-- </check> -->
 <!-- <check level="error" class="org.scalastyle.scalariform.PublicMethodsHaveTypeChecker" enabled="true"></check> -->
 <check level="error" class="org.scalastyle.file.NewLineAtEofChecker" enabled="true"></check>
 <check level="error" class="org.scalastyle.file.NoNewLineAtEofChecker" enabled="false"></check>
</scalastyle>
