package com.alibaba.blink.connectors.rds;

import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.core.io.InputSplit;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.types.InternalType;
import org.apache.flink.table.types.RowType;
import org.apache.flink.table.util.TableProperties;

import org.apache.flink.shaded.curator.org.apache.curator.framework.CuratorFramework;

import com.alibaba.blink.connectors.rds.source.MysqlBinlogSource;
import com.alibaba.blink.streaming.connector.zookeeper.utils.ZKUtils;
import com.alibaba.blink.streaming.connectors.common.errcode.ConnectorErrors;
import com.alibaba.blink.streaming.connectors.common.exception.UnsupportedTableException;
import com.alibaba.blink.streaming.connectors.common.reader.MonotonyIncreaseProgress;
import com.alibaba.blink.streaming.connectors.common.source.AbstractParallelSource;
import com.alibaba.blink.streaming.connectors.common.util.TestingServerFactory;
import com.alibaba.blink.table.connectors.conf.BlinkOptions;
import org.apache.curator.test.TestingServer;
import org.junit.Assert;
import org.junit.Test;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ZKUtilTest {
	protected TestingServer _zkServer;

	@Test
	public void testABTest() throws Exception {
		//ready
		TestingServerFactory.TestingServerInstance instance =  TestingServerFactory.createAndStartNewTestServer();
		int port = instance.getBindingPort();
		_zkServer = instance.getServer();
		Configuration conf = new Configuration();
		conf.setString(BlinkOptions.INNER_SAMPLE_PROJECT, "blink_user_daily_test");
		conf.setString(BlinkOptions.INNER_SAMPLE_JOB_NAME, "abtest_tt");
		conf.setString(BlinkOptions.INNER_SAMPLE_ZK_QUORUM, "localhost:" + port);
		ZKUtils.conf = conf;

		//ready to upgrade
		ZKUtils.getInstance().initSourceTable("test");
		ZKUtils.getInstance().setSourceFlag("0");
		Assert.assertEquals("0", ZKUtils.getInstance().getSourceFlag());

		ZKUtils.getInstance().setUpgrade("1");
		Assert.assertEquals("1", ZKUtils.getInstance().getUpgradeData());

		TableProperties tableProperties = new TableProperties();
		RichTableSchema richTableSchema = new RichTableSchema(new String[]{}, new InternalType[]{});
		String blinkEnvironmentType = tableProperties.getString(BlinkOptions.BLINK_ENVIRONMENT_TYPE_KEY);
		boolean isBatchMode = BlinkOptions.BLINK_ENVIRONMENT_BATCH_VALUE
				.equalsIgnoreCase(blinkEnvironmentType) ? true : false;
		if (isBatchMode) {
			throw new UnsupportedTableException(ConnectorErrors.INST.unsupportedSourceError("Rds"));
		}
		RowType rowType = richTableSchema.getResultType();
		String host = tableProperties.getString(BlinkOptions.MYSQLBINLOG.HOST);
		String user = tableProperties.getString(BlinkOptions.MYSQLBINLOG.USER_NAME);
		int dbPort = tableProperties.getInteger(BlinkOptions.MYSQLBINLOG.PORT);
		String password = tableProperties.getString(BlinkOptions.MYSQLBINLOG.PASSWORD);
		int cacheSize = tableProperties.getInteger(BlinkOptions.MYSQLBINLOG.CACHE_SIZE);
		String dbName = tableProperties.getString(BlinkOptions.MYSQLBINLOG.DB_NAME);
		String tableName = tableProperties.getString(BlinkOptions.MYSQLBINLOG.TABLE_NAME);
		AbstractParallelSource source = new MysqlBinlogSource(rowType, host, dbPort, user, password, dbName, tableName, cacheSize, false);
		List<Tuple2<InputSplit, MonotonyIncreaseProgress>> offsetData = new ArrayList<>();
		offsetData.add(Tuple2.of(new InputSplit() {
			@Override
			public int getSplitNumber() {
				return 0;
			}
		}, new MonotonyIncreaseProgress().add("", 10L)));
		Whitebox.setInternalState(source, "offsetData", offsetData);
		Whitebox.setInternalState(source, "tableName", "test");

		//normal cp
		source.notifyCheckpointComplete(0);
		AbstractParallelSource.STATE state = Whitebox.getInternalState(source, "sourceState");
		Assert.assertEquals(AbstractParallelSource.STATE.NORMAL_STATE, state);

		Map<String/*tableName*/, ZKUtils.ZKNode> tableNameToZKNode = Whitebox.getInternalState(ZKUtils.getInstance(), "tableNameToZKNode");
		ZKUtils.ZKNode zkNode = tableNameToZKNode.get("test");
		Map<String, String> inputSplitPaths = Whitebox.getInternalState(zkNode, "inputSplitPaths");
		CuratorFramework client = Whitebox.getInternalState(ZKUtils.getInstance(), "client");
		String offset = new String(client.getData().forPath(inputSplitPaths.get("0")));
		Assert.assertEquals(Long.toString(10L), offset);

		//block runner
		ZKUtils.getInstance().setSourceFlag("1");
		source.notifyCheckpointComplete(1);
		state = Whitebox.getInternalState(source, "sourceState");
		Assert.assertEquals(AbstractParallelSource.STATE.BLOCK_STATE, state);

		offset = new String(client.getData().forPath(inputSplitPaths.get("0")));
		Assert.assertEquals("-1", offset);
		_zkServer.close();
	}
}
