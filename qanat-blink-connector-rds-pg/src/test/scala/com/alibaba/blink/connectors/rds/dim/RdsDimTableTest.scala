/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.blink.connectors.rds.dim

import org.apache.flink.api.scala._
import org.apache.flink.streaming.api.scala.StreamExecutionEnvironment
import org.apache.flink.table.api.{RichTableSchema, TableEnvironment}
import org.apache.flink.table.api.scala.{StreamTableEnvironment, _}
import org.apache.flink.table.runtime.utils.TestingAppendSink
import org.apache.flink.table.types.DataTypes
import org.apache.flink.types.Row

import com.alibaba.blink.connectors.rds.RdsTableSource
import com.alibaba.blink.streaming.connectors.common.util.ConnectionPool
import com.alibaba.blink.table.cache.{CacheConfig, CacheStrategy}
import org.junit.Assert.assertEquals
import org.junit.{Before, Ignore, Test}

import java.util.Collections

@Ignore
class RdsDimTableTest {
  var env: StreamExecutionEnvironment = _
  var tEnv: StreamTableEnvironment = _

  @Before
  def start(): Unit = {
    env = StreamExecutionEnvironment.getExecutionEnvironment
    tEnv = TableEnvironment.getTableEnvironment(env)


    val param = new RdsConnectionParam()
      .setUrl("*************************************")
      .setUserName("test")
      .setPassword("123456")
      .setTableName("white_list")

    val schema = new RichTableSchema(Array("id", "name", "age"),
      Array(DataTypes.INT, DataTypes.STRING, DataTypes.INT))
    schema.setPrimaryKey("id")

    // 1  Jark  11
    // 2  Julian  22
    // 3  Lisa  33
    // 4  Jim  44
    // 5  Fabian  55
    // 7  Xi  77
    val rds = new RdsTableSource(
      null,
      "rds",
      schema,
      param,
      new CacheConfig(CacheStrategy.neverExpired(10, false)))

    val rds_cache_all = new RdsTableSource(
      null,
      "rds_cache_all",
      schema,
      param,
      new CacheConfig(CacheStrategy.all(), Collections.emptyList(), 1))

    tEnv.registerTableSource("rds", rds)
    tEnv.registerTableSource("rds_cache_all", rds_cache_all)

    val data = List(
      ("Jark", 1),
      ("Julian", 2),
      ("Lisa", 3),
      ("Jim", 4),
      ("Timo", 5),
      ("Deng", 6))

    val ds = env.fromCollection(data)
    tEnv.registerDataStream("MyTable", ds, 'content, 'id)
  }

  @Test
  def testRdsDimTable(): Unit = {
    val sql =
      """
        |SELECT T.id, T.content, R.name, R.age
        |FROM MyTable AS T
        |LEFT JOIN rds FOR SYSTEM_TIME AS OF PROCTIME() AS R
        |ON T.id = R.id AND T.content = R.name
      """.stripMargin

    val result = tEnv.sqlQuery(sql)
    val sink = new TestingAppendSink
    tEnv.toAppendStream[Row](result).addSink(sink)

    env.execute()

    val expected = List(
      "1,Jark,Jark,11",
      "2,Julian,Julian,22",
      "3,Lisa,Lisa,33",
      "4,Jim,Jim,44",
      "5,Timo,null,null",
      "6,Deng,null,null")

    assertEquals(expected.sorted, sink.getAppendResults.sorted)
    val field = classOf[RdsRowFetcher].getDeclaredField("dataSourcePool")
    field.setAccessible(true)
    val dataSourcePool = field
      .get(null)
      .asInstanceOf[ConnectionPool[_]]

    assertEquals(0, dataSourcePool.size())
  }

  @Test
  def testRdsDimTableWithCacheAll(): Unit = {
    val sql =
      """
        |SELECT T.id, T.content, R.name, R.age
        |FROM MyTable AS T
        |LEFT JOIN rds_cache_all FOR SYSTEM_TIME AS OF PROCTIME() AS R
        |ON T.id = R.id AND T.content = R.name
      """.stripMargin

    val result = tEnv.sqlQuery(sql)
    val sink = new TestingAppendSink
    tEnv.toAppendStream[Row](result).addSink(sink)

    env.execute()

    val expected = List(
      "1,Jark,Jark,11",
      "2,Julian,Julian,22",
      "3,Lisa,Lisa,33",
      "4,Jim,Jim,44",
      "5,Timo,null,null",
      "6,Deng,null,null")

    assertEquals(expected.sorted, sink.getAppendResults.sorted)
    val field = classOf[RdsRowFetcher].getDeclaredField("dataSourcePool")
    field.setAccessible(true)
    val dataSourcePool = field
      .get(null)
      .asInstanceOf[ConnectionPool[_]]

    assertEquals(0, dataSourcePool.size())
  }
}
