/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.rds

import org.apache.flink.api.java.tuple.Tuple3
import org.apache.flink.api.java.typeutils.RowTypeInfo
import org.apache.flink.api.scala._
import org.apache.flink.streaming.api.scala.{DataStream, StreamExecutionEnvironment}
import org.apache.flink.table.api.scala.{BatchTableEnvironment, _}
import org.apache.flink.table.api.{RichTableSchema, TableConfig, TableEnvironment, Types}
import org.apache.flink.table.runtime.batch.sql.BatchTestBase.row
import org.apache.flink.table.types.DataTypes

import com.alibaba.blink.streaming.connectors.common.source.parse.DirtyDataStrategy
import org.junit.{Ignore, Test}

import java.util
import java.util.Collections

import scala.collection.JavaConversions._
import scala.language.implicitConversions



class RdsTableSinkTest {
  def getSmall3TupleDataSet(env: StreamExecutionEnvironment): DataStream[Tuple3[Integer, Integer,
    String]] = {
    val data: util.List[Tuple3[Integer, Integer, String]] = new util.ArrayList[Tuple3[Integer,
      Integer,
      String]]
    data.add(new Tuple3[Integer, Integer, String](1, 1, "Hi"))
    data.add(new Tuple3[Integer, Integer, String](2, 1, "Hello\\\\"))
    data.add(new Tuple3[Integer, Integer, String](4, 3, "Hello world!\\"))

    Collections.shuffle(data)
    env.fromCollection(data)
  }

  /**
    * get batch source data
    *
    * @param tEnv
    */
  def getBatchData(tEnv: BatchTableEnvironment): Unit = {
    val smallData3 = Seq(
      row(1, 1, "Hi"),
      row(4, 2, "Hello"),
      row(3, 5, "Hello world"),
      row(30, 20, "Hello world"),
      row(30, 20, "my world")
    )
    val type3 = new RowTypeInfo(Types.INT, Types.INT, Types.STRING)
    tEnv.registerCollection("sourceTable", smallData3, type3, 'a, 'b, 'c)
  }

  @Ignore
  @Test
  def testRdsTableSink(): Unit = {
    val env = StreamExecutionEnvironment.getExecutionEnvironment
    val tEnv = TableEnvironment.getTableEnvironment(env)
    val ds = getSmall3TupleDataSet(env)

    val in = tEnv.fromDataStream(ds, 'a, 'b, 'c)

    val schema = new RichTableSchema(
      Array("id", "len", "content"),
      Array(
        DataTypes.INT,
        DataTypes.INT,
        DataTypes.STRING))

    val pkList=new util.ArrayList[String]()
    pkList.add("id")
    pkList.add("len")

    val rdsOutputFormatBuilder = new RdsOutputFormat.Builder()
    rdsOutputFormatBuilder.setUrl("*************************************")
      .setUserName("test")
      .setPassword("123456")
      .setTableName("test2")
      .setDirtyDataStrategy(DirtyDataStrategy.EXCEPTION)
     //     .setPkField(pkList)
    val sink = new RdsTableSink(rdsOutputFormatBuilder, schema)

    tEnv.registerTableSink(
      "dest",
      sink.getFieldNames,
      sink.getFieldTypes,
      sink)

    val res = in.select('a, 'b, 'c)
    tEnv.registerTable("sourceTable", res)
    res.writeToSink(sink)

    //    val sql = "INSERT INTO dest SELECT a, b,c FROM sourceTable"

    //    val t = tEnv.sqlQuery(sql)
    //    assertNull(t)
    env.execute()
  }

  @Ignore
  @Test
  def testRdsTableBatchSink(): Unit = {
    val conf = new TableConfig
    val env: StreamExecutionEnvironment = StreamExecutionEnvironment.getExecutionEnvironment
    val tEnv: BatchTableEnvironment = TableEnvironment.getBatchTableEnvironment(env, conf)

    getBatchData(tEnv)

    val schema = new RichTableSchema(
      Array("id", "len", "content"),
      Array(
        DataTypes.INT,
        DataTypes.INT,
        DataTypes.STRING))

    var pkList = new util.ArrayList[String]()
    pkList.add("id")

    val rdsOutputFormatBuilder = new RdsOutputFormat.Builder()
    rdsOutputFormatBuilder.setUrl("***************************************")
      .setUserName("galaxy")
      .setPassword("galaxy")
      .setTableName("rds_result")
      .setDirtyDataStrategy(DirtyDataStrategy.EXCEPTION)
      .setPkField(pkList)
    val sink = new RdsTableSink(rdsOutputFormatBuilder, schema)
    tEnv.registerTableSink(
      "dest",
      sink.getFieldNames,
      sink.getFieldTypes,
      sink)

    val res = tEnv.sqlQuery("select * from sourceTable")
    res.writeToSink(sink)
    tEnv.execute("testRdsOutput")
  }
}
