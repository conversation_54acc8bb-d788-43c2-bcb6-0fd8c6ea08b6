/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.blink.connectors.rds

import org.apache.flink.api.scala._
import org.apache.flink.streaming.api.scala.{DataStream, StreamExecutionEnvironment}
import org.apache.flink.table.api.{RichTableSchema, TableEnvironment}
import org.apache.flink.table.api.scala._
import org.apache.flink.table.types.{DataType, DataTypes, RowType}
import org.apache.flink.table.util.TableProperties

import com.alibaba.blink.table.connectors.conf.BlinkOptions
import com.alibaba.blink.table.util.BlinkTableFactoryUtil
import org.junit.Assert.{assertEquals, assertTrue}
import org.junit.Test

class RdsTableFactoryTest {

  @Test
  def testCreateRdsTableSink(): Unit = {
    val schema = new RichTableSchema(
      Array("id", "len", "content"),
      Array(
        DataTypes.INT,
        DataTypes.INT,
        DataTypes.STRING))

    schema.setPrimaryKey("id")

    val kvParams: TableProperties = new TableProperties()
    kvParams.setString("url", "********************************")
    kvParams.setString("tableName", "test")
    kvParams.setString("userName", "test")
    kvParams.setString("password", "123456")
    kvParams.putTableNameIntoProperties("rds")
    kvParams.putSchemaIntoProperties(schema)
    kvParams.setString(BlinkOptions.CONNECTOR_GROUP, BlinkOptions.CONNECTOR_GROUP_VALUE_BLINK)

    val data = List(
      (1, 2, "Hi"),
      (2, 5, "Hello"),
      (3, 6, "Hello!"))

    val env = StreamExecutionEnvironment.getExecutionEnvironment
    val tEnv = TableEnvironment.getTableEnvironment(env)
    val stream: DataStream[(Int, Int, String)] = env.fromCollection(data)
    val streamTable = stream.toTable(tEnv, 'id, 'len, 'content)
    tEnv.registerTable("source_t1", streamTable)
    tEnv.connect(BlinkTableFactoryUtil.getSimpleDescriptor("RDS", kvParams))
      .registerTableSink("sink_t1")

    tEnv.sqlUpdate("insert into sink_t1 select id, len, content from source_t1")
  }


  @Test
  def testCreateRdsDimTableSource(): Unit = {
    val schema = new RichTableSchema(
      Array("id", "name"),
      Array(DataTypes.INT, DataTypes.STRING))
    schema.setPrimaryKey("id")

    val kvParams: TableProperties = new TableProperties()
    kvParams.setString("url", "********************************")
    kvParams.setString("tableName", "test")
    kvParams.setString("userName", "test")
    kvParams.setString("password", "123456")
    kvParams.putTableNameIntoProperties("rds")
    kvParams.putSchemaIntoProperties(schema)
    kvParams.setString(BlinkOptions.CONNECTOR_GROUP, BlinkOptions.CONNECTOR_GROUP_VALUE_BLINK)

    val env = StreamExecutionEnvironment.getExecutionEnvironment
    val tEnv = TableEnvironment.getTableEnvironment(env)
    val otsDim = BlinkTableFactoryUtil.findAndCreateTableSource(tEnv,
      BlinkTableFactoryUtil.getSimpleDescriptor("RDS", kvParams))
    val rowTypeInfo = DataTypes.createRowTypeV2(
      Array(DataTypes.INT, DataTypes.STRING).toArray[DataType],
      Array("id", "name"))
    val expectedRowType = otsDim.getReturnType.toInternalType.asInstanceOf[RowType]
    assertTrue(otsDim.isInstanceOf[RdsTableSource])
    assertEquals(rowTypeInfo.getArity, expectedRowType.getArity)
    assertEquals(rowTypeInfo.getFieldNames()(0), expectedRowType.getFieldNames()(0))
    assertEquals(rowTypeInfo.getFieldNames()(1), expectedRowType.getFieldNames()(1))
    assertEquals(rowTypeInfo.getFieldTypes()(0), expectedRowType.getFieldTypes()(0))
    assertEquals(rowTypeInfo.getFieldTypes()(1), expectedRowType.getFieldTypes()(1))
  }

}
