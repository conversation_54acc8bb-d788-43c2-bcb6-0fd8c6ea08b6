/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.rds

import java.util.Properties
import com.alibaba.blink.launcher.util.JobBuildHelper
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment
import org.apache.flink.table.api.{TableConfigOptions, TableEnvironment}
import org.apache.flink.table.util.NodeResourceUtil.InferMode

import org.junit.{Ignore, Test}

@Ignore
class RdsBinlogTest {

  import RdsBinlogTest._
  @Test
  def testBinlog(): Unit = {
    executeDDL(
      "CREATE table rds (" +
        "a int, b boolean, c int, d varbinary, e varchar, g varbinary, h VARBINARY," +
        "j time, k date, l timestamp, m double, n double, o decimal(38, 18)) with(" +
        "type = 'rds', " +
        "dbName= 'bayes', " +
        "tableName = 'allkinds', " +
        "host = 'rm-uf6ktr4y6p8xd04xsgo.mysql.rds.aliyuncs.com'," +
        "userName = 'bayes', " +
        "password = 'bayesTest123', includeDelete = 'true'" +
        "); " +
      "CREATE table sink (" +
          "a int, b boolean, c int, d varbinary, e varchar, g varbinary, h VARBINARY," +
          "j time, k date, l timestamp, m double, n double, o decimal(38, 18)) with(" +
          "type = 'print');" +
      "insert into sink select * from rds")
    tEnv.execute()

  }
}

object RdsBinlogTest {
  val env = StreamExecutionEnvironment.getExecutionEnvironment
  val tEnv = TableEnvironment.getTableEnvironment(env)
  tEnv.getConfig.getConf.setString(TableConfigOptions.SQL_RESOURCE_INFER_MODE,
    InferMode.ONLY_SOURCE.toString)
  tEnv.getConfig.getConf.setInteger(TableConfigOptions.SQL_RESOURCE_DEFAULT_PARALLELISM, 5)
  tEnv.getConfig.getConf.setInteger(
    TableConfigOptions.SQL_RESOURCE_INFER_SOURCE_PARALLELISM_MAX, 1)
  def executeDDL(sql:String): Unit = {
    JobBuildHelper.buildSqlJobByString(
      true,
      getClass().getClassLoader(),
      new Properties(),
      tEnv,
      sql)
  }

  def sql(sql:String): Unit = {
    tEnv.sqlUpdate(sql)
  }


}
