/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.blink.connectors.rds.dim;

import com.alibaba.blink.connectors.mysql.dim.AbstractMySqlCacheAllRowFetcher;
import com.alibaba.blink.streaming.connectors.common.errcode.ConnectorErrors;
import com.alibaba.blink.streaming.connectors.common.reload.CacheAllReloadConf;
import com.alibaba.blink.streaming.connectors.common.util.ConnectionPool;

import org.apache.flink.table.plan.schema.IndexKey;
import org.apache.flink.table.typeutils.BaseRowTypeInfo;

import com.alibaba.druid.pool.DruidDataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;

public class RdsCacheAllRowFetcher extends AbstractMySqlCacheAllRowFetcher {
	private static final long serialVersionUID = 8167579100537770573L;

	private static Logger LOG = LoggerFactory.getLogger(RdsCacheAllRowFetcher.class);

	private static final ConnectionPool<DruidDataSource> dataSourcePool = new ConnectionPool<>();

	private final RdsConnectionParam param;
	private transient DruidDataSource dataSource = null;

	public RdsCacheAllRowFetcher(
			String sqlTableName,
			IndexKey index,
			CacheAllReloadConf reloadConf,
			BaseRowTypeInfo rowTypeInfo,
			RdsConnectionParam param) {

		super(sqlTableName, param.getTableName(), index, reloadConf, rowTypeInfo, param.getMaxRetryTime());
		this.param = param;
		setMaxFetchResults(param.getMaxFetchResult());
	}

	protected Connection connectToTable() {
		try {
			synchronized (RdsRowFetcher.class) {
				if (dataSourcePool.contains(sqlTableName)) {
					dataSource = dataSourcePool.get(sqlTableName);
				} else {
					dataSource = param.buildDataSource();
					dataSourcePool.put(sqlTableName, dataSource);
				}
				return dataSource.getConnection();
			}
		} catch (Exception e) {
			LOG.error("Exception while creating connection to RDS.", e);
			throw new RuntimeException(ConnectorErrors.INST.rdsGetConnectionError("RDS"), e);
		}
	}

	@Override
	protected void closeDataSource() {
		if (dataSourcePool.remove(sqlTableName) && dataSource != null) {
			dataSource.close();
		}
	}
}
