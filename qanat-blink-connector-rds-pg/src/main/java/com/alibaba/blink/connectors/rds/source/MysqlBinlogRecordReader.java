/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.rds.source;

import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.core.io.InputSplit;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.table.dataformat.BinaryString;
import org.apache.flink.table.dataformat.GenericRow;
import org.apache.flink.table.dataformat.util.BaseRowUtil;
import org.apache.flink.table.runtime.conversion.DataStructureConverters;
import org.apache.flink.table.types.DataTypes;
import org.apache.flink.table.types.RowType;

import com.alibaba.blink.streaming.connectors.common.reader.MonotonyIncreaseProgress;
import com.alibaba.blink.streaming.connectors.common.reader.RecordReader;
import com.github.shyiko.mysql.binlog.BinaryLogClient;
import com.github.shyiko.mysql.binlog.event.DeleteRowsEventData;
import com.github.shyiko.mysql.binlog.event.Event;
import com.github.shyiko.mysql.binlog.event.EventHeader;
import com.github.shyiko.mysql.binlog.event.EventType;
import com.github.shyiko.mysql.binlog.event.TableMapEventData;
import com.github.shyiko.mysql.binlog.event.UpdateRowsEventData;
import com.github.shyiko.mysql.binlog.event.WriteRowsEventData;
import com.github.shyiko.mysql.binlog.event.deserialization.DeleteRowsEventDataDeserializer;
import com.github.shyiko.mysql.binlog.event.deserialization.EventDataDeserializer;
import com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer;
import com.github.shyiko.mysql.binlog.event.deserialization.EventHeaderV4Deserializer;
import com.github.shyiko.mysql.binlog.event.deserialization.NullEventDataDeserializer;
import com.github.shyiko.mysql.binlog.event.deserialization.TableMapEventDataDeserializer;
import com.github.shyiko.mysql.binlog.event.deserialization.UpdateRowsEventDataDeserializer;
import com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer;

import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.BitSet;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;

/**
 * MySQL binlog record reader.
 */
public class MysqlBinlogRecordReader implements RecordReader<BaseRow, MysqlBinlogPosition> {

	private RowType rowType;
	private String host;
	private int port;
	private String dbName;
	private String tableName;
	private String user;
	private String password;
	private int cacheSize;
	private boolean includeDelete;

	private transient List<BaseRow> rowCache;
	private transient BaseRow currentRow;
	private transient volatile boolean running;
	private transient Exception caughtException;
	private transient BlockingQueue<Tuple3<Event, String, Long>> events;
	private transient BinaryLogClient client;
	private transient EventDeserializer eventDeserializer;
	private transient Map<Long, TableMapEventData> tableMapEventByTableId;
	private transient long currentProgress = 0;
	private transient String currentFile;
	private transient long currentPosition;
	private transient int currentOffset;
	private transient int recordToSkip;
	private transient Thread connectionThread;

	public MysqlBinlogRecordReader(
			RowType rowType, String host, int port, String user, String password,
			String dbName, String tableName, int cacheSize, boolean includeDelete) {
		this.rowType = rowType;
		this.host = host;
		this.port = port;
		this.user = user;
		this.password = password;
		this.cacheSize = cacheSize;
		this.dbName = dbName;
		this.tableName = tableName;
		this.includeDelete = includeDelete;
	}


	@Override
	public void open(InputSplit split, RuntimeContext context) throws IOException {
		events = new ArrayBlockingQueue<>(cacheSize);
		createConnection();
	}

	private void createConnection() throws IOException {

		client = new BinaryLogClient(host, port, user, password);
		tableMapEventByTableId = new HashMap<>();
		Map<EventType, EventDataDeserializer> eventDataDeserializers = new HashMap<>();
		eventDataDeserializers.put(EventType.TABLE_MAP,
				new TableMapEventDataDeserializer());
		eventDataDeserializers.put(EventType.WRITE_ROWS,
				new WriteRowsEventDataDeserializer(tableMapEventByTableId));
		eventDataDeserializers.put(EventType.UPDATE_ROWS,
				new UpdateRowsEventDataDeserializer(tableMapEventByTableId));
		eventDataDeserializers.put(EventType.DELETE_ROWS,
				new DeleteRowsEventDataDeserializer(tableMapEventByTableId));
		eventDataDeserializers.put(EventType.EXT_WRITE_ROWS,
				new WriteRowsEventDataDeserializer(tableMapEventByTableId).setMayContainExtraInformation(true));
		eventDataDeserializers.put(EventType.EXT_UPDATE_ROWS,
				new UpdateRowsEventDataDeserializer(tableMapEventByTableId).setMayContainExtraInformation(true));
		eventDataDeserializers.put(EventType.EXT_DELETE_ROWS,
				new DeleteRowsEventDataDeserializer(tableMapEventByTableId).setMayContainExtraInformation(true));
		eventDeserializer = new EventDeserializer(
				new EventHeaderV4Deserializer(),
				new NullEventDataDeserializer(),
				eventDataDeserializers,
				tableMapEventByTableId);
		eventDeserializer.setCompatibilityMode(
				EventDeserializer.CompatibilityMode.CHAR_AND_BINARY_AS_BYTE_ARRAY);
		client.setEventDeserializer(eventDeserializer);

		if (currentFile != null) {
			client.setBinlogPosition(currentPosition);
			client.setBinlogFilename(currentFile);
		}

		client.registerEventListener(new BinaryLogClient.EventListener() {
			@Override
			public void onEvent(Event event) {

				String currentFile = client.getBinlogFilename();
				long currentPosition = client.getBinlogPosition();
				EventHeader header = event.getHeader();
				switch (header.getEventType()) {
					case WRITE_ROWS:
					case UPDATE_ROWS:
					case DELETE_ROWS:
					case EXT_WRITE_ROWS:
					case EXT_UPDATE_ROWS:
					case EXT_DELETE_ROWS: {
						try {
							events.put(Tuple3.of(event, currentFile, currentPosition));
						} catch (InterruptedException e) {
							try {
								client.disconnect();
							} catch (IOException e1) {
								throw new RuntimeException(e1);
							}
						}
					}
				}
			}
		});
		client.registerLifecycleListener(new BinaryLogClient.LifecycleListener() {
			@Override
			public void onConnect(BinaryLogClient binaryLogClient) {

			}

			@Override
			public void onCommunicationFailure(BinaryLogClient binaryLogClient, Exception e) {
				caughtException = e;
			}

			@Override
			public void onEventDeserializationFailure(BinaryLogClient binaryLogClient,
					Exception e) {
				caughtException = e;
			}

			@Override
			public void onDisconnect(BinaryLogClient binaryLogClient) {

			}
		});
		connectionThread = new Thread(new Runnable() {
			@Override
			public void run() {
				try {
					client.connect();
				} catch (IOException e) {
					caughtException = e;
				}
			}
		});
		connectionThread.setDaemon(true);
		connectionThread.setName("Binlog-connection-thread");
		connectionThread.start();
		running = true;
	}

	@Override
	public boolean next() throws IOException, InterruptedException {
		if (rowCache != null && rowCache.size() > 0) {
			currentRow = rowCache.remove(0);
			currentOffset++;
			return true;
		}
		while(running && (rowCache == null || rowCache.isEmpty())) {
			if (caughtException != null) {
				throw new RuntimeException(caughtException);
			}
			Tuple3<Event, String, Long> eventAndPosition = events.take();
			currentFile = eventAndPosition.f1;
			currentPosition = eventAndPosition.f2;
			currentOffset = 0;
			Event event = eventAndPosition.f0;
			EventHeader header = event.getHeader();
			currentProgress = header.getTimestamp();

			switch (header.getEventType()) {
				case WRITE_ROWS:
				case EXT_WRITE_ROWS: {
					WriteRowsEventData writeRows = event.getData();
					long tableId = writeRows.getTableId();
					TableMapEventData meta = tableMapEventByTableId.get(tableId);
					if (meta != null && meta.getDatabase().equals(dbName) && meta.getTable().equals(tableName)) {

						List<Serializable[]> data = writeRows.getRows();
						List<BaseRow> rows = new ArrayList<>(data.size());

						BitSet bitSet = writeRows.getIncludedColumns();
						Iterator<Serializable[]> dataIterator = data.iterator();
						for (int rowId = 0; rowId < data.size(); rowId++) {
							GenericRow row = new GenericRow(rowType.getArity());
							Serializable[] rawData = dataIterator.next();
							fillRow(bitSet, row, rawData);
							if (recordToSkip > 0) {
								recordToSkip --;
							} else {
								rows.add(row);
							}
						}
						rowCache = rows;
					}
				}
				break;
				case UPDATE_ROWS:
				case EXT_UPDATE_ROWS: {
					UpdateRowsEventData updateRows = event.getData();
					long tableId = updateRows.getTableId();
					TableMapEventData meta = tableMapEventByTableId.get(tableId);
					if (meta != null && meta.getDatabase().equals(dbName) && meta.getTable().equals(tableName)) {
						List<Map.Entry<Serializable[], Serializable[]>> data = updateRows.getRows();
						List<BaseRow> rows = new ArrayList<>(data.size() * 2);

						BitSet bitSet = updateRows.getIncludedColumns();
						BitSet bitSetForDelete = updateRows.getIncludedColumnsBeforeUpdate();

						Iterator<Map.Entry<Serializable[], Serializable[]>> dataIterator = data.iterator();
						for (int rowId = 0; rowId < data.size(); rowId++) {
							Map.Entry<Serializable[], Serializable[]> rawData = dataIterator.next();
							if (includeDelete) {
								GenericRow rowForDelete = new GenericRow(rowType.getArity());
								BaseRowUtil.setRetract(rowForDelete);


								fillRow(bitSetForDelete, rowForDelete, rawData.getKey());
								if (recordToSkip > 0) {
									recordToSkip--;
								} else {
									rows.add(rowForDelete);
								}
							}

							GenericRow row = new GenericRow(rowType.getArity());
							fillRow(bitSet, row, rawData.getValue());
							if (recordToSkip > 0) {
								recordToSkip --;
							} else {
								rows.add(row);
							}
						}
						rowCache = rows;
					}
				}
				break;
				case DELETE_ROWS:
				case EXT_DELETE_ROWS: {
					if (! includeDelete) {
						break;
					}
					DeleteRowsEventData deleteRows = event.getData();
					long tableId = deleteRows.getTableId();
					TableMapEventData meta = tableMapEventByTableId.get(tableId);
					if (meta != null && meta.getDatabase().equals(dbName) && meta.getTable().equals(tableName)) {
						List<Serializable[]> data = deleteRows.getRows();
						List<BaseRow> rows = new ArrayList<>(data.size());

						BitSet bitSet = deleteRows.getIncludedColumns();

						Iterator<Serializable[]> dataIterator = data.iterator();
						for (int rowId = 0; rowId < data.size(); rowId++) {
							GenericRow rowForDelete = new GenericRow(rowType.getArity());
							BaseRowUtil.setRetract(rowForDelete);

							Serializable[] rawData = dataIterator.next();
							fillRow(bitSet, rowForDelete, rawData);
							if (recordToSkip > 0) {
								recordToSkip --;
							} else {
								rows.add(rowForDelete);
							}
						}
						rowCache = rows;
					}
				}
				break;

			}
		}
		currentRow = rowCache.remove(0);
		currentOffset++;
		return true;
	}

	private void fillRow(BitSet bitSet, GenericRow row, Serializable[] rawData) {
		int idx = 0;
		for (int i = 0; i < rowType.getArity(); i++) {
			if (bitSet.get(i)) {
				if (rawData[idx] == null) {
					row.setNullAt(i);
				} else if (rowType.getInternalTypeAt(i).equals(DataTypes.BOOLEAN)) {
					row.update(i, (int)rawData[idx] > 0);
				} else if (rowType.getInternalTypeAt(i).equals(DataTypes.STRING)) {
					row.update(i, BinaryString.fromBytes((byte[]) rawData[idx]));
				} else {
					row.update(
							i,
							DataStructureConverters.getConverterForType(
									rowType.getInternalTypeAt(i)).toInternal(rawData[idx]));
				}
				idx++;
			} else {
				row.setNullAt(i);
			}
		}
	}

	@Override
	public BaseRow getMessage() {
		return currentRow;
	}

	@Override
	public void close() throws IOException {
		running = false;
		if (client != null) {
			client.disconnect();
		}
		if (connectionThread != null) {
			connectionThread.interrupt();
			while (connectionThread.isAlive()) {
				try {
					connectionThread.join();
				} catch (InterruptedException e) {
				}
			}
		}
		while(!events.isEmpty()) {
			events.poll();
		}
	}

	@Override
	public void seek(MysqlBinlogPosition s) throws IOException {
		currentFile = s.getFileName();
		currentPosition = s.getPosition();
		recordToSkip = s.getRecordsToSkip();
	}

	@Override
	public MysqlBinlogPosition getProgress() throws IOException {
		return new MysqlBinlogPosition(currentFile, currentPosition, currentOffset - 1);
	}

	@Override
	public MonotonyIncreaseProgress getMonotonyIncreaseProgress() {
		MonotonyIncreaseProgress progress = new MonotonyIncreaseProgress();
		progress.add(currentFile, currentPosition);
		return progress;
	}

	@Override
	public long getDelay() {
		return currentProgress;
	}

	@Override
	public long getFetchedDelay() {
		return System.currentTimeMillis() - currentProgress;
	}

	@Override
	public boolean isHeartBeat() {
		return false;
	}

	@Override
	public long getWatermark() {
		return 0;
	}
}
