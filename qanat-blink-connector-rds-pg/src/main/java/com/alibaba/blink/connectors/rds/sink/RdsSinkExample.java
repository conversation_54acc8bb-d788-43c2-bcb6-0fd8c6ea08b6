/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.rds.sink;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.io.Serializable;
import java.util.Arrays;

/**
 * An example of RdsBaseSinkFunction.
 */
public class RdsSinkExample {

	static class MyData implements Serializable {
		private String name;
		private int age;

		public MyData(String name, int age) {
			this.name = name;
			this.age = age;
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		public int getAge() {
			return age;
		}

		public void setAge(int age) {
			this.age = age;
		}
	}

	static class MyRdsSinkFunction<MyData> extends RdsBaseSinkFunction<RdsSinkExample.MyData> {

		public MyRdsSinkFunction(String url, String tableName, String userName, String password) {
			super(url, tableName, userName, password);
		}

		@Override
		String constructSql(RdsSinkExample.MyData value) {
			return String.format("INSERT INTO %s (%s, %s) VALUES (%s, %s)",
					getTableName(),
					"name",
					"age",
					value.getName(),
					value.getAge() + "");
		}
	}

	public static void main(String[] args) throws Exception {
		StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
		DataStream<MyData> source = env.fromCollection(
				Arrays.asList(
						new RdsSinkExample.MyData("a", 1),
						new RdsSinkExample.MyData("b", 2))
		);
		source.addSink(new MyRdsSinkFunction<MyData> (
				"jdbc:xxxxx",
				"table",
				"user",
				"pass"
		));
		env.execute("example");
	}
}
