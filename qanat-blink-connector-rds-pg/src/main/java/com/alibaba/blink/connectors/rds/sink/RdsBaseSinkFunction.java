/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.rds.sink;

import com.alibaba.blink.streaming.connectors.common.MetricUtils;
import com.alibaba.blink.streaming.connectors.common.errcode.ConnectorErrors;
import com.alibaba.blink.streaming.connectors.common.exception.BlinkRuntimeException;
import com.alibaba.blink.streaming.connectors.common.exception.ErrorUtils;
import com.alibaba.blink.streaming.connectors.common.source.parse.DirtyDataStrategy;
import com.alibaba.blink.streaming.connectors.common.util.ConnectionPool;
import com.alibaba.blink.streaming.connectors.common.util.SQLExceptionSkipPolicy;
import com.alibaba.druid.pool.DruidDataSource;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.Meter;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.flink.util.Preconditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * A Rds Base SinkFunction which handles connections.
 * @param <IN>
 */
public abstract class RdsBaseSinkFunction<IN> extends RichSinkFunction<IN> {

	private static final Logger LOG = LoggerFactory.getLogger(RdsBaseSinkFunction.class);

	private final String url;

	private final String tableName;

	private final String userName;

	private final String password;

	private String driverClass = "com.mysql.jdbc.Driver";

	private transient DruidDataSource dataSource;

	private static ConnectionPool<DruidDataSource> dataSourcePool = new ConnectionPool<>();

	private transient Connection connection;

	private transient Statement statement;

	private String dataSourceKey = "";

	private int connectionMaxActive = 40;
	private int connectionInitialSize = 1;
	private int connectionMinIdle = 0;
	private boolean connectionTestWhileIdle = false;
	private int maxWait = 15000;
	private int maxRetryTime = 3;

	private Meter outTps;
	private Counter sinkSkipCounter;
	private MetricUtils.LatencyGauge latencyGauge;
	private DirtyDataStrategy dirtyDataStrategy = DirtyDataStrategy.EXCEPTION;

	public void setConnectionMaxActive(int connectionMaxActive) {
		this.connectionMaxActive = connectionMaxActive;
	}

	public void setConnectionInitialSize(int connectionInitialSize) {
		this.connectionInitialSize = connectionInitialSize;
	}

	public void setConnectionMinIdle(int connectionMinIdle) {
		this.connectionMinIdle = connectionMinIdle;
	}

	public void setConnectionTestWhileIdle(boolean connectionTestWhileIdle) {
		this.connectionTestWhileIdle = connectionTestWhileIdle;
	}

	public void setMaxWait(int maxWait) {
		this.maxWait = maxWait;
	}

	public void setMaxRetryTime(int maxRetryTime) {
		this.maxRetryTime = maxRetryTime;
	}

	public void setDirtyDataStrategy(DirtyDataStrategy dirtyDataStrategy) {
		this.dirtyDataStrategy = dirtyDataStrategy;
	}

	public String getUrl() {
		return url;
	}

	public String getTableName() {
		return tableName;
	}

	public String getUserName() {
		return userName;
	}

	public String getPassword() {
		return password;
	}

	public String getDriverClass() {
		return driverClass;
	}

	public String getDataSourceKey() {
		return dataSourceKey;
	}

	public int getConnectionMaxActive() {
		return connectionMaxActive;
	}

	public int getConnectionInitialSize() {
		return connectionInitialSize;
	}

	public int getConnectionMinIdle() {
		return connectionMinIdle;
	}

	public boolean isConnectionTestWhileIdle() {
		return connectionTestWhileIdle;
	}

	public int getMaxWait() {
		return maxWait;
	}

	public int getMaxRetryTime() {
		return maxRetryTime;
	}

	public Meter getOutTps() {
		return outTps;
	}

	public Counter getSinkSkipCounter() {
		return sinkSkipCounter;
	}

	public MetricUtils.LatencyGauge getLatencyGauge() {
		return latencyGauge;
	}

	public DirtyDataStrategy getDirtyDataStrategy() {
		return dirtyDataStrategy;
	}

	public RdsBaseSinkFunction(String url, String tableName, String userName, String password) {
		this.url = Preconditions.checkNotNull(url, "url can't be empty");
		this.tableName = Preconditions.checkNotNull(tableName, "tableName can't be empty");
		this.userName = Preconditions.checkNotNull(userName, "userName can't be empty");
		this.password = Preconditions.checkNotNull(password, "password can't be empty");
	}

	@Override
	public void open(Configuration parameters) throws Exception {
		super.open(parameters);

		synchronized (RdsBaseSinkFunction.class) {
			dataSourceKey = url + userName + password + tableName;
			if (dataSourcePool.contains(dataSourceKey)) {
				dataSource = dataSourcePool.get(dataSourceKey);
			} else {
				dataSource = new DruidDataSource();
				dataSource.setUrl(url);
				dataSource.setUsername(userName);
				dataSource.setPassword(password);
				dataSource.setDriverClassName(driverClass);
				dataSource.setMaxActive(connectionMaxActive);
				dataSource.setInitialSize(connectionInitialSize);
				dataSource.setMaxWait(maxWait);//默认为15s
				dataSource.setMinIdle(connectionMinIdle);
				dataSource.setTestWhileIdle(connectionTestWhileIdle);
				dataSource.setRemoveAbandoned(false);
				dataSourcePool.put(dataSourceKey, dataSource);
			}

			try {
				connection = dataSource.getConnection();
				statement = connection.createStatement();
			} catch (Exception e) {
				LOG.error("Error When Init DataSource", e);
				ErrorUtils.throwException(ConnectorErrors.INST.rdsGetConnectionError("RDS"),e);
			}
		}
		outTps = MetricUtils.registerOutTps(getRuntimeContext());
		latencyGauge = MetricUtils.registerOutLatency(getRuntimeContext());
		sinkSkipCounter = MetricUtils.registerSinkSkipCounter(getRuntimeContext(), "rds");
	}

	private void executeSql(String sql) {
		long start = System.currentTimeMillis();
		int retryTime = 0;
		while (retryTime++ < maxRetryTime) {
			try {
				if (LOG.isDebugEnabled()) {
					LOG.debug(sql);
				}
				if (connection.isClosed()) {
					connection = dataSource.getConnection();
					statement = connection.createStatement();
				}
				statement.execute(sql);
				break;
			} catch (SQLException e) {
				LOG.error("Insert into db error,exception:", e);
				if (retryTime == maxRetryTime) {
					BlinkRuntimeException blinkException = ErrorUtils.getException(
							ConnectorErrors.INST.rdsWriteError("RDS", sql),e);
					if (SQLExceptionSkipPolicy.judge(dirtyDataStrategy, e.getErrorCode(), blinkException)) {
						sinkSkipCounter.inc();
						LOG.error(blinkException.getErrorMessage() + " sql:" + sql);
					}
				}
				try {
					// sleep according to retryTimes
					Thread.sleep(1000 * retryTime);
				} catch (Exception e1) {
					//ignore
				}
			} finally {
				try {
					connection.close();
				} catch (Exception e) {
					LOG.error("", e);
				}
			}
		}

		// report metrics
		long end = System.currentTimeMillis();
		latencyGauge.report(end - start, 1);
		outTps.markEvent();
	}

	@Override
	public void close() throws Exception {
		try {
			if (connection != null && !connection.isClosed()) {
				connection.close();
			}
		} catch (Exception e) {
			LOG.error(e.getMessage(), e);
			// ignore this exception
		}
		synchronized (RdsBaseSinkFunction.class) {
			if (dataSourcePool.remove(dataSourceKey) && dataSource != null && !dataSource.isClosed()) {
				dataSource.close();
				dataSource = null;
			}
		}
	}

	/**
	 * User may be implement their own method.
	 * @param value
	 * @param context
	 * @throws Exception
	 */
	@Override
	public void invoke(IN value, Context context) throws Exception {
		String sql = constructSql(value);
		executeSql(sql);
	}

	/**
	 * construct sql by user.
	 * @param value
	 * @return
	 */
	abstract String constructSql(IN value);
}
