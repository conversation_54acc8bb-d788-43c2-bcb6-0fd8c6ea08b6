/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.rds.source;

import java.util.List;

import com.alibaba.blink.streaming.connectors.common.source.SourceCollector;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.util.Collector;

/**
 * Parser to extract List of base row to base row.
 */
public class MysqlBinlogParser implements SourceCollector<List<BaseRow>, BaseRow> {

	private TypeInformation<BaseRow> typeInfo;

	public MysqlBinlogParser(TypeInformation<BaseRow> typeInfo) {
		this.typeInfo = typeInfo;
	}

	@Override
	public void parseAndCollect(List<BaseRow> input, Collector<BaseRow> collector) {
		if (input != null) {
			for (BaseRow baseRow : input) {
				collector.collect(baseRow);
			}
		}
	}

	@Override
	public TypeInformation<BaseRow> getProducedType() {
		return typeInfo;
	}
}
