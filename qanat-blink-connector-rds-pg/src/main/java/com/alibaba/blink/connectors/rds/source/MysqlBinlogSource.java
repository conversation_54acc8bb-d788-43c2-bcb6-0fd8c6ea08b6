/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.rds.source;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.core.io.GenericInputSplit;
import org.apache.flink.core.io.InputSplit;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.table.types.RowType;
import org.apache.flink.table.types.TypeConverters;

import com.alibaba.blink.streaming.connectors.common.reader.RecordReader;
import com.alibaba.blink.streaming.connectors.common.source.AbstractParallelSource;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Binlog source function.
 */
public class MysqlBinlogSource extends AbstractParallelSource<BaseRow, MysqlBinlogPosition> {

	private RowType rowType;
	private String host;
	private int port;
	private String user;
	private String password;
	private String dbName;
	private int cacheSize;
	private boolean includeDelete;

	public MysqlBinlogSource(RowType rowType, String host, int port, String user, String password,
			String dbName, String tableName, int cacheSize, boolean includeDelete) {
		this.rowType = rowType;
		this.host = host;
		this.port = port;
		this.user = user;
		this.password = password;
		this.cacheSize = cacheSize;
		this.dbName = dbName;
		this.tableName = tableName;
		this.includeDelete = includeDelete;
	}



	@Override
	public RecordReader<BaseRow, MysqlBinlogPosition> createReader(Configuration config) throws IOException {
		return new MysqlBinlogRecordReader(rowType, host, port, user, password, dbName, tableName, cacheSize, includeDelete);
	}

	@Override
	public InputSplit[] createInputSplitsForCurrentSubTask(int numberOfParallelSubTasks,
			int indexOfThisSubTask) throws IOException {
		if (indexOfThisSubTask == 0) {
			return new InputSplit[]{new GenericInputSplit(0, 1)};
		} else {
			return new InputSplit[0];
		}
	}

	@Override
	public List<String> getPartitionList() throws Exception {
		List<String> splits = new ArrayList<String>(1);
		splits.add(new GenericInputSplit(0, 1).toString());
		return splits;
	}

	@Override
	public String toString() {
		return String.format("[%s:%d]-[%s]-[%s]", host, port, dbName, tableName);
	}

	@Override
	public TypeInformation<BaseRow> getProducedType() {
		return TypeConverters.toBaseRowTypeInfo(rowType);
	}
}
