/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.blink.connectors.rds;

import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.factories.BatchCompatibleTableSinkFactory;
import org.apache.flink.table.factories.BatchTableSourceFactory;
import org.apache.flink.table.factories.StreamTableSinkFactory;
import org.apache.flink.table.factories.StreamTableSourceFactory;
import org.apache.flink.table.sinks.BatchCompatibleStreamTableSink;
import org.apache.flink.table.sinks.StreamTableSink;
import org.apache.flink.table.sources.BatchTableSource;
import org.apache.flink.table.sources.StreamTableSource;
import org.apache.flink.table.types.RowType;
import org.apache.flink.table.util.TableProperties;
import org.apache.flink.types.Row;

import com.alibaba.blink.connectors.rds.dim.RdsConnectionParam;
import com.alibaba.blink.connectors.rds.source.MysqlBinlogSource;
import com.alibaba.blink.streaming.connectors.common.errcode.ConnectorErrors;
import com.alibaba.blink.streaming.connectors.common.exception.BlinkRuntimeException;
import com.alibaba.blink.streaming.connectors.common.exception.ErrorUtils;
import com.alibaba.blink.streaming.connectors.common.exception.InvalidParamException;
import com.alibaba.blink.streaming.connectors.common.exception.NotEnoughParamsException;
import com.alibaba.blink.streaming.connectors.common.source.SourceFunctionTableSource;
import com.alibaba.blink.streaming.connectors.common.source.parse.DirtyDataStrategy;
import com.alibaba.blink.streaming.connectors.common.util.BlinkStringUtil;
import com.alibaba.blink.table.cache.CacheConfig;
import com.alibaba.blink.table.cache.CacheStrategy;
import com.alibaba.blink.table.connectors.conf.BlinkOptions;
import com.alibaba.blink.table.factories.BlinkTableFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_PROPERTY_VERSION;
import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_TYPE;

public class RdsPgTableFactory extends BlinkTableFactory implements
		StreamTableSinkFactory<Tuple2<Boolean, Row>>,
		BatchCompatibleTableSinkFactory<Tuple2<Boolean, Row>>,
		BatchTableSourceFactory<Object>,
		StreamTableSourceFactory<Object> {

	private SourceFunctionTableSource createSource(Map<String, String> properties) {
		TableProperties tableProperties = new TableProperties();
		tableProperties.putProperties(properties);
		String sqlTableName = tableProperties.readTableNameFromProperties();
		RichTableSchema richTableSchema = tableProperties.readSchemaFromProperties(classLoader);
		String blinkEnvironmentType = tableProperties.getString(BlinkOptions.BLINK_ENVIRONMENT_TYPE_KEY);
		boolean isBatchMode = BlinkOptions.BLINK_ENVIRONMENT_BATCH_VALUE
				.equalsIgnoreCase(blinkEnvironmentType) ? true : false;
		// when is in batch mode, set source function to null as it is not supported in batch
		MysqlBinlogSource sourceFunction = null;
		if (!isBatchMode) {
			RowType rowType = richTableSchema.getResultType();
			String host = tableProperties.getString(BlinkOptions.MYSQLBINLOG.HOST);
			String user = tableProperties.getString(BlinkOptions.MYSQLBINLOG.USER_NAME);
			int port = tableProperties.getInteger(BlinkOptions.MYSQLBINLOG.PORT);
			String password = tableProperties.getString(BlinkOptions.MYSQLBINLOG.PASSWORD);
			int cacheSize = tableProperties.getInteger(BlinkOptions.MYSQLBINLOG.CACHE_SIZE);
			String dbName = tableProperties.getString(BlinkOptions.MYSQLBINLOG.DB_NAME);
			String tableName = tableProperties.getString(BlinkOptions.MYSQLBINLOG.TABLE_NAME);
			if (host != null && BlinkStringUtil.isEmpty(host, user, password, dbName, tableName)) {
				throw new NotEnoughParamsException(BlinkOptions.MYSQLBINLOG.PARAMS_HELP_MSG);
			}
			boolean includeDelete = tableProperties.getBoolean(BlinkOptions.MYSQLBINLOG.INCLUDE_DELETE);
			sourceFunction = new MysqlBinlogSource(
					rowType, host, port, user, password, dbName, tableName, cacheSize, includeDelete);
		}

		// dimension table options
		RdsConnectionParam param = createConnectionParam(tableProperties, richTableSchema);
		CacheConfig cacheConfig = createCacheConfig(tableProperties);

		return new RdsTableSource(sourceFunction, sqlTableName, richTableSchema, param, cacheConfig);
	}

	private RdsConnectionParam createConnectionParam(TableProperties properties, RichTableSchema schema) {
		String sqlTableName = properties.readTableNameFromProperties();
		String url = properties.getString(BlinkOptions.RDS.URL);
		String tableName = properties.getString(BlinkOptions.RDS.TABLE_NAME);
		String userName = properties.getString(BlinkOptions.RDS.USER_NAME);
		String password = properties.getString(BlinkOptions.RDS.PASSWORD);

		if (url != null && BlinkStringUtil.isEmpty(url, tableName, userName, password)) {
			throw new NotEnoughParamsException(BlinkOptions.RDS.PARAMS_DIM_HELP_MSG);
		}

		if (!url.startsWith("jdbc:mysql://") &&
			!url.startsWith("jdbc:postgresql://")) {
				throw new InvalidParamException(
					"RDS url must starts with jdbc:mysql:// or jdbc:postgresql:// format" +
					",but actual url is " + url);
			}

		if (url != null && schema.deduceAllIndexes().isEmpty()) {
			// TODO remove this when support scannable dim table source
			ErrorUtils.throwException(ConnectorErrors.INST.dimTableNoIndexKeyError(sqlTableName));
		}

		int maxRetryTimes = properties.getInteger(BlinkOptions.RDS.OPTIONAL_MAX_RETRY_TIMES);

		int joinMaxRows = properties.getInteger(BlinkOptions.DIM.OPTIONAL_JOIN_MAX_ROWS);
		RdsConnectionParam param = new RdsConnectionParam();
		param.setUrl(url)
		     .setTableName(tableName)
		     .setUserName(userName)
		     .setPassword(password)
		     .setMaxRetryTime(maxRetryTimes)
		     .setMaxFetchResult(joinMaxRows);
		return param;
	}

	private CacheConfig createCacheConfig(TableProperties properties) {
		CacheStrategy cacheStrategy = BlinkOptions.RDS.getCacheStrategy(properties);
		String cacheReloadTimeBlacklist = properties.getString(BlinkOptions.RDS.OPTIONAL_CACHE_RELOAD_TIME_BLACKLIST);
		List<Tuple2<Long, Long>> timeRangeBlacklist = BlinkOptions.RDS.parseTimeRangeBlacklist(cacheReloadTimeBlacklist);
		int scanLimit = properties.getInteger(BlinkOptions.RDS.OPTIONAL_CACHE_SCAN_LIMIT);
		CacheConfig cacheConfig = new CacheConfig(cacheStrategy, timeRangeBlacklist, scanLimit);
		return cacheConfig;
	}

	private RdsPgTableSink createSink(Map<String, String> props) {
		TableProperties properties = new TableProperties();
		properties.putProperties(props);
		RichTableSchema schema = properties.readSchemaFromProperties(classLoader);
		String url = properties.getString(BlinkOptions.RDS.URL);
		String tableName = properties.getString(BlinkOptions.RDS.TABLE_NAME);
		String userName = properties.getString(BlinkOptions.RDS.USER_NAME);
		String password = properties.getString(BlinkOptions.RDS.PASSWORD);

		if (BlinkStringUtil.isEmpty(url, tableName, userName, password)) {
			throw new NotEnoughParamsException(BlinkOptions.RDS.PARAMS_WRITER_HELP_MSG);
		}

		String driverClassName = "com.mysql.jdbc.Driver";
		if (url.startsWith("jdbc:postgresql://")) {
		    driverClassName = "org.postgresql.Driver";
		}

		int maxRetryTimes = properties.getInteger(BlinkOptions.RDS.OPTIONAL_MAX_RETRY_TIMES);
		int batchSize = properties.getInteger(BlinkOptions.RDS.OPTIONAL_BATCH_SIZE);
		int bufferSize = properties.getInteger(BlinkOptions.RDS.OPTIONAL_BUFFER_SIZE);
		int flushIntervalMs = properties.getInteger(BlinkOptions.RDS.OPTIONAL_FLUSH_INTERVAL_MS);
		long maxSinkTps = properties.getLong(BlinkOptions.MAX_SINK_TPS);
		String excludeUpdateColumnsStr = properties.getString(BlinkOptions.RDS.OPTIONAL_EXCLUDE_UPDATE_FIELDS);
		List<String> excludeUpdateColumns = new ArrayList<>();
		if (!BlinkStringUtil.isEmpty(excludeUpdateColumnsStr)) {
			for(String s1:excludeUpdateColumnsStr.split(",")){
				excludeUpdateColumns.add(s1);
			}
		}

		String actionOnInsertError = properties.getString(BlinkOptions.ACTION_ON_INSERT_ERROR);
		DirtyDataStrategy dirtyDataStrategy = DirtyDataStrategy.EXCEPTION;
		if(actionOnInsertError.equalsIgnoreCase("SKIP")){
			dirtyDataStrategy = DirtyDataStrategy.SKIP;
		}else if(actionOnInsertError.equalsIgnoreCase("SKIP_SINLENT")){
			dirtyDataStrategy = DirtyDataStrategy.SKIP_SILENT;
		}
		String partitionBy = properties.getString(BlinkOptions.PARTITION_BY);
		boolean shuffleEmptyKey = properties.getBoolean(BlinkOptions.SHUFFLE_EMPTY_KEY);

		boolean ignoreDelete = properties.getBoolean(BlinkOptions.RDS.OPTIONAL_IGNORE_DELETE);

		RdsPgOutputFormat.Builder builder = new RdsPgOutputFormat.Builder();
		builder.setUrl(url)
				.setTableName(tableName)
				.setUserName(userName)
				.setPassword(password)
				.setPkField(schema.getPrimaryKeys())
				.setMaxRetryTime(maxRetryTimes)
				.setBufferSize(bufferSize)
				.setBatchSize(batchSize)
				.setFlushIntervalMs(flushIntervalMs)
				.setExceptUpdateKeys(excludeUpdateColumns)
				.setMaxSinkTps(maxSinkTps)
				.setDirtyDataStrategy(dirtyDataStrategy)
				.setIgnoreDelete(ignoreDelete)
				.setDriverClassName(driverClassName);

		RdsPgTableSink sink = new RdsPgTableSink(builder, schema);
		if (partitionBy != null && !partitionBy.isEmpty()) {
			sink.setPartitionedField(partitionBy);
			sink.setShuffleEmptyKey(shuffleEmptyKey);
		}

		//create table
		boolean isCreateTable = properties.getBoolean(BlinkOptions.RDS.OPTIONAL_CREATE_TABLE_IFNOT_EXISTS);
		int varcharMaxLength = properties.getInteger(BlinkOptions.RDS.OPTIONAL_VARCHAR_MAX_LENGTH);
		if (isCreateTable) {
			builder.setRowTypeInfo((RowTypeInfo) sink.getRecordType());
			RdsPgOutputFormat rdsOutputFormat = builder.build();

			try {
				rdsOutputFormat.createTable(schema, varcharMaxLength);
			} catch (Exception e) {
				BlinkRuntimeException blinkException =
						ErrorUtils.getException(ConnectorErrors.INST.rdsCreateTableError("RDS"), e);
				throw blinkException;
			}
		}

		return sink;
	}

	@Override
	protected List<String> supportedSpecificProperties() {
		return mergeProperties(BlinkOptions.RDS.SUPPORTED_KEYS,
				BlinkOptions.DIM.SUPPORTED_KEYS, BlinkOptions.MYSQLBINLOG.SUPPORTED_KEYS);
	}

	@Override
	protected Map<String, String> requiredContextSpecific() {
		Map<String, String> context = new HashMap<>();
		context.put(CONNECTOR_TYPE, "RDS_PG"); // RDS
		context.put(CONNECTOR_PROPERTY_VERSION, "1"); // backwards compatibility
		return context;
	}

	@Override
	public BatchCompatibleStreamTableSink<Tuple2<Boolean, Row>> createBatchCompatibleTableSink(Map<String, String> properties) {
		return createSink(properties);
	}

	@Override
	public BatchTableSource createBatchTableSource(Map<String, String> properties) {
		return createSource(properties);
	}

	@Override
	public StreamTableSink<Tuple2<Boolean, Row>> createStreamTableSink(Map<String, String> properties) {
		return createSink(properties);
	}

	@Override
	public StreamTableSource createStreamTableSource(Map<String, String> properties) {
		return createSource(properties);
	}
}
