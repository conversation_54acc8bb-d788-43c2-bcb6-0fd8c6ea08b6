/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.connectors.rds;


import org.apache.flink.api.common.typeinfo.PrimitiveArrayTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.Meter;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.types.TypeConverters;
import org.apache.flink.types.Row;
import org.apache.commons.lang3.StringUtils;

import org.apache.flink.shaded.guava18.com.google.common.base.Joiner;

import com.alibaba.blink.streaming.connectors.common.MetricUtils;
import com.alibaba.blink.streaming.connectors.common.errcode.ConnectorErrors;
import com.alibaba.blink.streaming.connectors.common.exception.BlinkRuntimeException;
import com.alibaba.blink.streaming.connectors.common.exception.ErrorUtils;
import com.alibaba.blink.streaming.connectors.common.output.HasRetryTimeout;
import com.alibaba.blink.streaming.connectors.common.output.Syncable;
import com.alibaba.blink.streaming.connectors.common.output.TupleRichOutputFormat;
import com.alibaba.blink.streaming.connectors.common.source.parse.DirtyDataStrategy;
import com.alibaba.blink.streaming.connectors.common.util.ConnectionPool;
import com.alibaba.blink.streaming.connectors.common.util.JdbcUtils;
import com.alibaba.blink.streaming.connectors.common.util.SQLExceptionSkipPolicy;
import com.alibaba.blink.streaming.connectors.common.util.TpsLimitUtils;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.fastjson.JSON;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

public class RdsPgOutputFormat
		extends TupleRichOutputFormat
		implements Syncable, HasRetryTimeout {
	private transient static final Logger LOG = LoggerFactory.getLogger(RdsPgOutputFormat.class);

	private String url;
	private String tableName;
	private String userName;
	private String password;
	private RowTypeInfo rowTypeInfo;

	private List<String> pkFields = null;
	private int maxRetryTime = 3;

	private final String INSERT_SQL_TPL = "INSERT INTO %s (%s) VALUES (%s)";

	private final String DELETE_WITH_KEY_SQL_TPL = "DELETE FROM %s WHERE %s LIMIT 1";

	private final String CREATE_TABLE_SQL_TPL = "CREATE TABLE IF NOT EXISTS %s (%s)";

	private transient Configuration config;
	private transient Timer flusher;
	private volatile long lastFlushTime = 0;

	private String driverClassName = "com.mysql.jdbc.Driver";
	private int connectionMaxActive = 40;
	private int connectionInitialSize = 1;
	private int connectionMinIdle = 0;
	private boolean connectionTestWhileIdle = false;
	private int maxWait = 15000;
	private int removeAbandonedTimeout = 60 * 10;
	private int batchSize = 50;
	private int bufferSize = 500;
	private List<String> exceptUpdateKeys = new ArrayList<>();
	private long flushIntervalMs = 5000;
	private long currentCount = 0;
	private long maxSinkTps = -1;
	private int numTasks = 1;

	private final Map<String, Tuple2<Boolean,Row>> mapReduceBuffer = new HashMap<>();
	private List<Row> writeAddBuffer;
	private int curAddCount = 0;
	private List<Row> writeDelBuffer;
	private int curDelCount = 0;

	private transient DruidDataSource dataSource;
	private static ConnectionPool<DruidDataSource> dataSourcePool = new ConnectionPool<>();
	private String dataSourceKey = "";

	private String fieldNames = null;

	private boolean skipDuplicateEntryError = true;
	private boolean existsPrimaryKeys = true;

	private DirtyDataStrategy dirtyDataStrategy = DirtyDataStrategy.EXCEPTION;

	private Meter outTps;
	private Meter outBps;
	private Counter sinkSkipCounter;
	private MetricUtils.LatencyGauge latencyGauge;

	private transient Connection connection;
	private transient Statement statement;
	private boolean ignoreDelete = false;

	private volatile transient Exception flushException = null;
	private volatile boolean flushError = false;

	private RdsPgOutputFormat(
			String url,
			String tableName,
			String userName,
			String password,
			RowTypeInfo rowTypeInfo,
			List<String> pkFields,
			int maxRetryTime,
			String driverClassName,
			int connectionMaxActive,
			int connectionInitialSize,
			int connectionMinIdle,
			boolean connectionTestWhileIdle,
			int maxWait,
			int removeAbandonedTimeout) {
		this.url = url;
		this.tableName = tableName;
		this.userName = userName;
		this.password = password;
		this.rowTypeInfo = rowTypeInfo;
		this.pkFields = pkFields;
		this.maxRetryTime = maxRetryTime;
		this.driverClassName = driverClassName;
		this.connectionMaxActive = connectionMaxActive;
		this.connectionInitialSize = connectionInitialSize;
		this.connectionMinIdle = connectionMinIdle;
		this.connectionTestWhileIdle = connectionTestWhileIdle;
		Joiner joinerOnComma = Joiner.on(",").useForNull("null");
		String[] fieldNamesStr = new String[rowTypeInfo.getArity()];
		for (int i = 0; i < fieldNamesStr.length; i++) {
			fieldNamesStr[i] = rowTypeInfo.getFieldNames()[i];
		}
		this.fieldNames = joinerOnComma.join(fieldNamesStr);
		this.maxWait = maxWait;
		this.removeAbandonedTimeout = removeAbandonedTimeout;
		if (null != pkFields && !pkFields.isEmpty()){
			existsPrimaryKeys = true;
		} else {
			existsPrimaryKeys = false;
		}
	}

	public RdsPgOutputFormat setIgnoreDelete(boolean ignoreDelete) {
		this.ignoreDelete = ignoreDelete;
		return this;
	}

	public RdsPgOutputFormat setBatchSize(int batchSize) {
		this.batchSize = batchSize;
		return this;
	}

	public RdsPgOutputFormat setBufferSize(int bufferSize) {
		this.bufferSize = bufferSize;
		return this;
	}

	public RdsPgOutputFormat setExceptUpdateKeys(List<String> exceptUpdateKeys) {
		this.exceptUpdateKeys = exceptUpdateKeys;
		return this;
	}

	public RdsPgOutputFormat setFlushIntervalMs(long flushIntervalMs) {
		this.flushIntervalMs = flushIntervalMs;
		return this;
	}

	public RdsPgOutputFormat setMaxSinkTps(long maxSinkTps) {
		this.maxSinkTps = maxSinkTps;
		return this;
	}

	/**
	 * Start flusher that will flush buffer automatically
	 */
	protected void scheduleFlusher() {
		flusher = new Timer("RdsOutputFormat.buffer.flusher");
		flusher.schedule(new TimerTask() {
			@Override
			public void run() {
				try {
					if(System.currentTimeMillis() - lastFlushTime >= flushIntervalMs){
						synchronized (this){
							sync();
						}
					}
				} catch (Exception e) {
					LOG.error("flush buffer to Rds failed", e);
					flushException = e;
					flushError = true;
				}
			}
		}, flushIntervalMs, flushIntervalMs);
	}

	/**
	 * create rds table
	 *
	 * @param schema  table schema
	 * @param maxLength  varcharMaxLength
	 * @throws Exception
	 */
	public void  createTable(RichTableSchema schema, int maxLength) throws Exception {
		String sql = getCreateTableSql(schema, maxLength);

		DruidDataSource source = new DruidDataSource();
		source.setUrl(url);
		source.setUsername(userName);
		source.setPassword(password);
		source.setDriverClassName(driverClassName); // com.***.***.**.driver
		source.setMaxActive(connectionMaxActive);
		source.setInitialSize(connectionInitialSize);
		source.setMaxWait(maxWait);//默认为15s
		source.setMinIdle(connectionMinIdle);
		source.setTestWhileIdle(connectionTestWhileIdle);
		source.setRemoveAbandoned(false);
		source.init();

		Connection conn = null;
		Statement stat;

		int retryTime = 0;
		while (retryTime++ < maxRetryTime) {
			try {
				conn = source.getConnection();
				stat = conn.createStatement();
				stat.execute(sql);
				break;
			} catch (SQLException e) {
				LOG.error("create db error,exception:", e);
				if (retryTime == maxRetryTime) {
					BlinkRuntimeException blinkException = ErrorUtils.getException(
							ConnectorErrors.INST.rdsCreateTableError("RDS",sql),e);
					throw  blinkException;
				}
				try {
					Thread.sleep(1000 * retryTime);
				} catch (Exception e1) {
				}
			} finally {
				try {
					if (conn != null && !conn.isClosed()) {
						conn.close();
					}
				} catch (Exception e) {
					LOG.error("", e);
				}
			}
		}

		if (source != null && !source.isClosed()) {
			source.close();
			source = null;
		}
	}

	@Override
	public long getRetryTimeout() {
		return 0;
	}

	@Override
	public void configure(Configuration configuration) {
		this.config = configuration;
	}

	@Override
	public void open(int taskNumber, int numTasks) throws IOException {
		this.numTasks = numTasks;
		super.open(taskNumber, numTasks);
		synchronized (RdsPgOutputFormat.class) {
			dataSourceKey = url + userName + password + tableName;
			if (dataSourcePool.contains(dataSourceKey)){
				dataSource = dataSourcePool.get(dataSourceKey);
			} else {
				dataSource = new DruidDataSource();
				dataSource.setUrl(url);
				dataSource.setUsername(userName);
				dataSource.setPassword(password);
				dataSource.setDriverClassName(driverClassName); // com.***.***.**.driver
				dataSource.setMaxActive(connectionMaxActive);
				dataSource.setInitialSize(connectionInitialSize);
				dataSource.setMaxWait(maxWait);//默认为15s
				dataSource.setMinIdle(connectionMinIdle);
				dataSource.setTestWhileIdle(connectionTestWhileIdle);
				dataSource.setRemoveAbandoned(false);
				dataSourcePool.put(dataSourceKey, dataSource);
			}
			try {
				connection = dataSource.getConnection();
				statement = connection.createStatement();
			} catch (Exception e) {
				LOG.error("Error When Init DataSource", e);
				ErrorUtils.throwException(ConnectorErrors.INST.rdsGetConnectionError("RDS"),e);
			}
		}
		if(existsPrimaryKeys) {
			scheduleFlusher();
			// 默认会把主键排除掉
			for(String pk:pkFields) {
				if(!exceptUpdateKeys.contains(pk)) {
					exceptUpdateKeys.add(pk);
				}
			}
		}

		outTps = MetricUtils.registerOutTps(getRuntimeContext());
		outBps = MetricUtils.registerOutBps(getRuntimeContext(), "rds");
		latencyGauge = MetricUtils.registerOutLatency(getRuntimeContext());
		sinkSkipCounter = MetricUtils.registerSinkSkipCounter(getRuntimeContext(),getName());
	}

	@Override
	public void writeAddRecord(Row row) throws IOException {
		if (flushError && null != flushException){
			throw new RuntimeException(flushException);
		}
		if(!existsPrimaryKeys) {
			// 没有主键直接插入
			String fieldValues = JdbcUtils.toMysqlField(row.getField(0));
			for (int i = 1; i < row.getArity(); i++) {
				fieldValues = fieldValues.concat("," + JdbcUtils.toPostgreSqlField(row.getField(i)));
			}
			String sql = String.format(INSERT_SQL_TPL, tableName, this.fieldNames, fieldValues);
			executeSql(sql);
		} else {
			currentCount ++;
			String dupKey = JdbcUtils.constructDupKey(row, rowTypeInfo, pkFields);
			synchronized (mapReduceBuffer) {
				mapReduceBuffer.put(dupKey, new Tuple2<>(true, row));
			}
			if(currentCount >= bufferSize){
				sync();
			}
		}
	}

	@Override
	public void writeDeleteRecord(Row row) throws IOException {
		if (flushError && null != flushException){
			throw new RuntimeException(flushException);
		}
		if(ignoreDelete){
			return;
		}
		if(!existsPrimaryKeys) {
			if (null != row) {
				Joiner joinerOnComma = Joiner.on(" AND ").useForNull("null");
				List<String> sub = new ArrayList<>();
				for (int i = 0; i < row.getArity(); i++) {
					sub.add(" " + rowTypeInfo.getFieldNames()[i] + " = " +
							JdbcUtils.toMysqlField(row.getField(i)));
				}
				String sql = String.format(DELETE_WITH_KEY_SQL_TPL, tableName, joinerOnComma.join(sub));
				executeSql(sql);
			}
		} else {
			currentCount ++;
			String dupKey = JdbcUtils.constructDupKey(row, rowTypeInfo, pkFields);
			synchronized (mapReduceBuffer) {
				mapReduceBuffer.put(dupKey, new Tuple2<>(false, row));
			}
			if(currentCount >= bufferSize){
				sync();
			}
		}
	}

	private void executeSql(String sql) {
		long start = System.currentTimeMillis();
		int retryTime = 0;
		while (retryTime++ < maxRetryTime) {
			try {
				if (LOG.isDebugEnabled()) {
					LOG.debug(sql);
				}
				if (connection.isClosed()) {
					connection = dataSource.getConnection();
					statement = connection.createStatement();
				}
				statement.execute(sql);
				TpsLimitUtils.limitTps(maxSinkTps, numTasks, start, 1);
				break;
			} catch (SQLException e) {
				LOG.error("Insert into db error,exception:", e);
				if (retryTime == maxRetryTime) {
					BlinkRuntimeException blinkException = ErrorUtils.getException(
							ConnectorErrors.INST.rdsWriteError("RDS", sql), e);
					if (SQLExceptionSkipPolicy.judge(dirtyDataStrategy, e.getErrorCode(), blinkException)) {
						sinkSkipCounter.inc();
						LOG.error(blinkException.getErrorMessage() + " sql:" + sql);
					}
				}
				try {
					// sleep according to retryTimes
					Thread.sleep(1000 * retryTime);
				} catch (Exception e1) {
					//ignore
				}
			} finally {
				try {
					connection.close();
				} catch (Exception e) {
					LOG.error("", e);
				}
			}
		}


		// report metrics
		long end = System.currentTimeMillis();
		latencyGauge.report(end - start, 1);
		outTps.markEvent();
		outBps.markEvent(sql.length() * 2);
	}

	@Override
	public String getName() {
		return "rds";
	}

	@Override
	public synchronized void sync() throws IOException {
		if(!existsPrimaryKeys){
			return;
		} else {
			//使用synchronized关键字保证flush线程执行的时候，不会同时更新mapReduceBuffer的内容
			synchronized (mapReduceBuffer) {
				List<Row> addBuffer = new ArrayList<>();
				List<Row> deleteBuffer = new ArrayList<>();
				for (Tuple2<Boolean, Row> rowTuple2 : mapReduceBuffer.values()) {
					if (rowTuple2.f0) {
						addBuffer.add(rowTuple2.f1);
					} else {
						deleteBuffer.add(rowTuple2.f1);
					}
				}
				batchWrite(addBuffer);
				batchDelete(deleteBuffer);
				mapReduceBuffer.clear();
			}
		}
		lastFlushTime = System.currentTimeMillis();
		currentCount = 0;
	}

	private void initConnection() {
		SQLException exception = null;
		for (int i=0; i < maxRetryTime; i++) {
			try {
				if (null == connection) {
					connection = dataSource.getConnection();
				}
				if (connection.isClosed()) {
					connection = dataSource.getConnection();
				}
				exception = null;
				break;
			} catch (SQLException e) {
				exception = e;
				LOG.warn("get connection failed, retryTimes=" + i, e);
			}
		}

		if (exception != null || connection == null) {
			ErrorUtils.throwException(ConnectorErrors.INST.rdsGetConnectionError("Rds"), exception);
		}
	}

	private void batchWrite(List<Row> buffers) {
		try {
			initConnection();
			for (Row row : buffers) {
				if (writeAddBuffer == null) {
					writeAddBuffer = new ArrayList<>();
					this.curAddCount = 0;
				}
				writeAddBuffer.add(row);
				this.curAddCount++;
				if (curAddCount >= batchSize) {
					execBatchAdd();
					this.writeAddBuffer = null;
					this.curAddCount = 0;
				}
			}
			execBatchAdd();
		} finally {
			if (null != connection) {
				try {
					connection.close();
				} catch (SQLException e) {
					LOG.error("", e);
				}
			}
		}
	}
    
    private Map<String, Object> getInputData(Row row, RowTypeInfo rowTypeInfo) {
        Map<String, Object> inputData = new HashMap<>();
        for (String fieldName : rowTypeInfo.getFieldNames()) {
            Object value = row.getField(rowTypeInfo.getFieldIndex(fieldName));
            if (value instanceof java.sql.Timestamp) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String str = df.format((java.sql.Timestamp)value);
                value = str;
            }
            if (value instanceof java.math.BigDecimal) {
                value = ((java.math.BigDecimal)value).doubleValue();
            }
            inputData.put(fieldName, value);
        }
        return inputData;
    }
    
    private Object valueWrapper(Object val) {
        if(val == null) {
            return null;
        } else if (val instanceof String) {
            return "'" + val + "'";
        } else {
        	return val;
        }
    }
    
    private String getUpsertSql(
        Row row, RowTypeInfo columns, List<String> excludeKeys, String table,
        List<String> pkFields) {
        Map<String, Object> inputData = getInputData(row, rowTypeInfo);
        String pk = inputData.get(pkFields.get(0)) + "";
        LOG.info("inputDat={}", JSON.toJSONString(inputData));
        if (inputData.get("_optype") != null && "delete".equalsIgnoreCase(inputData.get("_optype").toString())) {
            String delSql = "delete from " + tableName  + " where " + pkFields.get(0) + "=" + valueWrapper(pk);
            LOG.info("delSql={}", delSql);
            return delSql;
        } else {
            String countSql = "select count(*) from " + tableName + " where " + pkFields.get(0) + "=" + valueWrapper(pk);
            LOG.info("countSql={}", countSql);
            Statement statement = null;
            ResultSet resultSet = null;
            int count = 0;
            try {
                statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
                resultSet = statement.executeQuery(countSql);
                if(resultSet.next()) {
                    count = resultSet.getInt(1);
                }
            } catch(Exception e) {
                LOG.info("count failed", e);
            } finally {
                if (resultSet != null) {
                    try {
                        resultSet.close();
                    } catch (SQLException e) {
                        resultSet = null;
                    }
                }
                if (statement != null) {
                    try {
                        statement.close();
                    } catch (SQLException e) {
                        statement = null;
                    }
                }
            }
            if (count > 0) {
                String updSql = "update " + tableName + " set ";
                for (String key : inputData.keySet()) {
                    if (key.equalsIgnoreCase(pkFields.get(0))
                        || key.equalsIgnoreCase("_optype")) {
                        continue;
                    }
                    updSql = updSql + key + "=" + valueWrapper(inputData.get(key));
                    updSql += ",";
                }
                updSql = updSql.substring(0, updSql.length()-1);
                updSql = updSql + " where " + pkFields.get(0) + "=" + valueWrapper(pk);
                LOG.info("updSql={}", updSql);
                return updSql;
            } else {
                List<String> colList = new ArrayList<>();
                for (String col : inputData.keySet()) {
                    if (col.equalsIgnoreCase("_optype")) {
                        continue;
                    }
                    colList.add(col);
                }
                String insSql = "insert into " + tableName + " (";
                for (int i = 0; i < colList.size(); i++) {
                    insSql = insSql + colList.get(i);
                    insSql += ",";
                }
                insSql = insSql.substring(0, insSql.length()-1);
                insSql += ") values (";
                for (int i = 0; i < colList.size(); i++) {
                    insSql = insSql + valueWrapper(inputData.get(colList.get(i)));
                    insSql += ",";
                }
                insSql = insSql.substring(0, insSql.length()-1);
                insSql += ")";
                LOG.info("insSql={}", insSql);
                return insSql;
            }
        }
    }

	private void execBatchAdd() {
		if( null == writeAddBuffer || writeAddBuffer.isEmpty()) {
			return;
		}

		int count = writeAddBuffer.size();
		int retriedTimes = 0;
		while (!writeAddBuffer.isEmpty() && retriedTimes++ < maxRetryTime) {
			long start = System.currentTimeMillis();
			Statement statement = null;
			int sinkCount = 0;
			try {
				connection.setAutoCommit(false);
				for (Row row : writeAddBuffer) {
					String sql = "";
					try {
						statement = connection.createStatement();
		                sql = getUpsertSql(row, rowTypeInfo, exceptUpdateKeys, tableName, pkFields);
		                int cnt = statement.executeUpdate(sql);
		                LOG.info("sql[{}],cnt={}", sql, cnt); 
	//	                statement.addBatch(sql);
						sinkCount++;
					} catch(Exception e) {
						LOG.error("sql[{}] exec failed", sql, e); 
					}
				}

				LOG.info("BatchUpdateSize [{}]", count);
				long s1 = System.currentTimeMillis();
//				statement.executeBatch();
//				statement.clearBatch();
				connection.commit();
				if (latencyGauge != null) {
					long s2 = System.currentTimeMillis();
					latencyGauge.report(s2 - s1, count);
				}
				if (outTps != null) {
					outTps.markEvent(count);
				}
				if (outBps != null) {
					// rough estimate
					outBps.markEvent(count * 1000);
				}
				TpsLimitUtils.limitTps(maxSinkTps, numTasks, start, sinkCount);

				lastFlushTime = System.currentTimeMillis();
				writeAddBuffer.clear();
				break;
			} catch (Exception e) {
				LOG.error("Batch write rds error, retry times=" + retriedTimes, e);
				if (connection != null) {
					try {
						LOG.warn("Transaction is being rolled back");
						connection.rollback();
					} catch (Exception ex) {
						LOG.warn("Rollback failed", ex);
					}
				}

				if (retriedTimes >= maxRetryTime) {
//					BlinkRuntimeException blinkException = ErrorUtils.getException(
//							ConnectorErrors.INST.rdsBatchWriteError("RDS",sql, String.valueOf(preparedStatement)),e);
//					if (SQLExceptionSkipPolicy.judge(dirtyDataStrategy, e.getErrorCode(), blinkException)) {
//						sinkSkipCounter.inc(count);
//						LOG.error(blinkException.getErrorMessage() + " sql:" + sql);
//					}
				}

				try {
					Thread.sleep(1000 * retriedTimes);
				} catch (Exception e1) {
					LOG.error("Thread sleep exception in RdsOutputFormat class", e1);
				}
			} finally {
				if (null != statement) {
					try {
						statement.close();
					} catch (SQLException e) {
						LOG.error("preparedStatement close error", e);
					}
				}
			}
		}
	}

	private void batchDelete(List<Row> buffers) {
		try {
			initConnection();
			for (Row row : buffers) {
				if (writeDelBuffer == null) {
					writeDelBuffer = new ArrayList<>();
					this.curDelCount = 0;
				}
				writeDelBuffer.add(row);
				this.curDelCount++;
				if (curDelCount >= batchSize) {
					execBatchDelete();
					this.writeDelBuffer = null;
					this.curDelCount = 0;
				}
			}
			execBatchDelete();
		} finally {
			if (null != connection) {
				try {
					connection.close();
				} catch (SQLException e) {
					LOG.error("", e);
				}
			}
		}
	}

	private void execBatchDelete() {
		if( null == writeDelBuffer || writeDelBuffer.isEmpty()) {
			return;
		}

		int count = writeDelBuffer.size();
		int retriedTimes = 0;
		while (!writeDelBuffer.isEmpty() && retriedTimes++ < maxRetryTime) {
			long start = System.currentTimeMillis();
			String sql = JdbcUtils.getDeleteSql(pkFields, tableName, true);
			PreparedStatement preparedStatement = null;
			int sinkCount = 0;
			try {
				connection.setAutoCommit(false);
				preparedStatement = connection.prepareStatement(sql);
				for (Row row : writeDelBuffer) {
					JdbcUtils.setDeleteStatement(preparedStatement, row, rowTypeInfo, pkFields);
					preparedStatement.addBatch();
					sinkCount++;
				}

				LOG.info("BatchDeleteSize [{}]", count);
				long s1 = System.currentTimeMillis();
				preparedStatement.executeBatch();
				connection.commit();
				if (latencyGauge != null) {
					long s2 = System.currentTimeMillis();
					latencyGauge.report(s2 - s1, count);
				}
				if (outTps != null) {
					outTps.markEvent(count);
				}
				if (outBps != null) {
					// rough estimate
					outBps.markEvent(count * 1000);
				}
				TpsLimitUtils.limitTps(maxSinkTps, numTasks, start, sinkCount);

				lastFlushTime = System.currentTimeMillis();
				writeDelBuffer.clear();
				break;
			} catch (SQLException e) {
				LOG.error("Batch delete rds error, retry times=" + retriedTimes, e);
				if (connection != null) {
					try {
						LOG.warn("Transaction is being rolled back");
						connection.rollback();
					} catch (Exception ex) {
						LOG.warn("Rollback failed", ex);
					}
				}

				if (retriedTimes >= maxRetryTime) {
					BlinkRuntimeException blinkException = ErrorUtils.getException(
							ConnectorErrors.INST.rdsBatchDeleteError("RDS",sql, String.valueOf(preparedStatement)),e);

					if (SQLExceptionSkipPolicy.judge(dirtyDataStrategy, e.getErrorCode(), blinkException)) {
						sinkSkipCounter.inc(count);
						LOG.error(blinkException.getErrorMessage() + " sql:" + sql);
					}
				}

				try {
					Thread.sleep(1000 * retriedTimes);
				} catch (Exception e1) {
					LOG.error("Thread sleep exception in RdsOutputFormat class", e1);
				}
			} finally {
				if (null != preparedStatement) {
					try {
						preparedStatement.close();
					} catch (SQLException e) {
						LOG.error("preparedStatement close error", e);
					}
				}
			}
		}
	}

	@Override
	public void close() throws IOException {
		if (flusher != null) {
			flusher.cancel();
			flusher = null;
		}
		sync();
		try {
			if (null != connection && !connection.isClosed()) {
				connection.close();
			}
		} catch (Exception e) {
			// ignore this exception
		}
		synchronized (RdsPgOutputFormat.class) {
			if (dataSourcePool.remove(dataSourceKey) && dataSource != null && !dataSource.isClosed()) {
				dataSource.close();
				dataSource = null;
			}
		}
	}

	/**
	 * construct create rds table statement sql
	 * @param schema  table schema
	 * @return
	 */
	public String getCreateTableSql(RichTableSchema schema, int varcharMaxLength) {
		int fieldsNum = schema.getColumnNames().length;
		List<String> resultList = new ArrayList<>();

		for(int i = 0; i < fieldsNum; i++) {
			String fieldName = schema.getColumnNames()[i];
			TypeInformation type = TypeConverters.createExternalTypeInfoFromDataType(schema.getColumnTypes()[i]);
			resultList.add(fieldName + " " + getRdsSqlType(type, varcharMaxLength));
		}
		Joiner joinerOnComma = Joiner.on(",");
		List<String> pks = schema.getPrimaryKeys();
		if (pks != null && !pks.isEmpty()) {
			String pkString = String.format("PRIMARY KEY(%s)", joinerOnComma.join(pks));
			resultList.add(pkString);
		}
		return String.format(CREATE_TABLE_SQL_TPL, tableName, joinerOnComma.join(resultList));
	}

	/**
	 * convert flink type to RDS sql type
	 */
	public String getRdsSqlType(TypeInformation type, int varcharMaxLength) {
		if (type.equals(Types.BOOLEAN)) {
			return "BOOLEAN";
		} else if (type.equals(Types.BYTE)) {
			return "TINYINT";
		} else if (type.equals(Types.SHORT)) {
			return "SMALLINT";
		} else if (type.equals(Types.INT)) {
			return "INT";
		} else if (type.equals(Types.LONG)) {
			return "BIGINT";
		} else if (type.equals(Types.FLOAT)) {
			return "FLOAT";
		} else if (type.equals(Types.BIG_DEC)) {    // FIXME if https://aone.alibaba-inc.com/code/D644520
			return "DECIMAL";
		} else if (type.equals(Types.DOUBLE)) {
			return "DOUBLE";
		} else if (type.equals(Types.SQL_DATE)) {
			return "DATE";
		} else if (type.equals(Types.SQL_TIME)) {
			return "TIME";
		} else  if (type.equals(Types.SQL_TIMESTAMP)) {
			return "TIMESTAMP";
		} else  if (type.equals(Types.STRING)) {
			return String.format("VARCHAR(%d)", varcharMaxLength);
		} else  if (type.equals(PrimitiveArrayTypeInfo.BYTE_PRIMITIVE_ARRAY_TYPE_INFO)) {
			return "VARBINARY";
		} else {
			throw new IllegalArgumentException("Unsupported sql column type " + type + " !");
		}
	}

	public RdsPgOutputFormat setDirtyDataStrategy(DirtyDataStrategy strategy) {
		dirtyDataStrategy = strategy;
		return this;
	}

	public static class Builder {
		private String url;
		private String tableName;
		private String userName;
		private String password;
		private RowTypeInfo rowTypeInfo;
		private List<String> pkField;

		private int maxRetryTime = 3;

		private String driverClassName = "com.mysql.jdbc.Driver";
		private int connectionMaxActive = 40;
		private int connectionInitialSize = 1;
		private int connectionMinIdle = 0;
		private boolean connectionTestWhileIdle = false;
		private int maxWait = 15000;
		private int removeAbandonedTimeout = 60 * 10;
		private int batchSize = 50;
		private int bufferSize = 500;
		private long flushIntervalMs = 5000;
		private List<String> exceptUpdateKeys = new ArrayList<>();
		private long maxSinkTps = -1;
		private boolean ignoreDelete = false;

		public Builder setIgnoreDelete(boolean ignoreDelete) {
			this.ignoreDelete = ignoreDelete;
			return this;
		}

		public Builder setMaxSinkTps(long maxSinkTps) {
			this.maxSinkTps = maxSinkTps;
			return this;
		}

		public Builder setBatchSize(int batchSize) {
			this.batchSize = batchSize;
			return this;
		}

		public Builder setBufferSize(int bufferSize) {
			this.bufferSize = bufferSize;
			return this;
		}

		public Builder setExceptUpdateKeys(List<String> exceptUpdateKeys) {
			if(null != exceptUpdateKeys) {
				this.exceptUpdateKeys = exceptUpdateKeys;
			}
			return this;
		}

		public Builder setFlushIntervalMs(long flushIntervalMs) {
			this.flushIntervalMs = flushIntervalMs;
			return this;
		}

		private DirtyDataStrategy dirtyDataStrategy = DirtyDataStrategy.EXCEPTION;

		void checkUserParameter(String target, String key) {
			if (StringUtils.isBlank(target)) {
				ErrorUtils.throwException(ConnectorErrors.INST.tableDDLConfigError(tableName, key));
			}
		}

		void checkIntervalParameter(String target, String key) {
			if (StringUtils.isBlank(target)) {
				ErrorUtils.throwException(ConnectorErrors.INST.tableDDLConfigError(tableName, key));
			}
		}

		void checkIntervalParameter(int target, String key) {
			if (target < 0) {
				ErrorUtils.throwException(ConnectorErrors.INST.tableDDLConfigError(tableName, key));
			}
		}

		public Builder setMaxWait(int maxWait) {
			checkIntervalParameter(maxWait, "maxWait");
			this.maxWait = maxWait;
			return this;
		}

		public Builder setRemoveAbandonedTimeout(int removeAbandonedTimeout) {
			checkIntervalParameter(removeAbandonedTimeout, "removeAbandonedTimeout");
			this.removeAbandonedTimeout = removeAbandonedTimeout;
			return this;
		}

		public Builder setUrl(String url) {
			checkUserParameter(url, "url");
			this.url = url;
			return this;
		}

		public Builder setTableName(String tableName) {
			checkUserParameter(tableName, "tableName");
			this.tableName = tableName;
			return this;
		}

		public String getTableName() {
			return  tableName;
		}

		public Builder setUserName(String userName) {
			checkUserParameter(userName, "userName");
			this.userName = userName;
			return this;
		}

		public Builder setPassword(String password) {
			checkUserParameter(password, "password");
			this.password = password;
			return this;
		}

		public Builder setRowTypeInfo(RowTypeInfo rowTypeInfo) {
			this.rowTypeInfo = rowTypeInfo;
			return this;
		}

		public Builder setDriverClassName(String driverClassName) {
			checkIntervalParameter(driverClassName, "driverClassName");
			this.driverClassName = driverClassName;
			return this;
		}

		public Builder setMaxRetryTime(int maxRetryTime) {
			checkIntervalParameter(maxRetryTime, "maxRetryTime");
			this.maxRetryTime = maxRetryTime;
			return this;
		}

		public Builder setPkField(List<String> pkField) {
			this.pkField = pkField;
			return this;
		}

		public Builder setConnectionMaxActive(int connectionMaxActive) {
			checkIntervalParameter(connectionMaxActive, "connectionMaxActive");
			this.connectionMaxActive = connectionMaxActive;
			return this;
		}

		public Builder setConnectionInitialSize(int connectionInitialSize) {
			checkIntervalParameter(connectionInitialSize, "connectionInitialSize");
			this.connectionInitialSize = connectionInitialSize;
			return this;
		}

		public Builder setConnectionMinIdle(int connectionMinIdle) {
			checkIntervalParameter(connectionMinIdle, "connectionMinIdle");
			this.connectionMinIdle = connectionMinIdle;
			return this;
		}

		public Builder setConnectionTestWhileIdle(boolean connectionTestWhileIdle) {
			this.connectionTestWhileIdle = connectionTestWhileIdle;
			return this;
		}

		public Builder setDirtyDataStrategy(DirtyDataStrategy strategy) {
			this.dirtyDataStrategy = strategy;
			return this;
		}

		public RdsPgOutputFormat build() {
			RdsPgOutputFormat outputFormat = new RdsPgOutputFormat(
					url,
					tableName,
					userName,
					password,
					rowTypeInfo,
					pkField,
					maxRetryTime,
					driverClassName,
					connectionMaxActive,
					connectionInitialSize,
					connectionMinIdle,
					connectionTestWhileIdle,
					maxWait,
					removeAbandonedTimeout);
			outputFormat.setDirtyDataStrategy(dirtyDataStrategy)
						.setBatchSize(batchSize)
						.setBufferSize(bufferSize)
						.setFlushIntervalMs(flushIntervalMs)
						.setExceptUpdateKeys(exceptUpdateKeys)
						.setMaxSinkTps(maxSinkTps)
						.setIgnoreDelete(ignoreDelete);
			return outputFormat;
		}
	}
}
