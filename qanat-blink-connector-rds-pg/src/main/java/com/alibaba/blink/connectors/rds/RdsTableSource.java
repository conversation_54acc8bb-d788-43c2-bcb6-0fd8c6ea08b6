/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.blink.connectors.rds;

import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.api.functions.AsyncTableFunction;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.plan.schema.IndexKey;
import org.apache.flink.table.sources.LookupConfig;
import org.apache.flink.table.sources.LookupableTableSource;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.TypeConverters;
import org.apache.flink.table.typeutils.BaseRowTypeInfo;

import com.alibaba.blink.connectors.rds.dim.RdsCacheAllRowFetcher;
import com.alibaba.blink.connectors.rds.dim.RdsConnectionParam;
import com.alibaba.blink.connectors.rds.dim.RdsRowFetcher;
import com.alibaba.blink.streaming.connectors.common.LookupFunctionWrapper;
import com.alibaba.blink.streaming.connectors.common.reload.CacheAllReloadConf;
import com.alibaba.blink.streaming.connectors.common.source.SourceFunctionTableSource;
import com.alibaba.blink.streaming.connectors.common.source.SourceUtils;
import com.alibaba.blink.table.cache.CacheConfig;
import com.alibaba.blink.table.cache.CacheStrategy;

public class RdsTableSource
		extends SourceFunctionTableSource<BaseRow>
		implements LookupableTableSource<BaseRow> {

	private final RichTableSchema richSchema;
	private final String sqlTableName;
	private final BaseRowTypeInfo rowTypeInfo;
	private final RdsConnectionParam param;
	private final CacheStrategy cacheStrategy;
	private final CacheConfig cacheConfig;

	public RdsTableSource(
			SourceFunction<BaseRow> sourceFunction,
			String sqlTableName,
			RichTableSchema schema,
			RdsConnectionParam param,
			CacheConfig cacheConfig) {
		super(sourceFunction);
		this.richSchema = schema;
		this.sqlTableName = sqlTableName;
		this.rowTypeInfo = TypeConverters.toBaseRowTypeInfo(schema.getResultType());
		this.param = param;
		this.cacheStrategy = cacheConfig.getCacheStrategy();
		this.cacheConfig = cacheConfig;
	}

	@Override
	public String explainSource() {
		return "RDS: [" + sqlTableName + "]";
	}

	@Override
	public TableSchema getTableSchema() {
		return SourceUtils.toTableSchema(richSchema);
	}

	@Override
	public DataType getReturnType() {
		return SourceUtils.toDataType(richSchema);
	}

	@Override
	public TableFunction<BaseRow> getLookupFunction(int[] lookupKeys) {
		IndexKey indexKey = SourceUtils.getSelectedIndexKey(richSchema, lookupKeys);
		if (cacheStrategy.isAllCache()) {
			CacheAllReloadConf reloadConf = new CacheAllReloadConf(
					cacheConfig.getTimeRangeBlacklist(),
					cacheConfig.getCacheScanLimit(),
					cacheStrategy.getTtlMs());
			return new LookupFunctionWrapper(new RdsCacheAllRowFetcher(
					sqlTableName,
					indexKey,
					reloadConf,
					rowTypeInfo,
					param));
		} else {
			return new LookupFunctionWrapper(new RdsRowFetcher(
					sqlTableName,
					indexKey,
					cacheStrategy,
					rowTypeInfo,
					param));
		}
	}

	@Override
	public AsyncTableFunction<BaseRow> getAsyncLookupFunction(int[] lookupKeys) {
		throw new UnsupportedOperationException("RDS do not support async join currently");
	}

	@Override
	public LookupConfig getLookupConfig() {
		// do not support async, return default
		return new LookupConfig();
	}
}
