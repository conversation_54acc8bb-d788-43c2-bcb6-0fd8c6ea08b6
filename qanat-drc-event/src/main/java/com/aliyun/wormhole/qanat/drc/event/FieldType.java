package com.aliyun.wormhole.qanat.drc.event;

public class FieldType {

	public static final int TYPE_INTEGER = 1; // int

	public static final int TYPE_LONG = 2; // long

	public static final int TYPE_DOUBLE = 3; // double ,bigdecimal

	public static final int TYPE_DATE = 4; // date , timestamp

	public static final int TYPE_STRING = 5; // string

	public static final int TYPE_NOT_SUPPORT = 100; // 不支持的类型
	
	public static DataMessage.Record.Field.Type parseDrcType(int type) {
        switch (type) {
        case FieldType.TYPE_STRING:
            return DataMessage.Record.Field.Type.STRING;
        case FieldType.TYPE_INTEGER:
            return DataMessage.Record.Field.Type.INT32;
        case FieldType.TYPE_LONG:
            return DataMessage.Record.Field.Type.INT64;
        case FieldType.TYPE_DOUBLE:
            return DataMessage.Record.Field.Type.DOUBLE;
        case FieldType.TYPE_DATE:
            return DataMessage.Record.Field.Type.TIMESTAMP;
        case FieldType.TYPE_NOT_SUPPORT:
            return DataMessage.Record.Field.Type.UNKOWN;
        default:
            return DataMessage.Record.Field.Type.UNKOWN;
        }
    }

	public static int parseDrcType(DataMessage.Record.Field.Type type) {
		switch (type) {
		case INT8:
			return FieldType.TYPE_INTEGER;
		case INT16:
			return FieldType.TYPE_INTEGER;
		case INT24:
			return FieldType.TYPE_INTEGER;
		case INT32:
			return FieldType.TYPE_INTEGER;
		case INT64:
			return FieldType.TYPE_LONG;
		case DECIMAL:
			return FieldType.TYPE_DOUBLE;
		case FLOAT:
			return FieldType.TYPE_DOUBLE;
		case DOUBLE:
			return FieldType.TYPE_DOUBLE;
		case NULL:
			return FieldType.TYPE_NOT_SUPPORT;
		case TIMESTAMP:
			return FieldType.TYPE_DATE;
		case DATE:
			return FieldType.TYPE_DATE;
		case TIME:
			return FieldType.TYPE_DATE;
		case DATETIME:
			return FieldType.TYPE_DATE;
		case YEAR:
			return FieldType.TYPE_DATE;
		case BIT:
			return FieldType.TYPE_NOT_SUPPORT;
		case ENUM:
			return FieldType.TYPE_NOT_SUPPORT;
		case SET:
			return FieldType.TYPE_NOT_SUPPORT;
		case BLOB:
			return FieldType.TYPE_NOT_SUPPORT;
		case GEOMETRY:
			return FieldType.TYPE_NOT_SUPPORT;
		case STRING:
			return FieldType.TYPE_STRING;
		case UNKOWN:
			return FieldType.TYPE_NOT_SUPPORT;
		default:
			return FieldType.TYPE_NOT_SUPPORT;
		}
	}
}
