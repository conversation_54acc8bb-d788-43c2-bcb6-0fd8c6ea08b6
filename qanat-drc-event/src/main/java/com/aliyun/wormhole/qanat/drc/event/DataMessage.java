package com.aliyun.wormhole.qanat.drc.event;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.Map.Entry;

import com.taobao.drc.client.message.ByteString;
import com.taobao.drc.client.message.Message;

/**
 * Message contains database updating data.
 *
 * <AUTHOR>
 */
public class DataMessage extends Message {

    /**
     * Record contains data of one record.
     *
     * <AUTHOR>
     */
    public static class Record {

        protected String opt;

        /* Record attributes. */
        protected Map<String, String> attributes;

        /* Fields */
        protected List<Field> fieldList;

        protected String timestamp;

        protected String safeTimestamp;

        public String getRegionId() {
            return regionId;
        }

        public void setRegionId(String regionId) {
            this.regionId = regionId;
        }

        /**
         * Field contains data of one field
         *
         * <AUTHOR>
         */
        public static class Field {

            public long length;

            public boolean primaryKey;

            public String name;

            public String type;

            public String encoding;

            public ByteString value;

            public boolean changeValue = true;


            public enum Type {
                INT8,
                INT16,
                INT24,
                INT32,
                INT64,
                DECIMAL,
                FLOAT,
                DOUBLE,
                NULL,
                TIMESTAMP,
                DATE,
                TIME,
                DATETIME,
                YEAR,
                BIT,
                ENUM,
                SET,
                BLOB,
                GEOMETRY,
                STRING,
                UNKOWN
            }

            public Field() {
                name = null;
                type = ""; // not existed in mysql
                length = 0;
                value = null;
                primaryKey = false;
            }

            public final boolean isPrimary() {
                return primaryKey;
            }

            public void setPrimary(boolean primary) {
                primaryKey = primary;
            }

            /**
             * Get the name of the field.
             *
             * @return the name of the field.
             */
            public final String getFieldname() {
                return name;
            }

            /**
             * Get the encoding of the field.
             *
             * @return the encoding of the field.
             */
            public final String getEncoding() {
                if (encoding.equalsIgnoreCase("utf8mb4"))
                    return "utf8";
                return encoding;
            }

            public static Type[] MYSQL_TYPES = new Type[256];

            {
                MYSQL_TYPES[0] = Type.DECIMAL;
                MYSQL_TYPES[1] = Type.INT8;
                MYSQL_TYPES[2] = Type.INT16;
                MYSQL_TYPES[3] = Type.INT32;
                MYSQL_TYPES[4] = Type.FLOAT;
                MYSQL_TYPES[5] = Type.DOUBLE;
                MYSQL_TYPES[6] = Type.NULL;
                MYSQL_TYPES[7] = Type.TIMESTAMP;
                MYSQL_TYPES[8] = Type.INT64;
                MYSQL_TYPES[9] = Type.INT24;
                MYSQL_TYPES[10] = Type.DATE;
                MYSQL_TYPES[11] = Type.TIME;
                MYSQL_TYPES[12] = Type.DATETIME;
                MYSQL_TYPES[13] = Type.YEAR;
                MYSQL_TYPES[14] = Type.DATETIME;
                MYSQL_TYPES[15] = Type.STRING;
                MYSQL_TYPES[16] = Type.BIT;
                //special
                MYSQL_TYPES[255] = Type.GEOMETRY;
                MYSQL_TYPES[254] = Type.STRING;
                MYSQL_TYPES[253] = Type.STRING;
                MYSQL_TYPES[252] = Type.BLOB;
                MYSQL_TYPES[251] = Type.BLOB;
                MYSQL_TYPES[250] = Type.BLOB;
                MYSQL_TYPES[249] = Type.BLOB;
                MYSQL_TYPES[248] = Type.SET;
                MYSQL_TYPES[247] = Type.ENUM;
                MYSQL_TYPES[246] = Type.DECIMAL;
            }

            /**
             * Get the enumerated type of the field.
             *
             * @return the enumerated type of the field.
             */
            public final Type getType() {
                return Type.valueOf(type);
            }

            public boolean isChangeValue() {
                return changeValue;
            }

            /**
             * Get the value of the field.
             *
             * @return the value {@linkByteString}
             */
            public final ByteString getValue() {
                return value;
            }

            /**
             * Clear the field.
             */
            public void clear() {
                type = ""; // unknown
                name = null;
                length = 0;
            }

            @Override
            public String toString() {
                StringBuilder builder = new StringBuilder();
                builder.append("Field name: " + name + System.getProperty("line.separator"));
                builder.append("Field type: " + type + System.getProperty("line.separator"));
                builder.append("Field length: " + length + System.getProperty("line.separator"));
                try {
                    if (value != null) {
                        if (encoding.equalsIgnoreCase("binary")) {
                            builder.append("Field value(binary): "
                                    + Arrays.toString(value.getBytes())
                                    + System.getProperty("line.separator"));
                        } else {
                            builder.append("Field value: "
                                    + value.toString(encoding)
                                    + System.getProperty("line.separator"));
                        }
                    } else {
                        builder.append("Field value: " + "null" +
                                System.getProperty("line.separator"));
                    }
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                    builder.append(System.getProperty("line.separator"));
                }
                return builder.toString();
            }
        } // End of Field

        /**
         *
         */
        public Record() {
            ending = false;
            attributes = new HashMap<String, String>();
        }

        boolean isEnding() {
            return ending;
        }

        /* Show whether decoding a record is completed. */
        private boolean ending = false;

        private String regionId;

        /* Record type. */
        public enum Type {
            INSERT(0),
            UPDATE(1),
            DELETE(2),
            REPLACE(3),
            HEARTBEAT(4),
            CONSISTENCY_TEST(5),
            BEGIN(6),
            COMMIT(7),
            DDL(8),
            ROLLBACK(9),
            DML(10),
            UNKNOWN(11);

            final int _value;

            Type(int value) {
                _value = value;
            }

            public int value() {
                return _value;
            }

            public static Type valueOf(int value) {
                for (Type type : Type.values()) {
                    if (type.value() == value)
                        return type;
                }

                return Type.UNKNOWN;
            }
        }


        /**
         * Get the type of the record in insert, delete, update and heartbeat.
         *
         * @return the type of the record.
         */
        public String getOpt() {
            return opt;
        }


        public String getId() {
            return getAttribute("record_id");
        }

        public String getDbname() {
            return getAttribute("db");
        }

        public String getTablename() {
            return getAttribute("table_name");
        }

        public String getCheckpoint() {
            return getAttribute("checkpoint");
        }

        @Deprecated
        public String getMetadataVersion() {
            return getAttribute("meta");
        }

        public String getTimestamp() {
            return timestamp;
        }

        public String getSafeTimestamp(){
            return safeTimestamp;
        }

        public String getServerId() {
            return getAttribute("instance");
        }

        public String getPrevId(){
            return getAttribute("prev_id");
        }

        public String getServerSeq(){
            return getAttribute("server_id");
        }

        public String getPrevServerSeq(){
            return getAttribute("prev_server_id");
        }

        public String getPrimaryKeys() {
            return getAttribute("primary");
        }

        public String getTraceInfo() {
            return "";
        }

        public String getUniqueColNames() {
            return getAttribute("unique");
        }

        public boolean isQueryBack() {
            String cate = getAttribute("source_category");
            if (cate.equalsIgnoreCase("full_recorded") ||
                    cate.equalsIgnoreCase("part_recorded") ||
                    cate.equalsIgnoreCase("full_faked")) {
                return false;
            } else {
                return true;
            }
        }

        /**
         * Now the api takes on different behavior between MYSQL and OCEANBASE,
         * for MYSQL, it returns true because the record is the LAST record in
         * the logevent, while for OCEANBASE, it is true because the record is
         * the first one.
         * TBD: server for mysql need change its behavior the same as OCEANBASE
         *
         * @return
         */
        public boolean isFirstInLogevent() {
            String isFirstLogevent = getAttribute("logevent");
            if (isFirstLogevent != null && isFirstLogevent.equals("1"))
                return true;
            return false;
        }

        public String getAttribute(final String key) {
            return attributes.get(key);
        }

        public Map<String, String> getAttributes() {
            return attributes;
        }

        public int getFieldCount() {
            getFieldList();
            if(fieldList==null){
                return 0;
            }
            return fieldList.size();
        }

        /**
         * Get the field list.
         *
         * @return the field list.
         */
        public List<Field> getFieldList() {
            return fieldList;
        }

        /**
         * Add one attribute to the record.
         *
         * @param key   the name of the attribute.
         * @param value the value of the attribute.
         */
        public void addAttribute(final String key, final String value) {
            attributes.put(key, value);
        }

        public byte[] getRawData() {
            return null;
        }

        public void setOpt(String opt) {
            this.opt = opt;
        }

        public void setFieldList(List<Field> fieldList) {
            this.fieldList = fieldList;
        }

        public void setTimestamp(String timestamp) {
            this.timestamp = timestamp;
        }

        public void setSafeTimestamp(String safeTimestamp) {
            this.safeTimestamp = safeTimestamp;
        }

        public String getThreadId() throws  Exception {
            return getAttribute("threadid");
        }

        public String getTraceId() throws Exception {
            return getAttribute("traceid");
        }

        public void parse(final byte[] data) throws Exception {
            throw new IOException(Record.class.getName() + " not support parse from raw data");
        }

        @Override
        public String toString() {
            StringBuilder builder = new StringBuilder();
            for (Entry<String, String> entry : attributes.entrySet()) {
                builder.append(entry.getKey() + ":" + entry.getValue());
                builder.append(System.getProperty("line.separator"));
            }
            builder.append(System.getProperty("line.separator"));
            for (Field field : fieldList) {
                builder.append(field.toString());
            }
            builder.append(System.getProperty("line.separator"));
            return builder.toString();
        }


    } // End of Record

    /* Record list. */
    private final List<Record> records;

    /**
     * Constructor of DataMessage, the type is 100 by default.
     */
    public DataMessage() {
        super();
        type = 100;
        records = new ArrayList<Record>();
    }

    /**
     * Get the number of all records in the message.
     *
     * @return the number of records.
     */
    public int getRecordCount() {
        return records.size();
    }

    /**
     * Get the list of records.
     *
     * @return the list of records.
     */
    public List<Record> getRecordList() {
        return records;
    }


    public void clear() {
        super.clear();
        records.clear();
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append(super.toString());
        for (Record record : records) {
            builder.append(record.toString());
        }
        builder.append(System.getProperty("line.separator"));
        return builder.toString();
    }

    public void addRecord(Record r) {
        records.add(r);
    }
}
