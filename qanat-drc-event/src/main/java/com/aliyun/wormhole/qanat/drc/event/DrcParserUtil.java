package com.aliyun.wormhole.qanat.drc.event;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;

public class DrcParserUtil {
	
	public static String parseValue(DataMessage.Record.Field field, DataMessage.Record.Field.Type type) {
		try {
			String strValue = field.getValue().toString("utf-8");
			if (isDate(type) && strValue.matches("^\\d+$")) {
//				strValue = new Timestamp(Long.parseLong(strValue) * 1000).toString();
				DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				strValue = dateFormat.format(new Timestamp(Long.parseLong(strValue) * 1000));
			}
			return strValue;
		} catch (Exception e) {
			return null;
		}
	}

	public static boolean isDate(DataMessage.Record.Field.Type type) {
		if (type.equals(DataMessage.Record.Field.Type.DATE) || type.equals(DataMessage.Record.Field.Type.DATETIME)
				|| type.equals(DataMessage.Record.Field.Type.TIMESTAMP)) {
			return true;
		}
		return false;
	}
	
}
