package com.aliyun.wormhole.qanat.drc.event;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.aliyun.wormhole.qanat.drc.event.DataMessage.Record.Field;

public class DrcMessageParser {

	public static DbEventInfo parse(DataMessage.Record record, String opType) {
		DbEventInfo dbEventInfo = new DbEventInfo();
		dbEventInfo.setTs(record.getTimestamp());
		String dbName = record.getDbname().toLowerCase();
		String tableName = record.getTablename().toLowerCase();
		if (RowEventType.INSERT.equalsIgnoreCase(opType)) {
			dbEventInfo.setEventType(DbEvent.INSERT);
			Set<String> fieldSet = new HashSet<String>();
			Set<String> pkFieldSet = new HashSet<String>();
			Map<String, DbEventFieldInfo> fieldData = new HashMap<String, DbEventFieldInfo>();
			for (DataMessage.Record.Field field : record.getFieldList()) {
				String filedName = field.getFieldname().toLowerCase();
				DataMessage.Record.Field.Type type = field.getType();
				if (field.isPrimary()) {
					pkFieldSet.add(filedName);
				}
				int eventType = FieldType.parseDrcType(type);
				DbEventFieldInfo fieldInfo = new DbEventFieldInfo();
				fieldSet.add(filedName);
				fieldInfo.setFieldName(filedName); // 字段名称
				fieldInfo.setFieldType(eventType); // 事件类型
				String fieldValue = null;
				if (null != field.getValue()) {
					fieldValue = DrcParserUtil.parseValue(field, type);
				}
				fieldInfo.setNewValue(fieldValue); // insert 有新值
				fieldInfo.setOldValue(null); // insert 无新值
				fieldData.put(filedName, fieldInfo);
			}
			dbEventInfo.setFieldValues(fieldData);
			dbEventInfo.setDbName(dbName);
			dbEventInfo.setTableName(tableName);
			dbEventInfo.setFieldSet(fieldSet);
			dbEventInfo.setPkField(pkFieldSet);
		}
		if (RowEventType.DELETE.equalsIgnoreCase(opType)) {
			dbEventInfo.setEventType(DbEvent.DELETE);
			Set<String> fieldSet = new HashSet<String>();
			Set<String> pkFieldSet = new HashSet<String>();
			Map<String, DbEventFieldInfo> fieldData = new HashMap<String, DbEventFieldInfo>();
			for (DataMessage.Record.Field field : record.getFieldList()) {
				String filedName = field.getFieldname().toLowerCase();
				DataMessage.Record.Field.Type type = field.getType();
				if (field.isPrimary()) {
					pkFieldSet.add(filedName);
				}
				int eventType = FieldType.parseDrcType(type);
				DbEventFieldInfo fieldInfo = new DbEventFieldInfo();
				fieldSet.add(filedName);
				fieldInfo.setFieldName(filedName); // 字段名称
				fieldInfo.setFieldType(eventType); // 事件类型
				String fieldValue = null;
				if (null != field.getValue()) {
					fieldValue = DrcParserUtil.parseValue(field, type);
				}
				fieldInfo.setOldValue(fieldValue); // delete 有旧值
				fieldInfo.setNewValue(null); // delete 无新值
				fieldData.put(filedName, fieldInfo);
			}
			dbEventInfo.setFieldValues(fieldData);
			dbEventInfo.setDbName(dbName);
			dbEventInfo.setTableName(tableName);
			dbEventInfo.setFieldSet(fieldSet);
			dbEventInfo.setPkField(pkFieldSet);
		}
		if (RowEventType.UPDATE.equalsIgnoreCase(opType)) {
			dbEventInfo.setEventType(DbEvent.UPDATE);
			Set<String> fieldSet = new HashSet<String>();
			Set<String> pkFieldSet = new HashSet<String>();
			Map<String, DbEventFieldInfo> fieldData = new HashMap<String, DbEventFieldInfo>();
			List<Field> fields = record.getFieldList();
			for (int i = 0; i < fields.size(); i += 2) {
				Field field = fields.get(i);
				String filedName = field.getFieldname().toLowerCase();
				DataMessage.Record.Field.Type type = field.getType();
				if (field.isPrimary()) {
					pkFieldSet.add(filedName);
				}
				int eventType = FieldType.parseDrcType(type);
				DbEventFieldInfo fieldInfo = new DbEventFieldInfo();
				fieldSet.add(filedName);
				fieldInfo.setFieldName(filedName); // 字段名称
				fieldInfo.setFieldType(eventType); // 事件类型
				String fieldValueOld = null;
				if (null != field.getValue()) {
					fieldValueOld = DrcParserUtil.parseValue(field, type);
				}
				
				Field fieldNew = fields.get(i+1);
				String fieldValueNew = null;
				if (null != fieldNew.getValue()) {
					fieldValueNew = DrcParserUtil.parseValue(fieldNew, type);
				}
				fieldInfo.setOldValue(fieldValueOld); // update 有新值
				fieldInfo.setNewValue(fieldValueNew); // new 有新值
				fieldData.put(filedName, fieldInfo);
			}
			dbEventInfo.setFieldValues(fieldData);
			dbEventInfo.setDbName(dbName);
			dbEventInfo.setTableName(tableName);
			dbEventInfo.setFieldSet(fieldSet);
			dbEventInfo.setPkField(pkFieldSet);
		}

		return dbEventInfo;
	}

	
}
