package com.aliyun.qanat.flink.connectors.option;

import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.configuration.ConfigOptions;

public class Options {
	public static final ConfigOption<String> JDBC_URL = ConfigOptions.key("jdbcUrl")
            .stringType()
            .noDefaultValue();
	
	public static final ConfigOption<String> USERNAME = ConfigOptions.key("username")
            .stringType()
            .noDefaultValue();
	public static final ConfigOption<String> PASSWORD = ConfigOptions.key("password")
            .stringType()
            .noDefaultValue();

    public static final ConfigOption<String> TABLE_NAME = ConfigOptions.key("table_name")
            .stringType()
            .noDefaultValue();

    public static final ConfigOption<String> OBJECT_UNIQUE_CODE = ConfigOptions.key("object_unique_code")
            .stringType()
            .noDefaultValue();

    public static final ConfigOption<String> OBJECT_PK = ConfigOptions.key("object_pk")
            .stringType()
            .defaultValue("id");

    public static final ConfigOption<String> FIELDS = ConfigOptions.key("fields")
            .stringType()
            .noDefaultValue();

    public static final ConfigOption<String> DISTRIBUTE_KEY = ConfigOptions.key("distribute_key")
            .stringType()
            .noDefaultValue();

    public static final ConfigOption<String> DB_NAME = ConfigOptions.key("db_name")
            .stringType()
            .noDefaultValue();
}