package com.aliyun.qanat.flink.connectors.sink;

import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.table.connector.sink.DynamicTableSink;
import org.apache.flink.table.connector.sink.SinkFunctionProvider;
import org.apache.flink.table.types.DataType;

import com.aliyun.qanat.flink.connectors.BeimingRtdw;

public class BeimingRtdwTableSink implements DynamicTableSink {
	
	private final DataType dataType;
    private final BeimingRtdw conf;
	
	public BeimingRtdwTableSink(DataType dataType, BeimingRtdw conf) {
		this.dataType = dataType;
        this.conf = conf;
	}

	@Override
	public String asSummaryString() {
		return "Sink For Beiming RealTime DataWarehouse";
	}

	@Override
	public DynamicTableSink copy() {
		return new BeimingRtdwTableSink(dataType, conf);
	}

	@Override
	public ChangelogMode getChangelogMode(ChangelogMode requestedMode) {
		return requestedMode;
	}

	@Override
	public SinkRuntimeProvider getSinkRuntimeProvider(Context context) {
    	final BeimingRtdwSinkFunction sourceFunction = new BeimingRtdwSinkFunction(conf);
		return SinkFunctionProvider.of(sourceFunction);
	}

}
