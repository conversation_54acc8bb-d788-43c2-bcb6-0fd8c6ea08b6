package com.aliyun.qanat.flink.connectors;

import java.util.HashSet;
import java.util.Set;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.configuration.ReadableConfig;
import org.apache.flink.table.connector.sink.DynamicTableSink;
import org.apache.flink.table.factories.DynamicTableSinkFactory;
import org.apache.flink.table.factories.FactoryUtil;
import org.apache.flink.table.types.DataType;

import com.aliyun.qanat.flink.connectors.option.Options;
import com.aliyun.qanat.flink.connectors.sink.BeimingRtdwTableSink;

public class BeimingRtdwTableFactory implements DynamicTableSinkFactory {

	@Override
	public String factoryIdentifier() {
		return "beiming-rtdw";
	}

	@Override
	public Set<ConfigOption<?>> optionalOptions() {
		final Set<ConfigOption<?>> options = new HashSet<>();
		options.add(Options.DB_NAME);
		options.add(Options.JDBC_URL);
		options.add(Options.USERNAME);
		options.add(Options.PASSWORD);
		options.add(Options.OBJECT_PK);
		options.add(Options.DISTRIBUTE_KEY);
		options.add(Options.FIELDS);
		return options;
	}

	@Override
	public Set<ConfigOption<?>> requiredOptions() {
		final Set<ConfigOption<?>> options = new HashSet<>();
		options.add(Options.OBJECT_UNIQUE_CODE);
		options.add(Options.TABLE_NAME);
		return options;
	}

	@Override
	public DynamicTableSink createDynamicTableSink(Context context) {
		final FactoryUtil.TableFactoryHelper helper = FactoryUtil.createTableFactoryHelper(this, context);
        helper.validate();
        final ReadableConfig options = helper.getOptions();
		if (StringUtils.isBlank(options.get(Options.DB_NAME))
				&& StringUtils.isBlank(options.get(Options.JDBC_URL))
				&& StringUtils.isBlank(options.get(Options.USERNAME))
				&& StringUtils.isBlank(options.get(Options.PASSWORD))) {
			throw new RuntimeException("db_name or jdbcUrl/username/password are necessary");
		}
		if (StringUtils.isBlank(options.get(Options.OBJECT_UNIQUE_CODE))) {
			throw new RuntimeException("object_unique_code is necessary");
		}
		if (StringUtils.isBlank(options.get(Options.OBJECT_PK))) {
			throw new RuntimeException("object_pk is necessary");
		}

		String jdbcUrl = null;
		String username = null;
		String password = null;
		if (StringUtils.isNotBlank(options.get(Options.DB_NAME))) {
			String dbMetaStr = QanatDataSourceUtils.getDbMeta(options.get(Options.DB_NAME), Thread.currentThread().getContextClassLoader());
			JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
			if (StringUtils.isNotBlank(dbMetaStr) || dbMetaJson == null) {
				throw new RuntimeException("Get connection to beiming rtdw failed");
			}
			jdbcUrl = dbMetaJson.getString("jdbcUrl");
			username = dbMetaJson.getString("username");
			password = dbMetaJson.getString("password");
		} else {
			jdbcUrl = options.get(Options.JDBC_URL);
			username = options.get(Options.USERNAME);
			password = options.get(Options.PASSWORD);
		}

        BeimingRtdw conf = new BeimingRtdw();
        conf.setJdbcUrl(jdbcUrl);
        conf.setUsername(username);
        conf.setPassword(password);
        conf.setTableName(options.get(Options.TABLE_NAME));
        conf.setObjectUniqueCode(options.get(Options.OBJECT_UNIQUE_CODE));
        conf.setObjectPk(options.get(Options.OBJECT_PK));
        conf.setFields(options.get(Options.FIELDS));
        conf.setDistributeKey(options.get(Options.DISTRIBUTE_KEY));
        
        final DataType producedDataType = context.getCatalogTable().getResolvedSchema().toPhysicalRowDataType();
		return new BeimingRtdwTableSink(producedDataType, conf);
	}
}