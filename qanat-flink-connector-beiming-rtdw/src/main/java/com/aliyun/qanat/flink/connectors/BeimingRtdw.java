package com.aliyun.qanat.flink.connectors;

import java.io.Serializable;

public class BeimingRtdw implements Serializable {
	private static final long serialVersionUID = -946687677246913738L;
	private String jdbcUrl;
	private String username;
	private String password;
	private String dbName;
	private String tableName;
	private String objectUniqueCode;
	private String objectPk;
	private String fields;
	private String distributeKey;

	public String getJdbcUrl() {
		return jdbcUrl;
	}

	public void setJdbcUrl(String jdbcUrl) {
		this.jdbcUrl = jdbcUrl;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getDbName() {
		return dbName;
	}

	public void setDbName(String dbName) {
		this.dbName = dbName;
	}

	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public String getObjectUniqueCode() {
		return objectUniqueCode;
	}

	public void setObjectUniqueCode(String objectUniqueCode) {
		this.objectUniqueCode = objectUniqueCode;
	}

	public String getObjectPk() {
		return objectPk;
	}

	public void setObjectPk(String objectPk) {
		this.objectPk = objectPk;
	}

	public String getFields() {
		return fields;
	}

	public void setFields(String fields) {
		this.fields = fields;
	}

	public String getDistributeKey() {
		return distributeKey;
	}

	public void setDistributeKey(String distributeKey) {
		this.distributeKey = distributeKey;
	}
}
