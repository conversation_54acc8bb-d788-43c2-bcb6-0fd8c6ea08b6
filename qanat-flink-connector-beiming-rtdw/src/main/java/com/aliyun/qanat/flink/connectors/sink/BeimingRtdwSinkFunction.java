package com.aliyun.qanat.flink.connectors.sink;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.flink.table.data.RowData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.qanat.flink.connectors.BeimingRtdw;
import com.aliyun.wormhole.qanat.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.datasource.RdsConnectionParam;

public class BeimingRtdwSinkFunction extends RichSinkFunction<RowData> {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -4224992614418251706L;

	private static final Logger log = LoggerFactory.getLogger(BeimingRtdwSinkFunction.class);
	
	private BeimingRtdw conf;
	
    private Connection conn = null;
    private RdsConnectionParam param;
    private int retryIntervalMs = 200;
    private int maxRetryTime = 3;
    private String objectUniqueCode;
	private String tableName;
    protected String pkField;
    private List<String> limitFieldList = new ArrayList<>();
    private String distributeKey;
	
	public BeimingRtdwSinkFunction(BeimingRtdw conf) {
		this.conf = conf;
	}

	public void open(Configuration parameters) throws Exception {
        super.open(parameters);
		boolean initSuccess = false;
        String lastErrorMsg = null;

        String jdbcUrl = conf.getJdbcUrl();
        String username = conf.getUsername();
        String password = conf.getPassword();
		objectUniqueCode = conf.getObjectUniqueCode();
        tableName = StringUtils.isNotBlank(conf.getTableName()) ? conf.getTableName() : objectUniqueCode;
        pkField = conf.getObjectPk();
        distributeKey = conf.getDistributeKey();
        String limitFields = conf.getFields();
        if (StringUtils.isNotBlank(limitFields)) {
        	limitFieldList = Arrays.asList(limitFields.split(","));
            if (StringUtils.isNotBlank(distributeKey) && !limitFieldList.contains(distributeKey)) {
                log.error("BeimingRtdwSinkFunction when distributeKey is not existed in fields");
            	throw new RuntimeException("BeimingRtdwSinkFunction when distributeKey is not existed in fields");
            }
        }
        
        int retris = 0;
        while (retris < 10) {
	        try {
	            synchronized (BeimingRtdwSinkFunction.class) {
	                param = new RdsConnectionParam();
	                param.setUrl(jdbcUrl)
	        	        .setUserName(username)
	        	        .setPassword(password);
	            	conn = QanatDatasourceHandler.connectToTable(param);
	            }
	            initSuccess = true;
	            break;
	        } catch (Exception e) {
	            retris++;
	            lastErrorMsg = e.getMessage();
	            log.error("sink open failed {} times, error:{}", retris, e.getMessage(), e);
	            try {
					Thread.sleep(100);
				} catch (InterruptedException e1) {}
	        }
        }
        if (!initSuccess) {
            log.error("BeimingRtdwSinkFunction open failed:{}, then try to failover", lastErrorMsg);
        	throw new RuntimeException("BeimingRtdwSinkFunction open failed:" + lastErrorMsg + ", then try to failover");
        }
	}
	
	public void invoke(RowData record, Context context) {
		String traceId = UUID.randomUUID().toString();
        Statement statement = null;
        try {
            String msg = new String(record.getString(0).toBytes(), "UTF-8");
			if (record.getArity() > 1) {
				traceId = new String(record.getString(1).toBytes(), "UTF-8");
			}
    		log.info("{} msg={}", traceId, msg);
    		
            JSONObject event = JSON.parseObject(msg);
            JSONObject objectInst = event.getJSONObject("extParam").getJSONObject("objectInstanceVO");
            String objectUniqueCodeFromMsg = objectInst.getString("objectUniqueCode");
            if (!objectUniqueCode.equalsIgnoreCase(objectUniqueCodeFromMsg)) {
                log.info("{} objectUniqueCode:{} in meta is not equals to msq:{}", traceId, objectUniqueCode, objectUniqueCodeFromMsg);
                return;
            }
            
            String sql = buildHoloDmlSqlFromDbEvent(tableName, pkField, objectInst.getString("objectBizId"), event.getString("operateType"), objectInst.getJSONObject("fieldMap"), limitFieldList, distributeKey);
            log.info("{} sql={}", traceId, sql);
            if (StringUtils.isBlank(sql)) {
                log.info("{} nothing to do", traceId);
            	return;
            }
            long startTs = System.currentTimeMillis();
    		int retryTime = 0;
    		while (retryTime < maxRetryTime) {
    			try {
    	            if (conn == null || conn.isClosed()) {
		                long startTsConn = System.currentTimeMillis();
    	            	conn = QanatDatasourceHandler.connectToTable(param);
			            log.info("{} retry:{} cost={}", traceId, retryTime, System.currentTimeMillis()-startTsConn);
    	            }
		            statement = conn.createStatement();
		            int execCnt = statement.executeUpdate(sql);
		            log.info("{} after execCnt={}, cost={}", traceId, execCnt, System.currentTimeMillis() - startTs);
		            
	                log.info("{} BeimingRtdwSinkFunction finish", traceId);
		            break;
    			} catch (SQLException e) {
    				log.error("{} sqlexception with retry:{} error:{}", traceId, retryTime, e.getMessage(), e);
					retryTime++;
					if (retryTime == maxRetryTime) {
						throw new RuntimeException(e);
					}
    				try {
						Thread.sleep(retryIntervalMs * retryTime);
    				} catch (Exception e1) {
    					//ignore
    				}
    			} finally {
    	            if (statement != null) {
    	                try {
    	                    statement.close();
    	                } catch (SQLException e) {
    	                	log.error("{} statement close failed", traceId, e);
    	                }
    	            }
    	            if (conn != null) {
    	                try {
    	                	conn.close();
    	                } catch (SQLException e) {
    	                	log.error("{} connection close failed", traceId, e);
    	                }
    	            }
    				
    			}
    		}
        } catch(Exception e) {
            log.error("{} BeimingRtdwSinkFunction failed:{}", traceId, e.getMessage(), e);
        }
	}
	
	private String buildHoloDmlSqlFromDbEvent(String tableName, String pkField, String pkValue, String operateType, JSONObject filedMap, List<String> limitFieldList, String distributeKey) {
        List<String> fieldList = new ArrayList<>();
        List<String> fieldValueList = new ArrayList<>();
        List<String> updateKVList = new ArrayList<>();
        fieldList.add(pkField);
        fieldValueList.add("'" + pkValue + "'");
        
        Set<String> pkFields = new HashSet<>();
        pkFields.add(pkField);
        if (StringUtils.isNotBlank(distributeKey) && !distributeKey.equalsIgnoreCase(pkField)) {
        	pkFields.add(distributeKey);
        }
        
        String distributeKeyValue = null;
        for (String fieldCode : filedMap.keySet()) {
        	if (CollectionUtils.isNotEmpty(limitFieldList) && !limitFieldList.contains(fieldCode)) {
        		continue;
        	}
        	if (StringUtils.isNotBlank(distributeKey) && distributeKey.equalsIgnoreCase(fieldCode)) {
        		distributeKeyValue = filedMap.getString(fieldCode);
        	}
        	
            fieldList.add(fieldCode);
            
            if (filedMap.getString(fieldCode) != null) {
            	fieldValueList.add("'" + filedMap.getString(fieldCode) + "'");
            	if (StringUtils.isBlank(distributeKey) || !distributeKey.equalsIgnoreCase(fieldCode)) {
            		updateKVList.add(fieldCode + "=excluded." + fieldCode);
            	}
            } else {
            	fieldValueList.add("NULL");
            	if (StringUtils.isBlank(distributeKey) || !distributeKey.equalsIgnoreCase(fieldCode)) {
            		updateKVList.add(fieldCode + "=excluded." + fieldCode);
            	}
            }
        }
        
        if (CollectionUtils.isEmpty(updateKVList)) {
        	return null;
        }
        
        if ("CREATE".equalsIgnoreCase(operateType) || "UPDATE".equalsIgnoreCase(operateType)) {
            return String.format("INSERT INTO %s(%s) VALUES(%s) ON CONFLICT (%s) DO UPDATE SET %s", tableName, StringUtils.join(fieldList, ","), StringUtils.join(fieldValueList, ","), StringUtils.join(pkFields, ","), StringUtils.join(updateKVList, ","));
        } else if ("DELETE".equalsIgnoreCase(operateType)) {
        	if (StringUtils.isBlank(distributeKey)) {
        		return  String.format("DELETE FROM %s WHERE %s='%s'", tableName, pkField, pkValue);
        	} else {
        		return  String.format("DELETE FROM %s WHERE %s='%s' AND %s='%s'", tableName, pkField, pkValue, distributeKey, distributeKeyValue);
        	}
        }
        return null;
    }
}