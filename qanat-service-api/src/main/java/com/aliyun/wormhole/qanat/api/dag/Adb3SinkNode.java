package com.aliyun.wormhole.qanat.api.dag;

import lombok.Data;

@Data
public class Adb3SinkNode extends Node {

    /**
     * 
     */
    private static final long serialVersionUID = 9071414235143178188L;
    private String srcDsName;
    private String dstDsName;
    
    public Adb3SinkNode() {}
    
    public Adb3SinkNode(String id, Dag dag) {
        super(id, dag);
        this.setNodeAction(NodeAction.STREAM);
        this.setAction("com.aliyun.wormhole.qanat.job.QanatRdsSinkJobProcessor");
    }
}
