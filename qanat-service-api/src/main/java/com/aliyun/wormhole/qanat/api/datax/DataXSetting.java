package com.aliyun.wormhole.qanat.api.datax;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import lombok.Data;

@Data
public class DataXSetting implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = -4804811386706452744L;
    
    private Speed speed;
    private ErrorLimit errorLimit;
    
    public DataXSetting() {
        
    }
    
    public DataXSetting(Integer channel) {
        speed = new Speed();
        speed.setChannel(channel);
    }
    
    public Map<String, Object> getJsonMap() {
        Map<String, Object> settingMap = new HashMap<>();
        if (speed != null) {
            Map<String, Object> speedMap = new HashMap<>();
            speedMap.put("channel", speed.getChannel());
            speedMap.put("byte", speed.getByteNum());
            settingMap.put("speed", speedMap);
        }
        if (errorLimit != null) {
            Map<String, Object> errorLimitMap = new HashMap<>();
            errorLimitMap.put("record", errorLimit.getRecord());
            errorLimitMap.put("percentage", errorLimit.getPercentage());
            settingMap.put("errorLimit", errorLimitMap);
        }
        return settingMap;
    }
    
    @Data
    public class Speed {
        
        private Integer channel;
        private Integer byteNum;
        
    }
    
    @Data
    public class ErrorLimit {
        
        private Integer record;
        private BigDecimal percentage;
        
    }
    
}
