package com.aliyun.wormhole.qanat.api.dto;

import lombok.Data;

@Data
public class TaskInstanceRequest extends ServiceBaseRequest {
    /**
     * task_instance.id (主键)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private Long id;

    /**
     * task_instance.task_id (任务id)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private Long taskId;

    /**
     * task_instance.operator (操作人工号)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private String operator;

    /**
     * task_instance.status (任务执行状态)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private Byte status;

    /**
     * task_instance.parent_instance_id (父任务实例id)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private Long parentInstanceId;

    /**
     * task_instance.external_inst_id (外部任务实例id(SchedulerX任务实例id))
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private String externalInstId;

    /**
     * task_instance.task_name (任务名称)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private String taskName;
    
    private String appName;

    /**
     * task_instance.external_id (外部任务id(SchedulerX任务id))
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private String externalId;

    /**
     * task_instance.host_addr (任务执行主机地址)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private String hostAddr;

    /**
     * task_instance.node_action
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private String nodeAction;
}