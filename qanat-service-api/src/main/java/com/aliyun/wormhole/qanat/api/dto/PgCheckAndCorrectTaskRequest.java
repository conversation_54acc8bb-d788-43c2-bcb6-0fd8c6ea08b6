package com.aliyun.wormhole.qanat.api.dto;

import java.io.Serializable;

import lombok.Data;

@Data
public class PgCheckAndCorrectTaskRequest extends DatatubeRequest {

    private String taskName;

    private String taskDesc;
    
    private String tableName;
    
    private DsInfo srcDs;
    
    private String pgTableName;
    
    private BlinkConf blinkConf;
    
    private String whereClause;
    
    @Data
    public class BlinkConf implements Serializable {
        private String queueName;
        private String clusterId;
        private String folderName;
    }
    
    @Data
    public class DsInfo implements Serializable {
        private String dsName;
        private String dsType;
        private String meta;
    }
}
