package com.aliyun.wormhole.qanat.api.datax;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class DataXMetaQReader extends DataXReader {
    /**
     * 
     */
    private static final long serialVersionUID = -4804811386706452744L;
    
    private String accessId;
    private String accessKey;
    private String consumerId;
    private String topicName;
    private String subExpression;
    private String onsChannel;
    private String domainName;
    private String contentType;
    private String beginOffset;
    private String nullCurrentOffset;
    private String fieldDelimiter;
    private List<String> column;
    
    @Override
    public Map<String, Object> getJsonMap() {
        Map<String, Object> reader = new HashMap<>();
        reader.put("name", "metaqwriter");
        Map<String, Object> parameter = new HashMap<>();
        reader.put("parameter", parameter);
        parameter.put("accessId", accessId);
        parameter.put("accessKey", accessKey);
        parameter.put("topicName", topicName);
        parameter.put("consumerId", consumerId);
        parameter.put("subExpression", subExpression);
        parameter.put("onsChannel", onsChannel);
        parameter.put("domainName", domainName);
        parameter.put("contentType",contentType);
        parameter.put("beginOffset", beginOffset);
        parameter.put("nullCurrentOffset", nullCurrentOffset);
        parameter.put("fieldDelimiter", fieldDelimiter);
        parameter.put("column", column);
        return reader;
    }
    
}
