package com.aliyun.wormhole.qanat.api.dag;

import lombok.Data;

@Data
public class MetricPubNode extends Node {
    
    private String dstTableName;
    
    //直接获取odps表分区更新时间指定
    private String srcDsName;
    
	//部分字段指标更新指定
    private Long datatubeInstId;
    private String subObj;
    
    //非嵌入DAG情况下指定
    private Long targetTaskId;
    private String targetSubTaskName;
    
    public MetricPubNode() {};
    
    public MetricPubNode(String id, Dag dag) {
        super(id, dag);
        this.setNodeAction(NodeAction.BATCH);
        this.setAction("com.aliyun.wormhole.qanat.job.QanatMetricPubJobProcessor");
    }
}
