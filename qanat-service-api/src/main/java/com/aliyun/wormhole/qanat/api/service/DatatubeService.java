package com.aliyun.wormhole.qanat.api.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.DatatubeCreateRequest;
import com.aliyun.wormhole.qanat.api.dto.DatatubeModifyRequest;

public interface DatatubeService {

	DataResult<Map<String, Object>> getDetail(Long id);

	DataResult<Long> create(DatatubeCreateRequest request);

	DataResult<Map<String, BigDecimal>> getComputeCost(Long id);

	DataResult<Boolean> makeupViewModelDatatubeTasks(Long id, String jobNames, String operateEmpid);

	DataResult<Long> makeupOdsDatatubeTasks(Long taskId, String datatubeLevel, String operateEmpid);

	DataResult<Long> makeupDAGDatatubeTasks(Long taskId, String datatubeLevel, String objectType, String operateEmpid);

	DataResult<List<Long>> makeupMultiOdsDatatubeTasks(String taskIds, String datatubeLevel, String operateEmpid);

	DataResult<Boolean> modify(DatatubeModifyRequest request);

	
}
