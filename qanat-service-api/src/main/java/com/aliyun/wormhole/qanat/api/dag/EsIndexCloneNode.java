package com.aliyun.wormhole.qanat.api.dag;

import lombok.Data;

@Data
public class EsIndexCloneNode extends Node {

    /**
     * 
     */
    private static final long serialVersionUID = 9071414235143178188L;
    private String indexName;
    private String dbName;
    
    public EsIndexCloneNode() {}
    
    public EsIndexCloneNode(String id, Dag dag) {
        super(id, dag);
        this.setNodeAction(NodeAction.BATCH);
        this.setAction("com.aliyun.wormhole.qanat.job.QanatEsIndexCloneJobProcessor");
    }
}
