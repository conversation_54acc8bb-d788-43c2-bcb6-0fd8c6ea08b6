package com.aliyun.wormhole.qanat.api.dag;

import lombok.Data;

@Data
public class DataXNode extends Node {

    /**
     * 
     */
    private static final long serialVersionUID = 9071414235143178188L;
    private String srcDsName;
    private String dstDsName;
    private String querySql;
    private String where;
    private String preSql;
    private Integer batchSize;
    private Integer parallism;
    
    public DataXNode() {}
    
    public DataXNode(String id, Dag dag) {
        super(id, dag);
        this.setNodeAction(NodeAction.BATCH);
        this.setAction("com.aliyun.wormhole.qanat.job.QanatDataXJobProcessor");
    }
}
