package com.aliyun.wormhole.qanat.api.dto;

import java.io.Serializable;

import lombok.Data;

@Data
public class SimpleTaskRequest extends ServiceBaseRequest {

    private String taskName;

    private String taskDesc;
    
    private String tableName;
    
    private FullSync fullSync;
    
    private IncrSync incrSync;
    
    private DsInfo srcDs;
    
    private DsInfo dstDs;
    
    private BlinkConf blinkConf;
    
    private SlaInfo slaInfo;
    
    private CheckInfo checkInfo;
    
    private FullLinkInfo fullLinkInfo;
    
    @Data
    public class DsInfo implements Serializable {
        private String dsName;
        private String dsType;
        private String meta;
    }
    
    @Data
    public class FullSync implements Serializable {
        private String blinkJobName;
        private String parallelism;
        private Integer batchSize;
    }
    
    @Data
    public class IncrSync implements Serializable {
        private String blinkJobName;
        private String parallelism;
        private String consumer;
        private String streamEvent;
        private String eventTopic;
        private String eventTag;
    }
    
    @Data
    public class BlinkConf implements Serializable {
        private String queueName;
        private String clusterId;
        private String folderName;
    }
    
    @Data
    public class SlaInfo implements Serializable {
    	private boolean enable;
        private String drcConsumer;
        private String logConsumer;
    }
    
    @Data
    public class CheckInfo implements Serializable {
    	private boolean enable;
        private String drcConsumer;
        private String correctConsumer;
        private String checkConsumer;
        private String delayMs;
    }
    
    @Data
    public class FullLinkInfo implements Serializable {
    	private boolean enable;
        private String drcConsumer;
        private String logConsumer;
    }
}
