package com.aliyun.wormhole.qanat.api.datax;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import lombok.Data;

@Data
public class DataXHoloWriter extends DataXCommonWriter {

    /**
     * 
     */
    private static final long serialVersionUID = 6508313142042372125L;
    protected String accessId;
    protected String accessKey;
    protected String endpoint;
    protected String database;
    
    @Override
    public Map<String, Object> getJsonMap() {
        Map<String, Object> writer = new HashMap<>();
        Map<String, Object> parameter = new HashMap<>();
        writer.put("name", "holowriter");
        writer.put("parameter", parameter);
        parameter.put("accessId", accessId);
        parameter.put("accessKey", accessKey);
        parameter.put("writeMode", writeMode);
        parameter.put("database", database);
        parameter.put("endpoint", endpoint);
        parameter.put("conflictMode", "ignore");
        parameter.put("column", columns);
        parameter.put("table", table);
        if (preSql != null) {
            parameter.put("preSql", Arrays.asList(preSql));
        }
        if (postSql != null) {
            parameter.put("postSql", Arrays.asList(postSql));
        }
        parameter.put("maxCommitSize", batchSize);
        return writer;
    }
}
