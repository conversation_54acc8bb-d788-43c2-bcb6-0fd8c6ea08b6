package com.aliyun.wormhole.qanat.api.dag;

import lombok.Data;

@Data
public class CreateOdsDsInfoNode extends Node {
	private String tenantId;
	private String appName;
    private String srcDsName;
    private String dstDbName;
    private String tableName;
    
    public CreateOdsDsInfoNode() {};
    
    public CreateOdsDsInfoNode(String id, Dag dag) {
        super(id, dag);
        this.setNodeAction(NodeAction.BATCH);
        this.setAction("com.aliyun.wormhole.qanat.job.QanatCreateOdsDsInfoJobProcessor");
    }
}
