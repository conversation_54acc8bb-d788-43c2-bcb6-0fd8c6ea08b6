package com.aliyun.wormhole.qanat.api.datax;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import lombok.Data;

@Data
public class DataXOdpsWriter extends DataXCommonWriter {

    /**
     * 
     */
    private static final long serialVersionUID = 6508313142042372125L;
    private String project;
    private String partition;
    private String accessId;
    private String accessKey;
    private String odpsServer;
    private String accountType;
    private Boolean truncate;
    private Boolean emptyAsNull;
    
    @Override
    public Map<String, Object> getJsonMap() {
        Map<String, Object> writer = new HashMap<>();
        writer.put("name", "odpswriter");
        Map<String, Object> parameter = new HashMap<>();
        writer.put("parameter", parameter);
        parameter.put("accessId", accessId);
        parameter.put("accessKey", accessKey);
        parameter.put("project", project);
        parameter.put("table", table);
        parameter.put("partition", partition);
        parameter.put("column", columns);
        parameter.put("odpsServer", odpsServer);
        parameter.put("emptyAsNull", emptyAsNull);
        parameter.put("truncate", truncate);
        parameter.put("preSql", preSql);
        parameter.put("postSql", postSql);
        parameter.put("accountType", accountType);
        return writer;
    }
}
