package com.aliyun.wormhole.qanat.api.datax;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class DataXLindormWriter extends DataXWriter {

    /**
     * 
     */
    private static final long serialVersionUID = 6508313142042372125L;
    private String username;
    private String password;
    private String seedserver;
    private String namespace;
    private String table;
    private List<String> columns;
    
    @Override
    public Map<String, Object> getJsonMap() {
        Map<String, Object> writer = new HashMap<>();
        writer.put("name", "lindormwriter");
        Map<String, Object> parameter = new HashMap<>();
        writer.put("parameter", parameter);
        parameter.put("table", table);
        parameter.put("columns", columns);
        Map<String, Object> configuration = new HashMap<>();
        parameter.put("configuration", configuration);
        configuration.put("lindorm.client.username", username);
        configuration.put("lindorm.client.password", password);
        configuration.put("lindorm.client.seedserver", seedserver);
        configuration.put("lindorm.client.namespace", namespace);
        return writer;
    }
}
