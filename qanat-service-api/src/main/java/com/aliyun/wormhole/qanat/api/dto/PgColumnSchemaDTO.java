package com.aliyun.wormhole.qanat.api.dto;

import java.io.Serializable;

/**
 * @Created with IntelliJ IDEA.
 * @Author: changting
 * @Date: 2018/1/15 10:31
 * @Description: 列元数据信息
 */
public class PgColumnSchemaDTO implements Serializable {

    private String columnName;
    private String columnDefault;
    private String isNullable;
    private String dataType;
    private String columnComment;

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public String getColumnDefault() {
        return columnDefault;
    }

    public void setColumnDefault(String columnDefault) {
        this.columnDefault = columnDefault;
    }

    public String getIsNullable() {
        return isNullable;
    }

    public void setIsNullable(String isNullable) {
        this.isNullable = isNullable;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getColumnComment() {
        return columnComment;
    }

    public void setColumnComment(String columnComment) {
        this.columnComment = columnComment;
    }

    @Override
    public String toString() {
        return "PgColumnSchemaDO{" +
            "columnName='" + columnName + '\'' +
            ", columnDefault='" + columnDefault + '\'' +
            ", isNullable='" + isNullable + '\'' +
            ", dataType='" + dataType + '\'' +
            ", columnComment='" + columnComment + '\'' +
            '}';
    }
}

