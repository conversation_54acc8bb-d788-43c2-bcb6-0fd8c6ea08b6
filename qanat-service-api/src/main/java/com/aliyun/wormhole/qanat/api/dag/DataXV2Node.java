package com.aliyun.wormhole.qanat.api.dag;

import lombok.Data;

@Data
public class DataXV2Node extends Node {

    /**
     * 
     */
    private static final long serialVersionUID = 9071414235143178188L;
    private String srcDsName;
    private String dstDbName;
    private String dstTableName;
    private String where;
    private String preSql;
    private Integer batchSize;
    private Integer parallism;
    
    public DataXV2Node() {}
    
    public DataXV2Node(String id, Dag dag) {
        super(id, dag);
        this.setNodeAction(NodeAction.BATCH);
        this.setAction("com.aliyun.wormhole.qanat.job.QanatDataXV2JobProcessor");
    }
}
