package com.aliyun.wormhole.qanat.api.dag;

import lombok.Data;

@Data
public class PgSinkNode extends Node {

    /**
     * 
     */
    private static final long serialVersionUID = 9071414235143178188L;
    private String srcDsName;
    private String dstDsName;
    
    public PgSinkNode() {};
    
    public PgSinkNode(String id, Dag dag) {
        super(id, dag);
        this.setNodeAction(NodeAction.STREAM);
        this.setAction("com.aliyun.wormhole.qanat.job.QanatPgSinkJobProcessor");
    }
}
