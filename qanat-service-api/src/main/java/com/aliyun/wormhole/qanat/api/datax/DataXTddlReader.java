package com.aliyun.wormhole.qanat.api.datax;

import java.util.HashMap;
import java.util.Map;

import lombok.Data;

@Data
public class DataXTddlReader extends DataXCommonReader {

    /**
     * 
     */
    private static final long serialVersionUID = 6508313142042372125L;
    private String appName;
    private String unitName;
    private Boolean single;
    
    @Override
    public Map<String, Object> getJsonMap() {
        Map<String, Object> reader = new HashMap<>();
        reader.put("name", "tddlreader");
        Map<String, Object> parameter = new HashMap<>();
        reader.put("parameter", parameter);
        parameter.put("appName", appName);
        parameter.put("unitName", unitName);
        parameter.put("single", single);
        parameter.put("column", columns);
        parameter.put("table", table);
        parameter.put("where", where);
        parameter.put("socketTimeout", 36000000);
        parameter.put("masterSlave", "only_slave");
        parameter.put("checkSlave", false);
        parameter.put("slaveDelayLimit", 10);
        return reader;
    }
}
