package com.aliyun.wormhole.qanat.api.datax;

import java.util.Map;

import lombok.Data;

@Data
public class DataXPostgreSQLWriter extends DataXJdbcWriter {

    /**
     * 
     */
    private static final long serialVersionUID = 6508313142042372125L;
    private Map<String, String> pgType;
    
    @Override
    public Map<String, Object> getJsonMap() {
        Map<String, Object> writer = super.getJsonMap();
        writer.put("name", "postgresqlwriter");
        ((Map<String, Object>)writer.get("parameter")).put("pgType", pgType);
        return writer;
    }
}
