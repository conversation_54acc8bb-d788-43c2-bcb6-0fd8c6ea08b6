package com.aliyun.wormhole.qanat.api.service;

import java.util.List;

import com.alibaba.fastjson.JSONObject;

public interface KafkaManagementService {

	Boolean createTopic(String tenantId, String appName, String topic);

	Boolean createConsumerGroup(String tenantId, String appName, String consumerId);

	JSONObject getKafkaConfByAppName(String tenantId, String appName);

	Boolean deleteConsumerGroup(String tenantId, String appName, String consumerId);

	List<String> getConsumerGroupList(String tenantId, String appName);

	JSONObject getKafkaConfByDbName(String tenantId, String dbName);

	Boolean createConsumerGroupFromDbInfo(String tenantId, String dbName, String consumerId);

	Boolean createTopic(String tenantId, String appName, String topic, Integer partitions);

}
