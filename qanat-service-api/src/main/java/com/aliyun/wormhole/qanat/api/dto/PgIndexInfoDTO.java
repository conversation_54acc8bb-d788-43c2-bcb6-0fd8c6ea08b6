package com.aliyun.wormhole.qanat.api.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @Created with IntelliJ IDEA.
 * @Author: changting
 * @Date: 2018/1/25 10:47
 * @Description: 索引信息
 */
public class PgIndexInfoDTO implements Serializable {

    private String schemaName;
    private String tableName;
    private String indexName;
    private String tableSpace;
    /**
     * 索引类型
     */
    private String amname;
    private String description;
    private List<String> columns;

    public String getSchemaName() {
        return schemaName;
    }

    public void setSchemaName(String schemaName) {
        this.schemaName = schemaName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getIndexName() {
        return indexName;
    }

    public void setIndexName(String indexName) {
        this.indexName = indexName;
    }

    public String getTableSpace() {
        return tableSpace;
    }

    public void setTableSpace(String tableSpace) {
        this.tableSpace = tableSpace;
    }

    public String getAmname() {
        return amname;
    }

    public void setAmname(String amname) {
        this.amname = amname;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getColumns() {
        return columns;
    }

    public void setColumns(List<String> columns) {
        this.columns = columns;
    }

    @Override
    public String toString() {
        return "PgIndexInfoDO{" +
            "schemaName='" + schemaName + '\'' +
            ", tableName='" + tableName + '\'' +
            ", indexName='" + indexName + '\'' +
            ", tableSpace='" + tableSpace + '\'' +
            ", amname='" + amname + '\'' +
            ", description='" + description + '\'' +
            ", columns=" + columns +
            '}';
    }
}

