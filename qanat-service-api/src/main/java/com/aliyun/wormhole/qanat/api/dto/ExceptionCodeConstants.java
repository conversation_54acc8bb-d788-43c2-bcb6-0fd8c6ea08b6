package com.aliyun.wormhole.qanat.api.dto;

import java.io.Serializable;

public final class ExceptionCodeConstants implements Serializable{
	private static final long serialVersionUID = 1L;
	/**系统共用的异常*/
	public static final class Common{
		/**请求参数为空*/
		public static final String PARAM_NULL = "10001";
		/**请求参数格式不正确*/
		public static final String PARAM_NOT_RIGHT = "10002";
		/**系统异常*/
		public static final String SYSTEM_ERROR = "10003";
		/**成功*/
		public static final String SUCCESS = "10004";
		/**没有结果集*/
		public static final String NO_RESULT = "10005";
		/**结果不唯一*/
		public static final String RESULT_NOT_UNIQUE = "10006";
		/**数据状态错误*/
		public static final String STATUS_ERROR = "10007";
		private Common() {
		}
	}

	/** 生成sql协议异常 */
	public static final class SqlProtocol {
		/**
		 * 没有设置目标project
		 */
		public static final String TAGET_PROJECT_NULL = "20001";
		/**
		 * 没有设置目标表
		 */
		public static final String TAGET_TABLE_NULL = "20002";
		/**
		 * 没有设置目标project
		 */
		public static final String SRC_PROJECT_NULL = "20003";
		/**
		 * 没有设置目标表
		 */
		public static final String SRC_TABLE_NULL = "20004";
		/**
		 * 没有设置合并表project
		 */
		public static final String JOIN_PROJECT_NULL = "20005";
		/**
		 * 没有设置合并表
		 */
		public static final String JOIN_TABLE_NULL = "20006";
		/**
		 * 没有设置目标partition
		 */
		public static final String TARGET_PARTITION_NULL = "20007";
		/**
		 * 没有设置源表partition
		 */
		public static final String SRC_PARTITION_NULL = "20008";
		/**
		 * 没有设置合并表partition
		 */
		public static final String JOIN_PARTITION_NULL = "20009";
		/**
		 * 没有设置合并条件
		 */
		public static final String JOIN_CONDITION_NULL = "20010";
		private SqlProtocol() {
		}

	}

	public static final class OdpsOperate {
		/**
		 * 没有设置目标project
		 */
		public static final String READ_PARTITION_ERROR = "30001";
		/**
		 * 连接odps出错
		 */
		public static final String CONNECT_ODPS_ERROR = "30002";
		/**
		 * 连接ODPS配置错误
		 */
		public static final String CONNECT_ODPS_CONFIG_ERROR = "30003";
		/**
		 * Run ODPS query错误
		 */
		public static final String RUN_ODPS_QUERY_ERROR = "30004";
		/**
		 * 创建odps表错误
		 */
		public static final String CREATE_ODPS_TABLE_ERROR = "30005";
		/**
		 * 删除odps表错误
		 */
		public static final String DROP_ODPS_TABLE_ERROR = "30006";
		private OdpsOperate() {
		}

	}

}
