package com.aliyun.wormhole.qanat.api.service;

import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.DatasourceDTO;
import com.aliyun.wormhole.qanat.api.dto.DatasourceRequest;
import com.aliyun.wormhole.qanat.api.dto.DsFieldInfoDTO;
import com.github.pagehelper.PageInfo;

public interface DatasourceService {

    DataResult<Long> createDatasource(DatasourceRequest request);

    DataResult<Boolean> modifyDatasource(DatasourceRequest request);

	DataResult<PageInfo<DatasourceDTO>> list4Page(DatasourceRequest request);
    
    DataResult<Boolean> deleteDatasource(DatasourceRequest request);

	DataResult<Boolean> rebuildDsInfo(String tenantId, String dsName);

	void addObjectField(String tenantId, String dbName, String dsName, String objectType, String dsUniqueName,
			String fieldName, String tagJson);

	void deleteObjectField(String tenantId, String dsUniqueName, String fieldName);

	Integer updObjectField(String tenantId, String objectType, String dsUniqueName, String fieldName, String tagJson);

	DataResult<Boolean> modifyDsInfoIncrConf(String tenantId, String dsName, String topicName);

	String getPkFieldByObjectType(String tenantId, String objectType, String objectUniqueCode);

	String getObjectFieldType(String objectType, String dsUniqueName, String fieldName);

	Map<String, String> getObjectFieldTypes(String objectType, String dsUniqueName);

	JSONObject getOdsTableMetaByDsName(String tenantId, String dsName);

	JSONObject getTableMetaByDsName(String tenantId, String dsName) throws QanatBizException;

	JSONObject getDbMetaByName(String dbName);

	Long getDsIdByTableName(String tenantId, String tableName, String dbName);

	JSONObject getTableMetaByDsUniqueName(String tenantId, String dsUniqueName) throws QanatBizException;

	String createMdpObject(String tenantId, String dsName, String enums);

	DataResult<Long> createDsInfoFromObject(DatasourceRequest request);

	JSONObject getOdpsTableMetaByObjectCode(String tenantId, String dsName);

	JSONObject getDbMetaByDsName(String tenantId, String dsName);

	DataResult<Boolean> modifyDsInfoMdpSlot(String tenantId, String dsName, String slotDbName);

	Map<String, Object> getObjectSlotMeta(String objectType, String objectUniqueCode);

	Map<String, Object> getObjectSlotMeta(String objectType, String objectUniqueCode, String domainCode);

	Map<String, String> getObjectSlotFieldMap(String objectType, String objectUniqueCode, String domainCode);

	List<String> getObjectSlotFieldList(String objectType, String objectUniqueCode, String domainCode);

	DataResult<Boolean> modifyDsInfoMdpSlot(String tenantId, String dsName, String slotDbName, String slotMeta);

	String getObjectSlotMetaJson(String objectType, String objectUniqueCode);

	JSONObject getOdpsTableMetaByOdsDsName(String tenantId, String dsName);

	DataResult<PageInfo<DsFieldInfoDTO>> listDsFields4Page(DatasourceRequest request);

	DataResult<Boolean> updateDsInfoMeta(String tenantId, String dbName, String tableName, String operateEmpid);

	Long getDbIdByName(String tenantId, String dbName);

	Boolean isObjectDirectWriteByMdp(String tenantId, String objectUniqueCode);

	String getDsNameByObjectCode(String tenantId, String objectUniqueCode);

	String getDsName(String tenantId, String appName, String dbName, String tableName);

	String getDsName(Long dbId, String dbType, String tenantId, String appName, String tableName);

	JSONObject getObjectMeta(String tenantId, String objectType, String objectUniqueCode);
}
