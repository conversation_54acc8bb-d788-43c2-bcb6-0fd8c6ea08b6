package com.aliyun.wormhole.qanat.api.datax;

import lombok.Data;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class DataXOtsReader extends DataXCommonReader {

    /**
     * 
     */
    private static final long serialVersionUID = 6508313142042372125L;
    private String instanceName;
    private String accessId;
    private String accessKey;
    private String endpoint;
    private List<Map<String, String>> column;
    
    @Override
    public Map<String, Object> getJsonMap() {
        Map<String, Object> reader = new HashMap<>();
        reader.put("name", "otsreader");
        Map<String, Object> parameter = new HashMap<>();
        reader.put("parameter", parameter);
        parameter.put("accessId", accessId);
        parameter.put("accessKey", accessKey);
        parameter.put("instanceName", instanceName);
        parameter.put("table", table);
        parameter.put("column", column);
        parameter.put("endpoint", endpoint);
        return reader;
    }
}
