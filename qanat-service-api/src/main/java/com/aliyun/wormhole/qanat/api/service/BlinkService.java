package com.aliyun.wormhole.qanat.api.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.BlinkJobRequest;
import com.aliyun.wormhole.qanat.api.dto.ResourcePackage;

public interface BlinkService {

    void updateJob(String tenantId, String appName, String jobName, String sql, String planJson, String engineVersion, String properties);

    void restartJob(String tenantId, String appName, String jobName, Date startTime, Boolean isBatch);

    Boolean commitJob(String tenantId, String appName, String jobName);

    void stopJob(String tenantId, String appName, String jobName);

    String getInstanceActualState(String tenantId, String appName, String jobName, Long instanceId);

    Long startJob(String tenantId, String appName, String jobName, Date startTime);

    Boolean createJob(BlinkJobRequest req);

	Boolean validateJob(String tenantId, String appName, String jobName);

	List<Map<String, Object>> listQueues(String tenantId, String appName, String clusterId, String queueName);
	
	void offlineJob(String tenantId, String appName, String jobName);
	
	void deleteJob(String tenantId, String appName, String jobName);
	
	boolean isJobExists(String tenantId, String appName, String jobName);

    String getPlanJson(String tenantId, String appName, String jobName, Integer expectedCUs, Boolean isAutoconfEnable);

	void buildBlinkJob(String tenantId, String appName, String jobName, String sql, String folderName, String packages, boolean isBatch);

	Boolean recommitBlinkJob(String tenantId, String appName, String jobName);

	Boolean rebuildBlinkJob(String tenantId, String appName, String jobName, Date startTime);

	String getBlinkExtensionsByPackage(String tenantId, ResourcePackage ... packages);

	String getJobRunSummary(String tenantId, String appName, String jobName);

	String getInstanceMetric(String tenantId, String appName, String jobName, String metricJson);

	String getJobExceptions(String tenantId, String appName, String jobName);

	void dropExistedJob(String tenantId, String appName, String jobName);

	void buildBlinkJob(String tenantId, String appName, String jobName, String sql, String folderName, String packages,
			boolean isBatch, String planJson) throws QanatBizException;

	List<String> getJobList(String tenantId, String appName);

	List<Map<String, Object>> getProjectCUs(String tenantId, String appName);

	String listProjectBindQueue(String tenantId, String appName);

	Long startBatchJob(String tenantId, String appName, String jobName, Map<String, String> params);

	Map<String, Object> getInstanceResource(String tenantId, String appName, String jobName);

	String getInstanceDetail(String tenantId, String appName, String jobName);

	boolean isJobCommitted(String tenantId, String appName, String jobName);

	Map<String, List<String>> recommitBlinkJobs(String tenantId, String appName, String jobNames);

	Map<String, List<String>> restartJobs(String tenantId, String appName, String jobNames, Integer retrieveSeconds);

	Long startJob(String tenantId, String appName, String jobName, Date startTime, Map<String, String> params);

	String getJobDetail(String tenantId, String appName, String jobName);

	String getJobScript(String tenantId, String appName, String jobName);

	String getBatchPlanJson4OdpsSource(String tableName, String pk, int dstDbNum);

	String getBatchPlanJson4DwSource(String tableName, String pk, int paralism, int dstDbNum);

	String getBatchPlanJson4DwSource(String tableName, String pk, int dstDbNum);

	String getInstanceConfig(String tenantId, String appName, String jobName);

	String getBatchPlanJson4DwSource2(String tableName, String pk, int paralism, int dstDbNum);

	void buildBlinkJob(String tenantId, String appName, String jobName, String sql, String folderName, String packages,
			Integer parallel) throws QanatBizException;

	void updateJob(String tenantId, String appName, String jobName, String sql, String planJson, String engineVersion,
			String properties, String packages);

	void resumeJob(String tenantId, String appName, String jobName);

	void pauseJob(String tenantId, String appName, String jobName);

	void modifyClusterQueue(String tenantId, String appName, String jobName, String clusterId, String queueName);

}
