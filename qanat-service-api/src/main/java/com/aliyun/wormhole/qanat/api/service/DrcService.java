package com.aliyun.wormhole.qanat.api.service;

import java.util.Date;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.dto.DataResult;

public interface DrcService {

	DataResult<Map<String, Long>> createDrcTaskForDs(String tenantId, String appName, String dsName, String empid);

	DataResult<String> getDrcTaskInfo(String tenantId, String appName, Long drcTaskId);

	JSONObject getDrcConfByAppName(String tenantId, String appName);

	DataResult<Long> createDrcTaskForDs(String tenantId, String appName, String dsName, Long drcTaskId, String empid);

	DataResult<Map<String, Long>> createDrcTaskForDs(String tenantId, String appName, String dsName, String empid,
			String datatubeLevel);

	Boolean restartDrcService(String tenantId, String appName, String jobName, Date startTime);

}
