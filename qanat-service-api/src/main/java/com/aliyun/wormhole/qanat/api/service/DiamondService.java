package com.aliyun.wormhole.qanat.api.service;

public interface DiamondService {

	Boolean publishConfig(String dataId, String group, String appName, String config);

	Boolean setFlowLimitIfNotExists(String gid, Double limit);

	Boolean publishConfigPre(String dataId, String group, String appName, String config);

	Boolean setFlowLimit(String gid, Double limit);

	String getConfigPre(String dataId, String group);

	String getConfig(String dataId, String group);

	Boolean publishBeta(String dataId, String group, String appName, String betaIps, String config);

	Boolean publishConfigByEnv(String dataId, String group, String appName, String config, String env);

	String getUnitList();
}
