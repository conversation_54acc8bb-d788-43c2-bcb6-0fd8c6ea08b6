package com.aliyun.wormhole.qanat.api.dto;

import java.util.List;

import lombok.Data;

@Data
public class OdsSyncTaskRequest extends ServiceBaseRequest {

    private String taskName;

    private String taskDesc;
    
    private String tableName;
    
    private String appName;
    
    private String fullSyncParallelism;
    
    private Integer fullSyncBatchSize;
    
    private String srcDsName;
    
    private String dstDsName;
    
    private String dstDbName;
    
    private String etlDbName;
    
    private List<String> dstDbNames;
    
    private Boolean enableIncrSync;
    
    private Boolean enableCheck;
    
    private String checkDelayMs;
    
    private Boolean enableFullLink;
    
    private Integer recordSizeW;
    
    private String checkAllFilter;
    
    private String timeExpression;
    
    private String filter;
    
    private String datatubeLevel;
    
    private Long predictSize;
    
    private Integer predictQph;
}
