package com.aliyun.wormhole.qanat.api.service;

import com.alibaba.fastjson.JSONObject;

public interface KvstoreService {

	String get(String tenantId, String appName, String key);

	Boolean exists(String tenantId, String appName, String key);

	JSONObject getResourceConf(String tenantId, String appName);

	String set(String tenantId, String appName, String key, String value);

	String setex(String tenantId, String appName, String key, String value, Integer seconds);

	Long del(String tenantId, String appName, String key);

}
