package com.aliyun.wormhole.qanat.api.datax;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import lombok.Data;

@Data
public class DataXTddlWriter extends DataXCommonWriter {
    /**
     * 
     */
    private static final long serialVersionUID = -4804811386706452744L;
    
    private String appName;
    private String unitName;
    private Boolean single;

    @Override
    public Map<String, Object> getJsonMap() {
        Map<String, Object> writer = new HashMap<>();
        writer.put("name", "tddlwriter");
        Map<String, Object> parameter = new HashMap<>();
        writer.put("parameter", parameter);
        parameter.put("appName", appName);
        parameter.put("unitName", unitName);
        parameter.put("single", single);
        parameter.put("column", columns);
        parameter.put("table", table);
        parameter.put("preSql", Arrays.asList(preSql));
        parameter.put("postSql", Arrays.asList(postSql));
        parameter.put("writeMode", writeMode);
        parameter.put("batchSize", batchSize);
        return writer;
    }
    
}
