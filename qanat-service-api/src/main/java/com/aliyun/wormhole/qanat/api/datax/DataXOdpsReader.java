package com.aliyun.wormhole.qanat.api.datax;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import lombok.Data;

@Data
public class DataXOdpsReader extends DataXCommonReader {

    /**
     * 
     */
    private static final long serialVersionUID = 6508313142042372125L;
    private String project;
    private String partition;
    private String accessId;
    private String accessKey;
    private String odpsServer;
    private String splitMode;
    private String packageAuthorizedProject;
    private Boolean isCompress;
    private String preSql;
    private String postSql;
    private Boolean successOnNoPartition;
    private String accountProvider;
    
    @Override
    public Map<String, Object> getJsonMap() {
        Map<String, Object> reader = new HashMap<>();
        reader.put("name", "odpsreader");
        Map<String, Object> parameter = new HashMap<>();
        reader.put("parameter", parameter);
        parameter.put("accessId", accessId);
        parameter.put("accessKey", accessKey);
        parameter.put("project", project);
        parameter.put("table", table);
        parameter.put("partition", Arrays.asList(partition));
        parameter.put("column", columns);
        parameter.put("odpsServer", odpsServer);
        parameter.put("splitMode", splitMode);
        parameter.put("packageAuthorizedProject", packageAuthorizedProject);
        parameter.put("isCompress", isCompress);
        parameter.put("successOnNoPartition", successOnNoPartition);
        parameter.put("preSql", preSql);
        parameter.put("postSql", postSql);
        parameter.put("accountProvider", accountProvider);
        return reader;
    }
}
