package com.aliyun.wormhole.qanat.api.datax;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import lombok.Data;

@Data
public class DataXMetaQWriter extends DataXWriter {
    /**
     * 
     */
    private static final long serialVersionUID = -4804811386706452744L;
    
    private String topic;
    private String producerGroup;
    private String tag;
    private Integer idIndex;
    private String nullFormat;
    private Boolean metaqNeedSendOk;
    private String fieldDelimiter;
    private String encoding;
    
    @Override
    public Map<String, Object> getJsonMap() {
        Map<String, Object> writer = new HashMap<>();
        writer.put("name", "metaqwriter");
        Map<String, Object> parameter = new HashMap<>();
        writer.put("parameter", parameter);
        parameter.put("topic", topic);
        parameter.put("producerGroup", producerGroup);
        parameter.put("tag", tag);
        parameter.put("idIndex", idIndex);
        parameter.put("nullFormat", nullFormat);
        parameter.put("metaqNeedSendOk",metaqNeedSendOk);
        parameter.put("fieldDelimiter", fieldDelimiter);
        parameter.put("encoding", encoding);
        return writer;
    }
    
}
