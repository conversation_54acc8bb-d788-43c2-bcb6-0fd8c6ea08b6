package com.aliyun.wormhole.qanat.api.dto;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

@Data
public class TaskInstanceDTO implements Serializable {
    /**
     * task_instance.id (主键)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private Long id;

    /**
     * task_instance.gmt_create (创建时间)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private Date gmtCreate;

    /**
     * task_instance.gmt_modified (修改时间)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private Date gmtModified;

    /**
     * task_instance.task_id (任务id)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private Long taskId;

    /**
     * task_instance.operator (操作人工号)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private String operator;

    /**
     * task_instance.start_time (任务执行开始时间)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private Date startTime;

    /**
     * task_instance.end_time (任务执行结束时间)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private Date endTime;

    /**
     * task_instance.status (任务执行状态)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private Byte status;

    /**
     * task_instance.create_empid (创建人工号)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private String createEmpid;

    /**
     * task_instance.modify_empid (修改人工号)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private String modifyEmpid;

    /**
     * task_instance.parent_instance_id (父任务实例id)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private Long parentInstanceId;

    /**
     * task_instance.external_inst_id (外部任务实例id(SchedulerX任务实例id))
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private String externalInstId;

    /**
     * task_instance.task_name (任务名称)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private String taskName;

    /**
     * task_instance.external_id (外部任务id(SchedulerX任务id))
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private String externalId;

    /**
     * task_instance.task_command (任务执行命令)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private String taskCommand;

    /**
     * task_instance.host_addr (任务执行主机地址)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private String hostAddr;

    /**
     * task_instance.node_action
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private String nodeAction;

    /**
     * task_instance.exec_param (任务执行参数)
     * @ibatorgenerated 2019-12-13 02:31:13
     */
    private String execParam;
}