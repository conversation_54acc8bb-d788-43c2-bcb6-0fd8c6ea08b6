package com.aliyun.wormhole.qanat.api.datax;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import lombok.Data;

@Data
public class DataXJdbcWriter extends DataXCommonWriter {

    /**
     * 
     */
    private static final long serialVersionUID = 6508313142042372125L;
    protected String username;
    protected String password;
    protected String session;
    protected String jdbcUrl;
    
    @Override
    public Map<String, Object> getJsonMap() {
        Map<String, Object> writer = new HashMap<>();
        Map<String, Object> parameter = new HashMap<>();
        writer.put("parameter", parameter);
        parameter.put("username", username);
        parameter.put("password", password);
        parameter.put("writeMode", writeMode);
        parameter.put("column", columns);
        if (session != null) {
            parameter.put("session", Arrays.asList(session));
        }
        Map<String, Object> connection = new HashMap<>();
        parameter.put("connection", Arrays.asList(connection));
        connection.put("table", Arrays.asList(table));
        connection.put("jdbcUrl", jdbcUrl);
        if (preSql != null) {
            parameter.put("preSql", Arrays.asList(preSql));
        }
        if (postSql != null) {
            parameter.put("postSql", Arrays.asList(postSql));
        }
        parameter.put("batchSize", batchSize);
        return writer;
    }
}
