package com.aliyun.wormhole.qanat.api;

import lombok.Data;

@Data
public class QanatBizException extends RuntimeException {

    /**
     * 
     */
    private static final long serialVersionUID = -1929596288307548218L;
    
    private String code;
    private String message;
    private Long bizId;
    private Throwable e;
    
    public QanatBizException(String message) {
        this.code = "500";
        this.message = message;
    }
    
    public QanatBizException(String code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public QanatBizException(String code, String message, Long bizId) {
        this.code = code;
        this.message = message;
        this.bizId = bizId;
    }

    public QanatBizException(String code, String message, Exception e) {
        this.code = code;
        this.message = message;
        this.e = e;
        
    }

}
