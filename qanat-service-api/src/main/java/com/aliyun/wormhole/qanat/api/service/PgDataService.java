package com.aliyun.wormhole.qanat.api.service;

import java.util.List;
import java.util.Map;

public interface PgDataService {

    List<Map<String, Object>> pgQuery(String tableName, String express, List<Object> values, Integer pageSize,
        Integer pageNum, String orderBy) throws Exception;

    List<Map<String, Object>> pgQuery(String tableName, List<String> conditionList, Integer pageSize, Integer pageNum,
        String orderBy) throws Exception;

    List<Map<String, Object>> pgQuery(String tableName, Map<String, String> columnsMap, List<String> conditionList,
        Integer pageSize, Integer pageNum, String orderBy) throws Exception;

    List<Map<String, Object>> pgQuery(String tableName, List<String> columns, List<String> conditionList,
        Integer pageSize, Integer pageNum, String orderBy) throws Exception;

    List<Map<String, Object>> pgQuery(String tableName, List<String> columns, String whereCondition, Integer pageSize,
        Integer pageNum, String orderBy) throws Exception;

    void execute(String sql) throws Exception;

    List<Map<String, Object>> pgQuery(String sql) throws Exception;

    Long pgCount(String tableName, List<String> conditionList) throws Exception;

    Long pgCount(String tableName, String whereCondition) throws Exception;

    Integer batchInsert(String tableName, List<Map<String, Object>> records) throws Exception;

    Integer update(String tableName, Map<String, Object> record, String whereCondition) throws Exception;

    Integer delete(String tableName, String whereCondition) throws Exception;

    Integer update(String sql);

}
