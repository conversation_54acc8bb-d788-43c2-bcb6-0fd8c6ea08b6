package com.aliyun.wormhole.qanat.api.datax;

import java.util.Map;

public class DataXPostgreSQLReader extends DataXJdbcReader {

    /**
     * 
     */
    private static final long serialVersionUID = 2510327759673452910L;
    
    @Override
    public Map<String, Object> getJsonMap() {
        Map<String, Object> reader = super.getJsonMap();
        reader.put("name", "postgresqlreader");
        return reader;
    }

}
