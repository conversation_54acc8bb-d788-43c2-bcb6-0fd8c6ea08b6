package com.aliyun.wormhole.qanat.api.dag;

import lombok.Data;

@Data
public class DataTubeIncrSyncNode extends Node {
    private String streamJobs;
    
    public DataTubeIncrSyncNode() {};
    
    public DataTubeIncrSyncNode(String id, Dag dag) {
        super(id, dag);
        this.setNodeAction(NodeAction.STREAM);
        this.setAction("com.aliyun.wormhole.qanat.job.QanatViewModelIncrSyncJobProcessor");
    }
}
