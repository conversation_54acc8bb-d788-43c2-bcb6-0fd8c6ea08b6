package com.aliyun.wormhole.qanat.api.dto;

import java.io.Serializable;
import java.util.Map;
/**
 * <AUTHOR>
 */
public class PgOperateTableDTO implements Serializable {
    /**
     * 数据库名
     */
    private String dbName;
    /**
     * 表名
     */
    private String tableName;
    /**
     * 列(列名为主键)
     */
    private Map<String, ColumnInfo> columns;

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public Map<String, ColumnInfo> getColumns() {
        return columns;
    }

    public void setColumns(Map<String, ColumnInfo> columns) {
        this.columns = columns;
    }

    @Override
    public String toString() {
        return "PgOperateTableDO{" +
            "dbName='" + dbName + '\'' +
            ", tableName='" + tableName + '\'' +
            ", columns=" + columns +
            '}';
    }
}
