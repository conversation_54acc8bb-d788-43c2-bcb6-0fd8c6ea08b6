package com.aliyun.wormhole.qanat.api.datax;

import java.util.Map;

public class DataXMySQLWriter extends DataXJdbcWriter {

    /**
     * 
     */
    private static final long serialVersionUID = 5955885320492808469L;
    
    @Override
    public Map<String, Object> getJsonMap() {
        Map<String, Object> writer = super.getJsonMap();
        writer.put("name", "mysqlwriter");
        return writer;
    }

}
