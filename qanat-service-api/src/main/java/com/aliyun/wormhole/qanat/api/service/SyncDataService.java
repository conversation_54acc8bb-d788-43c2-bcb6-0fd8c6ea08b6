package com.aliyun.wormhole.qanat.api.service;

import java.util.Map;

import com.aliyun.wormhole.qanat.api.datax.DataXReader;
import com.aliyun.wormhole.qanat.api.datax.DataXSetting;
import com.aliyun.wormhole.qanat.api.datax.DataXWriter;
import com.aliyun.wormhole.qanat.api.dto.DataResult;

/**
 * 
 * <AUTHOR>
 * 2019年7月23日
 */
public interface SyncDataService {

    DataResult<Map<String, String>> syncData(String requestId, DataXSetting setting, DataXReader reader, DataXWriter writer);

    DataResult<Map<String, String>> syncData(String requestId, String dataXConfig);

    String getDataXConfig(String requestId, DataXSetting setting, DataXReader reader, DataXWriter writer);


}