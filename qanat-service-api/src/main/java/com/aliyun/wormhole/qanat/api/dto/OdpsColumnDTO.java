package com.aliyun.wormhole.qanat.api.dto;

import java.io.Serializable;

import lombok.ToString;

@ToString
public class OdpsColumnDTO implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 2510195029714027696L;
	/**
	 * 字段名字
	 */
	private String columnName;
	/**
	 * 字段类型
	 */
	private String columnType;
	/**
	 * 字段注释
	 */
	private String columnComment;


	public String getColumnName() {
		return columnName;
	}

	public void setColumnName(String columnName) {
		this.columnName = columnName;
	}

	public String getColumnType() {
		return columnType;
	}

	public void setColumnType(String columnType) {
		this.columnType = columnType;
	}

	public String getColumnComment() {
		return columnComment;
	}

	public void setColumnComment(String columnComment) {
		this.columnComment = columnComment;
	}

}
