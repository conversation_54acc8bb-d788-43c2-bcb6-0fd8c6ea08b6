package com.aliyun.wormhole.qanat.api.datax;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import lombok.Data;

@Data
public class DataXJdbcReader extends DataXCommonReader {

    /**
     * 
     */
    private static final long serialVersionUID = 6508313142042372125L;
    private String username;
    private String password;
    private Boolean checkSlave;
    private String splitPk;
    private String jdbcUrl;
    
    @Override
    public Map<String, Object> getJsonMap() {
        Map<String, Object> reader = new HashMap<>();
        Map<String, Object> parameter = new HashMap<>();
        reader.put("parameter", parameter);
        parameter.put("username", username);
        parameter.put("password", password);
        parameter.put("checkSlave", checkSlave);
        parameter.put("column", columns);
        parameter.put("splitPk", splitPk);
        Map<String, Object> connection = new HashMap<>();
        parameter.put("connection", Arrays.asList(connection));
        connection.put("table", Arrays.asList(table));
        connection.put("jdbcUrl", Arrays.asList(jdbcUrl));
        parameter.put("where", where);
        return reader;
    }
}
