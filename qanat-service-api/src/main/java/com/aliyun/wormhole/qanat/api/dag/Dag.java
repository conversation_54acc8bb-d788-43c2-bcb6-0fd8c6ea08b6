package com.aliyun.wormhole.qanat.api.dag;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;

@Data
public class Dag implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 2970098994692858426L;
    private String id;
    private String timeExpression;
    private List<Node> nodeList;
    
    public Dag(String id) {
        this.id = id;
    }

    @JSONField(serialize = false)
    public void addNode(Node node) {
        if (nodeList == null) {
            nodeList = new ArrayList<>();
        }
        nodeList.add(node);
    }

    @JSONField(serialize = false)
    public List<Node> getHeadList() {
        if (nodeList == null || nodeList.size() == 0) {
            return null;
        }
        return nodeList.stream().filter(node -> node.getPrevNodeList() == null || node.getPrevNodeList().size() == 0).collect(Collectors.toList());
    }
}
