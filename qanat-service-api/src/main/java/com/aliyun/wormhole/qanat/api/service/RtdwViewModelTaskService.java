package com.aliyun.wormhole.qanat.api.service;

import java.util.List;

import com.aliyun.wormhole.qanat.api.dto.DataResult;

public interface RtdwViewModelTaskService {

	DataResult<Long> createViewModelFromYaml(ViewModelRequest request);

	DataResult<Long> modifyViewModel(ViewModelRequest request);

	DataResult<Boolean> createTableAndFullSync(String tenantId, Long viewModelId);

	DataResult<Long> restartModelTask(String tenantId, Long viewModelId, String operateEmpid);

	DataResult<Long> createViewModelFromObject(String tenantId, String appName, String objectType, String objectUniqueCode, String operateEmpid, String objectMsg);

	DataResult<Long> createBatchCheckTask(String tenantId, Long viewModelId, String operateEmpid);

	DataResult<Boolean> updateSlaData(String tenantId, String operateEmpid, List<Long> vmIds, String bizDate);

	DataResult<Boolean> createTableAndFullSync(String tenantId, Long viewModelId, String batchJobs);

	DataResult<Boolean> reflectObjectFieldChange(String string, String string2, String string3, Integer integer,
			String string4, String string5);

	DataResult<Long> createBatchStreamTasks(String tenantId, Long viewModelId, String operateEmpid);

	DataResult<Boolean> createTableAndFullSyncByYaml(String tenantId, Long viewModelId, String dstDbName,
			String userYaml);
	
}
