package com.aliyun.wormhole.qanat.api.dag;

import lombok.Data;

@Data
public class MultiDbSinkNode extends Node {
    private Long datatubeInstId;
    private String srcDbName;
    private String dstDbNames;
    private String tableName;
    private String tmpTableName;
    private String bakTableName;
    private String jobName;
    
    public MultiDbSinkNode() {};
    
    public MultiDbSinkNode(String id, Dag dag) {
        super(id, dag);
        this.setNodeAction(NodeAction.BATCH);
        this.setAction("com.aliyun.wormhole.qanat.job.QanatMultiDbSinkJobProcessor");
    }
}