package com.aliyun.wormhole.qanat.api.service;

import java.util.Map;

public interface ElasticSearchService {

    Map<String, Object> queryByPk(String tenantId, String dbName, String index, String pk);

    Boolean removeIndexAlias(String tenantId, String dbName, String alias, String index);

    Boolean setIndexAlias(String tenantId, String dbName, String alias, String newIndex, String oldIndex);

    Boolean createIndex(String tenantId, String dbName, String index, String settings, String mappings);

    Boolean createIndexByClone(String tenantId, String dbName, String newIndex, String oldIndex);

    Integer updateByQuery(String tenantId, String dbName, String index, String body);

    Long countIndex(String tenantId, String dbName, String index);

    String upsert(String tenantId, String dbName, String index, String id, Map<String, Object> dataMap);

	String getIndexByAlias(String tenantId, String dbName, String alias);

}
