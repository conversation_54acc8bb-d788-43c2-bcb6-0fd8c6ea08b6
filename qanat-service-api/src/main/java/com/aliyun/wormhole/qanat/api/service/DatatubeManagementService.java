package com.aliyun.wormhole.qanat.api.service;

import com.aliyun.wormhole.qanat.api.dto.DataResult;

public interface DatatubeManagementService {

	DataResult<Long> pauseAllDrcTasks();

	DataResult<Long> resumeAllDrcTasks();

	DataResult<Long> pauseAllTasks();

	DataResult<Long> resumeAllTasks();

	DataResult<Long> pauseTasksByObjType(String objType);

	DataResult<Long> resumeTasksByObjType(String objType);

	
}
