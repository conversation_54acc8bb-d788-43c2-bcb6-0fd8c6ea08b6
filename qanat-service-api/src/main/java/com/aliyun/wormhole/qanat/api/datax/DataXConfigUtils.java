package com.aliyun.wormhole.qanat.api.datax;

import java.io.Serializable;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class DataXConfigUtils implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 944981281310511711L;

    public static String getDataXConfig(DataXSetting setting, DataXReader reader, DataXWriter writer) {

        JSONObject contentJson = new JSONObject();
        contentJson.put("reader", reader.getJsonMap());
        contentJson.put("writer", writer.getJsonMap());
        JSONArray contentArrayJson = new JSONArray();
        contentArrayJson.add(contentJson);

        JSONObject jobJson = new JSONObject();
        jobJson.put("setting", setting.getJsonMap());
        jobJson.put("content", contentArrayJson);
        
        JSONObject rootJson = new JSONObject();
        rootJson.put("job", jobJson);
        
        return rootJson.toJSONString();
    }
}
