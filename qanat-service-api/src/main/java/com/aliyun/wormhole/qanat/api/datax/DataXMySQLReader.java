package com.aliyun.wormhole.qanat.api.datax;

import java.util.Map;

import lombok.Data;

@Data
public class DataXMySQLReader extends DataXJdbcReader {

    /**
     * 
     */
    private static final long serialVersionUID = 2510327759673452910L;
    private String version;
    
    @Override
    public Map<String, Object> getJsonMap() {
        Map<String, Object> reader = super.getJsonMap();
        if ("8.0".equalsIgnoreCase(version)) {
        	reader.put("name", "mysql8reader");
        } else {
        	reader.put("name", "mysqlreader");
        }
        return reader;
    }

}
