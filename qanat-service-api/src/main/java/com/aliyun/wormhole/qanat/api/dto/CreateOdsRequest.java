package com.aliyun.wormhole.qanat.api.dto;

import lombok.Data;

@Data
public class CreateOdsRequest extends DatatubeRequest {

    private String dbName;
    
    private String tableName;
    
    private String timeExpression;
    
    private boolean forceRebuildOnDuplicate = false;
    
    private String objectType;
    
    private String pkField;
    
    private String workTime;
    
    private Long predictSize;
    
    private Integer predictQph;
    
    private String dstDbName;
    
    private boolean fullSyncOnly = false;
    
    private String distributeKey;
    
    private Boolean broadcast;
    
    private Boolean noPartition;
    
    private String extConf;
    
    private String partitionPolicy = "T-1D";
    
    private String syncMode = "tmp_switch_online";

    private String partitionKey;
}
