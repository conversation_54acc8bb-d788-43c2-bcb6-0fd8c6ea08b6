package com.aliyun.wormhole.qanat.api.dag;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;

@Data
public class Node implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 9071414235143178188L;
    private String id;
    private String timeExpression;
    private NodeAction nodeAction;
    private Class<? extends Node> nodeClass = Node.class;
    private NodeExecType execType = NodeExecType.JOB;
    private String action;
    private boolean dataBaseline = false;
    
    private List<String> nextNodeList;
    
    private List<String> prevNodeList;
    
    public Node() {
        
    }
    
    public Node(String id, Dag dag) {
        this.id = id;
        this.setDag(dag);
        this.setNodeClass(this.getClass());
        this.setAction("com.aliyun.wormhole.qanat.job.QanatDryRunJobProcessor");
    }

    @JSONField(serialize = false)
    public void setNext(Node node) {
        if (nextNodeList == null || nextNodeList.size() == 0) {
            nextNodeList = new ArrayList<>();
        }
        if (!nextNodeList.contains(node.getId())) {
            nextNodeList.add(node.getId());
        }
        node.setPrev(this.getId());
    }

    @JSONField(serialize = false)
    private void setPrev(String nodeId) {
        if (prevNodeList == null || prevNodeList.size() == 0) {
            prevNodeList = new ArrayList<>();
        }
        if (!prevNodeList.contains(nodeId)) {
            prevNodeList.add(nodeId);
        }
    }

    @JSONField(serialize = false)
    public void setDag(Dag dag) {
        dag.addNode(this);
    }
}
