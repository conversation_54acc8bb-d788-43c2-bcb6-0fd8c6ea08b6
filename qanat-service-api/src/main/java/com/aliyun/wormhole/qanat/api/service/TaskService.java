package com.aliyun.wormhole.qanat.api.service;

import java.util.List;
import java.util.Map;

import com.aliyun.wormhole.qanat.api.dag.Dag;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.TaskInfoDTO;
import com.aliyun.wormhole.qanat.api.dto.TaskInfoPageRequest;
import com.aliyun.wormhole.qanat.api.dto.TaskInfoRequest;
import com.aliyun.wormhole.qanat.api.dto.TaskInstanceDTO;
import com.aliyun.wormhole.qanat.api.dto.TaskInstanceRequest;
import com.github.pagehelper.PageInfo;

/**
 * 
 * <AUTHOR>
 * 2019年7月23日
 */
public interface TaskService {

    DataResult<Long> runTask(String tenantId, String empid, Long taskId);

    DataResult<Boolean> updateTaskDag(Long taskId, String operateEmpid, String dagScript, Boolean isForceStop);

    DataResult<Long> createTask(TaskInfoRequest taskReq);

    boolean checkIfDagFailed(Long taskInstId);

    boolean checkIfDagFinished(Long taskInstId);

    boolean checkIfPrevFailed(Long taskInstId, List<String> prevList);

    boolean checkIfPrevReady(Long taskInstId, List<String> prevList);

    String evalDagScript(String accessId, String accessKey, String dagScript);

    DataResult<Long> runTask(String tenantId, String empid, Long taskId, Map<String, Object> params);

    DataResult<Boolean> stopTask(String tenantId, String empid, Long taskId);

    DataResult<Boolean> deleteScheduleTask(String tenantId, String empid, Long taskId);

	DataResult<PageInfo<TaskInfoDTO>> list4Page(TaskInfoPageRequest taskReq);

	DataResult<PageInfo<TaskInstanceDTO>> listTaskInstance4Page(TaskInstanceRequest req);

    Long createDAGTask(TaskInfoRequest taskReq);

	DataResult<Boolean> stopAndDeleteTask(String tenantId, String empid, Long taskId);

	Long createDAGTask(TaskInfoRequest taskReq, Dag dag);

	DataResult<Long> createTaskFromDAG(String tenantId, String appName, String operateEmpid, String taskDesc,
			String dagScript);

	Boolean updateTaskDag(Long taskId, String operateEmpid, Dag dag, Boolean isForceStop);

	DataResult<Boolean> stopAndDeleteScheduleTask(String tenantId, String empid, Long taskId);

	Long isTaskExists(String tenantId, String taskName);

	DataResult<Boolean> updateTaskScript(String tenantId, String script, String operateEmpid, Long scriptId);

	DataResult<Long> createTaskScript(String tenantId, String script, String operateEmpid);
}