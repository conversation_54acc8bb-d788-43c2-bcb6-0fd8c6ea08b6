package com.aliyun.wormhole.qanat.api.datax;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import lombok.Data;

@Data
public class DataXLindormReader extends DataXReader {

    /**
     * 
     */
    private static final long serialVersionUID = 6508313142042372125L;
    private String username;
    private String password;
    private String seedserver;
    private String namespace;
    private String table;
    private String columns;
    private String mode;
    
    @Override
    public Map<String, Object> getJsonMap() {
        Map<String, Object> reader = new HashMap<>();
        reader.put("name", "lindormreader");
        Map<String, Object> parameter = new HashMap<>();
        reader.put("parameter", parameter);
        parameter.put("table", table);
        parameter.put("columns", columns);
        parameter.put("namespace", namespace);
        parameter.put("mode", mode);
        Map<String, Object> configuration = new HashMap<>();
        parameter.put("configuration", configuration);
        configuration.put("lindorm.client.username", username);
        configuration.put("lindorm.client.password", password);
        configuration.put("lindorm.client.seedserver", seedserver);
        configuration.put("lindorm.client.namespace", namespace);
        return reader;
    }
}
