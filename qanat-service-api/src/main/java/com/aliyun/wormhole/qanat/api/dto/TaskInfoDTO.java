package com.aliyun.wormhole.qanat.api.dto;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

@Data
public class TaskInfoDTO implements Serializable {
    /**
     * task_info.id (主键)
     * @ibatorgenerated 2019-12-13 02:11:08
     */
    private Long id;

    /**
     * task_info.gmt_create (创建时间)
     * @ibatorgenerated 2019-12-13 02:11:08
     */
    private Date gmtCreate;

    /**
     * task_info.gmt_modified (修改时间)
     * @ibatorgenerated 2019-12-13 02:11:08
     */
    private Date gmtModified;

    /**
     * task_info.create_empid (创建人工号)
     * @ibatorgenerated 2019-12-13 02:11:08
     */
    private String createEmpid;

    /**
     * task_info.modify_empid (修改人工号)
     * @ibatorgenerated 2019-12-13 02:11:08
     */
    private String modifyEmpid;

    /**
     * task_info.name (任务名称)
     * @ibatorgenerated 2019-12-13 02:11:08
     */
    private String name;

    /**
     * task_info.task_desc (任务描述)
     * @ibatorgenerated 2019-12-13 02:11:08
     */
    private String taskDesc;

    /**
     * task_info.policy (任务策略)
     * @ibatorgenerated 2019-12-13 02:11:08
     */
    private String policy;

    /**
     * task_info.engine_type (任务执行引擎类型)
     * @ibatorgenerated 2019-12-13 02:11:08
     */
    private String engineType;

    /**
     * task_info.is_deleted (是否逻辑删除)
     * @ibatorgenerated 2019-12-13 02:11:08
     */
    private Long isDeleted;

    /**
     * task_info.external_id (外部标识(SchedulerX任务id等))
     * @ibatorgenerated 2019-12-13 02:11:08
     */
    private String externalId;
}