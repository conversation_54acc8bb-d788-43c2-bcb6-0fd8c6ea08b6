package com.aliyun.wormhole.qanat.api.service;

import java.util.List;

import com.aliyun.wormhole.qanat.api.dto.PgColumnSchemaDTO;
import com.aliyun.wormhole.qanat.api.dto.PgIndexInfoDTO;
import com.aliyun.wormhole.qanat.api.dto.PgOperateTableDTO;
import com.aliyun.wormhole.qanat.api.dto.PgPkInfoDTO;

public interface PgTableService {

    boolean createPgTable(PgOperateTableDTO pgOperateTableDO);

    void alterPgTable(PgOperateTableDTO pgOperateTableDO);

    boolean createPgIndex(String tableName, List<String> columns, String indexDes, String indexType);

    boolean dropPgIndex(String indexName);

    List<PgColumnSchemaDTO> queryColumnSchemaInfo(String tableName);

    List<PgColumnSchemaDTO> queryColumnsInfo(String tableName);

    boolean queryTableIsExist(String tableName);

    PgPkInfoDTO queryPkInfo(String tableName);

    boolean createTablePk(String tableName, String colName);

    boolean dropTablePk(String tableName);

    boolean createAdjustTable(String originalTable);

    List<PgIndexInfoDTO> queryPgIndex(String tableName);

}
