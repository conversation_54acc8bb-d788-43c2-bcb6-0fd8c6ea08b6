# 基础镜像的Dockerfile ： https://lark.alipay.com/aone/docker/rm2g1d
# 基于基础镜像，请勿改动
FROM reg.docker.alibaba-inc.com/amwp/kubeone-base:latest

# 指定应用名字，配置在$APP_NAME.release文件里
# 从 build.tools.docker.args=--build-arg APP_NAME=${APP_NAME} 传入
ARG APP_NAME
ENV APP_NAME=${APP_NAME}

############# testing #################
# 设置spring profile或者自定义的jvm参数。如果需要则打开下面的注释内容
# ENV SERVICE_OPTS=-Dspring.profiles.active=testing
# 设置打开jpda 调试端口。如果需要则打开下面的注释内容
# ENV JPDA_ENABLE=1
# 增加jdk路径
ENV PATH "$PATH:/opt/taobao/java/bin/"

# 构建时要做的事，一般是执行shell命令，例如用来安装必要软件，创建文件（夹），修改文件
RUN rpm -ivh --nodeps "http://yum.corp.taobao.com/taobao/7/x86_64/current/ajdk-8_4_8-b211/ajdk-8_4_8-b211-8.4.8-20180313191644.alios7.x86_64.rpm" && \
rpm -ivh --nodeps "http://yum.tbsite.net/taobao/7/x86_64/current/tengine-proxy/tengine-proxy-2.1.22-20190214175207.el7u2.x86_64.rpm"  && \
rpm -ivh --nodeps "http://yum.tbsite.net/alios/7/os/x86_64/Packages/taobao-cronolog-1.6.2-15.alios7.x86_64.rpm" && \
ln -s /opt/taobao/install/ajdk-8_4_8-b211/ /opt/taobao/java && \
## ilogtail配置，注意修改SLS主账号ID
mkdir -p /etc/ilogtail/users && \
touch /etc/ilogtail/users/1647796581073291 && \
echo "${APP_NAME}_oxs-staging" > /etc/ilogtail/user_defined_id

# 安装datax3
COPY environment/common/datax3_q.tar /home/<USER>/
RUN tar -xvf /home/<USER>/datax3_q.tar -C /home/<USER>/

# 将logtail配置文件拷贝到本地，并挂载到宿主机上面
# 保存sls日志的采集位点，保证没有数据丢失或者重复
COPY environment/staging-sg/ilogtail_config.json /usr/local/ilogtail/

# 将系统脚本复制到镜像中，请勿改动
COPY environment/common/sys-bin/ /home/<USER>/
# 将应用启动脚本和配置文件复制到镜像中，可以根据需要选择拷贝不同环境目录（production/testing等）下文件
COPY environment/production/app/bin/ /home/<USER>/${APP_NAME}/bin
COPY environment/staging-sg/bin/ /home/<USER>/${APP_NAME}/bin
COPY environment/staging-sg/bin/start.sh /home/<USER>/
# 将应用nginx脚本复制到镜像中，可以根据需要选择拷贝不同环境目录（production/testing等）下文件
COPY environment/production/cai/ /home/<USER>/cai/

# 将构建出的主包解压到指定镜像目录中
COPY ${APP_NAME}.tgz /home/<USER>/${APP_NAME}/target/

# 启动容器时进入的工作目录
WORKDIR /home/<USER>/$APP_NAME/bin

# 设置文件操作权限
RUN chown -R admin:admin /home/<USER>/ && \
chmod -R a+x /home/<USER>/${APP_NAME}/bin/ /home/<USER>/cai/bin/  /home/<USER>/*.sh /home/<USER>/${APP_NAME}/bin/*.sh && \
chmod 1777 /tmp

# 从Sar包镜像中复制Sar包到应用镜像
# Sar包升级文档：http://gitlab.alibaba-inc.com/middleware-container/pandora/wikis/sar-upgrade
# Sar包镜像列表：http://docker.alibaba-inc.com/#/imageDesc/2751570/detail
COPY --from=reg.docker.alibaba-inc.com/pandora-sar/sar:2019-09-stable-rocketmq-optimize /opt/taobao-hsf.tgz /home/<USER>/$APP_NAME/target/taobao-hsf.tgz

# 拷贝业务个性化配置：jmenv & jvm参数等，目前主要配置在setenv.sh
COPY environment/staging-sg/bin /home/<USER>/${APP_NAME}/bin
RUN chown -R admin:admin /home/<USER>/ && \
chmod -R a+x /home/<USER>/${APP_NAME}/bin/*.sh