#!/bin/bash

APP_HOME=$(cd $(dirname ${BASH_SOURCE[0]})/..; pwd)
source "$APP_HOME/bin/setenv.sh"

CHECK_PORT="$1"
CHECK_IP="$2"
CURL_OPT="$3"

if [ -z "$CHECK_PORT" ];then
  CHECK_PORT=$TOMCAT_PORT
fi
CURL_BIN=/usr/bin/curl
SPACE_STR="..................................................................................................."
#TOMCAT_PORT="7001"
if [ -z "$CHECK_IP" ];then
  OUTIF=`/sbin/route -n | tail -1  | sed -e 's/.* \([^ ]*$\)/\1/'`
  HTTP_IP="http://`/sbin/ifconfig | grep -A1 ${OUTIF} | grep inet | awk '{print $2}' | awk -F: '{print $2}'`:$CHECK_PORT"
else
  HTTP_IP="http://$CHECK_IP:$CHECK_PORT"
fi


#####################################
checkpage() {
  URL=$1
  TITLE=$2
  CHECK_TXT=$3
  echo "$CURL_BIN" "${CURL_OPT}" "${HTTP_IP}${URL}"
  if [ "$TITLE" == "" ]; then
    TITLE=$URL
  fi
  len=`echo $TITLE | wc -c`
  len=`expr 60 - $len`
  echo -n -e "$TITLE ...${SPACE_STR:1:$len}"
  TMP_FILE=`$CURL_BIN -m 150 ${CURL_OPT} "${HTTP_IP}${URL}" 2>&1`
  if [ "$CHECK_TXT" != "" ]; then
    checkret=`echo "$TMP_FILE" | fgrep "$CHECK_TXT"`
    if [ "$checkret" == "" ]; then
        echo "[FAILED]"
        status=0
        error=1
    else
        echo "[  OK  ]"
        status=1
        error=0
    fi;
  fi
  echo
  return $error
}
#####################################
if [ -n "$CURL_OPT" ];then
  checkpage "/checkpreload.htm" "${APP_NAME}##$VERSION" "success"
else
  checkpage "/checkpreload.htm" "${APP_NAME}" "success"
fi
