# proxy conf
include                     user.conf;

worker_rlimit_nofile        100000;

error_log                   "pipe:/opt/taobao/install/cronolog/sbin/cronolog logs/cronolog/%Y/%m/%Y-%m-%d-error_log" warn;
pid                         logs/tengine-proxy.pid;

events {
    use                     epoll;
    worker_connections      20480;
}

include dso.conf;
#include tmd_main.conf;

http {
    include                 mime.types;
    default_type            application/octet-stream;

    root                    htdocs;

    sendfile                on;
    tcp_nopush              on;

    server_tokens           off;

    keepalive_timeout       0;

    client_header_timeout   1m;
    send_timeout            1m;
    client_max_body_size    3m;
    client_body_temp_path   data/client_body;

    index                   index.html index.htm;

    log_format              proxyformat    "$remote_addr $request_time_usec $http_x_readtime [$time_local] \"$request_method http://$host$request_uri\" $status $body_bytes_sent \"$http_referer\" \"$upstream_addr\" \"$http_user_agent\" \"$cookie_unb\" \"$cookie_cookie2\" \"$eagleeye_traceid\"";

    access_log              "pipe:/opt/taobao/install/cronolog/sbin/cronolog logs/cronolog/%Y/%m/%Y-%m-%d-taobao-access_log" proxyformat;
    log_not_found           off;

    # lua entry
    init_by_lua_file        "/opt/taobao/tengine/conf/init_by_lua_file.lua";

    gzip                    on;
    gzip_http_version       1.0;
    gzip_comp_level         6;
    gzip_min_length         1024;
    gzip_proxied            any;
    gzip_vary               on;
    gzip_disable            msie6;
    gzip_buffers            96 8k;
    gzip_types              text/xml text/plain text/css application/javascript application/x-javascript application/rss+xml application/json;

    # taobao trans
    trans_cookie_name       _lang;
    trans_cookie_trans_value zh_CN:TB-GBK;
    trans_cookie_nottrans_value zh_CN:GBK;
    trans_ip_file           ip.dat;
    trans_code_file         sm2tr.txt;
    trans_content_type      application/xhtml+xml text/plain text/css text/xml text/javascript;
    trans_accept_language_trans zh-HK zh-TW zh-MO zh-Hant;
    trans_accept_language_notrans zh-CN zh-SG zh-Hans;

    eagleeye_traceid_var    $eagleeye_traceid;
    eagleeye_traceid_arg    tb_eagleeye_traceid;

    proxy_set_header        Host $host;
    proxy_set_header        X-Real-IP $remote_addr;
    proxy_set_header        Web-Server-Type nginx;
    proxy_set_header        WL-Proxy-Client-IP $remote_addr;
    proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header        EagleEye-TraceId $eagleeye_traceid;
    add_header              EagleEye-TraceId-daily $eagleeye_traceid;
    proxy_redirect          off;
    proxy_buffers           128 8k;
    proxy_temp_path         data/proxy;
    proxy_intercept_errors  on;

    include services.conf;
    include cell_main.conf;

    # fight mhtml/utf-7 bug
    hat_content             "\r\n";
    hat_types               text/html text/css;

    # waf, fight hashdos attack
    #waf_max_post_params              1000;
    #waf_max_args                     1000;
    #waf_max_cookies                  1000;
    #waf_post_delimiter_maxlen        70;

    # detector
    #tesla           on;
    #tsl_inject_tail on;
    #tsl_med         on;
    #tsl_med_cookie  _med;
    #tsl_med_jspath  med.js;
    #user_agent_detector on;
    #include detector.conf;

    # sinfo
    #include sinfo.conf;

    variables_hash_max_size     1024;
    variables_hash_bucket_size  64;

    server_names_hash_bucket_size 4096;

    server {
        listen              80;
        server_name         status.taobao.com;

        tmd off;

        location            = /nginx_status {
            stub_status     on;
        }
    }

    include apps/*.conf;
}
