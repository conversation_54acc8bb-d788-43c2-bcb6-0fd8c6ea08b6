server {
    listen              80 default_server;
    server_name         www.taobao.com;

    # for daily-traceid: use user_ip in eagleeye traceid
    set $eaddr $remote_addr;
    if ($http_x_forwarded_for != "") {
           set $eaddr $http_x_forwarded_for;
    }
    if ($http_x_real_ip != "") {
           set $eaddr $http_x_real_ip;
    }
    eagleeye_ip_part    $eaddr;

    client_max_body_size    200m;

    location / {
        proxy_pass   http://127.0.0.1:7001;
    }
}
