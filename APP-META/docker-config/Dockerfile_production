# 用基础镜像地址替换下方镜像地址
FROM reg.docker.alibaba-inc.com/aone-base/dayu-metada-portal-aliyun-com_20210824_10496006_init_1:20210824152228

# 指定运行时的系统环境变量,如下请替换appName为自己应用名称
ENV BUILD_APP_NAME dayu-metada-portal-aliyun-com

# 将应用nginx配置文件复制到镜像中
COPY environment/production/app/ /home/<USER>/$BUILD_APP_NAME/
COPY environment/production/cai/ /home/<USER>/cai/

# 设置文件操作权限
RUN chmod -R a+x /home/<USER>/$BUILD_APP_NAME/bin/ /home/<USER>/cai/bin/

# 将构建出的主包复制到指定镜像目录中
COPY $BUILD_APP_NAME.tgz /home/<USER>/$BUILD_APP_NAME/target/$BUILD_APP_NAME.tgz

# 挂载数据卷,指定目录挂载到宿主机上面,为了能够保存（持久化）数据以及共享容器间的数据，为了实现数据共享，例如日志文件共享到宿主机或容器间共享数据.
VOLUME /home/<USER>/ilogtail

# 创建用户标志文件，先获取sls登录云账号的pk(uid), 在/etc/ilogtail/users/${uid}下创建文件,可以写在具体环境的dockerfile中
RUN mkdir -p /etc/ilogtail/users && \
touch /etc/ilogtail/users/1443034909746742 && \
echo ${BUILD_APP_NAME}_production > /etc/ilogtail/user_defined_id

# 配置sls的ilogtail，设置check_point_filename到VOLUMN目录中(基础镜像中挂载)
# 配置位点文件、用户采集配置缓存文件，放到ilogtail_config.json中
COPY environment/common/ilogtail_config.json /usr/local/ilogtail/