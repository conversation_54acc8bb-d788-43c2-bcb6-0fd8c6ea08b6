# 基于基础镜像
FROM reg.docker.alibaba-inc.com/alibase/alios7u2-min

# 应用名定义, 把appName为自己的应用名称
ENV BUILD_APP_NAME qanat-aliyun-inc-com

# 指定应用名字，配置在$APP_NAME.release文件里
# 从 build.tools.docker.args=--build-arg APP_NAME=${APP_NAME} 传入
#ARG APP_NAME
#ENV APP_NAME=${APP_NAME}

# 构建时要做的事，一般是执行shell命令，例如用来安装必要软件，创建文件（夹），修改文件
RUN rpm -ivh --nodeps "http://yum.tbsite.net/taobao/7/x86_64/current/ali-jdk/ali-jdk-8.4.8-1574344.alios7.x86_64.rpm" && \
rpm -ivh --nodeps "http://yum.tbsite.net/taobao/7/x86_64/current/tengine-proxy/tengine-proxy-2.1.22-20190214175207.el7u2.x86_64.rpm"  && \
rpm -ivh --nodeps "http://yum.tbsite.net/alios/7/os/x86_64/Packages/taobao-cronolog-1.6.2-15.alios7.x86_64.rpm" && \
ln -s /opt/taobao/install/ajdk-8_4_8-b211/ /opt/taobao/java && \

mkdir -p /home/<USER>/$BUILD_APP_NAME/target/ && \ 
wget -c "http://pandora.alibaba-inc.com/pandora-web/sar/2019-09-stable-rocketmq-optimize/taobao-hsf.tgz" -O /home/<USER>/$BUILD_APP_NAME/target/taobao-hsf.tgz && \
mkdir -p /home/<USER>/$BUILD_APP_NAME/ && \
mkdir -p /home/<USER>/cai/ && \
echo "/home/<USER>/$BUILD_APP_NAME/bin/jbossctl stop" > /home/<USER>/stop.sh && \
echo "sudo /etc/init.d/ilogtaild restart" >> /home/<USER>/stop.sh && \
echo "sudo /etc/init.d/ilogtaild start" >> /home/<USER>/start.sh && \
echo "/home/<USER>/$BUILD_APP_NAME/bin/jbossctl restart" >> /home/<USER>/start.sh && \
echo "/home/<USER>/$BUILD_APP_NAME/bin/preload.sh" > /home/<USER>/health.sh && \
chmod +x /home/<USER>/*.sh

# 挂载数据卷,指定目录挂载到宿主机上面,为了能够保存（持久化）数据以及共享容器间的数据，为了实现数据共享，例如日志文件共享到宿主机或容器间共享数据.
VOLUME /home/<USER>/$BUILD_APP_NAME/logs \
              /home/<USER>/logs \
              /home/<USER>/cai/logs \
              /home/<USER>/diamond \
              /home/<USER>/snapshots \
              /home/<USER>/configclient \
              /home/<USER>/notify \
              /home/<USER>/catserver \
              /home/<USER>/liaoyuan-out \
              /home/<USER>/vipsrv-dns \
              /home/<USER>/vipsrv-failover \
              /home/<USER>/vipsrv-cache \
              /home/<USER>/csp \
              /home/<USER>/.rocketmq_offsets \
              /home/<USER>/amsdata \
              /home/<USER>/amsdata_all

# 启动容器时进入的工作目录
WORKDIR /home/<USER>/$BUILD_APP_NAME/bin

#容器启动时自动执行的脚本，我们一般会将应用启动脚本放在这里，相当于系统自启应用
ENTRYPOINT ["/home/<USER>/start.sh"]

#安装sls
RUN yum -y install -b current ali-sls-ilogtail

# 安装datax3
COPY environment/common/datax3_q.tar /home/<USER>/
RUN tar -xvf /home/<USER>/datax3_q.tar -C /home/<USER>/