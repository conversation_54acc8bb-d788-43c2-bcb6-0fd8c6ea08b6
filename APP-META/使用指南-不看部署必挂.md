
## Docker相关链接

* Aone关于docker的文章目录（新人必看）：https://lark.alipay.com/aone/docker
* Dockerfile文件命名的规范：https://lark.alipay.com/aone/docker/rm2g1d
* 使用docker常见问题： http://www.atatech.org/articles/53899
* docker答疑群：钉钉搜索“PouchSigmaAone答疑”，群号：21705153

## PandoraBoot基础知识学习

> 不了解pandora boot应用如何部署的亲先阅读下方知识，再进行逐步操作

* Pandora Boot 应用部署标准： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/online-deploy
* Pandora Boot 应用Docker参考： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/docker
* pandora-boot-docker-demo： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot-docker-demo

## 发布步骤

直接到Aone上新建发布变更即可。不同环境对应不同的DockerFile。

* Dockerfile文件命名的规范：https://lark.alipay.com/aone/docker/rm2g1d