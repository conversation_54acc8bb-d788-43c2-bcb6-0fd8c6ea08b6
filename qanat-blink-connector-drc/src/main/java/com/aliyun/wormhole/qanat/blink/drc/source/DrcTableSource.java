/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.blink.drc.source;

import java.util.List;

import com.alibaba.blink.streaming.connectors.common.source.SourceFunctionTableSource;

import com.aliyun.wormhole.qanat.blink.drc.io.DrcInputConfig;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.table.api.RichTableSchema;

public class DrcTableSource extends SourceFunctionTableSource<String> {

	private List<DrcInputConfig> drcInputConfigList;
	private RichTableSchema richSchema;

	public DrcTableSource(RichTableSchema richSchema, List<DrcInputConfig> drcInputConfigList) {
		this.drcInputConfigList = drcInputConfigList;
		this.richSchema = richSchema;
	}

	@Override
	public SourceFunction getSourceFunction() {
	    return new DrcSource(richSchema, drcInputConfigList);
	}

	@Override
	public String explainSource() {
		return String.format("DrcTableSource-drc");
	}
}
