/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.blink.drc.source;

import com.alibaba.blink.table.connectors.conf.BlinkOptions;
import com.alibaba.fastjson.JSON;
import com.aliyun.wormhole.qanat.blink.drc.io.DrcInputConfig;

import com.alibaba.blink.streaming.connectors.common.reader.RecordReader;
import com.alibaba.blink.streaming.connectors.common.source.AbstractParallelSource;
import com.alibaba.blink.streaming.connectors.common.source.SourceUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.core.io.InputSplit;
import org.apache.flink.table.api.RichTableSchema;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DrcSource extends AbstractParallelSource<String, Integer> {
	private static final Logger LOGGER = LoggerFactory.getLogger(DrcSource.class);
	private int totalPartition;
	private List<String> partitionList;
	private List<DrcInputConfig> drcReaderConfigList;
	private RichTableSchema richSchema;
	private List<DrcInputConfig> inputConfigList;
	
	@Override
	public void initOperator(Configuration config) throws IOException {
		config.setString(BlinkOptions.INNER_SAMPLE_TABLE_NAME, tableName);
	}

	@Override
	public RecordReader<String, Integer> createReader(Configuration configuration) throws IOException {
		LOGGER.info("DrcSource: begin to createReader.");
		return new DrcRecordReader(richSchema);
	}


	@Override
	public InputSplit[] createInputSplitsForCurrentSubTask(int numberOfParallelSubTasks, int indexOfThisSubTask) throws IOException {
		LOGGER.info("DrcSource: begin to create input splits for current subTask");
		LOGGER.info("DrcSource: get numberOfParallelSubTasks: {}, indexOfThisSubTask: {}", numberOfParallelSubTasks, indexOfThisSubTask);
		LOGGER.info("DrcSource: get totalPartition: {}", totalPartition);
		LOGGER.info("DrcSource: get inputConfigList2: {}", JSON.toJSONString(inputConfigList));

		List<Integer> subscribedGroups = SourceUtils.modAssign(this.toString(), numberOfParallelSubTasks, indexOfThisSubTask, totalPartition);
		LOGGER.info("DrcSource: get subscribedGroups: {}", JSON.toJSONString(subscribedGroups));

		DrcReaderConfig[] inputSplits = new DrcReaderConfig[subscribedGroups.size()];
		int i = 0;
		for (Integer partitionNumber : subscribedGroups) {
			DrcReaderConfig drcReaderConfig = new DrcReaderConfig(partitionNumber);
			drcReaderConfig.setDrcInputConfig(drcReaderConfigList.get(partitionNumber));
			LOGGER.info("DrcSource: partitionNumber: {}, add config to current parallel: {}", partitionNumber, JSON.toJSONString(drcReaderConfig));

			inputSplits[i++] = drcReaderConfig;
		}

		return inputSplits;
	}


	@Override
	public List<String> getPartitionList() {
		LOGGER.info("DrcSource: get getPartitionList: {}", partitionList);
		return partitionList;
	}


	@Override
	public String toString() {
		String name = String.format("%s:%s", getClass().getSimpleName(), "");
		LOGGER.info("DrcSource: get name: {}", name);
		return name;
	}


	public DrcSource(RichTableSchema richSchema, List<DrcInputConfig> inputConfigList) {
	    this.richSchema = richSchema;
	    this.inputConfigList = inputConfigList;
		LOGGER.info("DrcSource: get inputConfigList: {}", JSON.toJSONString(inputConfigList));
		// get depart by db name, cause db can be supported by only one connection at each time
		Map<String, DrcInputConfig> drcDBMapConfig = new HashMap<>();
		for (DrcInputConfig inputConfig : inputConfigList) {
        		String dbName = inputConfig.getDbName();
        		if (!drcDBMapConfig.containsKey(dbName)) {
        			drcDBMapConfig.put(dbName, inputConfig);
        		}
		}
		partitionList = new ArrayList<>();
		drcReaderConfigList = new ArrayList<>();
		for (Map.Entry<String, DrcInputConfig> entry : drcDBMapConfig.entrySet()) {
			partitionList.add(entry.getKey());
			drcReaderConfigList.add(entry.getValue());
		}
		totalPartition = drcReaderConfigList.size();
		LOGGER.info("DrcSource: get drcReaderConfigList: {}, totalPartition: {}", JSON.toJSONString(drcReaderConfigList), totalPartition);
		LOGGER.info("DrcSource: get partitionList: {}", JSON.toJSONString(partitionList));
	}

	public DrcSource setTableName(String tableName) {
		this.tableName = tableName;
		return this;
	}
}
