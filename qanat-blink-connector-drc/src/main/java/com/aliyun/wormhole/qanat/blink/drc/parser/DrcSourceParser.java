package com.aliyun.wormhole.qanat.blink.drc.parser;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.blink.streaming.connectors.common.TableInfoAware;
import com.alibaba.blink.streaming.connectors.common.source.SourceCollector;
import com.taobao.eagleeye.EagleEye;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.table.dataformat.BinaryString;
import org.apache.flink.table.dataformat.GenericRow;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.typeutils.BaseRowTypeInfo;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DrcSourceParser
    extends Object
    implements TableInfoAware, SourceCollector<String, BaseRow> {
    private static final long serialVersionUID = 1L;
    private static final Logger LOGGER = LoggerFactory.getLogger(DrcSourceParser.class);

    private Map<String, String> config = new HashMap<>();
    private List<String> fieldList = new ArrayList<>();

    private RowTypeInfo rowTypeInfo;
    private String taskId;
    private long lastPrintTS = 0L;

    public TableInfoAware setUserParamsMap(Map<String, String> map) {
        if (null == map || map.isEmpty()) {
            throw new IllegalArgumentException("Failed to get map info.");
        }

        this.config.putAll(map);
        LOGGER.info("DrcSourceParser setUserParamsMap: {}", this.config);

        return this;
    }

    public TableInfoAware setPrimaryKeys(List<String> list) {
        return this;
    }

    public TableInfoAware setHeaderFields(List<String> list) {
        return this;
    }

    public TableInfoAware setRowTypeInfo(RowTypeInfo rowTypeInfo) {
        this.rowTypeInfo = rowTypeInfo;

        this.fieldList.addAll(Arrays.asList(rowTypeInfo.getFieldNames()));
        LOGGER.info("DrcSourceParser get fieldList: {}", this.fieldList);

        return this;
    }

    public TableInfoAware setRowTypeInfo(DataType dataType) {
        return this;
    }

    public void open(FunctionContext context) {
        LOGGER.info("DrcSourceParser begin to open.");
        this.taskId = (String)this.config.get("taskId".toLowerCase());
        if (StringUtils.isBlank(this.taskId)) {
//            throw new IllegalArgumentException("failed to get taskId from parameter list.");
        }
        LOGGER.info("DrcSourceParser: get parameter url : {}", this.taskId);
    }

    public void parseAndCollect(String result, Collector<BaseRow> collector) {
        if (null == result) {
            return;
        }
        String traceId = null;
		try {
			traceId = EagleEye.getTraceId() != null ? EagleEye.getTraceId() : EagleEye.generateTraceId(InetAddress.getLocalHost().getHostAddress());
		} catch (UnknownHostException e1) {
			e1.printStackTrace();
		}
		long ts = System.currentTimeMillis();
		LOGGER.info("datatube_trace|drc_source {} msg={}", traceId, result);
        GenericRow genericRow = new GenericRow(this.fieldList.size());
        for (int idx = 0; idx < this.fieldList.size(); idx++) {
            if ("__ts__".equalsIgnoreCase(fieldList.get(idx))) {
                genericRow.update(idx, ts);
            } else if ("__traceId__".equalsIgnoreCase(fieldList.get(idx))) {
                genericRow.update(idx, traceId);
            } else {
                genericRow.update(idx, BinaryString.fromString(result));
            }
        }
        timingPrinter(genericRow);
        collector.collect(genericRow);
    }

    private void timingPrinter(GenericRow genericRow) {
        if (System.currentTimeMillis() - this.lastPrintTS > 100000L) {

            Map<String, String> debugInfo = new HashMap<String, String>();
            for (int idx = 0; idx < this.fieldList.size(); idx++) {
                debugInfo.put(this.fieldList.get(idx), genericRow.getField(idx).toString());
            }

            this.lastPrintTS = System.currentTimeMillis();
            LOGGER.info("DrcSourceParser get record: {}", debugInfo);
        }
    }

    public void close() {
        LOGGER.info("DrcSourceParser begin to close");
    }

    public TypeInformation<BaseRow> getProducedType() {
        return new BaseRowTypeInfo(GenericRow.class, this.rowTypeInfo.getFieldTypes(), this.rowTypeInfo.getFieldNames());
    }
}
