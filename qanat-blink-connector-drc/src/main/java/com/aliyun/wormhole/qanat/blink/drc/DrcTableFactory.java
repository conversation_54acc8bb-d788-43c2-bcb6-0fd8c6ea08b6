package com.aliyun.wormhole.qanat.blink.drc;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.api.TableSourceParser;
import org.apache.flink.table.factories.BatchTableSourceFactory;
import org.apache.flink.table.factories.StreamTableSourceFactory;
import org.apache.flink.table.factories.TableSourceParserFactory;
import org.apache.flink.table.sources.BatchTableSource;
import org.apache.flink.table.sources.StreamTableSource;
import org.apache.flink.table.types.InternalType;
import org.apache.flink.table.typeutils.TypeUtils;
import org.apache.flink.table.util.TableProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.blink.streaming.connector.hbase.utils.ByteSerializer;
import com.alibaba.blink.streaming.connectors.common.exception.InvalidParamException;
import com.alibaba.blink.streaming.connectors.common.exception.NotEnoughParamsException;
import com.alibaba.blink.streaming.connectors.common.source.SourceCollectorTableFunction;
import com.alibaba.blink.streaming.connectors.common.source.SourceUtils;
import com.alibaba.blink.streaming.connectors.common.util.BlinkStringUtil;
import com.alibaba.blink.streaming.connectors.common.util.DateUtil;
import com.alibaba.blink.table.connectors.conf.BlinkOptions;
import com.alibaba.blink.table.factories.BlinkTableFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.aliyun.wormhole.qanat.blink.drc.io.DrcInputConfig;
import com.aliyun.wormhole.qanat.blink.drc.parser.DrcSourceParser;
import com.aliyun.wormhole.qanat.blink.drc.source.DrcTableSource;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.OkHttpClient.Builder;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_PROPERTY_VERSION;
import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_TYPE;

public class DrcTableFactory extends BlinkTableFactory implements
		TableSourceParserFactory,
		BatchTableSourceFactory<String>,
		StreamTableSourceFactory<String> {

    private final static Logger log = LoggerFactory.getLogger(DrcTableFactory.class);
    
	@Override
	public TableSourceParser createParser(
			String tableName, RichTableSchema tableSchema, TableProperties properties) {
	    log.info("tableSchema={}", JSON.toJSONString(tableSchema));
		TableSourceParser tableSourceParser = SourceUtils.createParserFromDDL(tableSchema, properties, classLoader);
		if (tableSourceParser == null) {
		    DrcSourceParser parser = new DrcSourceParser();
		    tableSourceParser = new TableSourceParser(
                new SourceCollectorTableFunction<>(parser),
                Collections.singletonList("f0"));
		}
		return tableSourceParser;
	}

	private DrcTableSource createSource(Map<String, String> props) {
		TableProperties tableProperties = new TableProperties();
		tableProperties.putProperties(props);
		RichTableSchema richTableSchema = tableProperties.readSchemaFromProperties(classLoader);

		for (InternalType t : richTableSchema.getColumnTypes()) {
			Class<?> cls = TypeUtils.getExternalClassForType((t));
			if (!ByteSerializer.isSupportedType(cls)) {
				throw new IllegalArgumentException("Unsupported column type: " + cls);
			}
		}

		List<String> fieldList = new ArrayList<>();
		fieldList.addAll(Arrays.asList(richTableSchema.getColumnNames()));

		String url = tableProperties.getString(QanatDrcOptions.URL);
        String dbName = tableProperties.getString(QanatDrcOptions.DBNAME);
        String filter = tableProperties.getString(QanatDrcOptions.FILTER);
        String groupName = tableProperties.getString(QanatDrcOptions.GROUPNAME);
        String identification = tableProperties.getString(QanatDrcOptions.IDENTIFICATION);
        String taskId = tableProperties.getString(QanatDrcOptions.TASKID);
        String endpoint = tableProperties.getString(QanatDrcOptions.ENDPOINT);
        String dataModel = tableProperties.getString(QanatDrcOptions.DATAMODEL);
        String consumeId = tableProperties.getString(QanatDrcOptions.CONSUMEID);
        String consumeMode = tableProperties.getString(QanatDrcOptions.CONSUMEMODE);
        String cacheHost = tableProperties.getString(QanatDrcOptions.CACHEHOST);
        Integer cachePort = tableProperties.getInteger(QanatDrcOptions.CACHEPORT);
        
        if (StringUtils.isBlank(endpoint)) {
        	endpoint = "http://***************:8080";
        }
        
//        if (BlinkStringUtil.isEmpty(taskId, groupName, identification, consumeId)) {
//			throw new NotEnoughParamsException(QanatDrcOptions.PARAMS_HELP_MSG);
//		}
        
        Long startTime = null;
        String startDateTime = tableProperties.getString(QanatDrcOptions.STARTTIME);
        if (!BlinkStringUtil.isEmpty(startDateTime)) {
            try {
                startTime = DateUtil.parseDateString(BlinkOptions.DATE_FORMAT, startDateTime, null);
            } catch (ParseException e) {
                throw new InvalidParamException(String.format(
                        "Incorrect datetime format: %s, pls use ISO-8601 " +
                        "complete date plus hours, minutes and seconds format:%s",
                        startDateTime,
                        BlinkOptions.DATE_FORMAT), e);
            }
        }
        List<DrcInputConfig> inputConfigList = new ArrayList<>();
        if (StringUtils.isNotBlank(taskId)) {
            List<JSONObject> topics = getDrcTopics(groupName, identification, taskId, endpoint);
            if (topics != null && topics.size() > 0) {
                for (int i = 0; i < topics.size(); i++) {
                    DrcInputConfig drcInputConfig = new DrcInputConfig();
                    drcInputConfig.setTaskId(taskId);
                    JSONObject cfg = topics.get(i);
                    String topic = cfg.getString("topic");
                    drcInputConfig.setDbName(topic);
                    drcInputConfig.setFilter(filter);
                    drcInputConfig.setGroupName(groupName);
                    drcInputConfig.setIdentification(identification);
                    drcInputConfig.setEndpoint(endpoint);
                    drcInputConfig.setUrl(cfg.getString("manager.host"));
                    drcInputConfig.setDataModel(dataModel);
                    drcInputConfig.setConsumeId(consumeId);
                    drcInputConfig.setConsumeMode(consumeMode);
                    drcInputConfig.setCacheHost(cacheHost);
                    drcInputConfig.setCachePort(cachePort);
                    Long checkPointStart = cfg.getLong("checkpoint-start")*1000;
                    log.info("taskId={},startTime={},checkPointStart={},res={}", taskId, startTime, checkPointStart, (startTime < checkPointStart));
                    drcInputConfig.setStartTime(startTime == null ? null : (startTime < checkPointStart ? checkPointStart : startTime) + "");
                    inputConfigList.add(drcInputConfig);
                }
            } else {
                throw new InvalidParamException("Can not get drc topics");
            }
        } else {
            DrcInputConfig drcInputConfig = new DrcInputConfig();
            drcInputConfig.setTaskId(taskId);
            drcInputConfig.setDbName(dbName);
            drcInputConfig.setFilter(filter);
            drcInputConfig.setGroupName(groupName);
            drcInputConfig.setIdentification(identification);
            drcInputConfig.setUrl(url);
            drcInputConfig.setStartTime(startTime == null ? null : startTime + "");
            drcInputConfig.setDataModel(dataModel);
            drcInputConfig.setConsumeId(consumeId);
            drcInputConfig.setConsumeMode(consumeMode);
            drcInputConfig.setCacheHost(cacheHost);
            drcInputConfig.setCachePort(cachePort);
            inputConfigList.add(drcInputConfig);
        }

		return new DrcTableSource(richTableSchema, inputConfigList);
	}
    
    private List<JSONObject> getDrcTopics(String username, String password, String taskId, String endpoint) {
        List<JSONObject> topics = new ArrayList<>();
        String url = endpoint + "/api/auth";
        String token = null;
        String data = String.format("taskParam={\"user\":\"%s\",\"password\":\"%s\"}", username, password);
        String resp = doHttpPost(url, data);
        if (StringUtils.isNotBlank(resp)) {
            JSONObject respJson = JSON.parseObject(resp);
            if (respJson != null && respJson.getBoolean("success") && respJson.getJSONObject("data") != null) {
                token = respJson.getJSONObject("data").getString("token");
                
                data = String.format("taskParam={\"taskId\":\"%s\",\"token\":\"%s\"}", taskId, token);
                url = endpoint + "/api/query";
                resp = doHttpPost(url, data);
                if (StringUtils.isNotBlank(resp)) {
                    respJson = JSON.parseObject(resp);
                    if (respJson != null && respJson.getBoolean("success") && respJson.getJSONObject("data") != null) {
                        JSONArray cfgArray =  respJson.getJSONObject("data").getJSONArray("cfg");
                        if (cfgArray != null) {
                            for (int i = 0; i < cfgArray.size(); i++) {
                                JSONObject cfg = cfgArray.getJSONObject(i);
                                topics.add(cfg);
                            }
                        }
                    }
                }
            }
        }
        return topics;
    }
	
	private String doHttpPost(String url, String data) {
        String resp = null;
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(mediaType, data))
                .build();
        OkHttpClient okHttpClient = new OkHttpClient();
        okHttpClient = okHttpClient.newBuilder().connectTimeout(60, TimeUnit.SECONDS).readTimeout(60, TimeUnit.SECONDS).writeTimeout(60, TimeUnit.SECONDS).build();
        Response response = null;
        try {
            log.info("req:post {} -d'{}'", url, data);
            response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                resp = response.body().string(); 
                log.info("resp:{}", resp); 
            }
        } catch (IOException e) {
            log.error("http request failed", e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return resp;
    }

	@Override
	protected List<String> supportedSpecificProperties() {
		return BlinkOptions.MYSQLSCAN.SUPPORTED_KEYS;
	}

	@Override
	protected Map<String, String> requiredContextSpecific() {
		Map<String, String> context = new HashMap<>();
		context.put(CONNECTOR_TYPE, "qanat_scan"); // MYSQLSCAN
		context.put(CONNECTOR_PROPERTY_VERSION, "1"); // backwards compatibility
		return context;
	}

	@Override
	public BatchTableSource<String> createBatchTableSource(Map<String, String> properties) {
		return createSource(properties);
	}

	@Override
	public StreamTableSource<String> createStreamTableSource(Map<String, String> properties) {
		return createSource(properties);
	}
}
