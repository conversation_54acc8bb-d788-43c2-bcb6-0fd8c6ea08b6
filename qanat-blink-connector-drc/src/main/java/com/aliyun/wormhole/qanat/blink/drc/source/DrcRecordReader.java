/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.blink.drc.source;

import com.alibaba.blink.streaming.connectors.common.reader.Interruptible;
import com.alibaba.blink.streaming.connectors.common.reader.MonotonyIncreaseProgress;
import com.alibaba.blink.streaming.connectors.common.reader.RecordReader;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.blink.drc.io.DrcInputConfig;
import com.aliyun.wormhole.qanat.drc.event.DbEvent;
import com.aliyun.wormhole.qanat.drc.event.DbEventFieldInfo;
import com.aliyun.wormhole.qanat.drc.event.DbEventInfo;
import com.aliyun.wormhole.qanat.drc.event.FieldType;
import com.aliyun.wormhole.qanat.drc.event.RowEventType;
import com.taobao.drc.client.DRCClient;
import com.taobao.drc.client.DRCClientFactory;
import com.taobao.drc.client.DataFilter;
import com.taobao.drc.client.Listener;
import com.taobao.drc.client.message.DataMessage;
import com.taobao.drc.client.message.DataMessage.Record.Field;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPoolConfig;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.core.io.InputSplit;
import org.apache.flink.table.api.RichTableSchema;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.TimeUnit;

public class DrcRecordReader implements RecordReader<String, Integer>, Interruptible {

    private static final Logger log = LoggerFactory.getLogger(DrcRecordReader.class);

    private volatile boolean interrupted = false;
    private RichTableSchema richSchema;
    private DRCClient client = null;
    private static final String supportOpts = "update|delete|insert|ddl";
    private ArrayBlockingQueue<String> queue;
    private JedisCluster jedisCluster;
    
    public DrcRecordReader(RichTableSchema richSchema) {
        this.richSchema = richSchema;
        queue = new ArrayBlockingQueue<>(20000);
    }

    @Override
    public void open(InputSplit inputSplit, RuntimeContext context) {
    	DrcReaderConfig drcScanReaderConfig = (DrcReaderConfig)inputSplit;
        init(drcScanReaderConfig);
    }
    
    private void init(DrcReaderConfig drcScanReaderConfig) {
        log.info("drcScanReaderConfig={}", JSON.toJSONString(drcScanReaderConfig));
    	DrcInputConfig drcInputConfig = drcScanReaderConfig.getDrcInputConfig();
        Properties props = new Properties();
        props.put("manager.host", drcInputConfig.getUrl());
        
        String[] tokens = drcInputConfig.getDbName().split("-");
        String splitNumberSuffix = tokens[tokens.length - 1];
        
        String cacheKey = "DRC_" + drcInputConfig.getConsumeId() + "_" + splitNumberSuffix;
        log.info("splitNumber={} cacheKey={} topic={}", drcScanReaderConfig.getSplitNumber(), cacheKey, drcInputConfig.getDbName());
        
        if ("cache".equalsIgnoreCase(drcInputConfig.getConsumeMode())) {
	        try {
	        	jedisCluster = new JedisCluster(new HostAndPort(drcInputConfig.getCacheHost(), drcInputConfig.getCachePort()), 3000, 3000, 50, new JedisPoolConfig());
	        	jedisCluster.get(cacheKey);
	        } catch (Exception e) {
	        	log.error("JedisCluster init faield, error={}", e.getMessage(), e);
	        	throw new RuntimeException("JedisCluster init faield");
	        }
        }
        
        try {
            client = DRCClientFactory.create(DRCClientFactory.Type.MYSQL, props);
        } catch (Exception e) {
        	log.error("create  DRCClient faield, error={}", e.getMessage(), e);
        	throw new RuntimeException("create  DRCClient faield");
        }
        
        client.addListener(new Listener() {
            // 异步通知
            @Override
            public void notify(DataMessage message) throws Exception {
                if (message == null) {
                    return;
                }
                List<DataMessage.Record> records = message.getRecordList();
                if (records.size() == 0) {
                    return;
                }

                // 读取drc记录
                for (DataMessage.Record record : records) {
					final String timeSync = record.getTimestamp();
                    String currType = record.getOpt().toString();
                    String currTbName = record.getTablename();
                    if (!supportOpts.contains(currType.toLowerCase())) {
                        continue;
                    }
                    if (!"heartbeat".equalsIgnoreCase(currTbName)) {
                    	if ("ddl".equalsIgnoreCase(currType)) {
	                    	DbEventInfo dbEventInfo = parseDdl(record);
	                    	String event = JSON.toJSONString(dbEventInfo);
	                        log.info("datatuble_ddl_binlog_event|{}|{}", drcInputConfig.getTaskId(), event);
                    	} else {
                    		String event = null;
                    		if ("datatube".equalsIgnoreCase(drcInputConfig.getDataModel())) {
    	                    	DbEventInfo dbEventInfo = parse(record, currType);
    	                    	event = JSON.toJSONString(dbEventInfo);
                    		} else {
                    			event = JSON.toJSONString(record);
                    		}
                            if ("cache".equalsIgnoreCase(drcInputConfig.getConsumeMode())) {
	                            if (StringUtils.isNotBlank(drcInputConfig.getConsumeId())) {
	                            	long cacheStartTs = System.currentTimeMillis();
	                            	try {
		                            	jedisCluster.set(cacheKey, timeSync);
		    	                        log.info("kvstore taskId={},cacheKey={},timeSync={},cost={}", drcInputConfig.getTaskId(), cacheKey, timeSync, System.currentTimeMillis()-cacheStartTs);
	                            	} catch (Exception e) {
		    	                        log.error("kvstore taskId={},cacheKey={},timeSync={},failed={}", drcInputConfig.getTaskId(), cacheKey, timeSync, e.getMessage(), e);
	                            	}
	                            }
                            }
	                        log.info("event={}", event);
	                        queue.put(event);
                    	}
                    }
                }
            }

            @Override
            public void notifyRuntimeLog(String level, String logInfo)
                    throws Exception {
                if (!"info".equalsIgnoreCase(level)) {
                    log.error("notifyRuntimeLog {}, {}", level, logInfo);
                }
            }

            @Override
            public void handleException(Exception e) {
                log.error("handleException {}", e);
            }
        });
        DataFilter filter = new DataFilter(StringUtils.isBlank(drcInputConfig.getFilter()) ? "*.*" : drcInputConfig.getFilter());
        client.addDataFilter(filter);
        client.requireTxnMark(false);
        try {
            Long userInputStartMs = Long.valueOf(drcInputConfig.getStartTime());
            String startTime = Long.toString(userInputStartMs/1000);
            
        	long drcCheckpointStartMs = 0L;
            JSONObject topicInfo = getDrcTopic(drcInputConfig.getGroupName(), drcInputConfig.getIdentification(), drcInputConfig.getTaskId(), drcInputConfig.getEndpoint(), drcInputConfig.getDbName());
            if (topicInfo != null) {
                Long checkPointStart = topicInfo.getLong("checkpoint-start");
                drcCheckpointStartMs = checkPointStart*1000;
            }
            
            long cacheSyncMs = 0L;
            if ("cache".equalsIgnoreCase(drcInputConfig.getConsumeMode())) {
	            if (StringUtils.isNotBlank(drcInputConfig.getConsumeId())) {
                	long cacheStartTs = System.currentTimeMillis();
                	String timeSync = null;
                	try {
			            timeSync = jedisCluster.get(cacheKey);
	                    log.info("kvget taskId={},cacheKey={},timeSync={},cost={}", drcInputConfig.getTaskId(), cacheKey, timeSync, System.currentTimeMillis()-cacheStartTs);
	            	} catch (Exception e) {
	                    log.error("kvget taskId={},cacheKey={},failed={}", drcInputConfig.getTaskId(), cacheKey, e.getMessage(), e);
	            	}
		            if (StringUtils.isNotBlank(timeSync)) {
		            	cacheSyncMs = Long.valueOf(timeSync)*1000;
		            }
	            }
            }

        	int cacheBufferSec = 60;//回追60秒 
            Long startTimeMs = null;
	        if (userInputStartMs < drcCheckpointStartMs) {
	        	if (cacheSyncMs < drcCheckpointStartMs) {
	        		startTimeMs = drcCheckpointStartMs;
	        	} else {
	        		startTimeMs = cacheSyncMs - cacheBufferSec*1000;
	        	}
	        } else {
	        	if (cacheSyncMs < userInputStartMs) {
	        		startTimeMs = userInputStartMs;
	        	} else {
	        		startTimeMs = cacheSyncMs - cacheBufferSec*1000;
	        	}
	        }
        	startTime = Long.toString(startTimeMs/1000);
            log.info("taskId={},startTime={},cacheKey={},userInputStartMs={},drcCheckpointStartMs={},cacheSyncMs={}", drcInputConfig.getTaskId(), startTime, cacheKey, userInputStartMs, drcCheckpointStartMs,cacheSyncMs);
            
            client.initService(drcInputConfig.getGroupName(), drcInputConfig.getDbName(), drcInputConfig.getIdentification(), startTime,
                    null);
            log.info("Customer initService succeed, waiting for DataMessage..." + drcInputConfig.getStartTime());
            // 下面逻辑是重试逻辑
            new Thread(() -> {
                while (true) {
                    try {
                        Thread t = client.startService();
                        // 当join返回的时候，表示异步通知线程退出了，需要重启
                        t.join();
                        client.resetService();
                    } catch (Exception e) {
                        log.error("drc init failed", e);
                    }
                }
            }).start();
        } catch (Exception e) {
            log.error("drc init failed", e);
        }
    }
    
    private JSONObject getDrcTopic(String username, String password, String taskId, String endpoint, String topic) {
        String url = endpoint + "/api/auth";
        String token = null;
        String data = String.format("taskParam={\"user\":\"%s\",\"password\":\"%s\"}", username, password);
        String resp = doHttpPost(url, data);
        if (StringUtils.isNotBlank(resp)) {
            JSONObject respJson = JSON.parseObject(resp);
            if (respJson != null && respJson.getBoolean("success") && respJson.getJSONObject("data") != null) {
                token = respJson.getJSONObject("data").getString("token");
                
                data = String.format("taskParam={\"taskId\":\"%s\",\"token\":\"%s\"}", taskId, token);
                url = endpoint + "/api/query";
                resp = doHttpPost(url, data);
                if (StringUtils.isNotBlank(resp)) {
                    respJson = JSON.parseObject(resp);
                    if (respJson != null && respJson.getBoolean("success") && respJson.getJSONObject("data") != null) {
                        JSONArray cfgArray =  respJson.getJSONObject("data").getJSONArray("cfg");
                        if (cfgArray != null) {
                            for (int i = 0; i < cfgArray.size(); i++) {
                                JSONObject cfg = cfgArray.getJSONObject(i);
                                if (topic.equalsIgnoreCase(cfg.getString("topic"))) {
                                	return cfg;
                            	}
                            }
                        }
                    }
                }
            }
        }
        return null;
    }
	
	private String doHttpPost(String url, String data) {
        String resp = null;
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(mediaType, data))
                .build();
        OkHttpClient okHttpClient = new OkHttpClient();
        okHttpClient.newBuilder().connectTimeout(60, TimeUnit.SECONDS).readTimeout(60, TimeUnit.SECONDS).writeTimeout(60, TimeUnit.SECONDS).build();
        Response response = null;
        try {
        	log.debug("req:post {} -d'{}'", url, data);
            response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                resp = response.body().string(); 
                log.debug("resp:{}", resp); 
            }
        } catch (IOException e) {
        	log.error("http request failed", e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return resp;
    }

    @Override
    public Integer getProgress() {
        return 0;
    }

    @Override
    public MonotonyIncreaseProgress getMonotonyIncreaseProgress() {
        MonotonyIncreaseProgress monotonyIncreaseProgress = new MonotonyIncreaseProgress();
        monotonyIncreaseProgress.add("", 1L);
        return monotonyIncreaseProgress;
    }

    @Override
    public void seek(Integer idx) throws IOException {
    }

    @Override
    public boolean next() throws IOException, InterruptedException {
        if (interrupted) {
            log.info("DrcSource: received interrupt command, finish this consumer.");
            return false;
        }
        return true;
    }

    @Override
    public String getMessage() {
        try {
            return queue.take();
        } catch (InterruptedException e) {
            log.error("getMessage interrupted", e);
            return null;
        }
    }

    @Override
    public void close() {
        if (client != null) {
            try {
                client.stopService();
            } catch (Exception e) {
                log.error("drc stop service failed", e);
            }
        }
        if (jedisCluster != null) {
            try {
            	jedisCluster.close();
            } catch (Exception e) {
                log.error("jedis close failed", e);
            }
        }
    }

    @Override
    public long getWatermark() {
        return System.currentTimeMillis();
    }

    @Override
    public long getDelay() {
        return 0;
    }

    @Override
    public boolean isHeartBeat() {
        return false;
    }

    @Override
    public void interrupt() {
        interrupted = true;
    }

    @Override
    public long getFetchedDelay() {
        return 0;
    }

	public static DbEventInfo parseDdl(DataMessage.Record record) {
		DbEventInfo dbEventInfo = new DbEventInfo();
		dbEventInfo.setTs(record.getTimestamp());
		String dbName = record.getDbname().toLowerCase();
		
		dbEventInfo.setEventType(DbEvent.INSERT);
		Set<String> fieldSet = new HashSet<String>();
		Set<String> pkFieldSet = new HashSet<String>();
		Map<String, DbEventFieldInfo> fieldData = new HashMap<String, DbEventFieldInfo>();
		for (DataMessage.Record.Field field : record.getFieldList()) {
			String filedName = field.getFieldname().toLowerCase();
			DataMessage.Record.Field.Type type = field.getType();
			
			int eventType = parseDrcType(type);
			DbEventFieldInfo fieldInfo = new DbEventFieldInfo();
			fieldSet.add(filedName);
			fieldInfo.setFieldName(filedName); // 字段名称
			fieldInfo.setFieldType(eventType); // 事件类型
			String fieldValue = null;
			if (null != field.getValue()) {
				fieldValue = parseValue(field, type);
			}
			fieldInfo.setNewValue(fieldValue); // insert 有新值
			fieldInfo.setOldValue(null); // insert 无新值
			fieldData.put(filedName, fieldInfo);
		}
		dbEventInfo.setFieldValues(fieldData);
		dbEventInfo.setDbName(dbName);
		dbEventInfo.setFieldSet(fieldSet);
		dbEventInfo.setPkField(pkFieldSet);
		

		return dbEventInfo;
	}

	public static DbEventInfo parse(DataMessage.Record record, String opType) {
		DbEventInfo dbEventInfo = new DbEventInfo();
		dbEventInfo.setTs(record.getTimestamp());
		String dbName = record.getDbname().toLowerCase();
		String tableName = record.getTablename().toLowerCase();
		if (RowEventType.INSERT.equalsIgnoreCase(opType)) {
			dbEventInfo.setEventType(DbEvent.INSERT);
			Set<String> fieldSet = new HashSet<String>();
			Set<String> pkFieldSet = new HashSet<String>();
			Map<String, DbEventFieldInfo> fieldData = new HashMap<String, DbEventFieldInfo>();
			for (DataMessage.Record.Field field : record.getFieldList()) {
				String filedName = field.getFieldname().toLowerCase();
				DataMessage.Record.Field.Type type = field.getType();
				if (field.isPrimary()) {
					pkFieldSet.add(filedName);
				}
				int eventType = parseDrcType(type);
				DbEventFieldInfo fieldInfo = new DbEventFieldInfo();
				fieldSet.add(filedName);
				fieldInfo.setFieldName(filedName); // 字段名称
				fieldInfo.setFieldType(eventType); // 事件类型
				String fieldValue = null;
				if (null != field.getValue()) {
					fieldValue = parseValue(field, type);
				}
				fieldInfo.setNewValue(fieldValue); // insert 有新值
				fieldInfo.setOldValue(null); // insert 无新值
				fieldData.put(filedName, fieldInfo);
			}
			dbEventInfo.setFieldValues(fieldData);
			dbEventInfo.setDbName(dbName);
			dbEventInfo.setTableName(tableName);
			dbEventInfo.setFieldSet(fieldSet);
			dbEventInfo.setPkField(pkFieldSet);
		}
		if (RowEventType.DELETE.equalsIgnoreCase(opType)) {
			dbEventInfo.setEventType(DbEvent.DELETE);
			Set<String> fieldSet = new HashSet<String>();
			Set<String> pkFieldSet = new HashSet<String>();
			Map<String, DbEventFieldInfo> fieldData = new HashMap<String, DbEventFieldInfo>();
			for (DataMessage.Record.Field field : record.getFieldList()) {
				String filedName = field.getFieldname().toLowerCase();
				DataMessage.Record.Field.Type type = field.getType();
				if (field.isPrimary()) {
					pkFieldSet.add(filedName);
				}
				int eventType = parseDrcType(type);
				DbEventFieldInfo fieldInfo = new DbEventFieldInfo();
				fieldSet.add(filedName);
				fieldInfo.setFieldName(filedName); // 字段名称
				fieldInfo.setFieldType(eventType); // 事件类型
				String fieldValue = null;
				if (null != field.getValue()) {
					fieldValue = parseValue(field, type);
				}
				fieldInfo.setOldValue(fieldValue); // delete 有旧值
				fieldInfo.setNewValue(null); // delete 无新值
				fieldData.put(filedName, fieldInfo);
			}
			dbEventInfo.setFieldValues(fieldData);
			dbEventInfo.setDbName(dbName);
			dbEventInfo.setTableName(tableName);
			dbEventInfo.setFieldSet(fieldSet);
			dbEventInfo.setPkField(pkFieldSet);
		}
		if (RowEventType.UPDATE.equalsIgnoreCase(opType)) {
			dbEventInfo.setEventType(DbEvent.UPDATE);
			Set<String> fieldSet = new HashSet<String>();
			Set<String> pkFieldSet = new HashSet<String>();
			Map<String, DbEventFieldInfo> fieldData = new HashMap<String, DbEventFieldInfo>();
			List<Field> fields = record.getFieldList();
			for (int i = 0; i < fields.size(); i += 2) {
				Field field = fields.get(i);
				String filedName = field.getFieldname().toLowerCase();
				DataMessage.Record.Field.Type type = field.getType();
				if (field.isPrimary()) {
					pkFieldSet.add(filedName);
				}
				int eventType = parseDrcType(type);
				DbEventFieldInfo fieldInfo = new DbEventFieldInfo();
				fieldSet.add(filedName);
				fieldInfo.setFieldName(filedName); // 字段名称
				fieldInfo.setFieldType(eventType); // 事件类型
				String fieldValueOld = null;
				if (null != field.getValue()) {
					fieldValueOld = parseValue(field, type);
				}
				
				Field fieldNew = fields.get(i+1);
				String fieldValueNew = null;
				if (null != fieldNew.getValue()) {
					fieldValueNew = parseValue(fieldNew, type);
				}
				fieldInfo.setOldValue(fieldValueOld); // update 有新值
				fieldInfo.setNewValue(fieldValueNew); // new 有新值
				fieldData.put(filedName, fieldInfo);
			}
			dbEventInfo.setFieldValues(fieldData);
			dbEventInfo.setDbName(dbName);
			dbEventInfo.setTableName(tableName);
			dbEventInfo.setFieldSet(fieldSet);
			dbEventInfo.setPkField(pkFieldSet);
		}

		return dbEventInfo;
	}
	
	private static String parseValue(DataMessage.Record.Field field, DataMessage.Record.Field.Type type) {
		try {
			String strValue = field.getValue().toString("utf-8");
			if (isDate(type) && strValue.matches("^\\d+$")) {
//				strValue = new Timestamp(Long.parseLong(strValue) * 1000).toString();
				DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				strValue = dateFormat.format(new Timestamp(Long.parseLong(strValue) * 1000));
			}
			return strValue;
		} catch (Exception e) {
			return null;
		}
	}

	private static boolean isDate(DataMessage.Record.Field.Type type) {
		if (type.equals(DataMessage.Record.Field.Type.DATE) || type.equals(DataMessage.Record.Field.Type.DATETIME)
				|| type.equals(DataMessage.Record.Field.Type.TIMESTAMP)) {
			return true;
		}
		return false;
	}
	
	private static int parseDrcType(DataMessage.Record.Field.Type type) {
		switch (type) {
		case INT8:
			return FieldType.TYPE_INTEGER;
		case INT16:
			return FieldType.TYPE_INTEGER;
		case INT24:
			return FieldType.TYPE_INTEGER;
		case INT32:
			return FieldType.TYPE_INTEGER;
		case INT64:
			return FieldType.TYPE_LONG;
		case DECIMAL:
			return FieldType.TYPE_DOUBLE;
		case FLOAT:
			return FieldType.TYPE_DOUBLE;
		case DOUBLE:
			return FieldType.TYPE_DOUBLE;
		case NULL:
			return FieldType.TYPE_NOT_SUPPORT;
		case TIMESTAMP:
			return FieldType.TYPE_DATE;
		case DATE:
			return FieldType.TYPE_DATE;
		case TIME:
			return FieldType.TYPE_DATE;
		case DATETIME:
			return FieldType.TYPE_DATE;
		case YEAR:
			return FieldType.TYPE_DATE;
		case BIT:
			return FieldType.TYPE_NOT_SUPPORT;
		case ENUM:
			return FieldType.TYPE_NOT_SUPPORT;
		case SET:
			return FieldType.TYPE_NOT_SUPPORT;
		case BLOB:
			return FieldType.TYPE_NOT_SUPPORT;
		case GEOMETRY:
			return FieldType.TYPE_NOT_SUPPORT;
		case STRING:
			return FieldType.TYPE_STRING;
		case UNKOWN:
			return FieldType.TYPE_NOT_SUPPORT;
		default:
			return FieldType.TYPE_NOT_SUPPORT;
		}
	}
}
