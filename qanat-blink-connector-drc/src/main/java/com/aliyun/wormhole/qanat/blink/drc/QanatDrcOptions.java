package com.aliyun.wormhole.qanat.blink.drc;

import java.util.Arrays;
import java.util.List;

import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.configuration.ConfigOptions;

public class QanatDrcOptions {

    public static final ConfigOption<String> URL = ConfigOptions.key("url".toLowerCase()).noDefaultValue();
    public static final ConfigOption<String> GROUPNAME = ConfigOptions.key("groupName".toLowerCase()).noDefaultValue();
    public static final ConfigOption<String> DBNAME = ConfigOptions.key("dbName".toLowerCase()).noDefaultValue();
    public static final ConfigOption<String> IDENTIFICATION = ConfigOptions.key("identification".toLowerCase()).noDefaultValue();
    public static final ConfigOption<String> FILTER = ConfigOptions.key("filter".toLowerCase()).noDefaultValue();
    public static final ConfigOption<String> STARTTIME = ConfigOptions.key("startTime".toLowerCase()).noDefaultValue();
    public static final ConfigOption<String> TASKID = ConfigOptions.key("taskId".toLowerCase()).noDefaultValue();
    public static final ConfigOption<String> ENDPOINT = ConfigOptions.key("endpoint".toLowerCase()).noDefaultValue();
    public static final ConfigOption<String> DATAMODEL = ConfigOptions.key("dataModel".toLowerCase()).defaultValue("datatube");
    public static final ConfigOption<String> CONSUMEID = ConfigOptions.key("consumeId".toLowerCase()).noDefaultValue();
    public static final ConfigOption<String> CONSUMEMODE = ConfigOptions.key("consumeMode".toLowerCase()).defaultValue("cache");
    public static final ConfigOption<String> CACHEHOST = ConfigOptions.key("cacheHost".toLowerCase()).defaultValue("r-8vbu85tnqjhflorws4.redis.zhangbei.rds.aliyuncs.com");
    public static final ConfigOption<Integer> CACHEPORT = ConfigOptions.key("cachePort".toLowerCase()).defaultValue(6379);
    
    public static final String PARAMS_HELP_MSG = String.format(
        "required params:%s,%s,%s,%s\n",
        GROUPNAME,
        TASKID,
        IDENTIFICATION,
        CONSUMEID);

    public static final List<String> SUPPORTED_KEYS = Arrays.asList(
        URL.key(),
        GROUPNAME.key(),
        DBNAME.key(),
        IDENTIFICATION.key(),
        FILTER.key(),
        TASKID.key(),
        STARTTIME.key());
}