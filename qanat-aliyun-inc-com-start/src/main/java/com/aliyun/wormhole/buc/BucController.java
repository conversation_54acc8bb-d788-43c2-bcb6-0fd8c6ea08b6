package com.aliyun.wormhole.buc;

import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.buc.sso.client.util.SimpleUserUtil;
import com.alibaba.buc.sso.client.vo.BucSSOUser;

/**
 * buc登陆用户ID
 *
 * <AUTHOR>
 */
@Controller
public class BucController {

    @RequestMapping(value = "/buc/user")
    public @ResponseBody String getUser(HttpServletRequest request) {
        try {
            BucSSOUser user = SimpleUserUtil.getBucSSOUser(request);
            return "EmpId : " + user.getEmpId();
        } catch (Exception e) {
            return "fail!";
        }
    }
}
