package com.aliyun.wormhole;

public class ControllerResult<T> {

	/**
	 * 请求返回code
	 */
	private Integer code;

	/**
	 * 请求返回错误信息
	 */
	private String message;
	
	/**
	 * 返回结果
	 */
	private T data;

	public ControllerResult() {
	}

	public ControllerResult(Integer code, String message) {
		this.code = code;
		this.message = message;
	}

	public ControllerResult(Integer code, String message, T data) {
		this.code = code;
		this.message = message;
		this.data = data;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}

}
