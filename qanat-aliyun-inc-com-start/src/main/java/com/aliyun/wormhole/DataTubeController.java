package com.aliyun.wormhole;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.dto.CreateOdsRequest;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.DatasourceRequest;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.DatatubeManagementService;
import com.aliyun.wormhole.qanat.api.service.DrcService;
import com.aliyun.wormhole.qanat.api.service.RtdwTaskService;
import com.aliyun.wormhole.qanat.api.service.RtdwViewModelTaskService;
import com.aliyun.wormhole.qanat.api.service.TaskService;
import com.aliyun.wormhole.qanat.api.service.ViewModelRequest;
import com.aliyun.wormhole.qanat.service.metaq.MetaqService;
import com.aliyun.wormhole.qanat.service.odps.OdpsService;
import com.aliyun.wormhole.qanat.service.schedulerx.SchedulerXJobService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Controller
public class DataTubeController {
	
	@Resource
	private RtdwViewModelTaskService vmTaskService;
	
	@Resource
	private TaskService taskService;
	
	@Resource
	private DatasourceService dsInfoService;
	
	@Resource
	private RtdwTaskService odsTaskService;
	
	@Resource
	private DrcService drcService;
	
	@Resource
	private MetaqService metaqService;
	
	@Resource
	private SchedulerXJobService schedulerXJobService;
	
	@Resource
	private BlinkService blinkService;
	
	@Resource
	private OdpsService odpsService;
	
	@Resource
	private DatatubeManagementService datatubeManagementService;

	@RequestMapping(value = "/api/createViewModelFromObject")
    public @ResponseBody String createViewModelFromObject(@RequestBody String requestJson) {
		log.info("createViewModelFromYaml({})", requestJson);
		try {
			JSONObject json = JSON.parseObject(requestJson);
			DataResult<Long> result = vmTaskService.createViewModelFromObject(json.getString("tenantId"), json.getString("appName"), json.getString("objectType"), json.getString("objectUniqueCode"), json.getString("operateEmpid"), json.getString("objectMsg"));
			return JSON.toJSONString(result);
		} catch(Exception e) {
			log.error("createViewModelFromYaml failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/reflectObjectFieldChange")
    public @ResponseBody String reflectObjectFieldChange(@RequestBody String requestJson) {
		log.info("createViewModelFromYaml({})", requestJson);
		try {
			JSONObject json = JSON.parseObject(requestJson);
			DataResult<Boolean> result = vmTaskService.reflectObjectFieldChange(json.getString("tenantId"), json.getString("dsUniqueName"), json.getString("fieldName"), json.getInteger("isRef"), json.getString("operateType"), json.getString("tagJson"));
			return JSON.toJSONString(result);
		} catch(Exception e) {
			log.error("createViewModelFromYaml failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/createViewModelFromYaml")
    public @ResponseBody String createViewModelFromYaml(@RequestBody String requestJson) {
		log.info("createViewModelFromYaml({})", requestJson);
		try {
			DataResult<Long> result = vmTaskService.createViewModelFromYaml(JSON.parseObject(requestJson, ViewModelRequest.class));
			return JSON.toJSONString(result);
		} catch(Exception e) {
			log.error("createViewModelFromYaml failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/modifyViewModel")
    public @ResponseBody String modifyViewModel(@RequestBody String requestJson) {
		log.info("modifyViewModel({})", requestJson);
		try {
			DataResult<Long> result = vmTaskService.modifyViewModel(JSON.parseObject(requestJson, ViewModelRequest.class));
			return JSON.toJSONString(result);
		} catch(Exception e) {
			log.error("modifyViewModel failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/restartModelTask")
    public @ResponseBody String restartModelTask(@RequestBody String requestJson) {
		log.info("restartModelTask({})", requestJson);
		try {
			JSONObject json = JSON.parseObject(requestJson);
			DataResult<Long> result = vmTaskService.restartModelTask(json.getString("tenantId"), json.getLong("viewModelId"), json.getString("operateEmpid"));
			return JSON.toJSONString(result);
		} catch(Exception e) {
			log.error("restartModelTask failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/createTableAndFullSync")
    public @ResponseBody String createTableAndFullSync(@RequestBody String requestJson) {
		log.info("createTableAndFullSync({})", requestJson);
		try {
			JSONObject json = JSON.parseObject(requestJson);
			DataResult<Boolean> result = vmTaskService.createTableAndFullSync(json.getString("tenantId"), json.getLong("viewModelId"));
			return JSON.toJSONString(result);
		} catch(Exception e) {
			log.error("createTableAndFullSync failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/createBatchStreamTasks")
    public @ResponseBody String createBatchStreamTasks(@RequestBody String requestJson) {
		log.info("createSyncTask4Adb3({})", requestJson);
		try {
			JSONObject json = JSON.parseObject(requestJson);
			DataResult<Long> result = vmTaskService.createBatchStreamTasks(json.getString("tenantId"), json.getLong("viewModelId"), json.getString("operateEmpid"));
			return JSON.toJSONString(result);
		} catch(Exception e) {
			log.error("createSyncTask4Adb3 failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/runTask")
    public @ResponseBody String runTask(@RequestBody String requestJson) {
		log.info("runTask({})", requestJson);
		try {
			JSONObject json = JSON.parseObject(requestJson);
			DataResult<Long> result = taskService.runTask(json.getString("tenantId"), json.getString("operateEmpid"), json.getLong("taskId"));
			return JSON.toJSONString(result);
		} catch(Exception e) {
			log.error("runTask failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/stopTask")
    public @ResponseBody String stopTask(@RequestBody String requestJson) {
		log.info("stopTask({})", requestJson);
		try {
			JSONObject json = JSON.parseObject(requestJson);
			DataResult<Boolean> result = taskService.stopTask(json.getString("tenantId"), json.getString("operateEmpid"), json.getLong("taskId"));
			return JSON.toJSONString(result);
		} catch(Exception e) {
			log.error("stopTask failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/deleteScheduleTask")
    public @ResponseBody String deleteScheduleTask(@RequestBody String requestJson) {
		log.info("deleteTask({})", requestJson);
		try {
			JSONObject json = JSON.parseObject(requestJson);
			DataResult<Boolean> result = taskService.deleteScheduleTask(json.getString("tenantId"), json.getString("operateEmpid"), json.getLong("taskId"));
			return JSON.toJSONString(result);
		} catch(Exception e) {
			log.error("deleteTask failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/createDatasource")
    public @ResponseBody String createDatasource(@RequestBody String requestJson) {
		log.info("createDatasource({})", requestJson);
		try {
			DataResult<Long> result = dsInfoService.createDatasource(JSON.parseObject(requestJson, DatasourceRequest.class));
			return JSON.toJSONString(result);
		} catch(Exception e) {
			log.error("createDatasource failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/modifyDatasource")
    public @ResponseBody String modifyDatasource(@RequestBody String requestJson) {
		log.info("modifyDatasource({})", requestJson);
		try {
			DataResult<Boolean> result = dsInfoService.modifyDatasource(JSON.parseObject(requestJson, DatasourceRequest.class));
			return JSON.toJSONString(result);
		} catch(Exception e) {
			log.error("modifyDatasource failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/createDrcTaskForDs")
    public @ResponseBody String createDrcTaskForDs(@RequestBody String requestJson) {
		log.info("createDrcTaskForDs({})", requestJson);
		try {
			JSONObject json = JSON.parseObject(requestJson);
			DataResult<Map<String, Long>> result = drcService.createDrcTaskForDs(json.getString("tenantId"), json.getString("appName"), json.getString("dsName"), json.getString("empid"));
			return JSON.toJSONString(result);
		} catch(Exception e) {
			log.error("createDrcTaskForDs failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/send")
    public @ResponseBody String send(@RequestBody String requestJson) {
		log.info("send({})", requestJson);
		try {
			JSONObject json = JSON.parseObject(requestJson);
			metaqService.send(json.getString("requestId"), json.getString("topic"), json.getString("tag"), json.getString("key"), json.getString("msg"));
			return "SUCCESS";
		} catch(Exception e) {
			log.error("send failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/deleteSchedulerx2Job")
    public @ResponseBody String deleteSchedulerx2Job(@RequestBody String requestJson) {
		log.info("deleteSchedulerx2Job({})", requestJson);
		try {
			JSONObject json = JSON.parseObject(requestJson);
			DataResult<Boolean> result = schedulerXJobService.deleteJob(json.getString("tenantId"), json.getString("appName"), json.getLong("jobId"));
			return JSON.toJSONString(result);
		} catch(Exception e) {
			log.error("deleteSchedulerx2Job failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/getProjectCUs")
    public @ResponseBody List<Map<String, Object>> getProjectCUs(@RequestBody String requestJson) {
		log.info("deleteSchedulerx2Job({})", requestJson);
		try {
			JSONObject json = JSON.parseObject(requestJson);
			return blinkService.getProjectCUs(json.getString("tenantId"), json.getString("appName"));
		} catch(Exception e) {
			log.error("deleteSchedulerx2Job failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/listProjectBindQueue")
    public @ResponseBody String listProjectBindQueue(@RequestBody String requestJson) {
		log.info("deleteSchedulerx2Job({})", requestJson);
		try {
			JSONObject json = JSON.parseObject(requestJson);
			return blinkService.listProjectBindQueue(json.getString("tenantId"), json.getString("appName"));
		} catch(Exception e) {
			log.error("deleteSchedulerx2Job failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/createDsInfoAndOdsTask")
    public @ResponseBody String createDsInfoAndOdsTask(@RequestBody String requestJson) {
		log.info("createDsInfoAndOdsTask({})", requestJson);
		try {
			DataResult<Map<String, Long>> result = odsTaskService.createDsInfoAndOdsTask(JSON.parseObject(requestJson, CreateOdsRequest.class));
			return JSON.toJSONString(result);
		} catch(Exception e) {
			log.error("createDsInfoAndOdsTask failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/reflectOdpsMetaEvent")
    public @ResponseBody String reflectOdpsMetaEvent(@RequestBody String requestXml) {
		log.info("reflectOdpsMetaEvent({})", requestXml);
		try {
			return JSON.toJSONString(odpsService.reflectOdpsMetaEvent(requestXml));
		} catch(Exception e) {
			log.error("reflectOdpsMetaEvent failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/registerOdpsMetaEvent")
    public @ResponseBody String registerOdpsMetaEvent(@RequestBody String requestJson) {
		log.info("registerOdpsMetaEvent({})", requestJson);
		try {
			JSONObject json = JSON.parseObject(requestJson);
			return JSON.toJSONString(odpsService.registerOdpsMetaEvent(json.getString("tenantId"), json.getString("dsName")));
		} catch(Exception e) {
			log.error("registerOdpsMetaEvent failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/unregisterOdpsMetaEvent")
    public @ResponseBody String unregisterOdpsMetaEvent(@RequestBody String requestJson) {
		log.info("unregisterOdpsMetaEvent({})", requestJson);
		try {
			JSONObject json = JSON.parseObject(requestJson);
			return JSON.toJSONString(odpsService.unregisterOdpsMetaEvent(json.getString("tenantId"), json.getString("dsName")));
		} catch(Exception e) {
			log.error("unregisterOdpsMetaEvent failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/pauseAllDrcTasks")
    public @ResponseBody String pauseAllDrcTasks(@RequestBody String requestJson) {
		log.info("pauseAllDrcTasks({})", requestJson);
		try {
			datatubeManagementService.pauseAllDrcTasks();
		} catch(Exception e) {
			log.error("pauseAllDrcTasks failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/resumeAllDrcTasks")
    public @ResponseBody String resumeAllDrcTasks(@RequestBody String requestJson) {
		log.info("resumeAllDrcTasks({})", requestJson);
		try {
			datatubeManagementService.resumeAllDrcTasks();
		} catch(Exception e) {
			log.error("resumeAllDrcTasks failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/pauseAllTasks")
    public @ResponseBody String pauseAllTasks(@RequestBody String requestJson) {
		log.info("pauseAllTasks({})", requestJson);
		try {
			datatubeManagementService.pauseAllTasks();
		} catch(Exception e) {
			log.error("pauseAllTasks failed, error={}", e.getMessage(), e);
		}
        return null;
    }

	@RequestMapping(value = "/api/resumeAllTasks")
    public @ResponseBody String resumeAllTasks(@RequestBody String requestJson) {
		log.info("resumeAllTasks({})", requestJson);
		try {
			datatubeManagementService.resumeAllTasks();
		} catch(Exception e) {
			log.error("resumeAllTasks failed, error={}", e.getMessage(), e);
		}
        return null;
    }
}