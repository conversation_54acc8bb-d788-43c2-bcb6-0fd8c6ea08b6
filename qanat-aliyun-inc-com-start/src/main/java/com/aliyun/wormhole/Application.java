package com.aliyun.wormhole;

import com.taobao.ateye.servlet.AteyeServlet;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ImportResource;
import com.taobao.pandora.boot.PandoraBootstrap;

/**
 * Pandora Boot应用的入口类
 * <p>
 * 详情见http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-diamond
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.aliyun.wormhole"})
@ImportResource(value = { "classpath:spring-bpms.xml", "classpath:persistent.spring.xml"})
public class Application {

    public static void main(String[] args) {
        PandoraBootstrap.run(args);
        SpringApplication.run(Application.class, args);
        PandoraBootstrap.markStartupAndWait();
    }

//    @Bean
//    public ServletRegistrationBean ateyeServletRegistrationBean() {
//        AteyeServlet ateyeServlet = new AteyeServlet();
//        ServletRegistrationBean servletRegistrationBean = new ServletRegistrationBean(ateyeServlet,
//                "/agent.ateye");// ServletName默认值为首字母小写，即myServlet
//        servletRegistrationBean.setLoadOnStartup(1);
//        servletRegistrationBean.addInitParameter("app", "qanat-aliyun-inc-com");
//        servletRegistrationBean.addInitParameter("isRecordErrorLogger", "true");
//        servletRegistrationBean.addInitParameter("kvTime", "60");
//        return servletRegistrationBean;
//    }
}
