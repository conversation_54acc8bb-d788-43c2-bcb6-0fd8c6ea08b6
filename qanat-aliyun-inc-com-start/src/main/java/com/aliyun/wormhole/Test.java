package com.aliyun.wormhole;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.schedulerx.shade.com.google.common.collect.Lists;
import com.alibaba.schedulerx.shade.com.google.gson.Gson;

import java.util.List;

public class Test {
    public static void main(String []args){
        String body = "{\"body\":\"eyJkYk5hbWUiOiJhbGl5dW5fc2FsZXNfY29tYiIsImV2ZW50VHlwZSI6MSwiZmllbGRTZXQiOlsiZ210X2NyZWF0ZSIsImVtcGlkIiwib3BwX2lkIiwic2FsZXNfdHlwZSIsImJyZWFrX2ludmVudG9yeV9vcHBfcmVsYXRpb25faWQiLCJ0b19lbXBpZCIsImZyb21fZW1waWQiLCJnbXRfbW9kaWZpZWQiLCJyZWNvbW1lbmRfcmVhc29uIiwic2hhcmluZ19jb2VmZmljaWVudCIsImNyZWF0ZWRfZW1waWQiLCJsYXN0X21vZGlmaWVkX2VtcGlkIiwiYnJlYWtfaW52ZW50b3J5IiwibmFtZSIsIm9wcF9yZWxhdGVkX3JlYXNvbiIsIm93bmVyX3N0YXR1cyIsImlkIiwiY2F0ZWdvcnkiLCJjaWQiXSwiZmllbGRWYWx1ZXMiOnsiZ210X2NyZWF0ZSI6eyJmaWVsZE5hbWUiOiJnbXRfY3JlYXRlIiwiZmllbGRUeXBlIjo0LCJuZXdWYWx1ZSI6IjIwMjEtMDEtMTMgMTM6NDY6NTEifSwiZW1waWQiOnsiZmllbGROYW1lIjoiZW1waWQiLCJmaWVsZFR5cGUiOjUsIm5ld1ZhbHVlIjoiV0I0Mzk1NzIifSwib3BwX2lkIjp7ImZpZWxkTmFtZSI6Im9wcF9pZCIsImZpZWxkVHlwZSI6MiwibmV3VmFsdWUiOiI1NTM5MTExNCJ9LCJzYWxlc190eXBlIjp7ImZpZWxkTmFtZSI6InNhbGVzX3R5cGUiLCJmaWVsZFR5cGUiOjEsIm5ld1ZhbHVlIjoiMCJ9LCJicmVha19pbnZlbnRvcnlfb3BwX3JlbGF0aW9uX2lkIjp7ImZpZWxkTmFtZSI6ImJyZWFrX2ludmVudG9yeV9vcHBfcmVsYXRpb25faWQiLCJmaWVsZFR5cGUiOjJ9LCJ0b19lbXBpZCI6eyJmaWVsZE5hbWUiOiJ0b19lbXBpZCIsImZpZWxkVHlwZSI6NSwibmV3VmFsdWUiOiJXQjQzOTU3MiJ9LCJmcm9tX2VtcGlkIjp7ImZpZWxkTmFtZSI6ImZyb21fZW1waWQiLCJmaWVsZFR5cGUiOjV9LCJnbXRfbW9kaWZpZWQiOnsiZmllbGROYW1lIjoiZ210X21vZGlmaWVkIiwiZmllbGRUeXBlIjo0LCJuZXdWYWx1ZSI6IjIwMjEtMDEtMTMgMTM6NDY6NTEifSwicmVjb21tZW5kX3JlYXNvbiI6eyJmaWVsZE5hbWUiOiJyZWNvbW1lbmRfcmVhc29uIiwiZmllbGRUeXBlIjo1LCJuZXdWYWx1ZSI6IuaOqOmAgeWwj+S6jO+8muW8oOW5uCB3YjYyNTk4NSBcbuWbnueUteWPt+eggTogQ0lEOjM4NjA1Njc1LzEzNzA2NTAyOTkwXG7lrqLmiLfkuJrliqHlnLrmma865YW25LuWXG7pnIDmsYLkuqflk4HvvJrln5/lkI3lm57otK1cbuWSqOivoumXrumimDoifSwic2hhcmluZ19jb2VmZmljaWVudCI6eyJmaWVsZE5hbWUiOiJzaGFyaW5nX2NvZWZmaWNpZW50IiwiZmllbGRUeXBlIjoxfSwiY3JlYXRlZF9lbXBpZCI6eyJmaWVsZE5hbWUiOiJjcmVhdGVkX2VtcGlkIiwiZmllbGRUeXBlIjo1LCJuZXdWYWx1ZSI6InN5c3RlbSJ9LCJsYXN0X21vZGlmaWVkX2VtcGlkIjp7ImZpZWxkTmFtZSI6Imxhc3RfbW9kaWZpZWRfZW1waWQiLCJmaWVsZFR5cGUiOjUsIm5ld1ZhbHVlIjoic3lzdGVtIn0sImJyZWFrX2ludmVudG9yeSI6eyJmaWVsZE5hbWUiOiJicmVha19pbnZlbnRvcnkiLCJmaWVsZFR5cGUiOjEsIm5ld1ZhbHVlIjoiMCJ9LCJuYW1lIjp7ImZpZWxkTmFtZSI6Im5hbWUiLCJmaWVsZFR5cGUiOjUsIm5ld1ZhbHVlIjoi6aG55L2z6Z+zIn0sIm9wcF9yZWxhdGVkX3JlYXNvbiI6eyJmaWVsZE5hbWUiOiJvcHBfcmVsYXRlZF9yZWFzb24iLCJmaWVsZFR5cGUiOjUsIm5ld1ZhbHVlIjoiW0FdLUluY2FsbOi9rE9i57q/57Si562W55WlIn0sIm93bmVyX3N0YXR1cyI6eyJmaWVsZE5hbWUiOiJvd25lcl9zdGF0dXMiLCJmaWVsZFR5cGUiOjEsIm5ld1ZhbHVlIjoiNCJ9LCJpZCI6eyJmaWVsZE5hbWUiOiJpZCIsImZpZWxkVHlwZSI6MiwibmV3VmFsdWUiOiI0NjYwMTA3NiJ9LCJjYXRlZ29yeSI6eyJmaWVsZE5hbWUiOiJjYXRlZ29yeSIsImZpZWxkVHlwZSI6NSwibmV3VmFsdWUiOiJ1cHNhbGVzIn0sImNpZCI6eyJmaWVsZE5hbWUiOiJjaWQiLCJmaWVsZFR5cGUiOjIsIm5ld1ZhbHVlIjoiMzg2MDU2NzUifX0sInBrRmllbGQiOlsiaWQiXSwidGFibGVOYW1lIjoiY29tYl9vcHBvcnR1bml0eV9zYWxlc19yZWxhdGlvbiIsInRzIjoiMTYxMDUxNjgxMSJ9LDBiOGJmZGViMTYxMDUxNjgxMTY5NzM3ODdkYTc4OQ==\",\"bodyCRC\":1935817589,\"bornHost\":{\"address\":\"**************\",\"port\":50810},\"bornHostBytes\":{\"array\":\"C4v5ogAAxno=\",\"limit\":8,\"position\":0},\"bornHostNameString\":\"**************\",\"bornHostString\":\"**************\",\"bornTimestamp\":1610516812292,\"commitLogOffset\":14183596680573,\"delayTimeLevel\":0,\"flag\":0,\"msgId\":\"0B8BF9A28757070DEA4E40C15603007F\",\"offsetMsgId\":\"0B17000D00002A9F00000CE6603DE57D\",\"preparedTransactionOffset\":0,\"properties\":{\"MIN_OFFSET\":\"0\",\"TRACE_ON\":\"true\",\"eagleTraceId\":\"0b8bf9a216105168122911128d9cf7\",\"MAX_OFFSET\":\"59\",\"MSG_REGION\":\"DefaultRegion\",\"CONSUME_START_TIME\":\"1610516812294\",\"UNIQ_KEY\":\"0B8BF9A28757070DEA4E40C15603007F\",\"WAIT\":\"true\",\"TAGS\":\"cid_sales\",\"eagleRpcId\":\"9.1\"},\"queueId\":3,\"queueOffset\":58,\"reconsumeTimes\":0,\"storeHost\":{\"address\":\"**********\",\"port\":10911},\"storeHostBytes\":{\"array\":\"CxcADQAAKp8=\",\"limit\":8,\"position\":0},\"storeSize\":2329,\"storeTimestamp\":1610516812292,\"sysFlag\":0,\"tags\":\"cid_sales\",\"topic\":\"qanat_check_agg_sink\",\"version\":\"MESSAGE_VERSION_V1\",\"waitStoreMsgOK\":true}";
        MessageExt messageExt = JSONObject.parseObject(body, MessageExt.class);
        Gson gson = new Gson();
        System.out.println(gson.fromJson(new String(messageExt.getBody()), JSONObject.class).toJSONString());

    }
    public static String update(String tableName, List<String> keyList, List<String> valueList, String conditionKey, String conditionValue){
        StringBuilder sb = new StringBuilder();
        sb.append("UPDATE "+tableName+" SET ");
        for(int i=0;i<keyList.size();i++){
            if(!conditionKey.equals(keyList.get(i))){
                sb.append(keyList.get(i));
                sb.append("=");
                sb.append("'"+valueList.get(i)+"'");
                if(i!=keyList.size()-1){
                    sb.append(",");
                }
            }

        }
        sb.append(" WHERE conditionKey = '"+conditionValue+"'");
        return sb.toString();
    }
}
