project.name=qanat-aliyun-inc-com
management.security.enabled = false

# http服务器端口
server.port=7001
# endpoint配置
management.port=7002

# hsf配置，详见 http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-hsf
spring.hsf.group=HSF
spring.hsf.version=1.0.0.DAILY
spring.hsf.timeout=20000

# alimonitor配置，详见 http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-alimonitor
spring.alimonitor.method-patterns[0]=com.aliyun.wormhole.qanat.service.*
spring.alimonitor.method-patterns[1]=com.aliyun.wormhole.qanat.dal.*
spring.alimonitor.excluded-suffixes=gif,css,js,ico,do

# eagleeye配置，详见 http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-eagleeye
spring.eagleeye.enabled=true
spring.eagleeye.mdc-updater=slf4j
# acl配置，详见 http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-acl
spring.acl.access-key=BearAdvice2-&668Y8@8&AI@aQfmAn

# 从2017-11-release 版本后在buc服务器上配置，本地无效，参考buc本身文档
spring.buc.client-key=6940e739-9797-43ff-8da5-c993b82e45ee
spring.buc.app-code=e7919b83e2ac4e3bb9bfb7f608231759
spring.buc.filter.url-patterns=/*
spring.buc.sso-server-url=https://login-test.alibaba-inc.com
# 在2017-11-release 版本之后，loginEnv参数是必需的，日常环境配置为daily，线上环境配置为online。参考buc本身的文档
spring.buc.loginEnv=daily
spring.buc.exclusions=/checkpreload.htm

# Velocity配置，详见 http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-velocity
spring.velocity.resource-loader-path=classpath:/velocity/templates
spring.velocity.toolbox-config-location=/velocity/toolbox.xml
spring.velocity.layout-url=/velocity/layout/default.vm
spring.velocity.tools-base-packages=com.aliyun.wormhole.velocity
server.error.whitelabel.enabled=false

# Spring Security CSRF配置，详见 https://lark.alipay.com/sp/securityjar/csrf-token-validation
# 支持HTTP 请求方法，多值使用逗号分隔，默认值:"POST"
spring.security.csrf.supportedMethods = PUT
# 校验请求URL模式风格，可用值:"ant"和"regex"，默认值为:"ant"
spring.security.csrf.url.style = regex
spring.security.csrf.url.included = /.*?
spring.security.csrf.url.excluded = ^/csrf/nocheck

# Spring Security XSS配置，详见 http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-security-xss
spring.security.xss.enabled=true
spring.security.xss.ignored.files=security/xss/ignored.vm
spring.security.xss.ignored.context.names=ignoredName

# Spring Security HTTP 安全域名列表，详见 http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-security-http
spring.security.http.safe.domains=\
  *.alibaba-inc.com,\
  .alibaba-inc.net,\
  localhost
spring.security.filters.redirect-validation.enabled=true
spring.security.filters.referer-validation.enabled=true
spring.security.filters.referer-validation.url.style=regex
spring.security.filters.referer-validation.urls=^/jsonp/.*?
spring.security.jsonp.enabled=false

spring.diamond.data-id=com.aliyun.wormhole:qanat-aliyun-inc-com.properties
#mybatis
spring.mybatis.config=classpath:/mybatis-config.xml
spring.mybatis.scan-base-package=com.aliyun.wormhole.qanat.dal.mapper

#mysql
spring.datasource.url=*************************************************************************************************************************
spring.datasource.username=alyqanat
spring.datasource.password=alyqanat
spring.datasource.driver-class-name=com.mysql.jdbc.Driver

#ScheduleX2.0
spring.schedulerx2.groupId=qanat-aliyun-inc-com.defaultGroup
spring.schedulerx2.appKey=kNWLXQ1sezlgRjKiJOwpDA==
spring.schedulerx2.domainName=schedulerx2.taobao.net
#spring.schedulerx2.groupId=qanat-aliyun-inc-com.devGroup
#spring.schedulerx2.appKey=OXCu032xaggt3yaPACk23w==

#BPMS
qanat.bpms.appKey=qanat
qanat.bpms.authKey=1$qanat$hello1234
qanat.bpms.service.version=1.1.5.daily_aone_test

#ACL
spring.acl.hsf-version=2.0.0.daily
spring.acl.access-key=qanat-6dixaYM18vgtTFNmOdFvQVBz

#BUC
spring.buc.app-name=qanat-aliyun-inc-com
spring.buc.app-code=qanat-6dixaYM18vgtTFNmOdFvQVBz
spring.buc.filter.url-patterns=/*
spring.buc.login-env=daily

#Keycenter
spring.keycenter.http-service-address=http://daily.keycenter.alibaba.net/keycenter
spring.keycenter.app-publish-num=8b1e44629f864538903bd7f9f0a67016
spring.keycenter.key-name=qanat-admin

# environment
environment.type=${environment.type}
region.type=${region.type}

# OXS区域数据库配置
qanat.db.oxs.enabled=${OXS_ENABLED:false}

aone.id=107771
env.unit=center
qanat.vpc.endpoint=http://pre-qanat-sgp-vip.aliyun-inc.com
qanat.object.appname=beiming_xobject
qanat.default.dw=devata_rtdw
qanat.unit.id=1

#MDP
mdp.api.domainCode=qanat_api
mdp.api.domainAk=53f7c5e8-9175-45d3-b70f-a050db39aed1

# ODPS callback
qanat.odps.callback=http://pre-qanat.aliyun-inc.com/api/reflectOdpsMetaEvent

#Core version
datatube.codegen.version=20220811_13662967_datatube2.22_1

#OLAP
olap.api.ak=eba4bea8-d9db-4ccb-8e6f-ca3447f4c468