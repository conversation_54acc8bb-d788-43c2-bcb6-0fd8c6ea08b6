<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- https://github.com/spring-projects/spring-boot/blob/v1.5.13.RELEASE/spring-boot/src/main/resources/org/springframework/boot/logging/logback/defaults.xml -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <include resource="com/alibaba/boot/logback/alimonitor.xml"/>

    <property name="APP_NAME" value="qanat-aliyun-inc-com" />
    <property name="LOG_PATH" value="${user.home}/${APP_NAME}/logs" />
    <property name="LOG_FILE" value="${LOG_PATH}/application.log" />
    <property name="RDS_DRC_LOG_FILE" value="${LOG_PATH}/rds_drc.log" />
    <property name="PG_DRC_LOG_FILE" value="${LOG_PATH}/pg_drc.log" />
    <property name="DATAX_LOG_FILE" value="${LOG_PATH}/datax.log" />
    <property name="EAGLEEYE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} - traceId:%X{EAGLEEYE_TRACE_ID} -rpcId:%X{EAGLEEYE_RPC_ID} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}" />

    <appender name="APPLICATION"
        class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}</file>
        <encoder>
            <pattern>${EAGLEEYE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <appender name="RDS_DRC_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${RDS_DRC_LOG_FILE}</file>
        <encoder>
            <pattern>${EAGLEEYE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${RDS_DRC_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <appender name="PG_DRC_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${PG_DRC_LOG_FILE}</file>
        <encoder>
            <pattern>${EAGLEEYE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${PG_DRC_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <appender name="DATAX_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${DATAX_LOG_FILE}</file>
        <encoder>
            <pattern>${EAGLEEYE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${DATAX_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="APPLICATION" />
        <appender-ref ref="ALIMONITOR"/>
    </root>
    
    <logger name="com.aliyun.wormhole.qanat.job.QanatRdsSinkJobProcessor"  level="INFO" additivity="false" >
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="RDS_DRC_LOG" />
        <appender-ref ref="ALIMONITOR"/>
    </logger>
    
    <logger name="com.aliyun.wormhole.qanat.job.QanatPgSinkJobProcessor"  level="INFO" additivity="false" >
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="PG_DRC_LOG" />
        <appender-ref ref="ALIMONITOR"/>
    </logger>
    
    <logger name="com.aliyun.wormhole.qanat.service.impl.SyncDataServiceImpl"  level="INFO" additivity="false" >
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="DATAX_LOG" />
        <appender-ref ref="ALIMONITOR"/>
    </logger>
</configuration>