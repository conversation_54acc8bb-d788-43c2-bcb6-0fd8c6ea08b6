<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
http://www.springframework.org/schema/beans/spring-beans-3.1.xsd">
    <!-- DTS 配置 -->
    <bean id="processInstanceService" class="com.taobao.hsf.app.spring.util.HSFSpringConsumerBean">
	    <property name="interfaceName">
	        <value>com.alibaba.alipmc.api.ProcessInstanceService</value>
	    </property>
	    <property name="version">
	        <value>${qanat.bpms.service.version}</value>
	    </property>
    </bean>
    <bean id="processRecordService" class="com.taobao.hsf.app.spring.util.HSFSpringConsumerBean">
        <property name="interfaceName">
            <value>com.alibaba.alipmc.api.ProcessRecordService</value>
        </property>
        <property name="version">
            <value>${qanat.bpms.service.version}</value>
        </property>
    </bean>
</beans>