/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.source.reader;

import org.apache.flink.api.connector.source.SourceOutput;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.connector.base.source.reader.RecordEmitter;

import com.alibaba.ververica.connectors.ots.source.split.OtsInputSplitState;

/** The {@link RecordEmitter} implementation for {@link OtsSourceReader}. */
public class OtsRecordEmitter<T>
        implements RecordEmitter<Tuple3<T, String, Long>, T, OtsInputSplitState> {

    @Override
    public void emitRecord(
            Tuple3<T, String, Long> element,
            SourceOutput<T> sourceOutput,
            OtsInputSplitState splitState)
            throws Exception {
        sourceOutput.collect(element.f0, element.f2);
        splitState.setOffset(element.f1);
    }
}
