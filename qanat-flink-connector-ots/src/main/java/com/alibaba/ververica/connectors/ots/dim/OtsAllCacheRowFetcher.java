/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.dim;

import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.connector.source.KeyGroupPruner;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.util.Collector;
import org.apache.flink.util.FlinkRuntimeException;

import com.alibaba.ververica.connectors.common.dim.DimOptions;
import com.alibaba.ververica.connectors.common.dim.cache.CacheStrategy;
import com.alibaba.ververica.connectors.common.dim.reload.CacheAllReloadConf;
import com.alibaba.ververica.connectors.common.dim.reload.SerializableRunnable;
import com.alibaba.ververica.connectors.common.util.DateUtil;
import com.alibaba.ververica.connectors.common.util.RetryUtils;
import com.alibaba.ververica.connectors.ots.source.OtsConnectionParams;
import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.GetRangeRequest;
import com.alicloud.openservices.tablestore.model.GetRangeResponse;
import com.alicloud.openservices.tablestore.model.PrimaryKeyBuilder;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RangeRowQueryCriteria;
import com.alicloud.openservices.tablestore.model.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

import static com.alibaba.ververica.connectors.common.dim.DimOptions.parseTimeRangeBlacklist;

/**
 * Ots {@link OtsRowFetcher} implementation that join dim table data with all-cache strategy
 * synchronously.
 */
public class OtsAllCacheRowFetcher extends OtsRowFetcher
        implements FlatMapFunction<RowData, RowData> {
    private static final long serialVersionUID = 1L;
    private static final Logger LOG = LoggerFactory.getLogger(OtsAllCacheRowFetcher.class);

    public OtsAllCacheRowFetcher(
            String sqlTableName,
            RowType rowType,
            String[] lookupKeys,
            CacheStrategy cacheStrategy,
            OtsConnectionParams otsConnectionParam,
            KeyGroupPruner<RowData> cachePartitioner) {

        super(
                sqlTableName,
                rowType,
                lookupKeys,
                cacheStrategy,
                otsConnectionParam,
                cachePartitioner);
        Configuration properties = otsConnectionParam.getProperties();
        String blackListRaw = properties.getString(DimOptions.CACHE_RELOAD_TIME_BLACKLIST);
        List<Tuple2<Long, Long>> blacklist = parseTimeRangeBlacklist(blackListRaw);
        reloadConf =
                new CacheAllReloadConf(
                        blacklist,
                        properties.getInteger(DimOptions.CACHE_SCAN_LIMIT),
                        cacheStrategy.getTtlMs());
        setAllCacheReloadRunner(
                new ReloadCacheRunner(param.getTableName(), param.getRetryTimes()), reloadConf);
    }

    @Override
    public void flatMap(RowData rowData, Collector<RowData> collector) throws Exception {
        Object key = getCacheIndexKeyValues(rowData);
        LOG.info("OtsRowFetcher cache all {}", key);

        while (!allCacheHandler.isLoadedOrThrowException()) {
            // cache is not ready for the first, so sleep a while to wait
            Thread.sleep(10);
        }
        allCacheHandler.lock.readLock().lock();
        RowData cachedRow;
        try {
            cachedRow = allCacheHandler.get(key);
        } finally {
            allCacheHandler.lock.readLock().unlock();
        }
        if (cachedRow != null) {
            collector.collect(cachedRow);
        }
    }

    private class ReloadCacheRunner extends SerializableRunnable implements Callable<Void> {
        private static final long serialVersionUID = 1L;
        private final String tableName;
        private final int retryNum;

        private ReloadCacheRunner(String tableName, int retryNum) {
            this.tableName = tableName;
            this.retryNum = retryNum;
        }

        @Override
        public Void call() throws Exception {
            long currentTime = System.currentTimeMillis();
            if (DateUtil.isTimeInRange(reloadConf.timeRangeBlackList, currentTime)) {
                if (allCacheHandler.isLoaded()) {
                    LOG.info(
                            "Current time {} is in reload black list, so try to reload cache next time.",
                            currentTime);
                    return null;
                } else {
                    LOG.info(
                            "Current time {} is in reload black list, but this is the first time to load cache, so still load.",
                            currentTime);
                    // not return
                }
            }

            LOG.info("Reloading all data from tablestore '{}' ...", tableName);
            allCacheHandler.initialize();
            // reload all data
            long startTime = System.nanoTime();
            String fieldStr = String.join("|", fieldNames);
            LOG.info("RowDataTypeInfo.getFieldNames():{}", fieldStr);

            String pkStr =
                    primaryKeyList.stream()
                            .map(ColumnInfo::getFieldName)
                            .collect(Collectors.joining("|"));
            LOG.info("primaryKeyList:{}", pkStr);

            cacheAll(getSyncClientProviderInstance().getClient(), tableName, primaryKeyList);
            long endTime = System.nanoTime();
            LOG.info(
                    "loaded rows from tablestore '{}' into cache, used {}ms,cache row sum {}.",
                    tableName,
                    (endTime - startTime) / 1000_000,
                    cacheRowSum);
            allCacheHandler.switchCache();
            return null;
        }

        @Override
        public void run() {
            try {
                RetryUtils.executeWithRetry(this, retryNum, param.getRetryIntervalMs(), false);
            } catch (Throwable t) {
                FlinkRuntimeException e =
                        new FlinkRuntimeException("Error happens in reload thread.", t);
                LOG.warn("Error happens when scanning all data from tablestore.", e);
                allCacheHandler.setException(e);
            }
        }

        private void cacheAll(
                SyncClient client, String tableName, List<ColumnInfo> primaryKeyInfoList)
                throws Exception {

            ArrayList<String> primaryKeyNameList = new ArrayList<>();
            for (ColumnInfo columnInfo : primaryKeyInfoList) {
                primaryKeyNameList.add(columnInfo.getFieldName());
            }

            RangeRowQueryCriteria rangeRowQueryCriteria = new RangeRowQueryCriteria(tableName);
            PrimaryKeyValue startId = PrimaryKeyValue.INF_MIN;
            PrimaryKeyValue endId = PrimaryKeyValue.INF_MAX;
            PrimaryKeyBuilder primaryKeyBuilderBegin = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            PrimaryKeyBuilder primaryKeyBuilderEnd = PrimaryKeyBuilder.createPrimaryKeyBuilder();
            for (String primaryKeyName : primaryKeyNameList) {
                primaryKeyBuilderBegin.addPrimaryKeyColumn(primaryKeyName, startId);
                primaryKeyBuilderEnd.addPrimaryKeyColumn(primaryKeyName, endId);
            }
            rangeRowQueryCriteria.setInclusiveStartPrimaryKey(primaryKeyBuilderBegin.build());
            rangeRowQueryCriteria.setExclusiveEndPrimaryKey(primaryKeyBuilderEnd.build());

            rangeRowQueryCriteria.setMaxVersions(1);
            GetRangeResponse getRangeResponse;
            int count = 0;
            while (true) {
                getRangeResponse = client.getRange(new GetRangeRequest(rangeRowQueryCriteria));
                LOG.info("loading cache data(caching all)");
                List<Row> rowResults = getRangeResponse.getRows();
                count += rowResults.size();
                for (Row responseRow : rowResults) {
                    RowData resultRow = responseRowToRowData(responseRow);
                    Object indexKeyValues = getCacheIndexKeyValues(resultRow);
                    allCacheHandler.put(indexKeyValues, resultRow);
                }

                if (getRangeResponse.getNextStartPrimaryKey() != null) {
                    rangeRowQueryCriteria.setInclusiveStartPrimaryKey(
                            getRangeResponse.getNextStartPrimaryKey());
                } else {
                    break;
                }
            }
            cacheRowSum += count;
        }
    }
}
