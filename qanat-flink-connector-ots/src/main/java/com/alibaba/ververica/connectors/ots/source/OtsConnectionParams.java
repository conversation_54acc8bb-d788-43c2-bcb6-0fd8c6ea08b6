/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.source;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.StringUtils;

import com.alibaba.ververica.connectors.common.errorcode.ConnectorErrors;
import com.alibaba.ververica.connectors.common.exception.ErrorUtils;
import com.alibaba.ververica.connectors.ots.OtsOptions;
import com.alibaba.ververica.connectors.ots.sink.OverwriteMode;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

import static org.apache.flink.util.Preconditions.checkArgument;

/** */
public class OtsConnectionParams implements Serializable {
    private static final long serialVersionUID = 1L;

    private String instanceName;
    private String tableName;
    private String endPoint;
    private String accessId;
    private String accessKey;
    private String tunnelName;
    private Configuration properties;

    private int ioThreadCount;
    private int callbackThreadPoolSize;
    private int connectionTimeoutMs;
    private int socketTimeoutMs;
    private int retryTimes;
    private int retryIntervalMs;
    private long batchWriteTimeoutMs;
    private int bufferSize;
    private int batchSize;
    private boolean ignoreDelete;
    private boolean skipInvalidData;
    private RetryStrategy retryStrategy;
    private int retryCount;
    private int retryTimeoutMs;

    private String autoIncrementKey;
    private long timestamp;

    private OverwriteMode overwriteMode;
    private List<String> valueColumns;

    public OtsConnectionParams() {}

    public OtsConnectionParams(Configuration configuration) {
        setInstanceName(configuration.getString(OtsOptions.INSTANCE_NAME));
        setTableName(configuration.getString(OtsOptions.TABLE_NAME));
        setEndPoint(configuration.getString(OtsOptions.END_POINT));
        setAccessId(configuration.getString(OtsOptions.OPTIONAL_ACCESSKEY_ID));
        setAccessKey(configuration.getString(OtsOptions.OPTIONAL_ACCESSKEY_SECRET));
        setTunnelName(configuration.getString(OtsOptions.OPTIONAL_TUNNEL_NAME));
        setIoThreadCount(configuration.getInteger(OtsOptions.OPTIONAL_IO_THREAD_COUNT));
        setCallbackThreadPoolSize(
                configuration.getInteger(OtsOptions.OPTIONAL_CALLBACK_THREAD_POOL_SIZE));
        setConnectionTimeoutMs(configuration.getInteger(OtsOptions.OPTIONAL_CONNECT_TIMEOUT_MS));
        setSocketTimeoutMs(configuration.getInteger(OtsOptions.OPTIONAL_SOCKET_TIMEOUT_MS));
        setRetryTimes(configuration.getInteger(OtsOptions.OPTIONAL_MAX_RETRY_TIMES));
        setBufferSize(configuration.getInteger(OtsOptions.OPTIONAL_BUFFER_SIZE));
        setBatchWriteTimeoutMs(configuration.getLong(OtsOptions.OPTIONAL_BATCH_WRITE_TIMEOUT_MS));
        setBatchSize(configuration.getInteger(OtsOptions.OPTIONAL_BATCH_SIZE));
        setIgnoreDelete(configuration.getBoolean(OtsOptions.OPTIONAL_IGNORE_DELETE));
        setSkipInvalidData(configuration.getBoolean(OtsOptions.OPTIONAL_SKIP_INVALID));
        setValueColumns(configuration.getString(OtsOptions.OPTIONAL_VALUE_COLUMNS));
        setOverwriteMode(configuration.get(OtsOptions.OPTIONAL_OVERWRITE_TYPE));
        setRetryStrategy(configuration.get(OtsOptions.OPTIONAL_RETRY_STRATEGY));
        setRetryCount(configuration.getInteger(OtsOptions.OPTIONAL_RETRY_COUNT));
        setRetryIntervalMs(configuration.getInteger(OtsOptions.OPTIONAL_RETRY_INTERVAL_MS));
        setRetryTimeoutMs(configuration.getInteger(OtsOptions.OPTIONAL_RETRY_TIMEOUT_MS));
        setAutoIncrementKey(configuration.getString(OtsOptions.OPTIONAL_AUTO_INCREMENT_KEY));
        setTimestamp(configuration.getLong(OtsOptions.OPTIONAL_DEFAULT_TIMESTAMP));
        this.properties = configuration;
    }

    private static void checkEmptyString(String target, String errorMsg) {
        checkArgument(!StringUtils.isNullOrWhitespaceOnly(target), errorMsg);
    }

    public void check() {
        checkEmptyString(instanceName, "instance name must not empty!");
        checkEmptyString(tableName, "table name must not empty!");
        checkEmptyString(endPoint, "endPoint must not empty!");
    }

    public void setTunnelName(String tunnelName) {
        this.tunnelName = tunnelName;
    }

    public String getTunnelName() {
        return tunnelName;
    }

    public OtsConnectionParams setValueColumns(String columns) {
        if (columns != null) {
            valueColumns = Arrays.asList(columns.split("\\s*,\\s*"));
        } else {
            valueColumns = null;
        }
        return this;
    }

    public List<String> getValueColumnList() {
        return valueColumns;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public OtsConnectionParams setInstanceName(String instanceName) {
        this.instanceName = instanceName;
        return this;
    }

    public String getTableName() {
        return tableName;
    }

    public OtsConnectionParams setTableName(String tableName) {
        this.tableName = tableName;
        return this;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public OtsConnectionParams setEndPoint(String endPoint) {
        this.endPoint = endPoint;
        return this;
    }

    public String getAccessId() {
        return accessId;
    }

    public OtsConnectionParams setAccessId(String accessId) {
        this.accessId = accessId;
        return this;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public OtsConnectionParams setAccessKey(String accessKey) {
        this.accessKey = accessKey;
        return this;
    }

    public Configuration getProperties() {
        return properties;
    }

    public OtsConnectionParams setProperties(Configuration properties) {
        this.properties = properties;
        return this;
    }

    public int getIoThreadCount() {
        return ioThreadCount;
    }

    public OtsConnectionParams setIoThreadCount(int ioThreadCount) {
        checkPositiveValueConfig(ioThreadCount, OtsOptions.OPTIONAL_IO_THREAD_COUNT.key());
        this.ioThreadCount = ioThreadCount;
        return this;
    }

    public int getCallbackThreadPoolSize() {
        return callbackThreadPoolSize;
    }

    public OtsConnectionParams setCallbackThreadPoolSize(int callbackThreadPoolSize) {
        checkPositiveValueConfig(
                callbackThreadPoolSize, OtsOptions.OPTIONAL_CALLBACK_THREAD_POOL_SIZE.key());
        this.callbackThreadPoolSize = callbackThreadPoolSize;
        return this;
    }

    public int getConnectionTimeoutMs() {
        return connectionTimeoutMs;
    }

    public OtsConnectionParams setConnectionTimeoutMs(int connectionTimeoutMs) {
        checkPositiveValueConfig(connectionTimeoutMs, OtsOptions.OPTIONAL_CONNECT_TIMEOUT_MS.key());
        this.connectionTimeoutMs = connectionTimeoutMs;
        return this;
    }

    public int getSocketTimeoutMs() {
        return socketTimeoutMs;
    }

    public OtsConnectionParams setSocketTimeoutMs(int socketTimeoutMs) {
        checkPositiveValueConfig(socketTimeoutMs, OtsOptions.OPTIONAL_SOCKET_TIMEOUT_MS.key());
        this.socketTimeoutMs = socketTimeoutMs;
        return this;
    }

    public int getRetryTimes() {
        return retryTimes;
    }

    public OtsConnectionParams setRetryTimes(int retryTimes) {
        checkPositiveValueConfig(retryTimes, OtsOptions.OPTIONAL_MAX_RETRY_TIMES.key());
        this.retryTimes = retryTimes;
        return this;
    }

    public OtsConnectionParams setOverwriteMode(OverwriteMode mode) {
        overwriteMode = mode;
        return this;
    }

    public OverwriteMode getOverwriteMode() {
        return overwriteMode;
    }

    public int getBufferSize() {
        return bufferSize;
    }

    public OtsConnectionParams setBufferSize(int bufferSize) {
        checkPositiveValueConfig(bufferSize, OtsOptions.OPTIONAL_BUFFER_SIZE.key());
        this.bufferSize = bufferSize;
        return this;
    }

    public long getBatchWriteTimeoutMs() {
        return batchWriteTimeoutMs;
    }

    public OtsConnectionParams setBatchWriteTimeoutMs(long batchWriteTimeoutMs) {
        checkPositiveValueConfig(
                batchWriteTimeoutMs, OtsOptions.OPTIONAL_BATCH_WRITE_TIMEOUT_MS.key());
        this.batchWriteTimeoutMs = batchWriteTimeoutMs;
        return this;
    }

    public OtsConnectionParams setBatchSize(int batchSize) {
        checkPositiveValueConfig(batchSize, OtsOptions.OPTIONAL_BATCH_SIZE.key());
        this.batchSize = batchSize;
        return this;
    }

    public int getBatchSize() {
        return batchSize;
    }

    public OtsConnectionParams setIgnoreDelete(boolean ignoreDelete) {
        this.ignoreDelete = ignoreDelete;
        return this;
    }

    public boolean isIgnoreDelete() {
        return ignoreDelete;
    }

    public boolean isSkipInvalidData() {
        return skipInvalidData;
    }

    public OtsConnectionParams setSkipInvalidData(boolean skipInvalidData) {
        this.skipInvalidData = skipInvalidData;
        return this;
    }

    public String getAutoIncrementKey() {
        return autoIncrementKey;
    }

    public OtsConnectionParams setAutoIncrementKey(String autoIncrementKey) {
        this.autoIncrementKey = autoIncrementKey;
        return this;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public OtsConnectionParams setRetryStrategy(RetryStrategy retryStrategy) {
        this.retryStrategy = retryStrategy;
        return this;
    }

    public RetryStrategy getRetryStrategy() {
        return retryStrategy;
    }

    public OtsConnectionParams setRetryCount(int retryCount) {
        checkPositiveValueConfig(retryCount, OtsOptions.OPTIONAL_RETRY_COUNT.key());
        this.retryCount = retryCount;
        return this;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public OtsConnectionParams setRetryIntervalMs(int retryIntervalMs) {
        checkPositiveValueConfig(retryIntervalMs, OtsOptions.OPTIONAL_RETRY_INTERVAL_MS.key());
        this.retryIntervalMs = retryIntervalMs;
        return this;
    }

    public int getRetryIntervalMs() {
        return retryIntervalMs;
    }

    public OtsConnectionParams setRetryTimeoutMs(int retryTimeoutMs) {
        checkPositiveValueConfig(retryTimeoutMs, OtsOptions.OPTIONAL_RETRY_TIMEOUT_MS.key());
        this.retryTimeoutMs = retryTimeoutMs;
        return this;
    }

    public int getRetryTimeoutMs() {
        return this.retryTimeoutMs;
    }

    private void checkPositiveValueConfig(int value, String key) {
        if (value <= 0) {
            ErrorUtils.throwException(ConnectorErrors.INST.tableDDLConfigError(tableName, key));
        }
    }

    private void checkPositiveValueConfig(long value, String key) {
        if (value <= 0) {
            ErrorUtils.throwException(ConnectorErrors.INST.tableDDLConfigError(tableName, key));
        }
    }
}
