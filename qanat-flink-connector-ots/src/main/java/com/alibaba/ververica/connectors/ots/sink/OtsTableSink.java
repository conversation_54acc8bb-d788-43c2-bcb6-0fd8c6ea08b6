/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.sink;

import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.api.constraints.UniqueConstraint;
import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.table.connector.sink.DynamicTableSink;
import org.apache.flink.table.connector.sink.SinkFunctionProvider;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.types.RowKind;

import com.alibaba.ververica.connectors.common.exception.InvalidParamException;
import com.alibaba.ververica.connectors.ots.source.OtsConnectionParams;

/** Ots {@link DynamicTableSink} implementation that providing sink function . */
public class OtsTableSink implements DynamicTableSink {

    private final OtsConnectionParams param;
    private final TableSchema schema;
    private final String sqlTable;

    public OtsTableSink(String sqlTable, TableSchema schema, OtsConnectionParams param) {
        this.schema = schema;
        this.param = param;
        this.sqlTable = sqlTable;
    }

    @Override
    public ChangelogMode getChangelogMode(ChangelogMode requestedMode) {
        // UPSERT mode
        ChangelogMode.Builder builder = ChangelogMode.newBuilder();
        for (RowKind kind : requestedMode.getContainedKinds()) {
            if (kind != RowKind.UPDATE_BEFORE) {
                builder.addContainedKind(kind);
            }
        }
        return builder.build();
    }

    @Override
    public SinkRuntimeProvider getSinkRuntimeProvider(Context context) {
        RowType rowType = (RowType) schema.toPhysicalRowDataType().getLogicalType();
        UniqueConstraint pk =
                schema.getPrimaryKey()
                        .orElseThrow(
                                () ->
                                        new InvalidParamException(
                                                "OTS table sink primary key was not set."));
        return SinkFunctionProvider.of(new OtsSinkFunction(pk.getColumns(), rowType, param));
    }

    @Override
    public DynamicTableSink copy() {
        return new OtsTableSink(sqlTable, schema, param);
    }

    @Override
    public String asSummaryString() {
        return "OTS-Sink-" + sqlTable;
    }
}
