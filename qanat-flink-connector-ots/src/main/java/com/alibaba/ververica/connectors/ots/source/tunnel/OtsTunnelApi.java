/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.source.tunnel;

import com.alicloud.openservices.tablestore.model.tunnel.TunnelType;
import com.alicloud.openservices.tablestore.model.tunnel.internal.ReadRecordsResponse;

import java.io.Serializable;
import java.util.List;

/** Ots tunnel api interface. */
public interface OtsTunnelApi extends Serializable {

    /**
     * 读取tunnel相关ots信息.
     *
     * @return Tunnel ots meta
     */
    TunnelMeta getTunnelMeta();

    /**
     * 访问ots表tunnel service，返回所有open状态的channel id.
     *
     * @return 所有open状态的channel id list
     */
    List<String> getOpenedChannels();

    /**
     * 向ots tunnel service汇报已经消费完毕的token，用于tunnel service监控消费进度.
     *
     * @param splitID 消费的splitID
     * @param token 已经消费过的最新tunnel token
     */
    void checkpoint(String splitID, String token);

    /**
     * 获取初始checkpoint.
     *
     * @param splitID 消费的splitID
     * @return tunnel service中的初始checkpoint值或最新checkpoint值
     */
    String getCheckpoint(String splitID);

    /**
     * 读取指定split中的ots数据.
     *
     * @param splitID 消费的splitID
     * @param token getScheduledSplits返回的split初始token或上次消费返回的next token
     * @return tunnel records list
     * @throws Exception 网络exception或tunnel service返回的error exception
     */
    ReadRecordsResponse readRecords(String splitID, String token) throws Exception;

    /**
     * 创建tunnel.
     *
     * @param tunnelType tunnel类型，全量、增量、全转增
     */
    void createTunnel(TunnelType tunnelType);

    /** 删除tunnel. */
    void deleteTunnel();

    /** 回收OtsTunnelApi client申请的资源，关闭client. */
    void close();
}
