/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.dim;

import org.apache.flink.table.types.logical.LogicalType;

import java.io.Serializable;

/** Column info stores both ots index and table index. */
public class ColumnInfo implements Serializable {

    private static final long serialVersionUID = 1L;
    private final String fieldName;

    private final LogicalType type;

    // index in lookup keys
    private final int lookupKeyIndex;

    // index in table fields
    private final int tableIndex;

    public ColumnInfo(
            String fieldName, LogicalType type, Integer lookupKeyIndex, Integer tableIndex) {
        this.fieldName = fieldName;
        this.type = type;
        this.lookupKeyIndex = lookupKeyIndex;
        this.tableIndex = tableIndex;
    }

    public String getFieldName() {
        return fieldName;
    }

    public LogicalType getType() {
        return type;
    }

    public Integer getLookupKeyIndex() {
        return lookupKeyIndex;
    }

    public Integer getTableIndex() {
        return tableIndex;
    }
}
