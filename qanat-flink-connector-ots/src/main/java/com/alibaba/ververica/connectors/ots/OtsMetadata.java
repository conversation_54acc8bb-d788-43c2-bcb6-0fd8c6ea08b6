/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots;

import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.types.DataType;

import com.alibaba.ververica.connectors.common.source.resolver.DefaultSourceCollector;

/** The predefined OTS metadata. */
public enum OtsMetadata {
    /** record type of ots record. */
    TYPE("type", DataTypes.STRING(), createStringConverter("type")),
    /** record timestamp of ots record. */
    TIMESTAMP("timestamp", DataTypes.BIGINT(), createLongConverter("timestamp")),
    /** record epoch of ots record. */
    EPOCH("epoch", DataTypes.INT(), createIntConverter("epoch")),
    /** record rowIndex of ots record. */
    ROW_INDEX("row_index", DataTypes.INT(), createIntConverter("row_index")),
    /** record rowIndex of ots record. */
    TRACE_ID("trace_id", DataTypes.STRING(), createIntConverter("trace_id"));

    private final String key;

    private final DataType dataType;

    private final DefaultSourceCollector.MetadataConverter converter;

    OtsMetadata(String key, DataType dataType, DefaultSourceCollector.MetadataConverter converter) {
        this.key = key;
        this.dataType = dataType;
        this.converter = converter;
    }

    public String getKey() {
        return key;
    }

    public DataType getDataType() {
        return dataType;
    }

    public DefaultSourceCollector.MetadataConverter getConverter() {
        return converter;
    }

    private static DefaultSourceCollector.MetadataConverter createLongConverter(String key) {
        return m ->
                m.getProperties().get(key) == null
                        ? -1L
                        : Long.parseLong(m.getProperties().get(key).toString());
    }

    private static DefaultSourceCollector.MetadataConverter createStringConverter(String key) {
        return m ->
                m.getProperties().get(key) == null
                        ? null
                        : StringData.fromString(m.getProperties().get(key).toString());
    }

    private static DefaultSourceCollector.MetadataConverter createIntConverter(String key) {
        return m ->
                m.getProperties().get(key) == null
                        ? -1
                        : Integer.parseInt(m.getProperties().get(key).toString());
    }
}
