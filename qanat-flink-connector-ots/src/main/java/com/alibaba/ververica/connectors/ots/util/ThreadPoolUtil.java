/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.util;

import org.apache.flink.shaded.guava30.com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/** Thread pool util. */
public class ThreadPoolUtil {

    public static ExecutorService createThreadPoolExecutor(int maxThreadCount) {
        ThreadFactory threadFactory =
                new ThreadFactoryBuilder()
                        .setNameFormat("ots-callback-pool-%d")
                        .setDaemon(true)
                        .build();

        return new ThreadPoolExecutor(
                1 /* corePoolSize */,
                maxThreadCount,
                60 /* keepAliveTime */,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(100),
                threadFactory);
    }
}
