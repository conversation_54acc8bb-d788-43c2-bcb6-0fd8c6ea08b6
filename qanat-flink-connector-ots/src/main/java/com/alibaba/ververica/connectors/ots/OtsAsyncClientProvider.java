package com.alibaba.ververica.connectors.ots;

import org.apache.flink.configuration.Configuration;

import com.alibaba.ververica.connectors.common.sts.AbstractClientProvider;
import com.alibaba.ververica.connectors.ots.source.OtsConnectionParams;
import com.alibaba.ververica.connectors.ots.util.ThreadPoolUtil;
import com.alicloud.openservices.tablestore.AsyncClient;
import com.alicloud.openservices.tablestore.ClientConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** Ots {@link AbstractClientProvider} implementation that provide OTS async client functions. */
public class OtsAsyncClientProvider extends AbstractClientProvider<AsyncClient> {

    private static final Logger LOG = LoggerFactory.getLogger(OtsAsyncClientProvider.class);
    private final String instanceName;
    private final String endPoint;
    private final ClientConfiguration clientConfig;
    private final int callbackThreadPoolSize;

    public OtsAsyncClientProvider(
            String instanceName,
            String endPoint,
            String accessId,
            String accessKey,
            ClientConfiguration clientConfig,
            int callbackThreadPoolSize) {
        super(accessId, accessKey, null);
        this.instanceName = instanceName;
        this.endPoint = endPoint;
        this.clientConfig = clientConfig;
        this.callbackThreadPoolSize = callbackThreadPoolSize;
    }

    protected OtsAsyncClientProvider(
            String instanceName,
            String endPoint,
            Configuration properties,
            ClientConfiguration clientConfig,
            int callbackThreadPoolSize) {
        super(null, null, properties);
        this.instanceName = instanceName;
        this.endPoint = endPoint;
        this.clientConfig = clientConfig;
        this.callbackThreadPoolSize = callbackThreadPoolSize;
    }

    public static OtsAsyncClientProvider create(OtsConnectionParams param) {
        ClientConfiguration clientConfig = new ClientConfiguration();
        clientConfig.setIoThreadCount(param.getIoThreadCount());
        clientConfig.setConnectionTimeoutInMillisecond(param.getConnectionTimeoutMs());
        clientConfig.setSocketTimeoutInMillisecond(param.getSocketTimeoutMs());

        OtsAsyncClientProvider clientProvider =
                new OtsAsyncClientProvider(
                        param.getInstanceName(),
                        param.getEndPoint(),
                        param.getAccessId(),
                        param.getAccessKey(),
                        clientConfig,
                        param.getCallbackThreadPoolSize());
        return clientProvider;
    }

    @Override
    protected AsyncClient produceNormalClient(String accessId, String accessKey) {
        return new AsyncClient(
                endPoint,
                accessId,
                accessKey,
                instanceName,
                clientConfig,
                ThreadPoolUtil.createThreadPoolExecutor(callbackThreadPoolSize),
                null);
    }

    @Override
    protected AsyncClient produceStsClient(
            String accessId, String accessKey, String securityToken) {
        return new AsyncClient(
                endPoint,
                accessId,
                accessKey,
                instanceName,
                clientConfig,
                ThreadPoolUtil.createThreadPoolExecutor(callbackThreadPoolSize),
                securityToken);
    }

    @Override
    protected void closeClient() {
        if (null != client) {
            try {
                client.shutdown();
            } catch (Throwable e) {
                LOG.warn("Error when close ots client", e);
            } finally {
                client = null;
            }
        }
    }

    public void shutdownAndSetNull() {
        getClient().shutdown();
        setClientNull();
    }
}
