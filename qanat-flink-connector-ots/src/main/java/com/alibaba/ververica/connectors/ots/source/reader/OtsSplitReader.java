/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.source.reader;

import org.apache.flink.annotation.VisibleForTesting;
import org.apache.flink.api.connector.source.SourceReaderContext;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.connector.base.source.reader.RecordsWithSplitIds;
import org.apache.flink.connector.base.source.reader.splitreader.SplitReader;
import org.apache.flink.connector.base.source.reader.splitreader.SplitsAddition;
import org.apache.flink.connector.base.source.reader.splitreader.SplitsChange;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.metrics.SimpleCounter;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.util.Preconditions;

import com.alibaba.ververica.connectors.ots.source.OtsConnectionParams;
import com.alibaba.ververica.connectors.ots.source.StreamRecordParser;
import com.alibaba.ververica.connectors.ots.source.split.OtsInputSplit;
import com.alibaba.ververica.connectors.ots.source.tunnel.OtsTunnelApi;
import com.alicloud.openservices.tablestore.model.StreamRecord;
import com.alicloud.openservices.tablestore.model.tunnel.internal.ReadRecordsResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nullable;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;

/** A {@link SplitReader} implementation that reads records from ots shard. */
public class OtsSplitReader<T> implements SplitReader<Tuple3<T, String, Long>, OtsInputSplit> {
    private static final Logger LOG = LoggerFactory.getLogger(OtsSplitReader.class);
    public static final String OTS_FINISHED = "finished";
    private static final long DATA_FETCH_TIMEOUT = 10 * 1000;

    private final OtsConnectionParams param;
    private final OtsTunnelApi tunnelApi;
    private final StreamRecordParser parser;

    private final int subtaskId;
    private Queue<OtsInputSplit> splitList;
    private long totalCount;

    private volatile boolean wakeup = false;

    private DoubleGauge latencyGauge;
    private Counter readCounter;

    public OtsSplitReader(
            OtsTunnelApi api,
            OtsConnectionParams param,
            StreamRecordParser parser,
            SourceReaderContext context) {
        super();
        LOG.info("ots split reader init: {}", context.getIndexOfSubtask());
        this.param = param;
        this.tunnelApi = api;
        this.parser = parser;
        this.subtaskId = context.getIndexOfSubtask();
        this.splitList = new LinkedList<>();
        this.totalCount = 0L;

        latencyGauge =
                context.metricGroup()
                        .addGroup("input")
                        .addGroup("ots")
                        .gauge("readLatency", new DoubleGauge());
        readCounter =
                context.metricGroup()
                        .addGroup("input")
                        .addGroup("ots")
                        .counter("readCounter", new SimpleCounter());
    }

    @Override
    public RecordsWithSplitIds<Tuple3<T, String, Long>> fetch() {
        OtsSplitRecords<Tuple3<T, String, Long>> recordsBySplits = new OtsSplitRecords<>();

        int batchCount = 0;
        long startTime = System.currentTimeMillis();
        // fetch batch data on the limit of count and time
        while (!splitList.isEmpty()
                && batchCount < param.getBatchSize()
                && System.currentTimeMillis() - startTime <= DATA_FETCH_TIMEOUT
                && !wakeup) {
            try {
                OtsInputSplit currentSplit = splitList.poll();
                if (OTS_FINISHED.equals(currentSplit.getToken())) {
                    recordsBySplits.addFinishedSplit(currentSplit.splitId());
                    continue;
                } else {
                    int fetchCount = fetchOts(currentSplit, recordsBySplits);
                    batchCount += fetchCount;
                    totalCount += fetchCount;
                    if (fetchCount > 0) {
                        LOG.info(
                                String.format(
                                        "reader[%d] complete one time of ots fetch with data, splitID [%s], fetchCount [%d], batchCount [%d], totalCount [%d]",
                                        subtaskId,
                                        currentSplit.splitId(),
                                        fetchCount,
                                        batchCount,
                                        totalCount));
                    }
                    if (!OTS_FINISHED.equals(currentSplit.getToken())) {
                        splitList.add(currentSplit);
                    } else {
                        recordsBySplits.addFinishedSplit(currentSplit.splitId());
                    }
                }
            } catch (Exception e) {
                LOG.warn("Failed to read record", e);
                throw new RuntimeException(e);
            }
        }
        // report checkpoint to ots
        Iterator<OtsInputSplit> splitIterator = splitList.iterator();
        while (splitIterator.hasNext()) {
            OtsInputSplit split = splitIterator.next();
            tunnelApi.checkpoint(split.splitId(), split.getToken());
        }

        wakeup = false;
        recordsBySplits.prepareForRead();
        return recordsBySplits;
    }

    private int fetchOts(
            OtsInputSplit currentSplit, OtsSplitRecords<Tuple3<T, String, Long>> recordsBySplits)
            throws Exception {
        long beforeRead = System.nanoTime();
        ReadRecordsResponse resp =
                tunnelApi.readRecords(currentSplit.splitId(), currentSplit.getToken());
        List<StreamRecord> records = resp.getRecords();
        Collection<Tuple3<T, String, Long>> recordsForSplit =
                recordsBySplits.recordsForSplit(currentSplit.splitId());

        long readCost = System.nanoTime() - beforeRead;
        if (readCost > 0) {
            latencyGauge.report(readCost / 1000);
        }

        int fetchCount = 0;
        for (StreamRecord record : records) {
            GenericRowData row = parser.extractStreamRecord(record, param.getTableName());
            if (row != null) {
                recordsForSplit.add(
                        new Tuple3(
                                row,
                                resp.getNextToken(),
                                record.getSequenceInfo().getTimestamp() / 1000L));
                readCounter.inc();
                fetchCount++;
            }
        }
        if (fetchCount > 0) {
            LOG.info(
                    String.format(
                            "reader [%d] fetch ots with data: splitID [%s] beforeToken [%s] nextToken [%s] fetch size [%d] split size [%d]",
                            subtaskId,
                            currentSplit.splitId(),
                            currentSplit.getToken(),
                            resp.getNextToken(),
                            fetchCount,
                            recordsForSplit.size()));
        }
        currentSplit.setToken(resp.getNextToken());

        return fetchCount;
    }

    @Override
    public void handleSplitsChanges(SplitsChange<OtsInputSplit> splitsChange) {
        // Get all the partition assignments and stopping offsets.
        if (!(splitsChange instanceof SplitsAddition)) {
            throw new UnsupportedOperationException(
                    String.format(
                            "The SplitChange type of %s is not supported.",
                            splitsChange.getClass()));
        }
        LOG.info(String.format("Subtask %s got splits %s", subtaskId, splitsChange));

        splitList.addAll(splitsChange.splits());
    }

    @Override
    public void wakeUp() {
        this.wakeup = true;
    }

    @Override
    public void close() throws Exception {}

    @VisibleForTesting
    public Queue<OtsInputSplit> getInputSplits() {
        return splitList;
    }

    private static class OtsSplitRecords<T> implements RecordsWithSplitIds<T> {
        private final Map<String, Collection<T>> recordsBySplits;
        private Set<String> finishedSplits;
        private Iterator<Map.Entry<String, Collection<T>>> splitIterator;
        private String currentSplitId;
        private Iterator<T> recordIterator;

        private OtsSplitRecords() {
            this.recordsBySplits = new HashMap<>();
            this.finishedSplits = new HashSet<>();
        }

        private void addFinishedSplit(String finishedSplit) {
            this.finishedSplits.add(finishedSplit);
        }

        private Collection<T> recordsForSplit(String splitId) {
            return recordsBySplits.computeIfAbsent(splitId, id -> new ArrayList<>());
        }

        private void prepareForRead() {
            splitIterator = recordsBySplits.entrySet().iterator();
        }

        @Override
        @Nullable
        public String nextSplit() {
            if (splitIterator.hasNext()) {
                Map.Entry<String, Collection<T>> entry = splitIterator.next();
                currentSplitId = entry.getKey();
                recordIterator = entry.getValue().iterator();
                return currentSplitId;
            } else {
                currentSplitId = null;
                recordIterator = null;
                return null;
            }
        }

        @Override
        @Nullable
        public T nextRecordFromSplit() {
            Preconditions.checkNotNull(
                    currentSplitId,
                    "Make sure nextSplit() did not return null before "
                            + "iterate over the records split.");
            if (recordIterator.hasNext()) {
                return recordIterator.next();
            } else {
                return null;
            }
        }

        @Override
        public Set<String> finishedSplits() {
            return finishedSplits;
        }
    }

    /** Double gauge. */
    public static class DoubleGauge implements Gauge<Double> {
        private double value;

        void report(long value) {
            this.value = value * 1.0;
        }

        void report(double value) {
            this.value = value;
        }

        @Override
        public Double getValue() {
            return value;
        }
    }
}
