/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.util;

import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.types.logical.LogicalType;

import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RecordColumn;
import com.alicloud.openservices.tablestore.model.StreamRecord;

import java.util.HashMap;
import java.util.Map;

/** */
public class OtsRowUtils {

    public static PrimaryKeyValue getPrimaryKeyValue(LogicalType t, int i, RowData row) {
        if (row.isNullAt(i)) {
            throw new RuntimeException("PK should not be null!");
        }
        switch (t.getTypeRoot()) {
            case BINARY:
            case VARBINARY:
                return PrimaryKeyValue.fromBinary(row.getBinary(i));
            case CHAR:
            case VARCHAR:
                return PrimaryKeyValue.fromString(row.getString(i).toString());
            case TINYINT:
                return PrimaryKeyValue.fromLong(row.getByte(i));
            case SMALLINT:
                return PrimaryKeyValue.fromLong(row.getShort(i));
            case INTEGER:
                return PrimaryKeyValue.fromLong(row.getInt(i));
            case BIGINT:
                return PrimaryKeyValue.fromLong(row.getLong(i));
            default:
                throw new IllegalArgumentException(
                        "OTS primary key only accept "
                                + "String, Integer, byte array types, but is "
                                + t);
        }
    }

    public static ColumnValue getColumnValue(LogicalType t, int i, RowData row) {
        if (row.isNullAt(i)) {
            throw new RuntimeException("Should check before. Value should not be null here!");
        }
        switch (t.getTypeRoot()) {
            case BINARY:
            case VARBINARY:
                return ColumnValue.fromBinary(row.getBinary(i));
            case CHAR:
            case VARCHAR:
                return ColumnValue.fromString(row.getString(i).toString());
            case TINYINT:
                return ColumnValue.fromLong(row.getByte(i));
            case SMALLINT:
                return ColumnValue.fromLong(row.getShort(i));
            case INTEGER:
                return ColumnValue.fromLong(row.getInt(i));
            case BIGINT:
                return ColumnValue.fromLong(row.getLong(i));
            case FLOAT:
                return ColumnValue.fromDouble(row.getFloat(i));
            case DOUBLE:
                return ColumnValue.fromDouble(row.getDouble(i));
            case BOOLEAN:
                return ColumnValue.fromBoolean(row.getBoolean(i));
            default:
                throw new IllegalArgumentException(
                        "OTS column type only accept "
                                + "String, Integer, Boolean, Double, Binary types, but is "
                                + t);
        }
    }

    public static Object deserializePrimaryValue(PrimaryKeyValue value, LogicalType type) {
        switch (type.getTypeRoot()) {
            case BINARY:
            case VARBINARY:
                return value.asBinary();
            case CHAR:
            case VARCHAR:
                return StringData.fromString(value.asString());
            case TINYINT:
                return (byte) value.asLong();
            case SMALLINT:
                return (short) value.asLong();
            case INTEGER:
                return (int) value.asLong();
            case BIGINT:
                return value.asLong();
            default:
                throw new IllegalArgumentException(
                        "OTS primary key only accept "
                                + "String, Integer, byte array types, but is "
                                + type);
        }
    }

    public static Object deserializeToInnerValue(ColumnValue value, LogicalType type) {
        switch (type.getTypeRoot()) {
            case BINARY:
            case VARBINARY:
                return value.asBinary();
            case CHAR:
            case VARCHAR:
                return StringData.fromString(value.asString());
            case TINYINT:
                return (byte) value.asLong();
            case SMALLINT:
                return (short) value.asLong();
            case INTEGER:
                return (int) value.asLong();
            case BIGINT:
                return value.asLong();
            case FLOAT:
                return (float) value.asDouble();
            case DOUBLE:
                return value.asDouble();
            case BOOLEAN:
                return value.asBoolean();
            default:
                throw new IllegalArgumentException(
                        "OTS column type only accept "
                                + "String, Integer, Boolean, Double, Binary types, but is "
                                + type);
        }
    }

    public static Map<String, RecordColumn> recordMap(StreamRecord record) {
        Map<String, RecordColumn> columnMap =
                new HashMap<String, RecordColumn>(record.getColumns().size());
        for (RecordColumn column : record.getColumns()) {
            columnMap.put(column.getColumn().getName(), column);
        }
        return columnMap;
    }
}
