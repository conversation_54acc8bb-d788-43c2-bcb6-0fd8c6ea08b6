/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots;

import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.ReadableConfig;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.connector.sink.DynamicTableSink;
import org.apache.flink.table.connector.source.DynamicTableSource;
import org.apache.flink.table.factories.DynamicTableSinkFactory;
import org.apache.flink.table.factories.DynamicTableSourceFactory;
import org.apache.flink.table.factories.Factory;
import org.apache.flink.table.factories.FactoryUtil;
import org.apache.flink.table.types.logical.RowType;

import com.alibaba.ververica.connectors.common.dim.cache.CacheConfig;
import com.alibaba.ververica.connectors.common.dim.cache.CacheStrategy;
import com.alibaba.ververica.connectors.common.exception.NotEnoughParamsException;
import com.alibaba.ververica.connectors.common.sts.StsOptions;
import com.alibaba.ververica.connectors.common.util.StringUtils;
import com.alibaba.ververica.connectors.ots.sink.OtsTableSink;
import com.alibaba.ververica.connectors.ots.source.OtsConnectionParams;
import com.alibaba.ververica.connectors.ots.source.OtsTableSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.Set;

import static com.alibaba.ververica.connectors.common.dim.DimOptions.CACHE_EMPTY;
import static com.alibaba.ververica.connectors.common.dim.DimOptions.CACHE_RELOAD_TIME_BLACKLIST;
import static com.alibaba.ververica.connectors.common.dim.DimOptions.CACHE_SCAN_LIMIT;
import static com.alibaba.ververica.connectors.common.dim.DimOptions.CACHE_TTL;
import static com.alibaba.ververica.connectors.common.dim.DimOptions.CACHE_TYPE;
import static com.alibaba.ververica.connectors.common.dim.DimOptions.MAX_CACHE_SIZE;
import static com.alibaba.ververica.connectors.common.dim.DimOptions.OPTIONAL_ASYNC;
import static com.alibaba.ververica.connectors.common.dim.DimOptions.parseTimeRangeBlacklist;
import static com.alibaba.ververica.connectors.common.util.ContextUtil.normalizeContext;

/** */
public class QanatOtsTableFactory
        implements Factory, DynamicTableSinkFactory, DynamicTableSourceFactory {

    private static final Logger LOG = LoggerFactory.getLogger(QanatOtsTableFactory.class);

    static CacheConfig getCacheConfig(ReadableConfig config) {
        String cacheType = config.getOptional(CACHE_TYPE).orElse("NONE").toUpperCase();
        switch (cacheType) {
            case "LRU":
                return new CacheConfig(
                        CacheStrategy.lru(
                                config.get(MAX_CACHE_SIZE),
                                config.get(CACHE_TTL),
                                false,
                                config.get(CACHE_EMPTY)),
                        parseTimeRangeBlacklist(config.get(CACHE_RELOAD_TIME_BLACKLIST)),
                        config.get(CACHE_SCAN_LIMIT));
            case "ALL":
                return new CacheConfig(CacheStrategy.all(config.get(CACHE_TTL)));
            case "NONE":
                return new CacheConfig(CacheStrategy.none());
            default:
                throw new IllegalArgumentException(
                        "Ots only support NONE, LRU, ALL in cache strategy");
        }
    }

    @Override
    public String factoryIdentifier() {
        return "qanat-ots";
    }

    @Override
    public Set<ConfigOption<?>> requiredOptions() {
        Set<ConfigOption<?>> options = new HashSet<>();
        options.add(OtsOptions.INSTANCE_NAME);
        options.add(OtsOptions.TABLE_NAME);
        options.add(OtsOptions.END_POINT);
        return options;
    }

    @Override
    public Set<ConfigOption<?>> optionalOptions() {
        Set<ConfigOption<?>> options = new HashSet<>();

        // ots
        options.add(OtsOptions.OPTIONAL_ACCESSKEY_ID);
        options.add(OtsOptions.OPTIONAL_ACCESSKEY_SECRET);
        options.add(OtsOptions.OPTIONAL_VALUE_COLUMNS);
        options.add(OtsOptions.OPTIONAL_IO_THREAD_COUNT);
        options.add(OtsOptions.OPTIONAL_CALLBACK_THREAD_POOL_SIZE);
        options.add(OtsOptions.OPTIONAL_BATCH_SIZE);
        options.add(OtsOptions.OPTIONAL_MAX_RETRY_TIMES);
        options.add(OtsOptions.OPTIONAL_IGNORE_DELETE);
        options.add(OtsOptions.OPTIONAL_SKIP_INVALID);
        options.add(OtsOptions.OPTIONAL_CONNECT_TIMEOUT_MS);
        options.add(OtsOptions.OPTIONAL_SOCKET_TIMEOUT_MS);
        options.add(OtsOptions.OPTIONAL_RETRY_INTERVAL_MS);

        // source
        options.add(OtsOptions.OPTIONAL_TUNNEL_NAME);
        options.add(OtsOptions.OPTIONAL_RETRY_STRATEGY);
        options.add(OtsOptions.OPTIONAL_RETRY_COUNT);
        options.add(OtsOptions.OPTIONAL_RETRY_TIMEOUT_MS);

        // sink
        options.add(OtsOptions.OPTIONAL_OVERWRITE_TYPE);
        options.add(OtsOptions.OPTIONAL_BUFFER_SIZE);
        options.add(OtsOptions.OPTIONAL_BATCH_WRITE_TIMEOUT_MS);
        options.add(OtsOptions.OPTIONAL_AUTO_INCREMENT_KEY);
        options.add(OtsOptions.OPTIONAL_DEFAULT_TIMESTAMP);

        // dim
        options.add(CACHE_TYPE);
        options.add(MAX_CACHE_SIZE);
        options.add(CACHE_TTL);
        options.add(CACHE_EMPTY);
        options.add(CACHE_RELOAD_TIME_BLACKLIST);
        options.add(CACHE_SCAN_LIMIT);
        options.add(OPTIONAL_ASYNC);

        // For sts, not use anymore???
        options.add(StsOptions.STS.STS_ROLE_ARN);
        options.add(StsOptions.STS.STS_ACCESS_ID);
        options.add(StsOptions.STS.STS_ACCESS_KEY);
        options.add(StsOptions.STS.STS_UID);
        options.add(StsOptions.STS.STS_ROLEARN_UPDATE_SECONDS);
        options.add(StsOptions.STS.STS_REGION_ID);
        options.add(StsOptions.STS.INNER_STS_ENDPOINT_OPTION);
        return options;
    }

    private void validate(Context context) {
        final Context normalizedContext = normalizeContext(this, context);
        FactoryUtil.createTableFactoryHelper(this, normalizedContext).validate();
    }

    @Override
    public DynamicTableSink createDynamicTableSink(Context context) {
        validate(context);
        String sqlTable = context.getObjectIdentifier().getObjectName();
        TableSchema tableSchema = context.getCatalogTable().getSchema();
        Configuration properties = new Configuration();
        context.getCatalogTable()
                .getOptions()
                .forEach((k, v) -> properties.setString(k.toLowerCase(), v));
        OtsConnectionParams param = new OtsConnectionParams(properties);
        return new OtsTableSink(sqlTable, tableSchema, param);
    }

    @Override
    public DynamicTableSource createDynamicTableSource(Context context) {
        validate(context);
        Configuration properties = new Configuration();
        context.getCatalogTable()
                .getOptions()
                .forEach((k, v) -> properties.setString(k.toLowerCase(), v));
        TableSchema schema = context.getCatalogTable().getSchema();

        String sqlTable = context.getObjectIdentifier().getObjectName();
        LOG.info(
                "create ots stream table source: {} {} {}",
                sqlTable,
                schema.toString(),
                properties.toString());

        String instanceName = properties.getString(OtsOptions.INSTANCE_NAME);
        String tableName = properties.getString(OtsOptions.TABLE_NAME);
        String endPoint = properties.getString(OtsOptions.END_POINT);

        if (StringUtils.isNotEmpty(instanceName, tableName, endPoint)) {
            // send original cache properties
            CacheStrategy cacheStrategy =
                    getCacheConfig(Configuration.fromMap(context.getCatalogTable().getOptions()))
                            .getCacheStrategy();

            OtsConnectionParams param = new OtsConnectionParams(properties);

            return new OtsTableSource(
                    sqlTable,
                    schema,
                    (RowType) schema.toPhysicalRowDataType().getLogicalType(),
                    param,
                    cacheStrategy);
        } else {
            throw new NotEnoughParamsException(
                    String.format(
                            "Required options: %s, %s, %s, but current options are: %s",
                            OtsOptions.INSTANCE_NAME.key(),
                            OtsOptions.TABLE_NAME.key(),
                            OtsOptions.END_POINT.key(),
                            properties));
        }
    }
}
