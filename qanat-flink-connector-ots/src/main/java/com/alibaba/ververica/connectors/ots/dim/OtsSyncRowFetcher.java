/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.dim;

import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.table.connector.source.KeyGroupPruner;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.util.Collector;

import com.alibaba.ververica.connectors.common.dim.cache.CacheStrategy;
import com.alibaba.ververica.connectors.common.util.RetryUtils;
import com.alibaba.ververica.connectors.ots.source.OtsConnectionParams;
import com.alicloud.openservices.tablestore.model.GetRowRequest;
import com.alicloud.openservices.tablestore.model.GetRowResponse;
import com.alicloud.openservices.tablestore.model.SingleRowQueryCriteria;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Callable;

/**
 * Ots {@link OtsRowFetcher} implementation that join dim table data with LRU/none cache strategies
 * synchronously.
 */
public class OtsSyncRowFetcher extends OtsRowFetcher implements FlatMapFunction<RowData, RowData> {
    private static final long serialVersionUID = 1L;
    private static final Logger LOG = LoggerFactory.getLogger(OtsSyncRowFetcher.class);

    public OtsSyncRowFetcher(
            String sqlTableName,
            RowType rowType,
            String[] lookupKeys,
            CacheStrategy cacheStrategy,
            OtsConnectionParams otsConnectionParam,
            KeyGroupPruner<RowData> cachePartitioner) {

        super(
                sqlTableName,
                rowType,
                lookupKeys,
                cacheStrategy,
                otsConnectionParam,
                cachePartitioner);
        LOG.info("OtsSyncRowFetchercacheStrategy{}", cacheStrategy);
    }

    @Override
    public void flatMap(RowData rowData, Collector<RowData> collector) throws Exception {

        Object indexKeyValues = getCacheIndexKeyValues(rowData);
        LOG.info("OtsCacheRowFetcherlru{}", indexKeyValues);

        RowData cachedRow = cache.get(indexKeyValues);
        if (cachedRow != null) {
            if (cachedRow.getArity() > 0) {
                collector.collect(cachedRow);
            }
            return;
        }
        SingleRowQueryCriteria criteria = createCriteria(rowData);
        GetRowResponse getRowResponse = getOtsRowWithRetry(criteria);
        com.alicloud.openservices.tablestore.model.Row responseRow = getRowResponse.getRow();
        if (responseRow != null) {
            RowData resultRow = responseRowToRowData(responseRow);
            cache.put(indexKeyValues, resultRow);
            collector.collect(resultRow);
        } else if (cacheStrategy.isCacheEmpty()) {
            cache.put(indexKeyValues, NULL_ROWDATA);
        }
    }

    public GetRowResponse getOtsRowWithRetry(SingleRowQueryCriteria criteria) throws Exception {
        try {
            //            LoadOtsRowTask task = new LoadOtsRowTask(criteria);
            Callable<GetRowResponse> task =
                    () -> {
                        GetRowRequest getRowRequest = new GetRowRequest(criteria);
                        return getSyncClientProviderInstance().getClient().getRow(getRowRequest);
                    };
            return RetryUtils.executeWithRetry(
                    task, param.getRetryTimes(), param.getRetryIntervalMs(), false);
        } catch (Exception e) {
            LOG.error("get row response failed");
            throw e;
        }
    }
}
