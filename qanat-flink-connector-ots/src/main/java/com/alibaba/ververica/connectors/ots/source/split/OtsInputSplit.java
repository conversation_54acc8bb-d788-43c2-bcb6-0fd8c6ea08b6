/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.source.split;

import org.apache.flink.api.connector.source.SourceSplit;

import java.io.Serializable;

/** */
public class OtsInputSplit implements SourceSplit, Serializable {
    private static final long serialVersionUID = 1L;

    private String splitID;
    private String token;

    public OtsInputSplit(String splitID, String token) {
        this.splitID = splitID;
        this.token = token;
    }

    public void setSplitID(String splitID) {
        this.splitID = splitID;
    }

    @Override
    public String splitId() {
        return splitID;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
