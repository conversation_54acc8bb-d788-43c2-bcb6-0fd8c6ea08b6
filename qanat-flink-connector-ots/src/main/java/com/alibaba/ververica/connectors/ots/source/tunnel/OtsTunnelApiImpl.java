/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.source.tunnel;

import org.apache.flink.util.StringUtils;

import com.alibaba.ververica.connectors.ots.source.OtsConnectionParams;
import com.alibaba.ververica.connectors.ots.source.reader.OtsSplitReader;
import com.alicloud.openservices.tablestore.ClientException;
import com.alicloud.openservices.tablestore.TableStoreException;
import com.alicloud.openservices.tablestore.TunnelClientInterface;
import com.alicloud.openservices.tablestore.core.ErrorCode;
import com.alicloud.openservices.tablestore.model.tunnel.ChannelInfo;
import com.alicloud.openservices.tablestore.model.tunnel.ChannelStatus;
import com.alicloud.openservices.tablestore.model.tunnel.CreateTunnelRequest;
import com.alicloud.openservices.tablestore.model.tunnel.DeleteTunnelRequest;
import com.alicloud.openservices.tablestore.model.tunnel.DescribeTunnelRequest;
import com.alicloud.openservices.tablestore.model.tunnel.DescribeTunnelResponse;
import com.alicloud.openservices.tablestore.model.tunnel.StreamTunnelConfig;
import com.alicloud.openservices.tablestore.model.tunnel.TunnelType;
import com.alicloud.openservices.tablestore.model.tunnel.internal.CheckpointRequest;
import com.alicloud.openservices.tablestore.model.tunnel.internal.GetCheckpointRequest;
import com.alicloud.openservices.tablestore.model.tunnel.internal.GetCheckpointResponse;
import com.alicloud.openservices.tablestore.model.tunnel.internal.ReadRecordsRequest;
import com.alicloud.openservices.tablestore.model.tunnel.internal.ReadRecordsResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

import static org.apache.flink.util.Preconditions.checkArgument;

/** An ots tunnel api implementation. */
public class OtsTunnelApiImpl implements OtsTunnelApi {

    private static final long serialVersionUID = 1L;
    private static final Logger LOG = LoggerFactory.getLogger(OtsTunnelApiImpl.class);

    private static final String VVR_CLIENT_TAG = "vvr-client";

    private OtsConnectionParams param;
    private transient OtsTunnelClientProvider provider;
    private TunnelMeta meta;

    public OtsTunnelApiImpl(OtsConnectionParams param, boolean preFetchMeta)
            throws TableStoreException, ClientException {
        LOG.info("OtsTunnelApiImpl init");
        checkArgument(
                !StringUtils.isNullOrWhitespaceOnly(param.getTunnelName()),
                "tunnel name must not empty!");
        this.param = param;
        if (preFetchMeta) {
            fetchMeta();
            LOG.info("Tunnel detail: {}", this.meta.toString());
        }
    }

    private void fetchMeta() {
        DescribeTunnelRequest describeTunnelRequest =
                new DescribeTunnelRequest(param.getTableName(), param.getTunnelName());
        DescribeTunnelResponse describeTunnelResponse =
                getTunnelClient().describeTunnel(describeTunnelRequest);
        if (describeTunnelResponse.getTunnelInfo() == null) {
            throw new IllegalArgumentException(
                    String.format("tunnel not exist %s", param.getTunnelName()));
        } else {
            this.meta =
                    new TunnelMeta(
                            param.getInstanceName(),
                            param.getTableName(),
                            param.getTunnelName(),
                            describeTunnelResponse.getTunnelInfo().getTunnelId());
        }
    }

    /**
     * 读取tunnel相关ots信息.
     *
     * @return Tunnel ots meta
     */
    @Override
    public TunnelMeta getTunnelMeta() {
        LOG.info("request get tunnel meta");
        return meta;
    }

    @Override
    public List<String> getOpenedChannels() {
        LOG.info("request get opened channels");
        DescribeTunnelRequest describeTunnelRequest =
                new DescribeTunnelRequest(param.getTableName(), param.getTunnelName());
        DescribeTunnelResponse describeTunnelResponse =
                getTunnelClient().describeTunnel(describeTunnelRequest);
        List<String> channels = new ArrayList<>();
        for (ChannelInfo ci : describeTunnelResponse.getChannelInfos()) {
            if (ci.getChannelStatus() == ChannelStatus.OPEN) {
                channels.add(ci.getChannelId());
            }
        }
        return channels;
    }

    /** 向ots tunnel service汇报已经消费完毕的token，用于tunnel service监控消费进度. */
    @Override
    public void checkpoint(String splitID, String token) {
        LOG.info(String.format("checkpoint splitID [%s] token [%s]", splitID, token));
        CheckpointRequest checkpointRequest =
                new CheckpointRequest(meta.tunnelID, VVR_CLIENT_TAG, splitID, token, 0);
        getTunnelClient().checkpoint(checkpointRequest);
    }

    /** @param splitID 消费的splitID. */
    @Override
    public String getCheckpoint(String splitID) {
        LOG.info(String.format("request get checkpoint [%s]", splitID));
        GetCheckpointRequest getCheckpointRequest =
                new GetCheckpointRequest(meta.tunnelID, VVR_CLIENT_TAG, splitID);
        GetCheckpointResponse getCheckpointResponse =
                getTunnelClient().getCheckpoint(getCheckpointRequest);
        LOG.info(
                String.format(
                        "getCheckpoint splitID [%s] checkpoint [%s]",
                        splitID, getCheckpointResponse.getCheckpoint()));
        return getCheckpointResponse.getCheckpoint();
    }

    /** 读取指定split中的ots数据. */
    @Override
    public ReadRecordsResponse readRecords(String splitID, String token) {
        LOG.info(String.format("request read records splitID [%s] token [%s]", splitID, token));
        ReadRecordsRequest readRecordsRequest =
                new ReadRecordsRequest(meta.tunnelID, VVR_CLIENT_TAG, splitID, token);
        ReadRecordsResponse readRecordsResponse = getTunnelClient().readRecords(readRecordsRequest);
        if (readRecordsResponse.getNextToken() == null) {
            readRecordsResponse.setNextToken(OtsSplitReader.OTS_FINISHED);
        }
        return readRecordsResponse;
    }

    @Override
    public void createTunnel(TunnelType tunnelType) {
        LOG.info("request create tunnel");
        CreateTunnelRequest createTunnelRequest =
                new CreateTunnelRequest(param.getTableName(), param.getTunnelName(), tunnelType);
        StreamTunnelConfig streamTunnelConfig = new StreamTunnelConfig();
        createTunnelRequest.setStreamTunnelConfig(streamTunnelConfig);
        try {
            getTunnelClient().createTunnel(createTunnelRequest);
        } catch (TableStoreException e) {
            if (e.getErrorCode().equals(ErrorCode.TUNNEL_EXIST)) {
                LOG.warn("tunnel already exists");
            } else {
                LOG.error("request create tunnel failed", e);
                throw e;
            }
        }
    }

    @Override
    public void deleteTunnel() {
        LOG.info("request delete tunnel");
        DeleteTunnelRequest request =
                new DeleteTunnelRequest(param.getTableName(), param.getTunnelName());
        try {
            getTunnelClient().deleteTunnel(request);
        } catch (TableStoreException e) {
            if (e.getErrorCode().equals(ErrorCode.INVALID_PARAMETER)
                    && e.getMessage().contains("tunnel not exist")) {
                LOG.warn("tunnel not exists");
            } else {
                LOG.error("request delete tunnel failed", e);
                throw e;
            }
        }
    }

    /** 回收OtsTunnelApi client申请的资源，关闭client. */
    @Override
    public void close() {
        provider.shutdownAndSetNull();
    }

    synchronized TunnelClientInterface getTunnelClient() {
        if (null == provider) {
            provider = new OtsTunnelClientProvider(param);
        }
        return provider.getClient();
    }

    @Override
    public String toString() {
        return String.format(
                "instance:%s-table:%s-tunnelName:%s",
                param.getInstanceName(), param.getTableName(), param.getTunnelName());
    }
}
