/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.dim;

import org.apache.flink.annotation.VisibleForTesting;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.ResultTypeQueryable;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.connector.source.KeyGroupPruner;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.runtime.typeutils.InternalTypeInfo;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.util.CollectionUtil;

import com.alibaba.ververica.connectors.common.dim.DimJoinFetcher;
import com.alibaba.ververica.connectors.common.dim.cache.CacheStrategy;
import com.alibaba.ververica.connectors.ots.OtsClientProvider;
import com.alibaba.ververica.connectors.ots.source.OtsConnectionParams;
import com.alicloud.openservices.tablestore.model.Column;
import com.alicloud.openservices.tablestore.model.DescribeTableRequest;
import com.alicloud.openservices.tablestore.model.DescribeTableResponse;
import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.alicloud.openservices.tablestore.model.PrimaryKeyBuilder;
import com.alicloud.openservices.tablestore.model.PrimaryKeyColumn;
import com.alicloud.openservices.tablestore.model.PrimaryKeySchema;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.Row;
import com.alicloud.openservices.tablestore.model.SingleRowQueryCriteria;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.alibaba.ververica.connectors.ots.util.OtsRowUtils.deserializePrimaryValue;
import static com.alibaba.ververica.connectors.ots.util.OtsRowUtils.deserializeToInnerValue;
import static com.alibaba.ververica.connectors.ots.util.OtsRowUtils.getPrimaryKeyValue;

/** {@link DimJoinFetcher} abstract that implement base function for dim table . */
public abstract class OtsRowFetcher extends DimJoinFetcher<RowData>
        implements ResultTypeQueryable<RowData> {

    private static final long serialVersionUID = 1L;
    public static final RowData NULL_ROWDATA = new GenericRowData(0);
    private static final Logger LOG = LoggerFactory.getLogger(OtsRowFetcher.class);
    protected static int cacheRowSum = 0;
    protected final OtsConnectionParams param;
    protected final String[] fieldNames;
    protected transient List<ColumnInfo> primaryKeyList; // sort by key from ots
    protected transient List<ColumnInfo> baseRowFieldList;
    private transient Map<String, Integer> name2TableIndex;
    private transient String[] fieldNamesWithoutKey;
    protected transient InternalTypeInfo<RowData> rowTypeInfo;
    private transient OtsClientProvider syncProvider = null;

    public OtsRowFetcher(
            String sqlTableName,
            RowType rowType,
            String[] lookupKeys,
            CacheStrategy cacheStrategy,
            OtsConnectionParams param,
            KeyGroupPruner<RowData> cachePartitioner) {
        super(sqlTableName, rowType, lookupKeys, cacheStrategy, cachePartitioner);
        this.rowTypeInfo = InternalTypeInfo.of(rowType);
        this.param = param;
        this.fieldNames = dimRowType.getFieldNames().toArray(new String[0]);
    }

    @Override
    public void openConnection(Configuration parameters) {
        primaryKeyList = new ArrayList<>();
        baseRowFieldList = new ArrayList<>();
        name2TableIndex = new HashMap<>();

        Map<Integer, Integer> pkTarget2Source = new HashMap<>();
        for (int i = 0; i < lookupKeys.length; i++) {
            int targetIdx = getColumnIndex(lookupKeys[i], fieldNames);
            assert targetIdx != -1;
            pkTarget2Source.put(targetIdx, i);
        }

        // apply index for variable declared in dim table
        List<String> fieldNameListWithoutKey = new ArrayList<>();
        for (int i = 0; i < dimRowType.getFieldCount(); i++) {
            LogicalType type = dimRowType.getTypeAt(i);
            String name = fieldNames[i];
            name2TableIndex.put(name, i);
            if (pkTarget2Source.containsKey(i)) {
                ColumnInfo info = new ColumnInfo(name, type, pkTarget2Source.get(i), i);
                baseRowFieldList.add(info);
                primaryKeyList.add(info);
            } else {
                baseRowFieldList.add(new ColumnInfo(name, type, -1, i));
                fieldNameListWithoutKey.add(fieldNames[i]);
            }
        }
        this.fieldNamesWithoutKey = fieldNameListWithoutKey.toArray(new String[0]);

        // request table meta
        DescribeTableRequest describeTableRequest = new DescribeTableRequest(param.getTableName());
        DescribeTableResponse describeTableResponse =
                getSyncClientProviderInstance().getClient().describeTable(describeTableRequest);
        if (describeTableResponse != null
                && describeTableResponse.getTableMeta() != null
                && !CollectionUtil.isNullOrEmpty(
                        describeTableResponse.getTableMeta().getPrimaryKeyList())) {
            reArrangePrimaryKeys(describeTableResponse.getTableMeta().getPrimaryKeyList());
        } else {
            throw new RuntimeException(
                    "request primary keys of table meta fails with table name is "
                            + param.getTableName());
        }
    }

    @Override
    public void closeConnection() {
        try {
            if (null != syncProvider) {
                syncProvider.shutdownAndSetNull();
            }
        } catch (Exception e) {
            LOG.warn("ignore exception when shutdown sync client", e);
        }
    }

    @VisibleForTesting
    public synchronized OtsClientProvider getSyncClientProviderInstance() {
        if (null == syncProvider) {
            syncProvider = createProvider();
        }
        return syncProvider;
    }

    @VisibleForTesting
    public void setOtsClientProvider(OtsClientProvider syncProvider) {
        this.syncProvider = syncProvider;
    }

    private OtsClientProvider createProvider() {
        OtsClientProvider clientProvider = OtsClientProvider.create(param);

        LOG.info(
                "Init OTS client successfully with configuration: "
                        + "callbackThreadPoolSize="
                        + param.getCallbackThreadPoolSize()
                        + ", ioThreadCount="
                        + param.getIoThreadCount()
                        + ", connectionTimeoutMs="
                        + param.getConnectionTimeoutMs()
                        + ", socketTimeoutMs="
                        + param.getSocketTimeoutMs());
        return clientProvider;
    }

    private void reArrangePrimaryKeys(List<PrimaryKeySchema> primaryKey) {
        Map<String, Integer> map = new HashMap<>();
        for (int i = 0; i < primaryKey.size(); i++) {
            map.put(primaryKey.get(i).getName(), i);
        }
        Collections.sort(
                this.primaryKeyList,
                (o1, o2) -> {
                    try {
                        String nameA = fieldNames[o1.getTableIndex()];
                        String nameB = fieldNames[o2.getTableIndex()];
                        return map.get(nameA).compareTo(map.get(nameB));
                    } catch (Exception e) {
                        throw new RuntimeException(
                                "OTSDimTable PrimaryKey do not match: " + o1 + " " + o2);
                    }
                });
    }

    protected SingleRowQueryCriteria createCriteria(RowData rowData) {
        PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();

        for (ColumnInfo columnInfo : primaryKeyList) {
            PrimaryKeyValue keyValue =
                    getPrimaryKeyValue(
                            columnInfo.getType(), columnInfo.getLookupKeyIndex(), rowData);
            primaryKeyBuilder.addPrimaryKeyColumn(columnInfo.getFieldName(), keyValue);
        }
        PrimaryKey primaryKey = primaryKeyBuilder.build();

        // read only one row
        SingleRowQueryCriteria criteria =
                new SingleRowQueryCriteria(param.getTableName(), primaryKey);
        // read the latest version
        criteria.setMaxVersions(1);

        criteria.addColumnsToGet(fieldNamesWithoutKey);

        return criteria;
    }

    protected GenericRowData getCacheIndexKeyValues(RowData baseRow) {
        GenericRowData key = new GenericRowData(primaryKeyList.size());
        for (int i = 0; i < primaryKeyList.size(); i++) {
            ColumnInfo columnInfo = primaryKeyList.get(i);
            Object value =
                    this.safeGet(baseRow, columnInfo.getLookupKeyIndex(), columnInfo.getType());
            key.setField(i, value);
        }
        return key;
    }

    protected RowData responseRowToRowData(Row responseRow) throws IOException {
        GenericRowData resultRow = new GenericRowData(baseRowFieldList.size());
        PrimaryKey primaryKey = responseRow.getPrimaryKey();
        PrimaryKeyColumn[] primaryKeyColumns = primaryKey.getPrimaryKeyColumns();

        // clear row
        for (int i = 0; i < resultRow.getArity(); i++) {
            resultRow.setField(i, null);
        }

        for (PrimaryKeyColumn column : primaryKeyColumns) {
            Integer index = name2TableIndex.get(column.getName());
            LogicalType type = baseRowFieldList.get(index).getType();
            Object realValue = deserializePrimaryValue(column.getValue(), type);
            resultRow.setField(index, realValue);
        }
        Column[] columns = responseRow.getColumns();
        for (Column c : columns) {
            Integer index = name2TableIndex.get(c.getName());
            if (index == null) {
                // ignore column not declared in dim table schema
                continue;
            }
            LogicalType type = baseRowFieldList.get(index).getType();
            Object realValue = deserializeToInnerValue(c.getValue(), type);
            resultRow.setField(index, realValue);
        }
        return resultRow;
    }

    @Override
    public TypeInformation<RowData> getProducedType() {
        return rowTypeInfo;
    }

    @Override
    public boolean hasPrimaryKey() {
        return true;
    }
}
