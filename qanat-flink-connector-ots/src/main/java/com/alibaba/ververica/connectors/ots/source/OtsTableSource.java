/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.source;

import org.apache.flink.annotation.VisibleForTesting;
import org.apache.flink.api.connector.source.Source;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.table.connector.source.AsyncTableFunctionProvider;
import org.apache.flink.table.connector.source.KeyGroupPruner;
import org.apache.flink.table.connector.source.LookupTableSource;
import org.apache.flink.table.connector.source.ScanTableSource;
import org.apache.flink.table.connector.source.SourceProvider;
import org.apache.flink.table.connector.source.TableFunctionProvider;
import org.apache.flink.table.connector.source.abilities.SupportsKeyGroupPrune;
import org.apache.flink.table.connector.source.abilities.SupportsReadingMetadata;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.functions.AsyncTableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.RowType;

import com.alibaba.ververica.connectors.common.dim.AsyncLookupFunctionWrapper;
import com.alibaba.ververica.connectors.common.dim.DimOptions;
import com.alibaba.ververica.connectors.common.dim.LookupFunctionWrapper;
import com.alibaba.ververica.connectors.common.dim.cache.CacheStrategy;
import com.alibaba.ververica.connectors.ots.OtsMetadata;
import com.alibaba.ververica.connectors.ots.dim.OtsAllCacheRowFetcher;
import com.alibaba.ververica.connectors.ots.dim.OtsAsyncRowFetcher;
import com.alibaba.ververica.connectors.ots.dim.OtsSyncRowFetcher;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/** Ots {@link LookupTableSource} implementation that provide lookup table function (dim). */
public class OtsTableSource
        implements LookupTableSource,
                ScanTableSource,
                SupportsKeyGroupPrune<RowData>,
                SupportsReadingMetadata {

    private String sqlTableName;
    private TableSchema tableSchema;
    private RowType rowType;
    private OtsConnectionParams param;
    private CacheStrategy cacheStrategy;
    private KeyGroupPruner<RowData> cachePartitioner;
    private List<String> metadataKeys;

    public OtsTableSource(
            String sqlTableName,
            TableSchema tableSchema,
            RowType rowType,
            OtsConnectionParams param,
            CacheStrategy cacheStrategy) {
        param.check();
        this.sqlTableName = sqlTableName;
        this.tableSchema = tableSchema;
        this.rowType = rowType;
        this.param = param;
        this.cacheStrategy = cacheStrategy;
        this.metadataKeys = new ArrayList<>();
    }

    @Override
    public OtsTableSource copy() {
        return new OtsTableSource(sqlTableName, tableSchema, rowType, param, cacheStrategy);
    }

    @Override
    public String asSummaryString() {
        return "OTS-Source-" + sqlTableName;
    }

    @Override
    public LookupRuntimeProvider getLookupRuntimeProvider(LookupContext lookupContext) {
        String[] lookupKeys = new String[lookupContext.getKeys().length];
        for (int i = 0; i < lookupKeys.length; i++) {
            lookupKeys[i] = rowType.getFieldNames().get(lookupContext.getKeys()[i][0]);
        }

        if (param.getProperties().get(DimOptions.OPTIONAL_ASYNC)) {
            AsyncTableFunction<RowData> lookupFunc =
                    new AsyncLookupFunctionWrapper(
                            new OtsAsyncRowFetcher(
                                    sqlTableName,
                                    rowType,
                                    lookupKeys,
                                    cacheStrategy,
                                    param,
                                    cachePartitioner));
            return AsyncTableFunctionProvider.of(lookupFunc);
        } else {
            if (cacheStrategy.isAllCache()) {
                return TableFunctionProvider.of(
                        new LookupFunctionWrapper(
                                new OtsAllCacheRowFetcher(
                                        sqlTableName,
                                        rowType,
                                        lookupKeys,
                                        cacheStrategy,
                                        param,
                                        cachePartitioner)));
            } else {
                return TableFunctionProvider.of(
                        new LookupFunctionWrapper(
                                new OtsSyncRowFetcher(
                                        sqlTableName,
                                        rowType,
                                        lookupKeys,
                                        cacheStrategy,
                                        param,
                                        cachePartitioner)));
            }
        }
    }

    @Override
    public void applyKeyGroupPruner(KeyGroupPruner<RowData> cachePartitioner) {
        this.cachePartitioner = cachePartitioner;
    }

    @Override
    public ChangelogMode getChangelogMode() {
        return ChangelogMode.insertOnly();
    }

    @Override
    public ScanRuntimeProvider getScanRuntimeProvider(ScanContext scanContext) {
        RowType rowType = (RowType) tableSchema.toPhysicalRowDataType().getLogicalType();
        return new SourceProvider() {

            @Override
            public boolean isBounded() {
                return false;
            }

            @Override
            public Source<RowData, ?, ?> createSource() {
                return new OtsSource(rowType, param, metadataKeys);
            }
        };
    }

    @Override
    public Map<String, DataType> listReadableMetadata() {
        final Map<String, DataType> metadataMap = new LinkedHashMap<>();
        Stream.of(OtsMetadata.values())
                .forEachOrdered(m -> metadataMap.putIfAbsent(m.getKey(), m.getDataType()));
        return metadataMap;
    }

    @Override
    public void applyReadableMetadata(List<String> list, DataType dataType) {
        metadataKeys.clear();
        metadataKeys.addAll(list);
    }

    @VisibleForTesting
    public CacheStrategy cacheStrategy() {
        return cacheStrategy;
    }
}
