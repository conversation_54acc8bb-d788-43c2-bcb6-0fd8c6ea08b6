/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots;

import org.apache.flink.configuration.ConfigOption;

import com.alibaba.ververica.connectors.ots.sink.OverwriteMode;
import com.alibaba.ververica.connectors.ots.source.RetryStrategy;

import static org.apache.flink.configuration.ConfigOptions.key;

/** */
public class OtsOptions {

    public static final ConfigOption<String> INSTANCE_NAME =
            key("instanceName".toLowerCase()).stringType().noDefaultValue();
    public static final ConfigOption<String> TABLE_NAME =
            key("tableName".toLowerCase()).stringType().noDefaultValue();
    public static final ConfigOption<String> OPTIONAL_TUNNEL_NAME =
            key("tunnelName".toLowerCase()).stringType().noDefaultValue();
    public static final ConfigOption<String> END_POINT =
            key("endPoint".toLowerCase()).stringType().noDefaultValue();
    public static final ConfigOption<String> OPTIONAL_ACCESSKEY_ID =
            key("accessId".toLowerCase()).stringType().noDefaultValue();
    public static final ConfigOption<String> OPTIONAL_ACCESSKEY_SECRET =
            key("accessKey".toLowerCase()).stringType().noDefaultValue();
    public static final ConfigOption<String> OPTIONAL_VALUE_COLUMNS =
            key("valueColumns".toLowerCase()).stringType().noDefaultValue();
    public static final ConfigOption<Integer> OPTIONAL_IO_THREAD_COUNT =
            key("ioThreadCount".toLowerCase()).intType().defaultValue(4);
    public static final ConfigOption<Integer> OPTIONAL_CALLBACK_THREAD_POOL_SIZE =
            key("callbackThreadPoolSize".toLowerCase()).intType().defaultValue(4);
    public static final ConfigOption<Integer> OPTIONAL_BUFFER_SIZE =
            key("bufferSize".toLowerCase()).intType().defaultValue(5000);
    public static final ConfigOption<Long> OPTIONAL_BATCH_WRITE_TIMEOUT_MS =
            key("batchWriteTimeoutMs".toLowerCase()).longType().defaultValue(5000L);
    public static final ConfigOption<Integer> OPTIONAL_BATCH_SIZE =
            key("batchSize".toLowerCase()).intType().defaultValue(100);
    public static final ConfigOption<Integer> OPTIONAL_MAX_RETRY_TIMES =
            key("maxRetryTimes".toLowerCase()).intType().defaultValue(10);
    public static final ConfigOption<Boolean> OPTIONAL_IGNORE_DELETE =
            key("ignoreDelete".toLowerCase()).booleanType().defaultValue(false);
    public static final ConfigOption<Boolean> OPTIONAL_SKIP_INVALID =
            key("skipInvalidData".toLowerCase()).booleanType().defaultValue(false);
    public static final ConfigOption<Integer> OPTIONAL_CONNECT_TIMEOUT_MS =
            key("connectTimeout".toLowerCase()).intType().defaultValue(30000);
    public static final ConfigOption<Integer> OPTIONAL_SOCKET_TIMEOUT_MS =
            key("socketTimeout".toLowerCase()).intType().defaultValue(30000);
    public static final ConfigOption<OverwriteMode> OPTIONAL_OVERWRITE_TYPE =
            key("overwriteMode".toLowerCase())
                    .enumType(OverwriteMode.class)
                    .defaultValue(OverwriteMode.PUT);
    public static final ConfigOption<RetryStrategy> OPTIONAL_RETRY_STRATEGY =
            key("retryStrategy".toLowerCase())
                    .enumType(RetryStrategy.class)
                    .defaultValue(RetryStrategy.COUNT);
    public static final ConfigOption<Integer> OPTIONAL_RETRY_COUNT =
            key("retryCount".toLowerCase()).intType().defaultValue(3);
    public static final ConfigOption<Integer> OPTIONAL_RETRY_INTERVAL_MS =
            key("retryIntervalMs".toLowerCase()).intType().defaultValue(1000);
    public static final ConfigOption<Integer> OPTIONAL_RETRY_TIMEOUT_MS =
            key("retryTimeoutMs".toLowerCase()).intType().defaultValue(180000);

    public static final ConfigOption<String> PARTITION_BY =
            key("partitionBy".toLowerCase()).stringType().noDefaultValue();

    public static final ConfigOption<String> OPTIONAL_AUTO_INCREMENT_KEY =
            key("autoIncrementKey".toLowerCase()).stringType().noDefaultValue();

    public static final ConfigOption<Long> OPTIONAL_DEFAULT_TIMESTAMP =
            key("defaultTimestampInMillisecond".toLowerCase()).longType().defaultValue(-1L);
}
