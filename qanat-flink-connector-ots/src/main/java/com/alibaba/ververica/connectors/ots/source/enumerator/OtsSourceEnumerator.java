/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.source.enumerator;

import org.apache.flink.annotation.VisibleForTesting;
import org.apache.flink.api.connector.source.SplitEnumerator;
import org.apache.flink.api.connector.source.SplitEnumeratorContext;

import com.alibaba.ververica.connectors.ots.source.split.OtsInputSplit;
import com.alibaba.ververica.connectors.ots.source.tunnel.OtsTunnelApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nullable;

import java.io.IOException;
import java.util.ArrayDeque;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * A {@link SplitEnumerator} implementation that enumerates and assigns splits for ots source
 * consumption.
 */
public class OtsSourceEnumerator implements SplitEnumerator<OtsInputSplit, OtsSourceEnumState> {

    private static final Logger LOG = LoggerFactory.getLogger(OtsSourceEnumerator.class);

    private static final long DISCOVER_CHANNEL_INTERVAL_MS = 30 * 1000;

    private final OtsTunnelApi tunnelApi;

    private final SplitEnumeratorContext<OtsInputSplit> context;

    /** Readers which has been assigned new splits. */
    protected Set<Integer> readers;

    /** The pending splits that await to be assigned. */
    private Queue<OtsInputSplit> pendingSplits;

    /** The pending channels that await to be assigned. */
    private Queue<String> pendingChannels;

    /** The ots channels that have ever existed. */
    private Set<String> allChannels;

    public OtsSourceEnumerator(
            SplitEnumeratorContext<OtsInputSplit> context, OtsTunnelApi tunnelApi) {
        this.context = context;
        this.tunnelApi = tunnelApi;
        this.readers = new HashSet<>();
        this.pendingSplits = new ArrayDeque<>();
        this.pendingChannels = new ArrayDeque<>();
        this.allChannels = new HashSet<>();
    }

    public OtsSourceEnumerator(
            SplitEnumeratorContext<OtsInputSplit> context,
            OtsTunnelApi tunnelApi,
            OtsSourceEnumState otsSourceEnumState) {
        this.context = context;
        this.tunnelApi = tunnelApi;
        this.readers = new HashSet<>();
        this.pendingSplits = otsSourceEnumState.getPendingSplits();
        this.pendingChannels = otsSourceEnumState.getPendingChannels();
        this.allChannels = otsSourceEnumState.getAllChannels();
    }

    @Override
    public void start() {
        LOG.info(
                "Starting the OtsSourceEnumerator in streaming runtime with periodic channel discovery interval of {}ms.",
                DISCOVER_CHANNEL_INTERVAL_MS);
        context.callAsync(
                this::listChannels, this::processNewChannels, 0, DISCOVER_CHANNEL_INTERVAL_MS);
    }

    private List<String> listChannels() {
        LOG.info("list channels");
        return tunnelApi.getOpenedChannels();
    }

    @VisibleForTesting
    public void processNewChannels(List<String> channels, Throwable t) {
        LOG.info("process new channels");
        if (t != null) {
            LOG.error("Failed to list channels.", t);
            return;
        }

        // insert new splits into pending channels
        for (String channel : channels) {
            if (!allChannels.contains(channel)) {
                LOG.info("find new channel: {}", channel);
                allChannels.add(channel);
                pendingChannels.add(channel);
            }
        }

        assignSplits();
    }

    @Override
    public void handleSplitRequest(int subtaskId, @Nullable String s) {
        LOG.info("handle split request, reader: {}", subtaskId);
        // do nothing
    }

    @Override
    public void addSplitsBack(List<OtsInputSplit> splits, int subtaskId) {
        LOG.info(
                "add splits back, splits: {}, reader: {}",
                splits.stream().map(OtsInputSplit::splitId).collect(Collectors.joining(", ")),
                subtaskId);
        pendingSplits.addAll(splits);
        assignSplits();
    }

    private void assignSplits() {
        LOG.info(
                "assign splits, readers list: {}",
                readers.stream().map(String::valueOf).collect(Collectors.joining(", ")));
        int currentParallelism = context.currentParallelism();
        Queue<OtsInputSplit> unassignedSplits = new LinkedList<>();
        while (hasNextSplit()) {
            OtsInputSplit split = nextSplit();
            int assignReaderId = Math.abs(split.splitId().hashCode()) % currentParallelism;
            if (readers.contains(assignReaderId)) {
                try {
                    LOG.info(
                            "assign split to target reader, split: {}, reader id: {}, current parallelism: {}",
                            split.splitId(),
                            assignReaderId,
                            currentParallelism);
                    context.assignSplit(split, assignReaderId);
                } catch (Exception e) {
                    LOG.error(
                            "assign split to target reader failed, split: {}, reader id: {}, current parallelism: {}",
                            split.splitId(),
                            assignReaderId,
                            currentParallelism,
                            e);
                    unassignedSplits.add(split);
                }
            } else {
                LOG.warn(
                        "assign split to unregistered reader, split: {}, reader id: {}, current parallelism: {}",
                        split.splitId(),
                        assignReaderId,
                        currentParallelism);
                unassignedSplits.add(split);
            }
        }

        // rollback splits to pending splits queue
        while (!unassignedSplits.isEmpty()) {
            OtsInputSplit split = unassignedSplits.poll();
            LOG.info("rollback splits to pending splits queue, split: {}", split.splitId());
            pendingSplits.add(split);
        }
    }

    private boolean hasNextSplit() {
        return !pendingSplits.isEmpty() || !pendingChannels.isEmpty();
    }

    private OtsInputSplit nextSplit() {
        LOG.info(
                "compute next split, pending splits size: {}. pending channels size: {}",
                pendingSplits.size(),
                pendingChannels.size());
        // firstly, get split from pending splits
        if (!pendingSplits.isEmpty()) {
            return pendingSplits.poll();
        }

        // secondly, get split from pending channels
        if (!pendingChannels.isEmpty()) {
            String peekChannel = pendingChannels.poll();
            String initToken = tunnelApi.getCheckpoint(peekChannel);
            OtsInputSplit split = new OtsInputSplit(peekChannel, initToken);
            return split;
        }
        return null;
    }

    @Override
    public void addReader(int i) {
        LOG.info("add reader, reader id: {}", i);
        readers.add(i);
    }

    @Override
    public OtsSourceEnumState snapshotState(long l) throws Exception {
        return new OtsSourceEnumState(pendingSplits, pendingChannels, allChannels);
    }

    @Override
    public void close() throws IOException {
        LOG.info("close ots source enumerator");
        // do nothing
    }

    @VisibleForTesting
    public Queue<OtsInputSplit> getPendingSplits() {
        return pendingSplits;
    }

    @VisibleForTesting
    public Queue<String> getPendingChannels() {
        return pendingChannels;
    }

    @VisibleForTesting
    public Set<String> getAllChannels() {
        return allChannels;
    }
}
