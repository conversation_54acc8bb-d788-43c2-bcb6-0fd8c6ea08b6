package com.alibaba.ververica.connectors.ots.source;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.ververica.connectors.ots.OtsMetadata;
import com.alibaba.ververica.connectors.ots.OtsOptions;
import com.alibaba.ververica.connectors.ots.util.OtsRowUtils;
import com.alicloud.openservices.tablestore.model.RecordColumn;
import com.alicloud.openservices.tablestore.model.StreamRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/** Parser for ots stream record. */
public class StreamRecordParser implements Serializable {

    private static final long serialVersionUID = 1L;
    private static final Logger LOG = LoggerFactory.getLogger(StreamRecordParser.class);

    private final List<String> allColumns;
    private final LogicalType[] columnsTypes;
    private final boolean ignoreDelete;
    private final boolean skipInvalidData;
    private final List<String> metadataKeys;

    public StreamRecordParser(RowType rowType, Configuration params, List<String> metadataKeys) {
        this.allColumns = rowType.getFieldNames();
        this.ignoreDelete = params.getBoolean(OtsOptions.OPTIONAL_IGNORE_DELETE);
        this.skipInvalidData = params.getBoolean(OtsOptions.OPTIONAL_SKIP_INVALID);
        this.columnsTypes = new LogicalType[rowType.getFieldCount()];
        for (int i = 0; i < columnsTypes.length; i++) {
            columnsTypes[i] = rowType.getTypeAt(i);
        }
        this.metadataKeys = metadataKeys;
    }

    public GenericRowData extractStreamRecord(StreamRecord record, String tableName) {
        if (ignoreDelete && record.getRecordType() == StreamRecord.RecordType.DELETE) {
            return null;
        }
        GenericRowData resultRow = new GenericRowData(allColumns.size() + metadataKeys.size());
        JSONObject data = new JSONObject();
        String traceId = UUID.randomUUID().toString();
        data.put("eventId", traceId);
        data.put(
                "operateType",
                record.getRecordType() == StreamRecord.RecordType.PUT
                        ? "CREATE"
                        : record.getRecordType().toString());
        JSONObject extParam = new JSONObject();
        data.put("extParam", extParam);
        JSONObject objectInstanceVO = new JSONObject();
        extParam.put("objectInstanceVO", objectInstanceVO);
        JSONObject fieldMap = new JSONObject();
        Map<String, RecordColumn> recordColumnMap = OtsRowUtils.recordMap(record);
        for (String columnName : recordColumnMap.keySet()) {
            fieldMap.put(
                    columnName, recordColumnMap.get(columnName).getColumn().getValue().toString());
        }
        objectInstanceVO.put("fieldMap", fieldMap);
        objectInstanceVO.put(
                "objectBizId",
                record.getPrimaryKey().getPrimaryKeyColumns()[0].getValue().toString());
        objectInstanceVO.put("objectUniqueCode", tableName);
        int colIdx = 0;
        resultRow.setField(colIdx, StringData.fromString(data.toJSONString()));

        if (metadataKeys.size() > 0) {
            for (String metadataKey : metadataKeys) {
                colIdx++;
                if (metadataKey.equals(OtsMetadata.TRACE_ID.getKey())) {
                    resultRow.setField(colIdx, StringData.fromString(traceId));
                }
            }
        }
        return resultRow;
    }
}
