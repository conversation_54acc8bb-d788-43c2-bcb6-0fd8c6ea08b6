/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.source.reader;

import org.apache.flink.api.connector.source.SourceReaderContext;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.base.source.reader.RecordEmitter;
import org.apache.flink.connector.base.source.reader.RecordsWithSplitIds;
import org.apache.flink.connector.base.source.reader.SingleThreadMultiplexSourceReaderBase;
import org.apache.flink.connector.base.source.reader.splitreader.SplitReader;
import org.apache.flink.connector.base.source.reader.synchronization.FutureCompletingBlockingQueue;

import com.alibaba.ververica.connectors.ots.source.split.OtsInputSplit;
import com.alibaba.ververica.connectors.ots.source.split.OtsInputSplitState;
import com.alibaba.ververica.connectors.ots.source.tunnel.OtsTunnelApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;
import java.util.function.Supplier;

/**
 * A {@link SingleThreadMultiplexSourceReaderBase} implementation that manages readers to read
 * records from ots shard.
 */
public class OtsSourceReader<T>
        extends SingleThreadMultiplexSourceReaderBase<
                Tuple3<T, Long, Long>, T, OtsInputSplit, OtsInputSplitState> {

    private static final Logger LOG = LoggerFactory.getLogger(OtsSourceReader.class);
    private OtsTunnelApi tunnelApi;
    private Queue<String> pendingToFinishedSplits = new LinkedList<>();
    private Map<Long, Set<String>> checkpointFinishedSplits = new HashMap<>();

    public OtsSourceReader(
            OtsTunnelApi tunnelApi,
            FutureCompletingBlockingQueue<RecordsWithSplitIds<Tuple3<T, Long, Long>>> elementsQueue,
            Supplier<SplitReader<Tuple3<T, Long, Long>, OtsInputSplit>> splitReaderSupplier,
            RecordEmitter<Tuple3<T, Long, Long>, T, OtsInputSplitState> recordEmitter,
            Configuration config,
            SourceReaderContext context) {
        super(elementsQueue, splitReaderSupplier, recordEmitter, config, context);
        this.tunnelApi = tunnelApi;
    }

    @Override
    public void start() {
        LOG.info("ots source reader init: {}", context.getIndexOfSubtask());
    }

    @Override
    protected void onSplitFinished(Map<String, OtsInputSplitState> finishedSplitIds) {
        finishedSplitIds.forEach(
                (ignored, splitState) -> {
                    pendingToFinishedSplits.add(splitState.splitId());
                });
    }

    @Override
    protected OtsInputSplitState initializedState(OtsInputSplit otsInputSplit) {
        return new OtsInputSplitState(otsInputSplit);
    }

    @Override
    protected OtsInputSplit toSplitType(String s, OtsInputSplitState otsInputSplitState) {
        return otsInputSplitState.toOtsInputSplit();
    }

    @Override
    public List<OtsInputSplit> snapshotState(long checkpointId) {
        LOG.info("snapshotState, checkpoint id: {}", checkpointId);
        Set<String> finishedSplits = new HashSet<>();
        while (!pendingToFinishedSplits.isEmpty()) {
            finishedSplits.add(pendingToFinishedSplits.poll());
        }
        checkpointFinishedSplits.put(checkpointId, finishedSplits);
        return super.snapshotState(checkpointId);
    }

    @Override
    public void notifyCheckpointComplete(long checkpointId) throws Exception {
        LOG.info("notifyCheckpointComplete, checkpoint id: {}", checkpointId);
        if (checkpointFinishedSplits.containsKey(checkpointId)) {
            checkpointFinishedSplits
                    .get(checkpointId)
                    .forEach(
                            finishedSplit -> {
                                reportFinishedToOts(finishedSplit);
                            });
            checkpointFinishedSplits.remove(checkpointId);
        }
        super.notifyCheckpointComplete(checkpointId);
    }

    private void reportFinishedToOts(String splitId) {
        LOG.info("reportFinishedToOts, split id: {}", splitId);
        try {
            this.tunnelApi.checkpoint(splitId, OtsSplitReader.OTS_FINISHED);
        } catch (Exception e) {
            LOG.error(
                    String.format(
                            "Fatal error: Read Records or Update Finished CheckPoint Failed %s",
                            e));
            throw new RuntimeException("Fatal error: Update finished CheckPoint failed ", e);
        }
    }
}
