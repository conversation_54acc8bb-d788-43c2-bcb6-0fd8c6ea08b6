/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots;

import org.apache.flink.configuration.Configuration;

import com.alibaba.ververica.connectors.common.sts.AbstractClientProvider;
import com.alibaba.ververica.connectors.ots.source.OtsConnectionParams;
import com.alibaba.ververica.connectors.ots.util.ThreadPoolUtil;
import com.alicloud.openservices.tablestore.ClientConfiguration;
import com.alicloud.openservices.tablestore.SyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** Ots {@link AbstractClientProvider}. */
public class OtsClientProvider extends AbstractClientProvider<SyncClient> {

    private static final Logger LOG = LoggerFactory.getLogger(OtsClientProvider.class);

    private String instanceName;
    private String endPoint;
    private ClientConfiguration clientConfig;
    private int callbackThreadPoolSize;

    public OtsClientProvider(
            String instanceName,
            String endPoint,
            String accessId,
            String accessKey,
            Configuration properties,
            ClientConfiguration clientConfig,
            int callbackThreadPoolSize) {
        super(accessId, accessKey, properties);
        this.instanceName = instanceName;
        this.endPoint = endPoint;
        this.clientConfig = clientConfig;
        this.callbackThreadPoolSize = callbackThreadPoolSize;
    }

    public static OtsClientProvider create(OtsConnectionParams param) {
        ClientConfiguration clientConfig = new ClientConfiguration();
        clientConfig.setIoThreadCount(param.getIoThreadCount());
        clientConfig.setConnectionTimeoutInMillisecond(param.getConnectionTimeoutMs());
        clientConfig.setSocketTimeoutInMillisecond(param.getSocketTimeoutMs());

        OtsClientProvider clientProvider =
                new OtsClientProvider(
                        param.getInstanceName(),
                        param.getEndPoint(),
                        param.getAccessId(),
                        param.getAccessKey(),
                        param.getProperties(),
                        clientConfig,
                        param.getCallbackThreadPoolSize());
        return clientProvider;
    }

    @Override
    protected SyncClient produceNormalClient(String accessId, String accessKey) {
        return new SyncClient(
                endPoint,
                accessId,
                accessKey,
                instanceName,
                clientConfig,
                null,
                ThreadPoolUtil.createThreadPoolExecutor(callbackThreadPoolSize));
    }

    @Override
    protected SyncClient produceStsClient(String accessId, String accessKey, String securityToken) {
        return new SyncClient(
                endPoint,
                accessId,
                accessKey,
                instanceName,
                clientConfig,
                securityToken,
                ThreadPoolUtil.createThreadPoolExecutor(callbackThreadPoolSize));
    }

    @Override
    protected void closeClient() {
        if (null != client) {
            try {
                client.shutdown();
            } catch (Throwable e) {
                LOG.warn("Error when close ots client", e);
            } finally {
                client = null;
            }
        }
    }

    public void shutdownAndSetNull() {
        getClient().shutdown();
        setClientNull();
    }
}
