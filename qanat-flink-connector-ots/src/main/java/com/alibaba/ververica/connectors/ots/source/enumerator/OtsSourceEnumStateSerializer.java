/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.source.enumerator;

import org.apache.flink.core.io.SimpleVersionedSerializer;

import com.alibaba.ververica.connectors.ots.source.split.OtsInputSplit;
import com.alibaba.ververica.connectors.ots.source.split.OtsInputSplitSerializer;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.util.ArrayDeque;
import java.util.HashSet;
import java.util.Queue;
import java.util.Set;

/** {@link OtsSourceEnumStateSerializer}. */
public class OtsSourceEnumStateSerializer implements SimpleVersionedSerializer<OtsSourceEnumState> {
    private static final int CURRENT_VERSION = 0;

    private final SimpleVersionedSerializer<OtsInputSplit> splitSerializer =
            new OtsInputSplitSerializer();

    @Override
    public int getVersion() {
        return 0;
    }

    @Override
    public byte[] serialize(OtsSourceEnumState otsSourceEnumState) throws IOException {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
                DataOutputStream out = new DataOutputStream(baos)) {
            Queue<OtsInputSplit> pendingSplits = otsSourceEnumState.getPendingSplits();
            Queue<String> pendingChannels = otsSourceEnumState.getPendingChannels();
            Set<String> otsChannels = otsSourceEnumState.getAllChannels();

            // serialize pending splits
            out.writeInt(pendingSplits.size());
            out.writeInt(splitSerializer.getVersion());
            for (OtsInputSplit split : pendingSplits) {
                byte[] serializedSplit = splitSerializer.serialize(split);
                out.writeInt(serializedSplit.length);
                out.write(serializedSplit);
            }

            // serialize pending channels
            out.writeInt(pendingChannels.size());
            for (String pc : pendingChannels) {
                out.writeUTF(pc);
            }

            // serialize all channels
            out.writeInt(otsChannels.size());
            for (String c : otsChannels) {
                out.writeUTF(c);
            }

            out.flush();

            return baos.toByteArray();
        }
    }

    @Override
    public OtsSourceEnumState deserialize(int version, byte[] bytes) throws IOException {
        if (version != CURRENT_VERSION) {
            throw new IllegalArgumentException(
                    String.format(
                            "Unrecognized OtsSourceEnumState version %s, current version is %s",
                            version, CURRENT_VERSION));
        }
        try (ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
                DataInputStream in = new DataInputStream(bais)) {
            // deserialize pending splits
            int pendingSplitsSize = in.readInt();
            int splitSerializerVersion = in.readInt();
            Queue<OtsInputSplit> pendingSplits = new ArrayDeque<>();
            for (int i = 0; i < pendingSplitsSize; i++) {
                int serializedSplitSize = in.readInt();
                byte[] serializedSplit = new byte[serializedSplitSize];
                in.readFully(serializedSplit);
                OtsInputSplit split =
                        splitSerializer.deserialize(splitSerializerVersion, serializedSplit);
                pendingSplits.add(split);
            }

            // deserialize pending partitions
            int pendingChannelsSize = in.readInt();
            Queue<String> pendingChannels = new ArrayDeque<>();
            for (int i = 0; i < pendingChannelsSize; i++) {
                pendingChannels.add(in.readUTF());
            }

            // deserialize finished partitions
            int allChannelsSize = in.readInt();
            Set<String> allChannels = new HashSet<>();
            for (int i = 0; i < allChannelsSize; i++) {
                allChannels.add(in.readUTF());
            }

            return new OtsSourceEnumState(pendingSplits, pendingChannels, allChannels);
        }
    }
}
