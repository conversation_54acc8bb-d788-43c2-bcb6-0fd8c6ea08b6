package com.alibaba.ververica.connectors.ots.source.tunnel;

import java.io.Serializable;

/** Ots Tunnel Meta. */
public class TunnelMeta implements Serializable {

    private static final long serialVersionUID = 1L;

    String instanceName;
    String tableName;
    String tunnelName;
    String tunnelID;

    public TunnelMeta(String instanceName, String tableName, String tunnelName, String tunnelID) {
        this.instanceName = instanceName;
        this.tableName = tableName;
        this.tunnelName = tunnelName;
        this.tunnelID = tunnelID;
    }

    @Override
    public String toString() {
        return String.format(
                "instance:%s-table:%s-tunnelName:%s-tunnelID:%s",
                instanceName, tableName, tunnelName, tunnelID);
    }
}
