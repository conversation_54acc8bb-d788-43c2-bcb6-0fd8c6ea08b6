/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.sink;

import org.apache.flink.annotation.VisibleForTesting;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.metrics.Meter;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.util.FlinkRuntimeException;

import com.alibaba.ververica.connectors.common.MetricUtils;
import com.alibaba.ververica.connectors.common.errorcode.ConnectorErrors;
import com.alibaba.ververica.connectors.common.exception.ErrorUtils;
import com.alibaba.ververica.connectors.common.metrics.SimpleGauge;
import com.alibaba.ververica.connectors.common.util.RetryUtils;
import com.alibaba.ververica.connectors.ots.OtsClientProvider;
import com.alibaba.ververica.connectors.ots.source.OtsConnectionParams;
import com.alibaba.ververica.connectors.ots.util.OtsRowUtils;
import com.alicloud.openservices.tablestore.model.BatchWriteRowRequest;
import com.alicloud.openservices.tablestore.model.BatchWriteRowResponse;
import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.alicloud.openservices.tablestore.model.PrimaryKeyBuilder;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RowChange;
import com.alicloud.openservices.tablestore.model.RowDeleteChange;
import com.alicloud.openservices.tablestore.model.RowPutChange;
import com.alicloud.openservices.tablestore.model.RowUpdateChange;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/** Ots {@link SinkFunction} implementation that put sink data to OTS table . */
public class OtsSinkFunction extends RichSinkFunction<RowData> implements CheckpointedFunction {

    private static final long serialVersionUID = 2L;
    private static final Logger LOG = LoggerFactory.getLogger(OtsSinkFunction.class);
    private static final String DUPLICATE_RATIO = "dup_ratio";

    private final RowType rowType;
    private final OtsConnectionParams param;
    private final List<String> valueColumns;
    private final List<String> pkColumns;
    private transient ScheduledExecutorService scheduledExecutorService;

    private transient Map<PrimaryKey, RowChange> buffer;
    private transient OtsClientProvider clientProvider = null;
    private transient DoubleGauge dupRatioGauge;
    private transient Meter outTps;
    private transient Meter outBps;
    private transient SimpleGauge latencyGauge;
    private transient long inputCount = 0;
    private transient int currCount = 0;
    private transient BatchWriteRowRequest batchWriteReq;

    public OtsSinkFunction(List<String> pkColumns, RowType rowType, OtsConnectionParams param) {
        this.rowType = rowType;
        this.param = param;
        this.pkColumns = pkColumns;
        this.valueColumns = getValueColumnList();
    }

    @VisibleForTesting
    void setOtsClientProvider(OtsClientProvider provider) {
        this.clientProvider = provider;
    }

    @Override
    public void open(Configuration configuration) {
        LOG.info(
                "init ots client success, instance {} and table {}",
                param.getInstanceName(),
                param.getTableName());

        this.buffer = new ConcurrentHashMap<>();
        outTps = MetricUtils.registerNumRecordsOutRate(getRuntimeContext());
        outBps = MetricUtils.registerNumBytesOutRate(getRuntimeContext(), "ots");
        latencyGauge = MetricUtils.registerCurrentSendTime(getRuntimeContext());
        dupRatioGauge =
                getRuntimeContext()
                        .getMetricGroup()
                        .addGroup(getName())
                        .gauge(DUPLICATE_RATIO, new DoubleGauge());
    }

    @Override
    public void invoke(RowData row, Context context) throws Exception {
        switch (row.getRowKind()) {
            case INSERT:
            case UPDATE_AFTER:
                updateRowAddColumns(row);
                break;
            case DELETE:
                updateRowDeleteColumns(row);
                break;
            default:
                throw new UnsupportedOperationException(
                        "Unsupported row kind: " + row.getRowKind());
        }
    }

    public void updateRowAddColumns(RowData row) {
        inputCount++;
        PrimaryKey primaryKey = createPrimaryKey(row);

        switch (param.getOverwriteMode()) {
            case PUT:
                RowPutChange rowPutChange = new RowPutChange(param.getTableName(), primaryKey);
                updateRowChange(rowPutChange, row);
                buffer.put(primaryKey, rowPutChange);
                break;
            case UPDATE:
                if (!buffer.containsKey(primaryKey)) {
                    RowUpdateChange rowUpdateChange =
                            new RowUpdateChange(param.getTableName(), primaryKey);
                    buffer.put(primaryKey, rowUpdateChange);
                }
                updateRowChange(buffer.get(primaryKey), row);
                break;
        }
        outputDatas();
    }

    private void updateRowDeleteColumns(RowData row) {
        if (param.getAutoIncrementKey() != null) {
            return;
        }
        if (param.isIgnoreDelete()) {
            return;
        }
        inputCount++;
        PrimaryKey primaryKey = createPrimaryKey(row);

        if (param.getOverwriteMode() == OverwriteMode.PUT) {
            RowDeleteChange rowDeleteChange = new RowDeleteChange(param.getTableName(), primaryKey);
            buffer.put(primaryKey, rowDeleteChange);
        } else {
            if (!buffer.containsKey(primaryKey)) {
                RowUpdateChange rowUpdateChange =
                        new RowUpdateChange(param.getTableName(), primaryKey);
                buffer.put(primaryKey, rowUpdateChange);
            }
            deleteRowChange(buffer.get(primaryKey), row);
        }
        outputDatas();
    }

    private void outputDatas() {
        startScheduledExecutorService();
        if (inputCount >= param.getBufferSize()) {
            dupRatioGauge.report((1 - (buffer.size() * 1.0) / inputCount) * 100);
            if (LOG.isDebugEnabled()) {
                LOG.debug(
                        "buffer: "
                                + buffer.size()
                                + ", inputCount:"
                                + inputCount
                                + ", duplicate ratio: "
                                + (1 - (buffer.size() * 1.0) / inputCount) * 100);
            }
            sync();
        }
    }

    @Override
    public void snapshotState(FunctionSnapshotContext context) {
        sync();
    }

    @Override
    public void initializeState(FunctionInitializationContext context) {
        // nothing to do.
    }

    private synchronized void sync() {
        long start = System.currentTimeMillis();
        for (Entry<PrimaryKey, RowChange> entry : buffer.entrySet()) {
            if (batchWriteReq == null) {
                batchWriteReq = new BatchWriteRowRequest();
                this.currCount = 0;
            }
            batchWriteReq.addRowChange(entry.getValue());
            this.currCount++;
            if (currCount >= param.getBatchSize()) {
                batchWrite();
                this.batchWriteReq = null;
                this.currCount = 0;
            }
        }
        if (this.currCount >= 0) {
            batchWrite();
            this.batchWriteReq = null;
            this.currCount = 0;
        }

        // report metrics
        long end = System.currentTimeMillis();
        if (latencyGauge != null) {
            latencyGauge.report(end - start, buffer.size());
        }
        if (outTps != null) {
            outTps.markEvent(buffer.size());
        }
        if (outBps != null) {
            // rough estimate, each row regard as 1000 bytes
            outBps.markEvent(buffer.size() * 1000);
        }

        // clear status
        buffer.clear();
        inputCount = 0;
    }

    private void batchWrite() {
        if (batchWriteReq != null) {
            try {
                BatchWriteTask task = new BatchWriteTask(batchWriteReq);
                RetryUtils.executeWithRetry(
                        task, param.getRetryTimes(), param.getRetryIntervalMs(), false);
            } catch (Exception e) {
                LOG.error("Batch write to ots table {} fail after retries", param.getTableName());
            }
        }
    }

    @Override
    public void close() {
        try {
            if (null != buffer && (!buffer.isEmpty())) {
                sync();
            }
        } finally {
            getClientProvider().shutdownAndSetNull();
            scheduledExecutorService.shutdown();
            LOG.info("Close ots client success for table {}", param.getTableName());
        }
    }

    private void updateRowChange(RowChange rowChange, RowData row) {
        for (String valueName : valueColumns) {
            int valueIndex = rowType.getFieldIndex(valueName);
            if (valueIndex == -1) {
                ErrorUtils.throwException(
                        ConnectorErrors.INST.otsAttributeKeysError(
                                param.getTableName(), valueName));
            }
            if (!row.isNullAt(valueIndex)) {
                switch (param.getOverwriteMode()) {
                    case PUT:
                        RowPutChange putChange = (RowPutChange) rowChange;
                        if (param.getTimestamp() > 0) {
                            putChange.addColumn(
                                    valueName,
                                    getColumnValue(valueIndex, row),
                                    param.getTimestamp());
                        } else {
                            putChange.addColumn(valueName, getColumnValue(valueIndex, row));
                        }
                        break;
                    case UPDATE:
                    default:
                        RowUpdateChange updateChange = (RowUpdateChange) rowChange;
                        if (param.getTimestamp() > 0) {
                            updateChange.put(
                                    valueName,
                                    getColumnValue(valueIndex, row),
                                    param.getTimestamp());
                        } else {
                            updateChange.put(valueName, getColumnValue(valueIndex, row));
                        }
                        break;
                }
            } else {
                if (LOG.isDebugEnabled()) {
                    LOG.debug(
                            "update ignore null value, name: {}, index: {}", valueName, valueIndex);
                }
            }
        }
    }

    private void deleteRowChange(RowChange rowChange, RowData row) {
        RowUpdateChange updateChange = (RowUpdateChange) rowChange;
        for (String valueName : valueColumns) {
            int valueIndex = rowType.getFieldIndex(valueName);
            if (valueIndex == -1) {
                ErrorUtils.throwException(
                        ConnectorErrors.INST.otsAttributeKeysError(
                                param.getTableName(), valueName));
            }
            if (!row.isNullAt(valueIndex)) {
                updateChange.deleteColumns(valueName);
            } else {
                if (LOG.isDebugEnabled()) {
                    LOG.debug(
                            "delete ignore null value, name: {}, index: {}", valueName, valueIndex);
                }
            }
        }
    }

    private OtsClientProvider getClientProvider() {
        if (clientProvider == null) {
            clientProvider = OtsClientProvider.create(param);
        }
        return clientProvider;
    }

    @VisibleForTesting
    List<String> getValueColumnList() {
        List<String> columnList = param.getValueColumnList();
        if (columnList != null && !columnList.isEmpty()) {
            return columnList;
        }

        columnList = rowType.getFieldNames();
        for (String key : pkColumns) {
            columnList.remove(key);
        }
        return columnList;
    }

    private PrimaryKeyValue getPrimaryKeyValue(int i, RowData row) {
        return OtsRowUtils.getPrimaryKeyValue(rowType.getTypeAt(i), i, row);
    }

    private ColumnValue getColumnValue(int i, RowData row) {
        return OtsRowUtils.getColumnValue(rowType.getTypeAt(i), i, row);
    }

    public String getName() {
        return "ots";
    }

    @Override
    public String toString() {
        return getClass().getSimpleName()
                + ": "
                + param.getInstanceName()
                + "#"
                + param.getTableName();
    }

    public PrimaryKey createPrimaryKey(RowData row) {
        PrimaryKeyBuilder keyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        for (String pkName : pkColumns) {
            int pkIndex = rowType.getFieldIndex(pkName);
            if (pkIndex == -1) {
                ErrorUtils.throwException(
                        ConnectorErrors.INST.otsPrimaryKeyError(param.getTableName(), pkName));
            }
            try {
                keyBuilder.addPrimaryKeyColumn(pkName, getPrimaryKeyValue(pkIndex, row));
            } catch (Exception e) {
                ErrorUtils.throwException(
                        ConnectorErrors.INST.otsPkIsNullError(param.getTableName(), pkName));
            }
        }
        if (param.getAutoIncrementKey() != null) {
            keyBuilder.addPrimaryKeyColumn(
                    param.getAutoIncrementKey(), PrimaryKeyValue.AUTO_INCREMENT);
        }

        return keyBuilder.build();
    }

    private synchronized void startScheduledExecutorService() {
        if (null == scheduledExecutorService) {
            LOG.info("startScheduledExecutorService");
            scheduledExecutorService =
                    Executors.newScheduledThreadPool(
                            2,
                            new ThreadFactory() {
                                private final AtomicInteger counter = new AtomicInteger(0);

                                @Override
                                public Thread newThread(Runnable r) {
                                    return new Thread(
                                            r,
                                            "ots-sink-scheduled-pool-%d"
                                                    + counter.getAndIncrement());
                                }
                            });
            scheduledExecutorService.scheduleAtFixedRate(
                    new Runnable() {
                        @Override
                        public void run() {
                            LOG.debug(
                                    "Background auto flush data: "
                                            + param.getBatchWriteTimeoutMs()
                                            + "ms, buffer="
                                            + buffer.size());
                            sync();
                        }
                    },
                    0,
                    param.getBatchWriteTimeoutMs(),
                    TimeUnit.MILLISECONDS);
        }
    }

    /** Double {@link Gauge}. */
    public static class DoubleGauge implements Gauge<Double> {
        private double value;

        void report(double value) {
            this.value = value;
        }

        @Override
        public Double getValue() {
            return value;
        }
    }

    private class BatchWriteTask implements Callable<BatchWriteRowResponse> {
        BatchWriteRowRequest batchWriteReq;

        public BatchWriteTask(BatchWriteRowRequest batchWriteReq) {
            this.batchWriteReq = batchWriteReq;
        }

        @Override
        public BatchWriteRowResponse call() {
            BatchWriteRowResponse result =
                    getClientProvider().getClient().batchWriteRow(batchWriteReq);
            if (result.isAllSucceed()) {
                return result;
            } else {
                int failRows = result.getFailedRows().size();
                batchWriteReq = batchWriteReq.createRequestForRetry(result.getFailedRows());
                throw new FlinkRuntimeException("Batch write to ots fail rows count: " + failRows);
            }
        }
    }
}
