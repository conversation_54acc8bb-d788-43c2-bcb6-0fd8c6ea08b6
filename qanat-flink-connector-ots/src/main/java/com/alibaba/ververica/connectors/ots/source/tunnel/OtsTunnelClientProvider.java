/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.source.tunnel;

import com.alibaba.ververica.connectors.common.sts.AbstractClientProvider;
import com.alibaba.ververica.connectors.ots.source.OtsConnectionParams;
import com.alibaba.ververica.connectors.ots.source.RetryStrategy;
import com.alibaba.ververica.connectors.ots.util.ThreadPoolUtil;
import com.alicloud.openservices.tablestore.ClientConfiguration;
import com.alicloud.openservices.tablestore.TunnelClient;
import com.alicloud.openservices.tablestore.model.AlwaysRetryStrategy;
import com.alicloud.openservices.tablestore.model.DefaultRetryStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/** */
public class OtsTunnelClientProvider extends AbstractClientProvider<TunnelClient> {
    private static Logger logger = LoggerFactory.getLogger(OtsTunnelClientProvider.class);
    private OtsConnectionParams param;
    private ClientConfiguration clientConfig;

    public OtsTunnelClientProvider(OtsConnectionParams param) {
        super(param.getAccessId(), param.getAccessKey(), param.getProperties());
        this.param = param;
        clientConfig = new ClientConfiguration();
        clientConfig.setIoThreadCount(param.getIoThreadCount());
        RetryStrategy retryStrategy = param.getRetryStrategy();
        if (retryStrategy == RetryStrategy.COUNT) {
            Integer retryCount = param.getRetryCount();
            int retryIntervalMs = param.getRetryIntervalMs();
            clientConfig.setRetryStrategy(new AlwaysRetryStrategy(retryCount, retryIntervalMs));
        } else {
            Integer retryTimeoutS = param.getRetryTimeoutMs();
            clientConfig.setRetryStrategy(
                    new DefaultRetryStrategy(retryTimeoutS, TimeUnit.MILLISECONDS));
        }
    }

    @Override
    protected TunnelClient produceNormalClient(String accessId, String accessKey) {
        return new TunnelClient(
                param.getEndPoint(),
                accessId,
                accessKey,
                param.getInstanceName(),
                clientConfig,
                null,
                ThreadPoolUtil.createThreadPoolExecutor(param.getCallbackThreadPoolSize()));
    }

    @Override
    protected TunnelClient produceStsClient(
            String accessId, String accessKey, String securityToken) {
        return new TunnelClient(
                param.getEndPoint(),
                accessId,
                accessKey,
                param.getInstanceName(),
                clientConfig,
                securityToken,
                ThreadPoolUtil.createThreadPoolExecutor(param.getCallbackThreadPoolSize()));
    }

    @Override
    protected void closeClient() {
        if (null != client) {
            try {
                client.shutdown();
            } catch (Throwable e) {
                logger.warn("Error when close ots client", e);
            } finally {
                client = null;
            }
        }
    }

    public void shutdownAndSetNull() {
        getClient().shutdown();
        setClientNull();
    }
}
