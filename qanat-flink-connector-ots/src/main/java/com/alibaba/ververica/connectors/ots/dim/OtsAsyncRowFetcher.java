package com.alibaba.ververica.connectors.ots.dim;

import org.apache.flink.annotation.VisibleForTesting;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.async.AsyncFunction;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.apache.flink.table.connector.source.KeyGroupPruner;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.RowType;

import com.alibaba.ververica.connectors.common.dim.cache.CacheStrategy;
import com.alibaba.ververica.connectors.common.exception.InvalidParamException;
import com.alibaba.ververica.connectors.ots.OtsAsyncClientProvider;
import com.alibaba.ververica.connectors.ots.source.OtsConnectionParams;
import com.alicloud.openservices.tablestore.AsyncClient;
import com.alicloud.openservices.tablestore.TableStoreCallback;
import com.alicloud.openservices.tablestore.model.GetRowRequest;
import com.alicloud.openservices.tablestore.model.GetRowResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;

/** Ots {@link AsyncFunction} implementation that join dim table data asynchronously . */
public class OtsAsyncRowFetcher extends OtsRowFetcher implements AsyncFunction<RowData, RowData> {

    private static final long serialVersionUID = 1L;
    private static final Logger LOG = LoggerFactory.getLogger(OtsAsyncRowFetcher.class);
    private transient OtsAsyncClientProvider asyncProvider = null;

    public OtsAsyncRowFetcher(
            String sqlTableName,
            RowType rowType,
            String[] lookupKeys,
            CacheStrategy cacheStrategy,
            OtsConnectionParams otsConnectionParam,
            KeyGroupPruner<RowData> cachePartitioner) {
        super(
                sqlTableName,
                rowType,
                lookupKeys,
                cacheStrategy,
                otsConnectionParam,
                cachePartitioner);
        LOG.info("OtsAsyncRowFetcher");
        if (cacheStrategy.isAllCache()) {
            throw new InvalidParamException("Ots AsyncRowFetcher can't use with cache all");
        }
    }

    @Override
    public void asyncInvoke(RowData row, ResultFuture<RowData> resultFuture) {
        GenericRowData key = getCacheIndexKeyValues(row);
        if (key == null) {
            resultFuture.complete(Collections.emptyList());
            return;
        }
        LOG.debug("Join Ots on an empty key of row: {}", row);
        // always join on pk for now.
        RowData cachedRow = cache.get(key);
        if (cachedRow != null) {
            if (cachedRow.getArity() == 0) {
                // key is cached and not hit
                resultFuture.complete(Collections.emptyList());
            } else {
                resultFuture.complete(Collections.singleton(cachedRow));
            }
            return;
        }
        LOG.debug("get row from cache failed, async get row{}...", row);
        GetRowRequest getRowRequest = new GetRowRequest();
        getRowRequest.setRowQueryCriteria(createCriteria(row));

        OtsAsyncCallback callback = new OtsAsyncCallback(resultFuture, key);
        OtsAsyncClientProvider clientProviderInstance = getAsyncClientProviderInstance();
        AsyncClient asyncClient = clientProviderInstance.getClient();
        asyncClient.getRow(getRowRequest, callback);
    }

    @Override
    public void openConnection(Configuration parameters) {
        super.openConnection(parameters);
    }

    @Override
    public void closeConnection() {
        super.closeConnection();
        try {
            if (null != asyncProvider) {
                asyncProvider.shutdownAndSetNull();
            }
        } catch (Exception e) {
            LOG.warn("ignore exception when shutdown async client", e);
        }
    }

    @VisibleForTesting
    public void setAsyncClientProvider(OtsAsyncClientProvider asyncProvider) {
        this.asyncProvider = asyncProvider;
    }

    @VisibleForTesting
    public synchronized OtsAsyncClientProvider getAsyncClientProviderInstance() {
        if (null == asyncProvider) {
            asyncProvider = createProvider();
        }
        return asyncProvider;
    }

    private OtsAsyncClientProvider createProvider() {

        OtsAsyncClientProvider clientProvider = OtsAsyncClientProvider.create(param);

        LOG.info(
                "Init OTS client successfully with configuration: callbackThreadPoolSize={}, "
                        + "ioThreadCount={}, connectionTimeoutMs={}, socketTimeoutMs={}",
                param.getCallbackThreadPoolSize(),
                param.getIoThreadCount(),
                param.getConnectionTimeoutMs(),
                param.getSocketTimeoutMs());
        return clientProvider;
    }

    private class OtsAsyncCallback implements TableStoreCallback<GetRowRequest, GetRowResponse> {

        private final ResultFuture<RowData> resultFuture;
        private final Object key;

        private OtsAsyncCallback(ResultFuture<RowData> resultFuture, GenericRowData key) {
            this.resultFuture = resultFuture;
            this.key = key;
        }

        @Override
        public void onCompleted(GetRowRequest req, GetRowResponse res) {
            try {
                com.alicloud.openservices.tablestore.model.Row responseRow = res.getRow();
                if (responseRow != null) {
                    RowData rowData = responseRowToRowData(responseRow);
                    cache.put(key, rowData);
                    resultFuture.complete(Collections.singleton(rowData));
                } else {
                    if (cacheStrategy.isCacheEmpty()) {
                        cache.put(key, NULL_ROWDATA);
                    }
                    resultFuture.complete(Collections.emptyList());
                }
            } catch (Exception e) {
                resultFuture.completeExceptionally(e);
            }
        }

        @Override
        public void onFailed(GetRowRequest req, Exception ex) {
            resultFuture.completeExceptionally(ex);
        }
    }
}
