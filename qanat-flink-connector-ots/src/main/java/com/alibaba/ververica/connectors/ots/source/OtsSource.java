/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.ververica.connectors.ots.source;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.connector.source.Boundedness;
import org.apache.flink.api.connector.source.Source;
import org.apache.flink.api.connector.source.SourceReader;
import org.apache.flink.api.connector.source.SourceReaderContext;
import org.apache.flink.api.connector.source.SplitEnumerator;
import org.apache.flink.api.connector.source.SplitEnumeratorContext;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.typeutils.ResultTypeQueryable;
import org.apache.flink.connector.base.source.reader.RecordsWithSplitIds;
import org.apache.flink.connector.base.source.reader.synchronization.FutureCompletingBlockingQueue;
import org.apache.flink.core.io.SimpleVersionedSerializer;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.runtime.typeutils.InternalTypeInfo;
import org.apache.flink.table.types.logical.RowType;

import com.alibaba.ververica.connectors.ots.source.enumerator.OtsSourceEnumState;
import com.alibaba.ververica.connectors.ots.source.enumerator.OtsSourceEnumStateSerializer;
import com.alibaba.ververica.connectors.ots.source.enumerator.OtsSourceEnumerator;
import com.alibaba.ververica.connectors.ots.source.reader.OtsRecordEmitter;
import com.alibaba.ververica.connectors.ots.source.reader.OtsSourceReader;
import com.alibaba.ververica.connectors.ots.source.reader.OtsSplitReader;
import com.alibaba.ververica.connectors.ots.source.split.OtsInputSplit;
import com.alibaba.ververica.connectors.ots.source.split.OtsInputSplitSerializer;
import com.alibaba.ververica.connectors.ots.source.tunnel.OtsTunnelApi;
import com.alibaba.ververica.connectors.ots.source.tunnel.OtsTunnelApiImpl;

import java.util.List;
import java.util.function.Supplier;

import static org.apache.flink.api.connector.source.Boundedness.CONTINUOUS_UNBOUNDED;

/** OtsSource. */
public class OtsSource
        implements Source<RowData, OtsInputSplit, OtsSourceEnumState>,
                ResultTypeQueryable<RowData> {

    private static final long serialVersionUID = 1L;

    private TypeInformation<RowData> rowTypeInfo;
    private StreamRecordParser parser;
    private OtsConnectionParams param;
    private OtsTunnelApi tunnelApi;

    public OtsSource(RowType rowType, OtsConnectionParams param, List<String> metadataKeys) {
        this.rowTypeInfo = InternalTypeInfo.of(rowType);
        this.parser = new StreamRecordParser(rowType, param.getProperties(), metadataKeys);
        this.tunnelApi = new OtsTunnelApiImpl(param, true);
        this.param = param;
    }

    @Override
    public Boundedness getBoundedness() {
        return CONTINUOUS_UNBOUNDED;
    }

    @Override
    public SourceReader<RowData, OtsInputSplit> createReader(SourceReaderContext readerContext) {
        FutureCompletingBlockingQueue<RecordsWithSplitIds<Tuple3<RowData, Long, Long>>>
                elementsQueue = new FutureCompletingBlockingQueue<>();
        OtsRecordEmitter<RowData> recordEmitter = new OtsRecordEmitter<>();
        Supplier<OtsSplitReader<RowData>> splitReaderSupplier =
                () -> new OtsSplitReader<>(tunnelApi, param, parser, readerContext);
        return new OtsSourceReader(
                tunnelApi,
                elementsQueue,
                splitReaderSupplier,
                recordEmitter,
                param.getProperties(),
                readerContext);
    }

    @Override
    public SplitEnumerator<OtsInputSplit, OtsSourceEnumState> createEnumerator(
            SplitEnumeratorContext<OtsInputSplit> splitEnumeratorContext) {
        return new OtsSourceEnumerator(splitEnumeratorContext, tunnelApi);
    }

    @Override
    public SplitEnumerator<OtsInputSplit, OtsSourceEnumState> restoreEnumerator(
            SplitEnumeratorContext<OtsInputSplit> splitEnumeratorContext,
            OtsSourceEnumState otsSourceEnumState) {
        return new OtsSourceEnumerator(splitEnumeratorContext, tunnelApi, otsSourceEnumState);
    }

    @Override
    public SimpleVersionedSerializer<OtsInputSplit> getSplitSerializer() {
        return new OtsInputSplitSerializer();
    }

    @Override
    public SimpleVersionedSerializer<OtsSourceEnumState> getEnumeratorCheckpointSerializer() {
        return new OtsSourceEnumStateSerializer();
    }

    @Override
    public TypeInformation<RowData> getProducedType() {
        return rowTypeInfo;
    }
}
