connector:
  type: ots
  packaged: true
  source: false
  sink: true
  lookup: true
  environments:
    - PUBLIC_CLOUD
    - PRIVATE_CLOUD
    - ALIBABA_GROUP
  properties:
    - key: connector
      required: true
      description: Must be set to 'ots' to configure this connector.
    - key: instanceName
      required: true
      description: The instance name of ots.
    - key: tableName
      required: true
      description: The table name of ots.
    - key: tunnelName
      required: false
      description: The tunnel name of ots table.
    - key: endPoint
      required: true
      description: The end point of ots. Instance access address.
    - key: accessId
      required: false
      description: AccessKey ID.
      sensitive: true
    - key: accessKey
      required: false
      description: AccessKey Secret.
      sensitive: true
    - key: valueColumns
      required: false
      description: Required when sinking to ots. Specify the name of the field to be inserted. Multiple fields are separated by commas (,), such as 'id,name'.
    - key: ioThreadCount
      required: false
      description: The number of IO threads when creates Ots Client. The default value is 4.
    - key: callbackThreadPoolSize
      required: false
      description: The maximum number of IO threads in thread pool when creates Ots Client. The default value is 4.
    - key: bufferSize
      required: false
      description: How many records are flowed in and output is started. The default value is 5000, which means that the output will start when the input data reaches 5000.
    - key: batchWriteTimeoutMs
      required: false
      description: Time to write timeout. The unit is MS and the default value is 5000. Indicates that if the data in the cache still fails to meet the output conditions after waiting for 5 seconds, the system will automatically output all the data in the cache.
    - key: batchSize
      required: false
      description: The number of pieces written in batch at a time. The default value is 100.
    - key: retryIntervalMs
      required: false
      description: The time interval between retries, in milliseconds. The default value is 1000.
    - key: maxRetryTimes
      required: false
      description: The maximum number of retries. The default value is 10.
    - key: ignoreDelete
      required: false
      description: Whether to ignore the delete operation. The default value is false.
    - key: connectTimeout
      required: false
      description: Timeout time for connector to connect to the tablestore, in milliseconds. The default value is 30000 (30 seconds).
    - key: socketTimeout
      required: false
      description: The socket timeout time of the connector connecting to the tablestore, in milliseconds. The default value is 30000 (30 seconds).
    - key: overwriteMode
      required: false
      description: Required when sinking to ots. Use "update" to write part of columns in a row. Use "put" to overwrite whole row. The default value is "put".