package com.aliyun.qanat.flink.connectors.sink;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.flink.table.data.RowData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.qanat.flink.connectors.AuthCenter;
import com.aliyun.wormhole.qanat.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.datasource.RdsConnectionParam;

public class AuthCenterSinkFunction extends RichSinkFunction<RowData> {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -4224992614418251706L;

	private static final Logger log = LoggerFactory.getLogger(AuthCenterSinkFunction.class);
	
	private AuthCenter conf;
	
    private Connection conn = null;
    private RdsConnectionParam param;
    private int retryIntervalMs = 200;
    private int maxRetryTime = 3;
	private String tableName;
	
	public AuthCenterSinkFunction(AuthCenter conf) {
		this.conf = conf;
	}

	public void open(Configuration parameters) throws Exception {
        super.open(parameters);
		boolean initSuccess = false;
        String lastErrorMsg = null;

        String jdbcUrl = conf.getJdbcUrl();
        String username = conf.getUsername();
        String password = conf.getPassword();
        tableName = conf.getTableName();
        
        int retris = 0;
        while (retris < 10) {
	        try {
	            synchronized (AuthCenterSinkFunction.class) {
	                param = new RdsConnectionParam();
	                param.setUrl(jdbcUrl)
	        	        .setUserName(username)
	        	        .setPassword(password);
	            	conn = QanatDatasourceHandler.connectToTable(param);
	            }
	            initSuccess = true;
	            break;
	        } catch (Exception e) {
	            retris++;
	            lastErrorMsg = e.getMessage();
	            log.error("sink open failed {} times, error:{}", retris, e.getMessage(), e);
	            try {
					Thread.sleep(100);
				} catch (InterruptedException e1) {}
	        }
        }
        if (!initSuccess) {
            log.error("AuthCenterSinkFunction open failed:{}, then try to failover", lastErrorMsg);
        	throw new RuntimeException("AuthCenterSinkFunction open failed:" + lastErrorMsg + ", then try to failover");
        }
	}
	
	public void invoke(RowData record, Context context) {
		String traceId = UUID.randomUUID().toString();
        try {
			String bt = new String(record.getString(0).toBytes(), "UTF-8");
            String bi = new String(record.getString(1).toBytes(), "UTF-8");
			String authData = new String(record.getString(2).toBytes(), "UTF-8");
			if (record.getArity() > 3) {
				traceId = new String(record.getString(3).toBytes(), "UTF-8");
			}
    		log.info("{} bt={} bi={} authData={}", traceId, bt, bi, authData);

    		int retryTime = 0;
    		while (retryTime < maxRetryTime) {
				Statement statement = null;
    			try {
    	            if (conn == null || conn.isClosed()) {
		                long startTsConn = System.currentTimeMillis();
    	            	conn = QanatDatasourceHandler.connectToTable(param);
			            log.info("{} retry:{} cost={}", traceId, retryTime, System.currentTimeMillis()-startTsConn);
    	            }
					conn.setAutoCommit(false);
		            statement = conn.createStatement();

					String deleteByBiSql = "DELETE FROM " + tableName + " WHERE bt='" + bt + "' AND bi='" + bi + "'";
					log.info("{} deleteByBiSql={}", traceId, deleteByBiSql);
					long delStartTs = System.currentTimeMillis();
					int execCnt = statement.executeUpdate(deleteByBiSql);
					log.info("{} deleteByBiSql execCnt={} cost={}", traceId, execCnt, System.currentTimeMillis() - delStartTs);

					List<Auth> authList = new ArrayList<>();
					JSONArray authDataArray = JSON.parseArray(authData);
					if (authDataArray != null && authDataArray.size() > 0) {
						for (int i = 0; i < authDataArray.size(); i++) {
							JSONObject authDataJson = authDataArray.getJSONObject(i);
							Auth auth = new Auth();
							auth.setId(authDataJson.getString("id"));
							auth.setBid(authDataJson.getString("bid"));
							auth.setBi(authDataJson.getString("bi"));
							auth.setRt(authDataJson.getString("rt"));
							auth.setBt(authDataJson.getString("bt"));
							auth.setE(authDataJson.getString("e"));
							auth.setGmtCreate(authDataJson.getString("gmt_create"));
							authList.add(auth);
						}

						String batchInsertSql = getBatchInsertSql(authList);
						log.info("{} batchInsertSql={}", traceId, batchInsertSql);
						long insStartTs = System.currentTimeMillis();
						execCnt = statement.executeUpdate(batchInsertSql);
						log.info("{} batchInsertSql execCnt={} cost={}", traceId, execCnt, System.currentTimeMillis() - insStartTs);
					}
					conn.commit();
	                log.info("{} AuthCenterSinkFunction finish", traceId);
		            break;
    			} catch (SQLException e) {
    				log.error("{} sqlexception with retry:{} error:{}", traceId, retryTime, e.getMessage(), e);
					conn.rollback();
					retryTime++;
					if (retryTime == maxRetryTime) {
						throw new RuntimeException(e);
					}
    				try {
						Thread.sleep(retryIntervalMs * retryTime);
    				} catch (Exception e1) {
    					//ignore
    				}
    			} finally {
    	            if (statement != null) {
    	                try {
    	                    statement.close();
    	                } catch (SQLException e) {
    	                	log.error("{} statement close failed", traceId, e);
    	                }
    	            }
    	            if (conn != null) {
    	                try {
    	                	conn.close();
    	                } catch (SQLException e) {
    	                	log.error("{} connection close failed", traceId, e);
    	                }
    	            }

    			}
    		}
        } catch(Exception e) {
            log.error("{} AuthCenterSinkFunction failed:{}", traceId, e.getMessage(), e);
        }
	}

	private String getBatchInsertSql(List<Auth> authList) {
		List<String> fieldNameList = Arrays.asList("`id`", "`bid`", "`gmt_create`", "`rt`", "`e`", "`bt`", "`bi`");
		List<String> insertValues = new ArrayList<>();
		for (Auth auth : authList) {
			List<String> fieldValueList = Arrays.asList("'" + auth.getId() + "'","'" + auth.getBid() + "'","'" + auth.getGmtCreate() + "'","'" + auth.getRt() + "'", "'" + auth.getE() + "'", "'" + auth.getBt() + "'", "'" + auth.getBi() + "'");
			insertValues.add("(" + StringUtils.join(fieldValueList, ",") + ")");
		}
		return String.format("INSERT INTO %s(%s) VALUES %s", tableName, StringUtils.join(fieldNameList, ","),  StringUtils.join(insertValues, ","));
	}

	@Data
	public static class Auth {
		private String id;
		private String bid;
		private String gmtCreate;
		private String rt;
		private String e;
		private String bt;
		private String bi;
	}
}