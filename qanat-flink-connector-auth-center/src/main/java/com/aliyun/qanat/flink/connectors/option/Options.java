package com.aliyun.qanat.flink.connectors.option;

import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.configuration.ConfigOptions;

public class Options {
	public static final ConfigOption<String> JDBC_URL = ConfigOptions.key("jdbcUrl")
            .stringType()
            .noDefaultValue();
	
	public static final ConfigOption<String> USERNAME = ConfigOptions.key("username")
            .stringType()
            .noDefaultValue();
	public static final ConfigOption<String> PASSWORD = ConfigOptions.key("password")
            .stringType()
            .noDefaultValue();

    public static final ConfigOption<String> TABLE_NAME = ConfigOptions.key("table_name")
            .stringType()
            .noDefaultValue();
}