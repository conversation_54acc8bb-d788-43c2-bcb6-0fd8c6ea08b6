package com.aliyun.qanat.flink.connectors.sink;

import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.table.connector.sink.DynamicTableSink;
import org.apache.flink.table.connector.sink.SinkFunctionProvider;
import org.apache.flink.table.types.DataType;

import com.aliyun.qanat.flink.connectors.AuthCenter;

public class AuthCenterTableSink implements DynamicTableSink {
	
	private final DataType dataType;
    private final AuthCenter conf;
	
	public AuthCenterTableSink(DataType dataType, AuthCenter conf) {
		this.dataType = dataType;
        this.conf = conf;
	}

	@Override
	public String asSummaryString() {
		return "Sink For Auth Center";
	}

	@Override
	public DynamicTableSink copy() {
		return new AuthCenterTableSink(dataType, conf);
	}

	@Override
	public ChangelogMode getChangelogMode(ChangelogMode requestedMode) {
		return requestedMode;
	}

	@Override
	public SinkRuntimeProvider getSinkRuntimeProvider(Context context) {
    	final AuthCenterSinkFunction sourceFunction = new AuthCenterSinkFunction(conf);
		return SinkFunctionProvider.of(sourceFunction);
	}

}
