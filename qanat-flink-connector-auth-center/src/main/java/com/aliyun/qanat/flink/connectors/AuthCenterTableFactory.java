package com.aliyun.qanat.flink.connectors;

import java.util.HashSet;
import java.util.Set;

import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.configuration.ReadableConfig;
import org.apache.flink.table.connector.sink.DynamicTableSink;
import org.apache.flink.table.factories.DynamicTableSinkFactory;
import org.apache.flink.table.factories.FactoryUtil;
import org.apache.flink.table.types.DataType;

import com.aliyun.qanat.flink.connectors.option.Options;
import com.aliyun.qanat.flink.connectors.sink.AuthCenterTableSink;

public class AuthCenterTableFactory implements DynamicTableSinkFactory {

	@Override
	public String factoryIdentifier() {
		return "auth-center";
	}

	@Override
	public Set<ConfigOption<?>> optionalOptions() {
		final Set<ConfigOption<?>> options = new HashSet<>();
		return options;
	}

	@Override
	public Set<ConfigOption<?>> requiredOptions() {
		final Set<ConfigOption<?>> options = new HashSet<>();
		options.add(Options.JDBC_URL);
		options.add(Options.USERNAME);
		options.add(Options.PASSWORD);
		options.add(Options.TABLE_NAME);
		return options;
	}

	@Override
	public DynamicTableSink createDynamicTableSink(Context context) {
		final FactoryUtil.TableFactoryHelper helper = FactoryUtil.createTableFactoryHelper(this, context);
        helper.validate();
        final ReadableConfig options = helper.getOptions();

        AuthCenter conf = new AuthCenter();
        conf.setJdbcUrl(options.get(Options.JDBC_URL));
        conf.setUsername(options.get(Options.USERNAME));
        conf.setPassword(options.get(Options.PASSWORD));
        conf.setTableName(options.get(Options.TABLE_NAME));
        
        final DataType producedDataType = context.getCatalogTable().getResolvedSchema().toPhysicalRowDataType();
		return new AuthCenterTableSink(producedDataType, conf);
	}
}