<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
        <groupId>com.aliyun.wormhole</groupId>
        <artifactId>qanat-aliyun-inc-com</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>qanat-aliyun-inc-com-service</artifactId>
    <packaging>jar</packaging>
    <name>qanat-aliyun-inc-com-service</name>

    <dependencies>
       <dependency>
            <groupId>com.aliyun.datahub</groupId>
            <artifactId>aliyun-sdk-datahub</artifactId>
            <version>2.9.2-public</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-alimonitor-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-hsf-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-diamond-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-acl-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-web</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.spring</groupId>
            <artifactId>security-extras</artifactId>
        </dependency>
        <dependency>
        	<groupId>com.aliyun.wormhole</groupId>
        	<artifactId>qanat-service-api</artifactId>
        	<version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastsql</groupId>
            <artifactId>fastsql</artifactId>
        </dependency>
        <dependency>
          <groupId>com.alibaba.schedulerx</groupId>
          <artifactId>schedulerx2-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
          <groupId>com.taobao.drc</groupId>
          <artifactId>client</artifactId>
        </dependency>
        <dependency>
	        	<groupId>com.aliyun.wormhole</groupId>
	        	<artifactId>qanat-service-dao</artifactId>
	        	<version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.odps</groupId>
            <artifactId>odps-sdk-core-internal</artifactId>
            <exclusions>
                <exclusion>
	                <groupId>com.google.protobuf</groupId>
	                <artifactId>protobuf-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-foas</artifactId>
        </dependency>
        <dependency>
          <groupId>org.codehaus.groovy</groupId>
          <artifactId>groovy-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.14.2</version>
        </dependency>
        
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.24</version>
        </dependency>
        <dependency>
        	<groupId>com.aliyun.wormhole</groupId>
        	<artifactId>qanat-openapi</artifactId>
        	<version>1.0.6</version>
        </dependency>
        <dependency>
		  <groupId>com.alibaba.security</groupId>
		  <artifactId>security-all</artifactId>
		  <version>1.2-SNAPSHOT</version>
          <exclusions>
              <exclusion>
                  <groupId>com.alibaba.securitysdk</groupId>
                  <artifactId>fastjson-shade</artifactId>
              </exclusion>
          </exclusions>
		</dependency>
		<dependency>
		    <groupId>com.aliyun</groupId>
		    <artifactId>aliyun-java-sdk-alikafka</artifactId>
		    <version>1.2.4</version>
		</dependency>
        <dependency>
		    <groupId>org.apache.kafka</groupId>
		    <artifactId>kafka-clients</artifactId>
    		<version>2.2.0.7-inner-SNAPSHOT</version>
		</dependency>
		<!-- MDP -->
		<dependency>
		  <groupId>com.aliyun.devata</groupId>
		   <artifactId>xtag-api</artifactId>
		  <version>1.0.15</version>
		</dependency>
		<dependency>
		   <groupId>com.aliyun.tag</groupId>
		   <artifactId>tag-api</artifactId>
		   <version>1.14.3</version>
		</dependency>
		<dependency>
		  <groupId>commons-collections</groupId>
		  <artifactId>commons-collections</artifactId>
	     </dependency>
        <dependency>
            <groupId>com.taobao.ateye</groupId>
            <artifactId>ateye-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.csp</groupId>
            <artifactId>sentinel</artifactId>
        </dependency>
        <dependency>
		    <groupId>org.postgresql</groupId>
		    <artifactId>postgresql</artifactId>
		</dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
		<dependency>
		   <groupId>com.aliyun</groupId>
		   <artifactId>aliyun-java-sdk-ververica</artifactId>
		</dependency>
		 <dependency>
		   <groupId>com.aliyun</groupId>
		   <artifactId>ververica-common</artifactId>
		 </dependency>
		 <dependency>
		  <groupId>com.aliyun.devata.customer</groupId>
		  <artifactId>customer-pool-shared</artifactId>
		</dependency>
		<dependency>
		  <groupId>com.aliyun.dataworks</groupId>
		  <artifactId>aliyun-java-sdk-dataworks</artifactId>
		</dependency>
		<!--引入RASS包-->
		<dependency>
		  <groupId>com.aliyun.securitysdk</groupId>
		  <artifactId>rass-spring-context</artifactId>
		  <!--如果之前接入过RASS，这里需要更新一下版本-->
		  <version>2.6.2</version>
		</dependency>
		<!--引入RASP扩展-->
		<dependency>
		  <groupId>com.aliyun.securitysdk</groupId>
		  <artifactId>rass-rasp-extension</artifactId>
		  <version>2.6.2</version>
		</dependency>
		<!--引入安全包-->
		<dependency>
		  <groupId>com.aliyun.securitysdk</groupId>
		  <artifactId>rass-sdk-core</artifactId>
		  <version>2.6.2</version>
		</dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>tablestore</artifactId>
            <version>5.16.0</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun.inner</groupId>
            <artifactId>ververica_inner20220718</artifactId>
            <version>1.2.2</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>tea-openapi</artifactId>
            <version>0.2.6</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>tea</artifactId>
            <version>1.2.1</version>
        </dependency>
    </dependencies>
</project>