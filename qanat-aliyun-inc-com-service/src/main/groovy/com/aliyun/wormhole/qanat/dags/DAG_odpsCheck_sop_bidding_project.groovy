package com.aliyun.wormhole.qanat.dags

import com.alibaba.fastjson.*
import com.aliyun.wormhole.qanat.api.dag.*
import java.util.*

    Dag dag = new Dag("DAG_sync_dwd_devata_bidding_project_info_odps");
    
    DataXV2Node node1 = new DataXV2Node("DataX_sync_mysql_sop_bidding_project_odps", dag);
    node1.setSrcDsName("mysql_sop_bidding_project");
    node1.setDstDbName("qanat_odps");
    node1.setDstTableName("ods_sop_bidding_project_df");
    
    DataXV2Node node2 = new DataXV2Node("DataX_sync_mysql_sop_project_base_odps", dag);
    node2.setSrcDsName("mysql_sop_project_base");
    node2.setDstDbName("qanat_odps");
    node2.setDstTableName("ods_sop_project_base_df");
    
    DataXV2Node node3 = new DataXV2Node("DataX_sync_mysql_crm_customer_base_info_odps", dag);
    node3.setSrcDsName("mysql_crm_customer_base_info");
    node3.setDstDbName("qanat_odps");
    node3.setDstTableName("ods_crm_customer_base_info_df");
    
    DataXV2Node node4 = new DataXV2Node("DataX_sync_mysql_comb_opportunity_sales_relation_odps", dag);
    node4.setSrcDsName("mysql_comb_opportunity_sales_relation");
    node4.setDstDbName("qanat_odps");
    node4.setDstTableName("ods_comb_opportunity_sales_relation_df");
    
    DataXV2Node node5 = new DataXV2Node("DataX_sync_mysql_sop_project_followup_odps", dag);
    node5.setSrcDsName("mysql_sop_project_followup");
    node5.setDstDbName("qanat_odps");
    node5.setDstTableName("ods_sop_project_followup_df");
    
    DataXV2Node node6 = new DataXV2Node("DataX_sync_mysql_comb_opportunity_info_odps", dag);
    node6.setSrcDsName("mysql_comb_opportunity_info");
    node6.setDstDbName("qanat_odps");
    node6.setDstTableName("ods_comb_opportunity_info_df");
    
    DataXV2Node node7 = new DataXV2Node("DataX_sync_adb3_devata_person_team_relation_odps", dag);
    node7.setSrcDsName("adb3_devata_person_team_relation");
    node7.setDstDbName("qanat_odps");
    node7.setDstTableName("dwd_devata_person_team_relation_df");
    
    DataXV2Node node8 = new DataXV2Node("DataX_sync_adb3_dwd_devata_bidding_project_info_odps", dag);
    node8.setSrcDsName("adb3_dwd_devata_bidding_project_info");
    node8.setDstDbName("qanat_odps");
    node8.setDstTableName("dwd_devata_bidding_project_info_df");
    
    return dag;