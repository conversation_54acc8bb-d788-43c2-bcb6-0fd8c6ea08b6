package com.aliyun.wormhole.qanat.dags

import com.alibaba.fastjson.*
import com.aliyun.wormhole.qanat.api.dag.*
import java.util.*

	Dag dag = new Dag("Dag_refresh_daily_authorize_relation_record_v6");
	dag.setTimeExpression("0 30 6 * * ?");
	
	Adb3SqlNode clearNode = new Adb3SqlNode("Adb3Sql_clear_authorize_relation_record", dag);
	clearNode.setDbName("devata_rtdw");
	clearNode.setSql("truncate table tmp_authorize_relation_record");
	
	BlinkBatchNode fullSyncNode = new BlinkBatchNode("blinkbatch_fullsync_authorize_relation_record", dag);
	fullSyncNode.setJobName("fullsync_authorize_relation_record");
	fullSyncNode.setDataBaseline(true);
	
	clearNode.setNext(fullSyncNode);
	
	StopBlinkJobNode stopIncrSyncNode = new StopBlinkJobNode("Stop_incrsync_authorize_relation_record", dag);
	stopIncrSyncNode.setJobNames("incrsync_authorize_relation_record_cid_sales_rel,incrsync_authorize_relation_record_cid_arch_svc_rel,incrsync_authorize_relation_record_bpid_followup_rel");
	
	fullSyncNode.setNext(stopIncrSyncNode);
	
	Adb3MultiSqlNode renameNode = new Adb3MultiSqlNode("AdbSql_rename_authorize_relation_record", dag);
	renameNode.setDbName("devata_rtdw");
	renameNode.setSql("alter table authorize_relation_record rename to bak_authorize_relation_record;alter table tmp_authorize_relation_record rename to authorize_relation_record;alter table bak_authorize_relation_record rename to tmp_authorize_relation_record");
	
	stopIncrSyncNode.setNext(renameNode);
	
	BlinkStreamNode incrSyncNode1 = new BlinkStreamNode("blinkstream_refresh_incrsync_authorize_relation_record_cid_sales_rel", dag);
	incrSyncNode1.setJobName("incrsync_authorize_relation_record_cid_sales_rel");
	
	BlinkStreamNode incrSyncNode2 = new BlinkStreamNode("blinkstream_refresh_incrsync_authorize_relation_record_cid_arch_svc_rel", dag);
	incrSyncNode2.setJobName("incrsync_authorize_relation_record_cid_arch_svc_rel");
	
	BlinkStreamNode incrSyncNode3 = new BlinkStreamNode("blinkstream_refresh_incrsync_authorize_relation_record_bpid_followup_rel", dag);
	incrSyncNode3.setJobName("incrsync_authorize_relation_record_bpid_followup_rel");
	
	renameNode.setNext(incrSyncNode1);
	renameNode.setNext(incrSyncNode2);
	renameNode.setNext(incrSyncNode3);
	
	return dag;