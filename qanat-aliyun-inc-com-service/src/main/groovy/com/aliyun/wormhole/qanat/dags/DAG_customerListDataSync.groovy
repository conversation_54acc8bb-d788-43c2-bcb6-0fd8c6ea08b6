package com.aliyun.wormhole.qanat.dags

import com.alibaba.fastjson.*
import com.aliyun.wormhole.qanat.api.dag.*
import java.util.*

    Dag dag = new Dag("DAG_customer_list_v14");
	dag.setTimeExpression("1 0 6 * * ?");
            
    Adb3SqlNode clearNode = new Adb3SqlNode("Adb3Sql_clearData", dag);
    clearNode.setDsName("adb3_dwd_devata_cid_info");
    clearNode.setSql("truncate table tmp_dwd_devata_cid_info");
    
    Adb3SqlNode fullSyncNode = new Adb3SqlNode("Adb3Sql_fullSync", dag);
    fullSyncNode.setDsName("adb3_dwd_devata_cid_info");
    fullSyncNode.setDataBaseline(true);
    fullSyncNode.setSql(
        "replace into tmp_dwd_devata_cid_info(cid,name,type,super_biz_category,biz_category,biz_category_sub,biz_category_info,country,province,city,district,area_info,is_formal,is_del,gc_level " + 
        "  ,customer_id,customer_name,customer_pool_id,customer_pool_name,sales_empid,sales_teamid,sales_teampath,sales_empinfo,svc_empid,svc_teamid,svc_empinfo,arch_empid,arch_teamid,arch_empinfo,opp_category,uid,aliyun_id,contact_tel,gaap_d,gaap_ld,gaap_d_diff,gaap_d_rate,prv_gaap_d,prv_gaap_ld,prv_gaap_d_diff,prv_gaap_d_rate,pub_gaap_d,pub_gaap_ld,pub_gaap_d_diff,pub_gaap_d_rate,contract_cnt,contract_list,contract_expire_cnt,contract_expire_list,is_contract_expire_user,overdue_uid_cnt,overdue_uid_list,unpay_amount,faq_cnt,faq_list,order_cnt,order_list,is_unpay_user_3d,fin_gaap_s1,prv_fin_gaap_s1,pub_fin_gaap_s1,fin_gaap_s2,prv_fin_gaap_s2,pub_fin_gaap_s2,last_month_gaap,last_2th_gaap,month_inc_rate,gc_level_m,project_cnt,project_content,is_credit_user,credit_warning_uid_cnt,credit_warning_uid_list,is_credit_unpay,create_empid)  " + 
        "select  " + 
        "  a.cid,  " + 
        "  a.name,  " + 
        "  a.type,  " + 
        "  a.super_biz_category,  " + 
        "  a.biz_category,  " + 
        "  a.biz_category_sub,  " + 
        "  (IFNULL(a.super_biz_category,'')||'|'||IFNULL(a.biz_category,'')||'|'||IFNULL(a.biz_category_sub,'')) as biz_category_info,  " + 
        "  a.country,  " + 
        "  a.province,  " + 
        "  a.city,  " + 
        "  a.district,  " + 
        "  (IFNULL(a.country,'')||'|'||IFNULL(a.province,'')||'|'||IFNULL(a.city,'')||'|'||IFNULL(a.district,'')) as area_info,  " + 
        "  a.is_formal,  " + 
        "  a.is_del,  " + 
        "  biz.gc_level,  " + 
        "  s.customer_id,  " + 
        "  s.customer_name,  " + 
        "  cp.customer_pool_id,  " + 
        "  cp.customer_pool_name,  " + 
        "  t.sales_empid,  " + 
        "  t.sales_teamid,  " + 
        "  t.sales_teamid,  " + 
        "  y.sales_empinfo,  " + 
        "  t1.svc_empid,  " + 
        "  t1.svc_teamid,  " + 
        "  v.svc_empinfo,  " + 
        "  t2.arch_empid,  " + 
        "  t2.arch_teamid,  " + 
        "  x.arch_empinfo,  " + 
        "  y.opp_category,  " +
        "  acct.uid,  " +
        "  acct.aliyun_id,  " +
        "  contact.contact_tel,  " +
		"  biz.gaap_d,  " +
		"  biz.gaap_ld,  " +
		"  biz.gaap_d_diff,  " +
		"  biz.gaap_d_rate,  " +
		"  biz.prv_gaap_d,  " +
		"  biz.prv_gaap_ld,  " +
		"  biz.prv_gaap_d_diff,  " +
		"  biz.prv_gaap_d_rate,  " +
		"  biz.pub_gaap_d,  " +
		"  biz.pub_gaap_ld,  " +
		"  biz.pub_gaap_d_diff,  " +
		"  biz.pub_gaap_d_rate,  " +
		"  biz.contract_cnt,  " +
		"  biz.contract_list,  " +
		"  biz.contract_expire_cnt,  " +
		"  biz.contract_expire_list,  " +
		"  biz.is_contract_expire_user,  " +
		"  biz.overdue_uid_cnt,  " +
		"  biz.overdue_uid_list,  " +
		"  biz.unpay_amount,  " +
		"  biz.faq_cnt,  " +
		"  biz.faq_list,  " +
		"  biz.order_cnt,  " +
		"  biz.order_list,  " +
		"  biz.is_unpay_user_3d,  " +
		"  biz.fin_gaap_s1,  " +
		"  biz.prv_fin_gaap_s1,  " +
		"  biz.pub_fin_gaap_s1,  " +
		"  biz.fin_gaap_s2,  " +
		"  biz.prv_fin_gaap_s2,  " +
		"  biz.pub_fin_gaap_s2,  " +
		"  biz.last_month_gaap,  " +
		"  biz.last_2th_gaap,  " +
		"  biz.month_inc_rate,  " +
		"  biz.gc_level_m,  " +
		"  biz.project_cnt,  " +
		"  biz.project_content,  " +
		"  biz.is_credit_user,  " +
		"  biz.credit_warning_uid_cnt,  " +
		"  biz.credit_warning_uid_list,  " +
		"  biz.is_credit_unpay,  " +
		"  a.create_empid  " +
        "from crm_customer_base_info as a  " + 
        "left join (  " + 
        "  select a.cid,  " + 
        "    GROUP_CONCAT(DISTINCT d.empid) as sales_empid,  " + 
        "    REPLACE(GROUP_CONCAT(DISTINCT c.team_path separator ''),' ','') as sales_teamid  " + 
        "  from crm_customer_base_info as a  " + 
        "    inner join comb_opportunity_sales_relation as d on d.cid=a.cid and d.sales_type=0  " + 
        "     left join devata_person_team_relation as c on c.empid=d.empid and c.is_deleted=0 and c.is_admin in (0,4) " + 
        "  group by a.cid  " + 
        ") as t on a.cid = t.cid  " + 
        "left join (  " + 
        "  select a.cid,  " + 
        "    GROUP_CONCAT(DISTINCT d.empid) as svc_empid,  " + 
        "    REPLACE(GROUP_CONCAT(DISTINCT c.team_path separator ''),' ','') as svc_teamid  " + 
        "  from crm_customer_base_info as a  " + 
        "   inner join comb_customer_architect_ralation as d on d.cid=a.cid and d.is_del=0 and d.type=2  " + 
        "     left join devata_person_team_relation as c on c.empid=d.empid and c.is_deleted=0 and c.is_admin in (0,4) " + 
        "  group by a.cid  " + 
        ") as t1 on a.cid = t1.cid  " + 
        "left join (  " + 
        "  select a.cid,  " + 
        "    GROUP_CONCAT(DISTINCT d.empid) as arch_empid,  " + 
        "    REPLACE(GROUP_CONCAT(DISTINCT c.team_path separator ''),' ','') as arch_teamid  " + 
        "  from crm_customer_base_info as a   " + 
        "   inner join comb_customer_architect_ralation as d on d.cid=a.cid and d.is_del=0 and d.type=0 " + 
        "     left join devata_person_team_relation as c on c.empid=d.empid and c.is_deleted=0 and c.is_admin in (0,4) " + 
        "  group by a.cid " + 
        ") as t2 on a.cid = t2.cid " + 
        "left join ( " + 
        "    select  " + 
        "      a.cid, " + 
        "      GROUP_CONCAT(DISTINCT b.to_id) as customer_id, " + 
        "      GROUP_CONCAT(DISTINCT concat(c.name,'|',IFNULL(d.status,''),'|',b.to_id,'|',IFNULL(d.pool_id,''))) as customer_name " + 
        "    from crm_customer_base_info as a  " + 
        "      inner join devata_entity_relation as b on b.relation_type_id in (5,6) and b.is_del=0 and a.cid=b.from_id " + 
        "      inner join devata_customer as c on c.id=b.to_id and c.is_del=0 " + 
        "      left join pool_customer as d on d.customer_id=b.to_id and d.is_del=0 " + 
        "    group by a.cid " + 
        ") as s on a.cid = s.cid " + 
        "left join ( " + 
        "    select  " + 
        "      a.cid, " + 
        "      GROUP_CONCAT(DISTINCT c.pool_id) as customer_pool_id, " + 
        "      GROUP_CONCAT(DISTINCT d.name) as customer_pool_name " + 
        "    from crm_customer_base_info as a  " + 
        "      inner join devata_entity_relation as b on b.relation_type_id in (5,6) and b.is_del=0 and a.cid=b.from_id " + 
        "      inner join pool_customer as c on c.customer_id=b.to_id and c.is_del=0 " + 
        "      inner join customer_pool as d on c.pool_id=d.id and d.is_del=0 " + 
        "    group by a.cid " + 
        ") as cp on a.cid = cp.cid " + 
        "left join ( " + 
        "    select  " + 
        "      a.cid, " + 
        "      GROUP_CONCAT(DISTINCT concat(b.empid,'|',c.emp_name,'|',IFNULL(c.emp_nickname,''),'|',IFNULL(c.team_name_path,''))) as svc_empinfo " + 
        "    from crm_customer_base_info as a  " + 
        "     inner join comb_customer_architect_ralation as b on b.cid=a.cid and b.is_del=0 and b.type=2 " + 
        "     inner join devata_person_team_relation as c on c.empid=b.empid and c.is_deleted=0 and c.is_admin in (0,4) " + 
        "    group by a.cid " + 
        ") as v on a.cid = v.cid " + 
        "left join ( " + 
        "    select  " + 
        "      a.cid, " + 
        "      GROUP_CONCAT(DISTINCT concat(b.empid,'|',c.emp_name,'|',IFNULL(c.emp_nickname,''),'|',IFNULL(c.team_name_path,''))) as arch_empinfo " + 
        "    from crm_customer_base_info as a  " + 
        "     inner join comb_customer_architect_ralation as b on b.cid=a.cid and b.is_del=0 and b.type=0 " + 
        "     inner join devata_person_team_relation as c on c.empid=b.empid and c.is_deleted=0 and c.is_admin in (0,4) " + 
        "    group by a.cid " + 
        ") as x on a.cid = x.cid " + 
        "left join ( " + 
        "    select  " + 
        "      a.cid, " + 
        "      GROUP_CONCAT(DISTINCT IFNULL(b.category,'')) as opp_category, " + 
        "      GROUP_CONCAT(DISTINCT concat(IFNULL(b.category,''),'|',b.empid,'|',c.emp_name,'|',IFNULL(c.emp_nickname,''),'|',IFNULL(c.team_name_path,''),'|',IFNULL(b.owner_status,''),'|',IFNULL(d.lock_time,''), '|', IFNULL(b.gmt_create,''), '|', IFNULL(from_unixtime(d.gmt_plan_close/1000,'%Y-%m-%d %H:%i:%s'),''))) as sales_empinfo " + 
        "    from crm_customer_base_info as a  " + 
        "     inner join comb_opportunity_sales_relation as b on b.cid=a.cid and b.sales_type=0 " + 
        "     inner join devata_person_team_relation as c on c.empid=b.empid and c.is_deleted=0 and c.is_admin=0 " + 
        "     inner join comb_opportunity_info as d on b.opp_id=d.id and d.is_del=0 " + 
        "    group by a.cid " + 
        ") as y on a.cid = y.cid " + 
        "left join ( " + 
        "   select a.cid, group_concat(a.uid) as uid, group_concat(b.aliyunid) as aliyun_id  " + 
        "   from crm_customer_account_relation as a " + 
        "   join crm_account_info as b on a.uid=b.id and a.is_del=0 and b.is_deleted=0 " + 
        "   group by a.cid " + 
        ") as acct on a.cid = acct.cid " + 
        "left join ( " + 
        "   select cid, group_concat(distinct tel) as contact_tel  " + 
        "   from crm_contact_info  " + 
        "   where is_del=0 and tel is not null  " + 
        "   group by cid " + 
        ") as contact on a.cid = contact.cid " +
        "left join ads_aly_sal_cust_general_cid_info as biz on a.cid = biz.cid " + 
        ";");
    
    clearNode.setNext(fullSyncNode);

    BlinkBatchNode tagSyncNode = new BlinkBatchNode("BlinkBatch_cdp_tags_full_sync", dag);
    tagSyncNode.setJobName("cdp_tags_full_sync");

    fullSyncNode.setNext(tagSyncNode);
	
	StopBlinkJobNode stopBlinkNode = new StopBlinkJobNode("Stop_blink_stream", dag);
	stopBlinkNode.setJobNames("customer_list_cid_new_incr_sync,customer_list_cid_base_incr_sync,customer_list_cid_acl_incr_sync,customer_list_cid_pool_rel_incr_sync,customer_list_cid_customer_rel_incr_sync,cdp_tags_sync_online,cdp_tags_sync_online_slow,customer_list_cid_uid_rel_incr_sync,customer_list_cid_contact_tel_incr_sync,customer_list_cid_correct,customer_list_cid_salesempinfo_incr_sync,customer_list_cid_svcempinfo_incr_sync,customer_list_cid_archempinfo_incr_sync");

	tagSyncNode.setNext(stopBlinkNode);
	
    Adb3MultiSqlNode adbSqlNode = new Adb3MultiSqlNode("AdbSql_rename_dwd_devata_cid_info", dag);
    adbSqlNode.setDsName("adb3_dwd_devata_cid_info"); 
    adbSqlNode.setSql("alter table dwd_devata_cid_info rename to dwd_devata_cid_info_backup;alter table tmp_dwd_devata_cid_info rename to dwd_devata_cid_info;alter table dwd_devata_cid_info_backup rename to tmp_dwd_devata_cid_info");

    stopBlinkNode.setNext(adbSqlNode); 

    BlinkStreamNode cidNewNode = new BlinkStreamNode("BlinkSteam_cidNew_incrSync", dag);
    cidNewNode.setJobName("customer_list_cid_new_incr_sync");
    
    BlinkStreamNode cidBaseNode = new BlinkStreamNode("BlinkSteam_cidBase_incrSync", dag);
    cidBaseNode.setJobName("customer_list_cid_base_incr_sync");
    
    BlinkStreamNode cidAclNode = new BlinkStreamNode("BlinkSteam_cidAcl_incrSync", dag);
    cidAclNode.setJobName("customer_list_cid_acl_incr_sync");
    
    BlinkStreamNode cidPoolRelNode = new BlinkStreamNode("BlinkSteam_cidPoolRel_incrSync", dag);
    cidPoolRelNode.setJobName("customer_list_cid_pool_rel_incr_sync");
    
    BlinkStreamNode cidCustomerRelNode = new BlinkStreamNode("BlinkSteam_cidCustomerRel_incrSync", dag);
    cidCustomerRelNode.setJobName("customer_list_cid_customer_rel_incr_sync");
    
    BlinkStreamNode cdpNode = new BlinkStreamNode("BlinkSteam_cdp_tags_sync_online", dag);
    cdpNode.setJobName("cdp_tags_sync_online");
    
    BlinkStreamNode cdpSlowNode = new BlinkStreamNode("BlinkSteam_cdp_tags_sync_online_slow", dag);
    cdpSlowNode.setJobName("cdp_tags_sync_online_slow");
    
    BlinkStreamNode uidNode = new BlinkStreamNode("BlinkSteam_cid_uid_rel_incr_sync", dag);
    uidNode.setJobName("customer_list_cid_uid_rel_incr_sync");
    
    BlinkStreamNode contactNode = new BlinkStreamNode("BlinkSteam_contact_rel_incr_sync", dag);
    contactNode.setJobName("customer_list_cid_contact_tel_incr_sync");
    
    BlinkStreamNode correctNode = new BlinkStreamNode("BlinkSteam_cid_correct", dag);
    cdpSlowNode.setJobName("customer_list_cid_correct");
    
    BlinkStreamNode salesInfoNode = new BlinkStreamNode("BlinkSteam_cid_salesInfo", dag);
    salesInfoNode.setJobName("customer_list_cid_salesempinfo_incr_sync");
    
    BlinkStreamNode svcInfoNode = new BlinkStreamNode("BlinkSteam_cid_svcInfo", dag);
    svcInfoNode.setJobName("customer_list_cid_svcempinfo_incr_sync");
    
    BlinkStreamNode archInfoNode = new BlinkStreamNode("BlinkSteam_cid_archInfo", dag);
    archInfoNode.setJobName("customer_list_cid_archempinfo_incr_sync");
    
    adbSqlNode.setNext(cidNewNode);
    adbSqlNode.setNext(cidAclNode);
    adbSqlNode.setNext(cidBaseNode);
    adbSqlNode.setNext(cidPoolRelNode);
    adbSqlNode.setNext(cidCustomerRelNode);
    adbSqlNode.setNext(cdpNode);
    adbSqlNode.setNext(cdpSlowNode);
    adbSqlNode.setNext(uidNode);
    adbSqlNode.setNext(contactNode);
	adbSqlNode.setNext(correctNode);
	adbSqlNode.setNext(salesInfoNode);
	adbSqlNode.setNext(svcInfoNode);
	adbSqlNode.setNext(archInfoNode);
    
    return dag;