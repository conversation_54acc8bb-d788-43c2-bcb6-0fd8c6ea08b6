package com.aliyun.wormhole.qanat.dags

import com.alibaba.fastjson.*
import com.aliyun.wormhole.qanat.api.dag.*
import java.util.*

    Dag dag = new Dag("DAG_jinrong_contract_info");
            
    Adb3SqlNode clearNode = new Adb3SqlNode("Adb3Sql_clearJinrongContract", dag);
    clearNode.setDsName("adb3_ads_aly_cid_contract_jinrong");
    clearNode.setSql("truncate table ads_aly_cid_contract_jinrong");
    
    BlinkBatchNode contractNode = new BlinkBatchNode("BlinkBatch_jinrong_contract_info_sync", dag);
    contractNode.setJobName("jinrong_contract_info_sync");
    contractNode.setTimeExpression("1 30 7 * * ?");
    
    clearNode.setNext(contractNode);
    
    return dag;