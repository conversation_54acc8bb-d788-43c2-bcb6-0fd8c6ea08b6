package com.aliyun.wormhole.qanat.dags

import com.alibaba.fastjson.*
import com.aliyun.wormhole.qanat.api.dag.*
import java.util.*

Dag dag = new Dag("DAG_mdp_daily_refresh_v1");    
dag.setTimeExpression("1 0 5 * * ?");                

Adb3SqlNode clearNode = new Adb3SqlNode("Adb3Sql_mdp_clearData", dag);    
clearNode.setDsName("adb3_dwd_devata_cid_info");    
clearNode.setSql("truncate table tmp_tag_meta_tag_object_biz_relation");        

Adb3SqlNode fullSyncNode = new Adb3SqlNode("Adb3Sql_mdp_full_sync", dag);    
fullSyncNode.setDsName("adb3_dwd_devata_cid_info");    
fullSyncNode.setSql("INSERT OVERWRITE tmp_tag_meta_tag_object_biz_relation SELECT   concat_ws(     '_',     object_biz_id,     object_domain_code,     object_code,     tag_domain_code,     tag_code,     is_deleted   ) AS `pk`,   `id`,   `gmt_create`,   `gmt_modified`,   `tag_code`,   `tag_domain_code`,   `tag_unique_code`,   `object_code`,   `object_domain_code`,   `object_unique_code`,   `object_biz_id`,   `tag_value`,   `operate_domain_code`,   `expire_date`,   `is_deleted`,   `tag_value_zh`,   `tag_value_en`,   `tag_data_type`,   `biz_operator` FROM   odps_tag_meta_tag_object_biz_relation_external_table WHERE   ds = '#bizDate#'");    
fullSyncNode.setDataBaseline(true);    
clearNode.setNext(fullSyncNode);        

StopBlinkJobNode stopBlinkNode = new StopBlinkJobNode("Stop_mdp_blink_stream", dag);    
stopBlinkNode.setJobNames("adb30_tag_meta_tag_object_biz_relation_incr_sync,drc_tddl_tag_meta_tag_object_biz_relation,tag_meta_tag_object_biz_relation_drc_dispatch");    
fullSyncNode.setNext(stopBlinkNode);        

Adb3MultiSqlNode renameNode = new Adb3MultiSqlNode("AdbSql_rename_tag_meta_tag_object_biz_relation", dag);    
renameNode.setDsName("adb3_dwd_devata_cid_info");     
renameNode.setSql("alter table tag_meta_tag_object_biz_relation rename to bak_tag_meta_tag_object_biz_relation;alter table tmp_tag_meta_tag_object_biz_relation rename to tag_meta_tag_object_biz_relation;alter table bak_tag_meta_tag_object_biz_relation rename to tmp_tag_meta_tag_object_biz_relation");    
stopBlinkNode.setNext(renameNode);     

BlinkStreamNode odsIncr = new BlinkStreamNode("BlinkSteam_mdp_ods_incrSync", dag);    
odsIncr.setJobName("adb30_tag_meta_tag_object_biz_relation_incr_sync");    
odsIncr.setStartTimePolicy("everyday_00_00_00");        

BlinkStreamNode kafkaDrc = new BlinkStreamNode("BlinkSteam_mdp_ods_kafka_drc", dag);    
kafkaDrc.setJobName("drc_tddl_tag_meta_tag_object_biz_relation");    
kafkaDrc.setStartTimePolicy("everyday_00_00_00");    

BlinkStreamNode metaqDrc = new BlinkStreamNode("BlinkSteam_mdp_ods_metaq_drc", dag);    
metaqDrc.setJobName("tag_meta_tag_object_biz_relation_drc_dispatch");    
metaqDrc.setStartTimePolicy("everyday_00_00_00");      

renameNode.setNext(odsIncr);    
renameNode.setNext(kafkaDrc);      
renameNode.setNext(metaqDrc);        

return dag;




