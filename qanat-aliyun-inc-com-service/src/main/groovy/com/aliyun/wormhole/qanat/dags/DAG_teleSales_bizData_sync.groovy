package com.aliyun.wormhole.qanat.dags

import com.alibaba.fastjson.*
import com.aliyun.wormhole.qanat.api.dag.*
import java.util.*

    Dag dag = new Dag("DAG_teleSales_bizData_v1");
	dag.setTimeExpression("1 0 5 * * ?");
            
    Adb3SqlNode clearNode = new Adb3SqlNode("Adb3Sql_clearData", dag);
    clearNode.setDsName("adb3_dwd_devata_cid_info");
    clearNode.setSql("truncate table tmp_ads_aly_sal_phone_cust_cid_info");
    
    BlinkBatchNode fullSyncNode = new BlinkBatchNode("BlinkBatch_telesales_bizdata_full_sync", dag);
    fullSyncNode.setJobName("telesales_bizdata_full_sync");
	fullSyncNode.setDataBaseline(true);

    clearNode.setNext(fullSyncNode);
	
	StopBlinkJobNode stopBlinkNode = new StopBlinkJobNode("Stop_blink_stream", dag);
	stopBlinkNode.setJobNames("latest_project_inc_sync,latest_task_inc_sync,latest_note_inc_sync");

	fullSyncNode.setNext(stopBlinkNode);
	
    Adb3MultiSqlNode renameNode = new Adb3MultiSqlNode("AdbSql_rename_ads_aly_sal_phone_cust_cid_info", dag);
    renameNode.setDsName("adb3_dwd_devata_cid_info"); 
    renameNode.setSql("alter table ads_aly_sal_phone_cust_cid_info rename to ads_aly_sal_phone_cust_cid_info_backup;alter table tmp_ads_aly_sal_phone_cust_cid_info rename to ads_aly_sal_phone_cust_cid_info;alter table ads_aly_sal_phone_cust_cid_info_backup rename to tmp_ads_aly_sal_phone_cust_cid_info");

    stopBlinkNode.setNext(renameNode); 

    BlinkStreamNode latestProjectNode = new BlinkStreamNode("BlinkSteam_latest_project_inc_sync", dag);
    latestProjectNode.setJobName("latest_project_inc_sync");
	latestProjectNode.setStartTimePolicy("everyday_00_00_00");
    
    BlinkStreamNode latestTaskNode = new BlinkStreamNode("BlinkSteam_latest_task_inc_sync", dag);
    latestTaskNode.setJobName("latest_task_inc_sync");
	latestTaskNode.setStartTimePolicy("everyday_00_00_00");
    
    BlinkStreamNode latestNoteNode = new BlinkStreamNode("BlinkSteam_latest_note_inc_sync", dag);
    latestNoteNode.setJobName("latest_note_inc_sync");
	latestNoteNode.setStartTimePolicy("everyday_00_00_00");
    
    renameNode.setNext(latestProjectNode);
    renameNode.setNext(latestTaskNode);
    renameNode.setNext(latestNoteNode);
    
    return dag;