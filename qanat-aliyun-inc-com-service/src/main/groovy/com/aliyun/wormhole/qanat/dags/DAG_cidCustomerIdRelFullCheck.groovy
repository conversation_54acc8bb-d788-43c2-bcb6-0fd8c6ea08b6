package com.aliyun.wormhole.qanat.dags

import com.alibaba.fastjson.*
import com.aliyun.wormhole.qanat.api.dag.*
import java.util.*

Dag dag = new Dag("DAG_ OdpsSql_test");    OdpsNode node7 = new OdpsNode("OdpsSql_test", dag);  node7.setOdpsSql("use aliyun_wormhole;create or replace VIEW  aliyun_wormhole.dwd_devata_bidding_project_info_2 as" +       "  SELECT DISTINCT bid.bpid," +       "       bid.`tender_no`," +       "       bid.`project_amount`," +       "       bid.`bidding_project_name`," +       "       bid.`aliyun_subject`," +       "       bid.`bidding_time`," +       "       bid.`bidding_status`," +       "       bid.`gmt_create`," +       "       bid.`is_deleted`," +       "       bid.`sales_empid`," +       "       bid.`sales_teamid`," +       "       bid.`sales_empinfo`," +       "       ppl.`project_name`," +       "       ppl.`project_cate`," +       "       ppl.`cid`," +       "       cid.`customer_name`" +       "  FROM(" +       "SELECT bid.`bpid`, bid.`tender_no`, bid.`project_amount`, bid.`bidding_project_name`, bid.`aliyun_subject`, bid.`bidding_time`, bid.`bidding_status`, bid.`gmt_create`, bid.`is_deleted`, acl1.`sales_empid`, acl2.`sales_teamid`, acl3.`sales_empinfo`" +       "  FROM(" +       "SELECT bp_id AS bpid, tender_no AS tender_no, project_amount AS project_amount, project_name AS bidding_project_name, aliyun_subject AS aliyun_subject, bidding_time AS bidding_time, bidding_status AS bidding_status, gmt_create AS gmt_create, is_deleted AS is_deleted" +       "  FROM sop_bidding_project) AS bid" +       "  LEFT JOIN(" +       "select a.project_id as bpid, WM_CONCAT(',',a.employ_id) as sales_empid" +       "  from sop_project_followup as a" +       " where a.is_deleted= 0" +       "   and a.biz_role= '1'" +       " group by a.project_id) AS acl1 ON acl1.bpid= bid.bpid" +       "  LEFT JOIN(" +       "select p.id as bpid, WM_CONCAT(',', concat(b.category, '|', b.empid, '|', c.emp_name, '|', c.emp_nickname, '|', c.team_name_path, '|', b.owner_status, '|', d.lock_time, '|', coalesce(b.gmt_create, ''), '|', coalesce(from_unixtime(d.gmt_plan_close/1000), ''))) as sales_empinfo" +       "  from sop_project_base as p" +       "  inner join comb_opportunity_sales_relation as b on b.cid= p.customer_id" +       "   and b.sales_type= 0" +       "  inner join comb_opportunity_info as d on b.opp_id= d.id" +       "   and d.is_del= 0" +       "  inner join devata_person_team_relation as c on c.empid= b.empid" +       "   and c.is_deleted= 0" +       "   and c.is_admin= 0" +       " group by p.id) AS acl3 ON acl3.bpid= bid.bpid" +       "  LEFT JOIN(" +       "select a.project_id as bpid, REPLACE(WM_CONCAT('',  b.team_path), ' ', '') as sales_teamid" +       "  from sop_project_followup as a join devata_person_team_relation as b on a.employ_id= b.empid" +       "   and b.is_deleted= 0" +       "   and b.is_admin in(0, 4)" +       " where a.is_deleted= 0" +       "   and a.biz_role= '1'" +       " group by a.project_id) AS acl2 ON acl2.bpid= bid.bpid) AS bid" +       "  INNER JOIN(" +       "SELECT id AS bpid, project_name AS project_name, project_cate AS project_cate, customer_id AS cid" +       "  FROM sop_project_base) AS ppl ON ppl.bpid= bid.bpid" +       "  INNER JOIN(" +       "SELECT cid AS cid, name AS customer_name" +       "  FROM crm_customer_base_info) AS cid ON cid.cid= ppl.cid");    return dag;