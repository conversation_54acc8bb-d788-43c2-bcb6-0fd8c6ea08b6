package com.aliyun.wormhole.qanat.dags

import java.util.Calendar;
import java.text.SimpleDateFormat;

def execute(Date bidding_time, Integer bidding_status) {
	if (bidding_time == null || bidding_status == null) {
		return 0;
	}
	String fyStart = Calendar.getInstance().get(java.util.Calendar.YEAR) + "0401000000";
	SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
	Date fyStartDate = sdf.parse(fyStart);
	if (bidding_time.compareTo(fyStartDate) < 0) {
		return 0;
	}
	Calendar cal = Calendar.getInstance();
	cal.add(Calendar.DAY_OF_MONTH, -14);
	if (cal.getTime().compareTo(bidding_time) > 0 && bidding_status == 1) {
		return 1;
	} else {
		return 0;
	}
}