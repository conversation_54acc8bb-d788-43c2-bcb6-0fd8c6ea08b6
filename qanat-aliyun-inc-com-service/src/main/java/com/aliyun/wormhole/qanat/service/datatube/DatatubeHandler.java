package com.aliyun.wormhole.qanat.service.datatube;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.common.sdk.common.JobConfigInfo;
import com.alibaba.security.util.StringUtils;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.Adb3ExtTblNode;
import com.aliyun.wormhole.qanat.api.dag.Adb3MultiSqlNode;
import com.aliyun.wormhole.qanat.api.dag.Adb3SqlNode;
import com.aliyun.wormhole.qanat.api.dag.BlinkBatchNode;
import com.aliyun.wormhole.qanat.api.dag.BlinkNode;
import com.aliyun.wormhole.qanat.api.dag.BlinkStreamNode;
import com.aliyun.wormhole.qanat.api.dag.Dag;
import com.aliyun.wormhole.qanat.api.dag.DataXNode;
import com.aliyun.wormhole.qanat.api.dag.HoloExtTblNode;
import com.aliyun.wormhole.qanat.api.dag.Node;
import com.aliyun.wormhole.qanat.api.dag.NodeAction;
import com.aliyun.wormhole.qanat.api.dto.CreateOdsRequest;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.DatasourceRequest;
import com.aliyun.wormhole.qanat.api.dto.OdsSyncTaskRequest;
import com.aliyun.wormhole.qanat.api.dto.OdsSyncTaskResponse;
import com.aliyun.wormhole.qanat.api.dto.TaskInfoRequest;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.TaskService;
import com.aliyun.wormhole.qanat.api.service.ViewModelRequest;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceDsRelation;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceDsRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTaskExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfo;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfoWithBLOBs;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfo;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfo;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelVersionWithBLOBs;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceDsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TenantInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelVersionMapper;
import com.aliyun.wormhole.qanat.service.dag.DagService;
import com.aliyun.wormhole.qanat.service.ods.OdsHandler;
import com.aliyun.wormhole.qanat.service.schedulerx.SchedulerXJobService;
import com.aliyun.wormhole.qanat.service.util.YamlUtil;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelHandler;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Field;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.RelatedDataObject;
import com.aliyuncs.foas.model.v20181111.GetJobResponse;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 管道处理类
 * <AUTHOR>
 * 2019年7月23日
 */
@Slf4j
@Component
public class DatatubeHandler {
    
    @Resource
    private ViewModelHandler viewModelHandler;
    
    @Resource 
    private OdsHandler odsHandler;
    
    @Resource 
    private DatasourceService dsInfoService;
    
    @Resource
    private TaskService dagTaskService;
    
    @Resource 
    private DatatubeInstanceMapper datatubeInstanceMapper;
    
    @Resource 
    private TenantInfoMapper tenantInfoMapper;
    
    @Resource 
    private ViewModelInfoMapper viewModelInfoMapper;
    
    @Resource 
    private DatasourceMapper dsInfoMapper;
    
    @Resource 
    private TaskInfoMapper taskInfoMapper;
    
    @Resource
    private SchedulerXJobService schedulerXJobService;
    
    @Resource
    private DatatubeInstanceDsRelationMapper datatubeInstanceDsRelationMapper;
    
    @Resource
    private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;
    
    @Resource
    private ViewModelVersionMapper viewModelVersionMapper;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource 
    private BlinkService blinkService;
    
    @Resource
    private DagService dagService;

    @Transactional(propagation = Propagation.REQUIRED)
    public Long createOdsDatatube(CreateOdsRequest createOdsReq) {
        log.info("start createOdsDatatube({})", JSON.toJSONString(createOdsReq));
        
    	TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(createOdsReq.getTenantId());
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + createOdsReq.getTenantId() + " is not configured");
    	}
    	
    	Map<String, Long> createOdsResp = odsHandler.createDsInfoAndOdsTask(createOdsReq);
    	if (createOdsResp != null) {
        	Date now = new Date();
        	DatatubeInstance record = new DatatubeInstance();
        	record.setAppName(createOdsReq.getAppName());
        	record.setCode("ods_" + createOdsReq.getTenantId() + "_" + dsInfoService.getDbIdByName(createOdsReq.getTenantId(), tenantList.get(0).getDefaultDw()) + "_" + createOdsReq.getTableName());
        	record.setCreateEmpid(createOdsReq.getOperateEmpid());
        	record.setGmtCreate(now);
        	record.setGmtModified(now);
        	record.setIsDeleted(0L);
        	record.setIsTest(0L);
        	record.setLevel(createOdsReq.getDatatubeLevel());
        	record.setModifyEmpid(createOdsReq.getOperateEmpid());
        	record.setName("ODS datatube from " + createOdsReq.getDbName() + "." + createOdsReq.getTableName() + " to " + tenantList.get(0).getDefaultDw());
        	record.setObjectType(createOdsReq.getObjectType());
        	record.setProvider("ods");
        	record.setProviderId(createOdsResp.get("taskId"));
        	record.setTenantId(createOdsReq.getTenantId());
        	record.setType("ods");
        	datatubeInstanceMapper.insert(record);
        	
        	this.makeupOdsDatatubeTasks(createOdsResp.get("taskId"), createOdsReq.getDatatubeLevel(), createOdsReq.getOperateEmpid());
        	
        	return record.getId();
    	} else {
    		throw new QanatBizException("ods datatube create failed");
    	}
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Long createViewModelDatatube(ViewModelRequest createViewModelReq) {
        log.info("start createViewModelDatatube({})", JSON.toJSONString(createViewModelReq));
    	
    	Long viewModelId = viewModelHandler.createViewModelFromYaml(createViewModelReq);
    	
    	ViewModelInfo vm = viewModelInfoMapper.selectByPrimaryKey(viewModelId);
    	
    	Date now = new Date();
    	DatatubeInstance record = new DatatubeInstance();
    	record.setAppName(createViewModelReq.getAppName());
    	record.setCode(vm.getModelName());
    	record.setCreateEmpid(createViewModelReq.getOperateEmpid());
    	record.setGmtCreate(now);
    	record.setGmtModified(now);
    	record.setIsDeleted(0L);
    	if (createViewModelReq.getIsTest()) {
        	record.setIsTest(1L);
    	} else {
    		record.setIsTest(0L);
    	}
    	record.setLevel(createViewModelReq.getDatatubeLevel());
    	record.setModifyEmpid(createViewModelReq.getOperateEmpid());
    	record.setName(vm.getModelDesc());
    	record.setObjectType(createViewModelReq.getObjectType());
    	record.setProvider("viewmodel");
    	record.setProviderId(viewModelId);
    	record.setTenantId(createViewModelReq.getTenantId());
    	record.setType("dwd");
    	record.setVersion(createViewModelReq.getVersion());
    	datatubeInstanceMapper.insert(record);
    	
    	refreshViewModelDsRelations(createViewModelReq.getTenantId(), record.getId(), createViewModelReq.getOperateEmpid());
    	
    	return record.getId();
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Long modifyViewModelDatatube(ViewModelRequest createViewModelReq) {
        log.info("start createViewModelDatatube({})", JSON.toJSONString(createViewModelReq));
        DatatubeInstanceExample diExample = new DatatubeInstanceExample();
        diExample.createCriteria().andTenantIdEqualTo(createViewModelReq.getTenantId()).andProviderEqualTo("viewmodel").andProviderIdEqualTo(createViewModelReq.getModelId()).andIsDeletedEqualTo(0L);
        List<DatatubeInstance> insts = datatubeInstanceMapper.selectByExample(diExample);
    	
        Date now = new Date();
    	DatatubeInstance record = new DatatubeInstance();
    	record.setId(insts.get(0).getId());
    	record.setGmtModified(now);
    	if (StringUtils.isNotBlank(createViewModelReq.getDatatubeLevel())) {
    		record.setLevel(createViewModelReq.getDatatubeLevel());
    	}
    	record.setModifyEmpid(createViewModelReq.getOperateEmpid());
    	if (StringUtils.isNotBlank(createViewModelReq.getRemark())) {
    		record.setRemark(createViewModelReq.getRemark());
    	}
    	datatubeInstanceMapper.updateByPrimaryKeySelective(record);
        
    	Long versionId = viewModelHandler.modifyViewModel(createViewModelReq);
    	
    	refreshViewModelDsRelations(createViewModelReq.getTenantId(), insts.get(0).getId(), createViewModelReq.getOperateEmpid());
    	
    	return versionId;
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Long createDAGDatatube(TaskInfoRequest taskReq) {
        log.info("start createDAGDatatube({})", JSON.toJSONString(taskReq));
    	
    	DataResult<Long> createTaskResp = dagTaskService.createTask(taskReq);
    	
    	TaskInfo taskInfo = taskInfoMapper.selectByPrimaryKey(createTaskResp.getData());
    	
    	Date now = new Date();
    	DatatubeInstance record = new DatatubeInstance();
    	record.setAppName(taskReq.getAppName());
    	record.setCode(taskInfo.getName());
    	record.setCreateEmpid(taskReq.getOperateEmpid());
    	record.setGmtCreate(now);
    	record.setGmtModified(now);
    	record.setIsDeleted(0L);
    	record.setIsTest(0L);
    	record.setLevel(taskReq.getDatatubeLevel());
    	record.setModifyEmpid(taskReq.getOperateEmpid());
    	record.setName(taskInfo.getTaskDesc());
    	record.setObjectType(taskReq.getObjectType());
    	record.setProvider("DAG");
    	record.setProviderId(createTaskResp.getData());
    	record.setTenantId(taskReq.getTenantId());
    	record.setType("custom");
    	datatubeInstanceMapper.insert(record);
    	
    	this.makeupDAGDatatubeTasks(createTaskResp.getData(), taskReq.getDatatubeLevel(), taskReq.getObjectType(), taskReq.getOperateEmpid());
    	
    	return record.getId();
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Boolean modifyDAGDatatube(Long datatubeId, String operateEmpid, String dagScript, String datatubeLevel, String remark) {
        log.info("start modifyDAGDatatube({},{},{},{},{})", datatubeId, operateEmpid, dagScript, datatubeLevel, remark);
        
        DatatubeInstance inst = datatubeInstanceMapper.selectByPrimaryKey(datatubeId);
    	
        Date now = new Date();
    	DatatubeInstance record = new DatatubeInstance();
    	record.setId(datatubeId);
    	record.setGmtModified(now);
    	if (StringUtils.isNotBlank(datatubeLevel)) {
    		record.setLevel(datatubeLevel);
    	}
    	record.setModifyEmpid(operateEmpid);
    	if (StringUtils.isNotBlank(remark)) {
    		record.setRemark(remark);
    	}
    	datatubeInstanceMapper.updateByPrimaryKeySelective(record);
    	
    	dagTaskService.updateTaskDag(inst.getProviderId(), operateEmpid, dagScript, false);
    	
    	this.makeupDAGDatatubeTasks(inst.getProviderId(), datatubeLevel, inst.getObjectType(), operateEmpid);
    	
    	return true;
    }

	public void refreshViewModelDsRelations(String tenantId, Long datatubeInstId, String empid) {
		Date now = new Date();	
		DatatubeInstance datatubeInst = datatubeInstanceMapper.selectByPrimaryKey(datatubeInstId);
		ViewModelInfo viewModelInfo = viewModelInfoMapper.selectByPrimaryKey(datatubeInst.getProviderId());
		ViewModelVersionWithBLOBs ViewModelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());
    	ViewModel viewModel = YamlUtil.getViewModel(ViewModelVersion.getSysYaml());
		
    	DatatubeInstanceDsRelationExample example = new DatatubeInstanceDsRelationExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andDatatubeInstIdEqualTo(datatubeInstId).andIsDeletedEqualTo(0L);
		DatatubeInstanceDsRelation updRecord = new DatatubeInstanceDsRelation();
		updRecord.setIsDeleted(1L);
		updRecord.setModifyEmpid(empid);
		updRecord.setGmtModified(new Date());
		int updCnt = datatubeInstanceDsRelationMapper.updateByExampleSelective(updRecord, example);
		log.info("clear datatubeInstnaceDsRelations, delCnt={}", updCnt);
		
		if (CollectionUtils.isNotEmpty(viewModel.getRelatedObjects())) {
			for (RelatedDataObject relObj : viewModel.getRelatedObjects()) {
				DatatubeInstanceDsRelation dsRel = new DatatubeInstanceDsRelation();
				dsRel.setCreateEmpid(empid);
				if ("metadata".equalsIgnoreCase(relObj.getType())) {
					JSONObject dsJson = dsInfoService.getTableMetaByDsUniqueName(tenantId, relObj.getRef());
					dsRel.setDsName(dsJson.getString("dsName"));
					dsRel.setDsType(dsJson.getString("dsType"));
				} else if ("table".equalsIgnoreCase(relObj.getType())) {
					dsRel.setDsName(relObj.getRef());
					JSONObject dsJson = dsInfoService.getTableMetaByDsName(tenantId, relObj.getRef());
					dsRel.setDsType(dsJson.getString("dsType"));
				} else {
					dsRel.setDsName(relObj.getRef());
					dsRel.setDsType("component");
				}
				dsRel.setGmtCreate(now);
				dsRel.setGmtModified(now);
				dsRel.setIsDeleted(0L);
				dsRel.setModifyEmpid(empid);
				dsRel.setRelationType("from_ds");
				dsRel.setTenantId(tenantId);
				dsRel.setDatatubeInstId(datatubeInstId);
				datatubeInstanceDsRelationMapper.insert(dsRel);
			}
		}
		DatatubeInstanceDsRelation dsRel = new DatatubeInstanceDsRelation();
		dsRel.setCreateEmpid(empid);
		if ("metadata".equalsIgnoreCase(viewModel.getObject().getType())) {
			JSONObject dsJson = dsInfoService.getTableMetaByDsUniqueName(tenantId, viewModel.getObject().getRef());
			dsRel.setDsName(dsJson.getString("dsName"));
			dsRel.setDsType(dsJson.getString("dsType"));
		} else if ("table".equalsIgnoreCase(viewModel.getObject().getType())) {
			dsRel.setDsName(viewModel.getObject().getRef());
			JSONObject dsJson = dsInfoService.getTableMetaByDsName(tenantId, viewModel.getObject().getRef());
			dsRel.setDsType(dsJson.getString("dsType"));
		} 
		dsRel.setGmtCreate(now);
		dsRel.setGmtModified(now);
		dsRel.setIsDeleted(0L);
		dsRel.setModifyEmpid(empid);
		dsRel.setRelationType("from_ds");
		dsRel.setTenantId(tenantId);
		dsRel.setDatatubeInstId(datatubeInstId);
		try {
			datatubeInstanceDsRelationMapper.insert(dsRel);
		} catch(Exception e) {}
		
		if (CollectionUtils.isNotEmpty(viewModel.getObject().getFields())
				&& CollectionUtils.isNotEmpty(viewModel.getObject().getFields().stream().filter(e -> e.getObject() != null).collect(Collectors.toList()))) {
			for (Field field : viewModel.getObject().getFields().stream().filter(e -> e.getObject() != null).collect(Collectors.toList())) {
				if ("table".equalsIgnoreCase(field.getObject().getType())) {
					dsRel.setDsName(field.getObject().getRef());
					JSONObject dsJson = dsInfoService.getTableMetaByDsName(tenantId, field.getObject().getRef());
					dsRel.setDsType(dsJson.getString("dsType"));
				} else {
					dsRel.setDsName(field.getObject().getRef());
					dsRel.setDsType("component");
				}

				dsRel.setGmtCreate(now);
				dsRel.setGmtModified(now);
				dsRel.setIsDeleted(0L);
				dsRel.setModifyEmpid(empid);
				dsRel.setRelationType("from_ds");
				dsRel.setTenantId(tenantId);
				dsRel.setDatatubeInstId(datatubeInstId);
				try {
					datatubeInstanceDsRelationMapper.insert(dsRel);
				} catch(Exception e) {}
			}
		}
		
		TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(tenantId);
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + tenantId + " is not configured");
    	}
    	
    	List<String> dstDbNames = viewModelHandler.getExtDbNames(tenantList.get(0), datatubeInst);
    	dstDbNames.add(viewModelHandler.getEtlDbName(tenantId));
    	for (String dstDbName : dstDbNames) {
	    	DbInfoExample dbExample = new DbInfoExample();
	    	dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dstDbName);
	    	List<DbInfo> dbInfos = dbInfoMapper.selectByExample(dbExample);
			
	    	String dsName = dsInfoService.getDsName(dbInfos.get(0).getId(), dbInfos.get(0).getDbType(), tenantId, datatubeInst.getAppName(), viewModel.getCode());
			dsRel = new DatatubeInstanceDsRelation();
			dsRel.setCreateEmpid(empid);
			dsRel.setDsName(dsName);
			dsRel.setDsType(dbInfos.get(0).getDbType());
			dsRel.setGmtCreate(now);
			dsRel.setGmtModified(now);
			dsRel.setIsDeleted(0L);
			dsRel.setModifyEmpid(empid);
			dsRel.setRelationType("to_ds");
			dsRel.setTenantId(tenantId);
			dsRel.setDatatubeInstId(datatubeInstId);
			try {
				datatubeInstanceDsRelationMapper.insert(dsRel);
			} catch(Exception e) {}
			
			DatasourceExample dsInfoExample = new DatasourceExample();
        	dsInfoExample.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsNameEqualTo(dsName);
        	List<Datasource> dsInfos = dsInfoMapper.selectByExample(dsInfoExample);
        	if (CollectionUtils.isEmpty(dsInfos)) {
        		DatasourceRequest request = new DatasourceRequest();
        		request.setTenantId(tenantId);
        		request.setAppName(datatubeInst.getAppName());
        		request.setDsName(dsName);
        		request.setDbName(dstDbName);
        		request.setOperateEmpid("schedulerx2");
        		request.setDsType(dbInfos.get(0).getDbType());
        		request.setTableName(viewModel.getCode());
        		dsInfoService.createDatasource(request);
        		log.info("table:{} for dbName:{} create dsInfo success", viewModel.getCode(), dstDbName);
        	}
    	}
	}
	
	public Boolean makeupViewModelDatatubeTasks(Long id, String jobNames, String operateEmpid) {
    	Date now = new Date();
    	DatatubeInstance inst = datatubeInstanceMapper.selectByPrimaryKey(id);
    	ViewModelInfo vm = viewModelInfoMapper.selectByPrimaryKey(inst.getProviderId());
    	
    	DatatubeInstanceTaskExample ditExample = new DatatubeInstanceTaskExample();
    	ditExample.createCriteria().andTenantIdEqualTo(inst.getTenantId()).andDatatubeInstIdEqualTo(id).andIsDeletedEqualTo(0L);
    	DatatubeInstanceTask updDatatubeInstanceTask = new DatatubeInstanceTask();
    	updDatatubeInstanceTask.setIsDeleted(1L);
    	updDatatubeInstanceTask.setModifyEmpid(operateEmpid);
    	updDatatubeInstanceTask.setGmtModified(new Date());
    	datatubeInstanceTaskMapper.updateByExampleSelective(updDatatubeInstanceTask, ditExample);
    	
    	if (StringUtils.isNotBlank(jobNames)) {
        	String[] jobNameArray = jobNames.split(",");
	    	for (String jobName : jobNameArray) {
	    		DatatubeInstanceTask task = new DatatubeInstanceTask();
	    		task.setCreateEmpid(operateEmpid);
	    		task.setDatatubeInstId(id);
	    		task.setGmtCreate(now);
	    		task.setGmtModified(now);
	    		task.setIsDeleted(0L);
	    		task.setModifyEmpid(operateEmpid);
	    		GetJobResponse.Job job = JSON.parseObject(blinkService.getJobDetail(inst.getTenantId(), inst.getAppName(), jobName), GetJobResponse.Job.class);
	    		if (job != null && StringUtils.isNotBlank(job.getPlanJson()) && CollectionUtils.isNotEmpty((JSON.parseObject(job.getPlanJson()).getJSONArray("nodes")))) {
	    			task.setParallel(JSON.parseObject(job.getPlanJson()).getJSONArray("nodes").getJSONObject(0).getInteger("parallelism"));
	        		task.setTaskScript(job.getCode());
	    		}
	    		Map<String, Object> resource = blinkService.getInstanceResource(inst.getTenantId(), inst.getAppName(), jobName);
				if (resource != null) {
					BigDecimal vcoreCU = resource.get("allocatedVirtualCores") != null ? new BigDecimal(resource.get("allocatedVirtualCores").toString()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");
					BigDecimal mbCU = resource.get("allocatedMB") != null ? new BigDecimal(resource.get("allocatedMB").toString()).divide(new BigDecimal("4096"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");
					task.setTaskCu(vcoreCU.compareTo(mbCU) > 0 ? vcoreCU : mbCU);
				}
	    		task.setTaskName(jobName);
	    		task.setTaskType("blink_stream");
	    		task.setTenantId(inst.getTenantId());
	    		task.setVersion(vm.getVersionId().intValue());
	    		datatubeInstanceTaskMapper.insert(task);
	    	}
    	}
    	
    	refreshViewModelDsRelations(inst.getTenantId(), id, operateEmpid);
        	
        	return true;
    }
	
    @Transactional(propagation = Propagation.REQUIRED)
    public List<Long> makeupOdsDatatubeTasks(List<Long> taskIds, String datatubeLevel, String operateEmpid) {
    	List<Long> datatubeInstIds = new ArrayList<>();
    	for (Long taskId : taskIds) {
    		datatubeInstIds.add(this.makeupOdsDatatubeTasks(taskId, datatubeLevel, operateEmpid));
    	}
    	return datatubeInstIds;
    }
	
    @Transactional(propagation = Propagation.REQUIRED)
    public Long makeupOdsDatatubeTasks(Long taskId, String datatubeLevel, String operateEmpid) {
    	Date now = new Date();
    	TaskInfoWithBLOBs taskInfo = taskInfoMapper.selectByPrimaryKey(taskId);
    	Dag dag = dagService.getDagByJson(taskInfo.getDag());
        List<Node> nodes = dag.getNodeList();
        String srcDsName = null;
        List<String> dstDsNames = new ArrayList<>();
        Map<String, JSONObject> dstDsNameMetaMap = new HashMap<>();
        for (Node node : nodes) {
        	if ("com.aliyun.wormhole.qanat.job.QanatDataXJobProcessor".equalsIgnoreCase(node.getAction())) {
        		srcDsName = ((DataXNode)node).getSrcDsName();
        		dstDsNames.add(((DataXNode)node).getDstDsName());
        	} else if ("com.aliyun.wormhole.qanat.job.QanatAdb3ExternalTableJobProcessor".equalsIgnoreCase(node.getAction())) {
        		srcDsName = ((Adb3ExtTblNode)node).getSrcDsName();
        		JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(taskInfo.getTenantId(), srcDsName);
        		String dstDbName = ((Adb3ExtTblNode)node).getDstDbName();
        		JSONObject dstDbMetaJson = dsInfoService.getDbMetaByName(dstDbName);
        		dstDsNames.add(dsInfoService.getDsName(dstDbMetaJson.getLong("dbId"), dstDbMetaJson.getString("dbType"), taskInfo.getTenantId(), taskInfo.getAppName(), srcDsMetaJson.getString("table")));
        	} else if ("com.aliyun.wormhole.qanat.job.QanatHoloExternalTableJobProcessor".equalsIgnoreCase(node.getAction())) {
        		srcDsName = ((HoloExtTblNode)node).getSrcDsName();
        		JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(taskInfo.getTenantId(), srcDsName);
        		String dstDbName = ((HoloExtTblNode)node).getDstDbName();
        		JSONObject dstDbMetaJson = dsInfoService.getDbMetaByName(dstDbName);
        		dstDsNames.add(dsInfoService.getDsName(dstDbMetaJson.getLong("dbId"), dstDbMetaJson.getString("dbType"), taskInfo.getTenantId(), taskInfo.getAppName(), srcDsMetaJson.getString("table")));
        	}
        }

        JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(taskInfo.getTenantId(), srcDsName);
        for (String dstDsName : dstDsNames) {
        	dstDsNameMetaMap.put(dstDsName, dsInfoService.getTableMetaByDsName(taskInfo.getTenantId(), dstDsName));
        }
    	
        DatatubeInstance record = null;
        DatatubeInstanceExample diExample = new DatatubeInstanceExample();
        diExample.createCriteria().andTenantIdEqualTo(taskInfo.getTenantId()).andProviderEqualTo("ods").andProviderIdEqualTo(taskId).andIsDeletedEqualTo(0L);
        List<DatatubeInstance> insts = datatubeInstanceMapper.selectByExample(diExample);
        if (CollectionUtils.isNotEmpty(insts)) {
        	record = insts.get(0);
        	
        	DatatubeInstanceTaskExample ditExample = new DatatubeInstanceTaskExample();
        	ditExample.createCriteria().andTenantIdEqualTo(record.getTenantId()).andDatatubeInstIdEqualTo(record.getId()).andIsDeletedEqualTo(0L);
        	DatatubeInstanceTask updDatatubeInstanceTask = new DatatubeInstanceTask();
        	updDatatubeInstanceTask.setIsDeleted(1L);
        	updDatatubeInstanceTask.setModifyEmpid(operateEmpid);
        	updDatatubeInstanceTask.setGmtModified(new Date());
        	datatubeInstanceTaskMapper.updateByExampleSelective(updDatatubeInstanceTask, ditExample);
        	
        	DatatubeInstanceDsRelationExample didrExample = new DatatubeInstanceDsRelationExample();
        	didrExample.createCriteria().andTenantIdEqualTo(record.getTenantId()).andDatatubeInstIdEqualTo(record.getId()).andIsDeletedEqualTo(0L);
        	DatatubeInstanceDsRelation didr = new DatatubeInstanceDsRelation();
        	didr.setIsDeleted(1L);
        	didr.setModifyEmpid(operateEmpid);
        	didr.setGmtModified(new Date());
        	datatubeInstanceDsRelationMapper.updateByExampleSelective(didr, didrExample);
        } else {
        	record = new DatatubeInstance();
        	record.setAppName(taskInfo.getAppName());
        	record.setCode("ods_" + taskInfo.getTenantId() + "_" + srcDsName);
        	record.setCreateEmpid(operateEmpid);
        	record.setGmtCreate(now);
        	record.setGmtModified(now);
        	record.setIsDeleted(0L);
        	record.setIsTest(0L);
        	record.setLevel(datatubeLevel);
        	record.setModifyEmpid(operateEmpid);
        	record.setName("Makeup ODS datatube instance from taskId:" + taskId);
        	record.setObjectType(srcDsMetaJson.getString("objectType"));
        	record.setProvider("ods");
        	record.setProviderId(taskId);
        	record.setTenantId(taskInfo.getTenantId());
        	record.setType("ods");
        	datatubeInstanceMapper.insert(record);
        }
    	
        for (Node node : dag.getNodeList()) {
        	DatatubeInstanceTask task = new DatatubeInstanceTask();
    		task.setCreateEmpid(operateEmpid);
    		task.setDatatubeInstId(record.getId());
    		task.setGmtCreate(now);
    		task.setGmtModified(now);
    		task.setIsDeleted(0L);
    		task.setModifyEmpid(operateEmpid);
    		task.setTenantId(record.getTenantId());
    		task.setVersion(taskId.intValue());
        	if ("com.aliyun.wormhole.qanat.job.QanatDataXJobProcessor".equalsIgnoreCase(node.getAction())) {
        		task.setTaskName(((DataXNode)node).getId());
        		task.setTaskType("datax");
        		task.setParallel(((DataXNode)node).getParallism());
        	} else if ("com.aliyun.wormhole.qanat.job.QanatBlinkJobProcessor".equalsIgnoreCase(node.getAction())) {
        		GetJobResponse.Job job = JSON.parseObject(blinkService.getJobDetail(record.getTenantId(), record.getAppName(), ((BlinkStreamNode)node).getJobName()), GetJobResponse.Job.class);
        		if (job != null && StringUtils.isNotBlank(job.getPlanJson()) && CollectionUtils.isNotEmpty((JSON.parseObject(job.getPlanJson()).getJSONArray("nodes")))) {
        			task.setParallel(JSON.parseObject(job.getPlanJson()).getJSONArray("nodes").getJSONObject(0).getInteger("parallelism"));
        		}
        		Map<String, Object> resource = blinkService.getInstanceResource(record.getTenantId(), record.getAppName(), ((BlinkStreamNode)node).getJobName());
				if (resource != null) {
    				BigDecimal vcoreCU = resource.get("allocatedVirtualCores") != null ? new BigDecimal(resource.get("allocatedVirtualCores").toString()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");
    				BigDecimal mbCU = resource.get("allocatedMB") != null ? new BigDecimal(resource.get("allocatedMB").toString()).divide(new BigDecimal("4096"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");
    				task.setTaskCu(vcoreCU.compareTo(mbCU) > 0 ? vcoreCU : mbCU);
				}
        		task.setTaskName(((BlinkStreamNode)node).getJobName());
        		if (job != null) {
        			task.setTaskScript(job.getCode());
        		}
        		task.setTaskType("blink_stream");
        	}
    		datatubeInstanceTaskMapper.insert(task);
        }

        DatatubeInstanceDsRelation didrRecord = new DatatubeInstanceDsRelation();
        didrRecord.setCreateEmpid(operateEmpid);
        didrRecord.setDatatubeInstId(record.getId());
        didrRecord.setDsName(srcDsName);
        didrRecord.setDsType(srcDsMetaJson.getString("dsType"));
        didrRecord.setGmtCreate(now);
        didrRecord.setGmtModified(now);
        didrRecord.setIsDeleted(0L);
        didrRecord.setModifyEmpid(operateEmpid);
        didrRecord.setRelationType("from_ds");
        didrRecord.setTenantId(record.getTenantId());
        didrRecord.setVersion(taskId.intValue());
		datatubeInstanceDsRelationMapper.insert(didrRecord);
		
		for (String dstDsName : dstDsNames) {
			didrRecord = new DatatubeInstanceDsRelation();
			didrRecord.setCreateEmpid(operateEmpid);
			didrRecord.setDatatubeInstId(record.getId());
			didrRecord.setDsName(dstDsName);
			didrRecord.setDsType(dstDsNameMetaMap.get(dstDsName) == null ? "adb3" : dstDsNameMetaMap.get(dstDsName).getString("dsType"));
			didrRecord.setGmtCreate(now);
			didrRecord.setGmtModified(now);
			didrRecord.setIsDeleted(0L);
			didrRecord.setModifyEmpid(operateEmpid);
			didrRecord.setRelationType("to_ds");
			didrRecord.setTenantId(record.getTenantId());
			didrRecord.setVersion(taskId.intValue());
			datatubeInstanceDsRelationMapper.insert(didrRecord);
		}
        
    	return record.getId();
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Long makeupDAGDatatubeTasks(Long taskId, String datatubeLevel, String objectType, String operateEmpid) {
    	Date now = new Date();

    	TaskInfoWithBLOBs taskInfo = taskInfoMapper.selectByPrimaryKey(taskId);
    	Dag dag = dagService.getDagByJson(taskInfo.getDag());
    	
        DatatubeInstance record = null;
        DatatubeInstanceExample diExample = new DatatubeInstanceExample();
        diExample.createCriteria().andTenantIdEqualTo(taskInfo.getTenantId()).andProviderEqualTo("DAG").andProviderIdEqualTo(taskId).andIsDeletedEqualTo(0L);
        List<DatatubeInstance> insts = datatubeInstanceMapper.selectByExample(diExample);
        if (CollectionUtils.isNotEmpty(insts)) {
        	record = insts.get(0);
        	
        	DatatubeInstanceTaskExample ditExample = new DatatubeInstanceTaskExample();
        	ditExample.createCriteria().andTenantIdEqualTo(record.getTenantId()).andDatatubeInstIdEqualTo(record.getId()).andIsDeletedEqualTo(0L);
        	DatatubeInstanceTask updDatatubeInstanceTask = new DatatubeInstanceTask();
        	updDatatubeInstanceTask.setIsDeleted(1L);
        	updDatatubeInstanceTask.setModifyEmpid(operateEmpid);
        	updDatatubeInstanceTask.setGmtModified(new Date());
        	datatubeInstanceTaskMapper.updateByExampleSelective(updDatatubeInstanceTask, ditExample);
        } else {
        	record = new DatatubeInstance();
        	record.setAppName(taskInfo.getAppName());
        	record.setCode(taskInfo.getName());
        	record.setCreateEmpid(operateEmpid);
        	record.setGmtCreate(now);
        	record.setGmtModified(now);
        	record.setIsDeleted(0L);
        	record.setIsTest(0L);
        	record.setLevel(datatubeLevel);
        	record.setModifyEmpid(operateEmpid);
        	record.setName("Makeup DAG datatube instance from taskId:" + taskId);
        	record.setObjectType(objectType);
        	record.setProvider("DAG");
        	record.setProviderId(taskId);
        	record.setTenantId(taskInfo.getTenantId());
        	record.setType("custom");
        	datatubeInstanceMapper.insert(record);
        }
    	
        for (Node node : dag.getNodeList()) {
        	DatatubeInstanceTask task = new DatatubeInstanceTask();
    		task.setCreateEmpid(operateEmpid);
    		task.setDatatubeInstId(record.getId());
    		task.setGmtCreate(now);
    		task.setGmtModified(now);
    		task.setIsDeleted(0L);
    		task.setModifyEmpid(operateEmpid);
    		task.setTenantId(record.getTenantId());
    		task.setVersion(taskId.intValue());
        	if ("com.aliyun.wormhole.qanat.job.QanatDataXJobProcessor".equalsIgnoreCase(node.getAction())) {
        		task.setTaskName(node.getId());
        		task.setParallel(((DataXNode)node).getParallism());
        		task.setTaskType("datax");
        	} else if ("com.aliyun.wormhole.qanat.job.QanatBlinkJobProcessor".equalsIgnoreCase(node.getAction()) && node.getNodeAction().equals(NodeAction.STREAM)) {
        		GetJobResponse.Job job = JSON.parseObject(blinkService.getJobDetail(record.getTenantId(), record.getAppName(), ((BlinkStreamNode)node).getJobName()), GetJobResponse.Job.class);
        		if (job != null && StringUtils.isNotBlank(job.getPlanJson()) && CollectionUtils.isNotEmpty((JSON.parseObject(job.getPlanJson()).getJSONArray("nodes")))) {
        			task.setParallel(JSON.parseObject(job.getPlanJson()).getJSONArray("nodes").getJSONObject(0).getInteger("parallelism"));
        		}
        		Map<String, Object> resource = blinkService.getInstanceResource(record.getTenantId(), record.getAppName(), ((BlinkStreamNode)node).getJobName());
				if (resource != null) {
    				BigDecimal vcoreCU = resource.get("allocatedVirtualCores") != null ? new BigDecimal(resource.get("allocatedVirtualCores").toString()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");
    				BigDecimal mbCU = resource.get("allocatedMB") != null ? new BigDecimal(resource.get("allocatedMB").toString()).divide(new BigDecimal("4096"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");
    				task.setTaskCu(vcoreCU.compareTo(mbCU) > 0 ? vcoreCU : mbCU);
				}
        		task.setTaskName(((BlinkStreamNode)node).getJobName());
        		task.setTaskScript(job.getCode());
        		task.setTaskType("blink_stream");
        	} else if ("com.aliyun.wormhole.qanat.job.QanatBlinkJobProcessor".equalsIgnoreCase(node.getAction()) && node.getNodeAction().equals(NodeAction.BATCH)) {
         		task.setTaskName(((BlinkBatchNode)node).getJobName());
         		GetJobResponse.Job job = JSON.parseObject(blinkService.getJobDetail(record.getTenantId(), record.getAppName(), ((BlinkBatchNode)node).getJobName()), GetJobResponse.Job.class);
         		task.setTaskScript(job.getCode());
         		task.setTaskType("blink_batch");
        	} else if ("com.aliyun.wormhole.qanat.job.QanatRestartBlinkJobProcessor".equalsIgnoreCase(node.getAction())) {
         		GetJobResponse.Job job = JSON.parseObject(blinkService.getJobDetail(record.getTenantId(), record.getAppName(), ((BlinkNode)node).getJobName()), GetJobResponse.Job.class);
        		if (job != null && StringUtils.isNotBlank(job.getPlanJson()) && CollectionUtils.isNotEmpty((JSON.parseObject(job.getPlanJson()).getJSONArray("nodes")))) {
        			task.setParallel(JSON.parseObject(job.getPlanJson()).getJSONArray("nodes").getJSONObject(0).getInteger("parallelism"));
        		}
        		Map<String, Object> resource = blinkService.getInstanceResource(record.getTenantId(), record.getAppName(), ((BlinkNode)node).getJobName());
				if (resource != null) {
    				BigDecimal vcoreCU = resource.get("allocatedVirtualCores") != null ? new BigDecimal(resource.get("allocatedVirtualCores").toString()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");
    				BigDecimal mbCU = resource.get("allocatedMB") != null ? new BigDecimal(resource.get("allocatedMB").toString()).divide(new BigDecimal("4096"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");
    				task.setTaskCu(vcoreCU.compareTo(mbCU) > 0 ? vcoreCU : mbCU);
				}
         		task.setTaskName(((BlinkNode)node).getJobName());
         		if (job != null) {
         			task.setTaskScript(job.getCode());
         		}
         		task.setTaskType("blink_restart");
        	} else if ("com.aliyun.wormhole.qanat.job.QanatAdb3MultiSqlJobProcessor".equalsIgnoreCase(node.getAction())) {
        		task.setTaskName(node.getId());
        		task.setTaskType(node.getNodeClass().getName());
        		task.setTaskScript(((Adb3MultiSqlNode)node).getSql());
        	} else if ("com.aliyun.wormhole.qanat.job.QanatAdb3SqlJobProcessor".equalsIgnoreCase(node.getAction())) {
        		task.setTaskName(node.getId());
        		task.setTaskType(node.getNodeClass().getName());
        		task.setTaskScript(((Adb3SqlNode)node).getSql());
        	} else {
        		task.setTaskName(node.getId());
        		task.setTaskType(node.getNodeClass().getName());
        	}
    		datatubeInstanceTaskMapper.insert(task);
        }
        return record.getId();
    }

	public void modifyOdsDatatube(Long datatubeId, CreateOdsRequest req) {
		log.info("start modifyOdsDatatube({},{})", datatubeId, JSON.toJSONString(req));
        DatatubeInstance inst = datatubeInstanceMapper.selectByPrimaryKey(datatubeId);
        
        Date now = new Date();
    	DatatubeInstance record = new DatatubeInstance();
    	record.setId(inst.getId());
    	record.setGmtModified(now);
    	if (StringUtils.isNotBlank(req.getDatatubeLevel())) {
    		record.setLevel(req.getDatatubeLevel());
    	}
    	record.setModifyEmpid(req.getOperateEmpid());
    	datatubeInstanceMapper.updateByPrimaryKeySelective(record);
    	
    	TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(inst.getTenantId());
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + inst.getTenantId() + " is not found");
    	}
    	
    	DatatubeInstanceDsRelationExample didrExample = new DatatubeInstanceDsRelationExample();
    	didrExample.createCriteria().andIsDeletedEqualTo(0L).andRelationTypeEqualTo("from_ds").andDatatubeInstIdEqualTo(inst.getId()).andTenantIdEqualTo(inst.getTenantId());
    	List<DatatubeInstanceDsRelation> dsRelations = datatubeInstanceDsRelationMapper.selectByExample(didrExample);
    	
    	if (CollectionUtils.isEmpty(dsRelations)) {
    		log.error("datatube[{}:{}] has no ds relations found", inst.getId(), inst.getName());
    		throw new QanatBizException("datatube[" +  inst.getId() + ":" + inst.getName() + "] has no ds relations found");
    	}
    	
    	DatasourceExample dsExample = new DatasourceExample();
    	dsExample.createCriteria().andIsDeletedEqualTo(0L).andDsNameEqualTo(dsRelations.get(0).getDsName()).andTenantIdEqualTo(inst.getTenantId());
    	List<Datasource> dsInfos = dsInfoMapper.selectByExample(dsExample);
    	
    	if (CollectionUtils.isEmpty(dsRelations)) {
    		log.error("datatube[{}:{}:{}] has no dsInfo found", inst.getId(), inst.getName(), dsRelations.get(0).getDsName());
    		throw new QanatBizException("datatube[" +  inst.getId() + ":" + inst.getName() + ":" + dsRelations.get(0).getDsName() + "] has no dsInfo found");
    	}
    	
    	TaskInfo taskInfo = taskInfoMapper.selectByPrimaryKey(inst.getProviderId());
    	if (taskInfo == null) {
    		log.error("datatube[{}:{}:{}] has no ods task found", inst.getId(), inst.getName(), inst.getProviderId());
    		throw new QanatBizException("datatube[" +  inst.getId() + ":" + inst.getName() + ":" +  inst.getProviderId() + "] has no ods task found");
    	}
    	
    	DataResult<JobConfigInfo> jobInfoResult = schedulerXJobService.getJobInfo(inst.getTenantId(), inst.getAppName(), Long.valueOf(taskInfo.getExternalId()));
    	if (jobInfoResult == null || !jobInfoResult.getSuccess() || jobInfoResult.getData() == null) {
    		log.error("datatube[{}:{}:{}:{}] has no schedulerx job found", inst.getId(), inst.getName(), inst.getProviderId(), taskInfo.getExternalId());
    		throw new QanatBizException("datatube[" +  inst.getId() + ":" + inst.getName() + ":" +  inst.getProviderId() + ":" + taskInfo.getExternalId() + "] has no schedulerx job found");
    	}
    	JobConfigInfo jobInfo = jobInfoResult.getData();
    	
        //创建数据同步任务
    	OdsSyncTaskRequest taskReq = new OdsSyncTaskRequest();
    	taskReq.setAppName(inst.getAppName());
    	List<String> dstDbNames = new ArrayList<>();
    	dstDbNames.add(tenantList.get(0).getDefaultDw());
    	if (StringUtils.isNotBlank(tenantList.get(0).getBackupDw())) {
    		dstDbNames.addAll(Arrays.asList(tenantList.get(0).getBackupDw().split(",")));
    	}
    	taskReq.setTaskName(taskInfo.getName());
    	taskReq.setEtlDbName(tenantList.get(0).getEtlDw());
    	taskReq.setDstDbNames(dstDbNames);
    	taskReq.setEnableCheck(false);
    	taskReq.setEnableFullLink(false);
    	taskReq.setEnableIncrSync(true);
    	taskReq.setFullSyncBatchSize(204800);
    	taskReq.setFullSyncParallelism("16");
    	taskReq.setOperateEmpid("schedulerx");
    	taskReq.setRecordSizeW(dsInfos.get(0).getPredictSize() == null || dsInfos.get(0).getPredictSize() < 10000 ? 1 : (dsInfos.get(0).getPredictSize().intValue()/10000));
    	taskReq.setSrcDsName(dsInfos.get(0).getDsName());
    	taskReq.setTableName(dsInfos.get(0).getTableName());
    	taskReq.setTenantId(inst.getTenantId());
    	taskReq.setTimeExpression(jobInfo.getTimeConfig().getTimeExpression());
    	taskReq.setDatatubeLevel(inst.getLevel());
    	taskReq.setPredictQph(dsInfos.get(0).getPredictQph());
    	taskReq.setPredictSize(dsInfos.get(0).getPredictSize());
 
    	try {
    		OdsSyncTaskResponse createResult = odsHandler.createBatchStreamTasks(taskReq);
	    	log.info("createBatchStreamTasks result:{}", JSON.toJSONString(createResult));
	    	if (createResult != null && createResult.getDataSyncTaskId() != null) {
        		log.info("datatube[{}:{}:{}:{}] update DAG finished", inst.getId(), inst.getName(), inst.getProviderId(), taskInfo.getExternalId());

        		this.makeupOdsDatatubeTasks(createResult.getDataSyncTaskId(), req.getDatatubeLevel(), req.getOperateEmpid());
        		
        		TaskInfoWithBLOBs updRecord = new TaskInfoWithBLOBs();
        		updRecord.setId(inst.getProviderId());
        		updRecord.setGmtModified(now);
        		updRecord.setModifyEmpid(req.getOperateEmpid());
        		updRecord.setTimeExpression(jobInfo.getTimeConfig().getTimeExpression());
        		taskInfoMapper.updateByPrimaryKeySelective(updRecord);
	    	}
    	} catch(Exception e) {
    		log.error("update task[{}-{}-{}] DAG failed, error={}", inst.getId(), taskInfo.getId(), taskInfo.getName(), e.getMessage(), e);
    	}
	}
}