package com.aliyun.wormhole.qanat.service.odps;

import java.io.ByteArrayInputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;

import javax.annotation.Resource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.odps.Event;
import com.aliyun.odps.InternalOdps;
import com.aliyun.odps.Odps;
import com.aliyun.odps.account.Account;
import com.aliyun.odps.account.AliyunAccount;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.service.TaskService;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsTaskRelation;
import com.aliyun.wormhole.qanat.dal.domain.DsTaskRelationExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsTaskRelationMapper;
import com.taobao.ateye.util.reflect.StringUtils;

import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

@Slf4j
@Component
public class OdpsService {

	@Resource
    private DbInfoMapper dbInfoMapper;

	@Resource
    private DatasourceMapper dsInfoMapper;

	@Resource
    private DsTaskRelationMapper dsTaskRelationMapper;

	@Resource
    private TaskService taskService;
	
	@Value("${qanat.odps.callback}")
    private String odpsCallbackUri;
	
	private static int[] WORK_HOURS = {9, 20};

	public Boolean registerOdpsMetaEvent(String tenantId, String dsName) {
		log.info("registerOdpsMetaEvent({},{})", tenantId, dsName);
		try {
			DatasourceExample example = new DatasourceExample();
            example.createCriteria().andDsNameEqualTo(dsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsTypeEqualTo("odps");
            List<Datasource> dsInfos = dsInfoMapper.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isEmpty(dsInfos)) {
            	throw new QanatBizException("dsInfo:" + dsName + " is not found");
            }
            DbInfoExample dbExample = new DbInfoExample();
            dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dsInfos.get(0).getDbName()).andTenantIdEqualTo(tenantId);
            List<DbInfo> dbInfos = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
            if (CollectionUtils.isEmpty(dbInfos)) {
                throw new QanatBizException("db:" + dsInfos.get(0).getDbName() + " is not found");
            }
            DbInfo dbInfo = dbInfos.get(0);
            JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
			
	    	Account account = new AliyunAccount(dbMetaJson.getString("accessId"), dbMetaJson.getString("accessKey"));
	        Odps odps = new Odps(account);
	        odps.setEndpoint( dbMetaJson.getString("odpsServer"));
	        odps.setDefaultProject(dbMetaJson.getString("project"));
	        InternalOdps iodps = new InternalOdps(odps);
	        
	        // 创建事件 & 绑定回调
	        Event e = buildEvent("ODPS_META_EVENT_" + dsInfos.get(0).getId(), dsInfos.get(0).getTableName(), odpsCallbackUri, "odps mete event for table " + dbMetaJson.getString("project") + "." + dsInfos.get(0).getTableName()); 
	
	        // 注册事件
	        iodps.events().create(e);
	
	        // 查看已创建事件
	        Iterator<Event> events = iodps.events().iterator();
	        while(events.hasNext()) {
	            Event event = events.next();
	            log.info("Event found:{} uri:{}", event.getName(), event.getUri());
	        }
	        return true;
    	} catch (Exception e) {
			log.error("registerOdpsMetaEvent failed:{}", e.getMessage(), e);
    	}
        return false;
    }

	public Boolean unregisterOdpsMetaEvent(String tenantId, String dsName) {
		log.info("unregisterOdpsMetaEvent({},{})", tenantId, dsName);
		try {
			DatasourceExample example = new DatasourceExample();
            example.createCriteria().andDsNameEqualTo(dsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsTypeEqualTo("odps");
            List<Datasource> dsInfos = dsInfoMapper.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isEmpty(dsInfos)) {
            	throw new QanatBizException("dsInfo:" + dsName + " is not found");
            }
            DbInfoExample dbExample = new DbInfoExample();
            dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dsInfos.get(0).getDbName()).andTenantIdEqualTo(tenantId);
            List<DbInfo> dbInfos = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
            if (CollectionUtils.isEmpty(dbInfos)) {
                throw new QanatBizException("db:" + dsInfos.get(0).getDbName() + " is not found");
            }
            DbInfo dbInfo = dbInfos.get(0);
            JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
			
	    	Account account = new AliyunAccount(dbMetaJson.getString("accessId"), dbMetaJson.getString("accessKey"));
	        Odps odps = new Odps(account);
	        odps.setEndpoint( dbMetaJson.getString("odpsServer"));
	        odps.setDefaultProject(dbMetaJson.getString("project"));
	        InternalOdps iodps = new InternalOdps(odps);
	        
	        // 删除事件
	        iodps.events().delete(dbMetaJson.getString("project"), "ODPS_META_EVENT_" + dsInfos.get(0).getId());
	
	        // 查看已创建事件
	        Iterator<Event> events = iodps.events().iterator();
	        while(events.hasNext()) {
	            Event event = events.next();
	            log.info("Event found:{} uri:{}", event.getName(), event.getUri());
	        }
			log.info("unregisterOdpsMetaEvent finish");
	        return true;
    	} catch (Exception e) {
			log.error("unregisterOdpsMetaEvent failed:{}", e.getMessage(), e);
    	}
		return false;
    }
    
    private Event buildEvent(String eventName, String tableName, String callbackUri, String comment) throws URISyntaxException {
        Event event = new Event();
        event.setName(eventName);
        event.setComment(comment);
        event.setType(Event.SourceType.TABLE);
        Event.Config config = new Event.Config();
        config.setName("source");
		String[] tokens = tableName.split("\\.");
		if(tokens.length == 2){
			config.setValue(tokens[1]);
		} else {
			config.setValue(tableName);
		}
        event.setConfig(config);
        event.setUri(new URI(callbackUri));
        return event;
    }

	public static void main(String[] args) {
		String xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Notification><Account>ALIYUN$<EMAIL></Account><Project>aliyun_sc</Project><SourceType>Table</SourceType><SourceName>ads_aly_cust_ecid_rec_result_d</SourceName><Reason>INSERTOVERWRITEPARTITION</Reason><TimeStamp>Fri, 05 Apr 2024 23:06:10 GMT</TimeStamp><Properties><Property><Name>Name</Name><Value>ds=********</Value></Property><Property><Name>customizedInfo</Name><Value>{\"InstanceId\": \"********230601293gd8szl9t4mp\"}</Value></Property></Properties><OdpsMessagerId>********</OdpsMessagerId><OdpsMessagerTime>**********</OdpsMessagerTime></Notification>";
		new OdpsService().reflectOdpsMetaEvent(xml);
	}

	public Boolean reflectOdpsMetaEvent(String xml) {
		log.info("reflectOdpsMetaEvent({})", xml);
		try {
			String projectName = null;
			String sourceType = null;
			String tableName = null;
			String reason = null;
			String partition = null;
			
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			dbf.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
			dbf.setFeature("http://xml.org/sax/features/external-general-entities", false);
			dbf.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
			DocumentBuilder db = dbf.newDocumentBuilder();
            Document document = db.parse(new ByteArrayInputStream(xml.getBytes("UTF-8")));
            document.getDocumentElement().normalize();
            Element root = document.getDocumentElement();
            NodeList childNodes = root.getChildNodes();
			if (!"NOTIFICATION".equalsIgnoreCase(root.getNodeName())) {
				return false;
			}
            for (int i = 0; i < childNodes.getLength(); i++) {
            	Node secendNode = childNodes.item(i);
//            	if (node.getNodeType() == Node.ELEMENT_NODE) {
//            		NodeList secendNode = node.getChildNodes();
//                    for (int j = 0; j < firstNodes.getLength(); j++) {
//                    	Node firstNode = firstNodes.item(j);
				if ("Project".equalsIgnoreCase(secendNode.getNodeName())) {
					projectName = secendNode.getFirstChild().getNodeValue();
					if (StringUtils.isBlank(projectName)) {
						log.info("reflectOdpsMetaEvent cancelled due to projectName is null");
						return false;
					}
				} else if ("SourceType".equalsIgnoreCase(secendNode.getNodeName())) {
					sourceType = secendNode.getFirstChild().getNodeValue();
					if (!"Table".equalsIgnoreCase(sourceType)) {
						log.info("reflectOdpsMetaEvent cancelled due to sourceType:{}", sourceType);
						return false;
					}
				} else if ("SourceName".equalsIgnoreCase(secendNode.getNodeName())) {
					tableName = secendNode.getFirstChild().getNodeValue();
					if (StringUtils.isBlank(tableName)) {
						log.info("reflectOdpsMetaEvent cancelled due to tableName is null");
						return false;
					}
				} else if ("Reason".equalsIgnoreCase(secendNode.getNodeName())) {
					reason = secendNode.getFirstChild().getNodeValue();
					if (!("INSERTINTOPARTITION".equalsIgnoreCase(reason)
						 || "INSERTOVERWRITEPARTITION".equalsIgnoreCase(reason))) {
						log.info("reflectOdpsMetaEvent cancelled due to reason:{}", reason);
						return false;
					}
				} else if ("Properties".equalsIgnoreCase(secendNode.getNodeName())) {
					if (secendNode.getChildNodes() == null
							|| secendNode.getChildNodes().getLength() == 0
							|| secendNode.getChildNodes().item(0).getChildNodes() == null
							|| secendNode.getChildNodes().item(0).getChildNodes().getLength() == 0) {
						log.info("reflectOdpsMetaEvent cancelled due to properties empty");
						return false;
					}
					NodeList propNodes = secendNode.getChildNodes().item(0).getChildNodes();
					for (int l = 0; l < propNodes.getLength(); l++) {
						Node propNode = propNodes.item(l);
						if ("Value".equalsIgnoreCase(propNode.getNodeName())) {
							partition = propNode.getFirstChild().getNodeValue();
							break;
						}
					}
				}
            }
                 	
        	log.info("odpsMetaEvent:{} projectName:{} tableName:{} partition:{}", reason, projectName, tableName, partition);
             	
         	DatasourceExample example = new DatasourceExample();
    		example.createCriteria().andTableNameEqualTo(tableName).andIsDeletedEqualTo(0L).andDsTypeEqualTo("odps");
    		List<Datasource> dsInfos = dsInfoMapper.selectByExample(example);
    		if (CollectionUtils.isEmpty(dsInfos)) {
           		log.info("reflectOdpsMetaEvent cancelled due to tableName:{} do not match any dsInfos", tableName);
				   example = new DatasourceExample();
				example.createCriteria().andTableNameEqualTo(projectName + "." + tableName).andIsDeletedEqualTo(0L).andDsTypeEqualTo("odps");
				dsInfos = dsInfoMapper.selectByExample(example);
				if (CollectionUtils.isEmpty(dsInfos)) {
					log.info("reflectOdpsMetaEvent cancelled due to tableName:{} do not match any dsInfos", tableName);
					return false;
				}
    		}
			for (Datasource dsInfo : dsInfos) {
				DbInfoExample dbExample = new DbInfoExample();
                dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dsInfo.getDbName()).andTenantIdEqualTo(dsInfo.getTenantId());
                List<DbInfo> dbInfos = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
                if (CollectionUtils.isEmpty(dbInfos)) {
                    throw new QanatBizException("db:" + dsInfos.get(0).getDbName() + " is not found");
                }
                DbInfo dbInfo = dbInfos.get(0);
                JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
    			if (projectName.equalsIgnoreCase(dbMetaJson.getString("project"))) {
    				DsTaskRelationExample dsTaskRelExample = new DsTaskRelationExample();
    				dsTaskRelExample.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(dsInfo.getTenantId()).andDsNameEqualTo(dsInfo.getDsName()).andRelationTypeEqualTo("ods");
    				List<DsTaskRelation> dsTaskRels = dsTaskRelationMapper.selectByExample(dsTaskRelExample);
    				if (CollectionUtils.isEmpty(dsTaskRels)) {
    	           		log.info("reflectOdpsMetaEvent cancelled due to ds:{} is not refered to any tasks", dsInfo.getDsName());
    	       			return false;
    				}
					DsTaskRelation dsTaskRel = dsTaskRels.get(0);
					log.info("dsInfo:{} has ods Task:{}", dsInfo.getDsName(), dsTaskRel.getTaskId());
					Calendar cal = Calendar.getInstance();
					int hour = cal.get(Calendar.HOUR_OF_DAY);
					if (!dsInfo.getTenantId().equalsIgnoreCase("1") || (hour >= WORK_HOURS[1] || hour < WORK_HOURS[0])) {

						OdpsClient client = new OdpsClient(dbMetaJson.getString("odpsServer"), dbMetaJson.getString("accessId"), dbMetaJson.getString("accessKey"),
								dbMetaJson.getString("project"), dbMetaJson.getString("mcUrl"), dbMetaJson.getString("mcToken"));
						String srcTableName = dsInfo.getTableName();
						String [] tokens = srcTableName.split("\\.");
						if (tokens.length == 2) {
							srcTableName = tokens[1];
						}
						String maxPt = client.getMaxPt(srcTableName);
						String notifyPt = partition.split("\\=")[1];
						if (maxPt.compareTo(notifyPt) > 0) {
							log.info("reflectOdpsMetaEvent notifyPt:{} is older than maxPt:{}, history partition event will be ignored", notifyPt, maxPt);
							continue;
						}

						Map<String, Object> param = new HashMap<>();
						param.put("partition", partition);
						Datasource upd = new Datasource();
						upd.setId(dsInfo.getId());
						upd.setGmtModified(new Date());
						upd.setModifyEmpid("OdpsEvent");
						JSONObject metaJson = JSON.parseObject(dsInfo.getMeta());
						metaJson = metaJson == null ? new JSONObject() : metaJson;
						metaJson.put("maxPt", partition);
						upd.setMeta(metaJson.toJSONString());
						dsInfoMapper.updateByPrimaryKeySelective(upd);
						DataResult<Long> result = taskService.runTask(dsInfo.getTenantId(), "odpsMetaEvent", dsTaskRel.getTaskId(), param);
						log.info("dsInfo:{} ods Task:{} is running:{}", dsInfo.getDsName(), dsTaskRel.getId(), result == null ? null : result.getData());
					} else {
						log.info("dsInfo:{} ods Task:{} is canceled due to in worktime [{}:00~{}:00]", dsInfo.getDsName(), dsTaskRel.getId(), WORK_HOURS[0], WORK_HOURS[1]);
					}
    			}
			}

    		log.info("reflectOdpsMetaEvent finished");
    		return true;
		} catch (Exception e) {
			log.error("reflectOdpsMetaEvent failed:{}", e.getMessage(), e);
		}
		return false;
	}
}