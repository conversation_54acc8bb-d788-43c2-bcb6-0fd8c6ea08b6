package com.aliyun.wormhole.qanat.process;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class TmpTablesClearProcessor extends JavaProcessor {

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
	
	@Override
    public ProcessResult process(JobContext context) {
        JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
        log.info("start to add fields, param=[]", context.getJobParameters());
        
        String dbName = paramsJson.getString("dbName");
        Integer lifecycle = paramsJson.getInteger("lifecycle") == null ? 7 : paramsJson.getInteger("lifecycle");
        
		RdsConnectionParam param = new RdsConnectionParam();
	    JSONObject dbMetaJson = getAdbDbMeta(dbName);
	    param.setUrl(dbMetaJson.getString("jdbcUrl"))
	        .setUserName(dbMetaJson.getString("username"))
	        .setPassword(dbMetaJson.getString("password"));
	    Connection connection = null;
	    try {
	        connection = dsHandler.connectToTable(param);
	        
	        List<String> tableList = querySql(connection, dbMetaJson.getString("dbType"));
	        dropTable(connection, tableList, lifecycle);
	    } catch (Exception e) {
	        log.error("get table data monitor failed, error={}", e.getMessage(), e);
	        return new ProcessResult(false);
	    } finally {
	        if (param != null) {
            	dsHandler.closeDataSource(param);
	        }
	    }
        return new ProcessResult(true);
	}

    private JSONObject getAdbDbMeta(String dbName) {
        DbInfoExample example = new DbInfoExample();
        example.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("no db found");
        }
        String dbMeta = dbs.get(0).getMeta();
        JSONObject dbMetaJson = JSON.parseObject(dbMeta);
        dbMetaJson.put("dbType", dbs.get(0).getDbType());
        return dbMetaJson;
    }

    private List<String> querySql(Connection connection, String dbType) {
    	List<String> result = new ArrayList<>();
    	String sql = "SELECT table_name FROM information_schema.tables where table_schema='mariana' and table_name like 'tmp_%'";
    	if ("hologres".equalsIgnoreCase(dbType)) {
    		sql = "SELECT table_name FROM information_schema.tables where table_catalog='mariana' and table_schema not in ('hologres','pg_catalog','information_schema') and table_name like 'tmp_%'";
    	}
        log.info("before exec sql={}", sql);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            resultSet = statement.executeQuery(sql);
            int i=0;
            while (resultSet.next()) {
            	result.add(resultSet.getString("table_name"));
            	log.info("tableName:{}", resultSet.getString("table_name"));
            	i++;
            }
            log.info("after exec sql cnt={}", i);
        } catch(Exception e) {
            log.error("querySql failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                }
                resultSet = null;
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
        }
        return result;
    }

    private void dropTable(Connection connection, List<String> tableList, int lifecycle) {
        Statement statement = null;
        int i = 0;
        SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, -1 * lifecycle);
        Date lifecycleRefDate = cal.getTime();
        for (String tableName : tableList) {
        	log.info("begin to process table {}", tableName);
	        try {
	        	String[] tokens = tableName.split("_");
	        	String version = tokens[tokens.length - 1];
	        	if (StringUtils.isNotBlank(version) && StringUtils.isNumeric(version) && version.length() >= 6) {
	        		String tableDateStr = version.substring(0, 6);
	        		try {
		        		Date tableDate = sdf.parse(tableDateStr);
		        		if (tableDate.compareTo(lifecycleRefDate) <= 0) {
		    	            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
		    	            statement.execute("drop table " + tableName);
		    	        	log.info("drop table {}", tableName);
		    	            i++;
		        		} else {
			            	log.info("skip to process table {} due to in lifecycel", tableName);
		        			continue;
		        		
		        		}
	        		} catch (Exception e) {
		            	log.info("skip to process table {} due to illegal version", tableName);
	        			continue;
	        		}
	        	} else {
	            	log.info("skip to process table {}", tableName);
	        		continue;
	        	}
	        } catch(Exception e) {
	            log.error("querySql failed", e);
	        } finally {
	            if (statement != null) {
	                try {
	                    statement.close();
	                } catch (SQLException e) {
	                }
	                statement = null;
	            }
	        }
        }
        log.info("{} tables are dropped", i);
    }
    
    @Override
    public void kill(JobContext context) {
        
    }
}