package com.aliyun.wormhole.qanat.service.mdp;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfo;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.TenantInfoMapper;
import com.taobao.metaq.client.MetaPushConsumer;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class MdpObjectMetaListener {

	private String consumerGroupName = "CID-datatube-mdp";
	private MetaPushConsumer consumer;
	private Integer consumeThreadMin = 20;
	private Integer consumeThreadMax = 20;
	
    @Value("${env.unit}")
    private String unit;
	
	@Resource
	private TenantInfoMapper tenantMapper;
	
	public void init() throws MQClientException {
//		consumer = new MetaPushConsumer(consumerGroupName);
//		try {
//			TenantInfoExample example = new TenantInfoExample();
//			example.createCriteria().andUnitEqualTo(unit);
//			List<TenantInfo> tenants = tenantMapper.selectByExample(example);
//			List<JSONObject> topicInfos = new ArrayList<>();
//			if (CollectionUtils.isNotEmpty(tenants)) {
//				for (TenantInfo tenant : tenants) {
//					JSONObject mdpMetaJson = JSON.parseObject(tenant.getMdpMeta());
//					if (mdpMetaJson != null) {
//						if ("ali".equalsIgnoreCase(mdpMetaJson.getString("mqProvider")) && "metaq".equalsIgnoreCase(mdpMetaJson.getString("mqType"))) {
//							topicInfos.add(mdpMetaJson.getJSONObject("topics"));
//						}
//					}
//				}
//			}
//			if (CollectionUtils.isNotEmpty(topicInfos)) {
//				for (JSONObject topicJson : topicInfos) {
//					Set<String> topics = topicJson.keySet();
//					for (String topicName : topics) {
//						consumer.subscribe(topicName, topicJson.getJSONObject(topicName).getString("tag"));
//					}
//				}
//			}
//		} catch (MQClientException e) {
//			log.error("Error suscribeconsumer.", e);
//		}
//
//		consumer.registerMessageListener(new MessageListenerConcurrently() {
//
//			@Override
//			public ConsumeConcurrentlyStatus consumeMessage(
//					List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
//				for (MessageExt msg : msgs) {
//					try {
////						if (topicHandlerMap.get(msg.getTopic()) != null) {
////							MessageEventHandler handler = ((MessageEventHandler) topicHandlerMap
////									.get(msg.getTopic()));
////							handler.handle(msg);
////						}
//					} catch (Exception e) {
//						log.info(
//								"Error dealing with metaq message. Message:"
//										+ msg.toString(), e);
//						return ConsumeConcurrentlyStatus.RECONSUME_LATER;
//					}
//				}
//				return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
//			}
//		});
//		consumer.setConsumeThreadMin(consumeThreadMin);
//		consumer.setConsumeThreadMax(consumeThreadMax);
//		/**
//		 * Consumer对象在使用之前必须要调用start初始化，初始化一次即可<br>
//		 */
//		consumer.start();

	}
}
