package com.aliyun.wormhole.qanat.process;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class DataSyncMonitorProcessor extends JavaProcessor {

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
	
	@Override
    public ProcessResult process(JobContext context) {
        JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
        log.info("start to add fields, param=[]", context.getJobParameters());
        
        String tableName = paramsJson.getString("tableName");
        String dbName = paramsJson.getString("dbName");
        String monitorField = paramsJson.getString("monitorField");
        String timeUnit = paramsJson.getString("timeUnit");
        Integer timeGap = paramsJson.getInteger("timeGap");
        Integer times = paramsJson.getInteger("times");
        
		RdsConnectionParam param = new RdsConnectionParam();
	    JSONObject dbMetaJson = getAdbDbMeta(dbName);
	    param.setUrl(dbMetaJson.getString("jdbcUrl"))
	        .setUserName(dbMetaJson.getString("username"))
	        .setPassword(dbMetaJson.getString("password"));
	    Connection connection = null;
	    try {
	        connection = dsHandler.connectToTable(param);
	        
	        Map<String, String> data = querySql(connection, tableName, monitorField);
            String dateStr = data.get(monitorField);
        	
        	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        	Date date = sdf.parse(dateStr);
        	Date now = new Date();
        	Long gap = now.getTime() - date.getTime();
        	
        	Long ref = 0L;
        	if ("h".equals(timeUnit)) {
        		ref = Long.valueOf(times * timeGap * 60 * 60 * 1000);
        	} else if ("m".equals(timeUnit)) {
        		ref = Long.valueOf(times * timeGap * 60 * 1000);
        	} else if ("s".equals(timeUnit)) {
        		ref = Long.valueOf(times * timeGap * 1000);
        	}
        	log.info("now={}, {}={}, gap={}, ref={}", now.getTime(), monitorField, date.getTime(), gap, ref);
        	
        	if (gap > ref) {
        		log.info("qanat_monitor_event-{} has no updates in past {}ms[monitor ref:{}ms]", tableName, gap, ref);
        	}
        	
	    } catch (Exception e) {
	        log.error("get table data monitor failed, error={}", e.getMessage(), e);
	        return new ProcessResult(false);
	    } finally {
	        if (param != null) {
            	dsHandler.closeDataSource(param);
	        }
	    }
        return new ProcessResult(true);
	}

    private JSONObject getAdbDbMeta(String dbName) {
        DbInfoExample example = new DbInfoExample();
        example.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("no db found");
        }
        String dbMeta = dbs.get(0).getMeta();
        JSONObject dbMetaJson = JSON.parseObject(dbMeta);
        return dbMetaJson;
    }

    private Map<String, String> querySql(Connection connection, String tableName, String fieldName) {
        Map<String, String> data = new HashMap<>();
    	String sql = "select max(" + fieldName + ") as " + fieldName + " from " + tableName;
        log.info("before exec sql={}", sql);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                data.put(fieldName, resultSet.getString(fieldName));
            }
            log.info("after exec sql data={}", JSON.toJSONString(data));
        } catch(Exception e) {
            log.error("querySql failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                }
                resultSet = null;
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
        }
        return data;
    }
    
    @Override
    public void kill(JobContext context) {
        
    }
}