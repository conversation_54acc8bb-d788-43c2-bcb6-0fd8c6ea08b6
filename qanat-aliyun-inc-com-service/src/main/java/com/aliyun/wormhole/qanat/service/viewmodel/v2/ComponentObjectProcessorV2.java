package com.aliyun.wormhole.qanat.service.viewmodel.v2;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastsql.DbType;
import com.alibaba.fastsql.sql.SQLUtils;
import com.alibaba.fastsql.sql.ast.SQLStatement;
import com.alibaba.fastsql.sql.ast.expr.SQLBinaryOpExpr;
import com.alibaba.fastsql.sql.ast.expr.SQLBinaryOperator;
import com.alibaba.fastsql.sql.ast.expr.SQLIdentifierExpr;
import com.alibaba.fastsql.sql.ast.statement.SQLExprTableSource;
import com.alibaba.fastsql.sql.ast.statement.SQLJoinTableSource;
import com.alibaba.fastsql.sql.ast.statement.SQLSelectQuery;
import com.alibaba.fastsql.sql.ast.statement.SQLSelectQueryBlock;
import com.alibaba.fastsql.sql.ast.statement.SQLSelectStatement;
import com.alibaba.fastsql.sql.ast.statement.SQLSubqueryTableSource;
import com.alibaba.fastsql.sql.ast.statement.SQLTableSource;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.ResourcePackage;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.FlowCtlService;
import com.aliyun.wormhole.qanat.api.service.KafkaManagementService;
import com.aliyun.wormhole.qanat.dal.domain.AppInfo;
import com.aliyun.wormhole.qanat.dal.domain.AppInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelation;
import com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsRelation;
import com.aliyun.wormhole.qanat.dal.domain.DsRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.Extension;
import com.aliyun.wormhole.qanat.dal.domain.ExtensionExample;
import com.aliyun.wormhole.qanat.dal.mapper.AppInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ComponentDsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ExtensionMapper;
import com.aliyun.wormhole.qanat.service.viewmodel.FullLinkProcessor;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.DataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.RelatedDataObject;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ComponentObjectProcessorV2 {
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private KafkaManagementService kafkaManagementService;
    
    @Resource
    private AppInfoMapper appInfoMapper;
    
    @Resource
    private ExtensionMapper extensionMapper;
    
    @Resource
    private DatasourceService dsInfoService;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private DatasourceMapper dsInfoMapper;
    
    @Resource
    private ComponentDsRelationMapper componentDsRelationMapper;
    
    @Resource
    private FullLinkProcessor fullLinkProcessor;
	
	@Resource
	private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;
	
	@Resource
	private DsRelationMapper dsRelationMapper;
	
	@Resource
	private FlowCtlService flowCtlService;
    
    @Value("${datatube.codegen.version}")
    private String codegenVersion;
	
    public boolean processIncrSyncJobAsObject(String tenantId, String appName, String jobName, String etlDbName, String tableName, RelatedDataObject object, String operateEmpid, Long versionId, ViewModel dataModel, Long datatubeInstId) {
    	flowCtlService.setFlowControlIfNotExists(datatubeInstId, jobName, 1.0);
		String pkField = dataModel.getObject().getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0).getCode();
		String sql = null;
		String componentSql = null;
		String fkFieldOriginName = null;
		String fkFieldName = null;
		String fkFieldType = "bigint";

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		List<String> compFieldsDef = new ArrayList<>();
		List<String> compFieldsSel = new ArrayList<>();
		List<String> compFieldsSelOrig = new ArrayList<>();
		List<String> compFieldsJson = new ArrayList<>();
		for (ViewModel.Field field : object.getFields()) {
			if (field.isPk()) {
				fkFieldOriginName = field.getRef();
				continue;
			}
			compFieldsDef.add(field.getCode() + " " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
			compFieldsSel.add(field.getCode());
			compFieldsSelOrig.add(field.getRef());
			if ("varchar".equalsIgnoreCase(field.getType())) {
				compFieldsJson.add("JSON_VALUE(b.x, '$." + field.getRef() + "') AS " + field.getCode());
			} else {
				compFieldsJson.add("CAST(JSON_VALUE(b.x, '$." + field.getRef() + "') AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") AS " + field.getCode());
			}
		}
		if (CollectionUtils.isNotEmpty(object.getRelations())) {
			fkFieldName = object.getRelations().get(0).getRelatedField().split("\\.")[1];
			for (ViewModel.Field field : dataModel.getObject().getFields()) {
				if (field.getCode().equalsIgnoreCase(fkFieldName)) {
					fkFieldType = field.getType();
					break;
				}
			}
		}
		ExtensionExample example = new ExtensionExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + getDbType(tenantId, etlDbName)).andPluginEqualTo(object.getRef());
		List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isNotEmpty(exts)) {
			componentSql = getComponentSql(tenantId, etlDbName, fkFieldOriginName, compFieldsSelOrig, exts.get(0)).replace("'", "''");
		}
		if (StringUtils.isBlank(componentSql)) {
			log.error("no component conf found, objectCode:{}", object.getRef());
			throw new QanatBizException(object.getRef() + ":no component conf found");
		}
		ComponentDsRelationExample comDsRelExample = new ComponentDsRelationExample();
		comDsRelExample.createCriteria().andTenantIdEqualTo(tenantId).andComponentNameEqualTo(exts.get(0).getCode()).andRelationTypeEqualTo("ods");
		List<ComponentDsRelation> comRsRels = componentDsRelationMapper.selectByExample(comDsRelExample);
		if (CollectionUtils.isEmpty(comRsRels)) {
			log.error("no component ds found, componentName:{} relType:ods", exts.get(0).getCode());
			return false;
		}
		JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, comRsRels.get(0).getDsName());

        String dsSourceSql = "--SQL\n" + 
    			"--********************************************************************--\n" + 
    			"--Author: " + operateEmpid + "\n" + 
    			"--CreateTime: " + sdf.format(new Date()) + "\n" + 
    			"--Comment: " + ("sync for " + tableName + " from " + object.getCode()) + "\n" + 
    			"--Version: " + codegenVersion + "\n" + 
    			"--********************************************************************--\n" + 
    			"create table holobinlog_source (\n" + 
    			"    hg_binlog_lsn BIGINT,\n" +
    			"    hg_binlog_event_type BIGINT,\n" +
    			"    hg_binlog_timestamp_us BIGINT,\n" +
    			"   " + fkFieldOriginName + " " + fkFieldType + "\n" + 
    			") with (\n" + 
    			"  type = 'hologres',\n" + 
    			"  dbname = '" + srcDsMetaJson.getJSONObject("odsConf").getString("database") + "',\n" + 
    			"  tablename = '" + srcDsMetaJson.getJSONObject("odsConf").getString("tableName") + "',\n" + 
    			"  username = '" + srcDsMetaJson.getJSONObject("odsConf").getString("username") + "',\n" +
    			"  password = '" + srcDsMetaJson.getJSONObject("odsConf").getString("password") + "',\n" +
    			"  endpoint = '" + srcDsMetaJson.getJSONObject("odsConf").getString("endpoint") + "',\n" +
    			"  binlog = 'true',\n" +
    			"  binlogMaxRetryTimes = '10',\n" +
    			"  binlogRetryIntervalMs = '500',\n" +
    			"  binlogBatchReadSize = '256'\n" +
    			");\n" +
    			"\n" +
    			"CREATE FUNCTION trace AS 'com.aliyun.wormhole.qanat.blink.udf.QanatTraceUdf';\n" +
    			"CREATE FUNCTION flowCtl AS 'com.aliyun.wormhole.qanat.blink.udf.QanatFlowControlUdf';\n" +
    			"CREATE FUNCTION hololog AS 'com.aliyun.wormhole.qanat.blink.udf.QanatHoloLogTUdf';\n" +
    			"\n" + 
    			"create view v_source as \n" + 
    			"select \n" + 
    			"    hg_binlog_lsn,\n" + 
    			"    hg_binlog_event_type,\n" + 
    			"    hg_binlog_timestamp_us,\n" + 
    			"    " + fkFieldOriginName + ",\n" + 
    			"    trace(UUID(),hg_binlog_lsn,hg_binlog_event_type,hg_binlog_timestamp_us," + fkFieldOriginName + ",'" + jobName + "','holobinlog') as __trace_id__\n" + 
    			"from holobinlog_source;\n" + 
    			"\n" + 
    			"create view v_id as \n" + 
    			"select \n" + 
    			"    hg_binlog_lsn,\n" + 
    			"    hg_binlog_event_type,\n" + 
    			"    hg_binlog_timestamp_us,\n" + 
    			"    " + fkFieldOriginName + " as " + fkFieldName + ",\n" + 
    			"    flowCtl(__trace_id__,'" + jobName + "',cast(" + fkFieldOriginName + " as varchar)) as __trace_id__,\n" +
    			"    hololog(__trace_id__,hg_binlog_lsn,hg_binlog_event_type,hg_binlog_timestamp_us,'rowdata'," + fkFieldOriginName + ") as __haslog__\n" +
    			"from v_source\n" +
    			"where " + fkFieldOriginName + " is not null;" +
    			"\n" ;
		
        sql = dsSourceSql +
    			"\n" + 
    			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" + 
    			"\n" +
				"create view v_lookup as\n" + 
				"select\n" + 
				"  a." + fkFieldName + ",\n" + 
				"  " + StringUtils.join(compFieldsJson, ",") + ",\n" + 
				"  a.__trace_id__\n" + 
				"from v_id as a \n" + 
				"left join lateral table(\n" + 
				"  queryDim(concat_ws('|',__trace_id__,'" + exts.get(0).getDbName() + "'),\n" + 
				"    '" + componentSql + "'\n" + 
				"    , a." + fkFieldName + ")\n" + 
				") as b(x) on true;" +
				"\n";

    	String eventTopicName = "stream-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName);
    	boolean kfkRes = kafkaManagementService.createTopic(tenantId, appName, eventTopicName);
		if (!kfkRes) {
			log.error("topic:{} create is failed", eventTopicName);
		}
		
		String pkPart = "";
		if (fkFieldName.equalsIgnoreCase(pkField)) {
			pkPart = StringUtils.isNotBlank(dataModel.getSettings().getDistributeKey()) && !pkField.equalsIgnoreCase(dataModel.getSettings().getDistributeKey()) ? (pkField + "," + dataModel.getSettings().getDistributeKey()) : pkField;
		} else {
			pkPart = fkFieldName;
		}
		
		sql += "create table upsert_sink (\n" + 
			"    " + fkFieldName + " " + fkFieldType + ",\n" + 
			"    " + StringUtils.join(compFieldsDef, ",") + ",\n" + 
			"    __trace_id__ varchar,\n" + 
			"    primary key(" + pkPart + ")\n" + 
			") with (\n" + 
			"    type = 'QANAT_ADB30',\n" + 
			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
			"    dbName='" + etlDbName + "',\n" + 
			"    tableName='" + tableName + "',\n" + 
			"    replaceMode = '" + (fkFieldName.equalsIgnoreCase(pkField) ? "upsert" : "update_by_query") + "',\n" + 
			"    writeMode = 'single',\n" +
    		"    streamEvent = 'disable'\n" +
			");\n" + 
			"\n" + 
			"insert into upsert_sink\n" + 
			"select * from v_lookup;" +
			"\n";
		
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_UDF), 
        		false);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        return true;
    }

	public String getComponentSql(String tenantId, String etlDbName, String fkFieldOriginName,
			List<String> compFieldsSelOrig, Extension ext) {
		String componentSql;
		if (!etlDbName.equalsIgnoreCase(ext.getDbName())) {
			String origTableName = null;
			ComponentDsRelationExample comDsRelExample = new ComponentDsRelationExample();
			comDsRelExample.createCriteria().andTenantIdEqualTo(tenantId).andComponentNameEqualTo(ext.getCode()).andRelationTypeEqualTo("drc_in");
			List<ComponentDsRelation> comRsRels = componentDsRelationMapper.selectByExample(comDsRelExample);
			if (CollectionUtils.isNotEmpty(comRsRels)) {
				String drcDsName = comRsRels.get(0).getDsName();
				
				DsRelationExample dsRelExample = new DsRelationExample();
				dsRelExample.createCriteria().andTenantIdEqualTo(tenantId).andSrcDsNameEqualTo(drcDsName).andRelationTypeEqualTo("incr");
				List<DsRelation> dsRels = dsRelationMapper.selectByExample(dsRelExample);
				if (CollectionUtils.isNotEmpty(dsRels)) {
					String origDsName = dsRels.get(0).getDstDsName();
					
					DatasourceExample dsExample = new DatasourceExample();
					dsExample.createCriteria().andDsNameEqualTo(origDsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
					List<Datasource> dsList = dsInfoMapper.selectByExampleWithBLOBs(dsExample);
					if (CollectionUtils.isNotEmpty(dsList)) {
						origTableName = dsList.get(0).getTableName();
					} else {
						log.error("no dsinfo found, dsName:{}", origDsName);
					}
				} else {
					log.error("no ds relation found, srcDsName:{} relType:{}", drcDsName, "incr");
				}
			} else {
				log.error("no component ds found, componentName:{} relType:{}", ext.getCode(), "drc_in");
			}
			componentSql = generateStreamSQL4Mysql(ext.getScript(), fkFieldOriginName, origTableName);
		} else {
			componentSql = "select " + fkFieldOriginName + "," + StringUtils.join(compFieldsSelOrig, ",") + " from (" + ext.getScript() + ") as _t0_ where " + fkFieldOriginName + "=?";
		}
		return componentSql;
	}

	private String generateStreamSQL4Mysql(String sql, String fkFieldOriginName, String tableName) {
		DbType mysqlDbType = DbType.mysql;
		List<SQLStatement> stmtList = SQLUtils.parseStatements(sql, mysqlDbType);
		stmtList.forEach(sqlStatement -> {
			SQLSelectStatement sqlSelectStatement = (SQLSelectStatement) sqlStatement;
			SQLSelectQuery sqlSelectQuery = sqlSelectStatement.getSelect().getQuery();
			SQLSelectQueryBlock sqlSelectQueryBlock = (SQLSelectQueryBlock) sqlSelectQuery;
			SQLTableSource from = sqlSelectQueryBlock.getFrom();
			if (from instanceof SQLExprTableSource) {
	            // 形如：select column_name(s) FROM table_name1 t1 where t1.id=xxx
//	            SQLExprTableSource sqlExpreTableSource = (SQLExprTableSource) from;
				sqlSelectQueryBlock.addWhere(new SQLBinaryOpExpr(new SQLIdentifierExpr(fkFieldOriginName), SQLBinaryOperator.Equality, new SQLIdentifierExpr("?")));
	        } else if (from instanceof SQLSubqueryTableSource) {
	            // 形如：select * from (select * from temp) a，这里第一层from(...)是一个SQLSubqueryTableSource
	            SQLSubqueryTableSource sqlSubqueryTableSource = (SQLSubqueryTableSource) from;
				sqlSelectQueryBlock.addWhere(new SQLBinaryOpExpr(new SQLIdentifierExpr(fkFieldOriginName), SQLBinaryOperator.Equality, new SQLIdentifierExpr("?")));
	        } else if (from instanceof SQLJoinTableSource) {
	            // 形如：select * from emp e inner join org o on e.org_id = o.id 其中left 'emp e' 是一个SQLExprTableSource，right 'org o'也是一个SQLExprTableSource
	            SQLJoinTableSource sqlJoinTableSource = (SQLJoinTableSource) from;
	            String alias = sqlJoinTableSource.getLeft().getAlias();
				sqlSelectQueryBlock.addWhere(new SQLBinaryOpExpr(new SQLIdentifierExpr(alias + "." + fkFieldOriginName), SQLBinaryOperator.Equality, new SQLIdentifierExpr("?")));
	        }
		});
		return SQLUtils.toSQLString(stmtList, mysqlDbType, new SQLUtils.FormatOption(false, false));
	}
    
    private String getDbType(String tenantId, String dbName) {
    	DbInfoExample dbInfoExample = new DbInfoExample();
    	dbInfoExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
    	List<DbInfo> dbInfos = dbInfoMapper.selectByExample(dbInfoExample);
    	if (CollectionUtils.isEmpty(dbInfos)) {
    		throw new QanatBizException("DbInfo not found:" + dbName);
    	}
    	return dbInfos.get(0).getDbType();
    }
    
    public boolean processIncrSyncJobAsField(String tenantId, String appName, String jobName, String etlDbName, String tableName, String arrayFieldName, RelatedDataObject object, String operateEmpid, Long versionId, JSONObject appKafkaJson, JSONObject streamTopicInfo, List<JSONObject> idSourceTopicInfos, String pkField, ViewModel dataModel, Long datatubeInstId) {
    	String consumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "-incr_sync-" + arrayFieldName + "-" + versionId;
		flowCtlService.setFlowLimitIfNotExists(datatubeInstId, consumerId, 1.0);

		String sql = null;
		String componentSql = null;
		String fkFieldOriginName = null;
		String fkFieldName = null;
		String fkFieldType = "bigint";
		if (streamTopicInfo != null) {
			if (CollectionUtils.isNotEmpty(object.getRelations())) {
				fkFieldName = object.getRelations().get(0).getRelatedField().split("\\.")[1];
				for (ViewModel.Field field : dataModel.getObject().getFields()) {
					if (field.getCode().equalsIgnoreCase(fkFieldName)) {
						fkFieldType = field.getType();
						break;
					}
				}
			} else {
				fkFieldName = pkField;
				fkFieldType = "bigint";
			}
			boolean res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, streamTopicInfo.getString("dbName"), consumerId);
			if (!res) {
				log.error("consumer:{} create is failed", consumerId);
			}
			sql =
					"create table mq_source (\n" + 
					"    " + fkFieldName + " " + fkFieldType + ",\n" + 
				    "    " + arrayFieldName + " varchar,\n" +
					"    __traceId__ varchar header\n" + 
					") with (\n" + 
					"  type = 'custom',\n" + 
					"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
					"  topic = '" + streamTopicInfo.getString("topicName") + "',\n" + 
					"  `group.id` = '" + consumerId + "',\n" + 
					"  `dbName` = '" + streamTopicInfo.getString("dbName") + "',\n" +
					"  startupMode = 'TIMESTAMP',\n" +
					"  fieldDelimiter = '`'\n" +
					");\n" + 
					"\n";
        	String eventTopicName = "stream-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName);
        	boolean kfkRes = kafkaManagementService.createTopic(tenantId, appName, eventTopicName);
    		if (!kfkRes) {
    			log.error("topic:{} create is failed", eventTopicName);
    		}
			sql += "create table adb_sink (\n" + 
				"    " + fkFieldName + " " + fkFieldType + ",\n" + 
				"    " + arrayFieldName + " varchar,\n" + 
				"    __trace_id__ varchar,\n" + 
				"    primary key(" + fkFieldName + ")\n" + 
				") with (\n" + 
				"    type = 'QANAT_ADB30',\n" + 
				"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
				"    dbName='" + etlDbName + "',\n" + 
				"    tableName='" + tableName + "',\n" + 
				"    replaceMode = '"  + (fkFieldName.equalsIgnoreCase(pkField) ? "upsert" : "update_by_query") + "',\n" + 
				"    writeMode = 'single',\n" +
	    		"    streamEvent = 'disable'\n" +
				");\n" + 
				"\n" + 
				"insert into adb_sink\n" + 
				"select\n" + 
				"  " + fkFieldName + ",\n" + 
				"  " + arrayFieldName + ",\n" + 
				"  __traceId__\n" + 
				"from mq_source;\n";
		} else {
			ExtensionExample example = new ExtensionExample();
			example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + getDbType(tenantId, etlDbName)).andPluginEqualTo(object.getRef());
			List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
			if (CollectionUtils.isNotEmpty(exts)) {
				if (CollectionUtils.isNotEmpty(object.getRelations())) {
					fkFieldName = object.getRelations().get(0).getRelatedField().split("\\.")[1];
					fkFieldOriginName = object.getRelations().get(0).getField();
					for (ViewModel.Field field : dataModel.getObject().getFields()) {
						if (field.getCode().equalsIgnoreCase(fkFieldName)) {
							fkFieldType = field.getType();
							break;
						}
					}
				} else {
					fkFieldName = getObjectPk(tenantId, exts.get(0).getObjectType(), exts.get(0).getVersion());
					fkFieldType = "bigint";
				}
				componentSql = "select " + fkFieldOriginName + "," + arrayFieldName + " from (" + exts.get(0).getScript().replace("#pkField#", fkFieldName).replace("#arrayField#", arrayFieldName).replace("'", "''") + ") as _t0_ where " + fkFieldOriginName + "=?";
			}
			if (StringUtils.isBlank(componentSql)) {
				log.error("no component conf found, fieldName:{} objectCode:{}", arrayFieldName, object.getRef());
				throw new QanatBizException(object.getRef() + ":no component conf found");
			}
			
			String dsSourceSql = null;
			List<String> idSourceTopics = idSourceTopicInfos.stream().map(e -> e.getString("topicName")).collect(Collectors.toList());
			if (CollectionUtils.isNotEmpty(idSourceTopics)) {
				boolean res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, idSourceTopicInfos.get(0).getString("dbName"), consumerId);
				if (!res) {
					log.error("consumer:{} create is failed", consumerId);
				}
				dsSourceSql =
						"create table mq_source (\n" + 
						"    " + fkFieldName + " " + fkFieldType + ",\n" + 
						"    __traceId__ varchar header\n" + 
						") with (\n" + 
						"  type = 'custom',\n" + 
						"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
						"  topicPattern = '" + StringUtils.join(idSourceTopics, "|") + "',\n" + 
						"  `group.id` = '" + consumerId + "',\n" + 
						"  `dbName` = '" + idSourceTopicInfos.get(0).getString("dbName") + "',\n" +
						"  startupMode = 'TIMESTAMP',\n" +
						"  fieldDelimiter = '`'\n" +
						");\n" + 
						"create view v_id as select " + fkFieldName + ",__traceId__ from mq_source where " + fkFieldName + " is not null;\n" +
						"\n";
			} else {
				if (CollectionUtils.isNotEmpty(object.getRelations())) {
					String relObjectKey = object.getRelations().get(0).getRelatedField().split("\\.")[1];
					String relObjectKeyType = null;
					for (ViewModel.Field field : dataModel.getObject().getFields()) {
						if (field.getCode().equalsIgnoreCase(relObjectKey)) {
							relObjectKeyType = field.getType();
							break;
						}
					}
					String dsName = null;
			    	if ("metadata".equalsIgnoreCase(dataModel.getObject().getType())) {
			    		Datasource objDsInfo = getDsInfoByObjectCode(tenantId, dataModel.getObject().getRef());
				    	dsName = objDsInfo.getDsName();
			    	} else if ("table".equalsIgnoreCase(dataModel.getObject().getType())) {
				    	dsName = dataModel.getObject().getRef();
			    	}
					JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, dsName);

			        dsSourceSql = 
			    			"create table holobinlog_source (\n" + 
			    			"    hg_binlog_lsn BIGINT,\n" +
			    			"    hg_binlog_event_type BIGINT,\n" +
			    			"    hg_binlog_timestamp_us BIGINT,\n" +
			    			"   " + relObjectKey + " " + relObjectKeyType + "\n" + 
			    			") with (\n" + 
			    			"  type = 'hologres',\n" + 
			    			"  dbname = '" + srcDsMetaJson.getJSONObject("odsConf").getString("database") + "',\n" + 
			    			"  tablename = '" + srcDsMetaJson.getJSONObject("odsConf").getString("tableName") + "',\n" + 
			    			"  username = '" + srcDsMetaJson.getJSONObject("odsConf").getString("username") + "',\n" +
			    			"  password = '" + srcDsMetaJson.getJSONObject("odsConf").getString("password") + "',\n" +
			    			"  endpoint = '" + srcDsMetaJson.getJSONObject("odsConf").getString("endpoint") + "',\n" +
			    			"  binlog = 'true',\n" +
			    			"  binlogMaxRetryTimes = '10',\n" +
			    			"  binlogRetryIntervalMs = '500',\n" +
			    			"  binlogBatchReadSize = '256'\n" +
			    			");\n" +
			    			"\n" +
			    			"CREATE FUNCTION trace AS 'com.aliyun.wormhole.qanat.blink.udf.QanatTraceUdf';\n" +
			    			"CREATE FUNCTION flowCtl AS 'com.aliyun.wormhole.qanat.blink.udf.QanatFlowControlUdf';\n" +
			    			"CREATE FUNCTION hololog AS 'com.aliyun.wormhole.qanat.blink.udf.QanatHoloLogTUdf';\n" +
			    			"\n" + 
			    			"create view v_source as \n" + 
			    			"select \n" + 
			    			"    hg_binlog_lsn,\n" + 
			    			"    hg_binlog_event_type,\n" + 
			    			"    hg_binlog_timestamp_us,\n" + 
			    			"    " + relObjectKey + ",\n" + 
			    			"    trace(UUID(),hg_binlog_lsn,hg_binlog_event_type,hg_binlog_timestamp_us," + relObjectKey + ",'" + jobName + "','holobinlog') as __trace_id__\n" + 
			    			"from holobinlog_source;\n" + 
			    			"\n" + 
			    			"create view v_id as \n" + 
			    			"select \n" + 
			    			"    hg_binlog_lsn,\n" + 
			    			"    hg_binlog_event_type,\n" + 
			    			"    hg_binlog_timestamp_us,\n" + 
			    			"    " + relObjectKey + ",\n" + 
			    			"    hololog(flowCtl(__trace_id__,'" + jobName + "',cast(" + relObjectKey + " as varchar)),hg_binlog_lsn,hg_binlog_event_type,hg_binlog_timestamp_us,'rowdata'," + relObjectKey + ") as __traceId__\n" +
			    			"from v_source\n" +
			    			"where " + relObjectKey + " is not null;" +
			    			"\n" ;
				}
			}
			
	        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	        sql = "--SQL\n" + 
	    			"--********************************************************************--\n" + 
	    			"--Author: " + operateEmpid + "\n" + 
	    			"--CreateTime: " + sdf.format(new Date()) + "\n" + 
	    			"--Comment: " + ("sync for " + tableName + "." + arrayFieldName + " from " + object.getCode()) + "\n" + 
	    			"--Version: " + codegenVersion + "\n" + 
	    			"--********************************************************************--\n" + 
	    			dsSourceSql + "\n" +
	    			"\n" + 
	    			"create view v_lookup as\n" + 
	    			"select\n" + 
	    			"  a." + fkFieldName + ",\n" + 
	    			"  JSON_VALUE (b.x, '$." + arrayFieldName + "') as " + arrayFieldName + ",\n" + 
	    			"  a.__traceId__\n" + 
	    			"from v_id as a \n" + 
	    			"left join lateral table(\n" + 
	    			"  queryDim(concat_ws('|',__traceId__,'" + exts.get(0).getDbName() + "'),\n" + 
	    			"    '" + componentSql + "'\n" + 
	    			"    , a." + fkFieldName + ")\n" + 
	    			") as b(x) on true;" + 
	    			"\n" +
	    			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" + 
	    			"\n";

        	String eventTopicName = "stream-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName);
        	boolean kfkRes = kafkaManagementService.createTopic(tenantId, appName, eventTopicName);
    		if (!kfkRes) {
    			log.error("topic:{} create is failed", eventTopicName);
    		}
    		
    		sql += "create table update_sink (\n" + 
    			"    " + fkFieldName + " " + fkFieldType + ",\n" + 
    			"    " + arrayFieldName + " varchar,\n" + 
    			"    __trace_id__ varchar,\n" + 
    			"    primary key(" + fkFieldName + ")\n" + 
    			") with (\n" + 
    			"    type = 'QANAT_ADB30',\n" + 
    			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
    			"    dbName='" + etlDbName + "',\n" + 
    			"    tableName='" + tableName + "',\n" + 
    			"    replaceMode = '" + (fkFieldName.equalsIgnoreCase(pkField) ? "update" : "update_by_query") + "',\n" + 
    			"    writeMode = 'single',\n" +
	    		"    streamEvent = 'disable'\n" +
    			");\n" + 
    			"\n" + 
    			"insert into update_sink\n" + 
    			"select * from v_lookup;\n" +
    			"\n";
		}
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_UDF, ResourcePackage.BLINK_KAFKA010), 
        		false);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        return true;
    }
	
	private Datasource getDsInfoByObjectCode(String tenantId, String objectUniqueCode) {
		DatasourceExample example = new DatasourceExample();
		example.createCriteria().andTableNameEqualTo(objectUniqueCode).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsTypeEqualTo("obj");
		List<Datasource> dsList = dsInfoMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isEmpty(dsList)) {
			throw new QanatBizException(objectUniqueCode + " is not found");
		}
		return dsList.get(0);
	}
	
	private Long getAppIdByName(String tenantId, String appName) {
		AppInfoExample example = new AppInfoExample();
		example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
		List<AppInfo> apps = appInfoMapper.selectByExample(example);
		return apps.get(0).getId();
	}
	
	private String getObjectPk(String tenantId, String objectType, String objectUniqueCode) {
		return dsInfoService.getPkFieldByObjectType(tenantId, objectType, objectUniqueCode);
	}

	public List<JSONObject> getTopicsByComponentObject(String tenantId, DataObject object, String relType, String dstDbType) {
    	List<JSONObject> topics = new ArrayList<>();
    	ExtensionExample example = new ExtensionExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + dstDbType).andPluginEqualTo(object.getRef());
		List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
		
		if (CollectionUtils.isEmpty(exts)) {
			return topics;
		}
		
		ComponentDsRelationExample comDsRelExample = new ComponentDsRelationExample();
		comDsRelExample.createCriteria().andTenantIdEqualTo(tenantId).andComponentNameEqualTo(exts.get(0).getCode()).andRelationTypeEqualTo(relType);
		List<ComponentDsRelation> comRsRels = componentDsRelationMapper.selectByExample(comDsRelExample);
		if (CollectionUtils.isEmpty(comRsRels)) {
			log.error("no component ds found, componentName:{} relType:{}", exts.get(0).getCode(), relType);
			return topics;
		}
		List<String> dsNames = comRsRels.stream().map(e->e.getDsName()).collect(Collectors.toList());
		
		DatasourceExample dsExample = new DatasourceExample();
		dsExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsNameIn(dsNames);
		List<Datasource> dsList = dsInfoMapper.selectByExample(dsExample);
		if (CollectionUtils.isEmpty(dsList) || dsList.size() != comRsRels.size()) {
			log.error("[{}] no ds found or ds conf missing", StringUtils.join(dsNames, ","));
			throw new QanatBizException("[" + StringUtils.join(dsNames, ",") + "] no ds found or ds conf missing");
		}
		for (Datasource dsInfo : dsList) {
			JSONObject incrConfJson = new JSONObject();
			incrConfJson.put("type", dsInfo.getDsType());
			incrConfJson.put("topicName", dsInfo.getTableName());
			incrConfJson.put("dbName", dsInfo.getDbName());
			DbInfo dbInfo = getDbInfoByName(dsInfo.getTenantId(),dsInfo.getDbName());
			if (JSON.parseObject(dbInfo.getMeta()) != null && StringUtils.isNotBlank(JSON.parseObject(dbInfo.getMeta()).getString("dbName"))) {
				incrConfJson.put("dbName", JSON.parseObject(dbInfo.getMeta()).getString("dbName"));
			}
			topics.add(incrConfJson);
		}
		return topics;
    }

    private DbInfo getDbInfoByName(String tenantId, String dbName) {
        DbInfoExample dbExample = new DbInfoExample();
        dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName).andTenantIdEqualTo(tenantId);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("db:" + dbName + " not found");
        }
        DbInfo dbInfo = dbs.get(0);
        return dbInfo;
    }
}
