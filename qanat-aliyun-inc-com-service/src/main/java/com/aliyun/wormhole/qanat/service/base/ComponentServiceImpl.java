package com.aliyun.wormhole.qanat.service.base;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.ComponentRequest;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.service.ComponentService;
import com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelation;
import com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsRelation;
import com.aliyun.wormhole.qanat.dal.domain.DsRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.Extension;
import com.aliyun.wormhole.qanat.dal.domain.ExtensionExample;
import com.aliyun.wormhole.qanat.dal.mapper.ComponentDsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ExtensionMapper;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

/**
 * 数据源服务service
 * <AUTHOR>
 * 2019年7月23日
 */
@Slf4j
@Component
@HSFProvider(serviceInterface = ComponentService.class)
public class ComponentServiceImpl implements ComponentService {

	@Resource
    private DatasourceMapper datasourceMapper;
    
    @Resource
    private DatasourceMapper dsInfoMapper;
    
    @Resource
    private DsRelationMapper dsRelMapper;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private ExtensionMapper extensionMapper;
    
    @Resource
    private ComponentDsRelationMapper compDsRelationMapper;

    @Override
    public DataResult<Long> createComponent(ComponentRequest request) {
        log.info("start createComponet({})", JSON.toJSONString(request));
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	String componentName = null;
        	String relateDsName = null;
        	String script = null;
        	
        	if ("func-dfaas".equalsIgnoreCase(request.getType())
        			|| "func-groovy".equalsIgnoreCase(request.getType())) {
        		Extension record = new Extension();
            	record.setCode(request.getName());
            	record.setGmtCreate(new Date());
            	record.setGmtModified(new Date());
            	record.setIsDeleted(0L);
            	record.setPlugin(request.getType().split("-")[1]);
            	record.setScript(script);
            	record.setTenantId(request.getTenantId());
            	record.setType(request.getType().split("-")[0]);
            	extensionMapper.insert(record);
            	result.setData(record.getId());
            	return result;
        	}
        	
        	DatasourceExample dsExample = new DatasourceExample();
        	dsExample.createCriteria().andDsNameEqualTo(request.getRelDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(request.getTenantId());
        	List<Datasource> dsInfos = dsInfoMapper.selectByExample(dsExample);
        	if (CollectionUtils.isEmpty(dsInfos)) {
        		throw new QanatBizException("DsInfo:" + request.getRelDsName() + " is not exsited");
        	}
        	Datasource relDsInfo = dsInfos.get(0);
        	if ("kafka".equalsIgnoreCase(relDsInfo.getDsType())) {
        		relateDsName = relDsInfo.getDsName();
        	} else if ("mysql".equalsIgnoreCase(relDsInfo.getDsType())
        			|| "tddl".equalsIgnoreCase(relDsInfo.getDsType())) {
        		DbInfoExample dbExample = new DbInfoExample();
        		dbExample.createCriteria().andDbNameEqualTo(request.getWorkDbName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(request.getTenantId());
            	List<DbInfo> dbInfos = dbInfoMapper.selectByExample(dbExample);
            	if (CollectionUtils.isEmpty(dbInfos)) {
            		throw new QanatBizException("DbInfo:" + request.getWorkDbName() + " is not exsited");
            	}
            	DbInfo workDbInfo = dbInfos.get(0);
        		if ("adb3".equalsIgnoreCase(workDbInfo.getDbType())
        				|| "hologres".equalsIgnoreCase(workDbInfo.getDbType())) {
        			//Componet计算逻辑在源库执行,使用源表的drc数据源作为组件的增量源
        			//查找源表对应ODS数据源
        			DsRelationExample dsRelExp = new DsRelationExample();
        			dsRelExp.createCriteria().andTenantIdEqualTo(request.getTenantId())
        									.andIsDeletedEqualTo(0L)
        									.andRelationTypeEqualTo("ods")
        									.andSrcDsNameEqualTo(relDsInfo.getDsName());
        			List<DsRelation> dsRels = dsRelMapper.selectByExample(dsRelExp);
        			if (CollectionUtils.isEmpty(dsRels)) {
                		throw new QanatBizException("DsRelation for dsName:" + relDsInfo.getDsName() + " with relType:ods is not exsited");
                	}
        			String odsDsName = dsRels.get(0).getDstDsName();
        			//查找ODS表对应增量数据源
        			dsRelExp = new DsRelationExample();
        			dsRelExp.createCriteria().andTenantIdEqualTo(request.getTenantId())
        									.andIsDeletedEqualTo(0L)
        									.andRelationTypeEqualTo("post_ods")
        									.andDstDsNameEqualTo(odsDsName);
        			dsRels = dsRelMapper.selectByExample(dsRelExp);
        			if (CollectionUtils.isEmpty(dsRels)) {
                		throw new QanatBizException("DsRelation for dsName:" + odsDsName + " with post_ods is not exsited");
                	}
        			relateDsName = dsRels.get(0).getSrcDsName();
        		} else {
        			//Componet计算逻辑在源库执行,使用源表的drc数据源作为组件的增量源
        			DsRelationExample dsRelExp = new DsRelationExample();
        			dsRelExp.createCriteria().andTenantIdEqualTo(request.getTenantId())
        									.andIsDeletedEqualTo(0L)
        									.andRelationTypeEqualTo("incr")
        									.andDstDsNameEqualTo(relDsInfo.getDsName());
        			List<DsRelation> dsRels = dsRelMapper.selectByExample(dsRelExp);
        			if (CollectionUtils.isEmpty(dsRels)) {
                		throw new QanatBizException("DsRelation for dsName:" + relDsInfo.getDsName() + " with relType:incr is not exsited");
                	}
        			relateDsName = dsRels.get(0).getSrcDsName();
        		}
        	} else if ("adb3".equalsIgnoreCase(relDsInfo.getDsType())
        			|| "hologres".equalsIgnoreCase(relDsInfo.getDsType())) {
        		DsRelationExample dsRelExp = new DsRelationExample();
    			dsRelExp.createCriteria().andTenantIdEqualTo(request.getTenantId())
    									.andIsDeletedEqualTo(0L)
    									.andRelationTypeEqualTo("post_ods")
    									.andDstDsNameEqualTo(relDsInfo.getDsName());
    			List<DsRelation> dsRels = dsRelMapper.selectByExample(dsRelExp);
    			if (CollectionUtils.isEmpty(dsRels)) {
            		throw new QanatBizException("DsRelation for dsName:" + relDsInfo.getDsName() + " with post_ods is not exsited");
            	}
    			relateDsName = dsRels.get(0).getSrcDsName();
        	} else {
        		throw new QanatBizException("Not support relDsType:" + request.getRelDsName() + "[" + relDsInfo.getDsType() + "]");
        	}
        	
        	if ("component-adb3".equalsIgnoreCase(request.getType())) {
        		componentName = "adb3-" + request.getName() + "-sql";
        		
	        	ComponentDsRelationExample cdrExample = new ComponentDsRelationExample();
	        	cdrExample.createCriteria().andComponentNameEqualTo(componentName).andDsNameEqualTo(relateDsName).andRelationTypeEqualTo(request.getRelType());
	        	compDsRelationMapper.deleteByExample(cdrExample);
	        	
	        	ComponentDsRelation relation = new ComponentDsRelation();
	        	relation.setComponentName(componentName);
	        	relation.setDsName(relateDsName);
	        	relation.setGmtCreate(new Date());
	        	relation.setRelationType(request.getRelType());
	        	relation.setTenantId(request.getTenantId());
	        	compDsRelationMapper.insert(relation);
	        	
	        	script = request.getSql();
        	} else if ("component-hologres".equalsIgnoreCase(request.getType())) {
        		componentName = "hologres-" + request.getName() + "-sql";
        		
	        	ComponentDsRelationExample cdrExample = new ComponentDsRelationExample();
	        	cdrExample.createCriteria().andComponentNameEqualTo(componentName).andDsNameEqualTo(relateDsName).andRelationTypeEqualTo(request.getRelType());
	        	compDsRelationMapper.deleteByExample(cdrExample);
	        	
	        	ComponentDsRelation relation = new ComponentDsRelation();
	        	relation.setComponentName(componentName);
	        	relation.setDsName(relateDsName);
	        	relation.setGmtCreate(new Date());
	        	relation.setRelationType(request.getRelType());
	        	relation.setTenantId(request.getTenantId());
	        	compDsRelationMapper.insert(relation);
	        	
	        	script = request.getSql();
        	} else if ("component-stream".equalsIgnoreCase(request.getType())) {
        		componentName = "stream-" + request.getName();
        		script = "{\"topicName\":\"" + relDsInfo.getTableName() + "\"}";
        	} else {
        		throw new QanatBizException("Not support component type:" + request.getType());
        	}
        	ExtensionExample example = new ExtensionExample();
        	example.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(request.getTenantId()).andCodeEqualTo(componentName);
        	List<Extension> exts = extensionMapper.selectByExample(example);
        	if (CollectionUtils.isNotEmpty(exts)) {
        		throw new QanatBizException("component:" + request.getName() + " with type:" + request.getType() + " is already existd");
        	}
        	Extension record = new Extension();
        	record.setCode(componentName);
        	record.setDbName(request.getWorkDbName());
        	record.setGmtCreate(new Date());
        	record.setGmtModified(new Date());
        	record.setIsDeleted(0L);
        	record.setObjectType(request.getObjectType());
        	record.setPlugin(request.getName());
        	record.setRemark(request.getDesc());
        	record.setScript(script);
        	record.setTenantId(request.getTenantId());
        	record.setType(request.getType());
        	extensionMapper.insert(record);
        	
            result.setData(record.getId());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("createComponet failed, error={}", e.getMessage(), e);
        }
        return result;
    }
}