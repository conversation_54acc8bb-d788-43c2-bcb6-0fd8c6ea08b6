package com.aliyun.wormhole.qanat.service.flink;

import com.alibaba.fastjson.JSON;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.aliyun.ververica_inner20220718.Client;
import com.aliyun.ververica_inner20220718.models.Deployment;
import com.aliyun.ververica_inner20220718.models.DeploymentRestoreStrategy;
import com.aliyun.ververica_inner20220718.models.GetJobHeaders;
import com.aliyun.ververica_inner20220718.models.GetJobResponse;
import com.aliyun.ververica_inner20220718.models.Job;
import com.aliyun.ververica_inner20220718.models.JobStartParameters;
import com.aliyun.ververica_inner20220718.models.ListDeploymentsHeaders;
import com.aliyun.ververica_inner20220718.models.ListDeploymentsRequest;
import com.aliyun.ververica_inner20220718.models.ListDeploymentsResponse;
import com.aliyun.ververica_inner20220718.models.ListJobsHeaders;
import com.aliyun.ververica_inner20220718.models.ListJobsRequest;
import com.aliyun.ververica_inner20220718.models.ListJobsResponse;
import com.aliyun.ververica_inner20220718.models.LocalVariable;
import com.aliyun.ververica_inner20220718.models.StartJobWithParamsRequest;
import com.aliyun.ververica_inner20220718.models.StartJobWithParamsResponse;
import com.aliyun.ververica_inner20220718.models.StopJobHeaders;
import com.aliyun.ververica_inner20220718.models.StopJobRequest;
import com.aliyun.ververica_inner20220718.models.StopJobRequestBody;
import com.aliyun.ververica_inner20220718.models.StopJobResponse;
import com.ververica.common.model.deploymenttarget.DeploymentTarget;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Data
public class FlinkClientV2 {

	private static final int MAX_RETRY_COUNT = 200;
	private static final int SLEEP_MS = 3000;
	public static List<String> unexpectedFinalStatus = Arrays.asList("FAILED", "SUSPENDED", "CANCELLED");

	private FlinkConf conf;
	private Client client;

	public FlinkClientV2(FlinkConf conf) throws Exception {
		log.debug("flinkConfig={}", JSON.toJSONString(conf));
		Config config = new com.aliyun.teaopenapi.models.Config();
		config.setAccessKeyId(conf.getAccessId());
		config.setAccessKeySecret(conf.getAccessKey());
		config.setEndpoint(conf.getEndpoint());
		client = new Client(config);
		this.conf = conf;
	}

	/**
	 * 创建任务
	 *
	 * @param jobName
	 */
	public boolean createJob(String jobName, String sql, boolean isBatch) {
		log.info("begin to createJob({},{},{})", jobName, sql, isBatch);
		try {

		} catch (Exception e) {
			log.error("createJob({}) failed:{}", jobName, e.getMessage(), e);
		}
		return false;
	}

	/**
	 * 更新任务SQL
	 * @param jobName
	 * @param sql
	 * @return
	 */
	public boolean updateJobSql(String jobName, String sql) {
		log.info("begin to updateJobSql({},{})", jobName, sql);
		try {
		} catch (Exception e) {
			log.error("updateJobSql({}) failed:{}", jobName, e.getMessage(), e);
		}
		return false;
	}

	/**
	 * 更新任务引擎版本
	 * @param jobName
	 * @param version
	 * @return
	 */
	public boolean updateJobVersion(String jobName, String version) {
		log.info("begin to updateJobVersion({},{})", jobName, version);
		try {
		} catch (Exception e) {
			log.error("updateJobVersion({}) failed:{}", jobName, e.getMessage(), e);
		}
		return false;
	}

	/**
	 * 更新任务引擎
	 * @param jobName
	 * @param deploymentTarget
	 * @return
	 */
	public boolean updateJobEngine(String jobName, String deploymentTarget) {
		log.info("begin to updateJobEngine({},{})", jobName, deploymentTarget);
		try {
		} catch (Exception e) {
			log.error("updateJobEngine({}) failed:{}", jobName, e.getMessage(), e);
		}
		return false;
	}

	/**
	 * 根据任务名称获取DeploymentId
	 * @param jobName
	 * @return
	 */
	private String getDeploymentIdByName(String jobName) {
		log.info("begin to getDeploymentIdByName({})", jobName);
		Deployment deployment = this.getDeploymentByName(jobName);
		return deployment != null ? deployment.getDeploymentId() : null;
	}

	/**
	 * 根据任务名称获取DeploymentId
	 * @param jobName
	 * @return
	 */
	private Deployment getDeploymentByName(String jobName) {
		log.info("begin to getDeploymentByName({})", jobName);
		try {
			ListDeploymentsRequest listDeploymentsRequest = new ListDeploymentsRequest();
			listDeploymentsRequest.setPageIndex(1); // pageIndex must be larger than 0
			listDeploymentsRequest.setPageSize(1000); // pageSize must be between with 1 and 1000
			listDeploymentsRequest.setName(jobName);

			ListDeploymentsHeaders listDeploymentsHeaders = new ListDeploymentsHeaders();
			listDeploymentsHeaders.setWorkspace(conf.getWorkspace());

			ListDeploymentsResponse result =
					client.listDeploymentsWithOptions(
							conf.getNamespace(), listDeploymentsRequest, listDeploymentsHeaders, new RuntimeOptions());

			if (result != null && result.getBody() != null) {
				for (Deployment deployment : result.getBody().getData()) {
					if (jobName.equalsIgnoreCase(deployment.getName())) {
						return deployment;
					}
				}
			}
		} catch (Exception e) {
			log.error("getDeploymentByName({}) failed, error={}", jobName, e.getMessage(), e);
		}
		return null;
	}

	/**
	 * 启动任务
	 *
	 * @param jobName
	 * @param startTime
	 */
	public boolean startJob(String jobName, Date startTime) {
		return startJob(jobName, startTime, null);
	}

	/**
	 * 启动任务
	 *
	 * @param jobName
	 * @param startTime
	 */
	public boolean startJob(String jobName, Date startTime, Map<String, String> params) {
		log.info("begin to startJob({},{},{})", jobName, startTime, JSON.toJSONString(params));
		try {
			// restoreStrategy
			DeploymentRestoreStrategy restoreStrategy = new DeploymentRestoreStrategy();
			restoreStrategy.setKind("NONE");
			restoreStrategy.setAllowNonRestoredState(true);
			if (startTime != null) {
				restoreStrategy.setJobStartTimeInMs(startTime.getTime());
			}

			// start job requestBody
			JobStartParameters jobStartParameters = new JobStartParameters();
			jobStartParameters.setDeploymentId(getDeploymentIdByName(jobName));
			if (params != null && CollectionUtils.isNotEmpty(params.keySet())) {
				List<LocalVariable> localVariables = new ArrayList<>();
				for (String key : params.keySet()) {
					LocalVariable localVariable = new LocalVariable();
					localVariable.setName(key);
					localVariable.setValue(params.get(key));
					localVariables.add(localVariable);
				}
				jobStartParameters.setLocalVariables(localVariables);
			}
			jobStartParameters.setRestoreStrategy(restoreStrategy);

			StartJobWithParamsRequest startJobRequest = new StartJobWithParamsRequest();
			startJobRequest.setBody(jobStartParameters);

			StartJobWithParamsResponse result =
					client.startJobWithParams(
							conf.getNamespace(), startJobRequest);
			assert result != null;
			boolean apiResult = false;
			if (!result.getBody().getSuccess()) {
				int retries = 0;
				// 重试3次
				while (retries < 3) {
					log.info("retry startJob({}) {}times", jobName, retries + 1);
					result =
							client.startJobWithParams(
									conf.getNamespace(), startJobRequest);
					assert result != null;
					if (result.getBody().getSuccess()) {
						apiResult = true;
						break;
					}
					try {
						Thread.sleep(500);
					} catch (InterruptedException e) {}
					retries++;
				}
			} else {
				apiResult = true;
			}
			if (!apiResult) {
				throw new RuntimeException(
						String.format("Failed to start job, errorCode: %s, errorMessage: %s.",
								result.getBody().errorCode, result.getBody().errorMessage));
			}
			return waitUntilDesiredStatus(result.getBody().getData(), "RUNNING");
		} catch (Exception e) {
			log.error("startJob({}) failed:{}", jobName, e.getMessage(), e);
		}
		return false;
	}

	public boolean startBatchJob(String jobName, Map<String, String> params) {
		log.info("begin to startBatchJob({},{})", jobName, JSON.toJSONString(params));
		try {
			// restoreStrategy
			DeploymentRestoreStrategy restoreStrategy = new DeploymentRestoreStrategy();
			restoreStrategy.setKind("NONE");
			restoreStrategy.setAllowNonRestoredState(true);

			// start job requestBody
			JobStartParameters jobStartParameters = new JobStartParameters();
			jobStartParameters.setDeploymentId(getDeploymentIdByName(jobName));
			if (params != null && CollectionUtils.isNotEmpty(params.keySet())) {
				List<LocalVariable> localVariables = new ArrayList<>();
				for (String key : params.keySet()) {
					LocalVariable localVariable = new LocalVariable();
					localVariable.setName(key);
					localVariable.setValue(params.get(key));
					localVariables.add(localVariable);
				}
				jobStartParameters.setLocalVariables(localVariables);
			}
			jobStartParameters.setRestoreStrategy(restoreStrategy);

			StartJobWithParamsRequest startJobRequest = new StartJobWithParamsRequest();
			startJobRequest.setBody(jobStartParameters);

			StartJobWithParamsResponse result =
					client.startJobWithParams(
							conf.getNamespace(), startJobRequest);
			assert result != null;
			boolean apiResult = false;
			if (!result.getBody().getSuccess()) {
				int retries = 0;
				// 重试3次
				while (retries < 3) {
					log.info("retry startBatchJob({}) {}times", jobName, retries + 1);
					result =
							client.startJobWithParams(
									conf.getNamespace(), startJobRequest);
					assert result != null;
					if (result.getBody().getSuccess()) {
						apiResult = true;
						break;
					}
					try {
						Thread.sleep(500);
					} catch (InterruptedException e) {}
					retries++;
				}
			} else {
				apiResult = true;
			}
			if (!apiResult) {
				throw new RuntimeException(
						String.format("Failed to start job, errorCode: %s, errorMessage: %s.",
								result.getBody().errorCode, result.getBody().errorMessage));
			}
			return waitUntilDesiredStatus(result.getBody().getData(), "FINISHED");
		} catch (Exception e) {
			log.error("startBatchJob({}) failed:{}", jobName, e.getMessage(), e);
		}
		return false;
	}

	/**
	 * 启动任务
	 *
	 * @param jobName
	 * @param params
	 */
	public boolean restartJob(String jobName, Map<String, String> params) {
		log.info("begin to startJob({},{})", jobName, JSON.toJSONString(params));
		try {
			if (this.stopJob(jobName)) {
				// restoreStrategy
				DeploymentRestoreStrategy restoreStrategy = new DeploymentRestoreStrategy();
				restoreStrategy.setKind("LATEST_STATE");
				restoreStrategy.setAllowNonRestoredState(false);

				// start job requestBody
				JobStartParameters jobStartParameters = new JobStartParameters();
				jobStartParameters.setDeploymentId(getDeploymentIdByName(jobName));
				if (params != null && CollectionUtils.isNotEmpty(params.keySet())) {
					List<LocalVariable> localVariables = new ArrayList<>();
					for (String key : params.keySet()) {
						LocalVariable localVariable = new LocalVariable();
						localVariable.setName(key);
						localVariable.setValue(params.get(key));
						localVariables.add(localVariable);
					}
					jobStartParameters.setLocalVariables(localVariables);
				}
				jobStartParameters.setRestoreStrategy(restoreStrategy);

				StartJobWithParamsRequest startJobRequest = new StartJobWithParamsRequest();
				startJobRequest.setBody(jobStartParameters);

				StartJobWithParamsResponse result =
						client.startJobWithParams(
								conf.getNamespace(), startJobRequest);
				assert result != null;
				boolean apiResult = false;
				if (!result.getBody().getSuccess()) {
					throw new RuntimeException(
							String.format("Failed to start job, errorCode: %s, errorMessage: %s.",
									result.getBody().errorCode, result.getBody().errorMessage));
				}
				return waitUntilDesiredStatus(result.getBody().getData(), "RUNNING");
			}
		} catch (Exception e) {
			log.error("startJob({}) failed:{}", jobName, e.getMessage(), e);
		}
		return false;
	}

	private boolean waitUntilDesiredStatus(Job job, String desiredStatus)
			throws Exception {
		int retryCount = 0;
		Job currentJob = getJob(job.getJobId());
		while (retryCount++ < MAX_RETRY_COUNT) {
			assert currentJob != null;
			String currentJobStatus = currentJob.getStatus().getCurrentJobStatus();
			if (currentJobStatus.equals(desiredStatus)) {
				return true;
			}
			boolean reachUnexpectedFinalStatus =
					!unexpectedFinalStatus.contains(desiredStatus)
							&& unexpectedFinalStatus.contains(currentJobStatus);
			if (reachUnexpectedFinalStatus) {
				return false;
			}
			Thread.sleep(SLEEP_MS);
			currentJob = getJob(job.getJobId());
		}
		return false;
	}

	public Job getJob(String jobId) {
		try {
			GetJobHeaders getJobHeaders = new GetJobHeaders();
			getJobHeaders.setWorkspace(conf.getWorkspace());

			GetJobResponse result =
					client.getJobWithOptions(conf.getNamespace(), jobId, getJobHeaders, new RuntimeOptions());
			assert result != null;
			if (!result.getBody().getSuccess()) {
				throw new RuntimeException(
						String.format("Failed to get job, errorCode: %s, errorMessage: %s.",
								result.getBody().errorCode, result.getBody().errorMessage));
			}
			return result.getBody().getData();
		} catch (Exception e) {
			log.error("getJob({}) failed, error={}", jobId, e.getMessage(), e);
		}
		return null;
	}

	private Job getRunningJobByName(String jobName) throws Exception {
		ListJobsRequest listJobsRequest = new ListJobsRequest();
		listJobsRequest.setDeploymentId(getDeploymentIdByName(jobName));
		listJobsRequest.setPageIndex(1); // pageIndex must be larger than 0
		listJobsRequest.setPageSize(100); // pageSize must be between with 1 and 1000

		ListJobsHeaders listJobsHeaders = new ListJobsHeaders();
		listJobsHeaders.setWorkspace(conf.getWorkspace());

		ListJobsResponse result =
				client.listJobsWithOptions(
						conf.getNamespace(), listJobsRequest, listJobsHeaders, new RuntimeOptions());
		assert result != null;
		boolean apiResult = false;
		if (!result.getBody().getSuccess()) {
			int retries = 0;
			// 重试3次
			while (retries < 3) {
				log.info("retry listJobsWithOptions({}) {}times", jobName, retries + 1);
				result =
						client.listJobsWithOptions(
								conf.getNamespace(), listJobsRequest, listJobsHeaders, new RuntimeOptions());
				assert result != null;
				if (result.getBody().getSuccess()) {
					apiResult = true;
					break;
				}
				try {
					Thread.sleep(500);
				} catch (InterruptedException e) {}
				retries++;
			}
		} else {
			apiResult = true;
		}
		if (!apiResult) {
			throw new RuntimeException(
					String.format("Failed to list jobs, errorCode: %s, errorMessage: %s.",
							result.getBody().errorCode, result.getBody().errorMessage));
		}
		if (result.getBody() != null && result.getBody().getData() != null) {
			for (Job job : result.getBody().getData()) {
				if (job.getStatus().getCurrentJobStatus().equalsIgnoreCase("RUNNING")) {
					return job;
				}
			}
		}
		log.info("{} has no running jobs", jobName);
		return null;
	}

	/**
	 * 停止任务
	 *
	 * @param jobName
	 */
	public boolean stopJob(String jobName) {
		log.info("begin to stopJob({})", jobName);
		try {
			StopJobRequestBody stopJobRequestBody = new StopJobRequestBody();
			stopJobRequestBody.setStopStrategy("NONE");

			StopJobRequest stopJobRequest = new StopJobRequest();
			stopJobRequest.setBody(stopJobRequestBody);

			StopJobHeaders stopJobHeaders = new StopJobHeaders();
			stopJobHeaders.setWorkspace(conf.getWorkspace());

			Job job = getRunningJobByName(jobName);
			if (job == null
					|| "CANCELLED".equalsIgnoreCase(job.getStatus().getCurrentJobStatus())
					|| "FINISHED".equalsIgnoreCase(job.getStatus().getCurrentJobStatus())
					|| "FAILED".equalsIgnoreCase(job.getStatus().getCurrentJobStatus())) {
				log.info("job({}) is already stopped", jobName);
				return true;
			}

			StopJobResponse result =
					client.stopJobWithOptions(
							conf.getNamespace(), job.getJobId(), stopJobRequest, stopJobHeaders, new RuntimeOptions());
			assert result != null;
			boolean apiResult = false;
			if (!result.getBody().getSuccess()) {
				int retries = 0;
				// 重试3次
				while (retries < 3) {
					log.info("retry stopJob({}) {}times", jobName, retries + 1);
					result =
							client.stopJobWithOptions(
									conf.getNamespace(), job.getJobId(), stopJobRequest, stopJobHeaders, new RuntimeOptions());
					assert result != null;
					if (result.getBody().getSuccess()) {
						apiResult = true;
						break;
					}
					try {
						Thread.sleep(500);
					} catch (InterruptedException e) {}
					retries++;
				}
			} else {
				apiResult = true;
			}
			if (!apiResult) {
				throw new RuntimeException(
						String.format("Failed to stop job, errorCode: %s, errorMessage: %s.",
								result.getBody().errorCode, result.getBody().errorMessage));
			}
			return waitUntilDesiredStatus(result.getBody().getData(), "CANCELLED");
		} catch (Exception e) {
			log.error("stopJob({}) failed:{}", jobName, e.getMessage(), e);
		}
		return false;
	}

	public static void main(String[] args) throws Exception {
		FlinkConf conf = new FlinkConf();
		conf.setRegionId("center");
		conf.setNamespace("datatube");
		conf.setWorkspace("default");
		conf.setProduct("ververica");
		conf.setEndpoint("ververica-share.aliyuncs.com");
		conf.setDeploymentTarget("asi-zjk-flink-c02_na63blinkcptssd1_aly-devata-customer");

		FlinkClientV2 client = new FlinkClientV2(conf);

//		System.out.println(client.getDeploymentIdByName("incrsync-new_ppl-followup_parent"));

//		Map<String, String> params = new HashMap<>();
//		params.put("indexName", "indexName");
//		client.startJob("test_params", null, params);

//		System.out.println(JSON.toJSONString(client.getRunningJobByName("incrsync-dwd_devata_cpid_base-org")));

//		System.out.println("begin to stop job");
//		client.stopJob("fullsync-new_ppl-partner_bd_acl");
//		System.out.println("job is stopped");
//		System.out.println("begin to start job");
//		client.startJob("fullsync-new_ppl-partner_bd_acl", new Date(), null);
//		System.out.println("job is started");

		client.restartJob("fullsync-new_ppl-partner_bd_acl", null);
	}
}
