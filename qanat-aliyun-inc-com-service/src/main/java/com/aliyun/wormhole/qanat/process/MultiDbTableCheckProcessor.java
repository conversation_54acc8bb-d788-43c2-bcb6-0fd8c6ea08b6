package com.aliyun.wormhole.qanat.process;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastsql.DbType;
import com.alibaba.fastsql.sql.SQLUtils;
import com.alibaba.fastsql.sql.ast.SQLStatement;
import com.alibaba.fastsql.sql.ast.statement.SQLColumnDefinition;
import com.alibaba.fastsql.sql.ast.statement.SQLCreateTableStatement;
import com.alibaba.fastsql.sql.dialect.mysql.ast.statement.MySqlCreateTableStatement;
import com.alibaba.fastsql.sql.visitor.SchemaStatVisitor;
import com.alibaba.fastsql.stat.TableStat.Column;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.security.SecurityUtil;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelHandler;
import com.taobao.unifiedsession.core.commons.utils.DateUtils;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 多库数据稽核任务
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class MultiDbTableCheckProcessor extends JavaProcessor {

	@Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private ViewModelHandler viewModelHandler;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            log.info("MultiDbTableCheckProcessor, param=[]", context.getJobParameters());
            JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
            String tenantId = paramsJson.getString("tenantId");
            String checkCode = paramsJson.getString("checkCode");
            Boolean ifMakeupFields = paramsJson.getBoolean("makeupFields");
            ifMakeupFields = ifMakeupFields == null ? false : ifMakeupFields;
            String tableNames = paramsJson.getString("tableNames");
            String [] tableNameArray = tableNames.split(",");
            
            String etlDbName = viewModelHandler.getEtlDbName(tenantId);
            List<String> extDbNames = viewModelHandler.getDstDbNames(tenantId);
            extDbNames.remove(etlDbName);
            
        	for (String tableName : tableNameArray) {
            	log.info("start to check table:{}", tableName);
            	
            	Map<String, Object> checkResult = new HashMap<>();
            	Map<String, Long> metric = new HashMap<>();
            	checkResult.put("metric", metric);
            	checkResult.put("batchNo", "TableCheck-" + context.getJobInstanceId());
            	Map<String, String> dbInfo = new HashMap<>();
            	checkResult.put("dbInfo", dbInfo);
            	Map<String, Long> fromDsMetric = new HashMap<>();
            	checkResult.put("tableName", tableName);
            	checkResult.put("checkTime", DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            	try {
                	dbInfo.put("db0", etlDbName);
                	List<DsFieldInfo> fromTableFields = getFields(etlDbName, tableName);
                	if (CollectionUtils.isEmpty(fromTableFields)) {
                		checkResult.put("ddl_check", "get ddl from table:" + etlDbName + "." + tableName + " failed");
                    	log.info("qanat_multidb_tablecheck {} result={}", checkCode, JSON.toJSONString(checkResult));
                		continue;
                	}
                	String pkField = null;
                	if (CollectionUtils.isNotEmpty(fromTableFields.stream().filter(e -> e.getIsPk().intValue() == 1).collect(Collectors.toList()))) {
                		pkField = fromTableFields.stream().filter(e -> e.getIsPk().intValue() == 1).collect(Collectors.toList()).get(0).getFieldName();
                	}
                	fromDsMetric = this.getTableMetrics(etlDbName, tableName, pkField);
                	if (fromDsMetric == null || CollectionUtils.isEmpty(fromDsMetric.keySet())) {
                    	log.error("can not get table metric from {}.{}", etlDbName, tableName);
                    	log.info("qanat_multidb_tablecheck {} result={}", checkCode, JSON.toJSONString(checkResult));
                		continue;
                	}
                	metric.put("db0_total_cnt", fromDsMetric.get("total_cnt"));
                	metric.put("db0_max_id", fromDsMetric.get("max_id"));
                	
                	for (int i = 0; i < extDbNames.size(); i++) {
	                	dbInfo.put("db" + (i + 1), extDbNames.get(i));
	                	List<DsFieldInfo> toTablefields = getFields(extDbNames.get(i), tableName);
	                	if (CollectionUtils.isEmpty(toTablefields)) {
	                		checkResult.put("ddl_check", "get ddl from table:" + extDbNames.get(i) + "." + tableName + " failed");
	                		continue;
	                	} else if (fromTableFields.size() != toTablefields.size()) {
	                		checkResult.put("ddl_check", "the field count:" + toTablefields.size() +  " of table:" + extDbNames.get(i) + "." + tableName + " is not equal to " + fromTableFields.size() + " of table:" + etlDbName + "." + tableName);
	                		if (ifMakeupFields) {
	                			List<String> toTablefieldNames = toTablefields.stream().map(e -> e.getFieldName()).collect(Collectors.toList());
	                			for (DsFieldInfo fromField : fromTableFields) {
	                				if (!toTablefieldNames.contains(fromField.getFieldName())) {
	                					addField(extDbNames.get(i), tableName, fromField);
	                				}
	                			}
	                		}
	                		continue;
	                	}
	                	Map<String, Long> toDsMetric = this.getTableMetrics(extDbNames.get(i), tableName, pkField);
	                	if (toDsMetric == null || CollectionUtils.isEmpty(toDsMetric.keySet())) {
	                    	log.error("can not get table metric from {}.{}", extDbNames.get(i), tableName);
	                		continue;
	                	}
	                	metric.put("db" + (i + 1) + "_total_cnt", toDsMetric.get("total_cnt"));
	                	metric.put("db" + (i + 1) + "_max_id", toDsMetric.get("max_id"));
	                	metric.put("db" + (i + 1) + "_total_cnt_gap", fromDsMetric.get("total_cnt") - toDsMetric.get("total_cnt"));
                		metric.put("db" + (i + 1) + "_max_id_gap", fromDsMetric.get("max_id") - toDsMetric.get("max_id"));
                		if (fromDsMetric.get("total_cnt").intValue() == toDsMetric.get("total_cnt").intValue()
                				&& fromDsMetric.get("max_id").intValue() == toDsMetric.get("max_id").intValue()) {
                			checkResult.put("metric_check", "success");
                		} else {
                			checkResult.put("metric_check", "failed");
                		}
            		}
                	log.info("qanat_multidb_tablecheck {} result={}", checkCode, JSON.toJSONString(checkResult));
                	log.info("finish to check table:{}", tableName);
            	} catch(Exception e) {
                	log.error("failed to check table:{}, error={}", tableName, e.getMessage(), e);
            	}
        	}
        } catch (QanatBizException e) {
            log.error("MultiDbTableCheckProcessor任务调度异常:{}", e.getMessage());
            return new ProcessResult(false, e.getMessage());
        } catch (Exception e) {
            log.error("MultiDbTableCheckProcessor任务调度异常", e);
            return new ProcessResult(false, e.getMessage());
        }
        return new ProcessResult(true);
    }
    
    private void addField(String dbName, String tableName, DsFieldInfo field) {
    	Connection connection = null;
    	Statement statement = null;
	    try {
		    JSONObject dbMetaJson = getAdbDbMeta(dbName);
	    	RdsConnectionParam param = new RdsConnectionParam();
		    param.setUrl(dbMetaJson.getString("jdbcUrl"))
		        .setUserName(dbMetaJson.getString("username"))
		        .setPassword(dbMetaJson.getString("password"));
	        connection = dsHandler.connectToTable(param);
	        statement = connection.createStatement();
            statement.execute("ALTER TABLE " + tableName + " ADD COLUMN " + field.getFieldName() + " " + field.getFieldType());
	    } catch (Exception e) {
	        log.error("get table data monitor failed, error={}", e.getMessage(), e);
	    } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
            if (connection != null) {
                try {
                	connection.close();
                } catch (SQLException e) {
                }
                connection = null;
            }
	    }
	}

	private Map<String, Long> getTableMetrics(String dbName, String tableName, String pkField) {
    	String sql = null;
    	if (StringUtils.isNotBlank(pkField)) {
    		sql = "select count(1) as total_cnt, max(" + pkField + ") as max_id from " + tableName;
    	} else {
    		sql = "select count(1) as total_cnt, 1 as max_id from " + tableName;
    	}
    	
	    Connection connection = null;
	    try {
		    JSONObject dbMetaJson = getAdbDbMeta(dbName);
	    	RdsConnectionParam param = new RdsConnectionParam();
		    param.setUrl(dbMetaJson.getString("jdbcUrl"))
		        .setUserName(dbMetaJson.getString("username"))
		        .setPassword(dbMetaJson.getString("password"));
	        connection = dsHandler.connectToTable(param);

	        return querySql(connection, dbName, sql);
        	
	    } catch (Exception e) {
	        log.error("get table data monitor failed, error={}", e.getMessage(), e);
	        throw new QanatBizException(e.getMessage());
	    } finally {
            if (connection != null) {
                try {
                	connection.close();
                } catch (SQLException e) {
                }
                connection = null;
            }
	    }
    }

    private JSONObject getAdbDbMeta(String dbName) {
        DbInfoExample example = new DbInfoExample();
        example.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("no db found");
        }
        String dbMeta = dbs.get(0).getMeta();
        JSONObject dbMetaJson = JSON.parseObject(dbMeta);
        dbMetaJson.put("dbType", dbs.get(0).getDbType());
        return dbMetaJson;
    }

    private Map<String, Long> querySql(Connection connection, String dbName, String sql) {
        Map<String, Long> data = new HashMap<>();
        log.info("before exec dbName={} sql={}", dbName, sql);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            Long startTs = System.currentTimeMillis();
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                data.put("total_cnt", resultSet.getLong("total_cnt"));
                data.put("max_id", resultSet.getLong("max_id"));
            }
            log.info("after exec sql data={} cost={}", JSON.toJSONString(data), System.currentTimeMillis() - startTs);
        } catch(Exception e) {
            log.error("querySql failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                }
                resultSet = null;
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
        }
        return data;
    }

	private String getCreateTableDdl(JSONObject dbMetaJson, String tableName) {
		String ddl = null;
		Connection connection = null;
		Statement statement = null;
		ResultSet resultSet = null;
		String sql = null;
		try {
			if (tableName.startsWith("view_") && tableName.equalsIgnoreCase("view_telesale_solution_test")) {
				sql = "SHOW CREATE MATERIALIZED VIEW " + SecurityUtil.trimSql(tableName) + ";";
			} else if (tableName.startsWith("view_") || tableName.endsWith("_view")) {
				sql = "SHOW CREATE VIEW " + SecurityUtil.trimSql(tableName) + ";";
			} else {
				sql = "SHOW CREATE TABLE " + SecurityUtil.trimSql(tableName) + ";";
			}
		    log.info("show create table sql:{}", sql);
		    RdsConnectionParam param = new RdsConnectionParam();
		    param.setUrl(dbMetaJson.getString("jdbcUrl"))
			    .setUserName(SecurityUtil.trimSql(dbMetaJson.getString("username")))
			    .setPassword(dbMetaJson.getString("password"));
		    connection = dsHandler.connectToTable(param);
		    statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
		    resultSet = statement.executeQuery(sql);
		    if(resultSet.next()) {
		        ddl = resultSet.getString(2);
			    log.info("ddl:{}", ddl);
		    }
		} catch(Exception e) {
		    log.error("sql exec failed, e={}", e.getMessage());
		} finally {
		    if (resultSet != null) {
		        try {
		            resultSet.close();
		        } catch (Exception e) {
		        }
	            resultSet = null;
		    }
		    if (statement != null) {
		        try {
		            statement.close();
		        } catch (Exception e) {
		        }
	            statement = null;
		    }
		    if (connection != null) {
		        try {
		            connection.close();
		        } catch (Exception e) {
		        }
	            connection = null;
		    }
		}
		return ddl;
	}
	
	private List<DsFieldInfo> getFields(String dbName, String tableName) {
		List<DsFieldInfo> fields = new ArrayList<>();
	    JSONObject dbMetaJson = getAdbDbMeta(dbName);
	    String ddl = this.getCreateTableDdl(dbMetaJson, tableName);
	    if (StringUtils.isBlank(ddl)) {
	    	return fields;
	    }
	    if (tableName.startsWith("view_") || tableName.endsWith("_view")) {
	    	DsFieldInfo field = new DsFieldInfo();
			fields.add(field);
			field.setFieldName("id");
			field.setIsPk(Byte.valueOf("1"));
	    	return fields;
	    }
		DbType dbType = DbType.mysql;
		if (dbMetaJson.getString("dbType").equalsIgnoreCase("odps")) {
			ddl = ddl.replaceAll("`", "");
			dbType = DbType.odps;
		} else if (dbMetaJson.getString("dbType").equalsIgnoreCase("mysql") || dbMetaJson.getString("dbType").equalsIgnoreCase("tddl")) {
			dbType = DbType.mysql;
		} else if (dbMetaJson.getString("dbType").equalsIgnoreCase("adb3")) {
			dbType = DbType.mysql;
			ddl = ddl.split("DISTRIBUTE BY")[0];
		} else if (dbMetaJson.getString("dbType").equalsIgnoreCase("postgresql") || dbMetaJson.getString("dbType").equalsIgnoreCase("hologres")) {
			ddl = ddl.replaceAll("`", "");
			dbType = DbType.postgresql;
		}
		List<SQLStatement> stmtList = SQLUtils.parseStatements(ddl, dbType);
		SQLStatement stmt = stmtList.get(0);
		SchemaStatVisitor statVisitor = SQLUtils.createSchemaStatVisitor(dbType);
		stmt.accept(statVisitor);
		List<String> pkList = statVisitor.getColumns().stream().filter(column -> column.isPrimaryKey()).map(Column::getName).collect(Collectors.toList());
		if (dbMetaJson.getString("dbType").equalsIgnoreCase("mysql") || dbMetaJson.getString("dbType").equalsIgnoreCase("tddl") || dbMetaJson.getString("dbType").equalsIgnoreCase("adb3")) {
			MySqlCreateTableStatement createStmt = (MySqlCreateTableStatement)stmt;
			for (SQLColumnDefinition col : createStmt.getColumnDefinitions()) {
				DsFieldInfo field = new DsFieldInfo();
				fields.add(field);
				field.setFieldName(col.getColumnName().replaceAll("`", ""));
				field.setFieldType(col.getDataType().getName());
				field.setFieldDesc(col.getComment() == null ? null : col.getComment().toString().replaceAll("'", ""));
				if (pkList.contains(col.getColumnName())) {
					field.setIsPk(Byte.valueOf("1"));
				} else {
					field.setIsPk(Byte.valueOf("0"));
				}
			}
		} else if (dbMetaJson.getString("dbType").equalsIgnoreCase("postgresql") || dbMetaJson.getString("dbType").equalsIgnoreCase("hologres")) {
			SQLCreateTableStatement createStmt = (SQLCreateTableStatement)stmt;
			for (SQLColumnDefinition col : createStmt.getColumnDefinitions()) {
				DsFieldInfo field = new DsFieldInfo();
				fields.add(field);
				field.setFieldName(col.getColumnName().replaceAll("`", ""));
				field.setFieldType(col.getDataType().getName());
				if (pkList.contains(col.getColumnName())) {
					field.setIsPk(Byte.valueOf("1"));
				} else {
					field.setIsPk(Byte.valueOf("0"));
				}
			}
		}
		return fields;
	}
    
    @Override
    public void kill(JobContext context) {
        
    }
}