package com.aliyun.wormhole.qanat.service.viewmodel.v2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.DagPolicy;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.DatasourceRequest;
import com.aliyun.wormhole.qanat.api.dto.TaskInfoRequest;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTaskExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.Extension;
import com.aliyun.wormhole.qanat.dal.domain.ExtensionExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfoWithBLOBs;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfo;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelDsRelation;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfo;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelTaskRelation;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelTaskRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelVersionWithBLOBs;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.service.util.YamlUtil;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.AddOn;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.DataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Field;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.RelatedDataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Settings;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelHandler;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * ADB实时数仓同步服务
 * <AUTHOR>
 * 2019年7月23日
 */
@Slf4j
@Component
public class ViewModelHandlerV2 extends ViewModelHandler {
    
    @Resource
    private ComponentObjectProcessorV2 componentObjectProcessorV2;
    
    @Resource
    private FunctionObjectProcessorV2 functionObjectProcessorV2;
    
    @Resource
    private LookupProcessorV2 lookupProcessorV2;
    
    @Resource
    private TableObjectProcessorV2 tableObjectProcessorV2;
    
    @Resource
    private TableAggrProcessorV2 tableAggrProcessorV2;
    
    @Transactional(propagation = Propagation.REQUIRED)
    public Long createBatchStreamTasks(String tenantId, Long viewModelId, String operateEmpid) {
        log.info("start createBatchStreamTasks({},{},{})", tenantId, viewModelId, operateEmpid);

    	TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(tenantId);
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + tenantId + " is not configured");
    	}
    	TenantInfo tenantInfo = tenantList.get(0);
        
        String datatubeLevel = null;
        DatatubeInstanceExample diExample = new DatatubeInstanceExample();
        diExample.createCriteria().andTenantIdEqualTo(tenantId).andProviderEqualTo("viewmodel").andIsDeletedEqualTo(0L).andProviderIdEqualTo(viewModelId);
        List<DatatubeInstance> dis = datatubeInstanceMapper.selectByExample(diExample);
        if (CollectionUtils.isEmpty(dis)) {
        	throw new QanatBizException("viewModelId:" + viewModelId + " is not related to any datatube instance");
        }
        DatatubeInstance datatubeInst =  dis.get(0);
        
    	datatubeLevel = datatubeInst.getLevel();
    	
    	List<String> dbNames = getDstDbNames(tenantInfo, datatubeInst);
    	
		String mainDbName = this.getMainDbName(tenantInfo);
    	
    	String etlDbName = this.getEtlDbName(tenantInfo);
    
    	List<String> extDbNames = getExtDbNames(tenantInfo, datatubeInst);
    	
    	DatatubeInstanceTaskExample ditExample = new DatatubeInstanceTaskExample();
    	ditExample.createCriteria().andTenantIdEqualTo(tenantId).andDatatubeInstIdEqualTo(datatubeInst.getId()).andIsDeletedEqualTo(0L);
    	DatatubeInstanceTask updDatatubeInstanceTask = new DatatubeInstanceTask();
    	updDatatubeInstanceTask.setIsDeleted(1L);
    	updDatatubeInstanceTask.setModifyEmpid(operateEmpid);
    	updDatatubeInstanceTask.setGmtModified(new Date());
    	datatubeInstanceTaskMapper.updateByExampleSelective(updDatatubeInstanceTask, ditExample);
        
    	ViewModelInfoExample example = new ViewModelInfoExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andIdEqualTo(viewModelId).andIsDeletedEqualTo(0L);
		List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(viewModelInfos)) {
			throw new QanatBizException("tenant check failed");
		}
		ViewModelInfo viewModelInfo = viewModelInfos.get(0);
    	
    	DbInfoExample dbInfoExample = new DbInfoExample();
    	dbInfoExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDbNameEqualTo(mainDbName);
    	List<DbInfo> dbInfos = dbInfoMapper.selectByExample(dbInfoExample);
    	if (CollectionUtils.isEmpty(dbInfos)) {
    		throw new QanatBizException("DbInfo not found:" + mainDbName);
    	}
		
        ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());

        ViewModel originModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
    	ViewModel sysModel = null;
    	if (originModel.isDynamic()) {
    		sysModel = viewModelOptimizer.getOptimizedViewModel(tenantId, modelVersion.getUserYaml());
    	} else {
    		sysModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
    	}
    	ViewModel dataModel = new ViewModel();
    	BeanUtils.copyProperties(sysModel, dataModel);
        String tableName = dataModel.getCode();
        List<String> batchJobs = new ArrayList<>();
        List<String> streamJobs = new ArrayList<>();
        Map<String, Field> correctTopics = new HashMap<>();
        
    	JSONObject appKafkaJson = kafkaManagementService.getKafkaConfByAppName(tenantId, viewModelInfo.getAppName());

        DataObject mainObject = dataModel.getObject();
        Field pkField = mainObject.getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0);
        String pkFieldName = pkField.getCode();
        boolean isMetricTableUpsert = false;
        Long appId = getAppIdByName(tenantId, viewModelInfo.getAppName());
		Long dstDsId = dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName);
    	String logTopicName = "stream-" + appId + "-" + dstDsId + "-" + mainObject.getCode();
		if ("metadata".equalsIgnoreCase(mainObject.getType())) {
        	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsUniqueName(tenantId, mainObject.getRef());
	    	JSONObject drcTopicInfo = srcDsMetaJson.getJSONObject("incrConf");
	    	String drcTopicName = drcTopicInfo.getString("topicName");
	    	String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_v" + viewModelInfo.getVersionId();
        	if (mdpObjectProcessor.processIncrSyncJob(tenantId, viewModelInfo.getAppName(), jobName, srcDsMetaJson, dbNames, mainDbName, tableName, mainObject, operateEmpid, CollectionUtils.isEmpty(originModel.getObject().getFields()) || dataModel.isDynamic(), viewModelInfo.getVersionId(), appKafkaJson, drcTopicName, logTopicName, dataModel, datatubeLevel, datatubeInst.getId())) {
        		streamJobs.add(jobName);
        	}
        	String drcTopicNameBatch = srcDsMetaJson.getJSONObject("incrConf").getString("topicNameBatch");
	    	String logTopicNameBatch = "stream-" + appId + "-" + dstDsId + "-" + mainObject.getCode() + "-batch";
	    	String jobNameBatch = "incrsync_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_batch_v" + viewModelInfo.getVersionId();
        	if (StringUtils.isNotBlank(drcTopicNameBatch) && mdpObjectProcessor.processIncrSyncJob(tenantId, viewModelInfo.getAppName(), jobNameBatch, srcDsMetaJson, dbNames, mainDbName, tableName, mainObject, operateEmpid, CollectionUtils.isEmpty(originModel.getObject().getFields()) || dataModel.isDynamic(), viewModelInfo.getVersionId(), appKafkaJson, drcTopicNameBatch, logTopicNameBatch, dataModel, datatubeLevel, datatubeInst.getId())) {
        		streamJobs.add(jobNameBatch);
        	}
        } else if ("table".equalsIgnoreCase(mainObject.getType())) {
        	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, mainObject.getRef());
        	if ("metric".equals(srcDsMetaJson.getString("sysType"))) {
        		isMetricTableUpsert = true;
        		tableObjectProcessorV2.processBatchSyncJob(tenantId, viewModelInfo.getAppName(), dbNames, etlDbName, tableName, mainObject, operateEmpid, viewModelInfo.getVersionId(), viewModelInfo.getModelName(), appKafkaJson, isMetricTableUpsert, dataModel.getSettings(), datatubeInst.getId());
        	} else {
        		String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_v" + viewModelInfo.getVersionId();
        		if (tableObjectProcessorV2.processMainObjectIncrSyncJob(tenantId, viewModelInfo.getAppName(), jobName, srcDsMetaJson, etlDbName, tableName, mainObject, operateEmpid, viewModelInfo.getVersionId(), datatubeLevel, datatubeInst.getId(), dataModel)) {
            		streamJobs.add(jobName);
            	}
        	}
        }

		Map<ViewModel.Field, Map<String, List<ViewModel.Field>>> fieldObjFieldMap = new HashMap<>();
		Map<ViewModel.Field, List<ViewModel.Field>> fieldFuncFieldsMap = new HashMap<>();
		Map<ViewModel.Field, String> fieldFuncCodeMap = new HashMap<>();
    	for (ViewModel.Field field : mainObject.getFields()) {
    		if (field.getObject() != null) {
    			if ("metadata".equalsIgnoreCase(field.getObject().getType())) {
    				String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + field.getObject().getCode() + "_v" + viewModelInfo.getVersionId();
                	if (mdpObjectProcessor.processAggrIncrSyncJob(tenantId, viewModelInfo.getAppName(), jobName, mainDbName, tableName, field.getCode(), field.getObject(), operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, datatubeInst.getId())) {
                    	streamJobs.add(jobName);
        			}
                } else if ("table".equalsIgnoreCase(field.getObject().getType())) {
                	String jobName = tableAggrProcessorV2.processIncrSyncJob(tenantId, viewModelInfo.getAppName(), etlDbName, tableName, field.getCode(), field.getObject(), operateEmpid, viewModelInfo.getVersionId(), datatubeInst.getId());
                	streamJobs.add(jobName);
                } else if ("component".equalsIgnoreCase(field.getObject().getType())) {
                	JSONObject streamTopicInfo = null;
                	ExtensionExample extExample = new ExtensionExample();
                	extExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-stream").andPluginEqualTo(field.getObject().getRef());
        			List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(extExample);
        			if (CollectionUtils.isNotEmpty(exts)) {
        				streamTopicInfo = JSON.parseObject(exts.get(0).getScript());
        				
        				DatasourceExample dsExample = new DatasourceExample();
        				dsExample.createCriteria().andTableNameEqualTo(streamTopicInfo.getString("topicName")).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
        				List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(dsExample);
        				if (CollectionUtils.isEmpty(dsList)) {
        					throw new QanatBizException(streamTopicInfo.getString("topicName") + " is not found in dsInfo");
        				}
        				streamTopicInfo.put("type", dsList.get(0).getDsType());
        				streamTopicInfo.put("dbName", dsList.get(0).getDbName());
        			}
                	List<JSONObject> idSourceTopics = new ArrayList<>();
                	if (streamTopicInfo == null) {
                		idSourceTopics = componentObjectProcessorV2.getTopicsByComponentObject(tenantId, field.getObject(), "id_in", dbInfos.get(0).getDbType());
                	}
                	String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + field.getCode() + "_v" + viewModelInfo.getVersionId();
                	if (componentObjectProcessorV2.processIncrSyncJobAsField(tenantId, viewModelInfo.getAppName(), jobName, etlDbName, tableName, field.getCode(), field.getObject(), operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, streamTopicInfo, idSourceTopics, pkFieldName, dataModel, datatubeInst.getId())) {
                    	streamJobs.add(jobName);
                    }
                }
    		}
    		if (field.isFunc()) {
    			String funcExpress = field.getRef();
    			String funcCode = lookupProcessorV2.parseFuncExpress(funcExpress).get(0);
    			String[] cols = lookupProcessorV2.parseFuncExpress(funcExpress).get(1).split(",");
    			Map<String, List<ViewModel.Field>> objFieldMap = new HashMap<>();
    			List<ViewModel.Field> funcFields  = new ArrayList<>();
    			for (String col : cols) {
    				if (col.startsWith("'") && col.endsWith("'")) {
    					ViewModel.Field funcField = new ViewModel.Field();
    					funcField.setRef(col);
    					funcFields.add(funcField);
    					continue;
    				}
    				String[] tokens = col.split("\\.");
    				if (tokens.length == 2) {
    					DataObject obj = null;
    					if (tokens[0].equalsIgnoreCase(mainObject.getCode())) {
    						obj = mainObject;
    					} else {
    						obj = dataModel.getRelatedObjects().stream().filter(e->e.getCode().equalsIgnoreCase(tokens[0])).collect(Collectors.toList()).get(0);
    					}
    					ViewModel.Field funcField = obj.getFields().stream().filter(e->e.getCode().equalsIgnoreCase(tokens[1])).collect(Collectors.toList()).get(0);
    					funcFields.add(funcField);
    					if (objFieldMap.get(obj.getCode()) == null) {
    						List<ViewModel.Field> list = new ArrayList<>();
    						list.add(funcField);
    						objFieldMap.put(obj.getCode(), list);
    					} else {
    						objFieldMap.get(obj.getCode()).add(funcField);
    					}
    				} else {
    					ViewModel.Field funcField = mainObject.getFields().stream().filter(e->e.getCode().equalsIgnoreCase(tokens[0])).collect(Collectors.toList()).get(0);
    					funcFields.add(funcField);
    					if (objFieldMap.get(mainObject.getCode()) == null) {
    						List<ViewModel.Field> list = new ArrayList<>();
    						list.add(funcField);
    						objFieldMap.put(mainObject.getCode(), list);
    					} else {
    						objFieldMap.get(mainObject.getCode()).add(funcField);
    					}
    				}
    			}
    			
    			String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + field.getCode() + "_v" + viewModelInfo.getVersionId();
            	if (functionObjectProcessorV2.processIncrSyncJob(tenantId, appId, viewModelInfo.getAppName(), jobName, dataModel, mainDbName, tableName, dstDsId, field, objFieldMap,  operateEmpid, viewModelInfo.getVersionId(), pkFieldName, funcFields, funcCode, datatubeInst.getId())) {
                	streamJobs.add(jobName);
                }
            	fieldObjFieldMap.put(field, objFieldMap);
            	fieldFuncFieldsMap.put(field, funcFields);
            	fieldFuncCodeMap.put(field, funcCode);
            	
    		}
    	}
    	if (CollectionUtils.isNotEmpty(fieldObjFieldMap.keySet())) {
        	String jobName = "fullsync_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_func_v" + viewModelInfo.getVersionId();
        	String tmpTableName = getTmpTableName(tableName, viewModelInfo.getVersionId());
        	if (functionObjectProcessorV2.processBatchSyncJob(tenantId, appId, viewModelInfo.getAppName(), jobName, dataModel, etlDbName, tmpTableName, tableName, dstDsId, fieldObjFieldMap.keySet(), fieldObjFieldMap,  operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, pkFieldName, fieldFuncFieldsMap, fieldFuncCodeMap, datatubeInst.getId())) {
            	batchJobs.add(jobName);
            }
    	}
        
        if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
        	for (ViewModel.RelatedDataObject relatedObject : dataModel.getRelatedObjects()) {
    			if ("metadata".equalsIgnoreCase(relatedObject.getType())) {
    				ViewModel.RelatedDataObject originRelatedObject = null;
    				for (ViewModel.RelatedDataObject obj : originModel.getRelatedObjects()) {
    					if (obj.getRef().equalsIgnoreCase(relatedObject.getRef())) {
    						originRelatedObject = obj;
    						break;
    					}
    				}
                	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsUniqueName(tenantId, mainObject.getRef());
        	    	JSONObject drcTopicInfo = srcDsMetaJson.getJSONObject("incrConf");
        	    	String drcTopicName = drcTopicInfo.getString("topicName");
        	    	String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + relatedObject.getCode() + "_v" + viewModelInfo.getVersionId();
        	    	logTopicName = "stream-" + appId + "-" + dstDsId + "-" + relatedObject.getCode();
    				if (mdpObjectProcessor.processIncrSyncJob(tenantId, viewModelInfo.getAppName(), jobName, srcDsMetaJson, dbNames, mainDbName, tableName, relatedObject, operateEmpid, CollectionUtils.isEmpty(originRelatedObject.getFields()) || dataModel.isDynamic(), viewModelInfo.getVersionId(), appKafkaJson, drcTopicName, logTopicName, dataModel, datatubeLevel, datatubeInst.getId())) {
                		streamJobs.add(jobName);
                	}
                } else if ("table".equalsIgnoreCase(relatedObject.getType())) {
                	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, relatedObject.getRef());
                	if ("metric".equals(srcDsMetaJson.getString("sysType"))) {
                		tableObjectProcessorV2.processBatchSyncJob(tenantId, viewModelInfo.getAppName(), dbNames, etlDbName, tableName, relatedObject, operateEmpid, viewModelInfo.getVersionId(), viewModelInfo.getModelName(), appKafkaJson, isMetricTableUpsert, dataModel.getSettings(), datatubeInst.getId());
                	} else {
                    	String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + relatedObject.getCode() + "_v" + viewModelInfo.getVersionId();
                		if (tableObjectProcessorV2.processRelatedObjectIncrSyncJob(tenantId, viewModelInfo.getAppName(), jobName, etlDbName, tableName, relatedObject, operateEmpid, viewModelInfo.getVersionId(), datatubeLevel, datatubeInst.getId(), pkFieldName, dataModel)) {
                    		streamJobs.add(jobName);
                    	}
                	}
                } else if ("component".equalsIgnoreCase(relatedObject.getType())) {
                	List<JSONObject> drcSourceTopics = componentObjectProcessorV2.getTopicsByComponentObject(tenantId, relatedObject, "drc_in", dbInfos.get(0).getDbType());
                	if (CollectionUtils.isNotEmpty(drcSourceTopics)) {
	                	String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + relatedObject.getCode() + "_v" + viewModelInfo.getVersionId();
	                	if (componentObjectProcessorV2.processIncrSyncJobAsObject(tenantId, viewModelInfo.getAppName(), jobName, etlDbName, tableName, relatedObject, operateEmpid, viewModelInfo.getVersionId(), dataModel, datatubeInst.getId())) {
	                    	streamJobs.add(jobName);
	                    }
                	}
                }
        	}
        }
        boolean lookup = false;
        List<String> objCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
            for (RelatedDataObject relObj : dataModel.getRelatedObjects()) {
            	if (relObj.getRelations().stream().filter(e->
            													!e.getRelatedField().startsWith("exp#")
            													&&(!e.getRelatedField().split("\\.")[0].equalsIgnoreCase(dataModel.getObject().getCode())
            															||!e.getRelatedField().split("\\.")[1].equalsIgnoreCase(pkFieldName))).count() > 0) {
            		lookup = true;
            		break;
            	} else if (!"none".equalsIgnoreCase(relObj.getLookupFrom())) {
            		objCodeList.add(relObj.getCode());
            	}
            }
        }
        if (!lookup) {
            for (ViewModel.Field field : dataModel.getObject().getFields()) {
            	if (field.getObject() != null) {
            		if (CollectionUtils.isNotEmpty(field.getObject().getRelations())) {
                    	if (field.getObject().getRelations().stream().filter(e->
																				!e.getRelatedField().startsWith("exp#")
																				&&(!e.getRelatedField().split("\\.")[0].equalsIgnoreCase(dataModel.getObject().getCode())
																						||!e.getRelatedField().split("\\.")[1].equalsIgnoreCase(pkFieldName))).count() > 0) {
							lookup = true;
							break;
						}
            		}
            	} else if (field.isFunc() || field.getRef().startsWith("exp#")) {
            		lookup = true;
					break;
            	} else if (field.getObject() != null && !"none".equalsIgnoreCase(field.getObject().getLookupFrom())) {
            		objCodeList.add(field.getObject().getCode());
            	}
            }
        }
        if (dataModel.getSettings().isLookupOptimize() && CollectionUtils.isNotEmpty(objCodeList)) {
        	String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_lookup_v" + viewModelInfo.getVersionId();
        	if (lookupProcessorV2.processIncrSyncJob(tenantId, appId, viewModelInfo.getAppName(), jobName, dataModel, etlDbName, tableName, dstDsId, operateEmpid, viewModelInfo.getVersionId(), pkFieldName, objCodeList, datatubeInst.getId())) {
            	streamJobs.add(jobName);
            }
        } else if (lookup) {
        	String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_lookup_v" + viewModelInfo.getVersionId();
        	if (dataModel.getSettings().isLookupOptimize()) {
            	if (lookupProcessorV2.processIncrSyncJob(tenantId, appId, viewModelInfo.getAppName(), jobName, dataModel, etlDbName, tableName, dstDsId, operateEmpid, viewModelInfo.getVersionId(), pkFieldName, null, datatubeInst.getId())) {
                	streamJobs.add(jobName);
                }
        	}
        }
        //目标表级别的订正任务
        if (CollectionUtils.isNotEmpty(correctTopics.keySet()) && !"offhand".equalsIgnoreCase(dataModel.getSettings().getCorrectPolicy())) {
        	String correctJobName = mdpObjectProcessor.processCorrectJob(tenantId, viewModelInfo.getAppName(), mainDbName, tableName, operateEmpid, viewModelInfo.getVersionId(), dataModel, correctTopics, appKafkaJson, datatubeInst.getId());
        	streamJobs.add(correctJobName);
        }
        
        //附加任务
        if (CollectionUtils.isNotEmpty(dataModel.getAddOns())) {
        	for (AddOn addOn : dataModel.getAddOns()) {
        		if ("blink_stream".equalsIgnoreCase(addOn.getType())) {
        			streamJobs.add(addOn.getRef());
        		} else if ("blink_batch".equalsIgnoreCase(addOn.getType())) {
        			batchJobs.add(addOn.getRef());
        		}
        	}
        }
        
        //新增数据同步DAG任务
        Long taskId = processViewModelDAGTask(tenantId, viewModelInfo.getAppName(), operateEmpid, modelVersion.getViewModelId(), etlDbName, tableName, streamJobs, viewModelInfo.getVersionId(), batchJobs, dataModel.getSettings(), extDbNames, dataModel, datatubeInst);
        if (taskId == null) {
            throw new QanatBizException("任务创建失败");
        }

        try {
	        ViewModelTaskRelation record = new ViewModelTaskRelation();
	        record.setCreateEmpid(operateEmpid);
	        record.setGmtCreate(new Date());
	        record.setGmtModified(new Date());
	        record.setIsDeleted(0L);
	        record.setModelVersionId(viewModelInfo.getVersionId());
	        record.setModifyEmpid(operateEmpid);
	        record.setTaskId(taskId);
	        record.setTenantId(tenantId);
	        record.setViewModelName(viewModelInfo.getModelName());
	        record.setRelationType("main");
	        viewModelTaskRelationMapper.insert(record);
        } catch(Exception e) {}
        
        if (!"minor".equalsIgnoreCase(datatubeLevel)) {
        	this.createBatchCheckTask(tenantId, viewModelId, operateEmpid);
        }
        
        return taskId;
    }
    
    @Transactional(propagation = Propagation.REQUIRED)
    public Boolean createTableAndFullSync(String tenantId, Long viewModelId, String batchJobs) {
    	log.info("createTableAndFullSync({},{},{},{}) start", tenantId, viewModelId, batchJobs);
        Statement statement = null;
        Connection connection = null;
        try {
        	ViewModelInfoExample example = new ViewModelInfoExample();
    		example.createCriteria().andTenantIdEqualTo(tenantId).andIdEqualTo(viewModelId).andIsDeletedEqualTo(0L);
    		List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(example);
    		if (CollectionUtils.isEmpty(viewModelInfos)) {
    			throw new QanatBizException("tenant check failed");
    		}
    		ViewModelInfo viewModelInfo = viewModelInfos.get(0);
            ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());
            ViewModel originModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
	    	ViewModel dataModel = getSysViewModel(tenantId, modelVersion, originModel);
	    	
	    	String etlDbName = getEtlDbName(tenantId);
	    	
    		String workTableName = getTmpTableName(dataModel.getCode(), modelVersion.getId());
    		
            log.info("vm[{}] start to build[{}.{}]", viewModelId, etlDbName, workTableName);
            createTable(tenantId, dataModel, viewModelInfo.getObjectType(), etlDbName, workTableName, true);
            log.info("vm[{}] work table[{}.{}] created", viewModelId, etlDbName, workTableName);

            log.info("vm[{}] fullsync to [{}.{}] started", viewModelId, etlDbName, workTableName);
            Boolean isSuccess = syncfullDataForAdbTable(tenantId, dataModel, etlDbName, workTableName);
            if (!isSuccess) {
                log.error("vm[{}] fullsync to [{}.{}] failed", viewModelId, etlDbName, workTableName);
                throw new QanatBizException("vm:" + viewModelId + " full data sync to " + etlDbName + "." + workTableName + " failed");
            }
            log.info("vm[{}] fullsync to [{}.{}] finished", viewModelId, etlDbName, workTableName);
	    	
            if (StringUtils.isNotBlank(batchJobs)) {
            	String[] jobs = batchJobs.split(",");
            	if (jobs.length > 0) {
                    log.info("vm[{}] start to run blinkbatch jobs", viewModelId, batchJobs);
            		for (int i=0; i<jobs.length; i++) {
            			String jobName = jobs[i];
                        log.info("batch job[{}] start", jobName);
                        Map<String, String> params = new HashMap<>();
                        params.put("tableName", workTableName);
            			Long blinkInstId = blinkService.startBatchJob(tenantId, viewModelInfo.getAppName(), jobName, params);
                        if (blinkInstId != null) {
                            while (true) {
                                String instState = blinkService.getInstanceActualState(tenantId, viewModelInfo.getAppName(), jobName, blinkInstId);
                                if ("SUCCESS".equalsIgnoreCase(instState)) {
                                    break;
                                }
                                Thread.sleep(60000);//60s
                            }
                        }
                        log.info("batch job[{}] finished", jobName);
            		}
            	}
            }
            log.info("start to offline last version tasks");
            offlineLastVersionTasks(tenantId, "schedulerx2", viewModelInfo);
            log.info("finished to offline last version tasks");

            JSONObject dbMetaJson = dsInfoService.getDbMetaByName(etlDbName);
            RdsConnectionParam param = new RdsConnectionParam();
            param.setUrl(dbMetaJson.getString("jdbcUrl"))
	            .setUserName(dbMetaJson.getString("username"))
	            .setPassword(dbMetaJson.getString("password"));
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement();
            String finalTableName = dataModel.getCode();
            String backupTableName = "bak_" + finalTableName + "_" + viewModelInfo.getVersionId() + "_" + DateUtils.formatDate(new Date(), "yyMMddHHmm");
            
            statement = connection.createStatement();
            try {
                statement.execute("alter table " + finalTableName + " rename to " + backupTableName);
            } catch (Exception e) {
                log.error("alter final table name to backup failed, error={}", e.getMessage());
            }
            statement.execute("alter table " + workTableName + " rename to " + finalTableName);
            
            DatasourceRequest dsInfoReq = new DatasourceRequest();
            dsInfoReq.setTenantId(tenantId);
            dsInfoReq.setDsName(dsInfoService.getDsName(tenantId, viewModelInfo.getAppName(), etlDbName, finalTableName));
            dsInfoReq.setOperateEmpid("schedulerx2");
            dsInfoService.modifyDatasource(dsInfoReq);
            log.info("update dsInfo[{}] finished", dsInfoReq.getDsName());
            
            //重新生成从库临时表
        	List<String> extDbNames = getExtDbNames(tenantId);
        	if (CollectionUtils.isNotEmpty(extDbNames)) {
        		for (String extDbName : extDbNames) {
		        	String tmpTableName = "tmp_" + dataModel.getCode();
		            log.info("vm[{}] start to build[{}.{}] created", viewModelId, extDbName, tmpTableName);
		    		createTable(tenantId, dataModel, viewModelInfo.getObjectType(), extDbName, tmpTableName, true);
		            log.info("vm[{}] work table[{}.{}] created", viewModelId, extDbName, tmpTableName);
        		}
            	
            	//重新生成从库复制批量任务
        		try {
	            	DatatubeInstanceExample diExample = new DatatubeInstanceExample();
	            	diExample.createCriteria().andTenantIdEqualTo(tenantId).andProviderIdEqualTo(viewModelId).andProviderEqualTo("viewmodel");
	            	List<DatatubeInstance> datatubeInsts = datatubeInstanceMapper.selectByExample(diExample);
	                if (CollectionUtils.isNotEmpty(datatubeInsts)) {
		                this.rebuildMultiDbSyncJob(datatubeInsts.get(0).getId());
			            log.info("vm[{}] rebuildMultiDbSyncJob finished", viewModelId);
	                }
        		} catch (Exception e) {
        			log.error("重新生成从库复制批量任务失败:{}, vmId={}", e.getMessage(), viewModelId, e);
        		}
        	}
            
            return true;
        } catch (Exception e) {
            log.error("createTableAndFullSync({},{},{}) failed:{}",  tenantId, viewModelId, batchJobs, e.getMessage(), e);
            throw new QanatBizException("createTableAndFullSync faild:" + e.getMessage());
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                } finally {
                	statement = null;
                }
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                } finally {
                	connection = null;
                }
            }
        }
    }
	
    private Boolean syncfullDataForAdbTable(String tenantId, ViewModel dataModel, String dbName, String tableName) {
        log.info("start syncfullDataForAdbTable({},{},{},{})",tenantId, JSON.toJSONString(dataModel), dbName, tableName);
        Statement statement = null;
        Connection connection = null;
        try {
	    	DbInfoExample dbInfoExample = new DbInfoExample();
	    	dbInfoExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
	    	List<DbInfo> dbInfos = dbInfoMapper.selectByExample(dbInfoExample);
	    	if (CollectionUtils.isEmpty(dbInfos)) {
	    		throw new QanatBizException("DbInfo not found:" + dbName);
	    	}
            String sql = viewModelSqlBuilder.getLoadDataSql4Adb(tenantId, dataModel, tableName, dbInfos.get(0).getDbType());
            
            RdsConnectionParam param = new RdsConnectionParam();
            JSONObject dbMetaJson = dsInfoService.getDbMetaByName(dbName);
            param.setUrl(dbMetaJson.getString("jdbcUrl"))
    	        .setUserName(dbMetaJson.getString("username"))
    	        .setPassword(dbMetaJson.getString("password"));
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement();
            
    		log.info("before exec sql=[{}]", sql);
            
            statement = connection.createStatement();
            long startTs = System.currentTimeMillis();
            statement.execute(sql);
    		log.info("after exec sql=[{}], cost={}ms", sql, System.currentTimeMillis() - startTs);
            return true;
        } catch (Exception e) {
            log.error("syncfullDataForAdbTable failed", e);
            return false;
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                } finally {
                    statement = null;
                }
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                } finally {
                	connection = null;
                }
            }
        }
    }

    private Long processViewModelDAGTask(String tenantId, String appName, String empid, Long viewModelId, String dbName, String tableName, List<String> jobList, Long versionId, List<String> batchJobs, Settings setting, List<String> extDbNames, ViewModel dataModel, DatatubeInstance datatubeInst) {
    	boolean isDirectWriteObj = "metadata".equalsIgnoreCase(dataModel.getObject().getType()) && dsInfoService.isObjectDirectWriteByMdp(tenantId, dataModel.getObject().getRef());
    	
    	String blinkSqlTemplate = "DataTubeIncrSyncNode incrSync = new DataTubeIncrSyncNode(\"ViewModel_incrSync_%s\", dag);\r\n" + 
        "incrSync.setStreamJobs(\"%s\");\r\n";
    	
    	ViewModelInfoExample vmExample = new ViewModelInfoExample();
		vmExample.createCriteria().andTenantIdEqualTo(tenantId).andIdEqualTo(viewModelId).andIsDeletedEqualTo(0L);
		List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(vmExample);
		if (CollectionUtils.isEmpty(viewModelInfos)) {
			throw new QanatBizException("tenant check failed");
		}
		ViewModelInfo viewModelInfo = viewModelInfos.get(0);
        
        StringBuffer blinkPart = new StringBuffer();
        blinkPart.append(String.format(blinkSqlTemplate, tableName + "_v" + versionId, StringUtils.join(jobList, ",")));
        
    	DatasourceExample example = new DatasourceExample();
    	example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName).andTableNameEqualTo(tableName);
    	Datasource dstDsInfo = datasourceMapper.selectByExample(example).get(0);
    	
		DsFieldInfoExample dfiExample = new DsFieldInfoExample();
		dfiExample.createCriteria().andTenantIdEqualTo(tenantId).andDsNameEqualTo(dstDsInfo.getDsName()).andIsDeletedEqualTo(0L);
    	List<DsFieldInfo> dsFields = dsFieldInfoMapper.selectByExample(dfiExample);
    	Map<String, String> colNameTypeMap = new HashMap<>();
    	String pkColumn = "id";
    	for (DsFieldInfo dsField : dsFields) {
    		colNameTypeMap.put(dsField.getFieldName(), dsField.getFieldType());
    		if (dsField.getIsPk().intValue() == 1) {
    			pkColumn = dsField.getFieldName();
    		}
    	}

    	JSONObject srcDbMetaJson = dsInfoService.getDbMetaByName(dbName);
    	
    	//兼容逻辑，按新数据源命名规则找补数据源配置
    	DatasourceExample dsInfoExample = new DatasourceExample();
    	dsInfoExample.createCriteria().andIsDeletedEqualTo(0L)
								    	.andTenantIdEqualTo(tenantId)
								    	.andDsNameEqualTo(dsInfoService.getDsName(tenantId, appName, dbName, tableName));
    	List<Datasource> dsInfos = datasourceMapper.selectByExample(dsInfoExample);
    	if (CollectionUtils.isEmpty(dsInfos)) {
	    	DatasourceRequest dsInfoReq = new DatasourceRequest();
	    	dsInfoReq.setTenantId(tenantId);
	    	dsInfoReq.setDbName(dbName);
	    	dsInfoReq.setDsName(dsInfoService.getDsName(tenantId, appName, dbName, tableName));
	    	dsInfoReq.setDsType(srcDbMetaJson.getString("dbType"));
	    	dsInfoReq.setTableName(tableName);
	    	dsInfoReq.setOperateEmpid(empid);
	    	dsInfoReq.setPkField(pkColumn);
	    	dsInfoService.createDatasource(dsInfoReq);
	    	log.info("{} has been created", dsInfoReq.getDsName());
    	} else {
    		DatasourceRequest dsInfoReq = new DatasourceRequest();
            dsInfoReq.setTenantId(tenantId);
            dsInfoReq.setDsName(dsInfoService.getDsName(tenantId, appName, dbName, tableName));
            dsInfoReq.setOperateEmpid(empid);
    		dsInfoService.modifyDatasource(dsInfoReq);
    	}

        List<String> extDbSyncScript = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(extDbNames)) {
        	
        	Long predictSize = null;
        	if ("metadata".equalsIgnoreCase(dataModel.getObject().getType())) {
            	example = new DatasourceExample();
            	example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTableNameEqualTo(dataModel.getObject().getRef()).andDsTypeEqualTo("obj");
            	Datasource srcDsInfo = datasourceMapper.selectByExample(example).get(0);
            	predictSize = srcDsInfo.getSize() == null ? srcDsInfo.getPredictSize() : srcDsInfo.getSize();
        	} else if ("table".equalsIgnoreCase(dataModel.getObject().getType())) {
            	example = new DatasourceExample();
            	example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsNameEqualTo(dataModel.getObject().getRef());
            	Datasource srcDsInfo = datasourceMapper.selectByExample(example).get(0);
            	predictSize = srcDsInfo.getSize() == null ? srcDsInfo.getPredictSize() : srcDsInfo.getSize();
        	}
        	
        	int batchSize = 102400;
        	int parallelism = 10;
        	if (predictSize == null || predictSize > 500 * 10000) {
        		batchSize = 1000000;
        		parallelism = 20;
        	}
    		for (int i = 0; i < extDbNames.size(); i++) {
            	String dstDbName = extDbNames.get(i);
            	
            	JSONObject dstDbMetaJson = dsInfoService.getDbMetaByName(dstDbName);
            	
            	dsInfoExample = new DatasourceExample();
            	dsInfoExample.createCriteria().andIsDeletedEqualTo(0L)
        								    	.andTenantIdEqualTo(tenantId)
        								    	.andTableNameEqualTo(tableName)
        								    	.andDbNameEqualTo(dstDbName);
            	dsInfos = datasourceMapper.selectByExample(dsInfoExample);
            	if (CollectionUtils.isEmpty(dsInfos)) {
        	    	DatasourceRequest dsInfoReq = new DatasourceRequest();
        	    	dsInfoReq.setTenantId(tenantId);
        	    	dsInfoReq.setDbName(dstDbName);
        	    	dsInfoReq.setDsName(dsInfoService.getDsName(tenantId, appName, dstDbName, tableName));
        	    	dsInfoReq.setDsType(dstDbMetaJson.getString("dbType"));
        	    	dsInfoReq.setTableName(tableName);
        	    	dsInfoReq.setOperateEmpid(empid);
        	    	dsInfoReq.setPkField(pkColumn);
        	    	DataResult<Long> createDsInfoResult = dsInfoService.createDatasource(dsInfoReq);
        	    	log.info("createDsInfoResult={}", JSON.toJSONString(createDsInfoResult));
            	}
            	ViewModelDsRelation dsRel = new ViewModelDsRelation();
    			dsRel.setCreateEmpid(empid);
    			dsRel.setDsName(dsInfoService.getDsName(tenantId, appName, dstDbName, tableName));
    			dsRel.setGmtCreate(new Date());
    			dsRel.setGmtModified(new Date());
    			dsRel.setIsDeleted(0L);
    			dsRel.setModifyEmpid(empid);
    			dsRel.setRelationType("to_ds");
    			dsRel.setTenantId(tenantId);
    			dsRel.setViewModelName(viewModelInfo.getModelName());
    			try {
    				viewModelDsRelationMapper.insert(dsRel);
    			} catch(Exception e) {}
    		}
        	
//        	if (extDbNames.size() > 1) {
        	    String tmpTableName = "tmp_" + tableName;
        	    String bakTableName = "bak_" + tableName;
        		String jobName = odsHandler.generateMultiDbSinkBatchTask(tenantId, appName, dsInfoService.getDsName(tenantId, appName, dbName, tableName), extDbNames, tmpTableName, batchSize, parallelism, dataModel.getSettings().getMultiDbSyncFilter(), dataModel.getObject().getFields().stream().filter(e -> e.isPk()).collect(Collectors.toList()).get(0).getCode());
            	String script = "MultiDbSinkNode multiDbSinkNode = new MultiDbSinkNode(\"MultiDbSink_" + tableName + "_" + versionId + "\", dag);\r\n" + 
    					"multiDbSinkNode.setDatatubeInstId(" + datatubeInst.getId() + ");\r\n" +
    					"multiDbSinkNode.setSrcDbName(\"" + dbName + "\");\r\n" +
    					"multiDbSinkNode.setDstDbNames(\"" + StringUtils.join(extDbNames, ",") + "\");\r\n" +
    					"multiDbSinkNode.setTableName(\"" + tableName + "\");\r\n" +
    					"multiDbSinkNode.setTmpTableName(\"" + tmpTableName + "\");\r\n" +
    					"multiDbSinkNode.setBakTableName(\"" + bakTableName + "\");\r\n" +
    					"multiDbSinkNode.setJobName(\"" + jobName + "\");\r\n" +
            			"\r\n" +
            			"fullSync.setNext(multiDbSinkNode);\r\n" +
                		"multiDbSinkNode.setNext(incrSync);\r\n" +
            			"\r\n";
    			extDbSyncScript.add(script);
//        	} else {
//        		String tmpTableName = "tmp_" + tableName;
//            	String dstDbName = extDbNames.get(0);
//        		createTable(tenantId, dataModel, viewModelInfo.getObjectType(), dstDbName, tableName);
//        		createTable(tenantId, dataModel, viewModelInfo.getObjectType(), dstDbName, tmpTableName, true);
//        		
//        		String jobName = odsHandler.generateBatchTask(tenantId, appName, odsHandler.getDsName(tenantId, appName, dbName, tableName), dstDbName, tmpTableName, batchSize, parallelism, dataModel.getSettings().getMultiDbSyncFilter());
//            	String script = "BlinkBatchNode extDbSyncNode = new BlinkBatchNode(\"ExtDbSync_" + dstDbName + "_" + tableName + "_" + versionId + "\", dag);\r\n" + 
//    					"extDbSyncNode.setJobName(\"" + jobName + "\");\r\n" +
//            			"\r\n" +
//                		"fullSync.setNext(extDbSyncNode);\r\n" +
//            			"\r\n" +
//                		"Adb3MultiSqlNode renameNode = new Adb3MultiSqlNode(\"AdbSwitch_" + dstDbName + "_" + tableName + "_" + versionId + "\", dag);\r\n" + 
//                		"renameNode.setDbName(\"" + dstDbName + "\");\r\n" + 
//                		"renameNode.setSql(\"alter table " + tableName + " rename to bak_" + tableName + ";alter table tmp_" + tableName + " rename to " + tableName + ";alter table bak_" + tableName + " rename to tmp_" + tableName + "\");" +
//                		"\r\n" + 
//                		"extDbSyncNode.setNext(renameNode);\r\n" +
//        				"renameNode.setNext(incrSync);\r\n" +
//                		"\r\n";
//    			extDbSyncScript.add(script);
//        	}
        }
        
        String dagScript = "";
        if (isDirectWriteObj) {
    		String objDsName = dsInfoService.getDsNameByObjectCode(tenantId, dataModel.getObject().getRef());
    		dsInfoService.modifyDsInfoMdpSlot(tenantId, objDsName, getEtlDbName(tenantId));
    	} 
		dagScript =	String.format(VIEW_MODEL_DAG_SCRIPT
        , tableName + "_v" + versionId
        , ("daily_rebuild".equalsIgnoreCase(setting.getGuaranteePolicy()) && StringUtils.isNotBlank(setting.getTimeExpression()) && !isDirectWriteObj) ? ("dag.setTimeExpression(\"" + setting.getTimeExpression() + "\");") : ""
        , tableName + "_v" + versionId
        , viewModelId
        , StringUtils.join(batchJobs, ",")
        , blinkPart.toString()
        , (CollectionUtils.isNotEmpty(extDbSyncScript) ? StringUtils.join(extDbSyncScript, "\r\n") : "fullSync.setNext(incrSync);\r\n"));
        
        String taskName = "DAG_" + tableName + "_v" + versionId;
        Long taskId = taskService.isTaskExists(tenantId, taskName);
        if (taskId != null) {
        	taskService.updateTaskDag(taskId, empid, dagScript, false);
            log.info("[{}]update qanat viewModel task[{}] finished", viewModelId, taskId);
        } else {
	        TaskInfoRequest taskInfo = new TaskInfoRequest();
	        taskInfo.setDagScript(dagScript);
	        taskInfo.setName(taskName);
	        taskInfo.setOperateEmpid(empid);
	        taskInfo.setTaskDesc(taskName);
	        taskInfo.setPolicy(DagPolicy.ETL.toString());
	        taskInfo.setTenantId(tenantId);
	        taskInfo.setAppName(appName);
	        taskId = taskService.createDAGTask(taskInfo);
	        log.info("[{}]create qanat viewModel task[{}] finished", viewModelId,  taskId);
        }
        return taskId;
    }
    
    public void rebuildMultiDbSyncJob(Long datatubeInstId) {
    	DatatubeInstance datatube = datatubeInstanceMapper.selectByPrimaryKey(datatubeInstId);
        if (datatube == null) {
        	log.error("datatube:{} get instance failed", datatubeInstId);
        	throw new QanatBizException("datatube:" + datatubeInstId + " get instance failed");
        }
        
    	TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(datatube.getTenantId());
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + datatube.getTenantId() + " is not configured");
    	}
    	TenantInfo tenantInfo = tenantList.get(0);
    	
		ViewModelInfo viewModelInfo = viewModelInfoMapper.selectByPrimaryKey(datatube.getProviderId());
        if (viewModelInfo == null) {
        	log.error("datatube:{} get viewmodel failed", datatubeInstId);
        	throw new QanatBizException("datatube:" + datatubeInstId + " get viewmodel failed");
        }
		ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());
        if (modelVersion == null) {
        	log.error("datatube:{} get viewmodelVersion failed", datatubeInstId);
        	throw new QanatBizException("datatube:" + datatubeInstId + " get viewmodelVersion failed");
        }

        ViewModel originModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
        if (originModel == null) {
        	log.error("datatube:{} get yaml failed", datatubeInstId);
        	throw new QanatBizException("datatube:" + datatubeInstId + " get yaml failed");
        }
    	ViewModel sysModel = null;
    	if (originModel.isDynamic()) {
    		sysModel = viewModelOptimizer.getOptimizedViewModel(datatube.getTenantId(), modelVersion.getUserYaml());
    	} else {
    		sysModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
    	}
    	ViewModel dataModel = new ViewModel();
    	BeanUtils.copyProperties(sysModel, dataModel);
    	
        String tableName = dataModel.getCode();
    	
    	String etlDbName = getEtlDbName(tenantInfo);
    	List<String> extDbNames = getExtDbNames(tenantInfo, datatube);
        if (CollectionUtils.isNotEmpty(extDbNames)) {
        	Long predictSize = null;
        	if ("metadata".equalsIgnoreCase(dataModel.getObject().getType())) {
        		DatasourceExample example = new DatasourceExample();
            	example.createCriteria().andTenantIdEqualTo(datatube.getTenantId()).andIsDeletedEqualTo(0L).andTableNameEqualTo(dataModel.getObject().getRef()).andDsTypeEqualTo("obj");
            	Datasource srcDsInfo = datasourceMapper.selectByExample(example).get(0);
            	predictSize = srcDsInfo.getSize() == null ? srcDsInfo.getPredictSize() : srcDsInfo.getSize();
        	} else if ("table".equalsIgnoreCase(dataModel.getObject().getType())) {
        		DatasourceExample example = new DatasourceExample();
            	example.createCriteria().andTenantIdEqualTo(datatube.getTenantId()).andIsDeletedEqualTo(0L).andDsNameEqualTo(dataModel.getObject().getRef());
            	Datasource srcDsInfo = datasourceMapper.selectByExample(example).get(0);
            	predictSize = srcDsInfo.getSize() == null ? srcDsInfo.getPredictSize() : srcDsInfo.getSize();
        	}
        	
        	int batchSize = 102400;
        	int parallelism = 10;
        	if (predictSize == null || predictSize > 500 * 10000) {
        		batchSize = 1000000;
        		parallelism = 20;
        	}
        	
        	boolean isNewMultiDbSyncMode = false;
        	ViewModelTaskRelationExample modelTaskRelExample = new ViewModelTaskRelationExample();
        	modelTaskRelExample.createCriteria().andTenantIdEqualTo(datatube.getTenantId()).andIsDeletedEqualTo(0L).andModelVersionIdEqualTo(viewModelInfo.getVersionId()).andRelationTypeEqualTo("main");
        	List<ViewModelTaskRelation> modelTaskRels = viewModelTaskRelationMapper.selectByExample(modelTaskRelExample);
        	if (CollectionUtils.isNotEmpty(modelTaskRels)) {
            	Long taskId = modelTaskRels.get(0).getTaskId();
            	TaskInfoWithBLOBs taskInfo = taskInfoMapper.selectByPrimaryKey(taskId);
            	if (StringUtils.isNotBlank(taskInfo.getDag())) {
    	        	JSONObject dagJson = JSON.parseObject(taskInfo.getDag());
    	        	if (dagJson.getJSONArray("nodeList") != null && dagJson.getJSONArray("nodeList").size() > 0) {
    		        	for (int i = 0; i < dagJson.getJSONArray("nodeList").size(); i++) {
    		        		JSONObject nodeJson = dagJson.getJSONArray("nodeList").getJSONObject(i);
    		        		if ("com.aliyun.wormhole.qanat.job.QanatMultiDbSinkJobProcessor".equalsIgnoreCase(nodeJson.getString("action"))) {
    		        			isNewMultiDbSyncMode = true;
    		        			break;
    		        		}
    		        	}
    	        	}
            	}
        	}
        	log.info("datatube:{} isNewMultiDbSyncMode:{}", datatubeInstId, isNewMultiDbSyncMode);

    	    String tmpTableName = "tmp_" + tableName;
        	if (isNewMultiDbSyncMode) {
        		odsHandler.generateMultiDbSinkBatchTask(datatube.getTenantId(), datatube.getAppName(), dsInfoService.getDsName(datatube.getTenantId(), datatube.getAppName(), etlDbName, tableName), extDbNames, tmpTableName, batchSize, parallelism, dataModel.getSettings().getMultiDbSyncFilter(), dataModel.getObject().getFields().stream().filter(e -> e.isPk()).collect(Collectors.toList()).get(0).getCode());
        	} else {
        		odsHandler.generateBatchTask(datatube.getTenantId(), datatube.getAppName(), dsInfoService.getDsName(datatube.getTenantId(), datatube.getAppName(), etlDbName, tableName), extDbNames.get(0), tmpTableName, batchSize, parallelism, dataModel.getSettings().getMultiDbSyncFilter());
        	}
        }
    }
    
    public static String VIEW_MODEL_DAG_SCRIPT = "Dag dag = new Dag(\"DAG_%s\");\r\n" + 
            "%s\r\n" + 
            "DataTubeFullSyncNode fullSync = new DataTubeFullSyncNode(\"ViewModel_fullSync_%s\", dag);\r\n" + 
            "fullSync.setModelId(%s);\r\n" + 
            "fullSync.setBatchJobs(\"%s\");\r\n" + 
            "fullSync.setDataBaseline(true);\r\n" + 
            "%s\r\n" +
            "%s\r\n" +
            "return dag;";
    
    public static String VIEW_MODEL_DAG_SCRIPT_FOR_DIRECTWRITE = "Dag dag = new Dag(\"DAG_%s\");\r\n" + 
            "%s\r\n" + 
            "%s\r\n" +
            "%s\r\n" +
            "return dag;";
    
    public static void main(String [] args) {
    	String yaml = "code: dwd_devata_cid_ext\r\n" + 
    			"name: 鲲鹏客户扩展表\r\n" + 
    			"dynamic: true\r\n" + 
    			"object:\r\n" + 
    			"    code: ext\r\n" + 
    			"    type: metadata\r\n" + 
    			"    ref: DEVATA__CUSTOMER\r\n" + 
    			"settings: {batchCheckDateField: gmt_modified, batchCheckDays: 1, batchCheckPolicy: fullcolumns,\r\n" + 
    			"  check: false, correctPolicy: offhand, distributeKey: null, fullLink: false, fullSyncBatchSize: 10240,\r\n" + 
    			"  fullSyncParallelism: 10, guaranteePolicy: daily_rebuild, incrCheckDelayMs: 10000,\r\n" + 
    			"  lookupOptimize: true, rdsScanBatchSize: 100000, sla: false, sqlOptimize: false,\r\n" + 
    			"  timeExpression: null, multiDbSyncFilter:'cid<1939330525'}";
    	System.out.println(YamlUtil.getViewModel(yaml));
    }
}