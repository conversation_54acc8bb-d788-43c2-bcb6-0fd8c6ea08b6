package com.aliyun.wormhole.qanat.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.OdpsSqlNode;
import com.aliyun.wormhole.qanat.dal.domain.*;
import com.aliyun.wormhole.qanat.dal.mapper.*;
import com.aliyun.wormhole.qanat.service.dag.DagService;
import com.aliyun.wormhole.qanat.service.odps.OdpsClient;
import com.taobao.unifiedsession.core.commons.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * OdpsSql
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatOdpsSqlJobProcessor extends AbstractQanatNodeJobProcessor<OdpsSqlNode> {
    
    @Resource
    private TaskScriptMapper taskScriptMapper;
	
	@Resource
	private DbInfoMapper dbInfoMapper;
    
    @Resource
    private DagService dagService;

    @Override
    void doProcess(Map<String, Object> instParamsMap, OdpsSqlNode odpsNode) {
        try {
            String tenantId = String.valueOf(instParamsMap.get("tenantId"));

            DbInfoExample dbExample = new DbInfoExample();
            dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(odpsNode.getDbName()).andTenantIdEqualTo(tenantId);
            List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
            if (CollectionUtils.isEmpty(dbs)) {
                throw new QanatBizException("db:" + odpsNode.getDbName() + " is not found");
            }
            DbInfo dbInfo = dbs.get(0);
            JSONObject metaJson = JSON.parseObject(dbInfo.getMeta());
    	    OdpsClient client = new OdpsClient(metaJson.getString("endpoint"), metaJson.getString("accessId"), metaJson.getString("accessKey"),
    	    		metaJson.getString("project"), metaJson.getString("mcUrl"), metaJson.getString("mcToken"));

            String sql = odpsNode.getOdpsSql();
            if (odpsNode.getSqlId() != null) {
                TaskScriptExample dsExample = new TaskScriptExample();
                dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(odpsNode.getDbName()).andTenantIdEqualTo(tenantId);
                List<TaskScript> tsList = taskScriptMapper.selectByExampleWithBLOBs(dsExample);
                if (CollectionUtils.isEmpty(tsList)) {
                    throw new QanatBizException("db:" + odpsNode.getDbName() + " is not found");
                }
                TaskScript taskScript = tsList.get(0);
                sql = taskScript.getScript();
            }

            log.info("sql={}", sql);
            long startTs = System.currentTimeMillis();

            String dsFormat = "yyyyMMdd";
            Date today = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat(dsFormat);
            String bizDate = sdf.format(DateUtils.addDay(today, -1));

            String logview = client.queryOdpsSql(sql.replaceAll("#bizdate#", bizDate));
            log.info("sql exec finished using {} ms", System.currentTimeMillis()-startTs);
            log.info("Qanat Odps logview:{}", logview);
        } catch (Exception e) {
//            log.error("odpsSql任务调度异常", e);
            throw new QanatBizException("odpsSql任务调度异常");
        }
    }
}