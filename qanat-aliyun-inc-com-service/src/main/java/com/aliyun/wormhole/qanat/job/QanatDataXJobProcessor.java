package com.aliyun.wormhole.qanat.job;

import java.math.BigDecimal;
import java.net.InetAddress;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.DagInstStatus;
import com.aliyun.wormhole.qanat.api.dag.DataSourceType;
import com.aliyun.wormhole.qanat.api.dag.DataXNode;
import com.aliyun.wormhole.qanat.api.datax.DataXHoloWriter;
import com.aliyun.wormhole.qanat.api.datax.DataXMySQLReader;
import com.aliyun.wormhole.qanat.api.datax.DataXMySQLWriter;
import com.aliyun.wormhole.qanat.api.datax.DataXOdpsReader;
import com.aliyun.wormhole.qanat.api.datax.DataXOdpsWriter;
import com.aliyun.wormhole.qanat.api.datax.DataXReader;
import com.aliyun.wormhole.qanat.api.datax.DataXSetting;
import com.aliyun.wormhole.qanat.api.datax.DataXTddlReader;
import com.aliyun.wormhole.qanat.api.datax.DataXWriter;
import com.aliyun.wormhole.qanat.api.dto.ColumnInfo;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.DatasourceRequest;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.SyncDataService;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelation;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ResourceExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfo;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.mapper.AppResourceRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsFieldInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ResourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import com.aliyun.wormhole.qanat.service.dag.DagService;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.service.odps.OdpsClient;
import com.aliyun.wormhole.qanat.service.util.HoloUtils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * DataX任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatDataXJobProcessor extends JavaProcessor {
    
    @Resource
    private SyncDataService syncDataService;
    
    @Resource
    private TaskInfoMapper taskInfoMapper;
    
    @Resource
    private DatasourceMapper datasourceMapper;
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;
	
	@Resource
	private AppResourceRelationMapper appResourceRelationMapper;
	
	@Resource
	private ResourceMapper resourceMapper;
    
    @Resource
    private DagService dagService;

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private DsFieldInfoMapper dsFieldInfoMapper;
    
    @Resource
    private DatasourceService dsInfoService;

    // 新增OXS地域感知配置
    @Value("${region.type:}")
    private String regionType;

    @Value("${environment.type:}")
    private String environmentType;

    @Value("${qanat.db.oxs.enabled:false}")
    private boolean oxsEnabled;

    /**
     * 根据地域获取数据库连接URL
     * @param dbMetaJson 数据库元数据JSON
     * @return 适合当前地域的数据库连接URL
     */
    private String getDbConnectionUrl(JSONObject dbMetaJson) {
        if (oxsEnabled && isOxsRegion() && dbMetaJson.containsKey("oxsJdbcUrl")) {
            String oxsJdbcUrl = dbMetaJson.getString("oxsJdbcUrl");
            if (StringUtils.isNotBlank(oxsJdbcUrl)) {
                log.info("DataX Job using OXS JDBC URL for region: {} env: {}", regionType, environmentType);
                return oxsJdbcUrl;
            }
        }
        return dbMetaJson.getString("jdbcUrl");
    }

    /**
     * 判断当前是否为OXS区域
     * @return true如果是OXS区域
     */
    private boolean isOxsRegion() {
        return "singapore".equalsIgnoreCase(regionType) && 
               StringUtils.containsIgnoreCase(environmentType, "oxs");
    }

    @Override
    public ProcessResult process(JobContext context) {
        DataResult<Map<String, String>> result = new DataResult<>();
        String taskName = context.getTaskName();
        log.info("Qanat DataX Job[{}] start.", taskName);
        try {
            //实例参数
            JSONObject instParams = null;
            if (StringUtils.isNotBlank(context.getInstanceParameters())) {
                instParams = JSON.parseObject(context.getInstanceParameters());
            }
            if (instParams == null) {
                instParams = JSON.parseObject(context.getJobParameters());
            }
            Long taskId = instParams.getLong("taskId");
            TaskInfo taskInfo = taskInfoMapper.selectByPrimaryKey(taskId);
            
            JSONObject nodeJson = (JSONObject)instParams.get("node");
            DataXNode dataxNode = (DataXNode)dagService.getNodeByJSONObject(nodeJson);
            String operator = instParams.getString("operator");
            Long subTaskInstId = instParams.getLong("subTaskInstId");
            Long taskInstId = instParams.getLong("taskInstId");

            String tenantId = instParams.getString("tenantId");
            String appName = instParams.getString("appName");

            TaskInstance taskInstUpd = new TaskInstance();
            taskInstUpd.setId(subTaskInstId);
            taskInstUpd.setGmtModified(new Date());
            taskInstUpd.setModifyEmpid(operator);
            taskInstUpd.setExternalInstId(context.getJobInstanceId() + "");//SchedulerX任务实例id
            taskInstUpd.setStatus(DagInstStatus.EXECUTING.getCode().byteValue());
            taskInstUpd.setHostAddr(InetAddress.getLocalHost().getHostAddress());
            taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
            
            DataXSetting setting = new DataXSetting(dataxNode.getParallism() == null ? 10 : dataxNode.getParallism());
            DataXReader reader = null;
            DataXWriter writer = null;
            String srcDsName = dataxNode.getSrcDsName();
            String dstDsName = dataxNode.getDstDsName();
            String querySql = dataxNode.getQuerySql();
            String where = dataxNode.getWhere();
            DatasourceExample example = new DatasourceExample();
            example.createCriteria().andDsNameEqualTo(srcDsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(taskInfo.getTenantId());
            Datasource srcDs = datasourceMapper.selectByExampleWithBLOBs(example).get(0);
            String srcDsMeta = srcDs.getMeta();
            JSONObject srcDsMetaJson = JSON.parseObject(srcDsMeta);

            if (StringUtils.isBlank(srcDs.getDbName())) {
                throw new QanatBizException("dbName is not config");
            }
            DbInfoExample dbExample = new DbInfoExample();
            dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(srcDs.getDbName()).andTenantIdEqualTo(taskInfo.getTenantId());
            List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
            if (CollectionUtils.isEmpty(dbs)) {
                throw new QanatBizException("db not found");
            }
            DbInfo dbInfo = dbs.get(0);
            JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
            if (srcDs.getDsType().equalsIgnoreCase(DataSourceType.MYSQL.toString())
                || srcDs.getDsType().equalsIgnoreCase(DataSourceType.ADB3.toString())
                || srcDs.getDsType().equalsIgnoreCase(DataSourceType.POSTGRESQL.toString())) {
                
                // 改造点：使用地域感知的JDBC URL获取逻辑
                String jdbcUrl = getDbConnectionUrl(dbMetaJson);
                srcDsMetaJson.put("jdbcUrl", jdbcUrl);
                
                srcDsMetaJson.put("username", dbMetaJson.getString("username"));
                srcDsMetaJson.put("password", dbMetaJson.getString("password"));
                srcDsMetaJson.put("version", dbMetaJson.getString("version"));
            } else if (srcDs.getDsType().equalsIgnoreCase(DataSourceType.ODPS.toString())) {
                srcDsMetaJson.put("project", dbMetaJson.getString("project"));
                srcDsMetaJson.put("accessId", dbMetaJson.getString("accessId"));
                srcDsMetaJson.put("accessKey", dbMetaJson.getString("accessKey"));
                srcDsMetaJson.put("odpsServer", dbMetaJson.getString("odpsServer"));
            } else if (srcDs.getDsType().equalsIgnoreCase(DataSourceType.TDDL.toString())) {
                srcDsMetaJson.put("appName", dbMetaJson.getString("appName"));
            }
            //刷新源表元数据
            DatasourceRequest dsInfoModReq = new DatasourceRequest();
            dsInfoModReq.setTenantId(tenantId);
            dsInfoModReq.setDsName(srcDsName);
            dsInfoModReq.setOperateEmpid("schedulerx2");
            dsInfoService.modifyDatasource(dsInfoModReq);
            
            example = new DatasourceExample();
            example.createCriteria().andDsNameEqualTo(dstDsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(taskInfo.getTenantId());
            Datasource dstDs = datasourceMapper.selectByExampleWithBLOBs(example).get(0);
            String dstDsMeta = dstDs.getMeta();
            JSONObject dstDsMetaJson = JSON.parseObject(dstDsMeta);

            if (StringUtils.isBlank(dstDs.getDbName())) {
                throw new QanatBizException("dbName is not config");
            }
            dbExample = new DbInfoExample();
            dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dstDs.getDbName()).andTenantIdEqualTo(taskInfo.getTenantId());
            dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
            if (CollectionUtils.isEmpty(dbs)) {
                throw new QanatBizException("db not found");
            }
            dbInfo = dbs.get(0);
            dbMetaJson = JSON.parseObject(dbInfo.getMeta());
            if (dstDs.getDsType().equalsIgnoreCase(DataSourceType.POSTGRESQL.toString())
                || dstDs.getDsType().equalsIgnoreCase(DataSourceType.ADB3.toString())) {
                
                // 改造点：目标数据源也使用地域感知的JDBC URL获取逻辑
                String dstJdbcUrl = getDbConnectionUrl(dbMetaJson);
                dstDsMetaJson.put("jdbcUrl", dstJdbcUrl);
                
                dstDsMetaJson.put("username", dbMetaJson.getString("username"));
                dstDsMetaJson.put("password", dbMetaJson.getString("password"));
            } else if (dstDs.getDsType().equalsIgnoreCase(DataSourceType.ODPS.toString())) {
                dstDsMetaJson.put("project", dbMetaJson.getString("project"));
                dstDsMetaJson.put("accessId", dbMetaJson.getString("accessId"));
                dstDsMetaJson.put("accessKey", dbMetaJson.getString("accessKey"));
                dstDsMetaJson.put("odpsServer", dbMetaJson.getString("odpsServer"));
            } else if (dstDs.getDsType().equalsIgnoreCase(DataSourceType.HOLOGRES.toString())) {
                dstDsMetaJson.put("dbName", dbMetaJson.getString("dbName"));
                dstDsMetaJson.put("username", dbMetaJson.getString("username"));
                dstDsMetaJson.put("password", dbMetaJson.getString("password"));
                dstDsMetaJson.put("endpoint", dbMetaJson.getString("endpoint"));
                
                // 改造点：Hologres数据源也使用地域感知的JDBC URL获取逻辑
                String holoJdbcUrl = getDbConnectionUrl(dbMetaJson);
                dstDsMetaJson.put("jdbcUrl", holoJdbcUrl);
            }
            
            DsFieldInfoExample fieldExample = new DsFieldInfoExample();
            fieldExample.createCriteria().andDsNameEqualTo(srcDsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(taskInfo.getTenantId());
            List<DsFieldInfo> dsFields = dsFieldInfoMapper.selectByExample(fieldExample);
            if (querySql == null) {
                querySql = dataxNode.getQuerySql();
            }
            
            List<String> columns = new ArrayList<>();
            List<String> pkList = new ArrayList<>();
            Map<String, String> colNameTypeMap = new HashMap<>();
            Map<String, String> colNameDescMap = new HashMap<>();
            List<String> dstColumns = new ArrayList<>();

            if (CollectionUtils.isEmpty(dsFields)) {
            	throw new QanatBizException("dsFields is empty");
            }
        	columns = dsFields.stream().map(item -> item.getFieldName().toLowerCase()).collect(Collectors.toList());
        	pkList = dsFields.stream().filter(item -> item.getIsPk() == 1).map(DsFieldInfo::getFieldName).collect(Collectors.toList());
        	for (DsFieldInfo field : dsFields) {
        		colNameTypeMap.put(field.getFieldName(), field.getFieldType());
        		colNameDescMap.put(field.getFieldName(), field.getFieldDesc());
        	}
        	String dstTableComment = dstDs.getDsDesc();
            
        	SimpleDateFormat tsSdf = new SimpleDateFormat("yyMMddHHmm");
            String ts = tsSdf.format(new Date());
            String srcTableName = srcDs.getTableName();
			if (srcDs.getDsType().equalsIgnoreCase(DataSourceType.MYSQL.toString())
					|| srcDs.getDsType().equalsIgnoreCase(DataSourceType.ADB3.toString())) {
                DataXMySQLReader mysqlReader = new DataXMySQLReader();
                mysqlReader.setColumns(columns);
                mysqlReader.setJdbcUrl(srcDsMetaJson.getString("jdbcUrl"));
                mysqlReader.setPassword(srcDsMetaJson.getString("password"));
                mysqlReader.setQuerySql(querySql);
                mysqlReader.setSplitPk(pkList.get(0));
                mysqlReader.setTable(srcTableName);
                mysqlReader.setUsername(srcDsMetaJson.getString("username"));
                mysqlReader.setWhere(where);
                mysqlReader.setVersion(srcDsMetaJson.getString("version"));
                reader = mysqlReader;
            } else if (srcDs.getDsType().equalsIgnoreCase(DataSourceType.TDDL.toString())) {
                DataXTddlReader tddlReader = new DataXTddlReader();
                tddlReader.setColumns(columns);
                tddlReader.setAppName(srcDsMetaJson.getString("appName"));
                tddlReader.setQuerySql(querySql);
                tddlReader.setTable(srcTableName);
                tddlReader.setWhere(where);
                reader = tddlReader;
            } else if (srcDs.getDsType().equalsIgnoreCase(DataSourceType.ODPS.toString())) {
                DataXOdpsReader odpsReader = new DataXOdpsReader();
                odpsReader.setAccessId(srcDsMetaJson.getString("accessId"));
                odpsReader.setAccessKey(srcDsMetaJson.getString("accessKey"));
                odpsReader.setAccountProvider("aliyun");
                odpsReader.setOdpsServer(srcDsMetaJson.getString("odpsServer"));
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                String bizDate = sdf.format(DateUtils.addDays(new Date(), -1));
                odpsReader.setPartition(srcDsMetaJson.getString("partition").replace("#bizDate#", bizDate));
                odpsReader.setPackageAuthorizedProject(srcDsMetaJson.getString("project"));
                odpsReader.setProject(srcDsMetaJson.getString("project"));
        	    OdpsClient client = new OdpsClient(srcDsMetaJson.getString("odpsServer"), srcDsMetaJson.getString("accessId"), srcDsMetaJson.getString("accessKey"),
        	    		srcDsMetaJson.getString("project"), null, null);
        	    Map<String, String> columnsMap = client.getTableColumnType(srcTableName);
        	    columns = columnsMap.keySet().stream().collect(Collectors.toList());
                odpsReader.setColumns(columns);

                odpsReader.setQuerySql(querySql);
                odpsReader.setTable(srcTableName);
                odpsReader.setWhere(where);
                reader = odpsReader;
            }

            String tmpDstTableName = null;
            String dstTableName = dstDs.getTableName();
            if (dstDs.getDsType().equalsIgnoreCase(DataSourceType.ADB3.toString())) {
                tmpDstTableName = "tmp_" + dstTableName + "_" + ts;
                DataXMySQLWriter adbWriter = new DataXMySQLWriter();
                adbWriter.setColumns(CollectionUtils.isNotEmpty(dstColumns)?dstColumns:columns);
                adbWriter.setJdbcUrl(dstDsMetaJson.getString("jdbcUrl"));
                adbWriter.setPassword(dstDsMetaJson.getString("password"));
                adbWriter.setTable(tmpDstTableName);
                adbWriter.setUsername(dstDsMetaJson.getString("username"));
                adbWriter.setWriteMode("insert");
                if (null != dataxNode.getBatchSize()) {
                    adbWriter.setBatchSize(dataxNode.getBatchSize());
                }
                if (StringUtils.isNotBlank(dataxNode.getPreSql())) {
                    adbWriter.setPreSql(dataxNode.getPreSql());
                } else {
                    adbWriter.setPreSql("TRUNCATE TABLE " + tmpDstTableName);
                }
                writer = adbWriter;
                //try to create table in adb
                createAdbTable(dstTableName, colNameTypeMap, CollectionUtils.isNotEmpty(pkList) ? pkList.get(0) : null, dstDsMetaJson.getString("jdbcUrl"), 
                    dstDsMetaJson.getString("username"), 
                    dstDsMetaJson.getString("password"),
                    dstDsMetaJson.getBoolean("broadcast"),
                    dstDsMetaJson.getString("distributeKey"));
                createAdbTable(tmpDstTableName, colNameTypeMap, CollectionUtils.isNotEmpty(pkList) ? pkList.get(0) : null, dstDsMetaJson.getString("jdbcUrl"), 
                    dstDsMetaJson.getString("username"), 
                    dstDsMetaJson.getString("password"),
                    dstDsMetaJson.getBoolean("broadcast"),
                    dstDsMetaJson.getString("distributeKey"));
                
                //更新ds
                DatasourceRequest updDsReq = new DatasourceRequest();
                updDsReq.setTenantId(tenantId);
                updDsReq.setDsName(dstDs.getDsName());
                dsInfoService.modifyDatasource(updDsReq);
            } else if (dstDs.getDsType().equalsIgnoreCase(DataSourceType.HOLOGRES.toString())) {
            	if (dstTableName.split("\\.").length == 2) {
            		tmpDstTableName = dstTableName.split("\\.")[0] + ".tmp_" + dstTableName.split("\\.")[1] + "_" + ts;
            	} else {
                    tmpDstTableName = "tmp_" + dstTableName + "_" + ts;
            	}
            	DataXHoloWriter holoWriter = new DataXHoloWriter();
                holoWriter.setColumns(CollectionUtils.isNotEmpty(dstColumns)?dstColumns:columns);
                holoWriter.setEndpoint(dstDsMetaJson.getString("endpoint"));
                holoWriter.setAccessKey(dstDsMetaJson.getString("password"));
                holoWriter.setTable(tmpDstTableName);
                holoWriter.setAccessId(dstDsMetaJson.getString("username"));
                holoWriter.setWriteMode("sdk");
                holoWriter.setDatabase(dstDsMetaJson.getString("dbName"));
                if (null != dataxNode.getBatchSize()) {
                    holoWriter.setBatchSize(dataxNode.getBatchSize());
                }
                if (StringUtils.isNotBlank(dataxNode.getPreSql())) {
                    holoWriter.setPreSql(dataxNode.getPreSql());
                } else {
                    holoWriter.setPreSql("TRUNCATE TABLE " + tmpDstTableName);
                }
                writer = holoWriter;
                //try to create table in adb
                createHoloTable(dstTableName, colNameTypeMap, CollectionUtils.isNotEmpty(pkList) ? pkList.get(0) : null, dstDsMetaJson.getString("jdbcUrl"), 
                    dstDsMetaJson.getString("username"), 
                    dstDsMetaJson.getString("password"),
                    dstDsMetaJson.getString("distributeKey"),
                    dstDsMetaJson.getString("clusteringKey"));
                createHoloTable(tmpDstTableName, colNameTypeMap, CollectionUtils.isNotEmpty(pkList) ? pkList.get(0) : null, dstDsMetaJson.getString("jdbcUrl"), 
                    dstDsMetaJson.getString("username"), 
                    dstDsMetaJson.getString("password"),
                    dstDsMetaJson.getString("distributeKey"),
                    dstDsMetaJson.getString("clusteringKey"));
                
                //更新ds
                DatasourceRequest updDsReq = new DatasourceRequest();
                updDsReq.setTenantId(tenantId);
                updDsReq.setDsName(dstDs.getDsName());
                dsInfoService.modifyDatasource(updDsReq);
            } else if (dstDs.getDsType().equalsIgnoreCase(DataSourceType.ODPS.toString())) {
                List<ColumnInfo> colInfoList = new ArrayList<>();
                for (String fieldName : colNameTypeMap.keySet()) {
                    ColumnInfo colInfo = new ColumnInfo();
                    colInfo.setColumnName(fieldName);
                    colInfo.setColumnType(getOdpsType(colNameTypeMap.get(fieldName)));
                    colInfo.setColumnComment(colNameDescMap.get(fieldName));
                    colInfoList.add(colInfo);
                }
                //加分区字段
                ColumnInfo dsColInfo = new ColumnInfo();
                dsColInfo.setColumnName("ds");
                dsColInfo.setColumnType("string");
                dsColInfo.setColumnComment("yyyymmdd");
                colInfoList.add(dsColInfo);
                
                dstTableName = "ods_" + dstTableName + "_df";
                createOdpsTable(tenantId, appName, dstTableName, dstTableComment, colInfoList);
                
                DataXOdpsWriter odpsWriter = new DataXOdpsWriter();
                odpsWriter.setColumns(columns);
                odpsWriter.setAccessId(dstDsMetaJson.getString("accessId"));
                odpsWriter.setAccessKey(dstDsMetaJson.getString("accessKey"));
                odpsWriter.setAccountType("aliyun");
                odpsWriter.setOdpsServer(dstDsMetaJson.getString("odpsServer"));
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                String bizDate = sdf.format(new Date());
                odpsWriter.setPartition(dstDsMetaJson.getString("partition").replace("#bizDate#", bizDate));
                odpsWriter.setTable(dstTableName);
                odpsWriter.setProject(dstDsMetaJson.getString("project"));
                odpsWriter.setTruncate(true);
                writer = odpsWriter;
            }
            //调用数据同步
            result = syncDataService.syncData(srcDs.getDsName(), setting, reader, writer);
            if (result.getData() != null && result.getSuccess()) {
                if (StringUtils.isNotBlank(tmpDstTableName)) {
                    Connection connection = null;
                    Statement statement = null;
                    try {
                        RdsConnectionParam param = new RdsConnectionParam();
                        param.setUrl(dstDsMetaJson.getString("jdbcUrl"));
                        param.setPassword(dstDsMetaJson.getString("password"));
                        param.setUserName(dstDsMetaJson.getString("username"));
                        connection = dsHandler.connectToTable(param);
                        statement = connection.createStatement();

                    	boolean isSyncSuccess = true;
                    	if (StringUtils.isNotBlank(result.getData().get("datax_record_cnt")) && StringUtils.isNumeric(result.getData().get("datax_record_cnt"))) {
                    		int newCnt = Integer.valueOf(result.getData().get("datax_record_cnt"));
                    		int oldCnt = countTable(connection, dstTableName);
                    		log.info("table:{} newRecordCnt:{} oldRecordCnt:{}", dstTableName, newCnt, oldCnt);
                    		if (oldCnt > 0 && newCnt < oldCnt && BigDecimal.valueOf(newCnt).divide(BigDecimal.valueOf(oldCnt), 2, BigDecimal.ROUND_HALF_UP).compareTo(new BigDecimal("0.5")) < 0) {
                    			log.error("table:{} newRecordCnt:{} is less then 50% of oldRecordCnt:{}, isSyncSuccess:{}", dstTableName, newCnt, oldCnt, false);
                    			isSyncSuccess = false;
                    		}
                    	}
                		log.info("table:{} isSyncSuccess:{}", dstTableName, isSyncSuccess);
                    	if (isSyncSuccess) {
                    		int retries = 0;
                    		boolean isBakSuccess = false;
                    		while (retries < 6) {
		                        try {
		                            statement.execute("ALTER TABLE " + dstTableName + " RENAME TO bak_" + dstTableName + "_" + ts);
		                            isBakSuccess = true;
		                            break;
		                        } catch(Exception e) {
		                        	log.error("retry:{} alter table {} reanme to bak_{}_{} faield:{}", retries, dstTableName, dstTableName, ts, e.getMessage(), e);
		                        	retries++;
		                        	Thread.sleep(1000 * retries);//wait to retry
		                        }
                    		}
                    		boolean isSwichSuccess = false;
                    		if (isBakSuccess) {
                    			retries = 0;
	                    		while (retries < 6) {
			                        try {
			                            statement.execute("ALTER TABLE " + tmpDstTableName + " RENAME TO " + dstTableName);
			                            isSwichSuccess = true;
			                            break;
			                        } catch(Exception e) {
			                        	log.error("retry:{} alter table {} reanme to {} faield:{}", retries, tmpDstTableName, dstTableName, e.getMessage(), e);
			                        	retries++;
			                        	Thread.sleep(1000 * retries);//wait to retry
			                        }
	                    		}
	                    		log.info("table:{} isSwichSuccess:{}", dstTableName, isSwichSuccess);
	                    		if (!isSwichSuccess) {
	                    			throw new QanatBizException(dstTableName + " switch online failed");
	                    		}
                    		}
                    	}
                    } catch (Exception e) {
                        log.error("DataX任务调度异常:{}", e.getMessage(), e);
                        return new ProcessResult(false, e.getMessage());
                    } finally {
                        if (statement != null) {
                            try {
                                statement.close();
                            } catch (Exception e) {
                            }
                            statement = null;
                        }
                        if (connection != null) {
                            try {
                                connection.close();
                            } catch (Exception e) {
                            }
                            connection = null;
                        }
                    }
                }
                
                log.info("syncData finished");
                taskInstUpd = new TaskInstance();
                taskInstUpd.setId(subTaskInstId);
                taskInstUpd.setGmtModified(new Date());
                taskInstUpd.setModifyEmpid(operator);
                taskInstUpd.setStatus(DagInstStatus.SUCCESS.getCode().byteValue());
                taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
                
                //全局任务参数更新到主任务实例的参数中
                if (dataxNode.isDataBaseline()) {
                    TaskInstance taskInst = taskInstanceMapper.selectByPrimaryKey(taskInstId);
                    JSONObject execParam = JSON.parseObject(taskInst.getExecParam());
                    execParam.putAll(result.getData());
                    execParam.put("incr_sync_start_time", result.getData().get("datax_start_time"));
                    taskInstUpd = new TaskInstance();
                    taskInstUpd.setId(taskInst.getId());
                    taskInstUpd.setExecParam(JSON.toJSONString(execParam));
                    taskInstUpd.setGmtModified(new Date());
                    taskInstUpd.setEndTime(new Date());
                    taskInstUpd.setModifyEmpid(operator);
                    taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
                }
            } else {
                log.error("table{} syncData failed", dstTableName);
                return new ProcessResult(false, "datax脚本执行失败");
            }
        } catch (QanatBizException e) {
            log.error("DataX任务调度异常:{}", e.getMessage(), e);
            return new ProcessResult(false, e.getMessage());
        } catch (Exception e) {
            log.error("DataX任务调度异常:{}", e.getMessage(), e);
            return new ProcessResult(false, e.getMessage());
        }
        return new ProcessResult(true);
    }
    
    private Integer countTable(Connection connection, String tableName) {
    	int cnt = 0;
    	String sql = "select count(1) as total from " + tableName;
        log.info("before exec sql={}", sql);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
            	cnt = resultSet.getInt("total");
            }
            log.info("after exec sql cnt={}", cnt);
        } catch(Exception e) {
            log.error("querySql failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                }
                resultSet = null;
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
        }
        return cnt;
    }
    
    private void createAdbTable(String tableName, Map<String, String> colNameTypeMap, String pk, String url, String username, String password, Boolean isBroadCast, String distributeKey) {
        List<String> colDefList = new ArrayList<>();
        for (String fieldName : colNameTypeMap.keySet()) {
            colDefList.add(fieldName + " " + colNameTypeMap.get(fieldName));
        }
        String sql = null;
        if (pk == null) {
        	sql = String.format("Create Table %s ( %s ) INDEX_ALL='Y';", tableName, StringUtils.join(colDefList, ","));
        } else {
        	String distributePart = "";
        	String pkPart = pk;
        	if (isBroadCast != null && isBroadCast) {
        		distributePart = "BROADCAST";
        	} else if (StringUtils.isNotBlank(distributeKey) && !pk.equalsIgnoreCase(distributeKey)) {
        		distributePart = "HASH(" + distributeKey + ")";
            	pkPart = pk + "," + distributeKey;
        	} else {
        		distributePart = "HASH(" + pk + ")";
        	}
        	sql = String.format("Create Table %s ( %s , primary key (%s) ) DISTRIBUTE BY %s INDEX_ALL='Y';", tableName, StringUtils.join(colDefList, ","), pkPart, distributePart);
        } 
        RdsConnectionParam param = new RdsConnectionParam();
        param.setUrl(url);
        param.setUserName(username);
        param.setPassword(password);
        Connection connection = null;
        Statement statement = null;
        try {
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement();
            log.info("create table ddl:{}", sql);
            statement.execute(sql);
        } catch (Exception e) {
            log.error("create adb table failed", e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {}
                statement = null;
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {}
                connection = null;
            }
        }
    }

    private void createHoloTable(String tableName, Map<String, String> colNameTypeMap, String pk, String url, String username, String password, String distributeKey, String clusteringKey) {
        List<String> colDefList = new ArrayList<>();
        for (String fieldName : colNameTypeMap.keySet()) {
            colDefList.add(fieldName.toLowerCase() + " " + HoloUtils.getHoloTypeFromMysql(colNameTypeMap.get(fieldName)));
        }
        String sql = null;
        if (pk == null) {
        	sql = String.format("Create Table %s ( %s );", tableName, StringUtils.join(colDefList, ","));
            sql += "call set_table_property('" + tableName + "', 'orientation', 'column');";
        } else {
        	sql = String.format("Create Table %s ( %s , primary key (%s) );", tableName, StringUtils.join(colDefList, ","), (StringUtils.isNotBlank(distributeKey) && !pk.equalsIgnoreCase(distributeKey) ? (pk + "," + distributeKey) : pk));
            sql += "call set_table_property('" + tableName + "', 'orientation', 'row,column');" + 
            		"call set_table_property('" + tableName + "', 'distribution_key', '" + (StringUtils.isNotBlank(distributeKey) ? distributeKey : pk) + "');";
            		if (StringUtils.isNotBlank(clusteringKey)) {
            			sql += "call set_table_property('" + tableName + "', 'clustering_key', '" + clusteringKey + "');";
            		}
            		sql += "call set_table_property('" + tableName + "', 'binlog.level', 'replica');\n" + 
    				"call set_table_property('" + tableName + "', 'binlog.ttl', '86400');";
        }
        
        RdsConnectionParam param = new RdsConnectionParam();
        param.setUrl(url);
        param.setUserName(username);
        param.setPassword(password);
        Connection connection = null;
        Statement statement = null;
        try {
            connection = dsHandler.connectToTable(param);
            connection.setAutoCommit(false);
            statement = connection.createStatement();
            sql = sql.replaceAll("`", "");
            log.info("create table ddl:{}", sql);
            String[] subSqls = sql.split(";");
            for (String subSql : subSqls) {
                log.info("start to exec subSql:{}", subSql);
            	statement.execute(subSql);
            }
            connection.commit();
        } catch (Exception e) {
        	if (connection != null) {
	        	try {
					connection.rollback();
				} catch (SQLException e1) {
				}
        	}
            log.error("create holo table failed", e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {}
                statement = null;
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {}
                connection = null;
            }
        }
    }

	private String getOdpsType(String type) {
        if (type.equalsIgnoreCase("varchar")) {
            return "string";
        } else if (type.equalsIgnoreCase("tinyint")) {
            return "int";
        }
        return type;
    }

    private void createOdpsTable(String tenantId, String appName, String tableName, String tableComment, List<ColumnInfo> columns) {
        	AppResourceRelationExample example = new AppResourceRelationExample();
        	example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andRelationTypeEqualTo("odps");
        	List<AppResourceRelation> rels = appResourceRelationMapper.selectByExample(example);
        	if (CollectionUtils.isEmpty(rels)) {
        		throw new QanatBizException("no app resouces");
        	}
        	AppResourceRelation ref = rels.get(0);
        	ResourceExample example1 = new ResourceExample();
        	example1.createCriteria().andResourceNameEqualTo(ref.getResourceName()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L);
        	List<com.aliyun.wormhole.qanat.dal.domain.Resource> resources = resourceMapper.selectByExampleWithBLOBs(example1);
        	if (CollectionUtils.isEmpty(resources)) {
        		throw new QanatBizException("no app resouces");
        	}
        	com.aliyun.wormhole.qanat.dal.domain.Resource resource = resources.get(0);
        	JSONObject metaJson = JSON.parseObject(resource.getMeta());
    	    OdpsClient client = new OdpsClient(metaJson.getString("endpoint"), metaJson.getString("accessId"), metaJson.getString("accessKey"),
    	    		metaJson.getString("project"), metaJson.getString("mcUrl"), metaJson.getString("mcToken"));

        try {
    	    client.queryOdpsSql("ALTER TABLE " + metaJson.getString("project") + "." + tableName + " RENAME TO " + metaJson.getString("project") + "." + tableName + "_D" + System.currentTimeMillis() + ";");
            log.info("逻辑删除ODPS表[{}]成功", tableName);
        } catch (Exception e) {
            log.error("逻辑删除ODPS表[{}.{}]失败", tableName, e);
        }
        try {
            Map<String, String> columnTypeMap = buildColumnTypeMap(columns);
            Map<String, String> columnCommentMap = buildColumnCommentMap(columns);
            client.createOdpsTable(tableName, columnTypeMap, columnCommentMap, tableComment, 3600L);
            log.info("ODPS建表[{}]成功", tableName);
        } catch (Exception e) {
            log.error("ODPS建表[{}]失败", tableName, e);
        }
    }

    /**
     * 获取字段类型对应关系
     *
     * @param columns columns
     * @return 返回结果
     */
    private Map<String, String> buildColumnTypeMap(List<ColumnInfo> columns) {
        //字段与字段类型Map
        Map<String, String> columnTypeMap = new LinkedHashMap<String, String>(16);
        if (CollectionUtils.isNotEmpty(columns)) {
            String dsKey="ds";
            for (ColumnInfo column : columns) {
                String colName = column.getColumnName();
                if (dsKey.equals(colName)) {
                    continue;
                }
                columnTypeMap.put(colName, StringUtils.trimToEmpty(column.getColumnType()));
            }
        }
        return columnTypeMap;
    }

    /**
     * 获取字段描述对应关系
     *
     * @param columns columns
     * @return 返回结果
     */
    private Map<String, String> buildColumnCommentMap(List<ColumnInfo> columns) {
        //字段与字段类型Map
        Map<String, String> columnCommentMap = new LinkedHashMap<String, String>(16);
        String dsKey="ds";
        if (CollectionUtils.isNotEmpty(columns)) {
            for (ColumnInfo column : columns) {
                String colName = column.getColumnName();
                if (dsKey.equals(colName)) {
                    continue;
                }
                columnCommentMap.put(colName, StringUtils.trimToEmpty(column.getColumnComment()));
            }
        }
        return columnCommentMap;
    }  
}