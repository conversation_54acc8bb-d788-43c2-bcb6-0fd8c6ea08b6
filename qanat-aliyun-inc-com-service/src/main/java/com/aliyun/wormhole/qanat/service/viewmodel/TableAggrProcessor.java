package com.aliyun.wormhole.qanat.service.viewmodel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.dto.ResourcePackage;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.FlowCtlService;
import com.aliyun.wormhole.qanat.api.service.KafkaManagementService;
import com.aliyun.wormhole.qanat.dal.domain.AppInfo;
import com.aliyun.wormhole.qanat.dal.domain.AppInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.mapper.AppInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.RelatedDataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Settings;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class TableAggrProcessor {
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private KafkaManagementService kafkaManagementService;
    
    @Resource
    private AppInfoMapper appInfoMapper;
    
    @Resource
    private DatasourceService dsInfoService;
    
    @Resource
    private ViewModelSqlBuilder viewModelSqlBuilder;
    
    @Resource
    private FullLinkProcessor fullLinkProcessor;
	
	@Resource
	private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;
	
	@Resource
	private FlowCtlService limiterService;
    
    @Value("${datatube.codegen.version}")
    private String codegenVersion;
	
	public static String BLINK_INCR_CHECK_ARRAY_OBJECT_TABLE_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--********************************************************************--\n" + 
			"create table drc_source (\n" + 
			"    msg varchar,\n" + 
			"    __traceId__ varchar header\n" + 
			") with (\n" + 
			"  type = 'custom',\n" + 
			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"  topic = '%s',\n" + 
			"  `group.id` = '%s',\n" + 
			"  `dbName` = '%s',\n" + 
			"  startupMode = 'TIMESTAMP',\n" + 
			"  fieldDelimiter = '`'\n" + 
			");\n" + 
			"\n" + 
			"create table check_result_mq_sink (\n" + 
			"    msg varchar,\n" + 
			"    id varchar,\n" + 
			"    __traceId__ varchar,\n" + 
			"    primary key(id)\n" + 
			") with (\n" + 
			"    type='QANAT_KAFKA010',\n" + 
			"    class='com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"    topic='%s',\n" + 
			"    dbName='%s'\n" + 
			");\n" + 
			"\n" + 
			"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" + 
			"CREATE FUNCTION arrayCompare AS 'com.aliyun.wormhole.qanat.blink.udf.QanatArrayCompareUdf';\n" + 
			"CREATE FUNCTION delayMs AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDelayMsUdf';\n" + 
			"\n" + 
			"create view v_drc as\n" + 
			"select \n" + 
			"    (case when JSON_VALUE(t.a, '$.%s') is null then JSON_VALUE(t.a, '$.%s_old') else JSON_VALUE(t.a, '$.%s') end) as id,\n" + 
			"    'drc' as src,\n" + 
			"    __traceId__\n" + 
			"from drc_source, LATERAL TABLE (parseDrcFields (msg, '%s')) as t (a)\n" + 
			";\n" + 
			"\n" + 
			"create view v_delay as\n" + 
			"select\n" + 
			"    id,\n" + 
			"    src,\n" + 
			"    delayMs(%s, id) as delay_ms,\n" + 
			"    __traceId__\n" + 
			"from v_drc\n" + 
			";\n" + 
			"\n" + 
			"create view v_check AS\n" + 
			"select distinct\n" + 
			"    a.id,\n" + 
			"    JSON_VALUE (b.x, '$.%s') as l_value,\n" + 
			"    JSON_VALUE (c.x, '$.%s') as r_value,\n" + 
			"    a.src,\n" + 
			"    a.__traceId__\n" + 
			"from v_delay as a\n" + 
			"LEFT JOIN LATERAL TABLE (queryDim('%s', '%s', a.id)) as b(x) ON TRUE\n" + 
			"LEFT JOIN LATERAL TABLE (queryDim('%s', 'select %s from %s where %s=?', a.id)) as c(x) ON TRUE\n" + 
			";\n" + 
			"\n" + 
			"create view v_check_result as \n" + 
			"select \n" + 
			"    (CASE when arrayCompare(l_value, r_value)=false then concat_ws('|', '%s', id, src, 'NG', l_value, r_value) else concat_ws('|', '%s', id, src, 'OK') END) as msg, id, __traceId__\n" + 
			"from v_check;\n" + 
			"insert into check_result_mq_sink\n" + 
			"select * from v_check_result;\n" + 
			"\n" +
			"CREATE TABLE correct_mq_sink (\n" + 
			"    ids varchar,\n" + 
			"    `key` varchar,\n" + 
			"    __traceId__ varchar,\n" + 
			"    primary key(`key`)\n" + 
			") WITH (\n" + 
			"    type='QANAT_KAFKA010',\n" + 
			"    class='com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"    topic='%s',\n" + 
			"    dbName='%s',\n" + 
			"    fieldDelimiter='`'\n" +  
			");\n" + 
			"\n" + 
			"insert into correct_mq_sink\n" + 
			"select\n" + 
			"  split_index (msg, '|', 1) as ids,\n" + 
			"  split_index (msg, '|', 1) as `key`,\n" + 
			"  __traceId__\n" + 
			"from v_check_result\n" + 
			"where split_index (msg, '|', 3)<>'OK' and split_index (msg, '|', 1) is not null and split_index (msg, '|', 1) <> '';" +
			"\n" + 
			"CREATE TABLE full_link_sink (\n" + 
			"  trace_id varchar,\n" + 
			"  key varchar,\n" + 
			"  ts bigint,\n" + 
			"  db varchar,\n" + 
			"  msg varchar,\n" +
			"  gmt_create timestamp\n" + 
			") WITH (\n" + 
			"  %s" + 
			");\n" + 
			"\n" + 
			"insert into full_link_sink\n" + 
			"select\n" + 
			"  __traceId__ as trace_id,\n" + 
			"  split_index (msg, '|', 1) as key,\n" + 
			"  NOW() as ts,\n" + 
			"  '%s' as db,\n" + 
			"  msg,\n" + 
			"  CURRENT_TIMESTAMP as gmt_create\n" + 
			"from v_check_result;\n"
			;

    public String processIncrSyncJob(String tenantId, String appName, List<String> dbNames, String etlDbName, String tableName, String arrayFieldName, RelatedDataObject object, String operateEmpid, Long versionId, JSONObject kafkaJson, Long datatubeInstId) {
    	String jobName = "incrsync_" + getAppIdByName(tenantId, appName) + "_" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "_" + object.getCode() + "_v" + versionId;
    	JSONObject dsMetaJson = dsInfoService.getOdsTableMetaByDsName(tenantId, object.getRef());
    	if (dsMetaJson.getJSONObject("incrConf") == null) {
    		log.info("no incr sync conf");
    		return null;
    	}
    	String topicName = dsMetaJson.getJSONObject("incrConf").getString("topicName");
    	String consumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "-incr_sync-" + object.getCode() + "-" + versionId;
    	boolean res = kafkaManagementService.createConsumerGroup(tenantId, appName, consumerId);
		if (!res) {
			log.error("consumer:{} create is failed", consumerId);
		}
		limiterService.setFlowLimitIfNotExists(datatubeInstId,consumerId, 1.0);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> columnDefines = new ArrayList<>();
        List<String> drcParseColumns = new ArrayList<>();
        List<String> dimParseColumns = new ArrayList<>();
        List<String> fkColumns = new ArrayList<>();
        List<String> fkColumns1 = new ArrayList<>();
        List<String> fkColumns2 = new ArrayList<>();
        Map<String, String> srcTgtMap = new HashMap<>();
        for (ViewModel.Relation rel : object.getRelations()) {
        	if (!rel.getRelatedField().startsWith("exp#")) {
        		fkColumns.add(rel.getRelatedField().split("\\.")[1]);
        		fkColumns1.add("'" + rel.getField() + "'");
        		fkColumns2.add(rel.getField());
        		srcTgtMap.put(rel.getField(), rel.getRelatedField().split("\\.")[1]);
        	}
        	break;
        }
        for (ViewModel.Field field : object.getFields()) {
        	if (srcTgtMap.containsKey(field.getCode())) {
        		columnDefines.add("`" + srcTgtMap.get(field.getCode()) + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
        	}
        }
		columnDefines.add("`" + arrayFieldName + "` varchar");
        for (ViewModel.Field field : object.getFields()) {
        	if (srcTgtMap.containsKey(field.getCode())) {
        		if ("varchar".equalsIgnoreCase(field.getType())) {
            		drcParseColumns.add("(case when JSON_VALUE (b.x, '$." + field.getRef() + "') is null then JSON_VALUE (b.x, '$." + field.getRef() + "_old') else JSON_VALUE (b.x, '$." + field.getRef() + "') end) as " + field.getRef());
            	} else {
            		drcParseColumns.add("CAST((case when JSON_VALUE (b.x, '$." + field.getRef() + "') is null then JSON_VALUE (b.x, '$." + field.getRef() + "_old') else JSON_VALUE (b.x, '$." + field.getRef() + "') end) AS " + field.getType() + ") as " + field.getRef());
            	}
        		continue;
        	}
        }
    	dimParseColumns.add("JSON_VALUE (b.x, '$." + arrayFieldName + "') AS " + arrayFieldName);
        String sql = "--SQL\n" + 
    			"--********************************************************************--\n" + 
    			"--Author: " + operateEmpid + "\n" + 
    			"--CreateTime: " + sdf.format(new Date()) + "\n" + 
    			"--Comment: " + ("sync for " + tableName + " from " + object.getCode()) + "\n" + 
    			"--Version: " + codegenVersion + "\n" + 
    			"--********************************************************************--\n" + 
    			"create table mq_source (\n" + 
                "    msg varchar,\r\n" + 
                "    __traceId__ varchar header\r\n" +
                ") with (\r\n" + 
                "  type = 'custom',\r\n" + 
                "  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\r\n" + 
                "  topic = '" + topicName + "',\r\n" + 
                "  `group.id` = '" + consumerId + "',\r\n" + 
                "  `dbName` = '" + kafkaJson.getString("dbName") + "',\r\n" + 
                "  startupMode = 'TIMESTAMP',\r\n" + 
                "  fieldDelimiter = '`'\r\n" + 
    			");\n" + 
    			"\n" + 
    			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" + 
    			"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
    			"\n" + 
    			"create view v_mq as \n" + 
    			"select \n" + 
    			"    " + StringUtils.join(drcParseColumns, ",") + ",\n" + 
    			"    __traceId__ as __trace_id__\n" + 
    			"from\n" + 
    			"    mq_source as s,\n" + 
    			"    LATERAL TABLE (parseDrcFields (msg, " + StringUtils.join(fkColumns1, ",") + ")) as b(x)\n" + 
    			";\n" + 
    			"\n" + 
    			"create view v_group as \n" + 
    			"select\n" + 
    			"  " + StringUtils.join(fkColumns2, ",") + ",\n" + 
    			"  " + StringUtils.join(dimParseColumns, ",") + ",\n" + 
    			"  a.__trace_id__\n" + 
    			"from\n" + 
    			"      v_mq as a\n" + 
    			"      LEFT JOIN LATERAL TABLE (queryDim('" + ("ods".equalsIgnoreCase(object.getLookupFrom()) ? dsMetaJson.getString("srcDbName") : etlDbName) + "', '" + viewModelSqlBuilder.getSqlFromAggrField(tenantId, arrayFieldName, object, fkColumns2, ("ods".equalsIgnoreCase(object.getLookupFrom()) ? null : dsInfoService.getDbMetaByName(etlDbName).getString("dbType"))).replace("'", "''") + "', " + StringUtils.join(fkColumns2, ",") + ")) as b (x) ON TRUE\n" +  
    			"where " + StringUtils.join(fkColumns2, ",") + " is not null\n" + 
    			";\n" + 
    			"\n";

    	String eventTopicName = "stream-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "-" + object.getCode();
    	boolean kfkRes = kafkaManagementService.createTopic(tenantId, appName, eventTopicName);
		if (!kfkRes) {
			log.error("topic:{} create is failed", eventTopicName);
		}
		
        for (int i = 0; i < dbNames.size(); i++) {
			sql += "create table adb_sink_" + i + " (\n" +
    			"    " + StringUtils.join(columnDefines, ",") + ",\n" + 
    			"    __trace_id__ varchar,\n" + 
    			"    primary key(" + StringUtils.join(fkColumns, ",") + ")\n" + 
    			") with (\n" + 
    			"    type = 'QANAT_ADB30',\n" + 
    			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
    			"    dbName='" + dbNames.get(i) + "',\n" + 
    			"    tableName='" + tableName + "',\n" + 
    			"    replaceMode = 'update_by_query',\n" + 
    			"    writeMode = 'single',\n";
			if (etlDbName.equalsIgnoreCase(dbNames.get(i))) {
    			sql += "    streamType = 'kafka',\n" + 
    			"    eventTopic = '" + eventTopicName + "',\n" + 
    			"    eventServer = '" + kafkaJson.getString("dbName") + "'\n";
			} else {
    			sql += "    streamEvent = 'disable'\n";
			}
    		sql += ");\n" + 
    			"\n" + 
    			"insert into adb_sink_" + i + "\n" + 
    			"select * from v_group;" +
    			"\n";
        }
        
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_KAFKA010), false);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        
        return jobName;
    }

    public boolean processIncrCheckJob(String tenantId, String appName, String jobName, String dbName, String tableName, String arrayFieldName, RelatedDataObject object, String operateEmpid, Long versionId, JSONObject kafkaJson, Settings modelSettings, Long datatubeInstId) {
    	JSONObject dsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, object.getRef());
    	if (dsMetaJson.getJSONObject("incrConf") == null) {
    		log.info("no incr sync conf");
    		return false;
    	}
    	String checkResultTopicName = "chk-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-" + object.getCode();
    	String topicName = dsMetaJson.getJSONObject("incrConf").getString("topicName");
    	String consumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-incr_check-" + object.getCode() + "-" + versionId;
    	boolean res = kafkaManagementService.createConsumerGroup(tenantId, appName, consumerId);
		if (!res) {
			log.error("consumer:{} create is failed", consumerId);
		}
    	res = kafkaManagementService.createTopic(tenantId, appName, checkResultTopicName);
		if (!res) {
			log.error("topic:{} create is failed", checkResultTopicName);
		}
    	String correctTopicName = "crt-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-" + object.getCode();
    	res = kafkaManagementService.createTopic(tenantId, appName, correctTopicName);
		if (!res) {
			log.error("topic:{} create is failed", correctTopicName);
		}

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String pkField = null;
        String fkField = null;
        for (ViewModel.Relation rel : object.getRelations()) {
        	if (!rel.getRelatedField().startsWith("exp#")) {
        		fkField = object.getFields().stream().filter(e->e.getCode().equalsIgnoreCase(rel.getField())).collect(Collectors.toList()).get(0).getRef();
        		pkField = rel.getRelatedField().split("\\.")[1];
        	}
        	break;
        }
        String sql = String.format(BLINK_INCR_CHECK_ARRAY_OBJECT_TABLE_SQL
            , operateEmpid
            , sdf.format(new Date())
            , "incr check for " + tableName + " from " + object.getCode()
            , topicName
            , consumerId
            , kafkaJson.getString("dbName")
            , checkResultTopicName
            , kafkaJson.getString("dbName")
            , fkField
            , fkField
            , fkField
            , fkField
            , modelSettings.getIncrCheckDelayMs()
            , arrayFieldName
            , arrayFieldName
            , dsMetaJson.getString("dbName")
            , viewModelSqlBuilder.getSqlFromAggrField(tenantId, arrayFieldName, object, Arrays.asList(fkField), null).replace("'", "''")
            , dbName
            , arrayFieldName
            , tableName
            , pkField
            , tableName + "." + arrayFieldName
            , tableName + "." + arrayFieldName
            , correctTopicName
            , kafkaJson.getString("dbName")
            , fullLinkProcessor.getFullLinkSinkWithClause(tenantId, appName)
            , checkResultTopicName
            );
        
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_KAFKA010, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_UDF, ResourcePackage.BLINK_ADB3), false);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        
        return true;
    }
	
	private Long getAppIdByName(String tenantId, String appName) {
		AppInfoExample example = new AppInfoExample();
		example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
		List<AppInfo> apps = appInfoMapper.selectByExample(example);
		return apps.get(0).getId();
	}
}