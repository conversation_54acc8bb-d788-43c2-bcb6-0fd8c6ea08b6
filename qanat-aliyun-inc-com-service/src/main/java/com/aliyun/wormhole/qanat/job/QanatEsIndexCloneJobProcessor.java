package com.aliyun.wormhole.qanat.job;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.EsIndexCloneNode;
import com.aliyun.wormhole.qanat.api.service.ElasticSearchService;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * ES索引克隆任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatEsIndexCloneJobProcessor extends AbstractQanatNodeJobProcessor<EsIndexCloneNode> {
    
    @Resource
    private ElasticSearchService esService;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;

	@Override
	void doProcess(Map<String, Object> instParamsMap, EsIndexCloneNode node) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
        String dateStr = sdf.format(new Date());
        
        String indexAlias = node.getIndexName();
        String newIndexName = indexAlias + "_v" + dateStr;
        String dbName = node.getDbName();
        Long taskInstId = Long.valueOf(String.valueOf(instParamsMap.get("taskInstId")));
        
        DbInfoExample dbExample = new DbInfoExample();
        dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("db:" + dbName + " is not found");
        }
        DbInfo dbInfo = dbs.get(0);
        
        String indexName = esService.getIndexByAlias(dbInfo.getTenantId(), dbName, indexAlias);
        if (StringUtils.isBlank(indexName)) {
        	throw new QanatBizException("get current index name by alias[" + indexAlias + "] failed");
        }
        if (esService.createIndexByClone(dbInfo.getTenantId(), dbName, newIndexName, indexName)) {
        	// 记录流程变量
            TaskInstance taskInst = taskInstanceMapper.selectByPrimaryKey(taskInstId);
            JSONObject execParam = JSON.parseObject(taskInst.getExecParam());
            if (execParam == null) {
            	execParam = new JSONObject();
            }
            Map<String, String> data = new HashMap<>();
            execParam.put("data", data);
            data.put("es_index", newIndexName);
            data.put("es_index_old", indexName);
            data.put("es_alias", indexAlias);
            data.put("dbName", dbName);
            data.put("tenantId", dbInfo.getTenantId());
            
            TaskInstance taskInstUpd = new TaskInstance();
            taskInstUpd.setId(taskInstId);
            taskInstUpd.setExecParam(JSON.toJSONString(execParam));
            taskInstUpd.setGmtModified(new Date());
            taskInstUpd.setModifyEmpid("schedulerx");
            taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
            
        	log.info("new index:{} cloned", newIndexName);
        } else {
        	throw new QanatBizException("clone new index[" + newIndexName + "] name from current index[" + indexName + "] failed");
        }
	}
}