package com.aliyun.wormhole.qanat.job;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.EsIndexSwitchNode;
import com.aliyun.wormhole.qanat.api.service.ElasticSearchService;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

/**
 * ES索引切换任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatEsIndexSwitchJobProcessor extends AbstractQanatNodeJobProcessor<EsIndexSwitchNode> {
    
    @Resource
    private ElasticSearchService esService;
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;

	@Override
	void doProcess(Map<String, Object> instParamsMap, EsIndexSwitchNode node) {
        Long taskInstId = Long.valueOf(String.valueOf(instParamsMap.get("taskInstId")));
        
        TaskInstance taskInst = taskInstanceMapper.selectByPrimaryKey(taskInstId);
        JSONObject execParam = JSON.parseObject(taskInst.getExecParam());
        if (execParam == null) {
        	throw new QanatBizException("execParam is neccesary");
        }
        String newIndexName = execParam.getJSONObject("data").getString("es_index");
        String oldIndexName = execParam.getJSONObject("data").getString("es_index_old");
        String indexAlias = execParam.getJSONObject("data").getString("es_alias");
        String dbName = execParam.getJSONObject("data").getString("dbName");
        String tenantId =  execParam.getJSONObject("data").getString("tenantId");

        Long oldIndexCnt = esService.countIndex(tenantId, dbName, oldIndexName);
        Long newIndexCnt = esService.countIndex(tenantId, dbName, newIndexName);
        log.info("old index:{} count:{} new index:{} count:{}", oldIndexName, oldIndexCnt, newIndexName, newIndexCnt);

        if (oldIndexCnt > 0 && newIndexCnt == 0 ) {
            throw new QanatBizException("switch new index[" + newIndexName + "] failed due to index is empty");
        }
        if (oldIndexCnt > 0 && (double)newIndexCnt/oldIndexCnt < 0.5 ) {
            throw new QanatBizException("switch new index[" + newIndexName + "] failed due to new index total count check failed");
        }
        
        if (esService.setIndexAlias(tenantId, dbName, indexAlias, newIndexName, oldIndexName)) {
        	log.info("new index:{} switched", newIndexName);
        } else {
        	throw new QanatBizException("switch new index[" + newIndexName + "] failed");
        }
	}
}