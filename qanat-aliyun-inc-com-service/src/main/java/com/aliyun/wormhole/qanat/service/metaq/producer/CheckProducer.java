package com.aliyun.wormhole.qanat.service.metaq.producer;

import javax.annotation.PostConstruct;

import com.alibaba.rocketmq.common.message.Message;
import com.aliyun.wormhole.qanat.service.enumerate.EventType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.rocketmq.client.exception.MQClientException;
import com.google.gson.Gson;
import com.taobao.metaq.client.MetaProducer;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class CheckProducer{
	private static final Logger logger = LoggerFactory.getLogger(CheckProducer.class);
	private static final Gson gson = new Gson();

	private String group="CID_qanat_check_topic";

	private String instanceName = "qanat_check_task";

	private String topic = "qanat_check_topic";

	MetaProducer metaProducer;

	@PostConstruct
	public void init() {
		try {
			metaProducer = new MetaProducer(getGroup());
			metaProducer.setInstanceName(getInstanceName());
			metaProducer.start();
		} catch (MQClientException e) {
			throw new RuntimeException("metaq 发送消息异常", e);
		}
	}

	/**
	 * //TIME： 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
	 * //LEVEL：1  2  3   4   5  6  7  8  9  10 11 12 13 14  15  16  17 18
	 * @param body
	 * @param tag
	 * @param eventType
	 * @return
	 */
	public boolean sendDelayCheckMsg(byte[] body, String tag, EventType eventType) {
		Message message = new Message(getTopic(), tag, body);
		message.setDelayTimeLevel(1);
		try {
			metaProducer.send(message);
			return true;
		} catch (Exception e) {
			logger.error("sendAddMsg has error", e);
			return false;
		}
	}



	public String getGroup() {
		return group;
	}
	public void setGroup(String group) {
		this.group = group;
	}
	public String getInstanceName() {
		return instanceName;
	}
	public void setInstanceName(String instanceName) {
		this.instanceName = instanceName;
	}
	public String getTopic() {
		return topic;
	}
	public void setTopic(String topic) {
		this.topic = topic;
	}

	public MetaProducer getMetaProducer() {
		return this.metaProducer;
	}
}
