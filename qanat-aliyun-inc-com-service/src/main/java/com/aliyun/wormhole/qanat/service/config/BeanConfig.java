package com.aliyun.wormhole.qanat.service.config;

import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.servlet.AteyeServlet;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.http.HttpServlet;

@Configuration
public class BeanConfig {

    @Value("${region.type}")
    private String region;

    //*
    @Bean(name = "ateyeServlet")
    public ServletRegistrationBean ateyeServletRegistrationBean() {
        if(StringUtils.equals("inner", region)) {
            AteyeServlet ateyeServlet = new AteyeServlet();
            ServletRegistrationBean servletRegistrationBean = new ServletRegistrationBean(ateyeServlet, "/agent.ateye");
            servletRegistrationBean.setLoadOnStartup(1);
            servletRegistrationBean.addInitParameter("app", "qanat-aliyun-inc-com");
            servletRegistrationBean.addInitParameter("isRecordErrorLogger", "true");
            servletRegistrationBean.addInitParameter("kvTime","60");
            return servletRegistrationBean;
        } else {
            HttpServlet servlet = new HttpServlet() {};
            ServletRegistrationBean servletRegistrationBean = new ServletRegistrationBean(servlet, "/base");
            servletRegistrationBean.setLoadOnStartup(1);
            return servletRegistrationBean;
        }
    }

    @AteyeInvoker
    public String getRegion(){
        return region;
    }
}
