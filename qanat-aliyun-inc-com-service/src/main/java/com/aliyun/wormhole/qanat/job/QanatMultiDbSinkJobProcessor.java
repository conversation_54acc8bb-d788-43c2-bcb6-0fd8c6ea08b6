package com.aliyun.wormhole.qanat.job;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.MultiDbSinkNode;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTaskExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfo;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelVersionWithBLOBs;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelVersionMapper;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.service.ods.OdsHandler;
import com.aliyun.wormhole.qanat.service.util.YamlUtil;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelHandler;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelOptimizer;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 多库Sink任务入口
 * <AUTHOR>
 * 2022年2月22日
 */
@Slf4j
@Component
public class QanatMultiDbSinkJobProcessor extends AbstractQanatNodeJobProcessor<MultiDbSinkNode> {
    
    @Resource
    private DatasourceMapper datasourceMapper;

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
	
	@Resource
	private DatatubeInstanceMapper datatubeInstanceMapper;
	
	@Resource
	private ViewModelInfoMapper viewModelInfoMapper;
	
	@Resource
	private ViewModelVersionMapper viewModelVersionMapper;
	
	@Resource
	private ViewModelOptimizer viewModelOptimizer;
	
	@Resource
	private OdsHandler odsHandler;
	
	@Resource
	private ViewModelHandler viewModelHandler;
	
	@Resource
	private BlinkService blinkService;
	
	@Resource
	private TaskInstanceMapper taskInstanceMapper;
	
	@Resource
	private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;



	@Override
	void doProcess(Map<String, Object> instParamsMap, MultiDbSinkNode node) {
	    log.info("doProcess({},{})", JSON.toJSONString(instParamsMap), JSON.toJSONString(node));
	    String tenantId = (String)instParamsMap.get("tenantId");
	    String appName = (String)instParamsMap.get("appName");
        Long taskInstId = Long.valueOf(String.valueOf(instParamsMap.get("taskInstId")));
        String operator = (String)instParamsMap.get("operator");
	    
	    DatatubeInstance inst = datatubeInstanceMapper.selectByPrimaryKey(node.getDatatubeInstId());
	    ViewModelInfo viewModelInfo = viewModelInfoMapper.selectByPrimaryKey(inst.getProviderId());
	    
	    ViewModelVersionWithBLOBs viewModelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());

        ViewModel originModel = YamlUtil.getViewModel(viewModelVersion.getUserYaml());
    	ViewModel sysModel = null;
    	if (originModel.isDynamic()) {
    		sysModel = viewModelOptimizer.getOptimizedViewModel(tenantId, viewModelVersion.getUserYaml());
    	} else {
    		sysModel = YamlUtil.getViewModel(viewModelVersion.getSysYaml());
    	}
    	ViewModel dataModel = new ViewModel();
    	BeanUtils.copyProperties(sysModel, dataModel);
	    
    	String tmpTableName = "tmp_" + node.getTableName();
    	String bakTableName = "bak_" + node.getTableName();
    	
	    if (!clearTmpTableData(tenantId, node.getDstDbNames(), tmpTableName, dataModel, viewModelInfo.getObjectType())) {
        	throw new QanatBizException("clearTmpTableData[" + tmpTableName + "] failed");
	    }
    	if (!runBlinkJob(tenantId, appName, node.getJobName(), taskInstId, operator)) {
        	throw new QanatBizException("runBlinkJob[" + node.getJobName() + "] failed");
    	}
	    if (!switchOnlineTable(tenantId, node.getDstDbNames(), node.getTableName(), tmpTableName, bakTableName)) {
        	throw new QanatBizException("switchOnlineTable[" + node.getTableName() + "] failed");
	    }
	    log.info("QanatMultiDbSinkJobProcessor[{}] finished", node.getId());
	}
	
	private boolean clearTmpTableData(String tenantId, String dstDbNames, String tableName, ViewModel dataModel, String objectType) {
		for (String dbName : dstDbNames.split(",")) {
	        try {
		    	viewModelHandler.createTable(tenantId, dataModel, objectType, dbName, tableName, true);
	        } catch (Exception e) {
	            log.error("clearTmpTableData[{}.{}] failed, error={}", dbName, tableName, e.getMessage(), e);
	            return false;
	        }
		}
		return true;
	}
	
	private boolean switchOnlineTable(String tenantId, String dstDbNames, String tableName, String tmpTableName, String bakTableName) {
		for (String dbName : dstDbNames.split(",")) {
	        try {
		        DbInfoExample dbExample = new DbInfoExample();
		        dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName).andTenantIdEqualTo(tenantId);
		        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
		        if (CollectionUtils.isEmpty(dbs)) {
		            throw new QanatBizException("dbInfo:" + dbName + " is not found");
		        }
		        DbInfo dbInfo = dbs.get(0);
		        JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
		        
		        RdsConnectionParam param = new RdsConnectionParam();
		        String jdbcUrl = getDbConnectionUrl(dbMetaJson);
		        param.setUrl(jdbcUrl);
		        param.setPassword(dbMetaJson.getString("password"));
		        param.setUserName(dbMetaJson.getString("username"));

	            String sql = "alter table " + tableName + " rename to " + bakTableName + ";alter table " + tmpTableName + " rename to " + tableName + ";alter table " + bakTableName + " rename to " + tmpTableName;
		        executeSql(jdbcUrl, dbMetaJson.getString("username"), dbMetaJson.getString("password"), sql);
	        } catch (Exception e) {
	            log.error("clearTmpTableData[{}.{}] failed, error={}", dbName, tableName, e.getMessage(), e);
	            return false;
	        }
		}
		return true;
	}
	
	private void executeSql(String jdbcUrl, String username, String password, String userSql) {
		Connection connection = null;
        Statement statement = null;
        try {
	        
	        RdsConnectionParam param = new RdsConnectionParam();
	        param.setUrl(jdbcUrl);
	        param.setPassword(password);
	        param.setUserName(username);
	        
	        connection = dsHandler.connectToTable(param);
	        statement = connection.createStatement();
	       
            String [] sqls = userSql.split(";");
            for (String sql : sqls) {
                long startTs = System.currentTimeMillis();
                try {
	                statement.execute(sql);
	                log.info("sql:{} exec finished using {} ms", sql, System.currentTimeMillis()-startTs);
                } catch(Exception e) {
	                log.error("sql:{} exec failed due to:{}", sql, e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("exec sql failed, error={}", e.getMessage(), e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                }
                connection = null;
            }
        }
	}
	
	private boolean runBlinkJob(String tenantId, String appName, String jobName, Long taskInstId, String operator) {
		try {
			JSONObject execParamJson = null;
	        if (taskInstId != null) {
	        	TaskInstance taskInst = taskInstanceMapper.selectByPrimaryKey(taskInstId);
	        	execParamJson = JSON.parseObject(taskInst.getExecParam());
	        }
			Map<String, String> blinkPamams = null;
	        if (execParamJson != null) {
	           	JSONObject dataJson = execParamJson.getJSONObject("data");
	           	if (dataJson != null && CollectionUtils.isNotEmpty(dataJson.keySet())) {
	           		blinkPamams = new HashMap<>();
	           		for (String key : dataJson.keySet()) {
	           			blinkPamams.put(key, dataJson.getString(key));
	           		}
	           	}
	        }
			Long blinkInstId = blinkService.startJob(tenantId, appName, jobName, null, blinkPamams);
	        boolean isGetCU = false;
	
	        while (true) {
	            String instState = blinkService.getInstanceActualState(tenantId, appName, jobName, blinkInstId);
	            if ("SUCCESS".equalsIgnoreCase(instState)) {
	                break;
	            } else if ("RUNNING".equalsIgnoreCase(instState) && !isGetCU) {
	            	try {
	            		Map<String, Object> resource = blinkService.getInstanceResource(tenantId, appName, jobName);
	    				if (resource != null && CollectionUtils.isNotEmpty(resource.keySet())) {
	        				BigDecimal vcoreCU = resource.get("allocatedVirtualCores") != null ? new BigDecimal(resource.get("allocatedVirtualCores").toString()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");
	        				BigDecimal mbCU = resource.get("allocatedMB") != null ? new BigDecimal(resource.get("allocatedMB").toString()).divide(new BigDecimal("4096"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");
	
	                    	DatatubeInstanceTaskExample example = new DatatubeInstanceTaskExample();
	                    	example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTaskNameEqualTo(jobName).andTaskTypeEqualTo("blink_batch");
	                    	
	                    	DatatubeInstanceTask record = new DatatubeInstanceTask();
	                		record.setGmtModified(new Date());
	                		record.setModifyEmpid(operator);
	        				record.setTaskCu(vcoreCU.compareTo(mbCU) > 0 ? vcoreCU : mbCU);
	                    	datatubeInstanceTaskMapper.updateByExampleSelective(record, example);
	                    	
	                    	isGetCU = true;
	    				}
	            	} catch (Exception e) {
	            		log.error("{} get cu failed:{}", jobName, e.getMessage());
	                	isGetCU = true;
	            	}
	            } else if ("TERMINATED".equalsIgnoreCase(instState)
	            		|| "FAILED".equalsIgnoreCase(instState)) {
	            	throw new QanatBizException("Blink任务执行失败或手动停止");
	            }
	            Thread.sleep(60000);//60s
	        }
	        return true;
		} catch (Exception e) {
			log.error("exec blink job:{} failed:{}", jobName, e.getMessage());
			return false;
		}
	}
}