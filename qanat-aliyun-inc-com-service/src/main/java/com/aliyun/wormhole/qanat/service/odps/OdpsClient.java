package com.aliyun.wormhole.qanat.service.odps;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.odps.Column;
import com.aliyun.odps.Instance;
import com.aliyun.odps.Odps;
import com.aliyun.odps.OdpsException;
import com.aliyun.odps.OdpsType;
import com.aliyun.odps.PartitionSpec;
import com.aliyun.odps.TableSchema;
import com.aliyun.odps.account.Account;
import com.aliyun.odps.account.AliyunAccount;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.data.RecordReader;
import com.aliyun.odps.task.SQLTask;
import com.aliyun.odps.tunnel.TableTunnel;
import com.aliyun.odps.tunnel.TableTunnel.DownloadSession;
import com.aliyun.odps.type.PrimitiveTypeInfo;
import com.aliyun.odps.type.TypeInfoFactory;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.ExceptionCodeConstants;
import com.aliyun.wormhole.qanat.api.dto.OdpsColumnDTO;
import com.aliyun.wormhole.qanat.api.dto.QanatSystemConstants;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dataworks.model.v20200918.GetMetaTableColumnRequest;
import com.aliyuncs.dataworks.model.v20200918.GetMetaTableColumnResponse;
import com.aliyuncs.dataworks.model.v20200918.GetMetaTableColumnResponse.Data.ColumnListItem;
import com.aliyuncs.dataworks.model.v20200918.GetMetaTablePartitionRequest;
import com.aliyuncs.dataworks.model.v20200918.GetMetaTablePartitionResponse;
import com.aliyuncs.dataworks.model.v20200918.GetMetaTablePartitionResponse.Data.DataEntityListItem;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.taobao.diamond.client.Diamond;
import com.taobao.pandora.boot.PandoraBootstrap;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class OdpsClient {
    
	private Odps odps;
	private IAcsClient dataworksClient;
    private String project;
	
	private ArrayList<Callable<List<List<String>>>> callers=null;
	
	protected static DateTimeFormatter DF = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
	
	private static final String BIZ_ID_PREFIX="124299^aliyun_wormhole^id^";
	private static final String BIZ_ERROR_MSG="porject 或者 table 缺失";
	private static final String PROJECT_TABLE_SQL_MISS="porject 或者 table 或者 sql 缺失";
	private static final String ODPS_PREFIX="odps.";
	private static final String EXCUTE_SQL_ERROR="执行ODPS语句错误:";
	private static final String BIZ_ID_KEY="biz_id";
	
	public OdpsClient(String endpoint, String accessId, String accessKey, String project, String mcUrl, String mcToken) {
	    this.project = project;
	    try {
	    	//ODPS
			Account account = new AliyunAccount(accessId, accessKey);
			odps = new Odps(account);
			odps.setEndpoint(endpoint);
			odps.setDefaultProject(project);
			
			//Dataworks
			String dwJsonStr = Diamond.getConfig("qanat-dataworks", "DEFAULT_GROUP", 30000);
			log.info("dwJsonStr={}", dwJsonStr);
			JSONObject dwJson = JSON.parseObject(dwJsonStr);
	
			DefaultProfile.addEndpoint(dwJson.getString("region"), dwJson.getString("product"), dwJson.getString("endpoint"));
			IClientProfile profile = DefaultProfile.getProfile(dwJson.getString("region"), dwJson.getString("akId"), dwJson.getString("akSecret"));
			dataworksClient = new DefaultAcsClient(profile);
		} catch (Exception e) {
			throw new QanatBizException(ExceptionCodeConstants.OdpsOperate.CONNECT_ODPS_ERROR, "连接到ODPS错误",e);
		}
	}

	public Map<String, String> getTableColumnComment(String tableName) {
		Map<String, String> columnComment = new LinkedHashMap<>();
		try {
			GetMetaTableColumnRequest getMetaTableColumnRequest = new GetMetaTableColumnRequest();
			getMetaTableColumnRequest.setTableGuid(ODPS_PREFIX + project + "." + tableName);
			GetMetaTableColumnResponse acsResponse = dataworksClient.getAcsResponse(getMetaTableColumnRequest);
			if (acsResponse.getData() != null && CollectionUtils.isNotEmpty(acsResponse.getData().getColumnList())) {
				long totalCount = acsResponse.getData().getTotalCount();
				long pages = totalCount/100 + 1;
				for (int i = 0; i < pages; i++) {
					getMetaTableColumnRequest.setPageNum(i + 1);
					getMetaTableColumnRequest.setPageSize(100);
					acsResponse = dataworksClient.getAcsResponse(getMetaTableColumnRequest);
					if (acsResponse.getData() != null && CollectionUtils.isNotEmpty(acsResponse.getData().getColumnList())) {
						for (ColumnListItem item : acsResponse.getData().getColumnList()) {
						    if (!item.getIsPartitionColumn()) {
						    	columnComment.put(item.getColumnName(), item.getComment());
						    }
						}
					}
					if (i < (pages-1)) {
						Thread.sleep(1000);
					}
				}
			}
		} catch (Exception e) {
			throw new QanatBizException(ExceptionCodeConstants.Common.SYSTEM_ERROR, "获取表字段备注信息失败",e);
		}
		return columnComment;
	}
	
	public Map<String, String> getTableColumnType(String tableName) {
		Map<String, String> columnTypeMap = new HashMap<String, String>(16);
		try {
			GetMetaTableColumnRequest getMetaTableColumnRequest = new GetMetaTableColumnRequest();
			getMetaTableColumnRequest.setTableGuid(ODPS_PREFIX + project + "." + tableName);
			GetMetaTableColumnResponse acsResponse = dataworksClient.getAcsResponse(getMetaTableColumnRequest);
			if (acsResponse.getData() != null && CollectionUtils.isNotEmpty(acsResponse.getData().getColumnList())) {
				long totalCount = acsResponse.getData().getTotalCount();
				long pages = totalCount/100 + 1;
				for (int i = 0; i < pages; i++) {
					getMetaTableColumnRequest.setPageNum(i + 1);
					getMetaTableColumnRequest.setPageSize(100);
					acsResponse = dataworksClient.getAcsResponse(getMetaTableColumnRequest);
					if (acsResponse.getData() != null && CollectionUtils.isNotEmpty(acsResponse.getData().getColumnList())) {
						for (ColumnListItem item : acsResponse.getData().getColumnList()) {
						    if (!item.getIsPartitionColumn()) {
						        columnTypeMap.put(item.getColumnName(), item.getColumnType());
						    }
						}
					}
					if (i < (pages-1)) {
						Thread.sleep(1000);
					}
				}
			}
		} catch (Exception e) {
			throw new QanatBizException(ExceptionCodeConstants.Common.SYSTEM_ERROR, "获取表字段类型信息失败",e);
		}
		return columnTypeMap;
	}
	

	public List<Map<String, String>> getTablePartitions(String tableName) {
		if (StringUtils.isEmpty(tableName)) {
			throw new QanatBizException(ExceptionCodeConstants.Common.PARAM_NULL, BIZ_ERROR_MSG);
		}
		List<Map<String, String>> partitions = new ArrayList<Map<String, String>>();
		try {
			GetMetaTablePartitionRequest getMetaTablePartitionRequest = new GetMetaTablePartitionRequest();
			getMetaTablePartitionRequest.setTableGuid(ODPS_PREFIX + project + "." + tableName);
			GetMetaTablePartitionResponse acsResponse = dataworksClient.getAcsResponse(getMetaTablePartitionRequest);
			if (acsResponse.getData() != null && CollectionUtils.isNotEmpty(acsResponse.getData().getDataEntityList())) {
				for (DataEntityListItem item : acsResponse.getData().getDataEntityList()) {
					String[] patitionArr = item.getPartitionName().split("=");
					Map<String, String> partitionMap = new LinkedHashMap<String, String>();
					partitionMap.put(patitionArr[0], patitionArr[1]);
					partitionMap.put("ds", patitionArr[1]);
					partitionMap.put("createTime", String.valueOf(item.getCreateTime()));
					partitions.add(partitionMap);
				}
			}
		} catch (Exception e) {
			throw new QanatBizException(ExceptionCodeConstants.OdpsOperate.READ_PARTITION_ERROR,
					"读取odps分区错误:", e);
		}
		return partitions;
	}
	
	public String getMaxPt(String tableName) {
		List<Map<String, String>> dsList = this.getTablePartitions(tableName);
		if (CollectionUtils.isEmpty(dsList)) {
			return null;
		}
		return dsList.stream().map(e -> e.get("ds")).sorted(Comparator.reverseOrder()).collect(Collectors.toList()).get(0);
	}
	
	public Map<String, String> getMaxPtInfo(String tableName) {
		List<Map<String, String>> dsList = this.getTablePartitions(tableName);
		System.out.println("dsList=" + JSON.toJSONString(dsList));
		if (CollectionUtils.isEmpty(dsList)) {
			return null;
		}
		Map<String, String> maxPtInfo = new HashMap<>();
		String maxPt = dsList.stream().map(e -> e.get("ds")).sorted(Comparator.reverseOrder()).collect(Collectors.toList()).get(0);
		String createTime = dsList.stream().filter(e -> e.get("ds").equalsIgnoreCase(maxPt)).map(e -> e.get("createTime")).collect(Collectors.toList()).get(0);
		maxPtInfo.put(maxPt, createTime);
		return maxPtInfo;
	}

	public String queryOdpsSql(String sql) {
		if (null == odps) {
			return null;
		}
		if (StringUtils.isEmpty(sql)) {
			throw new QanatBizException(ExceptionCodeConstants.Common.PARAM_NULL, PROJECT_TABLE_SQL_MISS);
		}
		String logview = "";
		
		Instance i;
		try {
			Map<String, String> hints = new HashMap<String, String>(16);
			

			String dataStr = DF.format(LocalDateTime.now());
			hints.put(BIZ_ID_KEY, BIZ_ID_PREFIX + dataStr);
			i = SQLTask.run(odps, odps.getDefaultProject(), sql, hints, null);
			i.waitForSuccess();
			logview = odps.logview().generateLogView(i, 7 * 24);
		} catch (OdpsException e) {
			log.error("odps query faied, exception={}", e.getMessage(), e);
			throw new QanatBizException(ExceptionCodeConstants.OdpsOperate.RUN_ODPS_QUERY_ERROR,
					EXCUTE_SQL_ERROR , e);
		}
		return logview;
	}

	public String setOdpsLifecycle(String tableName, Long lifecycle) {
		if (null == odps) {
			return null;
		}
		if (StringUtils.isEmpty(tableName) || lifecycle <= 0) {
			throw new QanatBizException(ExceptionCodeConstants.Common.PARAM_NULL, "porject或者生命周期缺失");
		}
		String logview = "";
		
		Instance i;
		try {
			Map<String, String> hints = new HashMap<String, String>(16);
			String dataStr = DF.format(LocalDateTime.now());
			hints.put(BIZ_ID_KEY, BIZ_ID_PREFIX + dataStr);
			i = SQLTask.run(odps, odps.getDefaultProject(),
					"alter table " + tableName + " set lifecycle " + String.valueOf(lifecycle) + ";", hints, null);

			i.waitForSuccess();
			logview = odps.logview().generateLogView(i, 7 * 24);
		} catch (OdpsException e) {
			throw new QanatBizException(ExceptionCodeConstants.OdpsOperate.RUN_ODPS_QUERY_ERROR,
					EXCUTE_SQL_ERROR , e);
		}
		return logview;
	}

	public List<Map<String, String>> getOdpsDataFromQuery(String sql) {
		if (null == odps) {
			return Collections.emptyList();
		}
		if (StringUtils.isEmpty(sql)) {
			throw new QanatBizException(ExceptionCodeConstants.Common.PARAM_NULL, PROJECT_TABLE_SQL_MISS);
		}
		
		Instance i;
		List<Map<String, String>> lines = new ArrayList<>();
		try {
			Map<String, String> hints = new HashMap<String, String>(16);
			String dataStr = DF.format(LocalDateTime.now());
			hints.put(BIZ_ID_KEY, BIZ_ID_PREFIX + dataStr);
			i = SQLTask.run(odps, odps.getDefaultProject(), sql, hints, null);

			i.waitForSuccess();
			odps.logview().generateLogView(i, 7 * 24);
			List<Record> records = SQLTask.getResult(i);
			if (CollectionUtils.isNotEmpty(records)) {
				Column[] columns = records.get(0).getColumns();
				for (Record r : records) {
					Map<String, String> column = new LinkedHashMap<>();
					for (int l = 0; l < columns.length; l++) {
						column.put(columns[l].getName(), String.valueOf(r.get(columns[l].getName())));
					}
					lines.add(column);
				}
			}

		} catch (OdpsException e) {
			throw new QanatBizException(ExceptionCodeConstants.OdpsOperate.RUN_ODPS_QUERY_ERROR,
					EXCUTE_SQL_ERROR , e);
		}
		return lines;
	}

	public void createOdpsTable(String tableName, Map<String, String> columnTypeMap,
			Map<String, String> columnCommentMap, String tableComment, Long lifeCycle) {
		if (null == odps) {
			throw new QanatBizException(ExceptionCodeConstants.OdpsOperate.CREATE_ODPS_TABLE_ERROR, "创建odps表失败");
		}
		if (StringUtils.isEmpty(tableName) || null == columnTypeMap
				|| null == columnCommentMap) {
			throw new QanatBizException(ExceptionCodeConstants.Common.PARAM_NULL, "porject 或者 table 或者建表缺失");
		}
		try {
			
			TableSchema schema = new TableSchema();
			for (Map.Entry<String, String> entry : columnTypeMap.entrySet()) {
				OdpsType odpsType = OdpsType.STRING;
				PrimitiveTypeInfo primitiveTypeInfo = TypeInfoFactory.getPrimitiveTypeInfo(odpsType);
				for (OdpsType e : OdpsType.values()) {
					if (e.toString().equalsIgnoreCase(entry.getValue())) {
						odpsType = e;
						primitiveTypeInfo = TypeInfoFactory.getPrimitiveTypeInfo(odpsType);
						break;
					}
				}
				if (null != columnCommentMap && null != columnCommentMap.get(entry.getKey())) {
					schema.addColumn(
							new Column(entry.getKey(), primitiveTypeInfo, columnCommentMap.get(entry.getKey())));
				} else {
					schema.addColumn(new Column(entry.getKey(), odpsType));
				}
			}

			// 增加分区列
			schema.addPartitionColumn(
					new Column(QanatSystemConstants.OdpsConstants.PARTITION_CLOUM_NAME, OdpsType.STRING));
			odps.tables().createTableWithLifeCycle(project, tableName, schema, tableComment, false, lifeCycle);
		} catch (OdpsException e) {
			throw new QanatBizException(ExceptionCodeConstants.OdpsOperate.CREATE_ODPS_TABLE_ERROR,
					"创建odps表失败:" , e);
		}
	}

	public void dropOdpsTable(String tableName) {
		if (null == odps) {
			throw new QanatBizException(ExceptionCodeConstants.OdpsOperate.DROP_ODPS_TABLE_ERROR, "删除odps表失败");
		}
		if (StringUtils.isEmpty(tableName)) {
			throw new QanatBizException(ExceptionCodeConstants.Common.PARAM_NULL, "删除表参数缺失");
		}
		try {
			
			odps.tables().delete(tableName, false);
		} catch (OdpsException e) {
			throw new QanatBizException(ExceptionCodeConstants.OdpsOperate.DROP_ODPS_TABLE_ERROR,
					"删除odps表失败:" ,e);
		}

	}

	public Long getTableDataCount(String tableName, String partitionSpec) {
		if (null == odps) {
			throw new QanatBizException(ExceptionCodeConstants.OdpsOperate.DROP_ODPS_TABLE_ERROR, "获取需要下载的数据数量失败");
		}
		
		TableTunnel tunnel = new TableTunnel(odps);
		PartitionSpec targetPartitionSpec = new PartitionSpec(partitionSpec);
		DownloadSession downloadSession;
		long count = 0L;
		try {
			downloadSession = tunnel.createDownloadSession(project, tableName, targetPartitionSpec);
			count = downloadSession.getRecordCount();
		} catch (Exception e) {
		    log.error("createDownloadSession failed", e);
			throw new QanatBizException(ExceptionCodeConstants.OdpsOperate.DROP_ODPS_TABLE_ERROR, "获取需要下载的数据数量失败");
		}

		return count;
	}
	
	ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("opeerate-downloadDataSplit-pool-%d").build();
    //通用线程池
    ExecutorService pool = new ThreadPoolExecutor(1, 1,
             0L, TimeUnit.MILLISECONDS,
             new LinkedBlockingQueue<>(1024), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());

	public List<List<String>> downloadDataSplit(String tableName, String partitionSpec,
			Long splitStart, Long count) {
		if (null == odps) {
			throw new QanatBizException(ExceptionCodeConstants.OdpsOperate.DROP_ODPS_TABLE_ERROR, "下载数据失败");
		}
		List<List<String>> results = new ArrayList<List<String>>();
		
		TableTunnel tunnel = new TableTunnel(odps);
		PartitionSpec targetPartitionSpec = new PartitionSpec(partitionSpec);
		DownloadSession downloadSession;
		try {
			downloadSession = tunnel.createDownloadSession(project, tableName, targetPartitionSpec);
			RecordReader recordReader = downloadSession.openRecordReader(splitStart, count);
			Future<List<List<String>>> future=pool.submit(new DownloadThread(recordReader, downloadSession.getSchema()));
			results.addAll(future.get(1L, TimeUnit.HOURS));
		} catch (Exception e) {
			log.error("downloadDataSplit error:",e);
		} 
		return results;

	}
	
	public List<List<String>> downloadData(String tableName, String partitionSpec) {
		if (null == odps) {
			throw new QanatBizException(ExceptionCodeConstants.OdpsOperate.DROP_ODPS_TABLE_ERROR, "下载数据失败");
		}
		List<List<String>> results = new ArrayList<List<String>>();
		
		TableTunnel tunnel = new TableTunnel(odps);
		PartitionSpec targetPartitionSpec = new PartitionSpec(partitionSpec);
		DownloadSession downloadSession;
		try {
			downloadSession = tunnel.createDownloadSession(project, tableName, targetPartitionSpec);
			long count = downloadSession.getRecordCount();
			callers = new ArrayList<>();
			long step;
			int downLoadthreadNum=10;
			if (count <= downLoadthreadNum) {
				step = count;
				downLoadthreadNum = 1;
			} else {
				step = count / downLoadthreadNum;
			}
			
			ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
		            .setNameFormat("opeerate-project-pool-%d").build();
		        //通用线程池
		    ExecutorService pool = new ThreadPoolExecutor(downLoadthreadNum, downLoadthreadNum,
		             0L, TimeUnit.MILLISECONDS,
		             new LinkedBlockingQueue<>(1024), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());
			for (int i = 0; i < downLoadthreadNum - 1; i++) {
				RecordReader recordReader = downloadSession.openRecordReader(step * i, step);
				callers.add(new DownloadThread(recordReader, downloadSession.getSchema()));
			}
			if (count > step * (downLoadthreadNum - 1)) {
				RecordReader recordReader = downloadSession.openRecordReader(step * (downLoadthreadNum - 1),
						count - ((downLoadthreadNum - 1) * step));
				callers.add(new DownloadThread(recordReader, downloadSession.getSchema()));
			}
			List<Future<List<List<String>>>> recordFutureList = pool.invokeAll(callers);
			for (Future<List<List<String>>> recordFuture : recordFutureList){
				results.addAll(recordFuture.get(1L, TimeUnit.HOURS));
			}
			pool.shutdown();
		} catch (Exception e) {
			log.error("downloadData error",e);
		}
		return results;
	}

	public List<OdpsColumnDTO> queryOdpsColumn(String tableName) {
		List<OdpsColumnDTO> resultList = new ArrayList<>();
		Map<String, String> typeResult = getTableColumnType(tableName);
		Map<String, String> commentResult = getTableColumnComment(tableName);
		if (null != typeResult && typeResult.size() > 0) {
			for (Map.Entry<String, String> entry : typeResult.entrySet()) {
				OdpsColumnDTO odpsColumnDTO = new OdpsColumnDTO();
				odpsColumnDTO.setColumnName(entry.getKey());
				odpsColumnDTO.setColumnType(entry.getValue());
				if (null != commentResult.get(entry.getKey())) {
					odpsColumnDTO.setColumnComment(commentResult.get(entry.getKey()));
				}
				resultList.add(odpsColumnDTO);
			}
		}
		return resultList;
	}
	
	public static class DownloadThread implements Callable<List<List<String>>> {
	    
	    private RecordReader recordReader;
	    private TableSchema tableSchema;

	    public DownloadThread(RecordReader recordReader, TableSchema tableSchema) {
	        this.recordReader = recordReader;
	        this.tableSchema = tableSchema;
	    }

	    /**
	     * 返回本次处理的记录集
	     */
	    @Override
	    public List<List<String>> call() {
	        List<List<String>> records = new ArrayList<List<String>>();
	        try {
	            Record record;
	            while ((record = recordReader.read()) != null) {
	                records.add(consumeRecord(record, tableSchema));
	            }
	            recordReader.close();
	        } catch (IOException e) {
	            log.error(e.getMessage(),e);
	        }
	        return records;
	    }

	    /**
	     * 行转换组合
	     * 
	     * @param record record
	     * @param schema schema
	     * @return 返回结果
	     */
	    private static List<String> consumeRecord(Record record, TableSchema schema) {
	        List<String> result = new ArrayList<>();
	        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	        for (int i = 0; i < schema.getColumns().size(); i++) {
	            Column column = schema.getColumn(i);
	            String colValue ;
	            switch (column.getTypeInfo().getOdpsType()) {
	            case BIGINT: {
	                Long v = record.getBigint(i);
	                colValue = v == null ? null : v.toString();
	                break;
	            }
	            case BOOLEAN: {
	                Boolean v = record.getBoolean(i);
	                colValue = v == null ? null : v.toString();
	                break;
	            }
	            case DATETIME: {
	                Date v = record.getDatetime(i);
	                colValue = v == null ? null : sdf.format(v);
	                break;
	            }
	            case DOUBLE: {
	                Double v = record.getDouble(i);
	                colValue = v == null ? null : v.toString();
	                break;
	            }
	            case STRING: {
	                String v = record.getString(i);
	                StringBuilder sb = new StringBuilder("\t");
	                sb.append(v);
	                colValue = v == null ? null : sb.toString();
	                break;
	            }
	            case DECIMAL: {
	                BigDecimal v = record.getDecimal(i);
	                colValue = v == null ? null : v.toString();
	                break;
	            }
	            default:
	                throw new QanatBizException("Unknown column type: " + column.getTypeInfo().getOdpsType());
	            }
	            result.add(colValue == null ? "" : colValue);
	        }
	        return result;
	    }
	}
}
