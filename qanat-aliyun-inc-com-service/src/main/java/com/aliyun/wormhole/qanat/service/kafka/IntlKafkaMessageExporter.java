package com.aliyun.wormhole.qanat.service.kafka;

import com.alibaba.fastjson.JSON;

import lombok.extern.slf4j.Slf4j;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.HostAndPort;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.Future;

@Slf4j
public class IntlKafkaMessageExporter {

    private KafkaProducer<String, String> producer;
    private String topic;
    private String bootstrapServers;

	public IntlKafkaMessageExporter(String topic, String bootstrapServers) {
		this.topic = topic;
		this.bootstrapServers = bootstrapServers;
		init();
	}

	private void init() {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 30 * 1000);
        
        Map<HostAndPort, HostAndPort> hostAndPortMap = new HashMap<>();
        /* hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("************", 37062));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("************", 37083));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("************", 37084));*/
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("************", 37062));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("************", 37083));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("************", 37084));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("************0", 5551));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("**************", 5493));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("************0", 4280));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("************0", 5007));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("************0", 5598));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("************0", 5744));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("************0", 5870));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("**************", 4773));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("**************", 5281));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("**************", 5464));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("**************", 5520));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("************0", 4350));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("************0", 4698));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("************0", 4929));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("************0", 5035));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("**************", 4097));
        hostAndPortMap.put(new HostAndPort("************", 9092), new HostAndPort("**************", 4454));
        producer = new KafkaProducer<String, String>(props, hostAndPortMap);
	}

    public void send(String traceId, String msg, String key) {
        log.info("send({},{},())", traceId, msg, key);
        try {
		    Map<String, Object> message = new HashMap<>();
		    message.put("traceId", traceId);
		    message.put("data", msg);
		    message.put("ts", System.currentTimeMillis());
            ProducerRecord<String, String>  kafkaMessage =  new ProducerRecord<String, String>(topic, key, JSON.toJSONString(message));
            Future<RecordMetadata> metadataFuture = producer.send(kafkaMessage);
            RecordMetadata recordMetadata = metadataFuture.get();
            log.info("export topic:{} msg[{}] with result[{}]", this.topic, msg, recordMetadata.toString());
        } catch (Exception e) {
            log.error("export failed", e);
        }
    }
}