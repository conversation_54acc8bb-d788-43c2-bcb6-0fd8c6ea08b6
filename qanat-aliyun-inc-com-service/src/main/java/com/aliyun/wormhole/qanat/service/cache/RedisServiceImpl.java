package com.aliyun.wormhole.qanat.service.cache;

import java.io.IOException;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.stereotype.Component;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.security.util.StringUtils;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.service.RedisService;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelation;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.ResourceExample;
import com.aliyun.wormhole.qanat.dal.mapper.AppResourceRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ResourceMapper;

import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPoolConfig;

@Slf4j
@Component
@HSFProvider(serviceInterface = RedisService.class)
public class RedisServiceImpl implements RedisService {
	
	@Resource
	private AppResourceRelationMapper appResourceRelationMapper;
	
	@Resource
	private ResourceMapper resourceMapper;

	@Override
	public String get(String tenantId, String appName, String key) {
	    log.info("get({},{},{}) start", tenantId, appName, key);
		JSONObject metaJson = getRedisConfByAppName(tenantId, appName);
		GenericObjectPoolConfig<Jedis> config = new GenericObjectPoolConfig<>();
		// 最大空闲连接数，需自行评估，不超过Redis实例的最大连接数
		config.setMaxIdle(metaJson.getInteger("maxIdle") != null ? metaJson.getInteger("maxIdle") : 200);
		// 最大连接数，需自行评估，不超过Redis实例的最大连接数。
		config.setMaxTotal(metaJson.getInteger("maxTotal") != null ? metaJson.getInteger("maxTotal") : 300);
		config.setTestOnBorrow(metaJson.getBoolean("testOnBorrow") != null ? metaJson.getBoolean("testOnBorrow") : false);
		config.setTestOnReturn(metaJson.getBoolean("testOnReturn") != null ? metaJson.getBoolean("testOnReturn") : false);

		JedisCluster cluster = null;
		try {
			if (StringUtils.isNotBlank(metaJson.getString("password"))) {
				cluster = new JedisCluster(new HostAndPort(metaJson.getString("host"), metaJson.getInteger("port")), metaJson.getInteger("connectionTimeout"), metaJson.getInteger("soTimeout"), metaJson.getInteger("maxAttempts"), metaJson.getString("password"), config);
			} else {
				cluster = new JedisCluster(new HostAndPort(metaJson.getString("host"), metaJson.getInteger("port")), metaJson.getInteger("connectionTimeout"), metaJson.getInteger("soTimeout"), metaJson.getInteger("maxAttempts"), config);
			}
		    long startTs = System.currentTimeMillis();
		    String value = cluster.get(key);
		    log.info("get({},{},{})={} with {}ms", tenantId, appName, key, value, (System.currentTimeMillis()-startTs));
		    return value;
		} catch (Exception e) {
			log.error("get({},{},{}) failed by error:{}", tenantId, appName, key, e.getMessage(), e);
		} finally {
		    if (cluster != null) {
		    	try {
					cluster.close();
				} catch (Exception e) {
					cluster = null;
				}
		    }
		}
		return null;
	}

	@Override
	public JSONObject getRedisConfByAppName(String tenantId, String appName) {
		AppResourceRelationExample example = new AppResourceRelationExample();
    	example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andRelationTypeEqualTo("redis");
    	List<AppResourceRelation> rels = appResourceRelationMapper.selectByExample(example);
    	if (CollectionUtils.isEmpty(rels)) {
    		throw new QanatBizException("no app[" + appName + "] resouce[redis] relation found");
    	}
    	AppResourceRelation ref = rels.get(0);
    	ResourceExample example1 = new ResourceExample();
    	example1.createCriteria().andResourceNameEqualTo(ref.getResourceName()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andResourceNameEqualTo(ref.getResourceName());
    	List<com.aliyun.wormhole.qanat.dal.domain.Resource> resources = resourceMapper.selectByExampleWithBLOBs(example1);
    	if (CollectionUtils.isEmpty(resources)) {
    		throw new QanatBizException("no redis resouce[" + ref.getResourceName() + "] found");
    	}
    	com.aliyun.wormhole.qanat.dal.domain.Resource resource = resources.get(0);
    	JSONObject metaJson = JSON.parseObject(resource.getMeta());
		return metaJson;
	}

}