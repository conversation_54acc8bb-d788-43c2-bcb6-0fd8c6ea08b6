package com.aliyun.wormhole.qanat.bpms;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.alibaba.alipmc.api.ProcessInstanceService;
import com.alibaba.alipmc.api.ProcessRecordService;
import com.alibaba.alipmc.api.model.bpm.OperatorRecord;
import com.alibaba.fastjson.JSON;

import com.aliyun.wormhole.qanat.api.dag.DagInstStatus;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstanceExample;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;

@Slf4j
public class BpmsProcInstListener implements ApplicationListener<BpmsProcInstEvent> {
    
    @Resource
    private ProcessInstanceService processInstanceService;

    @Resource
    private ProcessRecordService processRecordService;

    @Value("${qanat.bpms.appKey}")
    private String appKey;

    @Value("${qanat.bpms.authKey}")
    private String bpmsAuthKey;
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Override
    public void onApplicationEvent(BpmsProcInstEvent event) {
        log.info("onApplicationEvent({})", JSON.toJSONString(event));
        TaskInstanceExample example = new TaskInstanceExample();
        example.createCriteria().andTaskCommandEqualTo("BPMS:" + event.getProcessInstanceId())
        .andStatusEqualTo(DagInstStatus.EXECUTING.getCode().byteValue());
        List<TaskInstance> taskInstList = taskInstanceMapper.selectByExample(example);
        String modifyEmpid = appKey;
        if (taskInstList != null && taskInstList.size() > 0) {
            TaskInstance taskInst = taskInstList.get(0);
            DagInstStatus status = DagInstStatus.EXECUTING;
            if ("PROCESS_INSTANCE_TERMINATE".equals(event.getEventType())) {
                //取消
                status = DagInstStatus.FAILED;
            } else {
                status = DagInstStatus.SUCCESS;
            }
            
            List<OperatorRecord> opRecordList = processRecordService.getOperatorRecords(event.getProcessInstanceId(), bpmsAuthKey);
            if (CollectionUtils.isNotEmpty(opRecordList)) {
                OperatorRecord opRecord = opRecordList.get(opRecordList.size() - 1);
                modifyEmpid = opRecord.getOperator();
            }
            
            TaskInstance taskUpd = new TaskInstance();
            taskUpd.setId(taskInst.getId());
            taskUpd.setStatus(status.getCode().byteValue());
            taskUpd.setGmtModified(new Date());
            taskUpd.setModifyEmpid(modifyEmpid);
            taskInstanceMapper.updateByPrimaryKey(taskUpd);
        }
    }
}
