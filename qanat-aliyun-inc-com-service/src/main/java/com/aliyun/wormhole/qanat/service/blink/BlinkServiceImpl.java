package com.aliyun.wormhole.qanat.service.blink;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.shade.org.apache.commons.beanutils.BeanUtils;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.BlinkJobRequest;
import com.aliyun.wormhole.qanat.api.dto.ResourcePackage;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelation;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.Extension;
import com.aliyun.wormhole.qanat.dal.domain.ExtensionExample;
import com.aliyun.wormhole.qanat.dal.domain.ResourceExample;
import com.aliyun.wormhole.qanat.dal.mapper.AppResourceRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ExtensionMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ResourceMapper;
import com.aliyun.wormhole.qanat.service.blink.BlinkClient;
import com.aliyuncs.foas.model.v20181111.GetInstanceRunSummaryResponse;
import com.aliyuncs.foas.model.v20181111.ListJobResponse;
import com.aliyuncs.foas.model.v20181111.ListProjectBindQueueResourceResponse.Queue;
import com.aliyuncs.foas.model.v20181111.ListProjectBindQueueResponse;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * Blink操作服务类
 * <AUTHOR>
 * 2019年2月1日
 */
@Data
@Slf4j
@Component
@HSFProvider(serviceInterface = BlinkService.class)
public class BlinkServiceImpl implements BlinkService {
	
	@Resource
	private AppResourceRelationMapper appResourceRelationMapper;
	
	@Resource
	private ResourceMapper resourceMapper;
	
	@Resource
	private ExtensionMapper extensionMapper;
    
    @Override
    public Boolean createJob(BlinkJobRequest req) {
        log.debug("begin to createJob({})", req);
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(req.getTenantId(), req.getAppName()));
            req.setProperties("enable.project.config=true\r\nblink.job.timeZone=Asia/Shanghai\r\nblink.job.sls.log.level=info");
            return blinkClient.createJob(req);
        } catch (Exception e) {
            log.error("createJob({}) failed:{}", req.getJobName(), e.getMessage(), e);
            return false;
        }
    }
    
    private BlinkConf getBlinkConfByAppName(String tenantId, String appName) {
    	AppResourceRelationExample example = new AppResourceRelationExample();
    	example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andRelationTypeEqualTo("blink");
    	List<AppResourceRelation> rels = appResourceRelationMapper.selectByExample(example);
    	if (CollectionUtils.isEmpty(rels)) {
    		throw new QanatBizException("no app resouces");
    	}
    	AppResourceRelation ref = rels.get(0);
    	ResourceExample example1 = new ResourceExample();
    	example1.createCriteria().andResourceNameEqualTo(ref.getResourceName()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L);
    	List<com.aliyun.wormhole.qanat.dal.domain.Resource> resources = resourceMapper.selectByExampleWithBLOBs(example1);
    	if (CollectionUtils.isEmpty(resources)) {
    		throw new QanatBizException("no app resouces");
    	}
    	com.aliyun.wormhole.qanat.dal.domain.Resource resource = resources.get(0);
    	JSONObject metaJson = new JSONObject();
    	if (resource.getParentResourceId() != null) {
    		com.aliyun.wormhole.qanat.dal.domain.Resource parentResource = resourceMapper.selectByPrimaryKey(resource.getParentResourceId());
        	JSONObject parentMetaJson = JSON.parseObject(parentResource.getMeta());
        	metaJson.putAll(parentMetaJson);
    	}
    	metaJson.putAll(JSON.parseObject(resource.getMeta()));
    	BlinkConf conf = new BlinkConf();
    	conf.setAccessId(metaJson.getString("accessId"));
    	conf.setAccessKey(metaJson.getString("accessKey"));
    	conf.setClusterId(metaJson.getString("clusterId"));
    	conf.setEngineVersion(metaJson.getString("engineVersion"));
    	conf.setPopRegionId(metaJson.getString("popRegionId"));
    	conf.setProjectName(metaJson.getString("projectName"));
    	conf.setQueueName(metaJson.getString("queueName"));
    	conf.setRegionId(metaJson.getString("regionId"));
    	conf.setUserId(metaJson.getString("userId"));
    	conf.setProtocolType(metaJson.getString("protocolType"));
    	conf.setIsAutoScaleOn(metaJson.getBoolean("isAutoScaleOn"));
    	conf.setMaxCuPerJob(metaJson.getInteger("maxCuPerJob"));
    	conf.setFetchDelaySec(metaJson.getInteger("fetchDelaySec"));
		return conf;
	}

	/**
     * 启动任务
     * @param jobName
     * @param startTime
     */
    @Override
    public Long startJob(String tenantId, String appName, String jobName, Date startTime) {
    	return this.startJob(tenantId, appName, jobName, startTime, null);
    }

	/**
     * 启动任务
     * @param jobName
     * @param startTime
     */
    @Override
    public Long startJob(String tenantId, String appName, String jobName, Date startTime, Map<String, String> params) {
        log.debug("begin to startJob({},{},{},{})", tenantId, appName, jobName, startTime, JSON.toJSONString(params));
        Long instanceId = -1L;
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            return blinkClient.startJob(jobName, startTime, params);
        } catch (Exception e) {
            log.error("startJob({}) failed:{}", jobName, e.getMessage(), e);
        } 
        return instanceId;
    }

	/**
     * 启动批任务
     * @param jobName
     * @param startTime
     */
    @Override
    public Long startBatchJob(String tenantId, String appName, String jobName, Map<String, String> params) {
        log.debug("begin to startBatchJob({},{},{},{})", tenantId, appName, jobName, JSON.toJSONString(params));
        Long instanceId = -1L;
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            return blinkClient.startJob(jobName, null, params);
        } catch (Exception e) {
            log.error("startBatchJob({}) failed:{}", jobName, e.getMessage(), e);
        } 
        return instanceId;
    }

    @Override
    public String getInstanceActualState(String tenantId, String appName, String jobName, Long instanceId) {
        log.debug("begin to getInstanceActualState({}, {})", jobName, instanceId);
        GetInstanceRunSummaryResponse getInstanceRunSummaryResponse = getInstanceRunSummary(tenantId, appName, jobName, instanceId);
        return getInstanceRunSummaryResponse == null || getInstanceRunSummaryResponse.getRunSummary() == null ? "" : getInstanceRunSummaryResponse.getRunSummary().getActualState();
    }

    @Override
    public String getJobRunSummary(String tenantId, String appName, String jobName) {
    	GetInstanceRunSummaryResponse resp = getInstanceRunSummary(tenantId, appName, jobName, -1L);
    	return resp == null ? null : JSON.toJSONString(resp);
    }

    @Override
    public String getInstanceMetric(String tenantId, String appName, String jobName, String metricJson) {
    	BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
        return JSON.toJSONString(blinkClient.getInstanceMetric(jobName, metricJson));
    }

    @Override
    public List<String> getJobList(String tenantId, String appName) {
    	BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
    	List<ListJobResponse.Job> jobs = blinkClient.listJob();
        return jobs == null ? null : jobs.stream().map(e -> e.getJobName()).collect(Collectors.toList());
    }
    
    private GetInstanceRunSummaryResponse getInstanceRunSummary(String tenantId, String appName, String jobName, Long instanceId) {
        BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
        return blinkClient.getInstanceRunSummary(jobName, instanceId);
    }
    
    /**
     * 停止任务
     * @param jobName
     */
    @Override
    public void stopJob(String tenantId, String appName, String jobName) {
        log.debug("begin to stopJob({},{},{})", tenantId, appName, jobName);
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            blinkClient.stopJob(jobName);
        } catch (Exception e) {
            log.error("stopJob({}) failed:{}", jobName, e.getMessage(), e);
        }
        
    }

    @Override
    public Boolean commitJob(String tenantId, String appName, String jobName) {
        log.debug("begin to commitJob({},{},{})", tenantId, appName, jobName);
        try {
        	BlinkConf conf = getBlinkConfByAppName(tenantId, appName);
            BlinkClient blinkClient = new BlinkClient(conf);
            return blinkClient.commitJob(jobName, conf.getIsAutoScaleOn(), conf.getMaxCuPerJob(), conf.getFetchDelaySec());
        } catch (Exception e) {
            log.error("commitJob({}) failed:{}", jobName, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void restartJob(String tenantId, String appName, String jobName, Date startTime, Boolean isBatch) {
        this.stopJob(tenantId, appName, jobName);
        this.startJob(tenantId, appName, jobName, startTime);
    }

    @Override
    public void pauseJob(String tenantId, String appName, String jobName) {
        log.debug("begin to pauseJob({},{},{})", tenantId, appName, jobName);
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            blinkClient.pauseJob(jobName);
        } catch (Exception e) {
            log.error("pauseJob({}) failed:{}", jobName, e.getMessage(), e);
        }
    }

    @Override
    public void resumeJob(String tenantId, String appName, String jobName) {
        log.debug("begin to resumeJob({},{},{})", tenantId, appName, jobName);
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            blinkClient.resumeJob(jobName);
        } catch (Exception e) {
            log.error("pauseJob({}) failed:{}", jobName, e.getMessage(), e);
        }
    }

    @Override
    public String getJobExceptions(String tenantId, String appName, String jobName) {
        log.debug("getJobExceptions({},{},{})", tenantId, appName, jobName);
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            return blinkClient.getInstanceExceptions(jobName);
        } catch(Exception e) {
            log.error("getJobExceptions failed:{}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public void offlineJob(String tenantId, String appName, String jobName) {
        log.debug("offlineJob({},{},{})", tenantId, appName, jobName);
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            blinkClient.offlineJob(jobName);
        } catch(Exception e) {
            log.error("offlineJob failed:{}", e.getMessage(), e);
        }
    }

    @Override
    public void updateJob(String tenantId, String appName, String jobName, String sql, String planJson, String engineVersion, String properties) {
    	this.updateJob(tenantId, appName, jobName, sql, planJson, engineVersion, properties, null);
    }

    @Override
    public void updateJob(String tenantId, String appName, String jobName, String sql, String planJson, String engineVersion, String properties, String packages) {
        log.debug("updateJob({},{},{},{},{},{},{},{})", tenantId, appName, jobName, sql, planJson, engineVersion, properties, packages);
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            BlinkJobRequest updateJobRequest = new BlinkJobRequest();
            updateJobRequest.setJobName(jobName);
            if (StringUtils.isNotBlank(sql)) {
                updateJobRequest.setSql(sql);
            }
            if (StringUtils.isNotBlank(properties)) {
                updateJobRequest.setProperties(properties);
            }
            if (StringUtils.isNotBlank(engineVersion)) {
                updateJobRequest.setEngineVersion(engineVersion);
            }
            if (StringUtils.isNotBlank(planJson)) {
                updateJobRequest.setPlanJson(planJson);
            }
            if (StringUtils.isNotBlank(packages)) {
                updateJobRequest.setPackages(packages);
            }
            blinkClient.updateJob(updateJobRequest);
        } catch (Exception e) {
            log.error("updateJob({}) failed:{}", jobName, e.getMessage(), e);
        }
    }

    @Override
    public void modifyClusterQueue(String tenantId, String appName, String jobName, String clusterId, String queueName) {
        log.debug("modifyClusterQueue({},{},{},{},{})", tenantId, appName, jobName, clusterId, queueName);
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            BlinkJobRequest updateJobRequest = new BlinkJobRequest();
            updateJobRequest.setJobName(jobName);
            updateJobRequest.setClusterId(clusterId);
            updateJobRequest.setQueueName(queueName);
            blinkClient.updateJob(updateJobRequest);
        } catch (Exception e) {
            log.error("modifyClusterQueue({}) failed:{}", jobName, e.getMessage(), e);
        }
    }
    
    @Override
    public List<Map<String, Object>> listQueues(String tenantId, String appName, String clusterId, String queueName) {
        log.debug("ListProjectBindQueueResourceResource({},{},{},{})", tenantId, appName, clusterId, queueName);
    	List<Map<String, Object>> dataList = new ArrayList<>();
        try {
	    	BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
	    	List<Queue> queueList = blinkClient.listProjectBindQueueResource(clusterId, queueName);
	    	if (CollectionUtils.isNotEmpty(queueList)) {
	    		for (Queue obj : queueList) {
	    			try {
						Map<String, Object> data = BeanUtils.describe(obj);
						dataList.add(data);
					} catch (Exception e) {
					}
	    		}
	    	}
        } catch(Exception e) {
            log.error("ListProjectBindQueueResourceResource failed:{}", e.getMessage(), e);
        }
    	return dataList;
    }
    
    @Override
    public List<Map<String, Object>> getProjectCUs(String tenantId, String appName) {
        log.debug("getProjectCUs({},{},{},{})", tenantId, appName);
    	Map<String, Object> data = new HashMap<>();
        try {
	    	BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
	    	
	    	List<ListProjectBindQueueResponse.Queue> clusterQueueList = blinkClient.listProjectBindQueue();
	    	
	    	List<Queue> queueList = new ArrayList<>();
	    	for (ListProjectBindQueueResponse.Queue queue : clusterQueueList) {
	    		try {
	    			queueList.addAll(blinkClient.listProjectBindQueueResource(queue.getClusterId(), queue.getQueueName()));
	    		} catch (Exception e) {}
	    	}
	    	List<Map<String, Object>> dataList = new ArrayList<>();
	    	for (Queue queue : queueList) {
	    		dataList.add(BeanUtils.describe(queue));
	    	}
	    	return dataList;
        } catch(Exception e) {
            log.error("getProjectCUs failed:{}", e.getMessage(), e);
        }
    	return null;
    }
    
    @Override
    public String listProjectBindQueue(String tenantId, String appName) {
        log.debug("getProjectCUs({},{},{},{})", tenantId, appName);
        try {
	    	BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
	    	List<ListProjectBindQueueResponse.Queue> queueList = blinkClient.listProjectBindQueue();
	    	return JSON.toJSONString(queueList);
        } catch(Exception e) {
            log.error("getProjectCUs failed:{}", e.getMessage(), e);
        }
    	return null;
    }
    
    @Override
    public Boolean validateJob(String tenantId, String appName, String jobName) {
        log.debug("validateJob({},{},{})", tenantId, appName, jobName);
        try {
	    	BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
	    	return blinkClient.validateJob(jobName);
        } catch(Exception e) {
            log.error("validateJob failed:{}", e.getMessage(), e);
        }
    	return false;
    }
    
    @Override
    public void deleteJob(String tenantId, String appName, String jobName) {
        log.debug("deleteJob({},{},{})", tenantId, appName, jobName);
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            blinkClient.deleteJob(jobName);
        } catch(Exception e) {
            log.error("deleteJob failed:{}", e.getMessage(), e);
        }
    }
    
    @Override
    public boolean isJobExists(String tenantId, String appName, String jobName) {
        log.debug("isJobExists({},{},{})", tenantId, appName, jobName);
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            return blinkClient.isJobExists(jobName);
        } catch(Exception e) {
            log.error("isJobExists failed:{}", e.getMessage(), e);
        }
        return false;
    }
    
    @Override
    public boolean isJobCommitted(String tenantId, String appName, String jobName) {
        log.debug("isJobCommitted({},{},{})", tenantId, appName, jobName);
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            return blinkClient.getJob(jobName).getIsCommitted();
        } catch(Exception e) {
            log.error("isJobCommitted failed:{}", e.getMessage(), e);
        }
        return false;
    }
    
    @Override
    public String getPlanJson(String tenantId, String appName, String jobName, Integer expectedCUs, Boolean isAutoconfEnable) {
    	return this.getPlanJson(tenantId, appName, jobName, expectedCUs, isAutoconfEnable, null);
    }
    
    private String getPlanJson(String tenantId, String appName, String jobName, Integer expectedCUs, Boolean isAutoconfEnable, Integer parallel) {
        log.debug("getPlanJson({},{},{},{},{})", tenantId, appName, jobName, expectedCUs, isAutoconfEnable);
        int retries = 10;
        while (retries > 0) {
	        try {
	            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
	            String planJsonStr = blinkClient.getPlanJson(jobName.toLowerCase(), expectedCUs, isAutoconfEnable);
	            
	            JSONObject planJson = JSON.parseObject(planJsonStr);
	            JSONArray linksArray = planJson.getJSONArray("links");
	            if (linksArray != null && linksArray.size() > 0) {
	            	for (int i = 0; i < linksArray.size(); i++) {
	            		JSONObject linkJson = linksArray.getJSONObject(i);
	            		if (linkJson.containsKey("ship_strategy")) {
	            			linkJson.remove("ship_strategy");
	            		}
	            	}
	            }

	            if (parallel != null && parallel > 0) {
		            JSONArray nodesArray = planJson.getJSONArray("nodes");
		            if (nodesArray != null && nodesArray.size() > 0) {
		            	for (int i = 0; i < nodesArray.size(); i++) {
		            		JSONObject nodeJson = nodesArray.getJSONObject(i);
		            		if (nodeJson.containsKey("parallelism")) {
		            			nodeJson.put("parallelism", parallel);
		            		}
		            	}
		            }
	            }
	            
	            log.info("job:{} planjson:{}", jobName, planJson.toJSONString());
	            return planJson.toJSONString();
	        } catch(Exception e) {
	            log.error("getPlanJson failed:{}", e.getMessage(), e);
	            try {
	            	Thread.sleep(1000);
	            }catch(Exception ex) {}
	            retries--;
	        }
        }
        return null;
    }

    @Override
    public Boolean recommitBlinkJob(String tenantId, String appName, String jobName) {
    	try {
			//删除即存同名任务(stop -> offline - > delete)
	    	if (!isJobExists(tenantId, appName, jobName)) {
	    		throw new QanatBizException("job:" + jobName + " does not exists");
	    	}
	        String planJson = getPlanJson(tenantId, appName, jobName, null, false);
	        log.debug("job:{} planjson={}", jobName, planJson);    
	        updateJob(tenantId, appName, jobName, null, planJson, null, null);
	        log.debug("update blink job planjson finished");
	        
	        // 提交job
	        boolean flag = commitJob(tenantId, appName, jobName);
	        if (!flag) {
	            throw new QanatBizException("blink job:" + jobName + " commit failed");
	        }
	        log.debug("blink job[{}] is committed", jobName);
	        return true;
    	} catch (Exception e) {
    		log.error("recommit failed:{}", e.getMessage(), e);
    		return false;
    	}
    }

    @Override
    public Map<String, List<String>> recommitBlinkJobs(String tenantId, String appName, String jobNames) {
    	Map<String, List<String>> resultMap = new HashMap<>();
    	List<String> successList = new ArrayList<>();
    	resultMap.put("success", successList);
    	List<String> failList = new ArrayList<>();
    	resultMap.put("fail", failList);
    	String[] jobNameArray = jobNames.split(",");
    	for (String jobName : jobNameArray) {
	    	if (recommitBlinkJob(tenantId, appName, jobName)) {
		        log.info("job[{}] is recommitted", jobName);
		        resultMap.get("success").add(jobName);
	    	} else {
	    		log.error("job:{} recommit failed", jobName);
		        resultMap.get("fail").add(jobName);
	    	}
    	}
    	return resultMap;
    }

    @Override
    public Map<String, List<String>> restartJobs(String tenantId, String appName, String jobNames, Integer retrieveSeconds) {
    	retrieveSeconds = retrieveSeconds == null ? 60 : retrieveSeconds;
    	Map<String, List<String>> resultMap = new HashMap<>();
    	List<String> successList = new ArrayList<>();
    	resultMap.put("success", successList);
    	List<String> failList = new ArrayList<>();
    	resultMap.put("fail", failList);
    	String[] jobNameArray = jobNames.split(",");
    	for (String jobName : jobNameArray) {
	    	try {
	    		Calendar cal = Calendar.getInstance();
	    		cal.add(Calendar.SECOND, -1*retrieveSeconds);
	    		this.restartJob(tenantId, appName, jobName, cal.getTime(), false);
		        log.info("job[{}] is restarted", jobName);
		        resultMap.get("success").add(jobName);
	    	} catch (Exception e) {
	    		log.error("job:{} restart failed:{}", jobName, e.getMessage(), e);
		        resultMap.get("fail").add(jobName);
	    	}
    	}
    	return resultMap;
    }

    @Override
    public Boolean rebuildBlinkJob(String tenantId, String appName, String jobName, Date startTime) {
    	startTime = (startTime == null) ? new Date() : startTime;
    	try {
			//删除即存同名任务(stop -> offline - > delete)
	    	if (!isJobExists(tenantId, appName, jobName)) {
	    		throw new QanatBizException("job:" + jobName + " does not exists");
	    	}
	        stopJob(tenantId, appName, jobName);
	        log.debug("job:{} is stopped", jobName);
	        String planJson = getPlanJson(tenantId, appName, jobName, null, false);
	        log.debug("job:{} planjson={}", jobName, planJson);    
	        updateJob(tenantId, appName, jobName, null, planJson, null, null);
	        log.debug("update blink job planjson finished");
	        
	        // 提交job
	        boolean flag = commitJob(tenantId, appName, jobName);
	        if (!flag) {
	            throw new QanatBizException("blink job:" + jobName + " commit failed");
	        }
	        log.debug("blink job[{}] is committed", jobName);
	        this.startJob(tenantId, appName, jobName, startTime);
	        log.debug("blink job[{}] is started", jobName);
	        return true;
    	} catch (Exception e) {
    		log.error("rebuild failed:{}", e.getMessage(), e);
    		return false;
    	}
    }

    @Override
	public void buildBlinkJob(String tenantId, String appName, String jobName, String sql, String folderName, String packages, Integer parallel)
			throws QanatBizException {
    	String planJson  = getPlanJson(tenantId, appName, jobName, null, false, parallel);
    	this.buildBlinkJob(tenantId, appName, jobName, sql, folderName, packages, false, planJson);
    }

    @Override
	public void buildBlinkJob(String tenantId, String appName, String jobName, String sql, String folderName, String packages, boolean isBatch)
			throws QanatBizException {
    	this.buildBlinkJob(tenantId, appName, jobName, sql, folderName, packages, isBatch, null);
    }

    @Override
	public void buildBlinkJob(String tenantId, String appName, String jobName, String sql, String folderName, String packages, boolean isBatch, String planJson)
			throws QanatBizException {
    	new Thread(()->{
    		if (isJobExists(tenantId, appName, jobName)) {
		        updateJob(tenantId, appName, jobName, sql, null, null, null, packages);
		        log.debug("update blink job:{} planjson finished", jobName);
		        updateJob(tenantId, appName, jobName, null, StringUtils.isBlank(planJson) ? getPlanJson(tenantId, appName, jobName, null, false) : planJson, null, null);
    		} else {
		        //创建任务
		        BlinkJobRequest req = new BlinkJobRequest();
		        req.setTenantId(tenantId);
		        req.setAppName(appName);
		        req.setJobName(jobName);
		        req.setSql(sql);
		        req.setFolderName(folderName);
		        req.setPackages(packages);
		        if (isBatch) {
		        	req.setIsBatch(true);
		        }
		        boolean flag = createJob(req);
		        if (!flag) {
		            throw new QanatBizException("blink job:" + jobName + " create failed");
		        }
		        log.debug("create blink job[{}] finished", jobName);
		        
		        log.debug("job:{} getPlanJson finished", jobName);    
		        updateJob(tenantId, appName, jobName, null, StringUtils.isBlank(planJson) ? getPlanJson(tenantId, appName, jobName, null, false) : planJson, null, null);
		        log.debug("update blink job:{} planjson finished", jobName);
    		}
	        
	        // 提交job
	        boolean flag = commitJob(tenantId, appName, jobName);
	        if (!flag) {
	            throw new QanatBizException("blink job:" + jobName + " commit failed");
	        }
	        log.debug("commit blink job[{}] finished", jobName);
    	}).start();
	}

    @Override
    public void dropExistedJob(String tenantId, String appName, String jobName) {
        if (isJobExists(tenantId, appName, jobName)) {
            stopJob(tenantId, appName, jobName);
            log.debug("job:{} is stopped", jobName);
            offlineJob(tenantId, appName, jobName);
            log.debug("job:{} is offlined", jobName);
            deleteJob(tenantId, appName, jobName);
            log.debug("job:{} is deleted", jobName);
        }
    }

    @Override
	public String getBlinkExtensionsByPackage(String tenantId, ResourcePackage ... packages) {
		List<String> packageCodes = Arrays.asList(packages).stream().map(item -> item.toString()).collect(Collectors.toList());
		ExtensionExample example = new ExtensionExample();
		example.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andTypeEqualTo("blink").andCodeIn(packageCodes);
		List<Extension> extensions = extensionMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(extensions)) {
			throw new QanatBizException("no extension found");
		}
		List<String> packageList =  extensions.stream().map(item -> item.getVersion()).collect(Collectors.toList());
		return StringUtils.join(packageList, ",");
	}

    @Override
	public Map<String, Object> getInstanceResource(String tenantId, String appName, String jobName) {
        log.debug("getInstanceResource({},{},{})", tenantId, appName, jobName);
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            return BeanUtils.describe(blinkClient.getInstanceResource(jobName));
        } catch(Exception e) {
            log.error("getInstanceResource failed:{}", e.getMessage(), e);
        }
        return null;
	}

    @Override
	public String getInstanceDetail(String tenantId, String appName, String jobName) {
        log.debug("getInstanceDetail({},{},{})", tenantId, appName, jobName);
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            return blinkClient.getInstanceDetail(jobName);
        } catch(Exception e) {
            log.error("getInstanceDetail failed:{}", e.getMessage(), e);
        }
        return null;
	}

    @Override
	public String getInstanceConfig(String tenantId, String appName, String jobName) {
        log.debug("getInstanceConfig({},{},{})", tenantId, appName, jobName);
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            return blinkClient.getInstanceConfig(jobName);
        } catch(Exception e) {
            log.error("getInstanceConfig failed:{}", e.getMessage(), e);
        }
        return null;
	}

    @Override
	public String getJobScript(String tenantId, String appName, String jobName) {
        log.debug("getJobScript({},{},{})", tenantId, appName, jobName);
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            return blinkClient.getJob(jobName).getCode();
        } catch(Exception e) {
            log.error("getJobScript failed:{}", e.getMessage(), e);
        }
        return null;
	}

    @Override
	public String getJobDetail(String tenantId, String appName, String jobName) {
        log.debug("getJobDetail({},{},{})", tenantId, appName, jobName);
        try {
            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            return JSON.toJSONString(blinkClient.getJob(jobName));
        } catch(Exception e) {
            log.error("getJobDetail failed:{}", e.getMessage(), e);
        }
        return null;
	}
    
    @Override
    public String getBatchPlanJson4DwSource2(String tableName, String pk, int paralism, int dstDbNum) {
    	JSONObject planJson = new JSONObject();
    	
    	JSONArray globalJsonArray = new JSONArray();
    	planJson.put("global", globalJsonArray);
    	JSONObject globalJson = new JSONObject();
    	globalJsonArray.add(globalJson);
    	globalJson.put("jobManagerMinCpuCores", 0.1);
    	globalJson.put("jobManagerMinMemoryCores", 1024);
    	globalJson.put("jobManagerCpuCores", 0.25);
    	globalJson.put("jobManagerMemoryInMB", 1024);
    	
    	JSONArray linksJsonArray = new JSONArray();
    	planJson.put("links", linksJsonArray);

    	JSONArray nodesJsonArray = new JSONArray();
    	planJson.put("nodes", nodesJsonArray);
    	
    	for (int i = 1; i <= paralism; i++) {
        	JSONObject sourceJson = new JSONObject();
        	sourceJson.put("id", i);
        	sourceJson.put("uid", i + "");
        	sourceJson.put("name", "MySQLScanTableSource-" + tableName + "-Batch");
        	sourceJson.put("pact", "Source");
        	sourceJson.put("chainingStrategy", "HEAD");
        	sourceJson.put("parallelism", 1);
        	sourceJson.put("maxParallelism", 1);
        	sourceJson.put("vcore", 1);
        	sourceJson.put("heap_memory", 4096);
        	nodesJsonArray.add(sourceJson);
        	
        	JSONObject sourceConversionJson = new JSONObject();
        	sourceConversionJson.put("id", paralism + ((i - 1) * 4 + 1));
        	sourceConversionJson.put("uid", (paralism + ((i - 1) * 4 + 1)) + "");
        	sourceConversionJson.put("name", "SourceConversion(table:[builtin, default, _DataStreamTable_0, source: [MySQLScanTableSource-" + tableName + "]], fields:(f0))");
        	sourceConversionJson.put("pact", "Operator");
        	sourceConversionJson.put("parallelism", 1);
        	sourceConversionJson.put("maxParallelism", 32768);
        	sourceConversionJson.put("vcore", 1);
        	sourceConversionJson.put("heap_memory", 4096);
        	nodesJsonArray.add(sourceConversionJson);
      	
        	JSONObject correlateJson = new JSONObject();
        	correlateJson.put("id", paralism + ((i - 1) * 4 + 2));
        	correlateJson.put("uid", (paralism + ((i - 1) * 4 + 2)) + "");
        	correlateJson.put("name", "correlate: table(MySQLSourceParser2($cor2.f0)), select: " + pk + ",");
        	correlateJson.put("pact", "Operator");
        	correlateJson.put("parallelism", 1);
        	correlateJson.put("maxParallelism", 32768);
        	correlateJson.put("vcore", 1);
        	correlateJson.put("heap_memory", 4096);
        	nodesJsonArray.add(correlateJson);
      	
        	for (int j = 0; j < dstDbNum; j++) {
	        	JSONObject sinkConversionJson = new JSONObject();
	        	sinkConversionJson.put("id", paralism + ((i - 1) * 4 + 3 + j));
	        	sinkConversionJson.put("uid", (paralism + ((i - 1) * 4 + 3 + j)) + "");
	        	sinkConversionJson.put("name", "SinkConversion to Tuple2 " + j);
	        	sinkConversionJson.put("pact", "Operator");
	        	sinkConversionJson.put("parallelism", 32);
	        	sinkConversionJson.put("maxParallelism", 32768);
	        	sinkConversionJson.put("vcore", 1);
	        	sinkConversionJson.put("heap_memory", 4096);
	        	nodesJsonArray.add(sinkConversionJson);
	      	
	        	JSONObject sinkJson = new JSONObject();
	        	sinkJson.put("id", paralism + ((i - 1) * 4 + 4 + j));
	        	sinkJson.put("uid", (paralism + ((i - 1) * 4 + 4 + j)) + "");
	        	sinkJson.put("name", "TupleOutputFormatAdapterSink:com.alibaba.blink.connectors.adb30.QanatAdb30OutputFormat@1958524b " + j);
	        	sinkJson.put("pact", "Sink");
	        	sinkJson.put("parallelism", 32);
	        	sinkJson.put("maxParallelism", 32768);
	        	sinkJson.put("vcore", 1);
	        	sinkJson.put("heap_memory", 4096);
	        	nodesJsonArray.add(sinkJson);
        	}
        	
        	JSONObject linkJson = new JSONObject();
        	linkJson.put("source", i);
        	linkJson.put("target", paralism + ((i - 1) * 4 + 1));
        	linksJsonArray.add(linkJson);

        	linkJson = new JSONObject();
        	linkJson.put("source", paralism + ((i - 1) * 4 + 1));
        	linkJson.put("target", paralism + ((i - 1) * 4 + 2));
        	linksJsonArray.add(linkJson);

        	for (int j = 0; j < dstDbNum; j++) {
	        	linkJson = new JSONObject();
	        	linkJson.put("source", paralism + ((i - 1) * 4 + 2));
	        	linkJson.put("target", paralism + ((i - 1) * 4 + 3 + j));
	        	linksJsonArray.add(linkJson);
	        	
	        	linkJson = new JSONObject();
	        	linkJson.put("source", paralism + ((i - 1) * 4 + 3));
	        	linkJson.put("target", paralism + ((i - 1) * 4 + 4 + j));
	        	linksJsonArray.add(linkJson);
        	}
    	}
    	return planJson.toJSONString();
    }

    @Override
    public String getBatchPlanJson4DwSource(String tableName, String pk, int paralism, int dstDbNum) {
    	JSONObject planJson = new JSONObject();
    	
    	JSONArray globalJsonArray = new JSONArray();
    	planJson.put("global", globalJsonArray);
    	JSONObject globalJson = new JSONObject();
    	globalJsonArray.add(globalJson);
    	globalJson.put("jobManagerMinCpuCores", 0.1);
    	globalJson.put("jobManagerMinMemoryCores", 1024);
    	globalJson.put("jobManagerCpuCores", 0.25);
    	globalJson.put("jobManagerMemoryInMB", 1024);
    	
    	JSONArray linksJsonArray = new JSONArray();
    	planJson.put("links", linksJsonArray);

    	JSONArray nodesJsonArray = new JSONArray();
    	planJson.put("nodes", nodesJsonArray);
    	
    	for (int i = 1; i <= paralism; i++) {
        	JSONObject sourceJson = new JSONObject();
        	sourceJson.put("id", i);
        	sourceJson.put("uid", i + "");
        	sourceJson.put("name", "MySQLScanTableSource-" + tableName + "-Batch");
        	sourceJson.put("pact", "Source");
        	sourceJson.put("chainingStrategy", "HEAD");
        	sourceJson.put("parallelism", 1);
        	sourceJson.put("maxParallelism", 1);
        	sourceJson.put("vcore", 1);
        	sourceJson.put("heap_memory", 4096);
        	nodesJsonArray.add(sourceJson);
        	
        	JSONObject sourceConversionJson = new JSONObject();
        	sourceConversionJson.put("id", paralism + ((i - 1) * (2 + 2*dstDbNum) + 1));
        	sourceConversionJson.put("uid", (paralism + ((i - 1) * (2 + 2*dstDbNum) + 1)) + "");
        	sourceConversionJson.put("name", "SourceConversion(table:[builtin, default, _DataStreamTable_0, source: [MySQLScanTableSource-" + tableName + "]], fields:(f0))");
        	sourceConversionJson.put("pact", "Operator");
        	sourceConversionJson.put("parallelism", 1);
        	sourceConversionJson.put("maxParallelism", 32768);
        	sourceConversionJson.put("vcore", 1);
        	sourceConversionJson.put("heap_memory", 4096);
        	nodesJsonArray.add(sourceConversionJson);
      	
        	JSONObject correlateJson = new JSONObject();
        	correlateJson.put("id", paralism + ((i - 1) * (2 + 2*dstDbNum) + 2));
        	correlateJson.put("uid", (paralism + ((i - 1) * (2 + 2*dstDbNum) + 2)) + "");
        	correlateJson.put("name", "correlate: table(MySQLSourceParser2($cor2.f0)), select: " + pk + ",");
        	correlateJson.put("pact", "Operator");
        	correlateJson.put("parallelism", 1);
        	correlateJson.put("maxParallelism", 32768);
        	correlateJson.put("vcore", 1);
        	correlateJson.put("heap_memory", 4096);
        	nodesJsonArray.add(correlateJson);
      	
        	for (int j = 0; j < dstDbNum; j++) {
	        	JSONObject sinkConversionJson = new JSONObject();
	        	sinkConversionJson.put("id", paralism + ((i - 1) * (2 + 2*dstDbNum) + 3 + j*2));
	        	sinkConversionJson.put("uid", (paralism + ((i - 1) * (2 + 2*dstDbNum) + 3 + j*2)) + "");
	        	sinkConversionJson.put("name", "SinkConversion to Tuple2 " + j);
	        	sinkConversionJson.put("pact", "Operator");
	        	sinkConversionJson.put("parallelism", 32);
	        	sinkConversionJson.put("maxParallelism", 32768);
	        	sinkConversionJson.put("vcore", 1);
	        	sinkConversionJson.put("heap_memory", 4096);
	        	nodesJsonArray.add(sinkConversionJson);
	      	
	        	JSONObject sinkJson = new JSONObject();
	        	sinkJson.put("id", paralism + ((i - 1) * (2 + 2*dstDbNum) + 4 + j*2));
	        	sinkJson.put("uid", (paralism + ((i - 1) * (2 + 2*dstDbNum) + 4 + j*2)) + "");
	        	sinkJson.put("name", "TupleOutputFormatAdapterSink:com.alibaba.blink.connectors.adb30.QanatAdb30OutputFormat@1958524b " + j);
	        	sinkJson.put("pact", "Sink");
	        	sinkJson.put("parallelism", 32);
	        	sinkJson.put("maxParallelism", 32768);
	        	sinkJson.put("vcore", 1);
	        	sinkJson.put("heap_memory", 4096);
	        	nodesJsonArray.add(sinkJson);
        	}
        	
        	JSONObject linkJson = new JSONObject();
        	linkJson.put("source", i);
        	linkJson.put("target", paralism + ((i - 1) * (2 + 2*dstDbNum) + 1));
        	linksJsonArray.add(linkJson);

        	linkJson = new JSONObject();
        	linkJson.put("source", paralism + ((i - 1) * (2 + 2*dstDbNum) + 1));
        	linkJson.put("target", paralism + ((i - 1) * (2 + 2*dstDbNum) + 2));
        	linksJsonArray.add(linkJson);

        	for (int j = 0; j < dstDbNum; j++) {
	        	linkJson = new JSONObject();
	        	linkJson.put("source", paralism + ((i - 1) * (2 + 2*dstDbNum) + 2));
	        	linkJson.put("target", paralism + ((i - 1) * (2 + 2*dstDbNum) + 3 + j*2));
	        	linksJsonArray.add(linkJson);
	        	
	        	linkJson = new JSONObject();
	        	linkJson.put("source", paralism + ((i - 1) * (2 + 2*dstDbNum) + 3 + j*2));
	        	linkJson.put("target", paralism + ((i - 1) * (2 + 2*dstDbNum) + 4 + j*2));
	        	linksJsonArray.add(linkJson);
        	}
    	}
    	return planJson.toJSONString();
    }

    @Override
    public String getBatchPlanJson4DwSource(String tableName, String pk, int dstDbNum) {
    	JSONObject planJson = new JSONObject();
    	
    	JSONArray globalJsonArray = new JSONArray();
    	planJson.put("global", globalJsonArray);
    	JSONObject globalJson = new JSONObject();
    	globalJsonArray.add(globalJson);
    	globalJson.put("jobManagerMinCpuCores", 0.1);
    	globalJson.put("jobManagerMinMemoryCores", 1024);
    	globalJson.put("jobManagerCpuCores", 0.25);
    	globalJson.put("jobManagerMemoryInMB", 1024);
    	
    	JSONArray linksJsonArray = new JSONArray();
    	planJson.put("links", linksJsonArray);

    	JSONArray nodesJsonArray = new JSONArray();
    	planJson.put("nodes", nodesJsonArray);
    	
    	
    	JSONObject sourceJson = new JSONObject();
    	sourceJson.put("id", 1);
    	sourceJson.put("uid", "1");
    	sourceJson.put("name", "MySQLScanTableSource-" + tableName + "-Batch");
    	sourceJson.put("pact", "Source");
    	sourceJson.put("chainingStrategy", "HEAD");
    	sourceJson.put("parallelism", 1);
    	sourceJson.put("maxParallelism", 1);
    	sourceJson.put("vcore", 1);
    	sourceJson.put("heap_memory", 4096);
    	nodesJsonArray.add(sourceJson);
    	
    	JSONObject sourceConversionJson = new JSONObject();
    	sourceConversionJson.put("id", 2);
    	sourceConversionJson.put("uid", "2");
    	sourceConversionJson.put("name", "SourceConversion(table:[builtin, default, _DataStreamTable_0, source: [MySQLScanTableSource-" + tableName + "]], fields:(f0))");
    	sourceConversionJson.put("pact", "Operator");
    	sourceConversionJson.put("parallelism", 1);
    	sourceConversionJson.put("maxParallelism", 32768);
    	sourceConversionJson.put("vcore", 1);
    	sourceConversionJson.put("heap_memory", 4096);
    	nodesJsonArray.add(sourceConversionJson);
  	
    	JSONObject correlateJson = new JSONObject();
    	correlateJson.put("id", 3);
    	correlateJson.put("uid", "3");
    	correlateJson.put("name", "correlate: table(MySQLSourceParser2($cor2.f0)), select: " + pk + ",");
    	correlateJson.put("pact", "Operator");
    	correlateJson.put("parallelism", 1);
    	correlateJson.put("maxParallelism", 32768);
    	correlateJson.put("vcore", 1);
    	correlateJson.put("heap_memory", 4096);
    	nodesJsonArray.add(correlateJson);
  	
    	for (int j = 0; j < dstDbNum; j++) {
        	JSONObject sinkConversionJson = new JSONObject();
        	sinkConversionJson.put("id", 4 + j*2);
        	sinkConversionJson.put("uid", (4 + j*2) + "");
        	sinkConversionJson.put("name", "SinkConversion to Tuple2 " + j);
        	sinkConversionJson.put("pact", "Operator");
        	sinkConversionJson.put("parallelism", 32);
        	sinkConversionJson.put("maxParallelism", 32768);
        	sinkConversionJson.put("vcore", 1);
        	sinkConversionJson.put("heap_memory", 4096);
        	nodesJsonArray.add(sinkConversionJson);
      	
        	JSONObject sinkJson = new JSONObject();
        	sinkJson.put("id", 5 + j*2);
        	sinkJson.put("uid", (5 + j*2) + "");
        	sinkJson.put("name", "TupleOutputFormatAdapterSink:com.alibaba.blink.connectors.adb30.QanatAdb30OutputFormat@1958524b " + j);
        	sinkJson.put("pact", "Sink");
        	sinkJson.put("parallelism", 32);
        	sinkJson.put("maxParallelism", 32768);
        	sinkJson.put("vcore", 1);
        	sinkJson.put("heap_memory", 4096);
        	nodesJsonArray.add(sinkJson);
    	}
    	
    	JSONObject linkJson = new JSONObject();
    	linkJson.put("source", 1);
    	linkJson.put("target", 2);
    	linksJsonArray.add(linkJson);

    	linkJson = new JSONObject();
    	linkJson.put("source", 2);
    	linkJson.put("target", 3);
    	linksJsonArray.add(linkJson);

    	for (int j = 0; j < dstDbNum; j++) {
        	linkJson = new JSONObject();
        	linkJson.put("source", 3);
        	linkJson.put("target", 4 + j * 2);
        	linksJsonArray.add(linkJson);
        	
        	linkJson = new JSONObject();
        	linkJson.put("source", 4 + j * 2);
        	linkJson.put("target", 5 + j * 2);
        	linksJsonArray.add(linkJson);
    	}
    	return planJson.toJSONString();
    }

    @Override
    public String getBatchPlanJson4OdpsSource(String tableName, String pk, int dstDbNum) {
    	JSONObject planJson = new JSONObject();
    	
    	JSONArray globalJsonArray = new JSONArray();
    	planJson.put("global", globalJsonArray);
    	JSONObject globalJson = new JSONObject();
    	globalJsonArray.add(globalJson);
    	globalJson.put("jobManagerMinCpuCores", 0.1);
    	globalJson.put("jobManagerMinMemoryCores", 1024);
    	globalJson.put("jobManagerCpuCores", 0.25);
    	globalJson.put("jobManagerMemoryInMB", 1024);
    	
    	JSONArray linksJsonArray = new JSONArray();
    	planJson.put("links", linksJsonArray);

    	JSONArray nodesJsonArray = new JSONArray();
    	planJson.put("nodes", nodesJsonArray);
    	
    	JSONObject sourceJson = new JSONObject();
    	sourceJson.put("id", 3);
    	sourceJson.put("uid", "3");
    	sourceJson.put("name", "ODPSSource-" + tableName + "-Batch");
    	sourceJson.put("pact", "Source");
    	sourceJson.put("chainingStrategy", "HEAD");
    	sourceJson.put("parallelism", 32);
    	sourceJson.put("maxParallelism", 32768);
    	sourceJson.put("vcore", 1);
    	sourceJson.put("heap_memory", 4096);
    	sourceJson.put("direct_memory", 0);
    	sourceJson.put("native_memory", 0);
    	nodesJsonArray.add(sourceJson);
    	
    	JSONObject sourceConversionJson = new JSONObject();
    	sourceConversionJson.put("id", 4);
    	sourceConversionJson.put("uid", "4");
    	sourceConversionJson.put("name", "Calc(select:" + pk + ")");
    	sourceConversionJson.put("pact", "Operator");
    	sourceConversionJson.put("parallelism", 32);
    	sourceConversionJson.put("maxParallelism", 32768);
    	sourceConversionJson.put("vcore", 1);
    	sourceConversionJson.put("heap_memory", 4096);
    	sourceConversionJson.put("direct_memory", 0);
    	sourceConversionJson.put("native_memory", 0);
    	nodesJsonArray.add(sourceConversionJson);
  	
    	for (int j = 0; j < dstDbNum; j++) {
        	JSONObject sinkConversionJson = new JSONObject();
        	sinkConversionJson.put("id", 5 + j * 2);
        	sinkConversionJson.put("uid", 5 + j * 2 + "");
        	sinkConversionJson.put("name", "SinkConversion to Tuple2 " + j);
        	sinkConversionJson.put("pact", "Operator");
        	sinkConversionJson.put("parallelism", 32);
        	sinkConversionJson.put("maxParallelism", 32768);
        	sinkConversionJson.put("vcore", 1);
        	sinkConversionJson.put("heap_memory", 4096);
        	sinkConversionJson.put("direct_memory", 0);
            sinkConversionJson.put("native_memory", 0);
        	nodesJsonArray.add(sinkConversionJson);
      	
        	JSONObject sinkJson = new JSONObject();
        	sinkJson.put("id", 6 + j * 2);
        	sinkJson.put("uid", 6 + j * 2);
        	sinkJson.put("name", "TupleOutputFormatAdapterSink:com.alibaba.blink.connectors.adb30.QanatAdb30OutputFormat@1958524b " + j);
        	sinkJson.put("pact", "Sink");
        	sinkJson.put("parallelism", 32);
        	sinkJson.put("maxParallelism", 32768);
        	sinkJson.put("vcore", 1);
        	sinkJson.put("heap_memory", 4096);
        	sinkJson.put("direct_memory", 0);
        	sinkJson.put("native_memory", 0);
        	nodesJsonArray.add(sinkJson);
    	}
    	
    	JSONObject linkJson = new JSONObject();
    	linkJson.put("source", 3);
    	linkJson.put("target", 4);
    	linksJsonArray.add(linkJson);

    	for (int j = 0; j < dstDbNum; j++) {
        	linkJson = new JSONObject();
        	linkJson.put("source", 4);
        	linkJson.put("target", 5 + j * 2);
        	linksJsonArray.add(linkJson);
        	
        	linkJson = new JSONObject();
        	linkJson.put("source", 5 + j * 2);
        	linkJson.put("target", 6 + j * 2);
        	linksJsonArray.add(linkJson);
    	}
    	
    	return planJson.toJSONString();
    }
}
