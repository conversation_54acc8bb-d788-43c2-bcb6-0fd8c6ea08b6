package com.aliyun.wormhole.qanat.process;

import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.odps.Event;
import com.aliyun.odps.InternalOdps;
import com.aliyun.odps.Odps;
import com.aliyun.odps.account.Account;
import com.aliyun.odps.account.AliyunAccount;
import com.aliyun.wormhole.qanat.api.service.BlinkService;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 流任务监控
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class StreamTaskRTMonitorProcessor extends JavaProcessor {
    
    @Resource
    private BlinkService blinkService;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
            log.info("start to monitor stream jobs, param=[]", context.getJobParameters());
            String tenantId = paramsJson.getString("tenantId");
            String type = paramsJson.getString("type");
            type = StringUtils.isBlank(type) ? "" : type;
            String appName = paramsJson.getString("appName");
            Boolean forceRebuild = paramsJson.getBoolean("forceRebuild");
            forceRebuild = forceRebuild == null ? false : forceRebuild;
            Integer intervalMins = paramsJson.getInteger("intervalMins");
            intervalMins = intervalMins == null ? 10 : intervalMins;
    		Integer errorLevelMultiple = paramsJson.getInteger("errorLevelMultiple");
    		errorLevelMultiple = errorLevelMultiple == null ? 3 : errorLevelMultiple;
            String jobNames = paramsJson.getString("jobNames");
            
        	List<String> jobNameList = Arrays.asList(jobNames.split(","));
            if (CollectionUtils.isNotEmpty(jobNameList)) {
            	for (String jobName : jobNameList) {
            		log.info("start to process:{}", jobName);
            		
            		//任务状态失败立即重启
            		if (checkJobRunning(tenantId, appName, jobName, type)) {
            			if (forceRebuild) {
            				Calendar cal = Calendar.getInstance();
                			cal.add(Calendar.MINUTE, -1*intervalMins);
                			blinkService.restartJob(tenantId, appName, jobName, cal.getTime(), false);
                    		log.info("datatube_stream_monitor" + type + "|job_not_run_recover|{}|job has recovered from not_run status", jobName);
	            		}
            			continue;//跳过后面所有check
            		}
            		
            		//任务近5mis写出量
					boolean jobSinkOutCheckFailed = checkJobSinkOut(tenantId, appName, jobName, intervalMins, "job_sink_tps_0_low", type);//warn
            		if (!jobSinkOutCheckFailed) {
            			jobSinkOutCheckFailed = checkJobSinkOut(tenantId, appName, jobName, intervalMins*errorLevelMultiple, "job_sink_tps_0_high", type);//error
            		}
            		
            		//任务Failover
            		boolean jobFailoverCheckFailed = checkJobFailOver(tenantId, appName, jobName, type);
            		
            		//如果任务failover并且输出流量为0则重启
            		if (jobSinkOutCheckFailed && jobFailoverCheckFailed) {
                		log.error("datatube_stream_monitor" + type + "|job_exception_and_sink_tps_0|{}|任务存在异常并输出持续为0", jobName);
            			if (forceRebuild) {
            				Calendar cal = Calendar.getInstance();
                			cal.add(Calendar.MINUTE, -1*intervalMins);
                			blinkService.rebuildBlinkJob(tenantId, appName, jobName, cal.getTime());
                    		log.info("datatube_stream_monitor" + type + "|job_failover_recover|{}|job has recovered from failover", jobName);
	            		}
            			continue;//跳过后面所有check
            		}
            		
            		//近5分钟数据滞留时间
            		boolean jobFetchDelayCheckFailed = checkJobFetchDelay(tenantId, appName, intervalMins, jobName, "job_fetch_delay_low", type);//warn
            		if (!jobFetchDelayCheckFailed) {
//            			jobFetchDelayCheckFailed = checkJobFetchDelay(tenantId, appName, intervalMins*errorLevelMultiple, jobName, "job_fetch_delay_high", type);//error
            		}

            		log.info("finish to process:{}", jobName);
            	}
            }
        } catch (Exception e) {
            log.error("StreamTaskRTMonitorProcessor任务调度异常", e);
            return new ProcessResult(false);
        }
        return new ProcessResult(true);
    }

	private boolean checkJobFailOver(String tenantId, String appName, String jobName, String type) {
		boolean jobFailoverCheckFailed = false;
		String jobExceptions = blinkService.getJobExceptions(tenantId, appName, jobName);
		log.info("getJobExceptions({})={}", jobName, jobExceptions);
		if (StringUtils.isNotBlank(jobExceptions)) {
			JSONObject jobExceptionsJson = JSON.parseObject(jobExceptions);
			if (jobExceptionsJson != null 
					&& StringUtils.isNotBlank(jobExceptionsJson.getString("root-exception"))) {
        		log.error("datatube_stream_monitor" + type + "|job_failover|{}|job has exceptions", jobName);
    			jobFailoverCheckFailed = true;
			}
		}
		return jobFailoverCheckFailed;
	}

	private boolean checkJobRunning(String tenantId, String appName, String jobName, String type) {
		String jobRunSummary = null;
		int retries = 0;
		while (retries < 3) {
			jobRunSummary = blinkService.getJobRunSummary(tenantId, appName, jobName);
			log.info("getJobRunSummary({})={}", jobName, jobRunSummary);
			if (StringUtils.isBlank(jobRunSummary)) {
				retries++;
				try {
					Thread.sleep(500*retries);
				} catch (InterruptedException e) {
				}
			} else {
				break;
			}
		}
		
		boolean jobStateCheckFailed = false;
		if (StringUtils.isBlank(jobRunSummary)) {
			if (!blinkService.isJobCommitted(tenantId, appName, jobName)) {
				log.error("job:{} is not committed", jobName);
				log.error("datatube_stream_monitor" + type + "|job_not_run|{}|job is not running", jobName);
				jobStateCheckFailed = true;
			}
		} else {
			JSONObject jobRunSummaryJson = JSON.parseObject(jobRunSummary);
			if (jobRunSummaryJson == null 
					|| jobRunSummaryJson.getJSONObject("runSummary") == null 
					|| StringUtils.isBlank(jobRunSummaryJson.getJSONObject("runSummary").getString("actualState"))) {
				log.error("datatube_stream_monitor" + type + "|job_not_run|{}|job is not running", jobName);
				jobStateCheckFailed = true;
			} else {
				if (!"RUNNING".equalsIgnoreCase(jobRunSummaryJson.getJSONObject("runSummary").getString("actualState"))) {
		    		log.error("datatube_stream_monitor" + type + "|job_not_run|{}|job is not running", jobName);
		    		jobStateCheckFailed = true;
				}
			}
		}
		return jobStateCheckFailed;
	}

	private Boolean checkJobFetchDelay(String tenantId, String appName, Integer intervalMins, String jobName, String errorType, String type) {
		boolean jobFetchDelayCheckFailed = false;
		JSONObject metricJson = new JSONObject();
		long offset = 60*1000;
		long tsEnd = System.currentTimeMillis() - offset;
		long tsStart = tsEnd - intervalMins*60*1000;
		metricJson.put("start", tsStart);
		metricJson.put("end", tsEnd);
		JSONArray metricQueriesJson = new JSONArray();
		metricJson.put("queries", metricQueriesJson);
		JSONObject queryJson = new JSONObject();
		queryJson.put("metric", "blink.alywormhole." + jobName + ".fetched_delay");
		queryJson.put("aggregator", "max");
		metricQueriesJson.add(queryJson);
		String instanceMetric = blinkService.getInstanceMetric(tenantId, appName, jobName, metricJson.toJSONString());
		log.info("getInstanceMetric({},{})={}", jobName, metricJson.toJSONString(), instanceMetric);
		if (StringUtils.isNotBlank(instanceMetric)) {
			JSONArray instanceMetricJsonArray = JSON.parseArray(instanceMetric);
			if (instanceMetricJsonArray != null && instanceMetricJsonArray.size() > 0
					&& instanceMetricJsonArray.getJSONObject(0).getJSONObject("dps") != null
					&& CollectionUtils.isNotEmpty(instanceMetricJsonArray.getJSONObject(0).getJSONObject("dps").values())) {
				BigDecimal bd = instanceMetricJsonArray.getJSONObject(0).getJSONObject("dps").values().stream().map(e->new BigDecimal(e.toString())).max(Comparator.naturalOrder()).get();
				if (bd.compareTo(new BigDecimal(intervalMins*60*1000)) > 0) {
					log.error("datatube_stream_monitor" + type + "|{}|{}|fetch_delay is {}ms in {}mins", errorType, jobName, bd, intervalMins);
					jobFetchDelayCheckFailed = true;
				}
			}
		}
		return jobFetchDelayCheckFailed;
	}

	private Boolean checkJobSinkOut(String tenantId, String appName, String jobName, int intervalMins, String errorType, String type) {
		boolean jobSinkOutCheckFailed = false;
		JSONObject metricJson = new JSONObject();
		long offset = 60*1000;
		long tsEnd = System.currentTimeMillis() - offset;
		long tsStart = tsEnd - intervalMins*60*1000;
		metricJson.put("start", tsStart);
		metricJson.put("end", tsEnd);
		JSONArray metricQueriesJson = new JSONArray();
		metricJson.put("queries", metricQueriesJson);
		JSONObject queryJson = new JSONObject();
		queryJson.put("metric", "blink.alywormhole." + jobName + ".sink.outTps.rate");
		queryJson.put("aggregator", "max");
		metricQueriesJson.add(queryJson);
		String instanceMetric = blinkService.getInstanceMetric(tenantId, appName, jobName, metricJson.toJSONString());
		log.info("getInstanceMetric({},{})={}", jobName, metricJson.toJSONString(), instanceMetric);
		if (StringUtils.isNotBlank(instanceMetric)) {
			JSONArray instanceMetricJsonArray = JSON.parseArray(instanceMetric);
			if (instanceMetricJsonArray != null && instanceMetricJsonArray.size() > 0
					&& instanceMetricJsonArray.getJSONObject(0).getJSONObject("dps") != null
					&& CollectionUtils.isNotEmpty(instanceMetricJsonArray.getJSONObject(0).getJSONObject("dps").values())) {
				BigDecimal bd = new BigDecimal("0.0");
				for (Object value : instanceMetricJsonArray.getJSONObject(0).getJSONObject("dps").values()) {
					bd = bd.add(new BigDecimal(value.toString()));
				}
				if (bd.compareTo(new BigDecimal("0.0")) == 0) {
					log.error("datatube_stream_monitor" + type + "|{}|{}|sinkOutTps is 0 in {} mins", errorType, jobName, intervalMins);
					jobSinkOutCheckFailed = true;
				}
			}
		}
		return jobSinkOutCheckFailed;
	}
    
    @Override
    public void kill(JobContext context) {
        
    }
    
    static Event buildEvent(String eventName, String tableName, String callbackUri, String comment) throws URISyntaxException {
        Event event = new Event();
        event.setName(eventName);   // 指定事件名称
        event.setComment(comment);    // 事件注释
        event.setType(Event.SourceType.TABLE);   // 指定事件类型，目前支持 TABLE
        Event.Config config = new Event.Config();
        config.setName("source");
        config.setValue(tableName);   // 指定事件源(即 表名). "*" 表示所有表.
        event.setConfig(config);
        event.setUri(new URI(callbackUri));   // 指定了一个回调地址
        return event;
    }
}