package com.aliyun.wormhole.qanat.bpms;

import com.alibaba.alipmc.api.ProcessInstanceService;
import com.alibaba.alipmc.api.model.bpm.ProcessInstance;
import com.alibaba.fastjson.JSONObject;

import com.taobao.notify.message.Message;
import com.taobao.notify.message.StringMessage;
import com.taobao.notify.remotingclient.MessageListener;
import com.taobao.notify.remotingclient.MessageStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import javax.annotation.Resource;

/**
 * 
 * <AUTHOR>
 * 2019年8月19日
 */
@Slf4j
@Component
public class BpmsNotifyListener implements MessageListener {

    @Autowired
    ApplicationContext applicationContext;

    @Value("${qanat.bpms.appKey}")
    private String appKey;

    @Value("${qanat.bpms.authKey}")
    private String bpmsAuthKey;
    
    @Resource
    private ProcessInstanceService processInstanceService;

    @Override
    public void receiveMessage(Message message, MessageStatus messageStatus) {
        if (message instanceof StringMessage) {
            StringMessage stringMessage = (StringMessage) message;
            try {
                	String contentKey = "CONTENT";
                	String appNameKey = "appName";
                	String typeKey = "type";
                	String bodyKey = "body";
                	String processInstanceIdKey = "processInstanceId";
                	List<String> eventTypeList = Arrays.asList("PROC_INST_FINISH", "PROCESS_INSTANCE_TERMINATE");
                JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(stringMessage));
                if ("ALIPMC".equals(stringMessage.getTopic())) {
                    JSONObject bodyJSON = JSONObject.parseObject(json.getString(bodyKey));
                    if (appKey.equals(bodyJSON.getJSONObject(contentKey).getString(appNameKey))) {
                    	log.info("stringMessage json:"+json.toString());
                        String requestId = UUID.randomUUID().toString();
                        String eventType = bodyJSON.getJSONObject(contentKey).getString(typeKey);
                        if (eventTypeList.contains(eventType)) {
                            String processInstanceId = bodyJSON.getJSONObject(contentKey).getJSONObject(bodyKey).getString(processInstanceIdKey);
                            ProcessInstance processInstance = processInstanceService.getProcessInstance(processInstanceId, bpmsAuthKey);
                            String bpmsProcessCode = processInstance.getProcessCode();
                            applicationContext.publishEvent(new BpmsProcInstEvent(this, processInstanceId, eventType, requestId,bpmsProcessCode));
                        }
                    }
                }
            } catch (Exception ex) {
                log.error("BpmsNotifyListener failed", ex);
            }
        }
    }
}
