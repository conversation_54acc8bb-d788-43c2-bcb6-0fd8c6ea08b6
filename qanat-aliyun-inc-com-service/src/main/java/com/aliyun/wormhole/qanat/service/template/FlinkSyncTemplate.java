package com.aliyun.wormhole.qanat.service.template;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Flink同步模板系统
 * 基于TDD思路实现，专门为Flink+Hologres架构设计的模板
 */
@Component
public class FlinkSyncTemplate {
    
    /**
     * Hologres binlog源表模板
     */
    public static final String HOLO_BINLOG_SOURCE = 
        "-- Hologres binlog源表定义\n" +
        "CREATE TABLE ${tableName} (\n" +
        "    ${fieldDefinitions},\n" +
        "    PRIMARY KEY (${primaryKeys}) NOT ENFORCED\n" +
        ") WITH (\n" +
        "    'connector' = 'hologres',\n" +
        "    'dbname' = '${dbname}',\n" +
        "    'tablename' = '${physicalTableName}',\n" +
        "    'username' = '${username}',\n" +
        "    'password' = '${password}',\n" +
        "    'endpoint' = '${endpoint}',\n" +
        "    'binlog' = 'true',\n" +
        "    'binlog.ignore.delete' = 'false',\n" +
        "    'binlog.scan.startup.mode' = 'latest'\n" +
        ");\n\n";
    
    /**
     * Hologres目标表模板
     */
    public static final String HOLO_SINK_TABLE = 
        "-- Hologres目标表定义\n" +
        "CREATE TABLE ${tableName} (\n" +
        "    ${fieldDefinitions},\n" +
        "    PRIMARY KEY (${primaryKeys}) NOT ENFORCED\n" +
        ") WITH (\n" +
        "    'connector' = 'hologres',\n" +
        "    'dbname' = '${dbname}',\n" +
        "    'tablename' = '${physicalTableName}',\n" +
        "    'username' = '${username}',\n" +
        "    'password' = '${password}',\n" +
        "    'endpoint' = '${endpoint}',\n" +
        "    'write.mode' = 'insert_or_replace',\n" +
        "    'write.batch.size' = '1000',\n" +
        "    'write.batch.interval' = '5000ms',\n" +
        "    'write.max.retries' = '3'\n" +
        ");\n\n";
    
    /**
     * Hologres维表模板
     */
    public static final String HOLO_DIM_TABLE = 
        "-- Hologres维表定义\n" +
        "CREATE TABLE ${tableName} (\n" +
        "    ${fieldDefinitions},\n" +
        "    PRIMARY KEY (${primaryKeys}) NOT ENFORCED\n" +
        ") WITH (\n" +
        "    'connector' = 'hologres',\n" +
        "    'dbname' = '${dbname}',\n" +
        "    'tablename' = '${physicalTableName}',\n" +
        "    'username' = '${username}',\n" +
        "    'password' = '${password}',\n" +
        "    'endpoint' = '${endpoint}',\n" +
        "    'lookup.cache.max-rows' = '10000',\n" +
        "    'lookup.cache.ttl' = '1800000',\n" +
        "    'lookup.max-retries' = '3'\n" +
        ");\n\n";
    
    /**
     * 完整的Flink SQL作业模板
     */
    public static final String FLINK_SYNC_SQL = 
        "--SQL\n" +
        "--********************************************************************--\n" +
        "--Author: ${author}\n" +
        "--CreateTime: ${createTime}\n" +
        "--JobName: ${jobName}\n" +
        "--ModelCode: ${modelCode}\n" +
        "--Architecture: Flink + Hologres\n" +
        "--Comment: ${comment}\n" +
        "--********************************************************************--\n\n" +
        
        "-- UDF函数定义\n" +
        "CREATE TEMPORARY FUNCTION IF NOT EXISTS qanatConcat AS 'com.aliyun.wormhole.qanat.flink.udf.QanatConcatUdf';\n" +
        "CREATE TEMPORARY FUNCTION IF NOT EXISTS qanatNvl AS 'com.aliyun.wormhole.qanat.flink.udf.QanatNvlUdf';\n" +
        "CREATE TEMPORARY FUNCTION IF NOT EXISTS qanatDateFormat AS 'com.aliyun.wormhole.qanat.flink.udf.QanatDateFormatUdf';\n\n" +
        
        "${sourceTableDefinitions}\n" +
        "${dimTableDefinitions}\n" +
        "${sinkTableDefinition}\n" +
        
        "-- 主要的数据处理逻辑\n" +
        "${mainInsertStatement}\n";
    
    /**
     * Flink DAG调度脚本模板
     */
    public static final String FLINK_DAG_SCRIPT = 
        "// Flink架构DAG脚本\n" +
        "Dag dag = new Dag(\"${dagId}\");\n\n" +
        
        "// 节点定义\n" +
        "${nodeDefinitions}\n" +
        
        "// 依赖关系\n" +
        "${dependencies}\n" +
        
        "return dag;\n";
    
    /**
     * DataWorks数据集成节点模板
     */
    public static final String DATAWORKS_NODE_TEMPLATE = 
        "DataWorksNode ${nodeId} = new DataWorksNode(\"${nodeId}\", dag);\n" +
        "${nodeId}.setSrcDsName(\"${srcDsName}\");\n" +
        "${nodeId}.setDstDbName(\"${dstDbName}\");\n" +
        "${nodeId}.setDstTableName(\"${dstTableName}\");\n" +
        "${nodeId}.setDataBaseline(${dataBaseline});\n" +
        "${nodeId}.setParallism(${parallism});\n" +
        "${nodeId}.setBatchSize(${batchSize});\n";
    
    /**
     * Flink流计算节点模板
     */
    public static final String FLINK_STREAM_NODE_TEMPLATE = 
        "FlinkStreamNode ${nodeId} = new FlinkStreamNode(\"${nodeId}\", dag);\n" +
        "${nodeId}.setJobName(\"${jobName}\");\n" +
        "${nodeId}.setStartTimePolicy(\"${startTimePolicy}\");\n";
    
    /**
     * Hologres外表节点模板
     */
    public static final String HOLO_EXT_TBL_NODE_TEMPLATE = 
        "HoloExtTblNode ${nodeId} = new HoloExtTblNode(\"${nodeId}\", dag);\n" +
        "${nodeId}.setSrcDsName(\"${srcDsName}\");\n" +
        "${nodeId}.setDstDbName(\"${dstDbName}\");\n" +
        "${nodeId}.setDstTableName(\"${dstTableName}\");\n";
    
    /**
     * Flink作业配置模板
     */
    public static final String FLINK_JOB_CONFIG = 
        "-- Flink作业配置\n" +
        "SET 'execution.checkpointing.interval' = '60s';\n" +
        "SET 'execution.checkpointing.mode' = 'EXACTLY_ONCE';\n" +
        "SET 'execution.checkpointing.timeout' = '10min';\n" +
        "SET 'execution.checkpointing.max-concurrent-checkpoints' = '1';\n" +
        "SET 'execution.checkpointing.min-pause' = '5s';\n" +
        "SET 'restart-strategy' = 'exponential-delay';\n" +
        "SET 'restart-strategy.exponential-delay.initial-backoff' = '10s';\n" +
        "SET 'restart-strategy.exponential-delay.max-backoff' = '2min';\n" +
        "SET 'restart-strategy.exponential-delay.backoff-multiplier' = '2.0';\n" +
        "SET 'restart-strategy.exponential-delay.reset-backoff-threshold' = '10min';\n" +
        "SET 'restart-strategy.exponential-delay.jitter-factor' = '0.1';\n\n";
    
    /**
     * 格式化模板
     */
    public String formatTemplate(String template, Map<String, Object> params) {
        String result = template;
        
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                String placeholder = "${" + entry.getKey() + "}";
                String value = entry.getValue() != null ? entry.getValue().toString() : "";
                result = result.replace(placeholder, value);
            }
        }
        
        // 清理未替换的占位符
        result = cleanUnreplacedPlaceholders(result);
        
        return result;
    }
    
    /**
     * 清理未替换的占位符
     */
    private String cleanUnreplacedPlaceholders(String content) {
        // 移除未替换的占位符，避免生成无效的SQL
        Pattern pattern = Pattern.compile("\\$\\{[^}]+\\}");
        Matcher matcher = pattern.matcher(content);
        
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            // 将未替换的占位符替换为空字符串或默认值
            String placeholder = matcher.group();
            String replacement = getDefaultValueForPlaceholder(placeholder);
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 获取占位符的默认值
     */
    private String getDefaultValueForPlaceholder(String placeholder) {
        // 根据占位符类型返回合适的默认值
        if (placeholder.contains("fieldDefinitions")) {
            return "id BIGINT";
        } else if (placeholder.contains("primaryKeys")) {
            return "id";
        } else if (placeholder.contains("tableName")) {
            return "default_table";
        } else if (placeholder.contains("dbname")) {
            return "default_db";
        } else if (placeholder.contains("username")) {
            return "default_user";
        } else if (placeholder.contains("password")) {
            return "default_password";
        } else if (placeholder.contains("endpoint")) {
            return "default_endpoint";
        } else {
            return ""; // 其他情况返回空字符串
        }
    }
    
    /**
     * 验证模板参数完整性
     */
    public boolean validateTemplateParams(String template, Map<String, Object> params) {
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(template);
        
        while (matcher.find()) {
            String paramName = matcher.group(1);
            if (params == null || !params.containsKey(paramName)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取模板中的所有参数名
     */
    public Set<String> getTemplateParams(String template) {
        Set<String> params = new HashSet<>();
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(template);
        
        while (matcher.find()) {
            params.add(matcher.group(1));
        }
        
        return params;
    }
}
