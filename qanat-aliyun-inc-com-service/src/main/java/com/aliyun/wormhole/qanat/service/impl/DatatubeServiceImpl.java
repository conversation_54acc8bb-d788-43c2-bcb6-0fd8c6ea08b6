package com.aliyun.wormhole.qanat.service.impl;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.CreateOdsRequest;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.DatatubeCreateRequest;
import com.aliyun.wormhole.qanat.api.dto.DatatubeModifyRequest;
import com.aliyun.wormhole.qanat.api.dto.TaskInfoRequest;
import com.aliyun.wormhole.qanat.api.service.DatatubeService;
import com.aliyun.wormhole.qanat.api.service.ViewModelRequest;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTaskExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyun.wormhole.qanat.service.datatube.DatatubeHandler;
import com.taobao.ateye.util.CollectionUtils;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * ADB实时数仓同步服务
 * <AUTHOR>
 * 2019年7月23日
 */
@Slf4j
@Component
@HSFProvider(serviceInterface = DatatubeService.class)
public class DatatubeServiceImpl implements DatatubeService {
    
    @Resource 
    private DatatubeHandler handler;
    
    @Resource 
    private DatatubeInstanceMapper datatubeInstanceMapper;
    
    @Resource 
    private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;
    
    @Override
    public DataResult<Map<String, Object>> getDetail(Long id) {
        DataResult<Map<String, Object>> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	result.setData(JSON.parseObject(JSON.toJSONString(datatubeInstanceMapper.selectByPrimaryKey(id))));
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("getDetail failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("getDetail failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<Map<String, BigDecimal>> getComputeCost(Long id) {
        DataResult<Map<String, BigDecimal>> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	Map<String, BigDecimal> data = new HashMap<>();
        	DatatubeInstance inst = datatubeInstanceMapper.selectByPrimaryKey(id);
        	if (inst == null) {
                result.setSuccess(false);
                result.setCode("501");
                result.setMessage("datatubeInstanceId:" + id + " is not exists");
                return result;
        	}
        	DatatubeInstanceTaskExample example = new DatatubeInstanceTaskExample();
        	example.createCriteria().andTenantIdEqualTo(inst.getTenantId()).andDatatubeInstIdEqualTo(id).andIsDeletedEqualTo(0L);
        	List<DatatubeInstanceTask> ditList = datatubeInstanceTaskMapper.selectByExample(example);
        	if (CollectionUtils.isNotEmpty(ditList)) {
        		BigDecimal sumCUs = ditList.stream().filter(e -> "blink_stream".equalsIgnoreCase(e.getTaskType())).map(e -> e.getTaskCu()).reduce(new BigDecimal("0"), BigDecimal::add);
        		data.put("total", sumCUs);
        		for (DatatubeInstanceTask dit : ditList) {
        			if ("blink_stream".equalsIgnoreCase(dit.getTaskType())
        					|| "blink_batch".equalsIgnoreCase(dit.getTaskType())) {
        				data.put(dit.getTaskName(), dit.getTaskCu());
        			}
        		}
        	}
        	result.setData(data);
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("getComputeCost failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("getComputeCost failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<Long> create(DatatubeCreateRequest request) {
    	log.info("create({})", JSON.toJSONString(request));
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	Long datatubeInstanceId = null;
        	if ("ods".equalsIgnoreCase(request.getProvider())) {
        		CreateOdsRequest req = JSON.parseObject(JSON.toJSONString(request.getProivderData()), CreateOdsRequest.class);
        		req.setTenantId(request.getTenantId());
        		req.setAppName(request.getAppName());
        		req.setDatatubeLevel(request.getDatatubeLevel());
        		req.setObjectType(request.getObjectType());
        		req.setOperateEmpid(request.getOperateEmpid());
        		datatubeInstanceId = handler.createOdsDatatube(req);
        	} else if ("viewmodel".equalsIgnoreCase(request.getProvider())) {
        		ViewModelRequest req = JSON.parseObject(JSON.toJSONString(request.getProivderData()), ViewModelRequest.class);
        		req.setTenantId(request.getTenantId());
        		req.setAppName(request.getAppName());
        		if (request.getIsTest()) {
        			req.setDatatubeLevel("minor");
        		} else {
        			req.setDatatubeLevel(request.getDatatubeLevel());
        		}
        		req.setObjectType(request.getObjectType());
        		req.setOperateEmpid(request.getOperateEmpid());
        		req.setIsTest(request.getIsTest());
        		req.setVersion(request.getVersion());
        		datatubeInstanceId = handler.createViewModelDatatube(req);
        	} else if ("dag".equalsIgnoreCase(request.getProvider())) {
        		TaskInfoRequest req = JSON.parseObject(JSON.toJSONString(request.getProivderData()), TaskInfoRequest.class);
        		req.setTenantId(request.getTenantId());
        		req.setAppName(request.getAppName());
        		req.setDatatubeLevel(request.getDatatubeLevel());
        		req.setObjectType(request.getObjectType());
        		req.setOperateEmpid(request.getOperateEmpid());
        		datatubeInstanceId = handler.createDAGDatatube(req);
        	}
        	result.setData(datatubeInstanceId);
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("create failed, error={}", e.getMessage(), e);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("create failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<Boolean> modify(DatatubeModifyRequest request) {
    	log.info("modify({})", JSON.toJSONString(request));
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
    		DatatubeInstance datatubeInst = datatubeInstanceMapper.selectByPrimaryKey(request.getDatatubeInstId());
    		if (datatubeInst == null) {
    			throw new QanatBizException("datatube:" + request.getDatatubeInstId() + " is not found");
    		}
        	if ("ods".equalsIgnoreCase(datatubeInst.getProvider())) {
        		CreateOdsRequest req = JSON.parseObject(JSON.toJSONString(request.getProivderData()), CreateOdsRequest.class);
        		req.setTenantId(request.getTenantId());
        		req.setAppName(request.getAppName());
        		req.setDatatubeLevel(request.getDatatubeLevel());
        		req.setObjectType(request.getObjectType());
        		req.setOperateEmpid(request.getOperateEmpid());
        		handler.modifyOdsDatatube(request.getDatatubeInstId(), req);
        	} else if ("viewmodel".equalsIgnoreCase(datatubeInst.getProvider())) {
        		ViewModelRequest req = JSON.parseObject(JSON.toJSONString(request.getProivderData()), ViewModelRequest.class);
        		req.setTenantId(datatubeInst.getTenantId());
        		req.setDatatubeLevel(request.getDatatubeLevel());
        		req.setRemark(request.getRemark());
        		req.setOperateEmpid(request.getOperateEmpid());
        		req.setModelId(datatubeInst.getProviderId());
        		handler.modifyViewModelDatatube(req);
        	} else if ("dag".equalsIgnoreCase(datatubeInst.getProvider())) {
        		TaskInfoRequest req = JSON.parseObject(JSON.toJSONString(request.getProivderData()), TaskInfoRequest.class);
    			handler.modifyDAGDatatube(request.getDatatubeInstId(), request.getOperateEmpid(), req.getDagScript(), request.getDatatubeLevel(), request.getRemark());
        	}
        	result.setData(true);
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("modify failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("modify failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<Boolean> makeupViewModelDatatubeTasks(Long id, String jobNames, String operateEmpid) {
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	result.setData(handler.makeupViewModelDatatubeTasks(id, jobNames, operateEmpid));
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("makeupViewModelDatatubeTasks failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("makeupViewModelDatatubeTasks failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<Long> makeupOdsDatatubeTasks(Long taskId, String datatubeLevel, String operateEmpid) {
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	result.setData(handler.makeupOdsDatatubeTasks(taskId, datatubeLevel, operateEmpid));
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("makeupOdsDatatubeTasks failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("makeupOdsDatatubeTasks failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<List<Long>> makeupMultiOdsDatatubeTasks(String taskIds, String datatubeLevel, String operateEmpid) {
        DataResult<List<Long>> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	String [] taskIdArray = taskIds.split(",");
        	List<Long> taskIdList = new ArrayList<>();
        	for (String taskIdStr : taskIdArray) {
        		taskIdList.add(Long.valueOf(taskIdStr.trim()));
        	}
        	result.setData(handler.makeupOdsDatatubeTasks(taskIdList, datatubeLevel, operateEmpid));
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("makeupOdsDatatubeTasks failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("makeupOdsDatatubeTasks failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<Long> makeupDAGDatatubeTasks(Long taskId, String datatubeLevel, String objectType, String operateEmpid) {
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	result.setData(handler.makeupDAGDatatubeTasks(taskId, datatubeLevel, objectType, operateEmpid));
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("makeupDAGDatatubeTasks failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("makeupDAGDatatubeTasks failed, error={}", e.getMessage(), e);
        }
        return result;
    }
}