package com.aliyun.wormhole.qanat.service.es;

import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.aliyun.wormhole.qanat.api.service.ElasticSearchBCPService;
import com.aliyun.wormhole.qanat.api.service.ElasticSearchService;

@HSFProvider(serviceInterface = ElasticSearchBCPService.class)
public class ElasticSearchBCPServiceImpl implements ElasticSearchBCPService {
    
    @Resource
    private ElasticSearchService esService;

    @Override
    public String queryByPk(String tenantId, String dbName, String index, String pk) {
    	Map<String, Object> result = esService.queryByPk(tenantId, dbName, index, pk);
        return JSON.toJSONString(result);
    }
}
