package com.aliyun.wormhole.qanat.job;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.BlinkBatchV2Node;
import com.aliyun.wormhole.qanat.api.dag.NodeAction;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTaskExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.taobao.ateye.util.CollectionUtils;
import com.taobao.unifiedsession.core.commons.utils.DateUtils;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

/**
 * Blink任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatBlinkBatchJobProcessor extends AbstractQanatNodeJobProcessor<BlinkBatchV2Node> {
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;

    @Override
    void doProcess(Map<String, Object> instParamsMap, BlinkBatchV2Node blink) {
        try {
        	String operator = (String)instParamsMap.get("operator");
            Date startTime = new Date();
            String tenantId = String.valueOf(instParamsMap.get("tenantId"));
            String appName = String.valueOf(instParamsMap.get("appName"));
            
            blinkService.stopJob(tenantId, appName, blink.getJobName());
            log.info("blink job[{}] has been stopped", blink.getJobName());
            
            Map<String, String> blinkPamams = new HashMap<>();
            Date today = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String bizDate = sdf.format(DateUtils.addDay(today, -1));
            blinkPamams.put("bizdate", bizDate);
            
            String bizDatePre = sdf.format(DateUtils.addDay(today, -2));
            blinkPamams.put("beforebizdate", bizDatePre);
            
            String bizDateAfter = sdf.format(today);
            blinkPamams.put("afterbizdate", bizDateAfter);
            
            sdf = new SimpleDateFormat("yyyy-MM-dd");
            String bizDateFormat2 = sdf.format(DateUtils.addDay(today, -1));
            blinkPamams.put("bizdate00", bizDateFormat2 + " 00:00:00");
            blinkPamams.put("bizdate24", bizDateFormat2 + " 23:59:59");
            
            String bizDateAfterFormat2 = sdf.format(today);
            blinkPamams.put("afterbizdate00", bizDateAfterFormat2 + " 00:00:00");
            blinkPamams.put("afterbizdate24", bizDateAfterFormat2 + " 23:59:59");
            
            for (String key : instParamsMap.keySet()) {
                blinkPamams.put(key, String.valueOf(instParamsMap.get(key)));
            }
            log.info("blinkPamams={}", JSON.toJSONString(blinkPamams));
            
            
            Long blinkInstId = blinkService.startJob(tenantId, appName, blink.getJobName(), startTime, blinkPamams);
            boolean isGetCU = false;
            while (true) {
                String instState = blinkService.getInstanceActualState(tenantId, appName, blink.getJobName(), blinkInstId);
                if ("SUCCESS".equalsIgnoreCase(instState)) {
                    break;
                } else if ("RUNNING".equalsIgnoreCase(instState) && !isGetCU) {
                	try {
                		Map<String, Object> resource = blinkService.getInstanceResource(tenantId, appName, blink.getJobName());
        				if (resource != null && CollectionUtils.isNotEmpty(resource.keySet())) {
            				BigDecimal vcoreCU = resource.get("allocatedVirtualCores") != null ? new BigDecimal(resource.get("allocatedVirtualCores").toString()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");
            				BigDecimal mbCU = resource.get("allocatedMB") != null ? new BigDecimal(resource.get("allocatedMB").toString()).divide(new BigDecimal("4096"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");

                        	DatatubeInstanceTaskExample example = new DatatubeInstanceTaskExample();
                        	example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTaskNameEqualTo(blink.getJobName()).andTaskTypeEqualTo("blink_batch");
                        	
                        	DatatubeInstanceTask record = new DatatubeInstanceTask();
                    		record.setGmtModified(new Date());
                    		record.setModifyEmpid(operator);
            				record.setTaskCu(vcoreCU.compareTo(mbCU) > 0 ? vcoreCU : mbCU);
                        	datatubeInstanceTaskMapper.updateByExampleSelective(record, example);
                        	
                        	isGetCU = true;
        				}
                	} catch (Exception e) {
                		log.error("{} get cu failed:{}", blink.getJobName(), e.getMessage());
                    	isGetCU = true;
                	}
                } else if ("TERMINATED".equalsIgnoreCase(instState)
                		|| "FAILED".equalsIgnoreCase(instState)) {
                	throw new QanatBizException("Blink批任务执行失败或手动停止");
                }
                Thread.sleep(60000);//60s
            }
        } catch (QanatBizException e) {
            log.error("Blink批任务调度异常:{}", e.getMessage());
            throw new QanatBizException(e.getMessage());
        } catch (Exception e) {
            log.error("Blink批任务调度异常", e);
            throw new QanatBizException(e.getMessage());
        }
    }

	@Override
    public void doKill(Map<String, Object> instParamsMap, BlinkBatchV2Node blink) {
        try {
            String tenantId = String.valueOf(instParamsMap.get("tenantId"));
            String appName = String.valueOf(instParamsMap.get("appName"));
            
            if (blink.getNodeAction().equals(NodeAction.STREAM)) {
                log.info("blink stream job[{}] skipped", blink.getJobName());
            	return;
            }
            
            blinkService.stopJob(tenantId, appName, blink.getJobName());
            log.info("blink job[{}] has been killed", blink.getJobName());
        } catch (Exception e) {
            log.error("Kill Blink批任务异常:{}", e.getMessage(), e);
            throw new QanatBizException(e.getMessage());
        }
    }
}