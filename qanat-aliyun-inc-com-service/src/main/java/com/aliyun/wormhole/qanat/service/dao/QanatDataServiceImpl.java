package com.aliyun.wormhole.qanat.service.dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFProvider;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.odps.Instance;
import com.aliyun.odps.Odps;
import com.aliyun.odps.account.Account;
import com.aliyun.odps.account.AliyunAccount;
import com.aliyun.odps.task.SQLTask;
import com.aliyun.wormhole.qanat.api.service.QanatDataService;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Component
@HSFProvider(serviceInterface = QanatDataService.class)
@Slf4j
public class QanatDataServiceImpl implements QanatDataService {

    @Resource
    @Qualifier("jdbcTemplate")
    private JdbcTemplate jdbcTemplate;
    @Resource
    private DbInfoMapper dbInfoMapper;

    @Override
    public void execute(String sql) {
        jdbcTemplate.execute(sql);
    }

    @Override
    public List<Map<String, Object>> query(String sql) {
        return jdbcTemplate.queryForList(sql);
    }

    @Override
    public Integer update(String sql) {
        return jdbcTemplate.update(sql.toString());
    }

    @Override
    public Boolean execSql(String dbName, String sql) {
        log.info("execSql({},{})", dbName, sql);
        try {
            DbInfoExample example = new DbInfoExample();
            example.createCriteria().andDbNameEqualTo(dbName).andIsDeletedEqualTo(0L);
            List<DbInfo> dbInfos = dbInfoMapper.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isNotEmpty(dbInfos)) {
                DbInfo dbInfo = dbInfos.get(0);
                JSONObject metaJson = JSON.parseObject(dbInfo.getMeta());

                if ("odps".equalsIgnoreCase(dbInfo.getDbType())) {
                    Account account = new AliyunAccount(metaJson.getString("accessId"), metaJson.getString("accessKey"));
                    Odps odps = new Odps(account);
                    odps.setEndpoint(metaJson.getString("odpsServer"));

                    odps.setDefaultProject(metaJson.getString("project"));
                    Instance inst = SQLTask.run(odps, odps.getDefaultProject(), sql, null, null);
                    inst.waitForSuccess();
                    String logview = odps.logview().generateLogView(inst, 7 * 24);
                    log.info("logview={}", logview);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("execSql failed:{}", e.getMessage(), e);
            return false;
        }
    }
}
