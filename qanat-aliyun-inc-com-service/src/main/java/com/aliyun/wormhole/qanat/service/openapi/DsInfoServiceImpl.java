package com.aliyun.wormhole.qanat.service.openapi;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.ali.unit.rule.util.lang.CollectionUtils;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.DatasourceDTO;
import com.aliyun.wormhole.qanat.api.dto.DatasourceRequest;
import com.aliyun.wormhole.qanat.api.dto.DsFieldInfoDTO;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelDsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelInfoMapper;
import com.aliyun.wormhole.qanat.openapi.DsInfoService;
import com.aliyun.wormhole.qanat.openapi.model.ApiResult;
import com.aliyun.wormhole.qanat.openapi.model.FieldInfoDTO;
import com.aliyun.wormhole.qanat.openapi.model.FieldInfoQeuryRequest;
import com.aliyun.wormhole.qanat.openapi.model.DsInfoDTO;
import com.aliyun.wormhole.qanat.openapi.model.DsInfoQeuryRequest;
import com.github.pagehelper.PageInfo;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@HSFProvider(serviceInterface = DsInfoService.class)
public class DsInfoServiceImpl extends OpenApiBase implements DsInfoService {
    
    @Resource
    private DatasourceService dsInfoService;
    
    @Resource
    private ViewModelInfoMapper viewModelInfoMapper;
    
    @Resource
    private DatasourceMapper datasourceMapper;
    
    @Resource
    private ViewModelDsRelationMapper viewModelDsRelationMapper;
    
    @Override
    public ApiResult<PageInfo<DsInfoDTO>> list4Page(DsInfoQeuryRequest request) {
        log.debug("list4Page({})", JSON.toJSONString(request));
        ApiResult<PageInfo<DsInfoDTO>> apiResult = new ApiResult<>();
        apiResult.setCode("200");
        apiResult.setSuccess(true);
        try {
            if (StringUtils.isBlank(request.getTenantId())) {
                throw new QanatBizException("TenantId is empty");
            }
            if (!checkAccessKey(request)) {
                throw new QanatBizException("AK failed");
            }
            DatasourceRequest dsReq = new DatasourceRequest();
            BeanUtils.copyProperties(request, dsReq);
            DataResult<PageInfo<DatasourceDTO>> queryResult = dsInfoService.list4Page(dsReq);
            if (queryResult != null 
            		&& queryResult.getSuccess() 
            		&& queryResult.getData() != null 
            		&& CollectionUtils.isNotEmpty(queryResult.getData().getList())) {
                List<DsInfoDTO> dtoList = new ArrayList<>();
            	for (DatasourceDTO innerDto : queryResult.getData().getList()) {
            		DsInfoDTO outerDto = new DsInfoDTO();
            		BeanUtils.copyProperties(innerDto, outerDto);
            		dtoList.add(outerDto);
            	}
            	PageInfo<DsInfoDTO> pageInfoDTO = new PageInfo<>();
            	pageInfoDTO.setList(dtoList);
            	pageInfoDTO.setPageNum(queryResult.getData().getPageNum());
            	pageInfoDTO.setPages(queryResult.getData().getPages());
            	pageInfoDTO.setPageSize(queryResult.getData().getPageSize());
            	pageInfoDTO.setTotal(queryResult.getData().getTotal());
            	apiResult.setData(pageInfoDTO);
            }
        } catch(Exception e) {
            log.error("list4Page failed, error={}", e.getMessage(), e);
            apiResult.setSuccess(false);
            apiResult.setCode("500");
            apiResult.setMessage(e.getMessage());
        }
        return apiResult;
    }
    
    @Override
    public ApiResult<PageInfo<FieldInfoDTO>> listDsFields4Page(FieldInfoQeuryRequest request) {
        log.debug("listDsFields4Page({})", JSON.toJSONString(request));
        ApiResult<PageInfo<FieldInfoDTO>> apiResult = new ApiResult<>();
        apiResult.setCode("200");
        apiResult.setSuccess(true);
        try {
            if (StringUtils.isBlank(request.getTenantId())) {
                throw new QanatBizException("TenantId is empty");
            }
            if (!checkAccessKey(request)) {
                throw new QanatBizException("AK failed");
            }
            DatasourceRequest dsReq = new DatasourceRequest();
            dsReq.setTenantId(request.getTenantId());
            dsReq.setDsName(request.getDsName());
            dsReq.setDbName(request.getDbName());
            dsReq.setTableName(request.getTableName());
            DataResult<PageInfo<DsFieldInfoDTO>> queryResult = dsInfoService.listDsFields4Page(dsReq);
            if (queryResult != null 
            		&& queryResult.getSuccess() 
            		&& queryResult.getData() != null 
            		&& CollectionUtils.isNotEmpty(queryResult.getData().getList())) {
                List<FieldInfoDTO> dtoList = new ArrayList<>();
            	for (DsFieldInfoDTO innerDto : queryResult.getData().getList()) {
            		FieldInfoDTO outerDto = new FieldInfoDTO();
            		BeanUtils.copyProperties(innerDto, outerDto);
            		dtoList.add(outerDto);
            	}
            	PageInfo<FieldInfoDTO> pageInfoDTO = new PageInfo<>();
            	pageInfoDTO.setList(dtoList);
            	pageInfoDTO.setPageNum(queryResult.getData().getPageNum());
            	pageInfoDTO.setPages(queryResult.getData().getPages());
            	pageInfoDTO.setPageSize(queryResult.getData().getPageSize());
            	pageInfoDTO.setTotal(queryResult.getData().getTotal());
            	apiResult.setData(pageInfoDTO);
            }
        } catch(Exception e) {
            log.error("listDsFields4Page failed, error={}", e.getMessage(), e);
            apiResult.setSuccess(false);
            apiResult.setCode("500");
            apiResult.setMessage(e.getMessage());
        }
        return apiResult;
    }
    
    @Override
    public ApiResult<DsInfoDTO> getTableNameByObject(DsInfoQeuryRequest request) {
        log.debug("getTableNameByObject({})", JSON.toJSONString(request));
        ApiResult<DsInfoDTO> apiResult = new ApiResult<>();
        apiResult.setCode("200");
        apiResult.setSuccess(true);
        try {
            if (StringUtils.isBlank(request.getTenantId())) {
                throw new QanatBizException("TenantId is empty");
            }
            if (!checkAccessKey(request)) {
                throw new QanatBizException("AK failed");
            }
        	DsInfoDTO dto = new DsInfoDTO();
        	dto.setTableName("dwd_" + request.getDsUniqueName());
        	
        	DatasourceExample example = new DatasourceExample();
        	example.createCriteria().andIsDeletedEqualTo(0L).andTableNameEqualTo("dwd_" + request.getDsUniqueName()).andTenantIdEqualTo(request.getTenantId());
        	List<Datasource> dsList = datasourceMapper.selectByExample(example);
        	if (CollectionUtils.isNotEmpty(dsList)) {
        		dto.setPkFields(dsList.get(0).getPkFields());
        	} else {
        		dto.setPkFields("id");
        	}
        	apiResult.setData(dto);
        } catch(Exception e) {
            log.error("getTableNameByObject failed, error={}", e.getMessage(), e);
            apiResult.setSuccess(false);
            apiResult.setCode("500");
            apiResult.setMessage(e.getMessage());
        }
        return apiResult;
    }
}
