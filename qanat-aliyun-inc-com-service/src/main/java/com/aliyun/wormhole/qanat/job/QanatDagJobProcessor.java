package com.aliyun.wormhole.qanat.job;

import java.net.InetAddress;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.common.sdk.common.JobInstanceDetail;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.Dag;
import com.aliyun.wormhole.qanat.api.dag.DagInstStatus;
import com.aliyun.wormhole.qanat.api.dag.Node;
import com.aliyun.wormhole.qanat.api.dag.NodeExecType;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.service.TaskService;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfoWithBLOBs;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import com.aliyun.wormhole.qanat.service.dag.DagService;
import com.aliyun.wormhole.qanat.service.schedulerx.SchedulerXJobService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * SchedulerX2版本的DAG调度入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatDagJobProcessor extends MapJobProcessor {
    /**
     * 设置并发运行策略子任务名称
     */
    private static String subTaskName = "qanatTaskService";
    
    @Resource
    private SchedulerXJobService schedulerXJobService;
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;
    
    @Resource
    private TaskInfoMapper taskInfoMapper;
    
    @Resource
    private TaskService taskService;
    
    @Resource
    private DagService dagService;

    @Override
    public ProcessResult process(JobContext context) {
        String taskName = context.getTaskName();
        Long jobInstId = context.getJobInstanceId();
        if (isRootTask(context)) {
            log.info("Qanat Dag Job[{}] start.", taskName);
            try {
                String jobParams = context.getJobParameters();
                JSONObject jobParamsJson = JSON.parseObject(jobParams);
                Long taskId = jobParamsJson.getLong("taskId");
                TaskInfoWithBLOBs task = taskInfoMapper.selectByPrimaryKey(taskId);
                Dag dag = dagService.getDagByJson(task.getDag());
                
                String instParams = context.getInstanceParameters();
                JSONObject instParamsJson = JSON.parseObject(instParams);
                if (instParamsJson == null) {
                    instParamsJson = JSON.parseObject(jobParams);
                }
                String operator = instParamsJson.getString("operator");
                String requestId = instParamsJson.getString("requestId");
                //更新jobParam
                Map<String, Object> instParamsMap = new HashMap<>();
//                instParamsMap.putAll(instParamsJson);
                instParamsMap.put("requestId", requestId);
                instParamsMap.put("operator", operator);
                instParamsMap.put("taskId", taskId);
                schedulerXJobService.updateNodeJob(task.getTenantId(), task.getAppName(), context.getJobId(), instParamsMap);
                
                TaskInstance taskInst = new TaskInstance();
                Date startTime = new Date();
                taskInst.setCreateEmpid(operator);
                taskInst.setGmtCreate(startTime);
                taskInst.setGmtModified(startTime);
                taskInst.setModifyEmpid(operator);
                taskInst.setOperator(operator);
                taskInst.setStartTime(startTime);
                taskInst.setStatus(DagInstStatus.EXECUTING.getCode().byteValue());
                taskInst.setTaskId(taskId);
                taskInst.setExecParam(JSON.toJSONString(instParamsMap));
                taskInst.setExternalId(context.getJobId()  + "");
                taskInst.setExternalInstId(context.getJobInstanceId() + "");
                taskInst.setTaskName(dag.getId());
                taskInst.setHostAddr(InetAddress.getLocalHost().getHostAddress());
                taskInst.setAppName(task.getAppName());
                taskInst.setTenantId(task.getTenantId());
                taskInstanceMapper.insert(taskInst);
                Long taskInstId = taskInst.getId();
                
                List<Node> headList = dag.getHeadList();
                Map<String, Node> nodeMap = null;
                if (CollectionUtils.isNotEmpty(dag.getNodeList())) {
                    nodeMap = dag.getNodeList().stream().collect(Collectors.toMap(Node::getId, Function.identity()));
                }
                for (Node head : headList) {
                    List<Map<String, Object>> mapList = new ArrayList<>();
                    Map<String, Object> mapData = new HashMap<>();
                    mapList.add(mapData);
                    mapData.put("requestId", requestId);
                    mapData.put("operator", operator);
                    mapData.put("taskId", taskId);
                    mapData.put("taskInstId", taskInstId);
                    mapData.put("node", head);
                    mapData.put("tenantId",  task.getTenantId());
                    mapData.put("appName", task.getAppName());
                    ProcessResult result = map(mapList, subTaskName);
                    if (result.getStatus().equals(InstanceStatus.SUCCESS)) {
                        nodeMap.remove(head.getId());
                    }
                }
                while (nodeMap.size() > 0) {
                    boolean hasFailed = false;
                    for(Node nd : dag.getNodeList()) {
                        Node node = nodeMap.get(nd.getId());
                        if (node == null) {
                            continue;
                        }
                        List<String> prevList = node.getPrevNodeList();
                        if (taskService.checkIfPrevFailed(taskInstId, prevList)) {
                            hasFailed = true;
                            break;
                        }
                        if (taskService.checkIfPrevReady(taskInstId, prevList)) {
                            List<Map<String, Object>> mapList = new ArrayList<>();
                            Map<String, Object> mapData = new HashMap<>();
                            mapList.add(mapData);
                            mapData.put("requestId", requestId);
                            mapData.put("operator", operator);
                            mapData.put("taskId", taskId);
                            mapData.put("taskInstId", taskInstId);
                            mapData.put("node", node);
                            mapData.put("tenantId",  task.getTenantId());
                            mapData.put("appName", task.getAppName());
                            ProcessResult result = map(mapList, subTaskName);
                            if (result.getStatus().equals(InstanceStatus.SUCCESS)) {
                                nodeMap.remove(nd.getId());
                            }
                        }
                    }
                    if (hasFailed) {
                        break;
                    }
                }
                //任务分发完毕后，定时刷新判断dag整体运行状态
                Thread.sleep(30000);//休息30s，等待最后一个子任务分发
                DagInstStatus status = null;
                while (true) {
                    if (taskService.checkIfDagFinished(taskInstId)) {
                        status = DagInstStatus.SUCCESS;
                        break;
                    } else if (taskService.checkIfDagFailed(taskInstId)) {
                        status = DagInstStatus.FAILED;
                        break;
                    } else {
                        Thread.sleep(10000);//10s刷新一次
                        continue;
                    }
                }
                Date endTime = new Date();
                TaskInstance taskInstUpd = new TaskInstance();
                taskInstUpd.setId(taskInstId);
                taskInstUpd.setGmtModified(endTime);
                taskInstUpd.setEndTime(endTime);
                taskInstUpd.setModifyEmpid(operator);
                taskInstUpd.setStatus(status.getCode().byteValue());
                taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
            } catch (QanatBizException e) {
                log.error("Dag任务调度异常:{}", e.getMessage());
                return new ProcessResult(false, e.getMessage());
            } catch (Exception e) {
                log.error("Dag任务调度异常", e);
                return new ProcessResult(false, e.getMessage());
            }
        } else if (taskName.equals(subTaskName)) {
            Map<String, Object> mapData = (Map<String, Object>)context.getTask();
            String requestId = (String)mapData.get("requestId");
            String operator = (String)mapData.get("operator");
            Long taskId = (Long)mapData.get("taskId");
            Long taskInstId = (Long)mapData.get("taskInstId");
            Node node = (Node)mapData.get("node");
            String tenantId = (String)mapData.get("tenantId");
            String appName = (String)mapData.get("appName");

            TaskInfoWithBLOBs task = taskInfoMapper.selectByPrimaryKey(taskId);
            JSONArray subTaskArray = JSON.parseArray(task.getSubTasks());
            List<?> subTaskObjects = subTaskArray.stream().filter(subTask -> ((JSONObject)subTask).getString("nodeId").equals(node.getId())).collect(Collectors.toList());
            JSONObject subTaskObject = (JSONObject)subTaskObjects.get(0);
            Long subJobId = subTaskObject.getLong("jobId");
            log.info("start subTask exec, subJobId:{}", subJobId);
        	try {
	            if (node.getExecType().equals(NodeExecType.JOB)) {
	                Map<String, Object> instParamsMap = new HashMap<>();
	                instParamsMap.put("requestId", requestId);
	                instParamsMap.put("operator", operator);
	                instParamsMap.put("taskInstId", taskInstId);
	                node.setNextNodeList(null);
	                instParamsMap.put("node", node);
	                instParamsMap.put("subJobId", subJobId);
	                instParamsMap.put("taskId", taskId);
	                instParamsMap.put("tenantId", tenantId);
	                instParamsMap.put("appName", appName);
	
	                TaskInstance taskInst = new TaskInstance();
	                Date startTime = new Date();
	                taskInst.setCreateEmpid(operator);
	                taskInst.setGmtCreate(startTime);
	                taskInst.setGmtModified(startTime);
	                taskInst.setModifyEmpid(operator);
	                taskInst.setOperator(operator);
	                taskInst.setStartTime(startTime);
	                taskInst.setStatus(DagInstStatus.INIT.getCode().byteValue());
	                taskInst.setTaskId(taskId);
	                taskInst.setExternalId(subJobId + "");
	                taskInst.setTaskName(node.getId());
	                taskInst.setParentInstanceId(taskInstId);
	                taskInst.setTaskCommand(node.getExecType() + ":" + node.getAction());
	                taskInst.setNodeAction(node.getNodeAction().toString());
	                taskInst.setAppName(appName);
	                taskInst.setTenantId(tenantId);
	                
	                taskInstanceMapper.insert(taskInst);
	                Long subTaskInstId = taskInst.getId();
	                instParamsMap.put("subTaskInstId", subTaskInstId);
	                instParamsMap.put("subJobInstId", jobInstId);
	                TaskInstance taskInstUpdImmidiately = new TaskInstance();
	                taskInstUpdImmidiately.setId(subTaskInstId);
	                taskInstUpdImmidiately.setExecParam(JSON.toJSONString(instParamsMap));
	                taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpdImmidiately);
	                //更新jobParam
	                schedulerXJobService.updateNodeJob(tenantId, appName, subJobId, instParamsMap);
	                log.info("updateNodeJob({},{},{}) finished", tenantId, appName, subJobId);
	                
	                //定时任务只更新job参数，不立即执行
	                if (StringUtils.isNotBlank(node.getTimeExpression())) {
	                    return new ProcessResult(true);
	                }
	
	                int retries = 3;
	                boolean failed =false;
	                while (retries > 0) {
	                    DataResult<Long> result = schedulerXJobService.runJob(tenantId, appName, subJobId, instParamsMap);
	                    retries--;
	                    int getJobInstDetailCnt = 0;
	                    boolean jobHang = false;
	                    while (true) {
	                        DataResult<JobInstanceDetail> jobInstDetailResult = schedulerXJobService.getJobInstDetail(tenantId, appName, subJobId, result.getData());
	                        getJobInstDetailCnt++;
	                        if (jobInstDetailResult.getSuccess()) {
	                            JobInstanceDetail jobInst = jobInstDetailResult.getData();
	                            if (jobInst.getStatus() == 4) {//成功
	                                failed = false;
	                                return new ProcessResult(true);
	                            }
	                            if (jobInst.getStatus() == 5 || jobInst.getStatus() == 9) {//失败 || 拒绝
	                                failed = true;
	                                try {
	                                    Thread.sleep(10000);//10s
	                                } catch (InterruptedException e) {
	                                }
	                                break;
	                            }
	                        }
	                        if (getJobInstDetailCnt == 3600) {
	                            jobHang = true;
	                            break;
	                        }
	                        try {
	                            Thread.sleep(10000);//10s
	                        } catch (InterruptedException e) {
	                        }
	                    }
	                    if (jobHang) {
	                        log.error("job instance[{}] is hanging", result.getData());
	                        break;
	                    }
	                }
	                if (failed) {
	                    TaskInstance taskInstUpd = new TaskInstance();
	                    taskInstUpd.setId(Long.valueOf(subTaskInstId));
	                    taskInstUpd.setGmtModified(new Date());
	                    taskInstUpd.setEndTime(new Date());
	                    taskInstUpd.setModifyEmpid(operator);
	                    taskInstUpd.setStatus(DagInstStatus.FAILED.getCode().byteValue());
	                    taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
	                    return new ProcessResult(false, subJobId + " is failed");
	                }
	                log.info("finish subTask exec, subJobId:{}", subJobId);
	            }
            } catch (Exception e) {
                log.error("Dag子任务[{}]调度异常:{}", subJobId, e.getMessage(), e);
                return new ProcessResult(false, e.getMessage());
            }
            return new ProcessResult(true);
        }
        return new ProcessResult(true);
    }
    
    @Override
    public void kill(JobContext context) {
        // TODO Auto-generated method stub
    }
}