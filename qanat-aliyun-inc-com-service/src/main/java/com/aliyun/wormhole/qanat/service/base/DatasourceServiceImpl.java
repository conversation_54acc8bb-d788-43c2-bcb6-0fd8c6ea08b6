package com.aliyun.wormhole.qanat.service.base;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastsql.DbType;
import com.alibaba.fastsql.sql.SQLUtils;
import com.alibaba.fastsql.sql.ast.SQLStatement;
import com.alibaba.fastsql.sql.ast.statement.SQLColumnDefinition;
import com.alibaba.fastsql.sql.ast.statement.SQLCreateTableStatement;
import com.alibaba.fastsql.sql.dialect.mysql.ast.statement.MySqlCreateTableStatement;
import com.alibaba.fastsql.sql.dialect.odps.ast.OdpsCreateTableStatement;
import com.alibaba.fastsql.sql.visitor.SchemaStatVisitor;
import com.alibaba.fastsql.stat.TableStat.Column;
import com.alibaba.security.SecurityUtil;
import com.aliyun.tag.api.request.ListTagByObjectRequest;
import com.aliyun.tag.api.request.dictionary.CreateEnumBatchRequest;
import com.aliyun.tag.api.request.dictionary.CreateEnumBatchRequest.CreateEnumVO;
import com.aliyun.tag.api.request.object.CreateObjectTypeRequest;
import com.aliyun.tag.api.request.object.CreateRichObjectRequest;
import com.aliyun.tag.api.request.object.GetObjectRequest;
import com.aliyun.tag.api.request.object.GetObjectTypeRequest;
import com.aliyun.tag.api.request.object.ListFieldByObjectRequest;
import com.aliyun.tag.api.request.object.CreateRichObjectRequest.TagMetaParam;
import com.aliyun.tag.api.request.object.GetObjectByCodeRequest;
import com.aliyun.tag.api.response.BaseResponse;
import com.aliyun.tag.api.response.DataResponse;
import com.aliyun.tag.api.response.ListResponse;
import com.aliyun.tag.api.service.IOperateEnumService;
import com.aliyun.tag.api.service.IOperateObjectMetaService;
import com.aliyun.tag.api.service.IQueryObjectMetaService;
import com.aliyun.tag.api.service.IQueryTagMetaService;
import com.aliyun.tag.api.vo.ObjectExtendVO;
import com.aliyun.tag.api.vo.ObjectTypeBaseVO;
import com.aliyun.tag.api.vo.ObjectVO;
import com.aliyun.tag.api.vo.ObjectWithFieldVO;
import com.aliyun.tag.api.vo.SimpleTagVO;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.DataSourceType;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.DatasourceDTO;
import com.aliyun.wormhole.qanat.api.dto.DatasourceRequest;
import com.aliyun.wormhole.qanat.api.dto.DsFieldInfoDTO;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.KafkaManagementService;
import com.aliyun.wormhole.qanat.dal.domain.AppInfo;
import com.aliyun.wormhole.qanat.dal.domain.AppInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldRelation;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.DsRelation;
import com.aliyun.wormhole.qanat.dal.domain.DsRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfo;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.AppInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsFieldInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsFieldRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TenantInfoMapper;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.service.odps.OdpsClient;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.CaseFormat;
import com.taobao.tddl.client.jdbc.TDataSource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 数据源服务service
 * <AUTHOR>
 * 2019年7月23日
 */
@Slf4j
@Component
@HSFProvider(serviceInterface = DatasourceService.class)
public class DatasourceServiceImpl implements DatasourceService {

	@Resource
    private DatasourceMapper datasourceMapper;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private DsFieldInfoMapper dsFieldInfoMapper;

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @HSFConsumer(clientTimeout=30000)
    private IQueryTagMetaService mdpService;

    @HSFConsumer(clientTimeout=30000)
    private IQueryObjectMetaService queryObjectMetaService;

    @HSFConsumer(clientTimeout=30000)
    private IOperateObjectMetaService operateObjectMetaService;
    
    @Resource
    private DsRelationMapper dsRelationMapper;
    
    @Resource
    private TenantInfoMapper tenantInfoMapper;
    
    @Resource
    private DsFieldRelationMapper dsFieldRelationMapper;
    
	@HSFConsumer(clientTimeout=30000)
    private IOperateEnumService operateEnumService;
    
    @Resource
    private KafkaManagementService kafkaManagementService;
    
    @Value("${mdp.api.domainCode}")
    private String domainCode;
    
    @Value("${mdp.api.domainAk}")
    private String domainAk;
    
    // 新增OXS地域感知配置
    @Value("${region.type:}")
    private String regionType;

    @Value("${environment.type:}")
    private String environmentType;

    @Value("${qanat.db.oxs.enabled:false}")
    private boolean oxsEnabled;
    
    @Resource
    private AppInfoMapper appInfoMapper;

    @Override
    public DataResult<PageInfo<DatasourceDTO>> list4Page(DatasourceRequest request) {
        log.info("start list4Page({})", request);
        DataResult<PageInfo<DatasourceDTO>> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	if (StringUtils.isBlank(request.getTenantId())) {
        		throw new QanatBizException("tenantId is empty");
        	}
            DatasourceExample example = new DatasourceExample();
            example.setOrderByClause("gmt_modified desc");
            DatasourceExample.Criteria criteria = example.createCriteria();
            criteria.andIsDeletedEqualTo(0L);
            criteria.andTenantIdEqualTo(request.getTenantId());
            
            if (StringUtils.isNotBlank(request.getDsNameFuzzy())) {
            	criteria.andDsNameLike("%" + request.getDsNameFuzzy() + "%");
            }
            if (StringUtils.isNotBlank(request.getDsName())) {
            	criteria.andDsNameEqualTo(request.getDsName());
            }
            if (StringUtils.isNotBlank(request.getDbName())) {
            	criteria.andDbNameEqualTo(request.getDbName());
            }
            if (StringUtils.isNotBlank(request.getTableName())) {
            	criteria.andTableNameEqualTo(request.getTableName());
            }
            if (StringUtils.isNotBlank(request.getObjectType())) {
            	criteria.andObjectTypeEqualTo(request.getObjectType());
            }
            if (StringUtils.isNotBlank(request.getDsUniqueName())) {
            	criteria.andDsUniqueNameEqualTo(request.getDsUniqueName());
            }
            if (StringUtils.isNotBlank(request.getDsType())) {
            	criteria.andDsTypeEqualTo(request.getDsType());
            }
            PageHelper.startPage(request.getPageNum(), request.getPageSize());
            List<Datasource> dsList = datasourceMapper.selectByExample(example);
            PageInfo<Datasource> pageInfoDO = new PageInfo<Datasource>(dsList);
            List<DatasourceDTO> dtoList = new ArrayList<>();
            if (pageInfoDO != null && CollectionUtils.isNotEmpty(pageInfoDO.getList())) {
            	for (Datasource dsDO : pageInfoDO.getList()) {
            		DatasourceDTO dsDTO = new DatasourceDTO();
            		BeanUtils.copyProperties(dsDO, dsDTO);
            		dtoList.add(dsDTO);
            	}
            }
        	PageInfo<DatasourceDTO> pageInfoDTO = new PageInfo<>();
        	pageInfoDTO.setList(dtoList);
        	pageInfoDTO.setPageNum(pageInfoDO.getPageNum());
        	pageInfoDTO.setPages(pageInfoDO.getPages());
        	pageInfoDTO.setPageSize(pageInfoDO.getPageSize());
        	pageInfoDTO.setTotal(pageInfoDO.getTotal());
        	result.setData(pageInfoDTO);
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("list4Page failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("list4Page failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public DataResult<PageInfo<DsFieldInfoDTO>> listDsFields4Page(DatasourceRequest request) {
        log.info("start listDsFields4Page({})", request);
        DataResult<PageInfo<DsFieldInfoDTO>> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	if (StringUtils.isBlank(request.getTenantId())) {
        		throw new QanatBizException("tenantId is empty");
        	}

            String dsName = null;
            if (StringUtils.isNotBlank(request.getDsName())) {
            	dsName = request.getDsName();
            } else if (StringUtils.isNotBlank(request.getTableName())) {
            	DatasourceExample example = new DatasourceExample();
            	if (StringUtils.isNotBlank(request.getDbName())) {
            		example.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(request.getTenantId()).andDbNameEqualTo(request.getDbName()).andTableNameEqualTo(request.getTableName());
            	} else {
            		TenantInfoExample tiExample = new TenantInfoExample();
	            	tiExample.createCriteria().andTenantIdEqualTo(request.getTenantId());
	            	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
	            	if (CollectionUtils.isEmpty(tenantList)) {
	            		throw new QanatBizException("tenantId:" + request.getTenantId() + " is not configured");
	            	}
            		example.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(request.getTenantId()).andDbNameEqualTo(tenantList.get(0).getDefaultDw()).andTableNameEqualTo(request.getTableName());
            	}
            	List<Datasource> dsList = datasourceMapper.selectByExample(example);
            	if (CollectionUtils.isEmpty(dsList)) {
            		throw new QanatBizException("can not get dsInfo by tableName:" + request.getTableName());
            	}
            	dsName = dsList.get(0).getDsName();
            } else {
        		throw new QanatBizException("dsName or tableName is neccesary");
            }  

            DsFieldInfoExample dsFieldExample = new DsFieldInfoExample();
            dsFieldExample.setOrderByClause("gmt_modified desc");
            DsFieldInfoExample.Criteria dsFieldCriteria = dsFieldExample.createCriteria();
            dsFieldCriteria.andIsDeletedEqualTo(0L);
            dsFieldCriteria.andTenantIdEqualTo(request.getTenantId());
            dsFieldCriteria.andDsNameEqualTo(dsName);

            List<DsFieldInfo> dsFieldList = dsFieldInfoMapper.selectByExample(dsFieldExample);
            PageInfo<DsFieldInfo> pageInfoDO = new PageInfo<DsFieldInfo>(dsFieldList);
            List<DsFieldInfoDTO> dtoList = new ArrayList<>();
            if (pageInfoDO != null && CollectionUtils.isNotEmpty(pageInfoDO.getList())) {
            	for (DsFieldInfo dsDO : pageInfoDO.getList()) {
            		DsFieldInfoDTO dsDTO = new DsFieldInfoDTO();
            		BeanUtils.copyProperties(dsDO, dsDTO);
            		dtoList.add(dsDTO);
            	}
            }
            
        	PageInfo<DsFieldInfoDTO> pageInfoDTO = new PageInfo<>();
        	pageInfoDTO.setList(dtoList);
        	pageInfoDTO.setPageNum(pageInfoDO.getPageNum());
        	pageInfoDTO.setPages(pageInfoDO.getPages());
        	pageInfoDTO.setPageSize(pageInfoDO.getPageSize());
        	pageInfoDTO.setTotal(pageInfoDO.getTotal());
        	result.setData(pageInfoDTO);
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("listDsFields4Page failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("listDsFields4Page failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public DataResult<Long> createDsInfoFromObject(DatasourceRequest request) {
        log.info("start createDsInfoFromObject({})", request);
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	if (StringUtils.isBlank(request.getTenantId())) {
        		throw new QanatBizException("tenantId is empty");
        	}
        	if (StringUtils.isBlank(request.getDbName())) {
        		throw new QanatBizException("dbName is empty");
        	}
            Date now = new Date();
            Datasource record = new Datasource();
            BeanUtils.copyProperties(request, record);
            record.setCreateEmpid(request.getOperateEmpid());
            record.setGmtCreate(now);
            record.setModifyEmpid(request.getOperateEmpid());
            record.setGmtModified(now);
            record.setIsDeleted(0L);
            record.setDbName(request.getDbName());
            record.setTenantId(request.getTenantId());
            record.setDsUniqueName(request.getDsUniqueName());
            record.setTableName(request.getTableName());
            record.setObjectType(request.getObjectType());
            record.setSysType(request.getSysType());
            record.setMeta(request.getMeta());

            DsFieldInfoExample example = new DsFieldInfoExample();
    		example.createCriteria().andDsNameEqualTo(record.getDsName()).andTenantIdEqualTo(record.getTenantId()).andIsDeletedEqualTo(0L);
    		int delCnt = dsFieldInfoMapper.deleteByExample(example);
    		log.info("tenantId[{}] fields in dsName[{}] are deleted, cnt={}", record.getTenantId(), record.getDsName(), delCnt);
			//默认插入id字段
            JSONObject metaJson = JSON.parseObject(request.getMeta());
			String pkField = metaJson.getJSONObject("extParam").getJSONObject("objectVO").getString("objectIdField");
			String pkFieldType = metaJson.getJSONObject("extParam").getJSONObject("objectVO").getString("objectIdType");
			DsFieldInfo idField = new DsFieldInfo();
			record.setPkFields(pkField);
			idField.setFieldName(pkField);
			idField.setFieldUniqueName(pkField);
			idField.setFieldType(pkFieldType);
			idField.setFieldDesc(pkField);
			idField.setTenantId(record.getTenantId());
			idField.setIsDeleted(0L);
			idField.setIsPk(Byte.valueOf("1"));
			idField.setDbName(record.getDbName());
			idField.setDsName(record.getDsName());
			idField.setDsUniqueName(record.getDsUniqueName());
			idField.setCreateEmpid(record.getModifyEmpid());
			idField.setGmtCreate(now);
			idField.setModifyEmpid(record.getModifyEmpid());
			idField.setGmtModified(now);
			dsFieldInfoMapper.insert(idField);

        	JSONArray fieldsJsonArray = metaJson.getJSONArray("fields");
            if (fieldsJsonArray != null && fieldsJsonArray.size() > 0) {
            	for (int i = 0; i < fieldsJsonArray.size(); i++) {
            		SimpleTagVO tag = fieldsJsonArray.getObject(i, SimpleTagVO.class);
					DsFieldInfo field = new DsFieldInfo();
					field.setFieldName(tag.getCode());
					field.setFieldUniqueName(tag.getUniqueCode());
					field.setFieldType(transformMdpTypeToAdbType(tag.getDataType()));
					field.setFieldDesc(tag.getName());
					field.setTenantId(record.getTenantId());
					field.setIsDeleted(0L);
					field.setIsPk(Byte.valueOf("0"));
					field.setDbName(record.getDbName());
					field.setDsUniqueName(record.getDsUniqueName());
					field.setDsName(record.getDsName());
					field.setCreateEmpid(record.getModifyEmpid());
					field.setGmtCreate(now);
					field.setModifyEmpid(record.getModifyEmpid());
					field.setGmtModified(now);
					if (tag.getIsQuote() != null && tag.getIsQuote() == 1) {
						field.setSysType("ref");
					}
					if (tag.getExtInfo() != null) {
						field.setExtInfo(tag.getExtInfo());
					}
					if (tag.getIsMultiple() != null && tag.getIsMultiple() == 1) {
						field.setDataType("multivalue");
					}
					dsFieldInfoMapper.insert(field);
            	}
            } else {
				ListTagByObjectRequest req = new ListTagByObjectRequest();
				req.setObjectType(record.getObjectType());
				req.setObjectUniqueCode(record.getDsUniqueName());
				try {
					ListResponse<SimpleTagVO> mdpResult = mdpService.listTagByObject(req);
					if (mdpResult != null && CollectionUtils.isNotEmpty(mdpResult.getData())) {
						for (SimpleTagVO tag : mdpResult.getData()) {
							DsFieldInfo field = new DsFieldInfo();
							field.setFieldName(tag.getCode());
							field.setFieldUniqueName(tag.getUniqueCode());
							field.setFieldType(transformMdpTypeToAdbType(tag.getDataType()));
							field.setFieldDesc(tag.getName());
							field.setTenantId(record.getTenantId());
							field.setIsDeleted(0L);
							field.setIsPk(Byte.valueOf("0"));
							field.setDbName(record.getDbName());
							field.setDsUniqueName(record.getDsUniqueName());
							field.setDsName(record.getDsName());
							field.setCreateEmpid(record.getModifyEmpid());
							field.setGmtCreate(now);
							field.setModifyEmpid(record.getModifyEmpid());
							field.setGmtModified(now);
							if (tag.getIsQuote() != null && tag.getIsQuote() == 1) {
								field.setSysType("ref");
							}
							if (tag.getExtInfo() != null) {
								field.setExtInfo(tag.getExtInfo());
							}
							if (tag.getIsMultiple() != null && tag.getIsMultiple() == 1) {
								field.setDataType("multivalue");
							}
							dsFieldInfoMapper.insert(field);
						}
					}
				} catch (Exception e) {
					log.error("listTagByObject[{}] failed", record.getDsUniqueName(), e);
				}
            }
            
            datasourceMapper.insert(record);
            
        	String topicName = metaJson.getJSONObject("extParam").getString("quickTopic");
        	String topicNameBatch = metaJson.getJSONObject("extParam").getString("slowTopic");
        	String resourceGroup = metaJson.getJSONObject("extParam").getJSONObject("objectVO").getString("resourceGroup");
        	String tddlTableName = "tag_meta_tag_object_biz_relation";
        	String odpsTableName = "s_tag_meta_tag_object_biz_relation_aliyun_tag_m_app_new";
        	if (StringUtils.isNotBlank(resourceGroup) && !"default".equalsIgnoreCase(resourceGroup)) {
        		tddlTableName = "tag_meta_tag_object_biz_relation_" + resourceGroup;
        		odpsTableName = "s_tag_meta_tag_object_biz_relation_" + resourceGroup + "_aliyun_tag_m_app_new";
        	}
        	JSONObject appKafkaConf = kafkaManagementService.getKafkaConfByAppName(request.getTenantId(), request.getAppName());
        	try {
            	Datasource drcDs = new Datasource();
                drcDs.setDsName("kafka_" + topicName);
                drcDs.setDsType("kafka");
                drcDs.setTableName(topicName);
                drcDs.setIsDeleted(0L);
                drcDs.setDbName(appKafkaConf.getString("dbName"));
                drcDs.setGmtModified(new Date());
                drcDs.setModifyEmpid(request.getOperateEmpid());
                drcDs.setGmtCreate(new Date());
                drcDs.setCreateEmpid(request.getOperateEmpid());
                drcDs.setTenantId(request.getTenantId());
                datasourceMapper.insert(drcDs);
        	} catch(Exception e) {}
        	try {
            	Datasource drcDs = new Datasource();
                drcDs.setDsName("kafka_" + topicNameBatch);
                drcDs.setDsType("kafka");
                drcDs.setTableName(topicNameBatch);
                drcDs.setIsDeleted(0L);
                drcDs.setDbName(appKafkaConf.getString("dbName"));
                drcDs.setGmtModified(new Date());
                drcDs.setModifyEmpid(request.getOperateEmpid());
                drcDs.setGmtCreate(new Date());
                drcDs.setCreateEmpid(request.getOperateEmpid());
                drcDs.setTenantId(request.getTenantId());
                datasourceMapper.insert(drcDs);
        	} catch(Exception e) {}
        	try {
            	Datasource tddlDs = new Datasource();
            	tddlDs.setDsName("tddl_" + tddlTableName);
            	tddlDs.setDsType("tddl");
            	tddlDs.setTableName(tddlTableName);
            	tddlDs.setIsDeleted(0L);
            	tddlDs.setDbName("mdp_tddl");
            	tddlDs.setGmtModified(new Date());
            	tddlDs.setModifyEmpid(request.getOperateEmpid());
            	tddlDs.setGmtCreate(new Date());
            	tddlDs.setCreateEmpid(request.getOperateEmpid());
            	tddlDs.setTenantId(request.getTenantId());
                datasourceMapper.insert(tddlDs);
        	} catch(Exception e) {}
        	try {
	        	DsRelation newDsRel = new DsRelation();
				newDsRel.setCreateEmpid(request.getOperateEmpid());
				newDsRel.setDstDsName(record.getDsName());
				newDsRel.setGmtCreate(now);
				newDsRel.setGmtModified(now);
				newDsRel.setIsDeleted(0L);
				newDsRel.setModifyEmpid(request.getOperateEmpid());
				newDsRel.setRelationType("incr");
				newDsRel.setSrcDsName("kafka_" + topicName);
				newDsRel.setTenantId(request.getTenantId());
				int insCnt = dsRelationMapper.insert(newDsRel);
				log.info("insert [{}] dsRelations[{},{},{}]", insCnt, topicName, record.getDsName(), "incr");
	    	} catch(Exception e) {}
        	try {
        		DsRelation newDsRel = new DsRelation();
				newDsRel.setCreateEmpid(request.getOperateEmpid());
				newDsRel.setDstDsName(record.getDsName());
				newDsRel.setGmtCreate(now);
				newDsRel.setGmtModified(now);
				newDsRel.setIsDeleted(0L);
				newDsRel.setModifyEmpid(request.getOperateEmpid());
				newDsRel.setRelationType("batch");
				newDsRel.setSrcDsName("kafka_" + topicNameBatch);
				newDsRel.setTenantId(request.getTenantId());
				int insCnt = dsRelationMapper.insert(newDsRel);
				log.info("insert [{}] dsRelations[{},{},{}]", insCnt, topicNameBatch, record.getDsName(), "batch");
        	} catch(Exception e) {}
            String storeType = metaJson.getString("storeType");
            if ("slot".equalsIgnoreCase(storeType)) {
            	JSONObject sltoMetaJson = metaJson.getJSONObject("slotMeta");
            	String offlineTableName = sltoMetaJson.getString("table");
    			String onlineTable = sltoMetaJson.getString("onlineTable").toString();
    			try {
                	Datasource slotOds = new Datasource();
                	slotOds.setDsName("adb3_" + onlineTable);
                	slotOds.setDsType("adb3");
                	slotOds.setTableName(onlineTable);
                	slotOds.setIsDeleted(0L);
                	slotOds.setDbName("mdp_rds");
                	slotOds.setGmtModified(new Date());
                	slotOds.setModifyEmpid("admin");
                	slotOds.setGmtCreate(new Date());
                	slotOds.setCreateEmpid("admin");
                	slotOds.setTenantId(request.getTenantId());
                    datasourceMapper.insert(slotOds);
            	} catch(Exception e) {}
    			try {
    				DsRelation dsRel = new DsRelation();
    				dsRel.setCreateEmpid("admin");
    				dsRel.setDstDsName("adb3_" + onlineTable);
    				dsRel.setGmtCreate(now);
    				dsRel.setGmtModified(now);
    				dsRel.setIsDeleted(0L);
    				dsRel.setModifyEmpid("admin");
    				dsRel.setRelationType("ods");
    				dsRel.setSrcDsName(record.getDsName());
    				dsRel.setTenantId(request.getTenantId());
    				dsRelationMapper.insert(dsRel);
    			} catch(Exception e) {}

            	try {
                	Datasource slotOdps = new Datasource();
                	slotOdps.setDsName("odps_" + offlineTableName);
                	slotOdps.setDsType("odps");
                	slotOdps.setTableName(offlineTableName);
                	slotOdps.setIsDeleted(0L);
                	slotOdps.setDbName("mdp_odps");
                	slotOdps.setGmtModified(new Date());
                	slotOdps.setModifyEmpid("admin");
                	slotOdps.setGmtCreate(new Date());
                	slotOdps.setCreateEmpid("admin");
                	slotOdps.setTenantId(request.getTenantId());
                    datasourceMapper.insert(slotOdps);
            	} catch(Exception e) {}
    			try {
    				DsRelation dsRel = new DsRelation();
    				dsRel.setCreateEmpid("admin");
    				dsRel.setDstDsName("odps_" + offlineTableName);
    				dsRel.setGmtCreate(now);
    				dsRel.setGmtModified(now);
    				dsRel.setIsDeleted(0L);
    				dsRel.setModifyEmpid("admin");
    				dsRel.setRelationType("odps");
    				dsRel.setSrcDsName(record.getDsName());
    				dsRel.setTenantId(request.getTenantId());
    				dsRelationMapper.insert(dsRel);
    			} catch(Exception e) {}
            } else {
	        	try {
	            	Datasource tddlDs = new Datasource();
	            	tddlDs.setDsName("adb3_" + tddlTableName);
	            	tddlDs.setDsType("adb3");
	            	tddlDs.setTableName(tddlTableName);
	            	tddlDs.setIsDeleted(0L);
	            	tddlDs.setDbName(request.getDbName());
	            	tddlDs.setGmtModified(new Date());
	            	tddlDs.setModifyEmpid(request.getOperateEmpid());
	            	tddlDs.setGmtCreate(new Date());
	            	tddlDs.setCreateEmpid(request.getOperateEmpid());
	            	tddlDs.setTenantId(request.getTenantId());
	                datasourceMapper.insert(tddlDs);
	        	} catch(Exception e) {}
	        	try {
	            	Datasource tddlDs = new Datasource();
	            	tddlDs.setDsName("odps_" + odpsTableName);
	            	tddlDs.setDsType("odps");
	            	tddlDs.setTableName(odpsTableName);
	            	tddlDs.setIsDeleted(0L);
	            	tddlDs.setDbName("mdp_odps");
	            	tddlDs.setGmtModified(new Date());
	            	tddlDs.setModifyEmpid(request.getOperateEmpid());
	            	tddlDs.setGmtCreate(new Date());
	            	tddlDs.setCreateEmpid(request.getOperateEmpid());
	            	tddlDs.setTenantId(request.getTenantId());
	                datasourceMapper.insert(tddlDs);
	        	} catch(Exception e) {}
	        	try {
	        		DsRelation newDsRel = new DsRelation();
					newDsRel.setCreateEmpid(request.getOperateEmpid());
					newDsRel.setDstDsName("adb3_" + tddlTableName);
					newDsRel.setGmtCreate(now);
					newDsRel.setGmtModified(now);
					newDsRel.setIsDeleted(0L);
					newDsRel.setModifyEmpid(request.getOperateEmpid());
					newDsRel.setRelationType("ods");
					newDsRel.setSrcDsName(record.getDsName());
					newDsRel.setTenantId(request.getTenantId());
					int insCnt = dsRelationMapper.insert(newDsRel);
					log.info("insert [{}] dsRelations[{},{},{}]", insCnt, record.getDsName(), "adb3_" + tddlTableName, "ods");
	        	} catch(Exception e) {}
	        	try {
	        		DsRelation newDsRel = new DsRelation();
					newDsRel.setCreateEmpid(request.getOperateEmpid());
					newDsRel.setDstDsName("odps_" + odpsTableName);
					newDsRel.setGmtCreate(now);
					newDsRel.setGmtModified(now);
					newDsRel.setIsDeleted(0L);
					newDsRel.setModifyEmpid(request.getOperateEmpid());
					newDsRel.setRelationType("odps");
					newDsRel.setSrcDsName(record.getDsName());
					newDsRel.setTenantId(request.getTenantId());
					int insCnt = dsRelationMapper.insert(newDsRel);
					log.info("insert [{}] dsRelations[{},{},{}]", insCnt, record.getDsName(), "odps_" + odpsTableName, "ods");
		    	} catch(Exception e) {}
            }
            result.setData(record.getId());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("createDsInfoFromObject failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public DataResult<Long> createDatasource(DatasourceRequest request) {
        log.info("start createDatasource({})", request);
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	if (StringUtils.isBlank(request.getTenantId())) {
        		throw new QanatBizException("tenantId is empty");
        	}
        	if (StringUtils.isBlank(request.getDbName())) {
        		throw new QanatBizException("dbName is empty");
        	}
//        	if ("odps".equalsIgnoreCase(request.getDsType()) && StringUtils.isBlank(request.getPkField())) {
//        		throw new QanatBizException("pkField is neccesary when odps");
//        	}
            Date now = new Date();
            Datasource record = new Datasource();
            BeanUtils.copyProperties(request, record);
            
            if (StringUtils.isBlank(request.getDsName())) {
            	record.setDsName(this.getDsName(request.getTenantId(), request.getAppName(), request.getDbName(), request.getTableName()));
            }
            record.setCreateEmpid(request.getOperateEmpid());
            record.setGmtCreate(now);
            record.setModifyEmpid(request.getOperateEmpid());
            record.setGmtModified(now);
            record.setIsDeleted(0L);
            record.setDbName(request.getDbName());
            record.setTenantId(request.getTenantId());
            record.setDsUniqueName(request.getDsUniqueName());
            record.setTableName(request.getTableName());
            record.setObjectType(request.getObjectType());
            record.setSysType(request.getSysType());
            record.setPkFields(request.getPkField());
            if ("odps".equalsIgnoreCase(request.getDsType())) {
            	record.setSysType("metric");
            }
            record.setPredictQph(request.getPredictQph());
            record.setPredictSize(request.getPredictSize());
            record.setWorkTime(request.getWorkTime());
            
            String dbMeta = null;
            DbInfoExample example = new DbInfoExample();
            example.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(request.getDbName()).andTenantIdEqualTo(request.getTenantId());
            List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isNotEmpty(dbs)) {
                DbInfo dbInfo = dbs.get(0);
                dbMeta = dbInfo.getMeta();
            }
            
            setDsMetaDdl(record, request.getDsType(), request.getMeta(), dbMeta);
            rebuildDsFields(record);
            datasourceMapper.insert(record);
            
            if ("obj".equalsIgnoreCase(request.getDsType())) {
            	String topicName = "TOPIC_MDP_" + request.getDsUniqueName() + "_QUICK";
            	String topicNameBatch = "TOPIC_MDP_" + request.getDsUniqueName() + "_SLOW";
            	JSONObject appKafkaConf = kafkaManagementService.getKafkaConfByAppName(request.getTenantId(), request.getAppName());
            	try {
	            	Datasource drcDs = new Datasource();
	                drcDs.setDsName("kafka_" + topicName);
	                drcDs.setDsType("kafka");
	                drcDs.setTableName(topicName);
	                drcDs.setIsDeleted(0L);
	                drcDs.setDbName(appKafkaConf.getString("dbName"));
	                drcDs.setGmtModified(new Date());
	                drcDs.setModifyEmpid(request.getOperateEmpid());
	                drcDs.setGmtCreate(new Date());
	                drcDs.setCreateEmpid(request.getOperateEmpid());
	                drcDs.setTenantId(request.getTenantId());
	                datasourceMapper.insert(drcDs);
            	} catch(Exception e) {}
            	try {
	            	Datasource drcDs = new Datasource();
	                drcDs.setDsName("kafka_" + topicNameBatch);
	                drcDs.setDsType("kafka");
	                drcDs.setTableName(topicNameBatch);
	                drcDs.setIsDeleted(0L);
	                drcDs.setDbName(appKafkaConf.getString("dbName"));
	                drcDs.setGmtModified(new Date());
	                drcDs.setModifyEmpid(request.getOperateEmpid());
	                drcDs.setGmtCreate(new Date());
	                drcDs.setCreateEmpid(request.getOperateEmpid());
	                drcDs.setTenantId(request.getTenantId());
	                datasourceMapper.insert(drcDs);
            	} catch(Exception e) {}
            	
            	DsRelation newDsRel = new DsRelation();
    			newDsRel.setCreateEmpid(request.getOperateEmpid());
    			newDsRel.setDstDsName(record.getDsName());
    			newDsRel.setGmtCreate(now);
    			newDsRel.setGmtModified(now);
    			newDsRel.setIsDeleted(0L);
    			newDsRel.setModifyEmpid(request.getOperateEmpid());
    			newDsRel.setRelationType("incr");
    			newDsRel.setSrcDsName("kafka_" + topicName);
    			newDsRel.setTenantId(request.getTenantId());
    			int insCnt = dsRelationMapper.insert(newDsRel);
    			log.info("insert [{}] dsRelations[{},{},{}]", insCnt, topicName, record.getDsName(), "incr");
    			newDsRel = new DsRelation();
    			newDsRel.setCreateEmpid(request.getOperateEmpid());
    			newDsRel.setDstDsName(record.getDsName());
    			newDsRel.setGmtCreate(now);
    			newDsRel.setGmtModified(now);
    			newDsRel.setIsDeleted(0L);
    			newDsRel.setModifyEmpid(request.getOperateEmpid());
    			newDsRel.setRelationType("batch");
    			newDsRel.setSrcDsName("kafka_" + topicNameBatch);
    			newDsRel.setTenantId(request.getTenantId());
    			insCnt = dsRelationMapper.insert(newDsRel);
    			log.info("insert [{}] dsRelations[{},{},{}]", insCnt, topicNameBatch, record.getDsName(), "batch");
            	newDsRel = new DsRelation();
    			newDsRel.setCreateEmpid(request.getOperateEmpid());
    			newDsRel.setDstDsName("adb3_tag_meta_tag_object_biz_relation");
    			newDsRel.setGmtCreate(now);
    			newDsRel.setGmtModified(now);
    			newDsRel.setIsDeleted(0L);
    			newDsRel.setModifyEmpid(request.getOperateEmpid());
    			newDsRel.setRelationType("ods");
    			newDsRel.setSrcDsName(record.getDsName());
    			newDsRel.setTenantId(request.getTenantId());
    			insCnt = dsRelationMapper.insert(newDsRel);
    			log.info("insert [{}] dsRelations[{},{},{}]", insCnt, record.getDsName(), "adb3_tag_meta_tag_object_biz_relation", "ods");
            }
            
            result.setData(record.getId());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("createDatasource failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<Boolean> rebuildDsInfo(String tenantId, String dsName) {
    	log.info("start rebuidDsInfo({},{})", tenantId, dsName);
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	if (StringUtils.isBlank(tenantId)) {
        		throw new QanatBizException("tenantId is empty");
        	}
            if (StringUtils.isBlank(dsName)) {
                throw new QanatBizException("dsName is empty");
            }
            
            Date now = new Date();
            DatasourceExample example = new DatasourceExample();
            example.createCriteria().andDsNameEqualTo(dsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
            List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isEmpty(dsList)) {
                throw new QanatBizException("Datasource:" + dsName + " doesn't exists!");
            }
            Datasource record = dsList.get(0);
            if (StringUtils.isBlank(record.getDbName())) {
        		throw new QanatBizException("dbName is empty, please set and then save again");
        	}
            if (StringUtils.isBlank(record.getTableName())) {
            	JSONObject dsMetaJson = JSON.parseObject(record.getMeta());
            	record.setTableName(dsMetaJson.getString("table"));
            }
            record.setModifyEmpid("qanat");
            record.setGmtModified(now);
            
            String dbMeta = null;
            DbInfoExample dbExample = new DbInfoExample();
            dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(record.getDbName()).andTenantIdEqualTo(tenantId);
            List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
            if (CollectionUtils.isEmpty(dbs)) {
        		throw new QanatBizException("dbName is not exists");
            }
            DbInfo dbInfo = dbs.get(0);
            dbMeta = dbInfo.getMeta();
            
            setDsMetaDdl(record, record.getDsType(), record.getMeta(), dbMeta);
            rebuildDsFields(record);
            datasourceMapper.updateByPrimaryKeySelective(record);
            
            result.setData(true);
        } catch (Exception e) {
            result.setData(false);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("modifyDatasource failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    @Override
	public Integer updObjectField(String tenantId, String objectType, String dsUniqueName, String fieldName, String tagJson) {
    	log.info("updObjectField({},{},{})", tenantId, dsUniqueName, fieldName);
    	int updCnt = 0;
    	try {
			SimpleTagVO tag = JSON.parseObject(tagJson, SimpleTagVO.class);
			DsFieldInfoExample example = new DsFieldInfoExample();
			example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsUniqueNameEqualTo(dsUniqueName).andFieldUniqueNameEqualTo(fieldName);
			DsFieldInfo record = new DsFieldInfo();
			record.setFieldDesc(tag.getName());
			if (tag.getIsQuote() != null && tag.getIsQuote() == 1) {
				record.setExtInfo(tag.getExtInfo());
				record.setSysType("ref");
			}
			record.setModifyEmpid("mq_mdp");
			record.setGmtModified(new Date());
			updCnt = dsFieldInfoMapper.updateByExampleSelective(record, example);
			log.info("upd cnt={}", updCnt);
		} catch(Exception e) {
			log.error("updObjectField failed,error={}", e.getMessage(), e);
		}
    	return updCnt;
	}

    @Override
	public String getObjectFieldType(String objectType, String dsUniqueName, String fieldName) {
    	ListTagByObjectRequest req = new ListTagByObjectRequest();
		req.setObjectType(objectType);
		req.setObjectUniqueCode(dsUniqueName);
		ListResponse<SimpleTagVO> result = mdpService.listTagByObject(req);
		log.info("listTagByObject({},{})={}",objectType, dsUniqueName, JSON.toJSONString(result));
		if (result != null && CollectionUtils.isNotEmpty(result.getData())) {
			List<SimpleTagVO> tagVoList = result.getData().stream().filter(item -> fieldName.equals(item.getUniqueCode())).collect(Collectors.toList());
			if (CollectionUtils.isEmpty(tagVoList)) {
				throw new QanatBizException("field is not found from mdp");
			}
			SimpleTagVO tag = tagVoList.get(0);
			return tag.getDataType();
		}
		return null;
    }

    @Override
	public Map<String, String> getObjectFieldTypes(String objectType, String dsUniqueName) {
    	ListTagByObjectRequest req = new ListTagByObjectRequest();
    	Map<String, String> fieldTypes = new HashMap<>();
		req.setObjectType(objectType);
		req.setObjectUniqueCode(dsUniqueName);
		ListResponse<SimpleTagVO> result = mdpService.listTagByObject(req);
		log.info("listTagByObject({},{})={}",objectType, dsUniqueName, JSON.toJSONString(result));
		if (result != null && CollectionUtils.isNotEmpty(result.getData())) {
			for (SimpleTagVO tag : result.getData()) {
				fieldTypes.put(tag.getCode(), tag.getDataType());
			}
		}
		return fieldTypes;
    }

    @Override
	public void addObjectField(String tenantId, String dbName, String dsName, String objectType, String dsUniqueName, String fieldName, String tagJson) {
    	log.info("addObjectField({},{},{},{},{},{},{})", tenantId, dbName, dsName, objectType, dsUniqueName, fieldName, tagJson);
    	try {
			SimpleTagVO tag = JSON.parseObject(tagJson, SimpleTagVO.class);
			DsFieldInfo field = new DsFieldInfo();
			field.setFieldName(tag.getCode());
			field.setFieldUniqueName(tag.getUniqueCode());
			field.setFieldType(transformMdpTypeToAdbType(tag.getDataType()));
			field.setFieldDesc(tag.getName());
			field.setTenantId(tenantId);
			field.setIsDeleted(0L);
			field.setIsPk(Byte.valueOf("0"));
			field.setDbName(dbName);
			field.setDsUniqueName(dsUniqueName);
			field.setDsName(dsName);
			field.setCreateEmpid("mq_mdp");
			field.setGmtCreate(new Date());
			field.setModifyEmpid("mq_mdp");
			field.setGmtModified(new Date());
			if (tag.getIsQuote() != null && tag.getIsQuote() == 1) {
				field.setSysType("ref");
			}
			if (tag.getExtInfo() != null) {
				field.setExtInfo(tag.getExtInfo());
			}
			if (tag.getIsMultiple() != null && tag.getIsMultiple() == 1) {
				field.setDataType("multivalue");
			}
			int cnt = dsFieldInfoMapper.insert(field);
			log.info("addObjectField cnt={}", cnt);
		} catch(Exception e) {
			log.error("addObjectField failed,error={}", e.getMessage(), e);
		}
	}

    @Override
	public void deleteObjectField(String tenantId, String dsUniqueName, String fieldName) {
    	log.info("deleteObjectField({},{},{})", tenantId, dsUniqueName, fieldName);
    	try {
			DsFieldInfoExample example = new DsFieldInfoExample();
			example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsUniqueNameEqualTo(dsUniqueName).andFieldUniqueNameEqualTo(fieldName);
			DsFieldInfo record = new DsFieldInfo();
			record.setIsDeleted(1L);
			record.setModifyEmpid("mq_mdp");
			record.setGmtModified(new Date());
			int cnt = dsFieldInfoMapper.updateByExampleSelective(record, example);
			log.info("logic del cnt={}", cnt);
		} catch(Exception e) {
			log.error("addObjectField failed,error={}", e.getMessage(), e);
		}
	}

	private void rebuildDsFields(Datasource record) {
		Date now = new Date();
		DsFieldInfoExample example = new DsFieldInfoExample();
		example.createCriteria().andDsNameEqualTo(record.getDsName()).andTenantIdEqualTo(record.getTenantId()).andIsDeletedEqualTo(0L);
		int delCnt = dsFieldInfoMapper.deleteByExample(example);
		log.info("tenantId[{}] fields in dsName[{}] are deleted, cnt={}", record.getTenantId(), record.getDsName(), delCnt);
		if (record.getDsType().equalsIgnoreCase("obj")) {
			//默认插入id字段
			DsFieldInfo idField = new DsFieldInfo();
			String pkField = getPkFieldByObjectType(record.getTenantId(), record.getObjectType(), record.getDsUniqueName());
			record.setPkFields(pkField.replaceAll("`", ""));
			idField.setFieldName(pkField);
			idField.setFieldUniqueName(pkField);
			idField.setFieldType("bigint");
			idField.setFieldDesc(pkField);
			idField.setTenantId(record.getTenantId());
			idField.setIsDeleted(0L);
			idField.setIsPk(Byte.valueOf("1"));
			idField.setDbName(record.getDbName());
			idField.setDsName(record.getDsName());
			idField.setDsUniqueName(record.getDsUniqueName());
			idField.setCreateEmpid(record.getModifyEmpid());
			idField.setGmtCreate(now);
			idField.setModifyEmpid(record.getModifyEmpid());
			idField.setGmtModified(now);
			dsFieldInfoMapper.insert(idField);
			
			ListTagByObjectRequest req = new ListTagByObjectRequest();
			req.setObjectType(record.getObjectType());
			req.setObjectUniqueCode(record.getDsUniqueName());
			ListResponse<SimpleTagVO> result = mdpService.listTagByObject(req);
			if (result != null && CollectionUtils.isNotEmpty(result.getData())) {
				for (SimpleTagVO tag : result.getData()) {
					DsFieldInfo field = new DsFieldInfo();
					field.setFieldName(tag.getCode());
					field.setFieldUniqueName(tag.getUniqueCode());
					field.setFieldType(transformMdpTypeToAdbType(tag.getDataType()));
					field.setFieldDesc(tag.getName());
					field.setTenantId(record.getTenantId());
					field.setIsDeleted(0L);
					field.setIsPk(Byte.valueOf("0"));
					field.setDbName(record.getDbName());
					field.setDsUniqueName(record.getDsUniqueName());
					field.setDsName(record.getDsName());
					field.setCreateEmpid(record.getModifyEmpid());
					field.setGmtCreate(now);
					field.setModifyEmpid(record.getModifyEmpid());
					field.setGmtModified(now);
					if (tag.getIsQuote() != null && tag.getIsQuote() == 1) {
						field.setSysType("ref");
					}
					if (tag.getExtInfo() != null) {
						field.setExtInfo(tag.getExtInfo());
					}
					if (tag.getIsMultiple() != null && tag.getIsMultiple() == 1) {
						field.setDataType("multivalue");
					}
					dsFieldInfoMapper.insert(field);
				}
			}
		} else {
			if (StringUtils.isBlank(record.getMeta())) {
				log.info("ds[" + record.getDsName() + "] has no meta");
				return;
			}
			JSONObject metaJson = JSON.parseObject(record.getMeta());
			if (StringUtils.isBlank(metaJson.getString("create_ddl"))) {
				log.info("ds[" + record.getDsName() + "] has no ddl");
				return;
			}
			String ddl = metaJson.getString("create_ddl");
			DbType dbType = DbType.mysql;
			if (record.getDsType().equalsIgnoreCase("odps")) {
				ddl = ddl.replaceAll("`", "");
				dbType = DbType.odps;
			} else if (record.getDsType().equalsIgnoreCase("mysql") || record.getDsType().equalsIgnoreCase("tddl")) {
				dbType = DbType.mysql;
			} else if (record.getDsType().equalsIgnoreCase("adb3")) {
				dbType = DbType.mysql;
				ddl = ddl.split("DISTRIBUTE BY")[0];
			} else if (record.getDsType().equalsIgnoreCase("postgresql") || record.getDsType().equalsIgnoreCase("hologres")) {
				ddl = ddl.replaceAll("`", "");
				dbType = DbType.postgresql;
			}
			List<SQLStatement> stmtList = SQLUtils.parseStatements(ddl, dbType);
			SQLStatement stmt = stmtList.get(0);
			SchemaStatVisitor statVisitor = SQLUtils.createSchemaStatVisitor(dbType);
			stmt.accept(statVisitor);
			List<String> pkList = statVisitor.getColumns().stream().filter(column -> column.isPrimaryKey()).map(Column::getName).collect(Collectors.toList());
			if (CollectionUtils.isNotEmpty(pkList)) {
				record.setPkFields(StringUtils.join(pkList, ",").replaceAll("`", ""));
			} else {
				pkList = new ArrayList<>();
				pkList.add(record.getPkFields());
			}
			if (record.getDsType().equalsIgnoreCase("odps")) {
				OdpsCreateTableStatement createStmt = (OdpsCreateTableStatement)stmt;
				for (SQLColumnDefinition col : createStmt.getColumnDefinitions()) {
					DsFieldInfo field = new DsFieldInfo();
					field.setFieldName(col.getColumnName().replaceAll("`", ""));
					field.setFieldType(col.getDataType().getName());
					field.setFieldDesc(col.getComment() == null ? null : col.getComment().toString().replaceAll("'", ""));
					field.setTenantId(record.getTenantId());
					if (pkList.contains(col.getColumnName())) {
						field.setIsPk(Byte.valueOf("1"));
					} else {
						field.setIsPk(Byte.valueOf("0"));
					}
					field.setIsDeleted(0L);
					field.setDbName(record.getDbName());
					field.setDsName(record.getDsName());
					field.setCreateEmpid(record.getModifyEmpid());
					field.setGmtCreate(now);
					field.setModifyEmpid(record.getModifyEmpid());
					field.setGmtModified(now);
					dsFieldInfoMapper.insert(field);
				}
			} else if (record.getDsType().equalsIgnoreCase("mysql") || record.getDsType().equalsIgnoreCase("tddl") || record.getDsType().equalsIgnoreCase("adb3")) {
				MySqlCreateTableStatement createStmt = (MySqlCreateTableStatement)stmt;
				for (SQLColumnDefinition col : createStmt.getColumnDefinitions()) {
					DsFieldInfo field = new DsFieldInfo();
					field.setFieldName(col.getColumnName().replaceAll("`", ""));
					field.setFieldType(getMysqlType(col.getDataType().getName()));
					field.setFieldDesc(col.getComment() == null ? null : col.getComment().toString().replaceAll("'", ""));
					field.setTenantId(record.getTenantId());
					if (pkList.contains(col.getColumnName())) {
						field.setIsPk(Byte.valueOf("1"));
					} else {
						field.setIsPk(Byte.valueOf("0"));
					}
					field.setIsDeleted(0L);
					field.setDbName(record.getDbName());
					field.setDsName(record.getDsName());
					field.setCreateEmpid(record.getModifyEmpid());
					field.setGmtCreate(now);
					field.setModifyEmpid(record.getModifyEmpid());
					field.setGmtModified(now);
					dsFieldInfoMapper.insert(field);
				}
			} else if (record.getDsType().equalsIgnoreCase("postgresql") || record.getDsType().equalsIgnoreCase("hologres")) {
				SQLCreateTableStatement createStmt = (SQLCreateTableStatement)stmt;
				for (SQLColumnDefinition col : createStmt.getColumnDefinitions()) {
					DsFieldInfo field = new DsFieldInfo();
					field.setFieldName(col.getColumnName().replaceAll("`", ""));
					field.setFieldType(col.getDataType().getName());
					field.setTenantId(record.getTenantId());
					if (pkList.contains(col.getColumnName())) {
						field.setIsPk(Byte.valueOf("1"));
					} else {
						field.setIsPk(Byte.valueOf("0"));
					}
					field.setIsDeleted(0L);
					field.setDbName(record.getDbName());
					field.setDsName(record.getDsName());
					field.setCreateEmpid(record.getModifyEmpid());
					field.setGmtCreate(now);
					field.setModifyEmpid(record.getModifyEmpid());
					field.setGmtModified(now);
					dsFieldInfoMapper.insert(field);}
			}
		}
	}

	private String getMysqlType(String type) {
		if ("decimal".equalsIgnoreCase(type)) {
			return "decimal(30,10)";
		} else {
			return type;
		}
	}

	@Override
    public String getPkFieldByObjectType(String tenantId, String objectType, String objectUniqueCode) {
		if (StringUtils.isBlank(objectType) || StringUtils.isBlank(objectUniqueCode)) {
			return "id";
		}
		DatasourceExample example = new DatasourceExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andDsUniqueNameEqualTo(objectUniqueCode).andObjectTypeEqualTo(objectType).andIsDeletedEqualTo(0L);
		List<Datasource> dsList = datasourceMapper.selectByExample(example);
		if (CollectionUtils.isNotEmpty(dsList)) {
			return dsList.get(0).getPkFields();
		} else {
	    	GetObjectRequest request = new GetObjectRequest();
	    	request.setObjectType(objectType);
	    	request.setObjectUniqueCode(objectUniqueCode);
	    	DataResponse<ObjectExtendVO> objExtResp = null;
	    	try {
	    		objExtResp = queryObjectMetaService.getObject(request);
	    	} catch(Exception e) {
	    		log.error("queryObjectMetaService.getObject failed", e);
	    	}
	    	if (objExtResp != null && objExtResp.getData() != null && objExtResp.getData().getObjectIdField() != null) {
	    		return objExtResp.getData().getObjectIdField();
	    	} else {
				return "id";
			}
		}
	}

	private static String transformMdpTypeToAdbType(String type) {
        String dbType = "";
        switch (type) {
            case "DATETIME":
                dbType = "datetime";
                break;
            case "ENUM":
                dbType = "varchar";
                break;
            case "BOOLEAN":
                dbType = "varchar";
                break;
            case "DECIMAL":
                dbType = "double";
                break;
            case "STRING":
                dbType = "varchar";
                break;
            case "ENUMS":
                dbType = "varchar";
                break;
            case "BIGINT":
                dbType = "bigint";
                break;
            default:
                dbType = "varchar";
                break;
        }
        return dbType;
    }

	private static String transformAdbTypeToMdpType(String type) {
        String dbType = "";
        switch (type) {
	        case "boolean":
	            dbType = "BOOLEAN";
	            break;
            case "datetime":
                dbType = "DATETIME";
                break;
            case "double":
                dbType = "DECIMAL";
                break;
            case "decimal":
                dbType = "DECIMAL";
                break;
            case "varchar":
                dbType = "STRING";
                break;
            case "bigint":
                dbType = "BIGINT";
                break;
            default:
                dbType = "STRING";
                break;
        }
        return dbType;
    }

    private void setDsMetaDdl(Datasource record, String dsType, String dsMeta, String dbMeta) {
        JSONObject dsMetaJson = JSON.parseObject(dsMeta);
        if (dsMetaJson == null) {
        	dsMetaJson = new JSONObject();
        }
        if ("obj".equalsIgnoreCase(dsType)) {
        	if (StringUtils.isNotBlank(dsMeta)) {
                record.setMeta(dsMeta);
        	}
        	return;
        }
        if ("mysql".equalsIgnoreCase(dsType)
        		|| "adb3".equalsIgnoreCase(dsType)) {
            String table = dsMetaJson.getString("table");
            if (StringUtils.isBlank(record.getTableName())) {
            	record.setTableName(table);
            } else {
            	table = record.getTableName();
            }
            String ddl = getMysqlCreateTableDdl(dbMeta, table);
            dsMetaJson.remove("jdbcUrl");
            dsMetaJson.remove("username");
            dsMetaJson.remove("password");
            dsMetaJson.remove("table");
            dsMetaJson.put("create_ddl", ddl);
            record.setMeta(dsMetaJson.toJSONString());
        } else if ("odps".equalsIgnoreCase(dsType)) {
            String table = dsMetaJson.getString("table");
            if (StringUtils.isBlank(record.getTableName())) {
            	record.setTableName(table);
            } else {
            	table = record.getTableName();
            }
        	JSONObject dbMetaJson = JSON.parseObject(dbMeta);
        	OdpsClient client = new OdpsClient(dbMetaJson.getString("odpsServer"), dbMetaJson.getString("accessId"), dbMetaJson.getString("accessKey"),
        			dbMetaJson.getString("project"), null, null);
    	    Map<String, String> columnTypeMap = client.getTableColumnType(table);
    	    Map<String, String> columnCommentMap = client.getTableColumnComment(table);
    	    StringBuffer ddl = new StringBuffer();
    	    ddl.append("CREATE TABLE ");
    	    ddl.append(dbMetaJson.getString("project"));
    	    ddl.append(".");
    	    ddl.append(table);
    	    ddl.append(" ( ");
    	    List<String> columns = new ArrayList<>();
    	    for (String field : columnTypeMap.keySet()) {
    	    	columns.add(field + " " + columnTypeMap.get(field) + " COMMENT '" + StringUtils.trimToEmpty(columnCommentMap.get(field)) + "'");
    	    }
    	    ddl.append(StringUtils.join(columns, ","));
    	    ddl.append(" ) COMMENT '");
    	    ddl.append(record.getDsDesc());
    	    ddl.append("' PARTITIONED BY (ds STRING) LIFECYCLE 30");
    	    dsMetaJson.remove("odpsServer");
            dsMetaJson.remove("accessId");
            dsMetaJson.remove("accessKey");
            dsMetaJson.remove("project");
            dsMetaJson.put("create_ddl", ddl.toString());
            record.setMeta(dsMetaJson.toJSONString());
        } else if ("tddl".equalsIgnoreCase(dsType)) {
            JSONObject dbMetaJson = JSON.parseObject(dbMeta);
        	System.setProperty("tddl.version.check", "false");
            TDataSource ds = new TDataSource();
            ds.setAppName(dbMetaJson.getString("appName"));
            ds.setDynamicRule(true);
            ds.init();
            Map<String, Set<String>> topology = ds.getTableTopology(record.getTableName());
            if (topology == null || CollectionUtils.isEmpty(topology.keySet())) {
            	log.error("can not get table topology for table:{}", dbMetaJson.getString("appName") + "." + record.getTableName());
        		throw new QanatBizException("can not get table topology for table:" + dbMetaJson.getString("appName") + "." + record.getTableName());
            }
            String groupKey = null;
            String physicalTableName = null;
            //取任意物理表
            for (Map.Entry<String, Set<String>> entry : topology.entrySet()) {
            	groupKey = entry.getKey();
                for (String realTableName : entry.getValue()) {
                	physicalTableName = realTableName;
                	break;
                }
            	break;
            }
            String ddl = getTddlCreateTableDdl(dbMeta, groupKey, physicalTableName);
            ddl = ddl.replace(physicalTableName, record.getTableName());
            dsMetaJson.remove("table");
            dsMetaJson.put("create_ddl", ddl);
            record.setMeta(dsMetaJson.toJSONString());
        } else if ("hologres".equalsIgnoreCase(dsType)) {
            String table = record.getTableName();
            String ddl = getHologresCreateTableDdl(dbMeta, table);
            dsMetaJson.remove("jdbcUrl");
            dsMetaJson.remove("username");
            dsMetaJson.remove("password");
            dsMetaJson.remove("table");
            dsMetaJson.put("create_ddl", ddl);
            record.setMeta(dsMetaJson.toJSONString());
        } else {
        	if (StringUtils.isBlank(dsMetaJson.getString("create_ddl"))) {
        		throw new QanatBizException("no create_ddl found");
        	}
        }
    }

	public String getTddlCreateTableDdl(String dbMeta, String groupKey, String physicalTableName) {
		String ddl = null;
		Connection connection = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		try {
		    String sql = "/*+TDDL({'type':'direct','dbid':?})*/SHOW CREATE TABLE " + SecurityUtil.trimSql(physicalTableName) + ";";
		    log.info("show create table sql:{}", sql);
		    JSONObject dbMetaJson = JSON.parseObject(dbMeta);
	    	System.setProperty("tddl.version.check", "false");
        	TDataSource ds = new TDataSource();
            ds.setAppName(dbMetaJson.getString("appName"));
            ds.setDynamicRule(true);
            ds.init();
            connection = ds.getConnection();
		    statement = connection.prepareStatement(sql);
		    statement.setString(1, groupKey);
		    resultSet = statement.executeQuery();
		    if(resultSet.next()) {
		        ddl = resultSet.getString(2);
			    log.info("ddl:{}", ddl);
		    }
		} catch(Exception e) {
		    log.error("sql exec failed, e={}", e.getMessage());
		} finally {
		    if (resultSet != null) {
		        try {
		            resultSet.close();
		        } catch (Exception e) {
		        }
	            resultSet = null;
		    }
		    if (statement != null) {
		        try {
		            statement.close();
		        } catch (Exception e) {
		        }
	            statement = null;
		    }
		    if (connection != null) {
		        try {
		            connection.close();
		        } catch (Exception e) {
		        }
	            connection = null;
		    }
		}
		return ddl;
	}

	private String getMysqlCreateTableDdl(String dbMeta, String table) {
		String ddl = null;
		Connection connection = null;
		Statement statement = null;
		ResultSet resultSet = null;
		try {
		    String sql = "SHOW CREATE TABLE " + SecurityUtil.trimSql(table) + ";";
		    log.info("show create table sql:{}", sql);
		    JSONObject dbMetaJson = JSON.parseObject(dbMeta);
		    RdsConnectionParam param = new RdsConnectionParam();
		    param.setUrl(dbMetaJson.getString("jdbcUrl"))
			    .setUserName(SecurityUtil.trimSql(dbMetaJson.getString("username")))
			    .setPassword(dbMetaJson.getString("password"));
		    connection = dsHandler.connectToTable(param);
		    statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
		    resultSet = statement.executeQuery(sql);
		    if(resultSet.next()) {
		        ddl = resultSet.getString(2);
			    log.info("ddl:{}", ddl);
		    }
		} catch(Exception e) {
		    log.error("sql exec failed, e={}", e.getMessage());
		} finally {
		    if (resultSet != null) {
		        try {
		            resultSet.close();
		        } catch (Exception e) {
		        }
	            resultSet = null;
		    }
		    if (statement != null) {
		        try {
		            statement.close();
		        } catch (Exception e) {
		        }
	            statement = null;
		    }
		    if (connection != null) {
		        try {
		            connection.close();
		        } catch (Exception e) {
		        }
	            connection = null;
		    }
		}
		return ddl;
	}

	private String getHologresCreateTableDdl(String dbMeta, String table) {
		String ddl = null;
		Connection connection = null;
		Statement statement = null;
		ResultSet resultSet = null;
		try {
		    String sql = "select hg_dump_script('" + SecurityUtil.trimSql(table) + "');";
		    log.info("show create table sql:{}", sql);
		    JSONObject dbMetaJson = JSON.parseObject(dbMeta);
		    RdsConnectionParam param = new RdsConnectionParam();
		    param.setUrl(dbMetaJson.getString("jdbcUrl"))
			    .setUserName(SecurityUtil.trimSql(dbMetaJson.getString("username")))
			    .setPassword(dbMetaJson.getString("password"));
		    connection = dsHandler.connectToTable(param);
		    statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
		    resultSet = statement.executeQuery(sql);
		    if(resultSet.next()) {
		        ddl = resultSet.getString(1);
		        ddl = ddl.substring(ddl.indexOf("CREATE TABLE")).split(";")[0];
			    log.info("ddl:{}", ddl);
		    }
		} catch(Exception e) {
		    log.error("sql exec failed, e={}", e.getMessage());
		} finally {
		    if (resultSet != null) {
		        try {
		            resultSet.close();
		        } catch (Exception e) {
		        }
	            resultSet = null;
		    }
		    if (statement != null) {
		        try {
		            statement.close();
		        } catch (Exception e) {
		        }
	            statement = null;
		    }
		    if (connection != null) {
		        try {
		            connection.close();
		        } catch (Exception e) {
		        }
	            connection = null;
		    }
		}
		return ddl;
	}

    @Override
    public DataResult<Boolean> modifyDatasource(DatasourceRequest request) {
        log.info("start modifyDatasource({})", request);
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	if (StringUtils.isBlank(request.getTenantId())) {
        		throw new QanatBizException("tenantId is empty");
        	}
            if (StringUtils.isBlank(request.getDsName())) {
                throw new QanatBizException("dsName is empty");
            }
            
            Date now = new Date();
            DatasourceExample example = new DatasourceExample();
            example.createCriteria().andDsNameEqualTo(request.getDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(request.getTenantId());
            List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isEmpty(dsList)) {
                throw new QanatBizException("Datasource:" + request.getDsName() + " doesn't exists!");
            }
            if (StringUtils.isBlank(dsList.get(0).getDbName())) {
        		throw new QanatBizException("dbName is empty, please set and then save again");
        	}
            Datasource record = new Datasource();
            BeanUtils.copyProperties(dsList.get(0), record);
            if (StringUtils.isNotBlank(request.getDsDesc())) {
                record.setDsDesc(request.getDsDesc());
            }
            if (StringUtils.isNotBlank(request.getRemark())) {
                record.setRemark(request.getRemark());
            }
            if (StringUtils.isNotBlank(request.getMeta())) {
                record.setMeta(request.getMeta());
            }
            if (StringUtils.isNotBlank(request.getDbName())) {
                record.setDbName(request.getDbName());
            }
            record.setModifyEmpid(request.getOperateEmpid());
            record.setGmtModified(now);
            
            String dbMeta = null;
            DbInfoExample dbExample = new DbInfoExample();
            dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dsList.get(0).getDbName()).andTenantIdEqualTo(request.getTenantId());
            List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
            if (CollectionUtils.isNotEmpty(dbs)) {
                DbInfo dbInfo = dbs.get(0);
                dbMeta = dbInfo.getMeta();
            }
            
            setDsMetaDdl(record, dsList.get(0).getDsType(), StringUtils.isNotBlank(request.getMeta()) ? request.getMeta() : dsList.get(0).getMeta(), dbMeta);
            rebuildDsFields(record);
            datasourceMapper.updateByPrimaryKeySelective(record);
            
            result.setData(true);
        } catch (Exception e) {
            result.setData(false);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("modifyDatasource failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public DataResult<Boolean> updateDsInfoMeta(String tenantId, String dbName, String tableName, String operateEmpid) {
        log.info("start updateDsInfoMeta({},{},{},{})", tenantId, dbName, tableName, operateEmpid);
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
            DatasourceExample example = new DatasourceExample();
            example.createCriteria().andDbNameEqualTo(dbName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andTableNameEqualTo(tableName);
            List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isEmpty(dsList)) {
                throw new QanatBizException("Datasource:" + dbName + "." + tableName + " doesn't exists!");
            }
            if (StringUtils.isBlank(dsList.get(0).getDbName())) {
        		throw new QanatBizException("dbName is empty, please set and then save again");
        	}
            Datasource record = new Datasource();
            BeanUtils.copyProperties(dsList.get(0), record);
            record.setModifyEmpid(operateEmpid);
            record.setGmtModified(new Date());
            
            String dbMeta = null;
            DbInfoExample dbExample = new DbInfoExample();
            dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dsList.get(0).getDbName()).andTenantIdEqualTo(tenantId);
            List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
            if (CollectionUtils.isNotEmpty(dbs)) {
                DbInfo dbInfo = dbs.get(0);
                dbMeta = dbInfo.getMeta();
            }
            
            setDsMetaDdl(record, dsList.get(0).getDsType(), dsList.get(0).getMeta(), dbMeta);
            rebuildDsFields(record);
            datasourceMapper.updateByPrimaryKeySelective(record);
            
            result.setData(true);
        } catch (Exception e) {
            result.setData(false);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("modifyDatasource failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public DataResult<Boolean> modifyDsInfoIncrConf(String tenantId, String dsName, String topicName) {
        log.info("start modifyDsInfoIncrConf({},{},{})", tenantId, dsName, topicName);
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
            Date now = new Date();
            DatasourceExample example = new DatasourceExample();
            example.createCriteria().andDsNameEqualTo(dsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
            List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isEmpty(dsList)) {
                throw new QanatBizException("Datasource:" + dsName + " doesn't exists!");
            }
            Datasource record = new Datasource();
            record.setId(dsList.get(0).getId());
            JSONObject metaJson = JSON.parseObject(dsList.get(0).getMeta());
            if (metaJson == null) {
            	metaJson = new JSONObject();
            }
            JSONObject incrConfJson = new JSONObject();
            metaJson.put("incrConf", incrConfJson);
            incrConfJson.put("topicName", topicName);
            incrConfJson.put("type", "kafka");
            record.setMeta(JSON.toJSONString(metaJson));
            record.setGmtModified(now);
            datasourceMapper.updateByPrimaryKeySelective(record);
            result.setData(true);
        } catch (Exception e) {
            result.setData(false);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("modifyDsInfoIncrConf failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public DataResult<Boolean> deleteDatasource(DatasourceRequest request) {
        log.info("start deleteDatasource({})", request);
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	if (StringUtils.isBlank(request.getTenantId())) {
        		throw new QanatBizException("tenantId is empty");
        	}
            if (StringUtils.isBlank(request.getDsName())) {
                throw new QanatBizException("dsName is empty");
            }
            
            Date now = new Date();
            DatasourceExample example = new DatasourceExample();
            example.createCriteria().andDsNameEqualTo(request.getDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(request.getTenantId());
            List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isEmpty(dsList)) {
                throw new QanatBizException("Datasource:" + request.getDsName() + " doesn't exists!");
            }
            Datasource record = new Datasource();
            record.setId(dsList.get(0).getId());
            record.setIsDeleted(dsList.get(0).getId());
            record.setModifyEmpid(request.getOperateEmpid());
            record.setGmtModified(now);
            
            datasourceMapper.updateByPrimaryKeySelective(record);
            result.setData(true);
        } catch (Exception e) {
            result.setData(false);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("deleteDatasource failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    @Override
	public JSONObject getOdsTableMetaByDsName(String tenantId, String dsName) {
		DatasourceExample example = new DatasourceExample();
		example.createCriteria().andDsNameEqualTo(dsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
		List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isEmpty(dsList)) {
			throw new QanatBizException(dsName + " is not found");
		}
		Datasource dsInfo = dsList.get(0);
		JSONObject dsMetaJson = null;
		String odsDsName = null;
		//如果dsInfo是adb3类型，直接返回metaInfo，否则通过ods类型的dsRelation获取adb3类型的dsInfo
		if ("adb3".equalsIgnoreCase(dsInfo.getDsType())) {
			dsMetaJson = JSON.parseObject(dsInfo.getMeta());
			dsMetaJson.put("table", dsInfo.getTableName());
			dsMetaJson.put("sysType", dsInfo.getSysType());
			odsDsName = dsInfo.getDsName();
		} else {
			DsRelationExample dsRelExp = new DsRelationExample();
			dsRelExp.createCriteria().andTenantIdEqualTo(tenantId)
									.andIsDeletedEqualTo(0L)
									.andRelationTypeEqualTo("ods")
									.andSrcDsNameEqualTo(dsName);
			List<DsRelation> dsRels = dsRelationMapper.selectByExample(dsRelExp);
			if (CollectionUtils.isEmpty(dsRels)) {
				throw new QanatBizException("no_ods_ds", "the ods dsInfo of " + dsName + " is not found");
			}
			example = new DatasourceExample();
			example.createCriteria().andDsNameEqualTo(dsRels.get(0).getDstDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
			List<Datasource> dsList2 = datasourceMapper.selectByExampleWithBLOBs(example);
			if (CollectionUtils.isEmpty(dsList2)) {
				throw new QanatBizException(dsRels.get(0).getDstDsName() + " is not found");
			}
			odsDsName = dsRels.get(0).getDstDsName();
			Datasource dsInfo2 = dsList2.get(0);
			dsMetaJson = JSON.parseObject(dsInfo2.getMeta());
			dsMetaJson = dsMetaJson == null ? new JSONObject() : dsMetaJson;
			dsMetaJson.put("table", dsInfo2.getTableName());
			dsMetaJson.put("sysType", dsInfo2.getSysType());
			dsMetaJson.put("srcDdName", dsInfo.getDbName());
		}
		
		//如果dsInfo里的增量配置为空，尝试通过dsRelation获取
		if (dsMetaJson.getJSONObject("incrConf") == null || StringUtils.isBlank(dsMetaJson.getJSONObject("incrConf").getString("topicName"))) {
			DsRelationExample dsRelExp = new DsRelationExample();
			dsRelExp.createCriteria().andTenantIdEqualTo(tenantId)
									.andIsDeletedEqualTo(0L)
									.andRelationTypeEqualTo("post_ods")
									.andDstDsNameEqualTo(odsDsName);
			List<DsRelation> dsRels = dsRelationMapper.selectByExample(dsRelExp);
			if (CollectionUtils.isNotEmpty(dsRels)) {
				example = new DatasourceExample();
				example.createCriteria().andDsNameEqualTo(dsRels.get(0).getSrcDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
				dsList = datasourceMapper.selectByExampleWithBLOBs(example);
				if (CollectionUtils.isEmpty(dsList)) {
					throw new QanatBizException(dsRels.get(0).getSrcDsName() + " is not found");
				}
				JSONObject incrConfJson = new JSONObject();
				incrConfJson.put("type", dsList.get(0).getDsType());
				incrConfJson.put("topicName", dsList.get(0).getTableName());
				dsMetaJson.put("incrConf", incrConfJson);
			}
		}
		JSONObject dsInfoMetaJson = JSONObject.parseObject(dsInfo.getMeta());
		if (dsInfoMetaJson != null) {
			dsMetaJson.put("storeType", dsInfoMetaJson.getString("storeType"));
			dsMetaJson.put("slotMeta", dsInfoMetaJson.getJSONObject("slotMeta"));
		}
		return dsMetaJson;
	}

    @Override
	public JSONObject getOdpsTableMetaByObjectCode(String tenantId, String objectUniqueCode) {
		DatasourceExample example = new DatasourceExample();
		example.createCriteria().andTableNameEqualTo(objectUniqueCode).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsTypeEqualTo("obj");
		List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isEmpty(dsList)) {
			throw new QanatBizException(objectUniqueCode + " is not found");
		}
		Datasource dsInfo = dsList.get(0);
		JSONObject dsMetaJson = null;
		String odsDsName = null;
		//如果dsInfo是adb3类型，直接返回metaInfo，否则通过ods类型的dsRelation获取adb3类型的dsInfo
		if ("adb3".equalsIgnoreCase(dsInfo.getDsType())) {
			dsMetaJson = JSON.parseObject(dsInfo.getMeta());
			dsMetaJson.put("table", dsInfo.getTableName());
			dsMetaJson.put("sysType", dsInfo.getSysType());
			odsDsName = dsInfo.getDsName();
		} else {
			DsRelationExample dsRelExp = new DsRelationExample();
			dsRelExp.createCriteria().andTenantIdEqualTo(tenantId)
									.andIsDeletedEqualTo(0L)
									.andRelationTypeEqualTo("odps")
									.andSrcDsNameEqualTo(dsInfo.getDsName());
			List<DsRelation> dsRels = dsRelationMapper.selectByExample(dsRelExp);
			if (CollectionUtils.isEmpty(dsRels)) {
				throw new QanatBizException("no_odps_ds", "the odps dsInfo of " + dsInfo.getDsName() + " is not found");
			}
			example = new DatasourceExample();
			example.createCriteria().andDsNameEqualTo(dsRels.get(0).getDstDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
			List<Datasource> dsList2 = datasourceMapper.selectByExampleWithBLOBs(example);
			if (CollectionUtils.isEmpty(dsList2)) {
				throw new QanatBizException(dsRels.get(0).getDstDsName() + " is not found");
			}
			odsDsName = dsRels.get(0).getDstDsName();
			Datasource dsInfo2 = dsList2.get(0);
			dsMetaJson = JSON.parseObject(dsInfo2.getMeta());
			dsMetaJson = dsMetaJson == null ? new JSONObject() : dsMetaJson;
			dsMetaJson.put("table", dsInfo2.getTableName());
			dsMetaJson.put("sysType", dsInfo2.getSysType());
		}
		
		//如果dsInfo里的增量配置为空，尝试通过dsRelation获取
		if (dsMetaJson.getJSONObject("incrConf") == null || StringUtils.isBlank(dsMetaJson.getJSONObject("incrConf").getString("topicName"))) {
			DsRelationExample dsRelExp = new DsRelationExample();
			dsRelExp.createCriteria().andTenantIdEqualTo(tenantId)
									.andIsDeletedEqualTo(0L)
									.andRelationTypeEqualTo("post_ods")
									.andDstDsNameEqualTo(odsDsName);
			List<DsRelation> dsRels = dsRelationMapper.selectByExample(dsRelExp);
			if (CollectionUtils.isNotEmpty(dsRels)) {
				example = new DatasourceExample();
				example.createCriteria().andDsNameEqualTo(dsRels.get(0).getSrcDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
				dsList = datasourceMapper.selectByExampleWithBLOBs(example);
				if (CollectionUtils.isEmpty(dsList)) {
					throw new QanatBizException(dsRels.get(0).getSrcDsName() + " is not found");
				}
				JSONObject incrConfJson = new JSONObject();
				incrConfJson.put("type", dsList.get(0).getDsType());
				incrConfJson.put("topicName", dsList.get(0).getTableName());
				dsMetaJson.put("incrConf", incrConfJson);
			}
		}
		JSONObject dsInfoMetaJson = JSONObject.parseObject(dsInfo.getMeta());
		if (dsInfoMetaJson != null) {
			dsMetaJson.put("storeType", dsInfoMetaJson.getString("storeType"));
			dsMetaJson.put("slotMeta", dsInfoMetaJson.getJSONObject("slotMeta"));
			dsMetaJson.put("objectType", dsInfo.getObjectType());
		}
		return dsMetaJson;
	}

    @Override
	public JSONObject getOdpsTableMetaByOdsDsName(String tenantId, String dsName) {
		DatasourceExample example = new DatasourceExample();
		example.createCriteria().andDsNameEqualTo(dsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsTypeEqualTo("adb3");
		List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isEmpty(dsList)) {
			throw new QanatBizException("ods " + dsName + " is not found");
		}
		Datasource dsInfo = dsList.get(0);
		JSONObject dsMetaJson = JSON.parseObject(dsInfo.getMeta());
		dsMetaJson = dsMetaJson == null ? new JSONObject() : dsMetaJson;
		
		//获取ods表关联的odps数据源
		DsRelationExample dsRelExp = new DsRelationExample();
		dsRelExp.createCriteria().andTenantIdEqualTo(tenantId)
								.andIsDeletedEqualTo(0L)
								.andRelationTypeEqualTo("odps")
								.andSrcDsNameEqualTo(dsName);
		List<DsRelation> dsRels = dsRelationMapper.selectByExample(dsRelExp);
		if (CollectionUtils.isEmpty(dsRels)) {
			throw new QanatBizException(dsName + " has no odps dsInfo related");
		}
		example = new DatasourceExample();
		example.createCriteria().andDsNameEqualTo(dsRels.get(0).getDstDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
		List<Datasource> dsList2 = datasourceMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isEmpty(dsList2)) {
			throw new QanatBizException(dsRels.get(0).getDstDsName() + " is not found");
		}
		Datasource dsInfo2 = dsList2.get(0);
		dsMetaJson.put("table", dsInfo2.getTableName());
		dsMetaJson.put("dsType", "odps");
		dsMetaJson.put("dsName", dsRels.get(0).getDstDsName());
		DbInfo dbInfo = getDbInfoByName(tenantId, dsInfo2.getDbName());
        JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
        dsMetaJson.putAll(dbMetaJson);
		return dsMetaJson;
	}

    @Override
	public JSONObject getTableMetaByDsName(String tenantId, String dsName) throws QanatBizException {
		DatasourceExample example = new DatasourceExample();
		example.createCriteria().andDsNameEqualTo(dsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
		List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isEmpty(dsList)) {
			throw new QanatBizException(dsName + " is not found");
		}
		Datasource dsInfo = dsList.get(0);
		JSONObject dsMetaJson = JSON.parseObject(dsInfo.getMeta());
		dsMetaJson = dsMetaJson == null ? new JSONObject() : dsMetaJson;
		dsMetaJson.put("table", dsInfo.getTableName());
		dsMetaJson.put("sysType", dsInfo.getSysType());
		dsMetaJson.put("dbName", dsInfo.getDbName());
		dsMetaJson.put("dsType", dsInfo.getDsType());
		dsMetaJson.put("dsName", dsInfo.getDsName());
		dsMetaJson.put("dsId", dsInfo.getId());
		dsMetaJson.put("qph", dsInfo.getQph() != null ? dsInfo.getQph() : dsInfo.getPredictQph());
		dsMetaJson.put("objectType", dsInfo.getObjectType());
		
		DbInfo dbInfo = getDbInfoByName(tenantId, dsInfo.getDbName());
        JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
		if (dbInfo.getDbType().equalsIgnoreCase(DataSourceType.ODPS.toString())) {
			dsMetaJson.put("project", dbMetaJson.getString("project"));
			dsMetaJson.put("accessId", dbMetaJson.getString("accessId"));
			dsMetaJson.put("accessKey", dbMetaJson.getString("accessKey"));
			dsMetaJson.put("odpsServer", dbMetaJson.getString("odpsServer"));
		} else if (dbInfo.getDbType().equalsIgnoreCase(DataSourceType.HOLOGRES.toString())) {
			dsMetaJson.put("endpoint", dbMetaJson.getString("endpoint"));
			dsMetaJson.put("username", dbMetaJson.getString("username"));
			dsMetaJson.put("password", dbMetaJson.getString("password"));
			dsMetaJson.put("database", dbMetaJson.getString("dbName"));
			
			JSONObject odsConfJson = new JSONObject();
			odsConfJson.put("type", dsInfo.getDsType());
			odsConfJson.put("tableName", dsInfo.getTableName());
			odsConfJson.put("dbName", dsInfo.getDbName());
	        odsConfJson.put("endpoint", dbMetaJson.getString("endpoint"));
	        odsConfJson.put("username", dbMetaJson.getString("username"));
	        odsConfJson.put("password", dbMetaJson.getString("password"));
	        odsConfJson.put("database", dbMetaJson.getString("dbName"));
			
			dsMetaJson.put("odsConf", odsConfJson);
		}
		
		//如果dsInfo里的增量配置为空，尝试通过dsRelation获取
		if (dsMetaJson.getJSONObject("incrConf") == null || StringUtils.isBlank(dsMetaJson.getJSONObject("incrConf").getString("topicName"))
				 || StringUtils.isBlank(dsMetaJson.getJSONObject("incrConf").getString("dbName"))) {
			DsRelationExample dsRelExp = new DsRelationExample();
			dsRelExp.createCriteria().andTenantIdEqualTo(tenantId)
									.andIsDeletedEqualTo(0L)
									.andRelationTypeEqualTo("incr")
									.andDstDsNameEqualTo(dsInfo.getDsName());
			List<DsRelation> dsRels = dsRelationMapper.selectByExample(dsRelExp);
			if (CollectionUtils.isNotEmpty(dsRels)) {
				example = new DatasourceExample();
				example.createCriteria().andDsNameEqualTo(dsRels.get(0).getSrcDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
				dsList = datasourceMapper.selectByExampleWithBLOBs(example);
				if (CollectionUtils.isEmpty(dsList)) {
					throw new QanatBizException(dsRels.get(0).getSrcDsName() + " is not found");
				}
				JSONObject incrConfJson = new JSONObject();
				incrConfJson.put("type", dsList.get(0).getDsType());
				incrConfJson.put("topicName", dsList.get(0).getTableName());
				incrConfJson.put("dbName", dsList.get(0).getDbName());
				
				dsMetaJson.put("incrConf", incrConfJson);
			}
		}
		
		//holo
		DsRelationExample dsRelExp = new DsRelationExample();
		dsRelExp.createCriteria().andTenantIdEqualTo(tenantId)
								.andIsDeletedEqualTo(0L)
								.andRelationTypeEqualTo("holo-ods")
								.andSrcDsNameEqualTo(dsName);
		List<DsRelation> dsRels = dsRelationMapper.selectByExample(dsRelExp);
		if (CollectionUtils.isNotEmpty(dsRels)) {
			example = new DatasourceExample();
			example.createCriteria().andDsNameEqualTo(dsRels.get(0).getDstDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
			dsList = datasourceMapper.selectByExampleWithBLOBs(example);
			if (CollectionUtils.isEmpty(dsList)) {
				throw new QanatBizException(dsRels.get(0).getDstDsName() + " is not found");
			}
			JSONObject odsConfJson = new JSONObject();
			odsConfJson.put("type", dsList.get(0).getDsType());
			odsConfJson.put("tableName", dsList.get(0).getTableName());
			odsConfJson.put("dbName", dsList.get(0).getDbName());
			
			DbInfo odsDbInfo = getDbInfoByName(tenantId, dsList.get(0).getDbName());
	        JSONObject odsDbMetaJson = JSON.parseObject(odsDbInfo.getMeta());
			
	        odsConfJson.put("endpoint", odsDbMetaJson.getString("endpoint"));
	        odsConfJson.put("username", odsDbMetaJson.getString("username"));
	        odsConfJson.put("password", odsDbMetaJson.getString("password"));
	        odsConfJson.put("database", odsDbMetaJson.getString("dbName"));
			
			dsMetaJson.put("odsConf", odsConfJson);
		}
		
		return dsMetaJson;
	}
    
    @Override
    public JSONObject getDbMetaByDsName(String tenantId, String dsName) {
        JSONObject srcDsMetaJson = null;
        DatasourceExample example = new DatasourceExample();
        example.createCriteria().andDsNameEqualTo(dsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
        List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(dsList)) {
            throw new QanatBizException("Datasource:" + dsName + " doesn't exists!");
        }
        Datasource ds = dsList.get(0);
        srcDsMetaJson = JSON.parseObject(ds.getMeta());
        srcDsMetaJson.put("dsType", ds.getDsType());
        srcDsMetaJson.put("table", ds.getTableName());
        srcDsMetaJson.put("dbName", ds.getDbName());
        srcDsMetaJson.put("dsId", ds.getId());

        DbInfo dbInfo = getDbInfoByName(tenantId, ds.getDbName());
        JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
        
        // 改造点：使用地域感知的JDBC URL获取逻辑
        String jdbcUrl = getJdbcUrlByRegion(dbMetaJson);
        srcDsMetaJson.put("jdbcUrl", jdbcUrl);
        
        srcDsMetaJson.put("username", dbMetaJson.getString("username"));
        srcDsMetaJson.put("password", dbMetaJson.getString("password"));
       
        String pk = "id";
        String ddlSql = srcDsMetaJson.getString("create_ddl");
        if (StringUtils.isNotBlank(ddlSql)) {
            ddlSql = ddlSql.replaceAll("`", "");
            DbType dbType = null;
            if ("mysql".equalsIgnoreCase(ds.getDsType())) {
            	dbType = DbType.mysql;
            } else if ("odps".equalsIgnoreCase(ds.getDsType())) {
            	dbType = DbType.odps;
            } else if ("adb3".equalsIgnoreCase(ds.getDsType())) {
            	dbType = DbType.ads;
            }
            try {
	            List<SQLStatement> stmtList = SQLUtils.parseStatements(ddlSql, dbType);
	            SQLStatement stmt = stmtList.get(0);
	            SchemaStatVisitor statVisitor = SQLUtils.createSchemaStatVisitor(dbType);
	            stmt.accept(statVisitor);
	            List<String> pkList = statVisitor.getColumns().stream().filter(column -> column.isPrimaryKey()).map(Column::getName).collect(Collectors.toList());
	            if (CollectionUtils.isNotEmpty(pkList)) {
	            	pk = pkList.get(0);
	            }
            } catch(Exception e) {
            	if (StringUtils.isNotBlank(ds.getPkFields())) {
            		pk = ds.getPkFields();
            	}
            }
        }
        srcDsMetaJson.put("pk", pk);
        return srcDsMetaJson;
    }

    @Override
    public Long getDbIdByName(String tenantId, String dbName) {
        return this.getDbInfoByName(tenantId, dbName).getId();
    }

    private DbInfo getDbInfoByName(String tenantId, String dbName) {
        DbInfoExample dbExample = new DbInfoExample();
        dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName).andTenantIdEqualTo(tenantId);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("db:" + dbName + " not found");
        }
        DbInfo dbInfo = dbs.get(0);
        return dbInfo;
    }

    @Override
	public JSONObject getDbMetaByName(String dbName) {
	    DbInfoExample example = new DbInfoExample();
	    example.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
	    List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(example);
	    if (CollectionUtils.isEmpty(dbs)) {
	        throw new QanatBizException("no db found");
	    }
	    String dbMeta = dbs.get(0).getMeta();
	    JSONObject dbMetaJson = JSON.parseObject(dbMeta);
	    dbMetaJson.put("dbType", dbs.get(0).getDbType());
	    dbMetaJson.put("dbId", dbs.get(0).getId());
	    return dbMetaJson;
	}
    
    @Override
    public Long getDsIdByTableName(String tenantId, String tableName, String dbName) {
		DatasourceExample example = new DatasourceExample();
		example.setOrderByClause("id asc");//利旧处理， 为了获取老版dsName的id
		example.createCriteria().andTableNameEqualTo(tableName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDbNameEqualTo(dbName);
		List<Datasource> dsInfos = datasourceMapper.selectByExample(example);
		return dsInfos.get(0).getId();
	}

    @Override
    public JSONObject getTableMetaByDsUniqueName(String tenantId, String dsUniqueName) throws QanatBizException {
		DatasourceExample example = new DatasourceExample();
		example.createCriteria().andTableNameEqualTo(dsUniqueName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsTypeEqualTo("obj");
		List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isEmpty(dsList)) {
			throw new QanatBizException(dsUniqueName + " is not found");
		}
		JSONObject dsMetaJson = JSON.parseObject(dsList.get(0).getMeta()) == null ? new JSONObject() : JSON.parseObject(dsList.get(0).getMeta());
		dsMetaJson.put("table", dsList.get(0).getTableName());
		dsMetaJson.put("sysType", dsList.get(0).getSysType());
		dsMetaJson.put("dsName", dsList.get(0).getDsName());
		dsMetaJson.put("dbName", dsList.get(0).getDbName());
		dsMetaJson.put("dsType", dsList.get(0).getDsType());
		dsMetaJson.put("objectType", dsList.get(0).getObjectType());
		dsMetaJson.put("qph", dsList.get(0).getQph() != null ? dsList.get(0).getQph() : dsList.get(0).getPredictQph());

		
		//如果dsInfo里的增量配置为空，尝试通过dsRelation获取
		if (dsMetaJson.getJSONObject("incrConf") == null || StringUtils.isBlank(dsMetaJson.getJSONObject("incrConf").getString("topicName"))) {
			JSONObject incrConfJson = new JSONObject();
			DsRelationExample dsRelExp = new DsRelationExample();
			dsRelExp.createCriteria().andTenantIdEqualTo(tenantId)
									.andIsDeletedEqualTo(0L)
									.andRelationTypeEqualTo("incr")
									.andDstDsNameEqualTo(dsList.get(0).getDsName());
			List<DsRelation> dsRels = dsRelationMapper.selectByExample(dsRelExp);
			if (CollectionUtils.isNotEmpty(dsRels)) {
				example = new DatasourceExample();
				example.createCriteria().andDsNameEqualTo(dsRels.get(0).getSrcDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
				List<Datasource> relDsList = datasourceMapper.selectByExampleWithBLOBs(example);
				if (CollectionUtils.isEmpty(relDsList)) {
					throw new QanatBizException(dsRels.get(0).getSrcDsName() + " is not found");
				}
				incrConfJson.put("type", relDsList.get(0).getDsType());
				incrConfJson.put("topicName", relDsList.get(0).getTableName());
				incrConfJson.put("dbName", relDsList.get(0).getDbName());
			}
			dsRelExp = new DsRelationExample();
			dsRelExp.createCriteria().andTenantIdEqualTo(tenantId)
					.andIsDeletedEqualTo(0L)
					.andRelationTypeEqualTo("batch")
					.andDstDsNameEqualTo(dsList.get(0).getDsName());
			dsRels = dsRelationMapper.selectByExample(dsRelExp);
			if (CollectionUtils.isNotEmpty(dsRels)) {
				example = new DatasourceExample();
				example.createCriteria().andDsNameEqualTo(dsRels.get(0).getSrcDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
				List<Datasource> relDsList = datasourceMapper.selectByExampleWithBLOBs(example);
				if (CollectionUtils.isEmpty(relDsList)) {
					throw new QanatBizException(dsRels.get(0).getSrcDsName() + " is not found");
				}
				incrConfJson.put("topicNameBatch", relDsList.get(0).getTableName());
			}
			incrConfJson.put("src", "object");
			dsMetaJson.put("incrConf", incrConfJson);
		}
		return dsMetaJson;
	}
    
    @Override
    public String createMdpObject(String tenantId, String dsName,String enums) {
    	log.info("createMdpObject({},{},{})", tenantId, dsName, enums);
		String domainAK = "57c7c5b6-b164-4e2a-ad6a-b02bad3fb060";
		String domainCode = "AliyunRoot";
		String objectCode = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, dsName);
		String objectUniqueCode = domainCode + "__" + objectCode;
    	try {
	    	DatasourceExample example = new DatasourceExample();
			example.createCriteria().andDsNameEqualTo(dsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
			List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
			if (CollectionUtils.isEmpty(dsList)) {
				throw new QanatBizException(dsName + " is not found");
			}
			Datasource dsInfo = dsList.get(0);
			
			if ("obj".equalsIgnoreCase(dsInfo.getDsType())) {
				throw new QanatBizException("obj is not support");
			}
			
			DsFieldInfoExample dsFieldInfoExample = new DsFieldInfoExample();
			dsFieldInfoExample.createCriteria().andDsNameEqualTo(dsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
			List<DsFieldInfo> dsFieldList = dsFieldInfoMapper.selectByExample(dsFieldInfoExample);
			
			String objectType = StringUtils.isNotBlank(dsInfo.getObjectType()) ? dsInfo.getObjectType() : objectCode;
			
			GetObjectTypeRequest getObjectTypeRequest = new GetObjectTypeRequest();
			getObjectTypeRequest.setObjectType(objectType);
			DataResponse<ObjectTypeBaseVO> getObjectTypeResponse = queryObjectMetaService.getObjectType(getObjectTypeRequest);
	    	log.info("getObjectTypeResponse={}", JSON.toJSONString(getObjectTypeResponse));
	    	
			if (getObjectTypeResponse.getData() == null) {
		    	CreateObjectTypeRequest createObjectTypeRequest = new CreateObjectTypeRequest();
		    	createObjectTypeRequest.setBizOperator("qanat");
		    	createObjectTypeRequest.setName(dsInfo.getDsDesc());
		    	createObjectTypeRequest.setIntro(dsInfo.getDsDesc());
		    	createObjectTypeRequest.setObjectIdField(dsInfo.getPkFields());
		    	createObjectTypeRequest.setType(objectType);
		    	createObjectTypeRequest.setRequestId(UUID.randomUUID().toString());
		    	DataResponse<ObjectTypeBaseVO> createObjectTypeResponse = operateObjectMetaService.createObjectType(createObjectTypeRequest);
		    	log.info("createObjectTypeResponse={}", JSON.toJSONString(createObjectTypeResponse));
			}
			
			CreateRichObjectRequest createRichObjectRequest = new CreateRichObjectRequest();
			createRichObjectRequest.setBizOperator("qanat");
			createRichObjectRequest.setCode(objectCode);
			createRichObjectRequest.setDomainAK(domainAK);
			createRichObjectRequest.setDomainCode(domainCode);
			createRichObjectRequest.setIntro(dsInfo.getDsDesc());
			createRichObjectRequest.setName(dsInfo.getDsDesc());
			createRichObjectRequest.setTagMarkerSelector("ADB");
			List<TagMetaParam> tagMetaParams = new ArrayList<>();
			List<DsFieldRelation> dsFieldRelationList = new ArrayList<>();
			String prefix = RandomStringUtils.randomAlphabetic(5).toLowerCase();
			Date now = new Date();
			for (DsFieldInfo dsField : dsFieldList) {
				if (dsField.getIsPk() == 1) {
					continue;
				}
				TagMetaParam param = new TagMetaParam();
				String objFieldCode = prefix + "_" + dsField.getFieldName();
				DsFieldRelation dsFieldRelation = new DsFieldRelation();
				dsFieldRelationList.add(dsFieldRelation);
				dsFieldRelation.setDstFieldName(objFieldCode);
				dsFieldRelation.setDstDsName("obj_" + objectUniqueCode);
				dsFieldRelation.setGmtCreate(now);
				dsFieldRelation.setGmtModified(now);
				dsFieldRelation.setIsDeleted(0L);
				dsFieldRelation.setRelationType("ds-obj");
				dsFieldRelation.setSrcDsName(dsName);
				dsFieldRelation.setSrcFieldName(dsField.getFieldName());
				dsFieldRelation.setTenantId(tenantId);
				param.setCode(objFieldCode);
				param.setDataType(transformAdbTypeToMdpType(dsField.getFieldType()));
				JSONObject extInfoJson = new JSONObject();
				extInfoJson.put("dsName", dsName);
				extInfoJson.put("fieldName", dsField.getFieldName());
				param.setExtInfo(extInfoJson.toJSONString());
				param.setIsQuote(1L);
				param.setName(dsField.getFieldDesc());
				param.setIntro(dsField.getFieldDesc());
				param.setTagMarkerSelector("ADB");
				param.setTagType("normal");
				tagMetaParams.add(param);
			}
			DsFieldRelationExample dsFieldRelationExample = new DsFieldRelationExample();
			dsFieldRelationExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andSrcDsNameEqualTo(dsName).andRelationTypeEqualTo("ds-obj");
			dsFieldRelationMapper.deleteByExample(dsFieldRelationExample);
			for (DsFieldRelation dsFieldRelation : dsFieldRelationList) {
				dsFieldRelationMapper.insert(dsFieldRelation);
			}
			createRichObjectRequest.setTagMetaParams(tagMetaParams);
			createRichObjectRequest.setType(objectType);
			createRichObjectRequest.setRequestId(UUID.randomUUID().toString());
	    	log.info("createRichObjectRequest={}", JSON.toJSONString(createRichObjectRequest));
			DataResponse<ObjectVO> createRichObjectResponse = operateObjectMetaService.createRichObject(createRichObjectRequest);
	    	log.info("createRichObjectResponse={}", JSON.toJSONString(createRichObjectResponse));
	    	
	    	if (createRichObjectResponse != null && createRichObjectResponse.isSuccess() && createRichObjectResponse.getData() != null 
	    			&& StringUtils.isNotBlank(enums)) {
	    		JSONObject json = JSON.parseObject(enums);
	    		for (String fieldName : json.keySet()) {
	    			String tagCode = prefix + "_" + fieldName;
			    	CreateEnumBatchRequest createEnumReq = new CreateEnumBatchRequest();
		        	createEnumReq.setBizOperator("qanat");
		        	List<CreateEnumVO> createEnumVOList = new ArrayList<>();
		        	createEnumReq.setCreateEnumVOList(createEnumVOList);
		            JSONArray enumsJsonArray = json.getJSONArray(fieldName);
		        	for (int j = 0; j < enumsJsonArray.size(); j++) {
		        		JSONObject enumsJson = enumsJsonArray.getJSONObject(j);
		        		CreateEnumVO createEnum = new CreateEnumVO();
		        		createEnum.setSort(j);
		        		createEnum.setValueCode(enumsJson.getString("valueCode"));
		        		createEnum.setValueEn(enumsJson.getString("valueEn"));
		        		createEnum.setValueZh(enumsJson.getString("valueZh"));
		        		createEnumVOList.add(createEnum);
		        	}
		        	createEnumReq.setDomainAK(domainAK);
		        	createEnumReq.setDomainCode(domainCode);
		        	createEnumReq.setRequestId(UUID.randomUUID().toString());
		        	createEnumReq.setTagCode(tagCode);
		        	createEnumReq.setTagDomainCode(domainCode);
		        	DataResponse<Map<String, BaseResponse>> createEnumResponse = operateEnumService.createBatch(createEnumReq);
		            log.info("field[{}] createEnumResponse={}", fieldName, JSON.toJSONString(createEnumResponse));
	    		}
	    	}
    	} catch (Exception e) {
    		log.error("createMdpObject failed, error={}", e.getMessage(), e);
    	}
    	return objectUniqueCode;
    }

	@Override
	public Map<String, Object> getObjectSlotMeta(String objectType, String objectUniqueCode) {
		return getObjectSlotMeta(objectType, objectUniqueCode, null);
	}

	@Override
	public String getObjectSlotMetaJson(String objectType, String objectUniqueCode) {
		return JSON.toJSONString(getObjectSlotMeta(objectType, objectUniqueCode, null));
	}

	@Override
	public Map<String, Object> getObjectSlotMeta(String objectType, String objectUniqueCode, String tagDomainCode) {
		Map<String, Object> objectSlotMataMap = new HashMap<>();
		Map<String, String> fieldCodeMap = new HashMap<>();
		objectSlotMataMap.put("fields", fieldCodeMap);
		ListFieldByObjectRequest request = new ListFieldByObjectRequest();
		request.setDomainCode(domainCode);
		request.setDomainAK(domainAk);
		request.setFromApp("hsfops");
		request.setLanguage("en");
		request.setObjectType(objectType);
		request.setObjectUniqueCode(objectUniqueCode);
		request.setRequestId(UUID.randomUUID().toString());
		request.setTagDomainCode(tagDomainCode);
		log.info("befor listFieldByObject({})", JSON.toJSONString(request));
		DataResponse<ObjectWithFieldVO> response = queryObjectMetaService.listFieldByObject(request);
		log.info("after listFieldByObject()={}", JSON.toJSONString(response));
		if (response != null && response.getData() != null) {
			ObjectWithFieldVO objectWithFieldVO = response.getData();
			String slotTable = objectWithFieldVO.getTableName();
			String slotOnlineTable = objectWithFieldVO.getOnlineTableName();
			objectSlotMataMap.put("table", slotTable);
			objectSlotMataMap.put("onlineTable", slotOnlineTable);
			List<SimpleTagVO> fields = objectWithFieldVO.getTagVOList();
			if (CollectionUtils.isNotEmpty(fields)) {
				for (SimpleTagVO field : fields) {
					fieldCodeMap.put(field.getUniqueCode(), field.getSlotFieldCode());
				}
			}
		}
		return objectSlotMataMap;
    }
	
	@Override
	public Map<String, String> getObjectSlotFieldMap(String objectType, String objectUniqueCode, String tagDomainCode) {
		Map<String, String> fieldCodeMap = new HashMap<>();
		ListFieldByObjectRequest request = new ListFieldByObjectRequest();
		request.setDomainCode(domainCode);
		request.setDomainAK(domainAk);
		request.setFromApp("hsfops");
		request.setLanguage("en");
		request.setObjectType(objectType);
		request.setObjectUniqueCode(objectUniqueCode);
		request.setRequestId(UUID.randomUUID().toString());
		request.setTagDomainCode(tagDomainCode);
		log.info("befor listFieldByObject({})", JSON.toJSONString(request));
		DataResponse<ObjectWithFieldVO> response = queryObjectMetaService.listFieldByObject(request);
		log.info("after listFieldByObject()={}", JSON.toJSONString(response));
		if (response != null && response.getData() != null) {
			ObjectWithFieldVO objectWithFieldVO = response.getData();
			List<SimpleTagVO> fields = objectWithFieldVO.getTagVOList();
			if (CollectionUtils.isNotEmpty(fields)) {
				for (SimpleTagVO field : fields) {
					fieldCodeMap.put(field.getUniqueCode(), field.getSlotFieldCode());
				}
			}
		}
		return fieldCodeMap;
    }
	
	@Override
	public List<String> getObjectSlotFieldList(String objectType, String objectUniqueCode, String tagDomainCode) {
		List<String> fieldList = new ArrayList<>();
		ListFieldByObjectRequest request = new ListFieldByObjectRequest();
		request.setDomainCode(domainCode);
		request.setDomainAK(domainAk);
		request.setFromApp("hsfops");
		request.setLanguage("en");
		request.setObjectType(objectType);
		request.setObjectUniqueCode(objectUniqueCode);
		request.setRequestId(UUID.randomUUID().toString());
		request.setTagDomainCode(tagDomainCode);
		log.info("befor listFieldByObject({})", JSON.toJSONString(request));
		DataResponse<ObjectWithFieldVO> response = queryObjectMetaService.listFieldByObject(request);
		log.info("after listFieldByObject()={}", JSON.toJSONString(response));
		if (response != null && response.getData() != null) {
			ObjectWithFieldVO objectWithFieldVO = response.getData();
			List<SimpleTagVO> fields = objectWithFieldVO.getTagVOList();
			if (CollectionUtils.isNotEmpty(fields)) {
				for (SimpleTagVO field : fields) {
					fieldList.add(JSON.toJSONString(field));
				}
			}
		}
		return fieldList;
    }

    @Override
    public DataResult<Boolean> modifyDsInfoMdpSlot(String tenantId, String dsName, String slotDbName) {
    	return this.modifyDsInfoMdpSlot(tenantId, dsName, slotDbName, null);
    }

    @Override
    public DataResult<Boolean> modifyDsInfoMdpSlot(String tenantId, String dsName, String slotDbName, String slotMeta) {
        log.info("start modifyDsInfoMdpSlot({},{},{},{})", tenantId, dsName, slotDbName, slotMeta);
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
            Date now = new Date();
            DatasourceExample example = new DatasourceExample();
            example.createCriteria().andDsNameEqualTo(dsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
            List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isEmpty(dsList)) {
                throw new QanatBizException("Datasource:" + dsName + " doesn't exists!");
            }
            Datasource record = new Datasource();
            record.setId(dsList.get(0).getId());
            JSONObject metaJson = JSON.parseObject(dsList.get(0).getMeta());
            if (metaJson == null) {
            	metaJson = new JSONObject();
            }
            metaJson.put("storeType", "slot");
            Map<String, Object> slotMetaMap = null;
            if (StringUtils.isBlank(slotMeta)) {
            	slotMetaMap = getObjectSlotMeta(dsList.get(0).getObjectType(), dsList.get(0).getTableName());
            	metaJson.put("slotMeta", slotMetaMap);
            } else {
            	slotMetaMap = JSON.parseObject(slotMeta);
            	metaJson.put("slotMeta", slotMetaMap);
            }
            record.setMeta(JSON.toJSONString(metaJson));
            record.setGmtModified(now);
            datasourceMapper.updateByPrimaryKeySelective(record);
            
			DsRelationExample dsRelExp = new DsRelationExample();
			dsRelExp.createCriteria().andTenantIdEqualTo(tenantId)
									.andIsDeletedEqualTo(0L)
									.andRelationTypeIn(Arrays.asList("ods","odps"))
									.andSrcDsNameEqualTo(dsName);
			List<DsRelation> dsRels = dsRelationMapper.selectByExample(dsRelExp);
			if (CollectionUtils.isNotEmpty(dsRels)) {
				int delCnt = dsRelationMapper.deleteByExample(dsRelExp);
				log.info("dsName:{} relType:ods delete {} records", dsName, delCnt);
			}
			String tableName = slotMetaMap.get("table").toString();
			String onlineTable = slotMetaMap.get("onlineTable").toString();
			try {
            	Datasource slotOds = new Datasource();
            	slotOds.setDsName("adb3_" + onlineTable);
            	slotOds.setDsType("adb3");
            	slotOds.setTableName(onlineTable);
            	slotOds.setIsDeleted(0L);
            	slotOds.setDbName(slotDbName);
            	slotOds.setGmtModified(new Date());
            	slotOds.setModifyEmpid("admin");
            	slotOds.setGmtCreate(new Date());
            	slotOds.setCreateEmpid("admin");
            	slotOds.setTenantId(tenantId);
                datasourceMapper.insert(slotOds);
        	} catch(Exception e) {}
			try {
				DsRelation dsRel = new DsRelation();
				dsRel.setCreateEmpid("admin");
				dsRel.setDstDsName("adb3_" + onlineTable);
				dsRel.setGmtCreate(now);
				dsRel.setGmtModified(now);
				dsRel.setIsDeleted(0L);
				dsRel.setModifyEmpid("admin");
				dsRel.setRelationType("ods");
				dsRel.setSrcDsName(dsName);
				dsRel.setTenantId(tenantId);
				dsRelationMapper.insert(dsRel);
			} catch(Exception e) {}

        	try {
            	Datasource slotOdps = new Datasource();
            	slotOdps.setDsName("odps_" + tableName);
            	slotOdps.setDsType("odps");
            	slotOdps.setTableName(tableName);
            	slotOdps.setIsDeleted(0L);
            	slotOdps.setDbName("mdp_odps");
            	slotOdps.setGmtModified(new Date());
            	slotOdps.setModifyEmpid("admin");
            	slotOdps.setGmtCreate(new Date());
            	slotOdps.setCreateEmpid("admin");
            	slotOdps.setTenantId(tenantId);
                datasourceMapper.insert(slotOdps);
        	} catch(Exception e) {}
			try {
				DsRelation dsRel = new DsRelation();
				dsRel.setCreateEmpid("admin");
				dsRel.setDstDsName("odps_" + tableName);
				dsRel.setGmtCreate(now);
				dsRel.setGmtModified(now);
				dsRel.setIsDeleted(0L);
				dsRel.setModifyEmpid("admin");
				dsRel.setRelationType("odps");
				dsRel.setSrcDsName(dsName);
				dsRel.setTenantId(tenantId);
				dsRelationMapper.insert(dsRel);
			} catch(Exception e) {}
            
            result.setData(true);
        } catch (Exception e) {
            result.setData(false);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("modifyDsInfoMdpSlot failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public Boolean isObjectDirectWriteByMdp(String tenantId, String objectUniqueCode) {
    	GetObjectByCodeRequest request = new GetObjectByCodeRequest();
    	request.setObjectUniqueCode(objectUniqueCode);
    	request.setDomainAK(domainAk);
    	request.setDomainCode(domainCode);
    	DataResponse<ObjectVO> objResp = null;
    	try {
    		objResp = queryObjectMetaService.getObjectByCode(request);
    		log.info("mdp obj meta {}={}", objectUniqueCode, JSON.toJSONString(objResp));
    	} catch(Exception e) {
    		log.error("queryObjectMetaService.getObject failed", e);
    	}
    	if (objResp != null && objResp.getData() != null && objResp.getData().getDirectWrite() != null) {
    		return objResp.getData().getDirectWrite() > 0;
    	} else {
			return false;
		}
//		Datasource objDsInfo = getDsInfoByObjectCode(tenantId, objectUniqueCode);
//		JSONObject odsDsMetaJson = getOdsTableMetaByDsName(tenantId, objDsInfo.getDsName());
//		if (odsDsMetaJson.containsKey("storeType") && "slot".equalsIgnoreCase(odsDsMetaJson.getString("storeType")) && odsDsMetaJson.getJSONObject("slotMeta") != null) {
//			return true;
//		}
//		return false;
    }
    
    @Override
	public String getDsNameByObjectCode(String tenantId, String objectUniqueCode) {
		return this.getDsInfoByObjectCode(tenantId, objectUniqueCode).getDsName();
	}
    
	private Datasource getDsInfoByObjectCode(String tenantId, String objectUniqueCode) {
		DatasourceExample example = new DatasourceExample();
		example.createCriteria().andTableNameEqualTo(objectUniqueCode).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsTypeEqualTo("obj");
		List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isEmpty(dsList)) {
			throw new QanatBizException(objectUniqueCode + " is not found");
		}
		return dsList.get(0);
	}

    @Override
	public String getDsName(String tenantId, String appName, String dbName, String tableName) {
    	DbInfo dbInfo = null;
    	if (StringUtils.isNotBlank(dbName)) {
    		dbInfo = getDbInfoByName(tenantId, dbName);
    	} else {
    		TenantInfoExample tiExample = new TenantInfoExample();
        	tiExample.createCriteria().andTenantIdEqualTo(tenantId);
        	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
        	if (CollectionUtils.isEmpty(tenantList)) {
        		throw new QanatBizException("tenantId:" + tenantId + " is not configured");
        	}
    		dbInfo = getDbInfoByName(tenantId, tenantList.get(0).getDefaultDw());
    	}
    	return this.getDsName(dbInfo.getId(), dbInfo.getDbType(), tenantId, appName, tableName);
    }

    @Override
    public String getDsName(Long dbId, String dbType, String tenantId, String appName, String tableName) {
    	return dbType + "_" + getAppIdByName(tenantId, appName) + "_" + dbId + "_" + tableName;
    }

    private Long getAppIdByName(String tenantId, String appName) {
    	AppInfoExample example = new AppInfoExample();
    	example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
    	List<AppInfo> apps = appInfoMapper.selectByExample(example);
    	return apps.get(0).getId();
    }

	@Override
	public JSONObject getObjectMeta(String tenantId, String objectType, String objectUniqueCode) {
		GetObjectRequest request = new GetObjectRequest();
		request.setObjectType(objectType);
		request.setObjectUniqueCode(objectUniqueCode);
		DataResponse<ObjectExtendVO> objExtResp = null;
		try {
			objExtResp = queryObjectMetaService.getObject(request);
		} catch(Exception e) {
			log.error("queryObjectMetaService.getObject failed", e);
		}
		if (objExtResp != null && objExtResp.getData() != null) {
			return JSON.parseObject(JSON.toJSONString(objExtResp.getData()));
		} else {
			return null;
		}
	}

    /**
     * 根据地域获取适合的JDBC URL
     * @param dbMetaJson 数据库元数据JSON
     * @return 适合当前地域的JDBC URL
     */
    private String getJdbcUrlByRegion(JSONObject dbMetaJson) {
        // 检查是否启用OXS功能
        if (!oxsEnabled) {
            return dbMetaJson.getString("jdbcUrl");
        }
        
        // 检查当前环境是否为新加坡OXS区
        if (isOxsRegion() && dbMetaJson.containsKey("oxsJdbcUrl")) {
            String oxsJdbcUrl = dbMetaJson.getString("oxsJdbcUrl");
            if (StringUtils.isNotBlank(oxsJdbcUrl)) {
                log.info("Using OXS JDBC URL for region: {}, env: {}", regionType, environmentType);
                return oxsJdbcUrl;
            }
        }
        
        // 默认使用标准jdbcUrl
        return dbMetaJson.getString("jdbcUrl");
    }

    /**
     * 判断当前是否为OXS区域
     * @return true如果是OXS区域
     */
    private boolean isOxsRegion() {
        return "singapore".equalsIgnoreCase(regionType) && 
               StringUtils.containsIgnoreCase(environmentType, "oxs");
    }
}