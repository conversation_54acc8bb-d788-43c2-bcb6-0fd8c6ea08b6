package com.aliyun.wormhole.qanat.service.metaq;

import javax.annotation.PostConstruct;

import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.common.message.Message;

import com.taobao.metaq.client.MetaProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MetaQProducer {
    
    private MetaProducer producer;
    
    @PostConstruct
    private void init() {
        producer = new MetaProducer("qanat_metaq_group");
        try {
            producer.start();
        } catch (Exception e) {
            log.error("MetaQProducer init failed", e);
        }
    }
    
    public void shutdown() {
        producer.shutdown();
    }
    
    public void sendMsg(String topic, String tag, String key, String message) {
        Message msg = new Message(topic, tag, key, message.getBytes());

        SendResult sendResult;
        try {
            sendResult = producer.send(msg);
            log.info("msgId={}, sendStatus={}", sendResult.getMsgId(), sendResult.getSendStatus());
        } catch (Exception e) {
            log.error("send msg failed", e);
        }
    }
}
