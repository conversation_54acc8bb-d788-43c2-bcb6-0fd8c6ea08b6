package com.aliyun.wormhole.qanat.service.metaq;

import java.util.List;

import javax.annotation.PostConstruct;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.aliyun.wormhole.qanat.service.kafka.IntlKafkaMessageExporter;
import com.taobao.metaq.client.MetaPushConsumer;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class NoteMetaqRouteListener {
	
    @Value("${env.unit}")
    private String envUnit;
    
    @PostConstruct
    private void init() {
    	if (!"center".equalsIgnoreCase(envUnit)) {
	    	MetaPushConsumer consumer = new MetaPushConsumer("CID_qanat-telesales_incrsync-cid_ext-noteCreateTag");
	    	try {
		        consumer.subscribe("SOP_TOPIC", "noteCreateTag");
				consumer.setConsumeThreadMin(1);
				consumer.setConsumeThreadMax(10);
				consumer.setPullInterval(1000);
				
		        consumer.setMessageListener(new MessageListenerConcurrently() {
		            @Override
		            public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
		                for (MessageExt msg : msgs) {
		                    try {
		                        String msgBody = new String(msg.getBody(), "utf-8");
		                        log.info("msg={}", msgBody);
		                        JSONObject msgJson = JSON.parseObject(msgBody);
		                        
		                        IntlKafkaMessageExporter exporter = new IntlKafkaMessageExporter("metaq_route_sop_topic_notecreate", "33.0.155.168:9092,33.0.155.166:9092,33.0.155.158:9092,33.0.155.156:9092,33.0.155.154:9092,33.0.155.152:9092,33.0.155.162:9092,33.0.155.160:9092,33.0.155.157:9092,33.0.155.155:9092,33.0.155.153:9092,33.0.149.140:9092,33.0.155.163:9092,33.0.155.161:9092,33.0.155.167:9092,33.0.149.139:9092,33.0.155.165:9092,33.0.155.159:9092,33.0.155.164:9092,33.0.149.141:9092");
		                        exporter.send(msg.getProperty("EagleEye-TraceID"), msgBody, msgJson.getString("cid"));
		                        log.info("send to kakfa finish, topic={} msg={}", "metaq_route_sop_topic_notecreate", msgBody);
		                    } catch (Exception e) {
		                        log.error("send to kakfa failed, message={}", e.getMessage(), e);
		    	                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
		                    }
		                }
		                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
		            }
		        });
	
		        consumer.start();
	    	} catch(Exception e) {
	    		log.error("create consumer for object create failed", e);
	    	}
    	}
    }
}