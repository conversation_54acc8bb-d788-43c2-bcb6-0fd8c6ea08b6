package com.aliyun.wormhole.qanat.service.template;

public interface Pg94SyncTemplate {
	
	public static String BLINK_CHECK_COMPUTE_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--********************************************************************--\n" + 
			"create table mq_source (\n" + 
			"    id bigint,\n" + 
			"    src varchar,\n" + 
			"    __store_timestamp__ varchar header  \n" + 
			") with (\n" + 
			"  type = 'metaq',\n" + 
			"  topic = 'qanat_pg94_check_id_midlayer_topic',\n" + 
			"  tag = '%s',\n" + 
			"  consumerGroup = '%s',\n" + 
			"  fieldDelimiter = '`',\n" + 
			"  pullIntervalMs = '200'\n" + 
			");\n" + 
			"\n" + 
			"CREATE TABLE mysql_dim (\n" + 
			"  %s,\n" + 
			"  PRIMARY KEY (%s),\n" + 
			"  PERIOD FOR SYSTEM_TIME\n" + 
			") WITH (\n" + 
			"  %s\n" + 
			");\n" + 
			"\n" + 
			"CREATE TABLE pg_dim (\n" + 
			"  %s,\n" + 
			"  PRIMARY KEY (%s),\n" + 
			"  PERIOD FOR SYSTEM_TIME\n" + 
			") WITH (\n" + 
			"    type = 'RDS_PG',\n" + 
			"    tablefactoryclass = 'com.alibaba.blink.connectors.rds.RdsPgTableFactory',\n" +
			"    url='***************************************************************************************************************************',\n" + 
			"    tableName='%s',\n" + 
			"    userName='mariana',\n" + 
			"    password='Postgres1234'\n" + 
			");\n" + 
			"\n" + 
			"CREATE TABLE bcp_mq_sink (\n" + 
			"    msg varchar\n" + 
			") WITH (\n" + 
			"    type='metaq',\n" + 
			"    topic='qanat_pg94_check_result_topic',\n" + 
			"    producerGroup='qanat_pg94_check_result_topic_BLINK_PRODUCER',\n" + 
			"    tag='%s',\n" + 
			"    encoding='utf-8',\n" + 
			"    fieldDelimiter='`',\n" + 
			"    retryTimes='5',\n" + 
			"    sleepTimeMs='50'\n" + 
			");\n" + 
			"\n" + 
			"CREATE FUNCTION checkData AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDataCheckUdf';\n" + 
			"CREATE FUNCTION qanatConcat AS 'com.aliyun.wormhole.qanat.blink.udf.QanatConcatUdf';\n" + 
			"\n" + 
			"create view v_id as\n" + 
			"select \n" + 
			"    id,\n" + 
			"    src\n" + 
			"from mq_source\n" + 
			";\n" + 
			"\n" + 
			"create view v_bcp as\n" + 
			"select \n" + 
			"    a.id,\n" + 
			"    a.src,\n" + 
			"    checkData('%s',\n" + 
			"    qanatConcat('|', %s),\n" + 
			"    qanatConcat('|', %s)) as msg\n" + 
			"from v_id as a\n" + 
			"left join mysql_dim FOR SYSTEM_TIME AS OF PROCTIME () as b on a.id=b.%s\n" + 
			"left join pg_dim FOR SYSTEM_TIME AS OF PROCTIME () as c on a.id=c.%s\n" + 
			";\n" + 
			"\n" + 
			"insert into bcp_mq_sink\n" + 
			"select (case when msg='' then concat_ws('|', '%s', id, src, 'OK') else concat_ws('|', '%s', id, src, msg) end) as msg\n" + 
			"from v_bcp;\n" + 
			"";
	
	public static String BLINK_CHECK_CORRECT_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--********************************************************************--\n" + 
			"create table mq_source (\n" + 
			"    ids varchar \n" + 
			") with (\n" + 
			"  type = 'metaq',\n" + 
			"  topic = 'qanat_pg94_id_correct_topic',\n" + 
			"  tag = '%s',\n" + 
			"  consumerGroup = '%s',\n" + 
			"  pullIntervalMs = '200'\n" + 
			");\n" + 
			"\n" + 
			"CREATE TABLE mysql_dim (\n" + 
			"  %s,\n" + 
			"  PRIMARY KEY (%s),\n" + 
			"  PERIOD FOR SYSTEM_TIME\n" + 
			") WITH (\n" + 
			"  %s\n" + 
			");\n" + 
			"\n" + 
			"CREATE TABLE pg_sink (\n" + 
			" %s,\n" + 
			"  PRIMARY KEY (%s)\n" + 
			") WITH (\n" + 
			"    type = 'RDS_PG',\n" + 
			"    tablefactoryclass = 'com.alibaba.blink.connectors.rds.RdsPgTableFactory',\n" + 
			"    url='***************************************************************************************************************************',\n" + 
			"    tableName='%s',\n" + 
			"    userName='mariana',\n" + 
			"    password='Postgres1234'\n" + 
			");\n" + 
			"\n" + 
			"create view v_id as\n" + 
			"select id from mq_source, lateral table (STRING_SPLIT (ids, ',')) as T (id)\n" + 
			";\n" + 
			"\n" + 
			"insert into pg_sink\n" + 
			"select b.%s\n" + 
			"from v_id as a\n" + 
			"inner join mysql_dim FOR SYSTEM_TIME AS OF PROCTIME () as b on a.id=b.id;";
	
	public static String BLINK_CHECK_ALL_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--********************************************************************--\n" + 
			"CREATE TABLE check_all_source (\n" + 
			"  %s bigint\n" + 
			") WITH (\n" + 
			"    type = 'custom',\n" + 
			"    tableFactoryClass = 'com.aliyun.wormhole.qanat.blink.mysql.scan.MySQLScanTableFactory',\n" + 
			"    %s\n" + 
			");\n" + 
			"\n" + 
			"CREATE TABLE bcp_id_midlayer_mq_sink (\n" + 
			"    id bigint,\n" + 
			"    src varchar\n" + 
			") WITH (\n" + 
			"    type='metaq',\n" + 
			"    topic='qanat_pg94_check_id_midlayer_topic',\n" + 
			"    producerGroup='qanat_pg94_check_id_midlayer_topic_BLINK_PRODUCER',\n" + 
			"    tag='%s',\n" + 
			"    encoding='utf-8',\n" + 
			"    fieldDelimiter='`',\n" + 
			"    retryTimes='5',\n" + 
			"    sleepTimeMs='50'\n" + 
			");\n" + 
			"\n" + 
			"insert into bcp_id_midlayer_mq_sink\n" + 
			"select \n" + 
			"    %s as id,\n" + 
			"    'all' as src\n" + 
			"from check_all_source;";
	
	public static String CHECK_ALL_DAG_SCRIPT = "Dag dag = new Dag(\"DAG_PgCheckAll_%s\");\r\n" +
            "BlinkBatchNode idScan = new BlinkBatchNode(\"BlinkBatch_pg_%s\", dag);\r\n" + 
            "idScan.setJobName(\"%s\");\r\n" + 
            "BlinkStreamNode chkCompute = new BlinkStreamNode(\"BlinkStream_pg_%s\", dag);\r\n" + 
            "chkCompute.setJobName(\"%s\");\r\n" + 
            "BlinkStreamNode chkCorrect = new BlinkStreamNode(\"BlinkStream_pg_%s\", dag);\r\n" + 
            "chkCorrect.setJobName(\"%s\");\r\n" + 
            "return dag;";
}
