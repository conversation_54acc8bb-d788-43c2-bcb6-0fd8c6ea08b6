package com.aliyun.wormhole.qanat.service.viewmodel.v2;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.ResourcePackage;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.FlowCtlService;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.Extension;
import com.aliyun.wormhole.qanat.dal.domain.ExtensionExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ExtensionMapper;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.RelatedDataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Relation;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelSqlBuilder;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class LookupProcessorV2 {
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private ExtensionMapper extensionMapper;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private DatasourceMapper datasourceMapper;
    
    @Resource
    private DatasourceService dsInfoService;
    
    @Resource
    private ViewModelSqlBuilder viewModelSqlBuilder;
	
	@Resource
	private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;
	
	@Resource
	private FlowCtlService flowCtlService;
	
	@Resource
	private ComponentObjectProcessorV2 componentObjectProcessorV2;
    
    @Value("${datatube.codegen.version}")
    private String codegenVersion;
	
	private Datasource getDsInfoByObjectCode(String tenantId, String objectUniqueCode) {
		DatasourceExample example = new DatasourceExample();
		example.createCriteria().andTableNameEqualTo(objectUniqueCode).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsTypeEqualTo("obj");
		List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isEmpty(dsList)) {
			throw new QanatBizException(objectUniqueCode + " is not found");
		}
		return dsList.get(0);
	}
    
    public boolean processIncrSyncJob(String tenantId, Long appId, String appName, String jobName,
			ViewModel dataModel, String etlDbName, String tableName, Long dstDsId, String operateEmpid, Long versionId,
			String pkField, List<String> objCodeList, Long datatubeInstId) {
		String pkFieldType = dataModel.getObject().getFields().stream().filter(e->e.isPk()).map(e->e.getType()).collect(Collectors.toList()).get(0);
    	List<String> relCols = new ArrayList<>();
    	List<String> mainObjColsExPk = new ArrayList<>();
    	List<String> mainObjColsDef = new ArrayList<>();
    	List<String> relColDefs = new ArrayList<>();
    	Map<String, String> colMap = new HashMap<>();
    	List<String> compJoinList = new ArrayList<>();
    	List<String> tableJoinList = new ArrayList<>();
    	List<ViewModel.Field> funcFields = new ArrayList<>();
    	JSONObject dbMetaJson = dsInfoService.getDbMetaByName(etlDbName);
    	String pkFieldRef = null;
    	for (ViewModel.Field field : dataModel.getObject().getFields()) {
    		if (field.getObject() != null && (objCodeList == null || objCodeList.contains(field.getObject().getCode()))) {
				if ("varchar".equalsIgnoreCase(field.getType())) {
					relCols.add("JSON_VALUE(" + field.getObject().getCode() + ".x, '$." + field.getCode() + "')");
				} else {
					relCols.add("CAST(JSON_VALUE(" + field.getObject().getCode() + ".x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
				}
    			relColDefs.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
				colMap.put(field.getCode(), "CAST(JSON_VALUE(" + field.getObject().getCode() + ".x, '$." + field.getCode() + "') AS " + field.getType() + ")");
				
				List<String> joinOnCondsLeft = new ArrayList<>();
				List<String> joinOnCondsRight = new ArrayList<>();
				for (ViewModel.Relation relation : field.getObject().getRelations()) {
					 String refObj = relation.getRelatedField().split("\\.")[0];
					 String refField = relation.getRelatedField().split("\\.")[1];
					 String refValue = null;
					 if (refObj.equalsIgnoreCase(dataModel.getObject().getCode())) {
						 refValue = relation.getRelatedField();
					 } else {
						 refValue = "JSON_VALUE(" + refObj + ".x, '$." + refField + "')";
					 }
					joinOnCondsLeft.add(field.getObject().getCode() + "." + relation.getField() + (StringUtils.isBlank(relation.getOp()) ? "=" : relation.getOp()) + "?");
					joinOnCondsRight.add(refValue);
				}
				String selectSql = "select * from " + viewModelSqlBuilder.getSubQueryFromFieldWithRefObj(tenantId, field.getCode(), field.getObject(), dbMetaJson.getString("dbType")) + " where " + StringUtils.join(joinOnCondsLeft, " and ");
				String blinkSql = null;
				ExtensionExample example = new ExtensionExample();
				example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + getDbType(tenantId, etlDbName)).andPluginEqualTo(field.getObject().getRef());
				List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
				String dbName = "origion".equalsIgnoreCase(field.getObject().getLookupFrom()) ? exts.get(0).getDbName() : etlDbName;
				if ("inner join".equalsIgnoreCase( field.getObject().getRelationType())) {
					blinkSql = " , lateral table(queryDim(concat_ws('|',__traceId__,'" + dbName + "'),'" + selectSql.replace("'", "''") + "'," + StringUtils.join(joinOnCondsRight, ",") + ")) as " + field.getObject().getCode() + "(x)\n";
				} else {
					blinkSql = " left join lateral table(queryDim(concat_ws('|',__traceId__,'" + dbName + "'),'" + selectSql.replace("'", "''") + "'," + StringUtils.join(joinOnCondsRight, ",") + ")) as " + field.getObject().getCode() + "(x) on true\n";
				}
				compJoinList.add(blinkSql);
    		} else if (field.isFunc()) {
    			funcFields.add(field);
    			continue;
    		} else if (field.isPk()) {
    			pkFieldRef = field.getRef();
    			mainObjColsDef.add(pkFieldRef + " " + field.getType());
    			continue;
    		} else if ("'null'".equalsIgnoreCase(field.getRef())) {
    			continue;
    		} else {
				if ("varchar".equalsIgnoreCase(field.getType())) {
					colMap.put(field.getCode(), dataModel.getObject().getCode() + "." + field.getCode() );
		    		mainObjColsExPk.add(field.getCode());
				} else {
					colMap.put(field.getCode(), "CAST(" + dataModel.getObject().getCode() + "." + field.getCode() + " AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
		    		mainObjColsExPk.add("CAST(" + field.getCode() + " AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " ) AS " + field.getCode());
				}
				mainObjColsDef.add(field.getCode() + " " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
			}
    	}
    	if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
    		for (RelatedDataObject relObj : dataModel.getRelatedObjects()) {
    			if ("none".equalsIgnoreCase(relObj.getLookupFrom())) {
    				continue;
    			}
    			Map<String, Relation> rels = relObj.getRelations().stream().filter(e->!e.getRelatedField().startsWith("exp#")).collect(Collectors.toMap(Relation::getField, Function.identity()));
    			List<ViewModel.Field> fields = relObj.getFields().stream().filter(e->!rels.containsKey(e.getCode())).collect(Collectors.toList());
				if (objCodeList == null || objCodeList.contains(relObj.getCode())) {
	    			for (ViewModel.Field field : fields) {
	    				if ("varchar".equalsIgnoreCase(field.getType())) {
	        				relCols.add("JSON_VALUE(" + relObj.getCode() + ".x, '$." + field.getCode() + "')");
	    					colMap.put(relObj.getCode() + "." + field.getCode(), "JSON_VALUE(" + relObj.getCode() + ".x, '$." + field.getCode() + "')");
	    				} else {
	        				relCols.add("CAST(JSON_VALUE(" + relObj.getCode() + ".x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
	    					colMap.put(relObj.getCode() + "." + field.getCode(), "CAST(JSON_VALUE(" + relObj.getCode() + ".x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
	    				}
	        			relColDefs.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
	    			}
				}
    			
    			List<String> joinOnCondsLeft = new ArrayList<>();
				List<String> joinOnCondsRight = new ArrayList<>();
    			List<String> lookupOrigionConds = new ArrayList<>();
				for (ViewModel.Relation relation : relObj.getRelations()) {
					 String refObj = relation.getRelatedField().split("\\.")[0];
					 String refField = relation.getRelatedField().split("\\.")[1];
					 String refValue = null;
					 if (refObj.equalsIgnoreCase(dataModel.getObject().getCode())) {
						 refValue = relation.getRelatedField();
					 } else {
						 refValue = "JSON_VALUE(" + refObj + ".x, '$." + refField + "')";
					 }
					joinOnCondsLeft.add(relObj.getCode() + "." + relation.getField() + (StringUtils.isBlank(relation.getOp()) ? "=" : relation.getOp()) + "?");
					lookupOrigionConds.add(relObj.getFields().stream().filter(item -> item.getCode().equalsIgnoreCase(relation.getField())).collect(Collectors.toList()).get(0).getRef() + (StringUtils.isBlank(relation.getOp()) ? "=" : relation.getOp()) + "?");
					joinOnCondsRight.add(refValue);
				}
				String selectSql = "select * from (" + viewModelSqlBuilder.getSubQueryFromObjectForLookup(tenantId,relObj, dbMetaJson.getString("dbType")) + ") as " + relObj.getCode() + " where " + StringUtils.join(joinOnCondsLeft, " and ");
				String blinkSql = null;
				String dbName = null;
				if ("component".equalsIgnoreCase(relObj.getType())) {
					ExtensionExample example = new ExtensionExample();
					example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + getDbType(tenantId, etlDbName)).andPluginEqualTo(relObj.getRef());
					List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
					dbName = StringUtils.isNotBlank(exts.get(0).getDbName()) ? exts.get(0).getDbName() : etlDbName;
					
					String fkFieldOriginName = null;
					List<String> compFieldsSelOrig = new ArrayList<>();
					for (ViewModel.Field field : relObj.getFields()) {
						if (field.isPk()) {
							fkFieldOriginName = field.getRef();
							continue;
						}
						compFieldsSelOrig.add(field.getRef());
					}
					selectSql = componentObjectProcessorV2.getComponentSql(tenantId, etlDbName, fkFieldOriginName, compFieldsSelOrig, exts.get(0));
				} else {
	    			JSONObject dsMetaJson = dsInfoService.getDbMetaByDsName(tenantId, relObj.getRef());
					dbName = "origion".equalsIgnoreCase(relObj.getLookupFrom()) ? dsMetaJson.getString("dbName") : etlDbName;
					
					if ("origion".equalsIgnoreCase(relObj.getLookupFrom())) {
						selectSql = viewModelSqlBuilder.getSubQueryFromObjectForLookup(tenantId,relObj, dbMetaJson.getString("dbType")) + " where " + StringUtils.join(lookupOrigionConds, " and ");
					}
				}
				if ("inner join".equalsIgnoreCase(relObj.getRelationType())) {
					blinkSql = " , lateral table(queryDim(concat_ws('|',__traceId__,'" + dbName + "'),'" + selectSql.replace("'", "''") + "'," + StringUtils.join(joinOnCondsRight, ",") + ")) as " + relObj.getCode() + "(x)\n";
				} else {
					blinkSql = " left join lateral table(queryDim(concat_ws('|',__traceId__,'" + dbName + "'),'" + selectSql.replace("'", "''") + "'," + StringUtils.join(joinOnCondsRight, ",") + ")) as " + relObj.getCode() + "(x) on true\n";
				}
				if (objCodeList == null || objCodeList.contains(relObj.getCode())) {
					tableJoinList.add(blinkSql);
				}
    		}
    	}
    	for (ViewModel.Field field : funcFields) {
    		String funcExpress = field.getRef();
			String funcCode = parseFuncExpress(funcExpress).get(0);
			String[] cols = parseFuncExpress(funcExpress).get(1).split(",");
			List<String> exps = new ArrayList<>();
			for (String col : cols) {
				if (col.startsWith("'") && col.endsWith("'")) {
					exps.add(col);
				} else {
					exps.add(colMap.get(col));
				}
			}
			ExtensionExample example = new ExtensionExample();
			example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andCodeEqualTo(funcCode);
			List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
			if (CollectionUtils.isEmpty(exts)) {
				log.error("funcCode:{} not found", funcCode);
				return false;
			}
			String func = exts.get(0).getPlugin().equalsIgnoreCase("groovy")?"groovyFunc":"dfaasFunc";
			String funcExp = null;
			if (field.getType().equalsIgnoreCase("varchar")) {
				funcExp = func + "('" + tenantId + "', '" + funcCode + "'," + StringUtils.join(exps, ",") + ") AS " + field.getCode();
			} else {
				funcExp = "CAST(" + func + "('" + tenantId + "', '" + funcCode + "'," + StringUtils.join(exps, ",") + ") AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + ") AS " + field.getCode();
			}
			relColDefs.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
			relCols.add(funcExp);
    	}
    	String dsName = null;
    	if ("metadata".equalsIgnoreCase(dataModel.getObject().getType())) {
    		Datasource objDsInfo = getDsInfoByObjectCode(tenantId, dataModel.getObject().getRef());
	    	dsName = objDsInfo.getDsName();
    	} else if ("table".equalsIgnoreCase(dataModel.getObject().getType())) {
	    	dsName = dataModel.getObject().getRef();
    	}
		JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId,dsName);
    	
		flowCtlService.setFlowControlIfNotExists(datatubeInstId, jobName, 1.0);
		
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        String pkDefs = null;
        String pkInDdl = null;
        String pkInLookup = null;
        if (StringUtils.isNotBlank(dataModel.getSettings().getDistributeKey())) {
        	String distPkType = dataModel.getObject().getFields().stream().filter(e -> e.getCode().equalsIgnoreCase(dataModel.getSettings().getDistributeKey())).map(e -> e.getType()).collect(Collectors.toList()).get(0);
        	pkDefs = pkField + " " + pkFieldType + ", " + dataModel.getSettings().getDistributeKey() + " " + distPkType;
        	pkInDdl = pkField + "," + dataModel.getSettings().getDistributeKey();
        	pkInLookup = dataModel.getObject().getCode() + "." + pkField + ", " + dataModel.getObject().getCode() + "." + dataModel.getSettings().getDistributeKey();
        } else {
        	pkDefs = pkField + " " + pkFieldType;
        	pkInDdl = pkField;
        	pkInLookup = dataModel.getObject().getCode() + "." + pkField;
        }
		
		String def = "--SQL\n" + 
    			"--********************************************************************--\n" + 
    			"--Author: " + operateEmpid + "\n" + 
    			"--CreateTime: " + sdf.format(new Date()) + "\n" + 
    			"--Comment: " + ("sync for " + tableName + " from " + dataModel.getObject().getCode()) + "\n" + 
    			"--Version: " + codegenVersion + "\n" + 
    			"--********************************************************************--\n" + 
    			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\r\n" + 
				"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\r\n" + 
				"CREATE FUNCTION groovyFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatFunctionUdf';\r\n" +
				"CREATE FUNCTION dfaasFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDfaasFunctionUdf';\r\n" +
    			"CREATE FUNCTION trace AS 'com.aliyun.wormhole.qanat.blink.udf.QanatTraceUdf';\n" +
    			"CREATE FUNCTION flowCtl AS 'com.aliyun.wormhole.qanat.blink.udf.QanatFlowControlUdf';\n" +
    			"CREATE FUNCTION hololog AS 'com.aliyun.wormhole.qanat.blink.udf.QanatHoloLogTUdf';\n" +
				"\n";
		
		String dsSink = "create table update_sink (\r\n" + 
				"    " + pkDefs + ",\r\n" + 
				"    " + StringUtils.join(relColDefs, ",") + ",\r\n" + 
				"    __trace_id__ varchar,\r\n" + 
				"    primary key(" + pkInDdl + ")\r\n" + 
				") with (\r\n" + 
				"    type = 'QANAT_ADB30',\r\n" + 
				"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\r\n" + 
				"    dbName='" + etlDbName + "',\r\n" + 
				"    tableName='" + tableName + "',\r\n" + 
				"    replaceMode = 'update',\r\n" + 
				"    writeMode = 'single',\r\n" + 
				"    streamEvent = 'disable'\r\n" +
				");" +
				"\n" +
				"insert into update_sink select * from v_lookup;\n" +
				"\n";

        String dsSource = 
    			"create table holobinlog_source (\n" + 
    			"    hg_binlog_lsn BIGINT,\n" +
    			"    hg_binlog_event_type BIGINT,\n" +
    			"    hg_binlog_timestamp_us BIGINT,\n" +
    			"   " + StringUtils.join(mainObjColsDef, ",\n") + "\n" + 
    			") with (\n" + 
    			"  type = 'hologres',\n" + 
    			"  dbname = '" + srcDsMetaJson.getJSONObject("odsConf").getString("database") + "',\n" + 
    			"  tablename = '" + tableName + "',\n" + 
    			"  username = '" + srcDsMetaJson.getJSONObject("odsConf").getString("username") + "',\n" +
    			"  password = '" + srcDsMetaJson.getJSONObject("odsConf").getString("password") + "',\n" +
    			"  endpoint = '" + srcDsMetaJson.getJSONObject("odsConf").getString("endpoint") + "',\n" +
    			"  binlog = 'true',\n" +
    			"  binlogMaxRetryTimes = '10',\n" +
    			"  binlogRetryIntervalMs = '500',\n" +
    			"  binlogBatchReadSize = '256'\n" +
    			");\n" +
    			"\n" + 
    			"create view v_source as \n" + 
    			"select *, trace(UUID(),hg_binlog_lsn,hg_binlog_event_type,hg_binlog_timestamp_us," + pkFieldRef + ",'" + jobName + "','holobinlog') as __trace_id__\n" + 
    			"from holobinlog_source;\n" + 
    			"\n" + 
    			"create view v_main_obj as \n" + 
    			"select \n" + 
    			"    *,\n" + 
    			"    flowCtl(__trace_id__,'" + jobName + "',cast(" + pkField + " as varchar)) as __traceId__\n" +
    			"from v_source\n" + 
    			"where hg_binlog_event_type=5;\n" +
    			"\n" ;
        
    	String lookupSql = "create view v_lookup as\r\n"
    			+ "select " + pkInLookup + ", " + StringUtils.join(relCols, ",") + ", " + dataModel.getObject().getCode() + ".__traceId__ \r\n"
    			+ "from v_main_obj as " + dataModel.getObject().getCode() + "\n"
    			+ (CollectionUtils.isNotEmpty(compJoinList) ? StringUtils.join(compJoinList, " ") : "")
    			+ (CollectionUtils.isNotEmpty(tableJoinList) ? StringUtils.join(tableJoinList, " ") : "")
    			+ ";\r\n"
    			;
    	
    	
	    String sql = def + "\r\n" + dsSource  + "\r\n" + lookupSql + "\r\n" + dsSink + "\r\n;";
	    blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
	    		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDF, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_KAFKA010, ResourcePackage.BLINK_HSF_UDF), 
	    		false);
	    
	    DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        return true;
	}
    
    private String getDbType(String tenantId, String dbName) {
    	DbInfoExample dbInfoExample = new DbInfoExample();
    	dbInfoExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
    	List<DbInfo> dbInfos = dbInfoMapper.selectByExample(dbInfoExample);
    	if (CollectionUtils.isEmpty(dbInfos)) {
    		throw new QanatBizException("DbInfo not found:" + dbName);
    	}
    	return dbInfos.get(0).getDbType();
    }

	public List<String> parseFuncExpress(String funcExpress) {
		String namePattern = "([_a-zA-Z0-9]+\\w*)";
		String argsPattern = "([_a-zA-Z0-9,\\.\\$']*)";
		String funcPattern = namePattern + "\\(" + argsPattern + "\\)";
		Pattern p = Pattern.compile(funcPattern);
		Matcher m = p.matcher(funcExpress);
		List<String> list = new ArrayList<String>();
		while (m.find()) {
			list.add(m.group(1));
			list.add(m.group(2));
		}
		return list;
	}
	public static void main(String[]args) {
		System.out.println(new LookupProcessorV2().parseFuncExpress("prehandle_multiple_tree_value(industries)"));
	}
}
