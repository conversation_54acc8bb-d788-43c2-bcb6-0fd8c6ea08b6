package com.aliyun.wormhole.qanat.job;

import java.net.InetAddress;
import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.BlinkNode;
import com.aliyun.wormhole.qanat.api.dag.DagInstStatus;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * Restart Blink任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatRestartBlinkJobProcessor extends JavaProcessor {
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Override
    public ProcessResult process(JobContext context) {
        String taskName = context.getTaskName();
        log.info("Qanat Blink Restart Job[{}] start.", taskName);
        try {
            Map<String, Object> instParamsMap = null;
            if (StringUtils.isNotBlank(context.getInstanceParameters())) {
                instParamsMap = (Map<String, Object>)JSON.parseObject(context.getInstanceParameters(), Map.class);
            }
            if (instParamsMap == null) {
                instParamsMap = (Map<String, Object>)JSON.parseObject(context.getJobParameters(), Map.class);
            }
            BlinkNode blink = ((JSONObject)instParamsMap.get("node")).toJavaObject(BlinkNode.class);
            String subTaskInstId = String.valueOf(instParamsMap.get("subTaskInstId"));
            String operator = (String)instParamsMap.get("operator");
            String tenantId = String.valueOf(instParamsMap.get("tenantId"));
            String appName = String.valueOf(instParamsMap.get("appName"));
            
            TaskInstance taskInstUpd = new TaskInstance();
            taskInstUpd.setId(Long.valueOf(subTaskInstId));
            taskInstUpd.setGmtModified(new Date());
            taskInstUpd.setModifyEmpid(operator);
            taskInstUpd.setExternalInstId(context.getJobInstanceId() + "");//SchedulerX任务实例id
            taskInstUpd.setStatus(DagInstStatus.EXECUTING.getCode().byteValue());
            taskInstUpd.setHostAddr(InetAddress.getLocalHost().getHostAddress());
            taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);

            Date startTime = new Date();
            Thread.sleep(5000);//往前追5s
            blinkService.stopJob(tenantId, appName, blink.getJobName());
            log.info("blink job[{}] has been stopped", blink.getJobName());
            Long blinkInstId = blinkService.startJob(tenantId, appName, blink.getJobName(), startTime);
            log.info("blink job[{}] has been started, blinkInstId={}, startTime={}", blink.getJobName(), blinkInstId, startTime);
            taskInstUpd = new TaskInstance();
            taskInstUpd.setId(Long.valueOf(subTaskInstId));
            taskInstUpd.setGmtModified(new Date());
            taskInstUpd.setEndTime(new Date());
            taskInstUpd.setModifyEmpid(operator);
            taskInstUpd.setStatus(DagInstStatus.EXECUTING.getCode().byteValue());
            taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
        } catch (QanatBizException e) {
            log.error("BlinkRestart任务调度异常:{}", e.getMessage());
            return new ProcessResult(false, e.getMessage());
        } catch (Exception e) {
            log.error("BlinkRestart任务调度异常", e, e.getMessage());
            return new ProcessResult(false);
        }
        return new ProcessResult(true);
    }
    
    @Override
    public void kill(JobContext context) {
        // TODO Auto-generated method stub
    }
}