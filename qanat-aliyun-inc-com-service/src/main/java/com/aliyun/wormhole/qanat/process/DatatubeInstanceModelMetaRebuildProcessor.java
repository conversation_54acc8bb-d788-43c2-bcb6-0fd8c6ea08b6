package com.aliyun.wormhole.qanat.process;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.service.FlowCtlService;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelField;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelFieldExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelObj;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelObjExample;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfo;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfo;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelVersionWithBLOBs;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceModelFieldMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceModelObjMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TenantInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelVersionMapper;
import com.aliyun.wormhole.qanat.service.util.YamlUtil;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.RelatedDataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelHandler;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelOptimizer;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 管道实例元数据重构任务
 * <AUTHOR>
 * 2022年8月8日
 */
@Slf4j
@Component
public class DatatubeInstanceModelMetaRebuildProcessor extends JavaProcessor {
    
    @Resource 
    private TenantInfoMapper tenantInfoMapper;
    
    @Resource 
    private ViewModelInfoMapper viewModelInfoMapper;
    
    @Resource 
    private ViewModelVersionMapper viewModelVersionMapper;
    
    @Resource 
    private DatatubeInstanceMapper datatubeInstanceMapper;
    
    @Resource 
    private DatatubeInstanceModelObjMapper datatubeInstanceModelObjMapper;
    
    @Resource 
    private DatatubeInstanceModelFieldMapper datatubeInstanceModelFieldMapper;
    
    @Resource
    private ViewModelOptimizer viewModelOptimizer;
    
    @Resource
    private FlowCtlService flowCtlService;
    
    @Resource 
    private ViewModelHandler viewModelHandler;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
            log.info("DatatubeInstanceModelMetaRebuildProcessor, param=[]", context.getJobParameters());
            String tenantId = paramsJson.getString("tenantId");
            if (StringUtils.isBlank(tenantId)) {
            	log.info("tenantId is empty");
                return new ProcessResult(false, "tenantId is empty");
            }
            
    		TenantInfoExample tiExample = new TenantInfoExample();
        	tiExample.createCriteria().andTenantIdEqualTo(tenantId);
        	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
        	if (CollectionUtils.isEmpty(tenantList)) {
            	return new ProcessResult(false, "no datatube instances found");
        	}
        	
            JSONArray datatubeInstIdArray = paramsJson.getJSONArray("datatubeInstIds");
            List<Long> datatubeInstIds = new ArrayList<>();
            if (datatubeInstIdArray != null && datatubeInstIdArray.size() > 0) {
            	for (int i = 0 ; i < datatubeInstIdArray.size(); i++) {
                	datatubeInstIds.add(datatubeInstIdArray.getLong(i));
            	}
            }
            
            DatatubeInstanceExample diExample = new DatatubeInstanceExample();
            DatatubeInstanceExample.Criteria criteria = diExample.createCriteria();
            criteria.andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andProviderEqualTo("viewmodel").andIsTestEqualTo(0L);
            if (CollectionUtils.isNotEmpty(datatubeInstIds)) {
            	criteria.andIdIn(datatubeInstIds);
            }
            List<DatatubeInstance> datatubeInstList = datatubeInstanceMapper.selectByExample(diExample);
            if (CollectionUtils.isEmpty(datatubeInstList)) {
            	return new ProcessResult(false, "no datatube instances found");
            }
            
            Date now = new Date();
            Long opBatchNo = System.currentTimeMillis();
        	for (DatatubeInstance inst : datatubeInstList) {
            	log.info("datatube[{}-{}-{}] update started", inst.getId(), inst.getName(), inst.getProviderId());
            	try {
            		ViewModelInfoExample example = new ViewModelInfoExample();
            		example.createCriteria().andTenantIdEqualTo(tenantId).andIdEqualTo(inst.getProviderId()).andIsDeletedEqualTo(0L);
            		List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(example);
            		if (CollectionUtils.isEmpty(viewModelInfos)) {
            			throw new QanatBizException("datatube:" + inst.getId() + " has no viewmodel configed");
            		}
            		ViewModelInfo viewModelInfo = viewModelInfos.get(0);
            		
                    ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());

                    ViewModel originModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
                	ViewModel sysModel = null;
                	if (originModel.isDynamic()) {
                		sysModel = viewModelOptimizer.getOptimizedViewModel(tenantId, modelVersion.getUserYaml());
                	} else {
                		sysModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
                	}
                	DatatubeInstance updDi = new DatatubeInstance();
                	updDi.setId(inst.getId());
                	updDi.setIsDynamic(sysModel.isDynamic() ? 1 : 0);
                	datatubeInstanceMapper.updateByPrimaryKeySelective(updDi);
                	
                	DatatubeInstanceModelObj updDmo = new DatatubeInstanceModelObj();
                	updDmo.setIsDeleted(opBatchNo);
                	updDmo.setModifyEmpid("schedulerx");
                	updDmo.setGmtModified(now);
                	DatatubeInstanceModelObjExample dimoExample = new DatatubeInstanceModelObjExample();
                	dimoExample.createCriteria().andTenantIdEqualTo(tenantId).andDatatubeInstIdEqualTo(inst.getId()).andIsDeletedEqualTo(0L);
                	
                	//如已存在配置，获取备份，主要为了继承已经配置的tpm、sla、lookupSla等参数
                	List<DatatubeInstanceModelObj> bakConfList = datatubeInstanceModelObjMapper.selectByExample(dimoExample);
                	Map<String, DatatubeInstanceModelObj> bakConfMap = new HashMap<>();
                	if (CollectionUtils.isNotEmpty(bakConfList)) {
                		bakConfMap = bakConfList.stream().collect(Collectors.toMap(DatatubeInstanceModelObj::getModelObjectCode, Function.identity()));
                	}
                	
                	//旧配置如果存在则逻辑删除
                	datatubeInstanceModelObjMapper.updateByExampleSelective(updDmo, dimoExample);

            		DatatubeInstanceModelField updDimf = new DatatubeInstanceModelField();
            		updDimf.setIsDeleted(opBatchNo);
            		updDimf.setModifyEmpid("schedulerx");
            		updDimf.setGmtModified(now);
                	DatatubeInstanceModelFieldExample dimfExample = new DatatubeInstanceModelFieldExample();
                	dimoExample.createCriteria().andTenantIdEqualTo(tenantId).andDatatubeInstIdEqualTo(inst.getId()).andIsDeletedEqualTo(0L);
                	datatubeInstanceModelFieldMapper.updateByExampleSelective(updDimf, dimfExample);

                	Map<String, String> gids = new HashMap<>();
                	if ("v2".equalsIgnoreCase(inst.getVersion())) {
                		//TODO
                	} else {
                    	gids = viewModelHandler.getViewModelGids(inst);
                	}
                	
                	DatatubeInstanceModelObj insDmo = new DatatubeInstanceModelObj();
                	insDmo.setCreateEmpid("schedulerx");
                	insDmo.setDatatubeInstId(inst.getId());
                	if (CollectionUtils.isNotEmpty(sysModel.getObject().getFields())) {
                		insDmo.setFields(StringUtils.join(sysModel.getObject().getFields().stream().map(e -> e.getCode()).collect(Collectors.toList()), ","));
                	}
                	insDmo.setFilter(sysModel.getObject().getFilter());
                	if (StringUtils.isBlank(inst.getVersion()) || "v1".equalsIgnoreCase(inst.getVersion())) {
                		Double flowLimit = flowCtlService.getFlowLimitV1(gids.get(sysModel.getObject().getCode()));
                		insDmo.setFlowLimit(flowLimit == null ? null : new BigDecimal(flowLimit));
                	}
                	insDmo.setGmtCreate(now);
                	insDmo.setGmtModified(now);
                	insDmo.setIsDeleted(0L);
                	insDmo.setIsLookup(0);
                	insDmo.setIsMain(1);
                	insDmo.setLookupFlowLimit(null);
                	insDmo.setLookupFrom(null);
                	insDmo.setLookupSla(null);
                	insDmo.setModelObjectCode(sysModel.getObject().getCode());
                	insDmo.setModelObjectType(sysModel.getObject().getType());
                	insDmo.setModifyEmpid("schedulerx");
                	insDmo.setRefDsName(sysModel.getObject().getRef());
                	if (CollectionUtils.isNotEmpty(sysModel.getObject().getFields())) {
                		insDmo.setRefFields(StringUtils.join(sysModel.getObject().getFields().stream().map(e -> e.getRef()).collect(Collectors.toList()), ","));
                	}
                	insDmo.setRelType(null);
                	insDmo.setSla(bakConfMap.get(sysModel.getObject().getCode()) == null ? null : bakConfMap.get(sysModel.getObject().getCode()).getSla());
                	insDmo.setTenantId(tenantId);
                	insDmo.setTpm(bakConfMap.get(sysModel.getObject().getCode()) == null ? null : bakConfMap.get(sysModel.getObject().getCode()).getTpm());
                	datatubeInstanceModelObjMapper.insert(insDmo);
                	
                	if (CollectionUtils.isNotEmpty(sysModel.getObject().getFields())) {

                		for (ViewModel.Field field : sysModel.getObject().getFields()) {
                			if (field.getObject() != null) {
                				insDmo = new DatatubeInstanceModelObj();
                            	insDmo.setCreateEmpid("schedulerx");
                            	insDmo.setDatatubeInstId(inst.getId());
                        		insDmo.setFields(field.getCode());
                            	insDmo.setFilter(field.getObject().getFilter());
                            	if (StringUtils.isBlank(inst.getVersion()) || "v1".equalsIgnoreCase(inst.getVersion())) {
                            		Double flowLimit = flowCtlService.getFlowLimitV1(gids.get(field.getObject().getCode()));
                            		insDmo.setFlowLimit(flowLimit == null ? null : new BigDecimal(flowLimit));
                            	}
                            	insDmo.setGmtCreate(now);
                            	insDmo.setGmtModified(now);
                            	insDmo.setIsDeleted(0L);
                            	insDmo.setIsLookup(!"none".equalsIgnoreCase(field.getObject().getLookupFrom()) ? 1 : 0);
                            	insDmo.setIsMain(0);
                            	if (StringUtils.isBlank(inst.getVersion()) || "v1".equalsIgnoreCase(inst.getVersion())) {
                            		Double flowLimit = flowCtlService.getFlowLimitV1(gids.get(sysModel.getObject().getCode() + "__lookup"));
                            		insDmo.setLookupFlowLimit(flowLimit == null ? null : new BigDecimal(flowLimit));
                            	}
                            	insDmo.setLookupFrom(field.getObject().getLookupFrom());
                            	insDmo.setLookupSla(bakConfMap.get(field.getObject().getCode()) == null ? null : bakConfMap.get(field.getObject().getCode()).getLookupSla());
                            	insDmo.setModelObjectCode(field.getObject().getCode());
                            	insDmo.setModelObjectType(field.getObject().getType());
                            	insDmo.setModifyEmpid("schedulerx");
                            	insDmo.setRefDsName(field.getObject().getRef());
                            	insDmo.setRefFields(field.getObject().getCode());
                            	insDmo.setRelType(null);
                            	insDmo.setSla(bakConfMap.get(field.getObject().getCode()) == null ? null : bakConfMap.get(field.getObject().getCode()).getSla());
                            	insDmo.setTenantId(tenantId);
                            	insDmo.setTpm(bakConfMap.get(field.getObject().getCode()) == null ? null : bakConfMap.get(field.getObject().getCode()).getTpm());
                            	datatubeInstanceModelObjMapper.insert(insDmo);
                            	
                            	DatatubeInstanceModelField insDimf = new DatatubeInstanceModelField();
                    			insDimf.setCreateEmpid("schedulerx");
                    			insDimf.setDatatubeInstId(inst.getId());
                    			insDimf.setExtRefFields(null);
                    			insDimf.setFieldDesc(field.getName());
                    			insDimf.setFieldName(field.getCode());
                    			insDimf.setFieldType(field.getType());
                    			insDimf.setGmtCreate(now);
                    			insDimf.setGmtModified(now);
                    			insDimf.setIsDeleted(0L);
                    			insDimf.setIsFk(0);
                    			insDimf.setIsFunc(field.isFunc() ? 1 : 0);
                    			insDimf.setIsMultivalue(field.isMultivalue() ? 1 : 0);
                    			insDimf.setIsPk(field.isPk() ? 1 : 0);
                    			insDimf.setModelObjectCode(sysModel.getObject().getCode());
                    			insDimf.setModifyEmpid("schedulerx");
                    			insDimf.setMvToken(field.getMvToken());
                    			insDimf.setRefDsName(field.getObject().getRef());
                    			insDimf.setRefFieldName(field.getObject().getRef());
                    			insDimf.setTenantId(tenantId);
                    			datatubeInstanceModelFieldMapper.insert(insDimf);
                			}
                		}
                		
                		for (ViewModel.Field field : sysModel.getObject().getFields()) {
                			if (field.getObject() != null) {
                				continue;
                			}
                			DatatubeInstanceModelField insDimf = new DatatubeInstanceModelField();
                			insDimf.setCreateEmpid("schedulerx");
                			insDimf.setDatatubeInstId(inst.getId());
                			insDimf.setExtRefFields(null);
                			insDimf.setFieldDesc(field.getName());
                			insDimf.setFieldName(field.getCode());
                			insDimf.setFieldType(field.getType());
                			insDimf.setGmtCreate(now);
                			insDimf.setGmtModified(now);
                			insDimf.setIsDeleted(0L);

                			insDimf.setIsFk(0);
                			if (CollectionUtils.isNotEmpty(sysModel.getRelatedObjects())) {
                				boolean hasMachFk = false;
        	                	for (RelatedDataObject relObject : sysModel.getRelatedObjects()) {
        	                		for (ViewModel.Relation rel : relObject.getRelations()) {
        	                			if (rel.getRelatedField().split("\\.").length == 2 
        	                					&& sysModel.getObject().getCode().equalsIgnoreCase(rel.getRelatedField().split("\\.")[0])
        	                					&& field.getCode().equalsIgnoreCase(rel.getRelatedField().split("\\.")[1])) {
        	                    			insDimf.setIsFk(field.isPk() ? 0 : 1);
        	                    			hasMachFk = true;
        	                				break;
        	                			}
        	                		}
        	                		if (hasMachFk) {
        	                			break;
        	                		}
        	                	}
                			}
                			
                			insDimf.setIsFunc(field.isFunc() ? 1 : 0);
                			insDimf.setIsMultivalue(field.isMultivalue() ? 1 : 0);
                			insDimf.setIsPk(field.isPk() ? 1 : 0);
                			insDimf.setModelObjectCode(sysModel.getObject().getCode());
                			insDimf.setModifyEmpid("schedulerx");
                			insDimf.setMvToken(field.getMvToken());
                			insDimf.setRefDsName(sysModel.getObject().getRef());
                			insDimf.setRefFieldName(field.getRef());
                			insDimf.setTenantId(tenantId);
                			datatubeInstanceModelFieldMapper.insert(insDimf);
                		}
                	}
                	
                	if (CollectionUtils.isNotEmpty(sysModel.getRelatedObjects())) {
	                	for (RelatedDataObject relObject : sysModel.getRelatedObjects()) {
	                		insDmo = new DatatubeInstanceModelObj();
	                    	insDmo.setCreateEmpid("schedulerx");
	                    	insDmo.setDatatubeInstId(inst.getId());
	                    	if (CollectionUtils.isNotEmpty(relObject.getFields())) {
	                    		insDmo.setFields(StringUtils.join(relObject.getFields().stream().map(e -> e.getCode()).collect(Collectors.toList()), ","));
	                    	}
	                    	insDmo.setFilter(relObject.getFilter());
	                    	if (StringUtils.isBlank(inst.getVersion()) || "v1".equalsIgnoreCase(inst.getVersion())) {
	                    		Double flowLimit = flowCtlService.getFlowLimitV1(gids.get(relObject.getCode()));
	                    		insDmo.setFlowLimit(flowLimit == null ? null : new BigDecimal(flowLimit));
	                    	}
	                    	insDmo.setGmtCreate(now);
	                    	insDmo.setGmtModified(now);
	                    	insDmo.setIsDeleted(0L);
	                    	insDmo.setIsLookup(!"none".equalsIgnoreCase(relObject.getLookupFrom()) ? 1 : 0);
	                    	insDmo.setIsMain(0);
	                    	if (StringUtils.isBlank(inst.getVersion()) || "v1".equalsIgnoreCase(inst.getVersion())) {
	                    		Double flowLimit = flowCtlService.getFlowLimitV1(gids.get(sysModel.getObject().getCode() + "__lookup"));
	                    		insDmo.setLookupFlowLimit(flowLimit == null ? null : new BigDecimal(flowLimit));
	                    	}
	                    	insDmo.setLookupFrom(relObject.getLookupFrom());
	                    	insDmo.setLookupSla(bakConfMap.get(relObject.getCode()) == null ? null : bakConfMap.get(relObject.getCode()).getLookupSla());
	                    	insDmo.setModelObjectCode(relObject.getCode());
	                    	insDmo.setModelObjectType(relObject.getType());
	                    	insDmo.setModifyEmpid("schedulerx");
	                    	insDmo.setRefDsName(relObject.getRef());
	                    	if (CollectionUtils.isNotEmpty(relObject.getFields())) {
	                    		insDmo.setRefFields(StringUtils.join(relObject.getFields().stream().map(e -> e.getRef()).collect(Collectors.toList()), ","));
	                    	}
	                    	insDmo.setRelType(relObject.getRelationType());
	                    	insDmo.setSla(bakConfMap.get(relObject.getCode()) == null ? null : bakConfMap.get(relObject.getCode()).getSla());
	                    	insDmo.setTenantId(tenantId);
	                    	insDmo.setTpm(bakConfMap.get(relObject.getCode()) == null ? null : bakConfMap.get(relObject.getCode()).getTpm());
	                    	datatubeInstanceModelObjMapper.insert(insDmo);
	                    	
	                    	for (ViewModel.Field field : relObject.getFields()) {
	                			DatatubeInstanceModelField insDimf = new DatatubeInstanceModelField();
	                			insDimf.setCreateEmpid("schedulerx");
	                			insDimf.setDatatubeInstId(inst.getId());
	                			insDimf.setExtRefFields(null);
	                			insDimf.setFieldDesc(field.getName());
	                			insDimf.setFieldName(field.getCode());
	                			insDimf.setFieldType(field.getType());
	                			insDimf.setGmtCreate(now);
	                			insDimf.setGmtModified(now);
	                			insDimf.setIsDeleted(0L);

	                			insDimf.setIsFk(0);
	                			if (CollectionUtils.isNotEmpty(sysModel.getRelatedObjects())) {
	                				boolean hasMachFk = false;
	        	                	for (RelatedDataObject otherRelObject : sysModel.getRelatedObjects()) {
	        	                		if (relObject.getCode().equalsIgnoreCase(otherRelObject.getCode())) {
	        	                			continue;
	        	                		}
	        	                		for (ViewModel.Relation rel : otherRelObject.getRelations()) {
	        	                			if (rel.getRelatedField().split("\\.").length == 2 
	        	                					&& relObject.getCode().equalsIgnoreCase(rel.getRelatedField().split("\\.")[0])
	        	                					&& field.getCode().equalsIgnoreCase(rel.getRelatedField().split("\\.")[1])) {
	        	                    			insDimf.setIsFk(1);
	        	                    			hasMachFk = true;
	        	                				break;
	        	                			}
	        	                		}
	        	                		if (hasMachFk) {
	        	                			break;
	        	                		}
	        	                	}
	                			}
	                			
	                			insDimf.setIsFunc(field.isFunc() ? 1 : 0);
	                			insDimf.setIsMultivalue(field.isMultivalue() ? 1 : 0);
	                			insDimf.setIsPk(0);
	                			insDimf.setModelObjectCode(relObject.getCode());
	                			insDimf.setModifyEmpid("schedulerx");
	                			insDimf.setMvToken(field.getMvToken());
	                			insDimf.setRefDsName(relObject.getRef());
	                			insDimf.setRefFieldName(field.getRef());
	                			insDimf.setTenantId(tenantId);
	                			datatubeInstanceModelFieldMapper.insert(insDimf);
	                		}
	                	}
                	}
            		
                	log.info("datatube[{}-{}-{}] update DAG finished", inst.getId(), inst.getName(), inst.getProviderId());
            	} catch(Exception e) {
            		log.error("datatube[{}-{}-{}] update DAG failed, error={}", inst.getId(), inst.getName(), inst.getProviderId(), e.getMessage(), e);
            	}
        	}
        } catch (QanatBizException e) {
            log.error("DatatubeInstanceModelMetaRebuildProcessor任务调度异常:{}", e.getMessage());
            return new ProcessResult(false, e.getMessage());
        } catch (Exception e) {
            log.error("DatatubeInstanceModelMetaRebuildProcessor任务调度异常", e);
            return new ProcessResult(false, e.getMessage());
        }
        return new ProcessResult(true);
    }
    
    @Override
    public void kill(JobContext context) {
        
    }
}