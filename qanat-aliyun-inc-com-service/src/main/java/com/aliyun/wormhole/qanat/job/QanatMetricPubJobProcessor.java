package com.aliyun.wormhole.qanat.job;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.devata.customer.pool.hsf.api.RPCResult;
import com.aliyun.devata.customer.pool.hsf.api.TableDataUpdateTimeService;
import com.aliyun.devata.customer.pool.hsf.api.dto.TableDataUpdateTimeDTO;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.DagInstStatus;
import com.aliyun.wormhole.qanat.api.dag.MetricPubNode;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstanceExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfo;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelVersionWithBLOBs;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelVersionMapper;
import com.aliyun.wormhole.qanat.service.odps.OdpsClient;
import com.aliyun.wormhole.qanat.service.util.YamlUtil;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelOptimizer;
import com.taobao.ateye.util.reflect.StringUtils;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 批任务数据更新指标下发任务入口
 * <AUTHOR>
 * 2022年11月1日
 */
@Slf4j
@Component
public class QanatMetricPubJobProcessor extends AbstractQanatNodeJobProcessor<MetricPubNode> {
	
    @HSFConsumer(clientTimeout=30000)
    private TableDataUpdateTimeService tableDataUpdateTimeService;
    
    @Resource
    private ViewModelInfoMapper viewModelInfoMapper;
    
    @Resource
    private ViewModelVersionMapper viewModelVersionMapper;
	
	@Resource
	private DatatubeInstanceMapper datatubeInstanceMapper;
	
	@Resource
	private ViewModelOptimizer viewModelOptimizer;
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private DatasourceMapper dsInfoMapper;
    
    @Value("${aone.id}")
    private String olapAppId;
    
    @Value("${olap.api.ak}")
    private String olapAk;

	@Override
	void doProcess(Map<String, Object> instParamsMap, MetricPubNode node) {
	    log.info("[{}]instParamsMap={}", node.getId(), JSON.toJSONString(instParamsMap));
	    try {
		    log.info("table:{} updateFieldUpdateTime start", node.getDstTableName());
		    
		    Date updateTime = getUpdateTime(instParamsMap, node);
		    
		    TableDataUpdateTimeDTO updateTimeDTO = new TableDataUpdateTimeDTO();
		    updateTimeDTO.setTableName(node.getDstTableName());
		    updateTimeDTO.setEmpId("datatube");
		    updateTimeDTO.setLastUpdateTime(updateTime);
		    updateTimeDTO.setAppId(olapAppId);
		    updateTimeDTO.setAk(olapAk);
		    
	    	if (node.getDatatubeInstId() != null && StringUtils.isNotBlank(node.getSubObj())) {
	    	    
	            DatatubeInstance datatubeInst = datatubeInstanceMapper.selectByPrimaryKey(node.getDatatubeInstId());
	        	
	        	ViewModelInfoExample example = new ViewModelInfoExample();
	    		example.createCriteria().andTenantIdEqualTo(datatubeInst.getTenantId()).andIdEqualTo(datatubeInst.getProviderId()).andIsDeletedEqualTo(0L);
	    		List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(example);
	    		if (CollectionUtils.isEmpty(viewModelInfos)) {
	    			throw new QanatBizException("viewmodel not found");
	    		}
	    		ViewModelInfo viewModelInfo = viewModelInfos.get(0);
	    		
	            ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());
	
	            ViewModel originModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
	        	ViewModel sysModel = null;
	        	if (originModel.isDynamic()) {
	        		sysModel = viewModelOptimizer.getOptimizedViewModel(datatubeInst.getTenantId(), modelVersion.getUserYaml());
	        	} else {
	        		sysModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
	        	}
	    		
	        	ViewModel.RelatedDataObject relObj = sysModel.getRelatedObjects().stream().filter(e -> node.getSubObj().equalsIgnoreCase(e.getCode())).collect(Collectors.toList()).get(0);
	        	List<String> joinOnFields = relObj.getRelations().stream().map(e -> e.getField()).collect(Collectors.toList());
	        	Map<String, Date> fieldChangeTimeMap = relObj.getFields().stream().filter(e -> !joinOnFields.contains(e.getCode())).collect(Collectors.toMap(ViewModel.Field::getCode, e -> updateTime));
	        	updateTimeDTO.setFieldChangeTimeMap(fieldChangeTimeMap);
	    	}
		    log.info("table:{} updateFieldUpdateTime req:{} cost:{}", node.getDstTableName(), JSON.toJSONString(updateTimeDTO));
		    long startTs = System.currentTimeMillis();
		    RPCResult<Map<Long, List<Long>>> result = tableDataUpdateTimeService.updateFieldUpdateTime(updateTimeDTO);
		    log.info("table:{} updateFieldUpdateTime result:{} cost:{}", node.getDstTableName(), JSON.toJSONString(result), System.currentTimeMillis() - startTs);
	    } catch (Exception e) {
	    	log.error("QanatMetricPubJobProcessor failed due to {}", e.getMessage(), e);
	    	throw e;
	    }
	}

	private Date getUpdateTime(Map<String, Object> instParamsMap, MetricPubNode node) {
		String tenantId = String.valueOf(instParamsMap.get("tenantId"));
		Date updateTime = new Date();
		if (StringUtils.isNotBlank(node.getSrcDsName())) {
			DatasourceExample dsExample = new DatasourceExample();
        	dsExample.createCriteria().andIsDeletedEqualTo(0L).andDsNameEqualTo(node.getSrcDsName()).andTenantIdEqualTo(tenantId);
            List<Datasource> dsInfos = dsInfoMapper.selectByExampleWithBLOBs(dsExample);
            if (CollectionUtils.isEmpty(dsInfos)) {
                throw new QanatBizException("dsInfo:" + node.getSrcDsName() + " is not found");
            }
        	
        	DbInfoExample dbExample = new DbInfoExample();
            dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dsInfos.get(0).getDbName()).andTenantIdEqualTo(tenantId);
            List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
            if (CollectionUtils.isEmpty(dbs)) {
                throw new QanatBizException("db:" + dsInfos.get(0).getDbName() + " is not found");
            }
            DbInfo dbInfo = dbs.get(0);
            JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
            
            OdpsClient client = new OdpsClient(dbMetaJson.getString("odpsServer"), dbMetaJson.getString("accessId"), dbMetaJson.getString("accessKey"),
            		dbMetaJson.getString("project"), null, null);
    	    Map<String, String> maxPtInfo = client.getMaxPtInfo(dsInfos.get(0).getTableName());
    	    log.info("db:{},table:{},maxPtInfo:{}", dsInfos.get(0).getDbName(), dsInfos.get(0).getTableName(), JSON.toJSONString(maxPtInfo));
    	    if (maxPtInfo != null && CollectionUtils.isNotEmpty(maxPtInfo.keySet())) {
    		    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:dd");
    		    for (String ds : maxPtInfo.keySet()) {
    		    	try {
						updateTime = sdf.parse(maxPtInfo.get(ds));
					} catch (ParseException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
    		    }
    	    }
		} else if (CollectionUtils.isNotEmpty(node.getPrevNodeList())) {
			TaskInstanceExample example = new TaskInstanceExample();
		    example.createCriteria().andTaskIdEqualTo(Long.valueOf(instParamsMap.get("taskId")+""))
		    .andTaskNameEqualTo(node.getPrevNodeList().get(0))
		    .andStatusEqualTo(DagInstStatus.SUCCESS.getCode().byteValue());
		    example.setOrderByClause("gmt_modified desc");
		    List<TaskInstance> subTaskInstList = taskInstanceMapper.selectByExample(example);
		    if (CollectionUtils.isNotEmpty(subTaskInstList)) {
		    	updateTime = subTaskInstList.get(0).getEndTime();
		    }
		} else if (node.getTargetTaskId() != null && StringUtils.isNotBlank(node.getTargetSubTaskName())) {
			TaskInstanceExample example = new TaskInstanceExample();
		    example.createCriteria().andTaskIdEqualTo(node.getTargetTaskId())
		    .andTaskNameEqualTo(node.getTargetSubTaskName())
		    .andStatusEqualTo(DagInstStatus.SUCCESS.getCode().byteValue());
		    example.setOrderByClause("gmt_modified desc");
		    List<TaskInstance> subTaskInstList = taskInstanceMapper.selectByExample(example);
		    if (CollectionUtils.isNotEmpty(subTaskInstList)) {
		    	updateTime = subTaskInstList.get(0).getEndTime();
		    }
		}
		return updateTime;
	}
	
//	public static void main(String[] args) {
//		ViewModel.RelatedDataObject relObj = new ViewModel.RelatedDataObject ();
//		ViewModel.Field f1 = new ViewModel.Field();
//		f1.setCode("f1");
//		ViewModel.Field f2 = new ViewModel.Field();
//		f2.setCode("f2");
//		ViewModel.Field f3 = new ViewModel.Field();
//		f3.setCode("f3");
//		List<ViewModel.Field> flist = new ArrayList<>();
//		relObj.setFields(flist);
//		flist.add(f1);
//		flist.add(f2);
//		flist.add(f3);
//	    Date now = new Date();
//		System.out.println(JSON.toJSONString(relObj.getFields().stream().collect(Collectors.toMap(ViewModel.Field::getCode, e -> now))));
//	}
}