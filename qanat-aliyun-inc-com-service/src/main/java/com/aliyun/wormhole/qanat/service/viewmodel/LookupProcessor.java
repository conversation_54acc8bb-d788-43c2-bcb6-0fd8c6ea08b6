package com.aliyun.wormhole.qanat.service.viewmodel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.ResourcePackage;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.FlowCtlService;
import com.aliyun.wormhole.qanat.api.service.KafkaManagementService;
import com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelation;
import com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsRelation;
import com.aliyun.wormhole.qanat.dal.domain.DsRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.Extension;
import com.aliyun.wormhole.qanat.dal.domain.ExtensionExample;
import com.aliyun.wormhole.qanat.dal.mapper.ComponentDsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsFieldInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ExtensionMapper;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.RelatedDataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Relation;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class LookupProcessor {
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private KafkaManagementService kafkaManagementService;
    
    @Resource
    private ExtensionMapper extensionMapper;
    
    @Resource
    private DatasourceMapper dsInfoMapper;

    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private DsFieldInfoMapper dsFieldInfoMapper;
    
    @Resource
    private ComponentDsRelationMapper componentDsRelationMapper;
	
	@Resource
	private DsRelationMapper dsRelationMapper;
    
    @Resource
    private DatasourceService dsInfoService;
    
    @Resource
    private ViewModelSqlBuilder viewModelSqlBuilder;
	
	@Resource
	private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;
	
	@Resource
	private FlowCtlService limiterService;
	
	@Resource
	private ComponentObjectProcessor componentObjectProcessor;
    
    @Value("${datatube.codegen.version}")
    private String codegenVersion;
	
	private Datasource getDsInfoByObjectCode(String tenantId, String objectUniqueCode) {
		DatasourceExample example = new DatasourceExample();
		example.createCriteria().andTableNameEqualTo(objectUniqueCode).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsTypeEqualTo("obj");
		List<Datasource> dsList = dsInfoMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isEmpty(dsList)) {
			throw new QanatBizException(objectUniqueCode + " is not found");
		}
		return dsList.get(0);
	}
    
    public boolean processIncrSyncJob(String tenantId, Long appId, String appName, String jobName,
			ViewModel dataModel, String dstDbName, String tableName, Long dstDsId, String operateEmpid, Long versionId,
			JSONObject kafkaJson, String pkField, Long datatubeInstId) {
		String pkFieldType = dataModel.getObject().getFields().stream().filter(e->e.isPk()).map(e->e.getType()).collect(Collectors.toList()).get(0);
    	List<String> relCols = new ArrayList<>();
    	List<String> relColDefs = new ArrayList<>();
    	Map<String, String> colMap = new HashMap<>();
    	List<ViewModel.Field> funcFields = new ArrayList<>();
    	for (ViewModel.Field field : dataModel.getObject().getFields()) {
    		if (field.getObject() != null) {
				if ("varchar".equalsIgnoreCase(field.getType())) {
					relCols.add("JSON_VALUE(b.x, '$." + field.getCode() + "')");
				} else {
					relCols.add("CAST(JSON_VALUE(b.x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
				}
    			relColDefs.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
				colMap.put(field.getCode(), "CAST(JSON_VALUE(b.x, '$." + field.getCode() + "') AS " + field.getType() + ")");
    		} else if (field.isFunc()) {
    			funcFields.add(field);
    			continue;
    		} else if (field.isPk()) {
    			continue;
    		} else {
				if ("varchar".equalsIgnoreCase(field.getType())) {
					colMap.put(field.getCode(), "JSON_VALUE(b.x, '$." + field.getCode() + "')");
				} else {
					colMap.put(field.getCode(), "CAST(JSON_VALUE(b.x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
				}
			}
    	}
    	if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
    		for (RelatedDataObject relObj : dataModel.getRelatedObjects()) {
    			Map<String, Relation> rels = relObj.getRelations().stream().filter(e->!e.getRelatedField().startsWith("exp#")).collect(Collectors.toMap(Relation::getField, Function.identity()));
    			List<ViewModel.Field> fields = relObj.getFields().stream().filter(e->!rels.containsKey(e.getCode())).collect(Collectors.toList());
    			for (ViewModel.Field field : fields) {
    				if ("varchar".equalsIgnoreCase(field.getType())) {
        				relCols.add("JSON_VALUE(b.x, '$." + field.getCode() + "')");
    					colMap.put(relObj.getCode() + "." + field.getCode(), "JSON_VALUE(b.x, '$." + field.getCode() + "')");
    				} else {
        				relCols.add("CAST(JSON_VALUE(b.x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
    					colMap.put(relObj.getCode() + "." + field.getCode(), "CAST(JSON_VALUE(b.x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
    				}
        			relColDefs.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
    			}
    		}
    	}
    	for (ViewModel.Field field : funcFields) {
    		String funcExpress = field.getRef();
			String funcCode = parseFuncExpress(funcExpress).get(0);
			String[] cols = parseFuncExpress(funcExpress).get(1).split(",");
			List<String> exps = new ArrayList<>();
			for (String col : cols) {
				if (col.startsWith("'") && col.endsWith("'")) {
					exps.add(col);
				} else {
					exps.add(colMap.get(col));
				}
			}
			ExtensionExample example = new ExtensionExample();
			example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andCodeEqualTo(funcCode);
			List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
			if (CollectionUtils.isEmpty(exts)) {
				log.error("funcCode:{} not found", funcCode);
				return false;
			}
			String func = exts.get(0).getPlugin().equalsIgnoreCase("groovy")?"groovyFunc":"dfaasFunc";
			String funcExp = null;
			if (field.getType().equalsIgnoreCase("varchar")) {
				funcExp = func + "('" + tenantId + "', '" + funcCode + "'," + StringUtils.join(exps, ",") + ") AS " + field.getCode();
			} else {
				funcExp = "CAST(" + func + "('" + tenantId + "', '" + funcCode + "'," + StringUtils.join(exps, ",") + ") AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + ") AS " + field.getCode();
			}
			relColDefs.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
			relCols.add(funcExp);
    	}
    	String logTopicName = null;
    	if ("metadata".equalsIgnoreCase(dataModel.getObject().getType())) {
	    	logTopicName = "stream-" + appId + "-" + dstDsId + "-" + dataModel.getObject().getCode() + "_lookup";
    	} else if ("table".equalsIgnoreCase(dataModel.getObject().getType())) {
    		logTopicName = "stream-" + appId + "-" + dstDsId + "-" + dataModel.getObject().getCode() + "_lookup";
    	}
    	String consumerId = "GID-" + appId + "-" + dstDsId + "-incr_sync-" + dataModel.getObject().getCode() + "_lookup-" + versionId;
    	boolean res = kafkaManagementService.createConsumerGroup(tenantId, appName, consumerId);
		if (!res) {
			log.error("consumer:{} create is failed", consumerId);
		}
		
		String dsSink = "CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\r\n" + 
				"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\r\n" + 
				"CREATE FUNCTION groovyFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatFunctionUdf';\r\n" +
				"CREATE FUNCTION dfaasFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDfaasFunctionUdf';\r\n" +
				"create table adb_sink (\r\n" + 
				"    " + pkField + " " + pkFieldType + ",\r\n" + 
				"    " + StringUtils.join(relColDefs, ",") + ",\r\n" + 
				"    __trace_id__ varchar,\r\n" + 
				"    primary key(" + pkField + ")\r\n" + 
				") with (\r\n" + 
				"    type = 'QANAT_ADB30',\r\n" + 
				"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\r\n" + 
				"    dbName='" + dstDbName + "',\r\n" + 
				"    tableName='" + tableName + "',\r\n" + 
				"    replaceMode = 'update',\r\n" + 
				"    writeMode = 'single',\r\n" + 
				"    streamEvent = 'disable'\r\n" +
				");";
		String odsSource = null;
		if ("metadata".equalsIgnoreCase(dataModel.getObject().getType())) {
    		Datasource objDsInfo = getDsInfoByObjectCode(tenantId, dataModel.getObject().getRef());
			JSONObject odsDsMeta = dsInfoService.getOdsTableMetaByDsName(tenantId, objDsInfo.getDsName());
	    	String odsConsumerId = "GID-" + appId + "-" + dstDsId + "-ods_incr_sync-" + dataModel.getObject().getCode() + "_lookup-" + versionId;
	    	res = kafkaManagementService.createConsumerGroup(tenantId, appName, odsConsumerId);
			if (!res) {
				log.error("consumer:{} create is failed", odsConsumerId);
			}
			odsSource = "create table ods_mq_source (\n" + 
					"    msg varchar,\n" + 
					"    __traceId__ varchar header\n" + 
					") with (\n" + 
					"  type = 'custom',\n" + 
					"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
					"  topic = '" + odsDsMeta.getJSONObject("incrConf").getString("topicName") + "',\n" + 
					"  `group.id` = '" + odsConsumerId + "',\n" + 
					"  `dbName` = '" + kafkaJson.getString("dbName") + "',\n" +
					"  startupMode = 'TIMESTAMP',\n" +
					"  fieldDelimiter = '`'\n" +
					");\n"
					+ "\n"
					+ "create view v_ods_id as\r\n"
					+ "select CAST(JSON_VALUE(b.x, '$.object_biz_id') as " + pkFieldType + ") as id,a.__traceId__ from ods_mq_source as a , LATERAL TABLE(parseDrcFields(a.msg, 'object_unique_code', 'object_biz_id')) as b(x)\r\n"
					+ "where JSON_VALUE(b.x, '$.object_unique_code')='" + dataModel.getObject().getRef() + "' and JSON_VALUE(b.x, '$.eventType')='1';"
					+ "\n"
					+ "create view v_join_id as\r\n"
					+ "select a.id, a.__traceId__ from v_id as a join v_ods_id as b on a.id=b.id;\n";
		} else {
			JSONObject odsDsMeta = dsInfoService.getOdsTableMetaByDsName(tenantId, dataModel.getObject().getRef());
	    	String odsConsumerId = "GID-" + appId + "-" + dstDsId + "-ods_incr_sync-" + dataModel.getObject().getCode() + "_lookup-" + versionId;
	    	res = kafkaManagementService.createConsumerGroup(tenantId, appName, odsConsumerId);
			if (!res) {
				log.error("consumer:{} create is failed", odsConsumerId);
			}
			String odsPkField = dataModel.getObject().getFields().stream().filter(e->e.getCode().equalsIgnoreCase(pkField)).collect(Collectors.toList()).get(0).getRef();
			odsSource = "create table ods_mq_source (\n" + 
					"    msg varchar,\n" + 
					"    __traceId__ varchar header\n" + 
					") with (\n" + 
					"  type = 'custom',\n" + 
					"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
					"  topic = '" + odsDsMeta.getJSONObject("incrConf").getString("topicName") + "',\n" + 
					"  `group.id` = '" + odsConsumerId + "',\n" + 
					"  `dbName` = '" + kafkaJson.getString("dbName") + "',\n" +
					"  startupMode = 'TIMESTAMP',\n" +
					"  fieldDelimiter = '`'\n" +
					");\n"
					+ "create view v_ods_id as\r\n"
					+ "select CAST(JSON_VALUE(b.x, '$." + odsPkField + "') as " + pkFieldType + ") as id,a.__traceId__ from ods_mq_source as a , LATERAL TABLE(parseDrcFields(a.msg, '" + odsPkField + "')) as b(x) where JSON_VALUE(b.x, '$.eventType')='1';\r\n"
					+ "\n"
					+ "create view v_join_id as\r\n"
					+ "select a.id, a.__traceId__ from v_id as a join v_ods_id as b on a.id=b.id and a.__traceId__=b.__traceId__;\n";
		}
		
    	String dsSource = "create table mq_source (\n" + 
				"    msg varchar,\n" + 
				"    __traceId__ varchar header\n" + 
				") with (\n" + 
				"  type = 'custom',\n" + 
				"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
				"  topic = '" + logTopicName + "',\n" + 
				"  `group.id` = '" + consumerId + "',\n" + 
				"  `dbName` = '" + kafkaJson.getString("dbName") + "',\n" +
				"  startupMode = 'TIMESTAMP',\n" +
				"  fieldDelimiter = '`'\n" +
				");\n"
				+ "create view v_id as\r\n"
				+ "select CAST(JSON_VALUE(b.x, '$." + pkField + "') as " + pkFieldType + ") as id,a.__traceId__ from mq_source as a , LATERAL TABLE(parseDrcFields(a.msg, '" + pkField + "')) as b(x) where JSON_VALUE(b.x, '$.eventType')='1';\r\n";

		String fetchSql = "select * from (" + viewModelSqlBuilder.getSelectSql(tenantId, dataModel, null).replace("'", "''") + ") as t where " + pkField + "=?";
    	String lookupSql = "insert into adb_sink\r\n"
    			+ "select a.id, " + StringUtils.join(relCols, ",") + ", a.__traceId__ from v_join_id as a left join LATERAL TABLE (queryDim('" + dstDbName + "', '" + fetchSql + "', a.id)) as b(x) on true;\r\n";
    	
    	
	    String sql = dsSink + "\r\n" + dsSource + "\r\n" + odsSource + "\r\n" + lookupSql;
	    blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
	    		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDF, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_KAFKA010, ResourcePackage.BLINK_HSF_UDF), 
	    		false);
	    
	    DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        return true;
	}
    
    public boolean processIncrSyncJobOptimize(String tenantId, Long appId, String appName, String jobName,
			ViewModel dataModel, List<String> dbNames, String etlDbName, String tableName, Long dstDsId, String operateEmpid, Long versionId,
			JSONObject kafkaJson, String pkField, List<String> objCodeList, Long datatubeInstId) {
		String pkFieldType = dataModel.getObject().getFields().stream().filter(e->e.isPk()).map(e->e.getType()).collect(Collectors.toList()).get(0);
    	List<String> relCols = new ArrayList<>();
    	List<String> mainObjColsExPkDrcParse = new ArrayList<>();
    	List<String> mainObjColsExPkDrc = new ArrayList<>();
    	List<String> mainObjColsExPkDrcForObj = new ArrayList<>();
    	List<String> relColDefs = new ArrayList<>();
    	
    	//模型设置分布键的情况，目前只支持主对象上的字段设置分布键，关联对象上的字段暂不支持
    	if (StringUtils.isNotBlank(dataModel.getSettings().getDistributeKey())) {
    		relCols.add(dataModel.getObject().getCode() + "." + dataModel.getSettings().getDistributeKey());
    		relColDefs.add("`" + dataModel.getSettings().getDistributeKey().trim() + "` " + dataModel.getObject().getFields().stream().filter(e->e.getCode().equalsIgnoreCase(dataModel.getSettings().getDistributeKey().trim())).map(e->e.getType()).collect(Collectors.toList()).get(0));
    	}
    	
    	Map<String, String> colMap = new HashMap<>();
    	List<String> compJoinList = new ArrayList<>();
    	List<String> tableJoinList = new ArrayList<>();
    	List<ViewModel.Field> funcFields = new ArrayList<>();
    	JSONObject dbMetaJson = dsInfoService.getDbMetaByName(etlDbName);
    	String pkFieldRef = null;
    	for (ViewModel.Field field : dataModel.getObject().getFields()) {
    		if (field.getObject() != null && (objCodeList == null || objCodeList.contains(field.getObject().getCode()))) {
    			if ("none".equalsIgnoreCase(field.getObject().getLookupFrom())) {
    				continue;
    			}
				if ("varchar".equalsIgnoreCase(field.getType())) {
					relCols.add("JSON_VALUE(" + field.getObject().getCode() + ".x, '$." + field.getCode() + "')");
				} else {
					relCols.add("CAST(JSON_VALUE(" + field.getObject().getCode() + ".x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
				}
    			relColDefs.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
				colMap.put(field.getCode(), "CAST(JSON_VALUE(" + field.getObject().getCode() + ".x, '$." + field.getCode() + "') AS " + field.getType() + ")");
				
				List<String> joinOnCondsLeft = new ArrayList<>();
				List<String> joinOnCondsRight = new ArrayList<>();
				for (ViewModel.Relation relation : field.getObject().getRelations()) {
					 String refObj = relation.getRelatedField().split("\\.")[0];
					 String refField = relation.getRelatedField().split("\\.")[1];
					 String refValue = null;
					 if (refObj.equalsIgnoreCase(dataModel.getObject().getCode())) {
						 refValue = relation.getRelatedField();
					 } else {
						 refValue = "JSON_VALUE(" + refObj + ".x, '$." + refField + "')";
					 }
					joinOnCondsLeft.add(field.getObject().getCode() + "." + relation.getField() + (StringUtils.isBlank(relation.getOp()) ? "=" : relation.getOp()) + "?");
					joinOnCondsRight.add(refValue);
				}
				String selectSql = "select * from " + viewModelSqlBuilder.getSubQueryFromFieldWithRefObj(tenantId, field.getCode(), field.getObject(), dbMetaJson.getString("dbType")) + " where " + StringUtils.join(joinOnCondsLeft, " and ");
				String blinkSql = null;
				ExtensionExample example = new ExtensionExample();
				example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + getDbType(tenantId, etlDbName)).andPluginEqualTo(field.getObject().getRef());
				List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
				String dbName = "origion".equalsIgnoreCase(field.getObject().getLookupFrom()) ? exts.get(0).getDbName() : etlDbName;
				if ("inner join".equalsIgnoreCase( field.getObject().getRelationType())) {
					blinkSql = " , lateral table(queryDim(concat_ws('|',__traceId__,'" + dbName + "'),'" + selectSql.replace("'", "''") + "'," + StringUtils.join(joinOnCondsRight, ",") + ")) as " + field.getObject().getCode() + "(x)\n";
				} else {
					blinkSql = " left join lateral table(queryDim(concat_ws('|',__traceId__,'" + dbName + "'),'" + selectSql.replace("'", "''") + "'," + StringUtils.join(joinOnCondsRight, ",") + ")) as " + field.getObject().getCode() + "(x) on true\n";
				}
				compJoinList.add(blinkSql);
    		} else if (field.isFunc()) {
    			funcFields.add(field);
    			continue;
    		} else if (field.isPk()) {
    			pkFieldRef = field.getRef();
    			continue;
    		} else if ("'null'".equalsIgnoreCase(field.getRef())) {
    			continue;
    		} else {
				if ("varchar".equalsIgnoreCase(field.getType())) {
					colMap.put(dataModel.getObject().getCode() + "." + field.getCode(), dataModel.getObject().getCode() + "." + field.getCode() );
		    		mainObjColsExPkDrcParse.add("JSON_VALUE(b.x, '$." + ("metadata".equalsIgnoreCase(dataModel.getObject().getType())?field.getCode():field.getRef()) + "') AS " + field.getCode());
				} else {
					colMap.put(dataModel.getObject().getCode() + "." + field.getCode(), "CAST(" + dataModel.getObject().getCode() + "." + field.getCode() + " AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
		    		mainObjColsExPkDrcParse.add("CAST(JSON_VALUE(b.x, '$." + ("metadata".equalsIgnoreCase(dataModel.getObject().getType())?field.getCode():field.getRef()) + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " ) AS " + field.getCode());
				}
				mainObjColsExPkDrc.add("'" + field.getRef() + "'");
				mainObjColsExPkDrcForObj.add("'" + field.getCode() + "'");
			}
    	}
    	if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
    		for (RelatedDataObject relObj : dataModel.getRelatedObjects()) {
    			if ("none".equalsIgnoreCase(relObj.getLookupFrom())) {
    				continue;
    			}
    			Map<String, Relation> rels = relObj.getRelations().stream().filter(e->!e.getRelatedField().startsWith("exp#")).collect(Collectors.toMap(Relation::getField, Function.identity()));
    			List<ViewModel.Field> fields = relObj.getFields().stream().filter(e->!rels.containsKey(e.getCode())).collect(Collectors.toList());
				if (objCodeList == null || objCodeList.contains(relObj.getCode())) {
	    			for (ViewModel.Field field : fields) {
	    				if ("varchar".equalsIgnoreCase(field.getType())) {
	        				relCols.add("JSON_VALUE(" + relObj.getCode() + ".x, '$." + field.getCode() + "')");
	    					colMap.put(relObj.getCode() + "." + field.getCode(), "JSON_VALUE(" + relObj.getCode() + ".x, '$." + field.getCode() + "')");
	    				} else {
	        				relCols.add("CAST(JSON_VALUE(" + relObj.getCode() + ".x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
	    					colMap.put(relObj.getCode() + "." + field.getCode(), "CAST(JSON_VALUE(" + relObj.getCode() + ".x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
	    				}
	        			relColDefs.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
	    			}
				}
    			
    			List<String> joinOnCondsLeft = new ArrayList<>();
				List<String> joinOnCondsRight = new ArrayList<>();
    			List<String> lookupOrigionConds = new ArrayList<>();
				for (ViewModel.Relation relation : relObj.getRelations()) {
					 String refObj = relation.getRelatedField().split("\\.")[0];
					 String refField = relation.getRelatedField().split("\\.")[1];
					 String refValue = null;
					 String refValueType = null;
					 String refValueOrigionType = null;
					 for (ViewModel.Field field : relObj.getFields()) {
						 if (field.getCode().equalsIgnoreCase(relation.getField())) {
							 String origionFieldName = field.getRef();
							 refValueType = field.getType();
							 if ("component".equalsIgnoreCase(relObj.getType())) {
								ExtensionExample example = new ExtensionExample();
								example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + getDbType(tenantId, etlDbName)).andPluginEqualTo(relObj.getRef());
								List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
								if (CollectionUtils.isNotEmpty(exts)) {
									ComponentDsRelationExample comDsRelExample = new ComponentDsRelationExample();
									comDsRelExample.createCriteria().andTenantIdEqualTo(tenantId).andComponentNameEqualTo(exts.get(0).getCode()).andRelationTypeEqualTo("drc_in");
									List<ComponentDsRelation> comRsRels = componentDsRelationMapper.selectByExample(comDsRelExample);
									if (CollectionUtils.isNotEmpty(comRsRels)) {
										String drcDsName = comRsRels.get(0).getDsName();
										
										DsRelationExample dsRelExample = new DsRelationExample();
										dsRelExample.createCriteria().andTenantIdEqualTo(tenantId).andSrcDsNameEqualTo(drcDsName).andRelationTypeEqualTo("incr");
										List<DsRelation> dsRels = dsRelationMapper.selectByExample(dsRelExample);
										if (CollectionUtils.isNotEmpty(dsRels)) {
											DsFieldInfoExample dfiExample = new DsFieldInfoExample();
											dfiExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andFieldNameEqualTo(origionFieldName).andDsNameEqualTo(dsRels.get(0).getDstDsName());
											List<DsFieldInfo> dsFieldList = dsFieldInfoMapper.selectByExample(dfiExample);
											if (CollectionUtils.isNotEmpty(dsFieldList)) {
												refValueOrigionType = dsFieldList.get(0).getFieldType();
											}
										}
									}
								}
							 } else {
								DsFieldInfoExample example = new DsFieldInfoExample();
								example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andFieldNameEqualTo(origionFieldName).andDsNameEqualTo(relObj.getRef());
								List<DsFieldInfo> dsFieldList = dsFieldInfoMapper.selectByExample(example);
								if (CollectionUtils.isNotEmpty(dsFieldList)) {
									refValueOrigionType = dsFieldList.get(0).getFieldType();
								}
							 }
							 break;
						 }
					 }
					 if (refObj.equalsIgnoreCase(dataModel.getObject().getCode())) {
						 refValue = relation.getRelatedField();
						 if ("origion".equalsIgnoreCase(relObj.getLookupFrom()) 
								 && StringUtils.isNotBlank(refValueOrigionType) 
								 && !refValueType.equalsIgnoreCase(refValueOrigionType)) {
							 refValue = "CAST(" + refValue + " AS " + refValueOrigionType + ")";
						 }
					 } else {
						 refValue = "JSON_VALUE(" + refObj + ".x, '$." + refField + "')";
						 if ("origion".equalsIgnoreCase(relObj.getLookupFrom()) 
								 && StringUtils.isNotBlank(refValueOrigionType) 
								 && !refValueType.equalsIgnoreCase(refValueOrigionType) 
								 && !"varchar".equalsIgnoreCase(refValueOrigionType)) {
							 refValue = "CAST(" + refValue + " AS " + refValueOrigionType + ")";
						 }
					 }
					joinOnCondsLeft.add(relObj.getCode() + "." + relation.getField() + (StringUtils.isBlank(relation.getOp()) ? "=" : relation.getOp()) + "?");
					lookupOrigionConds.add(relObj.getFields().stream().filter(item -> item.getCode().equalsIgnoreCase(relation.getField())).collect(Collectors.toList()).get(0).getRef() + (StringUtils.isBlank(relation.getOp()) ? "=" : relation.getOp()) + "?");
					joinOnCondsRight.add(refValue);
				}
				String selectSql = "select * from (" + viewModelSqlBuilder.getSubQueryFromObjectForLookup(tenantId,relObj, dbMetaJson.getString("dbType")) + ") as " + relObj.getCode() + " where " + StringUtils.join(joinOnCondsLeft, " and ");
				String blinkSql = null;
				String dbName = null;
				if ("component".equalsIgnoreCase(relObj.getType())) {
					ExtensionExample example = new ExtensionExample();
					example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + getDbType(tenantId, etlDbName)).andPluginEqualTo(relObj.getRef());
					List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
					dbName = StringUtils.isNotBlank(exts.get(0).getDbName()) ? exts.get(0).getDbName() : etlDbName;
					
					String fkFieldOriginName = null;
					List<String> compFieldsSelOrig = new ArrayList<>();
					for (ViewModel.Field field : relObj.getFields()) {
						if (field.isPk()) {
							fkFieldOriginName = field.getRef();
							continue;
						}
						compFieldsSelOrig.add(field.getRef());
					}
					selectSql = componentObjectProcessor.getComponentSql(tenantId, etlDbName, fkFieldOriginName, compFieldsSelOrig, exts.get(0));
				} else {
	    			JSONObject dsMetaJson = dsInfoService.getDbMetaByDsName(tenantId, relObj.getRef());
					dbName = "origion".equalsIgnoreCase(relObj.getLookupFrom()) ? dsMetaJson.getString("dbName") : etlDbName;
					
					if ("origion".equalsIgnoreCase(relObj.getLookupFrom())) {
						selectSql = viewModelSqlBuilder.getSubQueryFromObjectForLookup(tenantId,relObj, dbMetaJson.getString("dbType")) + " where " + StringUtils.join(lookupOrigionConds, " and ");
					}
				}
				if ("inner join".equalsIgnoreCase(relObj.getRelationType())) {
					blinkSql = " , lateral table(queryDim(concat_ws('|',__traceId__,'" + dbName + "'),'" + selectSql.replace("'", "''") + "'," + StringUtils.join(joinOnCondsRight, ",") + ")) as " + relObj.getCode() + "(x)\n";
				} else {
					blinkSql = " left join lateral table(queryDim(concat_ws('|',__traceId__,'" + dbName + "'),'" + selectSql.replace("'", "''") + "'," + StringUtils.join(joinOnCondsRight, ",") + ")) as " + relObj.getCode() + "(x) on true\n";
				}
				if (objCodeList == null || objCodeList.contains(relObj.getCode())) {
					tableJoinList.add(blinkSql);
				}
    		}
    	}
    	for (ViewModel.Field field : funcFields) {
    		String funcExpress = field.getRef();
			String funcCode = parseFuncExpress(funcExpress).get(0);
			String[] cols = parseFuncExpress(funcExpress).get(1).split(",");
			List<String> exps = new ArrayList<>();
			for (String col : cols) {
				if (col.startsWith("'") && col.endsWith("'")) {
					exps.add(col);
				} else {
					exps.add(colMap.get(col));
				}
			}
			ExtensionExample example = new ExtensionExample();
			example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andCodeEqualTo(funcCode);
			List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
			if (CollectionUtils.isEmpty(exts)) {
				log.error("funcCode:{} not found", funcCode);
				return false;
			}
			String func = exts.get(0).getPlugin().equalsIgnoreCase("groovy")?"groovyFunc":"dfaasFunc";
			String funcExp = null;
			if (field.getType().equalsIgnoreCase("varchar")) {
				funcExp = func + "('" + tenantId + "', '" + funcCode + "'," + StringUtils.join(exps, ",") + ") AS " + field.getCode();
			} else {
				funcExp = "CAST(" + func + "('" + tenantId + "', '" + funcCode + "'," + StringUtils.join(exps, ",") + ") AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + ") AS " + field.getCode();
			}
			relColDefs.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
			relCols.add(funcExp);
    	}
    	String dsName = null;
    	String logTopicName = "stream-" + appId + "-" + dstDsId + "-" + dataModel.getObject().getCode();
    	String logLookupTopicName = "stream-" + appId + "-" + dstDsId + "-" + dataModel.getObject().getCode() + "_lookup";
    	boolean kfkRes = kafkaManagementService.createTopic(tenantId, appName, logLookupTopicName);
		if (!kfkRes) {
			log.error("topic:{} create is failed", logLookupTopicName);
		}
    	if ("metadata".equalsIgnoreCase(dataModel.getObject().getType())) {
    		Datasource objDsInfo = getDsInfoByObjectCode(tenantId, dataModel.getObject().getRef());
	    	dsName = objDsInfo.getDsName();
    	} else if ("table".equalsIgnoreCase(dataModel.getObject().getType())) {
	    	dsName = dataModel.getObject().getRef();
    	}
		JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId,dsName);
    	String logConsumerId = "GID-" + appId + "-" + dstDsId + "-incr_sync-" + dataModel.getObject().getCode() + "_lookup_log-" + versionId;
    	boolean res = kafkaManagementService.createConsumerGroup(tenantId, appName, logConsumerId);
		if (!res) {
			log.error("consumer:{} create is failed", logConsumerId);
		}
		limiterService.setFlowLimitIfNotExists(datatubeInstId, logConsumerId, 1.0);
    	String consumerId = "GID-" + appId + "-" + dstDsId + "-incr_sync-" + dataModel.getObject().getCode() + "_lookup-" + versionId;
    	res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, srcDsMetaJson.getJSONObject("incrConf").getString("dbName"), consumerId);
		if (!res) {
			log.error("consumer:{} create is failed", consumerId);
		}
		limiterService.setFlowLimitIfNotExists(datatubeInstId, consumerId, 1.0);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String def = "--SQL\n" + 
    			"--********************************************************************--\n" + 
    			"--Author: " + operateEmpid + "\n" + 
    			"--CreateTime: " + sdf.format(new Date()) + "\n" + 
    			"--Comment: " + ("sync for " + tableName + " from " + dataModel.getObject().getCode()) + " lookup\n" + 
    			"--Version: " + codegenVersion + "\n" + 
    			"--********************************************************************--\n" + 
    			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\r\n" + 
				"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\r\n" + 
				"CREATE FUNCTION groovyFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatFunctionUdf';\r\n" +
				"CREATE FUNCTION dfaasFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDfaasFunctionUdf';\r\n" +
				"\n";
		
		String dsSink = "";
		for (int i = 0; i < dbNames.size(); i++) {
			dsSink += "create table adb_sink_" + i +" (\r\n" + 
				"    " + pkField + " " + pkFieldType + ",\r\n" + 
				"    " + StringUtils.join(relColDefs, ",") + ",\r\n" + 
				"    __trace_id__ varchar,\r\n" + 
				"    primary key(" + pkField + ")\r\n" + 
				") with (\r\n" + 
				"    type = 'QANAT_ADB30',\r\n" + 
				"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\r\n" + 
				"    dbName='" + dbNames.get(i) + "',\r\n" + 
				"    tableName='" + tableName + "',\r\n" + 
				"    replaceMode = 'update',\r\n" + 
				"    writeMode = 'single',\r\n";
				if (etlDbName.equalsIgnoreCase(dbNames.get(i))) {
	    			dsSink += "    streamType = 'kafka',\n" + 
	    			"    eventTopic = '" + logLookupTopicName + "',\n" + 
	    			"    eventServer = '" + kafkaJson.getString("dbName") + "'\n";
	    		} else {
	    			dsSink += "    streamEvent = 'disable'\n";
	    		}
				dsSink += ");" +
				"\n" +
				"insert into adb_sink_" + i + " select * from v_lookup;\n" +
				"\n";
		}	
		
    	String dsSource = "create table mq_source (\n" + 
				"    msg varchar,\n" + 
				"    __traceId__ varchar header\n" + 
				") with (\n" + 
				"  type = 'custom',\n" + 
				"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
				"  topic = '" + srcDsMetaJson.getJSONObject("incrConf").getString("topicName") + "',\n" + 
				"  `group.id` = '" + consumerId + "',\n" + 
				"  `dbName` = '" + srcDsMetaJson.getJSONObject("incrConf").getString("dbName") + "',\n" +
				"  startupMode = 'TIMESTAMP',\n" +
				"  fieldDelimiter = '`'\n" +
				");\n" +
				"\n" +
				"create table log_mq_source (\n" + 
				"    msg varchar,\n" + 
				"    __traceId__ varchar header\n" + 
				") with (\n" + 
				"  type = 'custom',\n" + 
				"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
				"  topic = '" + logTopicName + "',\n" + 
				"  `group.id` = '" + logConsumerId + "',\n" + 
				"  `dbName` = '" + kafkaJson.getString("dbName") + "',\n" +
				"  startupMode = 'TIMESTAMP',\n" +
				"  fieldDelimiter = '`'\n" +
				");\n" +
				"\n" +
				"create view v_mq as\n" +
				"select a.msg, a.__traceId__ from mq_source as a inner join log_mq_source as b on a.__traceId__=b.__traceId__;\n"
				+ "\n"
				+ "create view v_main_obj as\r\n"
				+ "select CAST(JSON_VALUE(b.x, '$." + pkFieldRef + "') as " + pkFieldType + ") as " + pkField + ","
				+ StringUtils.join(mainObjColsExPkDrcParse, ",") + ",\n"
				+ "a.__traceId__ from v_mq as a , LATERAL TABLE(parseDrcFields(a.msg, '" + pkFieldRef + "'," + StringUtils.join(mainObjColsExPkDrc, ",") + ")) as b(x) \n"
				+ "where JSON_VALUE(b.x, '$.eventType')='1';\n";
    	if ("metadata".equalsIgnoreCase(dataModel.getObject().getType())) {
        	JSONObject srcObjDsMetaJson = dsInfoService.getTableMetaByDsUniqueName(tenantId, dataModel.getObject().getRef());

        	res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, srcObjDsMetaJson.getJSONObject("incrConf").getString("dbName"), consumerId);
    		if (!res) {
    			log.error("consumer:{} create is failed", consumerId);
    		}
        	
    		dsSource = "create table mq_source (\n" + 
    				"    msg varchar,\n" + 
    				"    __traceId__ varchar header\n" + 
    				") with (\n" + 
    				"  type = 'custom',\n" + 
    				"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
    				"  topic = '" + srcObjDsMetaJson.getJSONObject("incrConf").getString("topicName") + "',\n" + 
    				"  `group.id` = '" + consumerId + "',\n" + 
    				"  `dbName` = '" + srcObjDsMetaJson.getJSONObject("incrConf").getString("dbName") + "',\n" +
    				"  startupMode = 'TIMESTAMP',\n" +
    				"  fieldDelimiter = '`'\n" +
    				");\n" +
    				"\n" +
    				"create table log_mq_source (\n" + 
					"    msg varchar,\n" + 
					"    __traceId__ varchar header\n" + 
					") with (\n" + 
					"  type = 'custom',\n" + 
					"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
					"  topic = '" + logTopicName + "',\n" + 
					"  `group.id` = '" + logConsumerId + "',\n" + 
					"  `dbName` = '" + kafkaJson.getString("dbName") + "',\n" +
					"  startupMode = 'TIMESTAMP',\n" +
					"  fieldDelimiter = '`'\n" +
					");\n" 
					+ "\n"
					+ "create view v_mq as\n" 
					+ "select b.msg, b.__traceId__,JSON_VALUE(a.msg, '$.operateType') as operateType from mq_source as a inner join log_mq_source as b on a.__traceId__=b.__traceId__;\n"
					+ "\n"
					+ "create view v_main_obj as\r\n"
					+ "select CAST(JSON_VALUE(b.x, '$." + pkFieldRef + "') as " + pkFieldType + ") as " + pkField + ","
					+ StringUtils.join(mainObjColsExPkDrcParse, ",") + ",\n"
					+ "a.__traceId__ from v_mq as a , LATERAL TABLE(parseDrcFields(a.msg, '" + pkFieldRef + "'," + StringUtils.join(mainObjColsExPkDrcForObj, ",") + ")) as b(x) "
					+ "where operateType='CREATE';\n";
    	}

    	String lookupSql = "create view v_lookup as\r\n"
    			+ "select " + dataModel.getObject().getCode() + "." + pkField + ", " + StringUtils.join(relCols, ",") + ", " + dataModel.getObject().getCode() + ".__traceId__ \r\n"
    			+ "from v_main_obj as " + dataModel.getObject().getCode() + "\n"
    			+ (CollectionUtils.isNotEmpty(compJoinList) ? StringUtils.join(compJoinList, " ") : "")
    			+ (CollectionUtils.isNotEmpty(tableJoinList) ? StringUtils.join(tableJoinList, " ") : "")
    			+ ";\r\n"
    			;
    	
    	
	    String sql = def + "\r\n" + dsSource  + "\r\n" + lookupSql + "\r\n" + dsSink + "\r\n;";
	    blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
	    		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDF, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_KAFKA010, ResourcePackage.BLINK_HSF_UDF), 
	    		false);
	    
	    DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        return true;
	}
    
    private String getDbType(String tenantId, String dbName) {
    	DbInfoExample dbInfoExample = new DbInfoExample();
    	dbInfoExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
    	List<DbInfo> dbInfos = dbInfoMapper.selectByExample(dbInfoExample);
    	if (CollectionUtils.isEmpty(dbInfos)) {
    		throw new QanatBizException("DbInfo not found:" + dbName);
    	}
    	return dbInfos.get(0).getDbType();
    }

	public List<String> parseFuncExpress(String funcExpress) {
		String namePattern = "([_a-zA-Z0-9]+\\w*)";
		String argsPattern = "([_a-zA-Z0-9,\\.\\$']*)";
		String funcPattern = namePattern + "\\(" + argsPattern + "\\)";
		Pattern p = Pattern.compile(funcPattern);
		Matcher m = p.matcher(funcExpress);
		List<String> list = new ArrayList<String>();
		while (m.find()) {
			list.add(m.group(1));
			list.add(m.group(2));
		}
		return list;
	}
	public static void main(String[]args) {
		System.out.println(new LookupProcessor().parseFuncExpress("prehandle_multiple_tree_value(industries)"));
	}
}
