package com.aliyun.wormhole.qanat.process;

import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelHandler;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 管道实例全字段批量稽核任务生产
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class DatatubeInstanceFcBatchCheckProcessor extends JavaProcessor {
    
    @Resource
    private ViewModelHandler viewModelHandler;
    
    @Resource 
    private DatatubeInstanceMapper datatubeInstanceMapper;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
            log.info("DatatubeInstanceFcBatchCheckProcessor, param=[]", context.getJobParameters());
            String tenantId = paramsJson.getString("tenantId");
            if (StringUtils.isBlank(tenantId)) {
            	log.info("tenantId is empty");
                return new ProcessResult(false, "tenantId is empty");
            }
            
            DatatubeInstanceExample example = new DatatubeInstanceExample();
            example.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andProviderEqualTo("viewmodel").andIsTestEqualTo(0L).andTypeEqualTo("dwd");
            List<DatatubeInstance> datatubeInstList = datatubeInstanceMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(datatubeInstList)) {
            	return new ProcessResult(false, "no datatube instances found");
            }
            
        	for (DatatubeInstance datatubeInst : datatubeInstList) {
        		try {
        			Long taskId = viewModelHandler.createBatchCheckTask(tenantId, datatubeInst.getProviderId(), "schedulerx2");
        			log.info("fcbatch task:{} for datatubeInstance:{} has been created", taskId, datatubeInst.getId());
        		} catch (Exception e) {
        			log.error("datatubeInstance:{} fail to create fcbatch task, error={}", datatubeInst.getId(), e.getMessage());
        		}
        	}
        } catch (QanatBizException e) {
            log.error("DatatubeInstanceFcBatchCheckProcessor任务调度异常:{}", e.getMessage());
            return new ProcessResult(false, e.getMessage());
        } catch (Exception e) {
            log.error("DatatubeInstanceFcBatchCheckProcessor任务调度异常", e);
            return new ProcessResult(false, e.getMessage());
        }
        return new ProcessResult(true);
    }
    
    @Override
    public void kill(JobContext context) {
        
    }
}