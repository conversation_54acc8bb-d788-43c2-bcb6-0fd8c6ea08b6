package com.aliyun.wormhole.qanat.service.impl;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.BlinkStreamNode;
import com.aliyun.wormhole.qanat.api.dag.Dag;
import com.aliyun.wormhole.qanat.api.dag.Node;
import com.aliyun.wormhole.qanat.api.dag.NodeAction;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatatubeManagementService;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTaskExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfoWithBLOBs;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInfoMapper;
import com.aliyun.wormhole.qanat.service.dag.DagService;
import com.aliyun.wormhole.qanat.service.datatube.DatatubeHandler;
import com.taobao.ateye.util.CollectionUtils;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 数据管道管理服务
 * <AUTHOR>
 * 2022年9月23日
 */
@Slf4j
@Component
@HSFProvider(serviceInterface = DatatubeManagementService.class)
public class DatatubeManagementServiceImpl implements DatatubeManagementService {
    
    @Resource 
    private DatatubeHandler handler;
    
    @Resource 
    private DatatubeInstanceMapper datatubeInstanceMapper;
    
    @Resource 
    private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;
    
    @Resource
    private TaskInfoMapper taskInfoMapper;
    
    @Resource
    private DagService dagService;
    
    @Resource
    private BlinkService blinkService;
    
    private ThreadPoolExecutor pool = new ThreadPoolExecutor(20, 50, 1000L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(1000));
    
    @Override
    public DataResult<Long> pauseAllDrcTasks() {
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	TaskInfoExample example = new TaskInfoExample();
    	    example.createCriteria().andIsDeletedEqualTo(0L).andNameLike("DAG_drc_%");
    	    List<TaskInfoWithBLOBs> taskInfos = taskInfoMapper.selectByExampleWithBLOBs(example);
    	    if (CollectionUtils.isEmpty(taskInfos)) {
    	    	throw new QanatBizException("no records found");
    	    }
    	    
    	    for (TaskInfoWithBLOBs taskInfo : taskInfos) {
    	    	Dag dag = dagService.getDagByJson(taskInfo.getDag());
    	    	for (Node node : dag.getNodeList()) {
    	    		try {
    		    		if ("com.aliyun.wormhole.qanat.job.QanatBlinkJobProcessor".equalsIgnoreCase(node.getAction()) && node.getNodeAction().equals(NodeAction.STREAM)) {
    		    			String jobName = ((BlinkStreamNode)node).getJobName();

    		    			log.info("taskCount:{} activeCount:{} completedTaskCount:{}" , pool.getTaskCount(), pool.getActiveCount(), pool.getCompletedTaskCount());
    	            		
    		    			pool.submit(() -> {
    		    				try {
    	    		    			log.info("Thread:{} start to process:{}", Thread.currentThread().getName(), jobName);
    	    		    			
    		    					blinkService.pauseJob(taskInfo.getTenantId(), taskInfo.getAppName(), jobName);
    		    					
    	    	            		log.info("Thread:{} finish to process:{}", Thread.currentThread().getName(), jobName);
    		    				} catch (Exception e) {
    		    					log.error("Thread:{} failed to process:{} error:{}", Thread.currentThread().getName(), jobName, e.getMessage(), e);
    		    				}
    		    			});
    		    			
    	
    		                break;
    		        	}
    		    	} catch (Exception e) {
    		    		log.error("DAG:{} check failed, error={}", dag.getId(), e.getMessage(), e);
    		    	}
    		    }
    	    }
        	
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("pauseAllDrcTasks failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("pauseAllDrcTasks failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<Long> resumeAllDrcTasks() {
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	TaskInfoExample example = new TaskInfoExample();
    	    example.createCriteria().andIsDeletedEqualTo(0L).andNameLike("DAG_drc_%");
    	    List<TaskInfoWithBLOBs> taskInfos = taskInfoMapper.selectByExampleWithBLOBs(example);
    	    if (CollectionUtils.isEmpty(taskInfos)) {
    	    	throw new QanatBizException("no records found");
    	    }
    	    
    	    for (TaskInfoWithBLOBs taskInfo : taskInfos) {
    	    	Dag dag = dagService.getDagByJson(taskInfo.getDag());
    	    	for (Node node : dag.getNodeList()) {
    	    		try {
    		    		if ("com.aliyun.wormhole.qanat.job.QanatBlinkJobProcessor".equalsIgnoreCase(node.getAction()) && node.getNodeAction().equals(NodeAction.STREAM)) {
    		    			String jobName = ((BlinkStreamNode)node).getJobName();

    		    			log.info("taskCount:{} activeCount:{} completedTaskCount:{}" , pool.getTaskCount(), pool.getActiveCount(), pool.getCompletedTaskCount());
    	            		
    		    			pool.submit(() -> {
    		    				try {
    	    		    			log.info("Thread:{} start to process:{}", Thread.currentThread().getName(), jobName);
    	    		    			
    		    					blinkService.resumeJob(taskInfo.getTenantId(), taskInfo.getAppName(), jobName);
    		    					
    	    	            		log.info("Thread:{} finish to process:{}", Thread.currentThread().getName(), jobName);
    		    				} catch (Exception e) {
    		    					log.error("Thread:{} failed to process:{} error:{}", Thread.currentThread().getName(), jobName, e.getMessage(), e);
    		    				}
    		    			});
    		    			
    	
    		                break;
    		        	}
    		    	} catch (Exception e) {
    		    		log.error("DAG:{} check failed, error={}", dag.getId(), e.getMessage(), e);
    		    	}
    		    }
    	    }
        	
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("resumeAllDrcTasks failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("resumeAllDrcTasks failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<Long> pauseAllTasks() {
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	List<String> jobList = blinkService.getJobList("1", "qanat_pre");
    	    
    	    for (String jobName : jobList) {
	    		try {
	    			log.info("taskCount:{} activeCount:{} completedTaskCount:{}" , pool.getTaskCount(), pool.getActiveCount(), pool.getCompletedTaskCount());
            		
	    			pool.submit(() -> {
	    				try {
    		    			log.info("Thread:{} start to process:{}", Thread.currentThread().getName(), jobName);
    		    			
	    					blinkService.pauseJob("1", "qanat_pre", jobName);
	    					
    	            		log.info("Thread:{} finish to process:{}", Thread.currentThread().getName(), jobName);
	    				} catch (Exception e) {
	    					log.error("Thread:{} failed to process:{} error:{}", Thread.currentThread().getName(), jobName, e.getMessage(), e);
	    				}
	    			});
		    	} catch (Exception e) {
		    		log.error("job pause:{} failed, error={}", jobName, e.getMessage(), e);
		    	}
    	    }
        	
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("pauseAllTasks failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("pauseAllTasks failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<Long> resumeAllTasks() {
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	List<String> jobList = blinkService.getJobList("1", "qanat_pre");
    	    
    	    for (String jobName : jobList) {
	    		try {
	    			log.info("taskCount:{} activeCount:{} completedTaskCount:{}" , pool.getTaskCount(), pool.getActiveCount(), pool.getCompletedTaskCount());
            		
	    			pool.submit(() -> {
	    				try {
    		    			log.info("Thread:{} start to process:{}", Thread.currentThread().getName(), jobName);
    		    			
	    					blinkService.resumeJob("1", "qanat_pre", jobName);
	    					
    	            		log.info("Thread:{} finish to process:{}", Thread.currentThread().getName(), jobName);
	    				} catch (Exception e) {
	    					log.error("Thread:{} failed to process:{} error:{}", Thread.currentThread().getName(), jobName, e.getMessage(), e);
	    				}
	    			});
		    	} catch (Exception e) {
		    		log.error("job resume:{} failed, error={}", jobName, e.getMessage(), e);
		    	}
    	    }
        	
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("resumeAllTasks failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("resumeAllTasks failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<Long> pauseTasksByObjType(String objType) {
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	 DatatubeInstanceExample diExample = new DatatubeInstanceExample();
        	 DatatubeInstanceExample.Criteria criteria = diExample.createCriteria();
             criteria.andIsDeletedEqualTo(0L).andTenantIdEqualTo("1").andObjectTypeEqualTo(objType).andIsTestEqualTo(0L);
             List<DatatubeInstance> datatubeInstList = datatubeInstanceMapper.selectByExample(diExample);
             if (CollectionUtils.isEmpty(datatubeInstList)) {
             	throw new QanatBizException("no datatube instances found");
             }
             
             List<Long> datatubeInstIds = datatubeInstList.stream().map(e -> e.getId()).collect(Collectors.toList());
            
            DatatubeInstanceTaskExample ditExample = new DatatubeInstanceTaskExample();
            ditExample.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo("1").andTaskTypeEqualTo("blink_stream").andDatatubeInstIdIn(datatubeInstIds);
            
            List<DatatubeInstanceTask> datatubeInstTaskList = datatubeInstanceTaskMapper.selectByExample(ditExample);
            if (CollectionUtils.isEmpty(datatubeInstTaskList)) {
            	throw new QanatBizException("no datatube instance tasks found");
            }
            
        	for (DatatubeInstanceTask instTask : datatubeInstTaskList) {
        		String jobName = instTask.getTaskName();
	    		try {
	    			log.info("taskCount:{} activeCount:{} completedTaskCount:{}" , pool.getTaskCount(), pool.getActiveCount(), pool.getCompletedTaskCount());
	    			pool.submit(() -> {
	    				try {
    		    			log.info("Thread:{} start to process:{}", Thread.currentThread().getName(), jobName);
    		    			
	    					blinkService.pauseJob("1", "qanat_pre", jobName);
	    					
    	            		log.info("Thread:{} finish to process:{}", Thread.currentThread().getName(), jobName);
	    				} catch (Exception e) {
	    					log.error("Thread:{} failed to process:{} error:{}", Thread.currentThread().getName(), jobName, e.getMessage(), e);
	    				}
	    			});
		    	} catch (Exception e) {
		    		log.error("job pause:{} failed, error={}", jobName, e.getMessage(), e);
		    	}
    	    }
        	
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("pauseTasksByObjType failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("pauseTasksByObjType failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<Long> resumeTasksByObjType(String objType) {
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	 DatatubeInstanceExample diExample = new DatatubeInstanceExample();
        	 DatatubeInstanceExample.Criteria criteria = diExample.createCriteria();
             criteria.andIsDeletedEqualTo(0L).andTenantIdEqualTo("1").andObjectTypeEqualTo(objType).andIsTestEqualTo(0L);
             List<DatatubeInstance> datatubeInstList = datatubeInstanceMapper.selectByExample(diExample);
             if (CollectionUtils.isEmpty(datatubeInstList)) {
             	throw new QanatBizException("no datatube instances found");
             }
             
             List<Long> datatubeInstIds = datatubeInstList.stream().map(e -> e.getId()).collect(Collectors.toList());
            
            DatatubeInstanceTaskExample ditExample = new DatatubeInstanceTaskExample();
            ditExample.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo("1").andTaskTypeEqualTo("blink_stream").andDatatubeInstIdIn(datatubeInstIds);
            
            List<DatatubeInstanceTask> datatubeInstTaskList = datatubeInstanceTaskMapper.selectByExample(ditExample);
            if (CollectionUtils.isEmpty(datatubeInstTaskList)) {
            	throw new QanatBizException("no datatube instance tasks found");
            }
            
        	for (DatatubeInstanceTask instTask : datatubeInstTaskList) {
        		String jobName = instTask.getTaskName();
	    		try {
	    			log.info("taskCount:{} activeCount:{} completedTaskCount:{}" , pool.getTaskCount(), pool.getActiveCount(), pool.getCompletedTaskCount());
	    			pool.submit(() -> {
	    				try {
    		    			log.info("Thread:{} start to process:{}", Thread.currentThread().getName(), jobName);
    		    			
	    					blinkService.pauseJob("1", "qanat_pre", jobName);
	    					
    	            		log.info("Thread:{} finish to process:{}", Thread.currentThread().getName(), jobName);
	    				} catch (Exception e) {
	    					log.error("Thread:{} failed to process:{} error:{}", Thread.currentThread().getName(), jobName, e.getMessage(), e);
	    				}
	    			});
		    	} catch (Exception e) {
		    		log.error("job resume:{} failed, error={}", jobName, e.getMessage(), e);
		    	}
    	    }
        	
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("resumeTasksByObjType failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("resumeTasksByObjType failed, error={}", e.getMessage(), e);
        }
        return result;
    }
}