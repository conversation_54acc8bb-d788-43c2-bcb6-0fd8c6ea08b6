package com.aliyun.wormhole.qanat.service.kvstore;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.service.KvstoreService;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelation;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.ResourceExample;
import com.aliyun.wormhole.qanat.dal.mapper.AppResourceRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ResourceMapper;

import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPoolConfig;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * Kvstore服务service
 * <AUTHOR>
 * 2019年7月23日
 */
@Slf4j
@Component
@HSFProvider(serviceInterface = KvstoreService.class)
public class KvstoreServiceImpl implements KvstoreService {
	
	@Resource
	private AppResourceRelationMapper appResourceRelationMapper;
	
	@Resource
	private ResourceMapper resourceMapper;
	
	@Override
	public String get(String tenantId, String appName, String key) {
		log.info("get({},{},{}) started", tenantId, appName, key);
		JedisCluster jedisCluster = null;
		try {
			JSONObject kvMetaJson = getResourceConf(tenantId, appName);
			
			jedisCluster = new JedisCluster(new HostAndPort(kvMetaJson.getString("host"), kvMetaJson.getInteger("port")), 3000, 3000, 50, new JedisPoolConfig());
			return jedisCluster.get(key);
		} catch (Exception e) {
			log.error("get({},{},{}) failed, error={}", tenantId, appName, key, e.getMessage(), e);
		} finally {
			if (jedisCluster != null) {
				try {
					jedisCluster.close();
				} catch (Exception e) {
				}
			}
		}
		return null;
	}
	
	@Override
	public Boolean exists(String tenantId, String appName, String key) {
		log.info("exists({},{},{}) started", tenantId, appName, key);
		JedisCluster jedisCluster = null;
		try {
			JSONObject kvMetaJson = getResourceConf(tenantId, appName);
			
			jedisCluster = new JedisCluster(new HostAndPort(kvMetaJson.getString("host"), kvMetaJson.getInteger("port")), 3000, 3000, 50, new JedisPoolConfig());
			return jedisCluster.exists(key);
		} catch (Exception e) {
			log.error("exists({},{},{}) failed, error={}", tenantId, appName, key, e.getMessage(), e);
		} finally {
			if (jedisCluster != null) {
				try {
					jedisCluster.close();
				} catch (Exception e) {
				}
			}
		}
		return false;
	}
	
	@Override
	public JSONObject getResourceConf(String tenantId, String appName) {
		try {
			AppResourceRelationExample example = new AppResourceRelationExample();
			example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andRelationTypeEqualTo("redis");
			List<AppResourceRelation> rels = appResourceRelationMapper.selectByExample(example);
			if (CollectionUtils.isEmpty(rels)) {
				throw new QanatBizException("no redis resouces config for appName:" + appName + " tenantId:" + tenantId);
			}
			AppResourceRelation ref = rels.get(0);
			ResourceExample example1 = new ResourceExample();
			example1.createCriteria().andResourceNameEqualTo(ref.getResourceName()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andResourceNameEqualTo(ref.getResourceName());
			List<com.aliyun.wormhole.qanat.dal.domain.Resource> resources = resourceMapper.selectByExampleWithBLOBs(example1);
			if (CollectionUtils.isEmpty(resources)) {
				throw new QanatBizException("no app resouces for " + ref.getResourceName());
			}
			com.aliyun.wormhole.qanat.dal.domain.Resource resource = resources.get(0);
			JSONObject kvMetaJson = JSON.parseObject(resource.getMeta());
			return kvMetaJson;
		} catch(Exception e) {
			log.error("get kv resource failed:{}", e.getMessage(), e);
		}
		return null;
	}
	
	@Override
	public String set(String tenantId, String appName, String key, String value) {
		log.info("setValue({},{},{},{}) started", tenantId, appName, key, value);
		JedisCluster jedisCluster = null;
		try {
			JSONObject kvMetaJson = getResourceConf(tenantId, appName);
			jedisCluster = new JedisCluster(new HostAndPort(kvMetaJson.getString("host"), kvMetaJson.getInteger("port")), 3000, 3000, 50, new JedisPoolConfig());
			return jedisCluster.set(key, value);
		} catch (Exception e) {
			log.error("set({},{},{},{}) failed, error={}", tenantId, appName, key, value, e.getMessage(), e);
		} finally {
			if (jedisCluster != null) {
				try {
					jedisCluster.close();
				} catch (Exception e) {
				}
			}
		}
		return null;
	}
	
	@Override
	public String setex(String tenantId, String appName, String key, String value, Integer seconds) {
		log.info("setValue({},{},{},{},{}) started", tenantId, appName, key, value, seconds);
		JedisCluster jedisCluster = null;
		try {
			JSONObject kvMetaJson = getResourceConf(tenantId, appName);
			jedisCluster = new JedisCluster(new HostAndPort(kvMetaJson.getString("host"), kvMetaJson.getInteger("port")), 3000, 3000, 50, new JedisPoolConfig());
			return jedisCluster.setex(key, seconds, value);
		} catch (Exception e) {
			log.error("setex({},{},{},{},{}) failed, error={}", tenantId, appName, key, value, seconds, e.getMessage(), e);
		} finally {
			if (jedisCluster != null) {
				try {
					jedisCluster.close();
				} catch (Exception e) {
				}
			}
		}
		return null;
	}
	
	@Override
	public Long del(String tenantId, String appName, String key) {
		log.info("del({},{},{}) started", tenantId, appName, key);
		JedisCluster jedisCluster = null;
		try {
			JSONObject kvMetaJson = getResourceConf(tenantId, appName);
			jedisCluster = new JedisCluster(new HostAndPort(kvMetaJson.getString("host"), kvMetaJson.getInteger("port")), 3000, 3000, 50, new JedisPoolConfig());
			return jedisCluster.del(key);
		} catch (Exception e) {
			log.error("del({},{},{}) failed, error={}", tenantId, appName, key, e.getMessage(), e);
		} finally {
			if (jedisCluster != null) {
				try {
					jedisCluster.close();
				} catch (Exception e) {
				}
			}
		}
		return null;
	}
}