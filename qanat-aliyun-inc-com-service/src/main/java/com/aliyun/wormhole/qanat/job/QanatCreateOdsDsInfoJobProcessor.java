package com.aliyun.wormhole.qanat.job;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.CreateOdsDsInfoNode;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.OdsSyncTaskRequest;
import com.aliyun.wormhole.qanat.api.dto.OdsSyncTaskResponse;
import com.aliyun.wormhole.qanat.api.service.TaskService;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstanceExample;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import com.aliyun.wormhole.qanat.service.ods.OdsHandler;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * ViewModel创建及全量同步执行任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatCreateOdsDsInfoJobProcessor extends AbstractQanatNodeJobProcessor<CreateOdsDsInfoNode> {
    
    @Resource
    private OdsHandler odsHandler;
    
    @Resource
    private TaskService taskService;
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Override
    void doProcess(Map<String, Object> instParamsMap, CreateOdsDsInfoNode node) {
    	String tenantId = node.getTenantId();
    	String appName = node.getAppName();
    	String srcDsName = node.getSrcDsName();
    	String dstDbName = node.getDstDbName();
    	String tableName = node.getTableName();
    	
    	OdsSyncTaskRequest taskReq = new OdsSyncTaskRequest();
    	taskReq.setAppName(appName);
    	taskReq.setDstDbName(dstDbName);
    	taskReq.setEnableCheck(true);
    	taskReq.setEnableFullLink(true);
    	taskReq.setEnableIncrSync(true);
    	taskReq.setFullSyncBatchSize(10240);
    	taskReq.setFullSyncParallelism("10");
    	taskReq.setOperateEmpid("DAG");
    	taskReq.setSrcDsName(srcDsName);
    	taskReq.setTableName(tableName);
    	taskReq.setTaskDesc("DAG_ods_" + tableName);
    	taskReq.setTaskName("DAG_ods_" + tableName);
    	taskReq.setTenantId(tenantId);
    	OdsSyncTaskResponse createOdsResult = odsHandler.createBatchStreamTasks(taskReq);
    	log.info("createOdsResult={}", JSON.toJSONString(createOdsResult));
    	if (createOdsResult != null && createOdsResult.getDataSyncTaskId() != null) {
    		try {
    			Thread.sleep(60 * 1000);
    		} catch(Exception e) {}
    		DataResult<Long> runTaskResult = taskService.runTask(tenantId, "DAG", createOdsResult.getDataSyncTaskId());
	    	log.info("runTaskResult={}", runTaskResult);
    		int retries = 0;
    		while (retries < 60) {//wait for 1 hours
    			TaskInstanceExample example = new TaskInstanceExample();
    			example.createCriteria().andTenantIdEqualTo(tenantId)
    			.andTaskIdEqualTo(createOdsResult.getDataSyncTaskId())
    			.andParentInstanceIdIsNull()
    			.andStatusEqualTo(Byte.decode("2"));
    			List<TaskInstance> taskInsts = taskInstanceMapper.selectByExample(example);
    			if (CollectionUtils.isNotEmpty(taskInsts)) {
    				log.info("task[{}] is finished", createOdsResult.getDataSyncTaskId());
    				break;
    			} else {
    				retries++;
    				log.info("wait {} mins to retry task[{}] status for {} times", 1, createOdsResult.getDataSyncTaskId(), retries);
    				try {
    	    			Thread.sleep(60 * 1000);
    	    		} catch(Exception e) {}
    			}
    		}
    	} else {
    		log.error("createOdsResult failed");
    		throw new QanatBizException("createOdsResult failed");
    	}
    }
}