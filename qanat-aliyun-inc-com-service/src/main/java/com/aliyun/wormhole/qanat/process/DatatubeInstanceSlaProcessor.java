package com.aliyun.wormhole.qanat.process;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelHandler;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

/**
 * 管道实例SLA数据生产任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class DatatubeInstanceSlaProcessor extends JavaProcessor {
    
    @Resource
    private ViewModelHandler viewModelHandler;
    
    @Resource 
    private DatatubeInstanceMapper datatubeInstanceMapper;

    @Override
    public ProcessResult process(JobContext context) {
        try {
        	String bizDate = null;
            DateTime dt = context.getDataTime();
            if (dt != null) {
            	Calendar cal = dt.toCalendar(Locale.CHINA);
            	cal.add(Calendar.DATE, -1);
            	Date date = cal.getTime();
            	SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                bizDate = sdf.format(date);
            }
            JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
            log.info("ViewModelSlaProcessor, param=[]", context.getJobParameters());
            String tenantId = paramsJson.getString("tenantId");
            if (StringUtils.isBlank(tenantId)) {
            	log.info("tenantId is empty");
                return new ProcessResult(false, "tenantId is empty");
            }
            
            DatatubeInstanceExample example = new DatatubeInstanceExample();
            example.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andProviderEqualTo("viewmodel").andIsTestEqualTo(0L).andTypeEqualTo("dwd");
            List<DatatubeInstance> datatubeInstList = datatubeInstanceMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(datatubeInstList)) {
            	return new ProcessResult(false, "no datatube instances found");
            }
            
        	List<Long> vmIds = datatubeInstList.stream().map(e -> e.getProviderId()).collect(Collectors.toList());
            viewModelHandler.updateSlaData(tenantId, "schedulerx2", vmIds, bizDate);
        } catch (QanatBizException e) {
            log.error("DatatubeInstanceSlaProcessor任务调度异常:{}", e.getMessage());
            return new ProcessResult(false, e.getMessage());
        } catch (Exception e) {
            log.error("DatatubeInstanceSlaProcessor任务调度异常", e);
            return new ProcessResult(false, e.getMessage());
        }
        return new ProcessResult(true);
    }
    
    @Override
    public void kill(JobContext context) {
        
    }
}