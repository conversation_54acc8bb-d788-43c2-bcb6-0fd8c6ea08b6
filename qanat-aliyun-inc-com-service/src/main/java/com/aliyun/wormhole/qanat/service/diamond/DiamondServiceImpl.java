package com.aliyun.wormhole.qanat.service.diamond;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.aliyun.wormhole.qanat.api.service.DiamondService;
import com.taobao.ateye.util.reflect.StringUtils;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.client.impl.DiamondUnitSite;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

/**
 * Dimaond服务service
 * <AUTHOR>
 * 2019年7月23日
 */
@Slf4j
@Component
@HSFProvider(serviceInterface = DiamondService.class)
public class DiamondServiceImpl implements DiamondService {
	
	@Override
	public Boolean publishConfigPre(String dataId, String group, String appName, String config) {
		return DiamondUnitSite.getDiamondUnitEnv("pre").publishSingle(dataId, group, appName, config);
	}
	
	@Override
	public String getConfig(String dataId, String group) {
		try {
			return Diamond.getConfig(dataId, group, 30000);
		} catch (Exception e) {
			log.error("getConfig({},{}) failed, error={}", dataId, group, e.getMessage());
		}
		return null;
	}
	
	@Override
	public String getConfigPre(String dataId, String group) {
		try {
			return DiamondUnitSite.getDiamondUnitEnv("pre").getConfig(dataId, group, 30000);
		} catch (Exception e) {
			log.error("getConfigPre({},{}) failed, error={}", dataId, group, e.getMessage());
		}
		return null;
	}
	
	@Override
	public Boolean setFlowLimit(String gid, Double limit) {
		try {
			boolean result = publishConfigPre(gid + "-flow", "DATATUBE-FLOW", "qanat-aliyun-inc-com", "{\"contentType\":\"drc\",\"rules\":[{\"name\":\"" + gid + "__" + limit + "\",\"filter\":{},\"limit\":" + limit + "}]}");
			log.info("setFlowLimit({},{}) finished, result={}", gid, limit, result);
			return result;
		} catch(Exception e) {
			log.error("setFlowLimit({},{}) failed, error={}", gid, limit, e.getMessage());
			return false;
		}
	}
	
	@Override
	public Boolean setFlowLimitIfNotExists(String gid, Double limit) {
		try {
			String config = getConfigPre(gid + "-flow", "DATATUBE-FLOW");
			if (StringUtils.isBlank(config) || JSON.parseObject(config) == null || JSON.parseObject(config).getJSONArray("rules") == null) {
				boolean result = publishConfigPre(gid + "-flow", "DATATUBE-FLOW", "qanat-aliyun-inc-com", "{\"contentType\":\"drc\",\"rules\":[{\"name\":\"" + gid + "__" + limit + "\",\"filter\":{},\"limit\":" + limit + "}]}");
				log.info("setFlowLimit({},{}) finished, result={}", gid, limit, result);
				return result;
			} else {
				log.info("{} already has config:{}", gid, config);
			}
		} catch(Exception e) {
			log.error("setFlowLimit({},{}) failed, error={}", gid, limit, e.getMessage());
			return false;
		}
		return true;
	}
	
	@Override
	public Boolean publishConfig(String dataId, String group, String appName, String config) {
		return Diamond.publishSingle(dataId, group, appName, config);
	}
	
	@Override
	public Boolean publishBeta(String dataId, String group, String appName, String betaIps, String config) {
		try {
			return Diamond.publishBeta(dataId, group, appName, betaIps, config);
		} catch(Exception e) {
			log.error("publishBeta failed, error={}", e.getMessage(), e);
			return false;
		}
	}
	
	@Override
	public Boolean publishConfigByEnv(String dataId, String group, String appName, String config, String env) {
		return DiamondUnitSite.getDiamondUnitEnv(env).publishSingle(dataId, group, appName, config);
	}
	
	@Override
	public String getUnitList() {
		try {
			return JSON.toJSONString(DiamondUnitSite.getUnitList());
		} catch (Exception e) {
			log.error("getUnitList failed, error={}", e.getMessage(), e);
			return null;
		}
	}
}