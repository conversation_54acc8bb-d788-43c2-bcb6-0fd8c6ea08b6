package com.aliyun.wormhole.qanat.job;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.Adb3ExtTblNode;
import com.aliyun.wormhole.qanat.api.dag.DataSourceType;
import com.aliyun.wormhole.qanat.api.dto.DatasourceRequest;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.AppResourceRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsFieldInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ResourceMapper;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.service.odps.OdpsClient;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;

/**
 * DataX任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatAdb3ExternalTableJobProcessor extends AbstractQanatNodeJobProcessor<Adb3ExtTblNode> {
    
    @Resource
    private DatasourceMapper datasourceMapper;
	
	@Resource
	private AppResourceRelationMapper appResourceRelationMapper;
	
	@Resource
	private ResourceMapper resourceMapper;

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private DsFieldInfoMapper dsFieldInfoMapper;
    
    @Resource
    private DatasourceService dsInfoService;
    

    
    private void createAdbTable(String tableName, Map<String, String> colNameTypeMap, String pk, JSONObject dstDbMetaJson, JSONObject srcDsMetaJson) {
        List<String> colDefList = new ArrayList<>();
        for (String fieldName : colNameTypeMap.keySet()) {
            colDefList.add("`" + fieldName + "` " + getType(srcDsMetaJson, fieldName, colNameTypeMap.get(fieldName)));
        }
        String sql = null;
        if (pk == null) {
        	sql = String.format("Create Table %s ( %s ) INDEX_ALL='Y';", tableName, StringUtils.join(colDefList, ","));
        } else {
            String distributeKey = srcDsMetaJson.getString("distributeKey");
        	String distributePart = "";
        	String pkPart = pk;
        	if (StringUtils.isNotBlank(distributeKey) && !pk.equalsIgnoreCase(distributeKey) && !Arrays.asList(pk.split(",")).contains(distributeKey)) {
        		distributePart = "HASH(" + distributeKey + ")";
            	pkPart = pk + "," + distributeKey;
        	} else {
        		distributePart = "HASH(" + pk + ")";
        	}
        	sql = String.format("Create Table %s ( %s , primary key (%s) ) DISTRIBUTE BY %s INDEX_ALL='Y';", tableName, StringUtils.join(colDefList, ","), pkPart, distributePart);
        }
        RdsConnectionParam param = new RdsConnectionParam();
        param.setUrl(getDbConnectionUrl(dstDbMetaJson));
        param.setPassword(dstDbMetaJson.getString("password"));
        param.setUserName(dstDbMetaJson.getString("username"));
        Connection connection = null;
        Statement statement = null;
        try {
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement();
            log.info("create table ddl:{}", sql);
            statement.execute(sql);
        } catch (Exception e) {
            log.error("create adb table failed", e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {}
                statement = null;
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {}
                connection = null;
            }
        }
    }
    
    private String getType(JSONObject srcDsMetaJson, String fieldName, String type) {
		if (srcDsMetaJson.getJSONObject("extConf") != null 
				&& srcDsMetaJson.getJSONObject("extConf").getJSONObject(fieldName) != null
				&& srcDsMetaJson.getJSONObject("extConf").getJSONObject(fieldName).getBoolean("isTree") != null
				&& srcDsMetaJson.getJSONObject("extConf").getJSONObject(fieldName).getBoolean("isTree")) {
			String seperator = srcDsMetaJson.getJSONObject("extConf").getJSONObject(fieldName).getString("seperator");
			seperator = StringUtils.isNotBlank(seperator) ? seperator : ".";
			String treeItemType = srcDsMetaJson.getJSONObject("extConf").getJSONObject(fieldName).getString("treeItemType");
			treeItemType = StringUtils.isNotBlank(treeItemType) ? treeItemType : "varchar";
			return "multivalue delimiter_tokenizer '" + seperator + "' value_type '" + treeItemType + "'";
		} else {
			return type;
		}
	}

	private void execAdbSql(String sql, JSONObject dstDbMetaJson) {
        RdsConnectionParam param = new RdsConnectionParam();
        param.setUrl(getDbConnectionUrl(dstDbMetaJson));
        param.setUserName(dstDbMetaJson.getString("username"));
        param.setPassword(dstDbMetaJson.getString("password"));
        Connection connection = null;
        Statement statement = null;
        try {
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement();
            log.info("create table ddl:{}", sql);
            statement.execute(sql);
        } catch (Exception e) {
            log.error("create adb table failed:{}", e.getMessage(), e);
            throw new QanatBizException("sql exec failed:" + e.getMessage());
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {}
                statement = null;
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {}
                connection = null;
            }
        }
    }
    
    private boolean createAdbExternalTable(String tableName, Map<String, String> colNameTypeMap, JSONObject srcDsMetaJson, JSONObject dstDbMetaJson) {
        List<String> colDefList = new ArrayList<>();
        for (String fieldName : colNameTypeMap.keySet()) {
            colDefList.add(fieldName + " " + colNameTypeMap.get(fieldName));
        }
        if (srcDsMetaJson.getBoolean("noPartition") != null && srcDsMetaJson.getBoolean("noPartition")) {
        } else {
        	colDefList.add("ds varchar");
        }
        String dropSql = "drop table " + tableName + ";";
        String sql = "Create Table " + tableName + " ( " + StringUtils.join(colDefList, ",") + " ) ENGINE='ODPS' TABLE_PROPERTIES='{\"endpoint\":\"" + srcDsMetaJson.getString("odpsServer") + "\",\n" + 
        		"\"accessid\":\"" + srcDsMetaJson.getString("accessId") + "\",\n" + 
        		"\"accesskey\":\"" + srcDsMetaJson.getString("accessKey") + "\",\n" + 
        		"\"project_name\":\"" + srcDsMetaJson.getString("project") + "\",\n" + 
        		"\"table_name\":\"" + srcDsMetaJson.getString("table") + "\"\n";
        		if (srcDsMetaJson.getBoolean("noPartition") != null && srcDsMetaJson.getBoolean("noPartition")) {
        			sql += "}';";
        		} else {
        		sql +=
        		",\"partition_column\":\"" + (srcDsMetaJson.containsKey("partitionKey") ? srcDsMetaJson.getString("partitionKey") : "ds") + "\"}';";
        		}
        		;
        RdsConnectionParam param = new RdsConnectionParam();
        param.setUrl(getDbConnectionUrl(dstDbMetaJson));
        param.setUserName(dstDbMetaJson.getString("username"));
        param.setPassword(dstDbMetaJson.getString("password"));
        Connection connection = null;
        Statement statement = null;
        try {
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement();
            try {
	            log.info("drop table ddl:{}", dropSql);
	            statement.execute(dropSql);
            } catch(Exception e) {}
            log.info("create table ddl:{}", sql);
            statement.execute(sql);
            return true;
        } catch (Exception e) {
            log.error("create adb table failed", e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {}
                statement = null;
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {}
                connection = null;
            }
        }
        return false;
    }

	@Override
	void doProcess(Map<String, Object> instParamsMap, Adb3ExtTblNode node) {
        String tenantId = String.valueOf(instParamsMap.get("tenantId"));
        
        String srcDsName = node.getSrcDsName();
        String dstDbName = node.getDstDbName();
        DatasourceExample example = new DatasourceExample();
        example.createCriteria().andDsNameEqualTo(srcDsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
        Datasource srcDs = datasourceMapper.selectByExampleWithBLOBs(example).get(0);
        String srcDsMeta = srcDs.getMeta();
        JSONObject srcDsMetaJson = JSON.parseObject(srcDsMeta);

        if (StringUtils.isBlank(srcDs.getDbName())) {
            throw new QanatBizException("dbName of ds:" + srcDsName + " is not config");
        }
        DbInfoExample dbExample = new DbInfoExample();
        dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(srcDs.getDbName()).andTenantIdEqualTo(tenantId);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("db:" + srcDs.getDbName() + " is not found");
        }
        DbInfo srcDbInfo = dbs.get(0);
        JSONObject srcDbMetaJson = JSON.parseObject(srcDbInfo.getMeta());
        if (srcDs.getDsType().equalsIgnoreCase(DataSourceType.ODPS.toString())) {
            srcDsMetaJson.put("project", srcDbMetaJson.getString("project"));
            srcDsMetaJson.put("accessId", srcDbMetaJson.getString("accessId"));
            srcDsMetaJson.put("accessKey", srcDbMetaJson.getString("accessKey"));
            srcDsMetaJson.put("odpsServer", srcDbMetaJson.getString("odpsServer"));
            srcDsMetaJson.put("table", srcDs.getTableName());
        } else {
        	log.info("only odps dsInfo supported");
            return;
        }

        dbExample = new DbInfoExample();
        dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dstDbName).andTenantIdEqualTo(tenantId);
        dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("db:" + dstDbName + " is not found");
        }
        DbInfo dstDbInfo = dbs.get(0);
        JSONObject dstDbMetaJson = JSON.parseObject(dstDbInfo.getMeta());

        //刷新源表元数据
        if (srcDsMetaJson.getBoolean("manualDdl") == null || srcDsMetaJson.getBoolean("manualDdl") == false) {
	        DatasourceRequest dsInfoModReq = new DatasourceRequest();
	        dsInfoModReq.setTenantId(tenantId);
	        dsInfoModReq.setDsName(srcDsName);
	        dsInfoModReq.setOperateEmpid("schedulerx2");
	        dsInfoService.modifyDatasource(dsInfoModReq);
        }
        
        DsFieldInfoExample fieldExample = new DsFieldInfoExample();
        fieldExample.createCriteria().andDsNameEqualTo(srcDsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
        List<DsFieldInfo> dsFields = dsFieldInfoMapper.selectByExample(fieldExample);

        if (CollectionUtils.isEmpty(dsFields)) {
        	throw new QanatBizException("dsFields of ds:" + srcDsName + " is empty");
        }
        
        List<String> pkList = new ArrayList<>();
        Map<String, String> colNameTypeMap = new HashMap<>();
        Map<String, String> colNameDescMap = new HashMap<>();
    	pkList = dsFields.stream().filter(item -> item.getIsPk() == 1).map(DsFieldInfo::getFieldName).collect(Collectors.toList());
    	List<String> colSelList = new ArrayList<>();
    	for (DsFieldInfo field : dsFields) {
    		colNameTypeMap.put(field.getFieldName(), field.getFieldType());
    		colNameDescMap.put(field.getFieldName(), field.getFieldDesc());
            colSelList.add(field.getFieldName());
    	}
        
        String srcTableName = srcDs.getTableName();
        String adbExtTblName = "odps_" + srcTableName + "_external_table";


	    OdpsClient client = new OdpsClient(srcDsMetaJson.getString("odpsServer"), srcDsMetaJson.getString("accessId"), srcDsMetaJson.getString("accessKey"),
	    		srcDsMetaJson.getString("project"), srcDsMetaJson.getString("mcUrl"), srcDsMetaJson.getString("mcToken"));
	    String maxPt = client.getMaxPt(srcTableName);
        
        //创建外部表
        if (!createAdbExternalTable(adbExtTblName, colNameTypeMap, srcDsMetaJson, dstDbMetaJson)) {
        	throw new QanatBizException("External Table:" + adbExtTblName + " create failed");
        }
        
        String dstTableName = StringUtils.isNotBlank(node.getDstTableName()) ? node.getDstTableName() : srcDs.getTableName();
//        String bizDate = getBizDate(srcDsMetaJson.getString("partitionPolicy"));
        String dsExp = "";
        if (srcDsMetaJson.getBoolean("noPartition") != null && srcDsMetaJson.getBoolean("noPartition")) {
        	dsExp = " 1=1 ";
        } else {
        	dsExp = (srcDsMetaJson.containsKey("partitionKey") ? srcDsMetaJson.getString("partitionKey") : "ds") + "='" + maxPt + "'";
        }
        log.info("getOdpsMeta project:{} table:{} maxPt:{}", srcDsMetaJson.getString("project"), srcTableName, dsExp);
        
        if (StringUtils.isBlank(srcDsMetaJson.getString("syncMode")) || "tmp_switch_online".equalsIgnoreCase(srcDsMetaJson.getString("syncMode"))) {
	    	SimpleDateFormat tsSdf = new SimpleDateFormat("yyMMddHHmm");
	        String ts = tsSdf.format(new Date());
	        String tmpDstTableName = "tmp_" + dstTableName + "_" + ts;
	        String bakDstTableName = "bak_" + dstTableName + "_" + ts;
	        
	        //创建最终表
	        createAdbTable(dstTableName, colNameTypeMap, CollectionUtils.isNotEmpty(pkList) ? pkList.get(0) : (StringUtils.isNotBlank(srcDs.getPkFields()) ? srcDs.getPkFields() : null), dstDbMetaJson, srcDsMetaJson);
	        //创建临时表表
	        createAdbTable(tmpDstTableName, colNameTypeMap, CollectionUtils.isNotEmpty(pkList) ? pkList.get(0) : (StringUtils.isNotBlank(srcDs.getPkFields()) ? srcDs.getPkFields() : null), dstDbMetaJson, srcDsMetaJson);
	        
	        log.info("srcTableName{},adbExtTblName{},dstTableName:{},tmpDstTableName:{},bakDstTableName:{}", srcTableName, adbExtTblName, dstTableName, tmpDstTableName, bakDstTableName);

	        //执行外表数据load入
	        String sql = String.format("insert overwrite into %s ( %s ) select %s from %s where %s", tmpDstTableName , StringUtils.join(colSelList, ","), StringUtils.join(colSelList, ","), adbExtTblName, dsExp);
	        long beforeExecTs = System.currentTimeMillis();
	        execAdbSql(sql, dstDbMetaJson);
	        log.info("sql:{} exec cost:{} ms", sql, System.currentTimeMillis() - beforeExecTs);
	        
	        //Load执行结果校验
	        int cnt = countTable(tmpDstTableName, dstDbMetaJson);
			if (cnt == 0) {
				log.error("load 0 records into {} from {} with partition:{}", tmpDstTableName, adbExtTblName, dsExp);
				throw new QanatBizException("load 0 records into " + tmpDstTableName + " from " + adbExtTblName + " with partition:" + dsExp);
			}
	        
			//临时表切换线上表
	        Connection connection = null;
	        Statement statement = null;
	        try {
	            RdsConnectionParam param = new RdsConnectionParam();
	            param.setUrl(getDbConnectionUrl(dstDbMetaJson));
	            param.setPassword(dstDbMetaJson.getString("password"));
	            param.setUserName(dstDbMetaJson.getString("username"));
	            connection = dsHandler.connectToTable(param);
	            statement = connection.createStatement();
	            try {
	                statement.execute("ALTER TABLE " + dstTableName + " RENAME TO " + bakDstTableName);
	            } catch(Exception e) {}
	            try {
	                statement.execute("ALTER TABLE " + tmpDstTableName + " RENAME TO " + dstTableName);
	            } catch(Exception e) {}
	
	            //刷新目标表元数据
	            dsInfoService.updateDsInfoMeta(tenantId, dstDbName, dstTableName, "schedulerx2");
	        } catch (Exception e) {
	            log.error("AdbSql任务调度异常", e);
	            throw new QanatBizException("ADB表操作失败");
	        } finally {
	            if (statement != null) {
	                try {
	                    statement.close();
	                } catch (SQLException e) {
	                }
	                statement = null;
	            }
	            if (connection != null) {
	                try {
	                    connection.close();
	                } catch (SQLException e) {
	                }
	                connection = null;
	            }
	        }
        } else if ("overwrite".equalsIgnoreCase(srcDsMetaJson.getString("syncMode"))) {
        	DatasourceExample dstDsExample = new DatasourceExample();
        	dstDsExample.createCriteria().andDbNameEqualTo(dstDbName).andTableNameEqualTo(dstTableName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
            List<Datasource> dstDsList = datasourceMapper.selectByExampleWithBLOBs(dstDsExample);
            
            if (CollectionUtils.isEmpty(dstDsList)) {
            	throw new QanatBizException("dsInfo not found by tenantId:" + tenantId + " db:" + dstDbName + " table:" + dstTableName);
            }

        	DsFieldInfoExample dstFieldExample = new DsFieldInfoExample();
        	dstFieldExample.createCriteria().andDsNameEqualTo(dstDsList.get(0).getDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
            List<DsFieldInfo> dstDsFields = dsFieldInfoMapper.selectByExample(dstFieldExample);

            if (CollectionUtils.isEmpty(dstDsFields)) {//创建最终表
    	        createAdbTable(dstTableName, colNameTypeMap, CollectionUtils.isNotEmpty(pkList) ? pkList.get(0) : (StringUtils.isNotBlank(srcDs.getPkFields()) ? srcDs.getPkFields() : null), dstDbMetaJson, srcDsMetaJson);
	        	
	            //刷新目标表元数据
	            dsInfoService.updateDsInfoMeta(tenantId, dstDbName, dstTableName, "schedulerx2");
            } else {
                Map<String, DsFieldInfo> dstDsFieldMap = dstDsFields.stream().collect(Collectors.toMap(DsFieldInfo::getFieldName, Function.identity()));
                Map<String, DsFieldInfo> srcDsFieldMap = dsFields.stream().collect(Collectors.toMap(DsFieldInfo::getFieldName, Function.identity()));
                
                List<DsFieldInfo> alterAddFields = new ArrayList<>();
                
                for (String fieldName : srcDsFieldMap.keySet()) {
            		if (!dstDsFieldMap.keySet().contains(fieldName)) {
            			alterAddFields.add(srcDsFieldMap.get(fieldName));
            		}
                }
                
                if (CollectionUtils.isNotEmpty(alterAddFields)) {
                	for (DsFieldInfo field : alterAddFields) {
	                	//临时表切换线上表
	        	        Connection connection = null;
	        	        Statement statement = null;
	        	        try {
	        	            RdsConnectionParam param = new RdsConnectionParam();
	        	            param.setUrl(getDbConnectionUrl(dstDbMetaJson));
	        	            param.setPassword(dstDbMetaJson.getString("password"));
	        	            param.setUserName(dstDbMetaJson.getString("username"));
	        	            connection = dsHandler.connectToTable(param);
	        	            statement = connection.createStatement();
	        	            
	        	            String alterSql = "ALTER TABLE " + dstTableName + " ADD COLUMN " + field.getFieldName() + " " + getType(srcDsMetaJson, field.getFieldName(), field.getFieldType());
	        	            log.info("add column sql:{}", alterSql);
	        	            statement.execute(alterSql);
	        	        } catch (Exception e) {
	        	            log.error("ALTER TABLE异常, error={}", e.getMessage(), e);
	        	        } finally {
	        	            if (statement != null) {
	        	                try {
	        	                    statement.close();
	        	                } catch (SQLException e) {
	        	                }
	        	                statement = null;
	        	            }
	        	            if (connection != null) {
	        	                try {
	        	                    connection.close();
	        	                } catch (SQLException e) {
	        	                }
	        	                connection = null;
	        	            }
	        	        }
                	}
    	        	
    	            //刷新目标表元数据
    	            dsInfoService.updateDsInfoMeta(tenantId, dstDbName, dstTableName, "schedulerx2");
                }
            }
        	
        	//执行外表数据load入
	        String sql = String.format("submit job insert overwrite into %s ( %s ) select %s from %s where %s", dstTableName , StringUtils.join(colSelList, ","), StringUtils.join(colSelList, ","), adbExtTblName, dsExp);
	        long beforeExecTs = System.currentTimeMillis();
	        execAdbSql(sql, dstDbMetaJson);
	        log.info("sql:{} exec cost:{} ms", sql, System.currentTimeMillis() - beforeExecTs);
        }
        log.info("Full Data Sync from {} to {} is finished", srcDsName, dstDbName);
	}

//	private static String getBizDate(String partitionPolicy) {
//		if (partitionPolicy == null || "T-1D".equalsIgnoreCase(partitionPolicy)) {
//			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
//			String bizDate = sdf.format(DateUtils.addDays(new Date(), -1));
//			return bizDate;
//		} else if ("T-2D".equalsIgnoreCase(partitionPolicy)) {
//			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
//			String bizDate = sdf.format(DateUtils.addDays(new Date(), -2));
//			return bizDate;
//		} else if ("T-15M".equalsIgnoreCase(partitionPolicy)) {//偏移15min
//			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHH");
//			Calendar cal = Calendar.getInstance();
//			cal.setTime(DateUtils.addMinutes(new Date(), -30));//偏移30分钟
//			int currentMinute = cal.get(Calendar.MINUTE);
//			String bizDate = sdf.format(cal.getTime());
//			String minute = "00";
//			if (currentMinute >= 0 && currentMinute < 15) {
//				minute = "45";
//			} else if (currentMinute >= 15 && currentMinute < 30) {
//				minute = "00";
//			} else if (currentMinute >= 30 && currentMinute < 45) {
//				minute = "15";
//			} else if (currentMinute >= 45 && currentMinute <= 59) {
//				minute = "30";
//			}
//			return bizDate + minute;
//		} else {
//			return null;
//		}
//	}
	
	private Integer countTable(String tableName, JSONObject dstDbMetaJson) {
    	int cnt = 0;
    	String sql = "select count(1) as total from " + tableName;
        log.info("before exec sql={}", sql);
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            RdsConnectionParam param = new RdsConnectionParam();
            param.setUrl(getDbConnectionUrl(dstDbMetaJson));
            param.setPassword(dstDbMetaJson.getString("password"));
            param.setUserName(dstDbMetaJson.getString("username"));
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
            	cnt = resultSet.getInt("total");
            }
            log.info("after exec sql cnt={}", cnt);
        } catch(Exception e) {
            log.error("execSql failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (Exception e) {
                }
                resultSet = null;
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (Exception e) {
                }
                statement = null;
            }
            if (connection != null) {
                try {
                	connection.close();
                } catch (Exception e) {
                }
                connection = null;
            }
        }
        return cnt;
    }
}