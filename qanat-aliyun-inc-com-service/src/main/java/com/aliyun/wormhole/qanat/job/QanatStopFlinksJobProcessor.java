package com.aliyun.wormhole.qanat.job;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.StopFlinksNode;
import com.aliyun.wormhole.qanat.service.flink.FlinkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * Flink任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatStopFlinksJobProcessor extends AbstractQanatNodeJobProcessor<StopFlinksNode> {
    
    @Resource
    private FlinkService flinkService;

    @Override
    void doProcess(Map<String, Object> instParamsMap, StopFlinksNode flink) {
        try {
            String tenantId = String.valueOf(instParamsMap.get("tenantId"));
            String appName = String.valueOf(instParamsMap.get("appName"));
            String[] jobNameArray = flink.getJobNames().split(",");
            for (String jobName : jobNameArray) {
                flinkService.stopJob(tenantId, appName, jobName);
            }
            log.info("Flink任务:{} 已停止", flink.getJobNames());
        } catch (QanatBizException e) {
            log.error("Flink任务调度异常:{}", e.getMessage());
            throw new QanatBizException(e.getMessage());
        } catch (Exception e) {
            log.error("Flink任务调度异常", e);
            throw new QanatBizException(e.getMessage());
        }
    }
}