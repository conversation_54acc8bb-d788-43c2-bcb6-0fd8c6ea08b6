package com.aliyun.wormhole.qanat.service.ateye;

import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.DatasourceRequest;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.RtdwViewModelTaskService;
import com.aliyun.wormhole.qanat.api.service.ViewModelRequest;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsFieldInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import com.taobao.ateye.annotation.AteyeInvoker;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Component
public class AteyeService {

    @Resource
    private DatasourceMapper datasourceMapper;

    @Resource
    private RtdwViewModelTaskService rtdwViewModelTaskService;

    @Resource
    private DatasourceService dsInfoService;

    @Resource
    private DsFieldInfoMapper dsFieldInfoMapper;

    @Resource
    private DbInfoMapper dbInfoMapper;

    @AteyeInvoker(description = "更新datasource", paraDesc = "主键id&datasoure表名&datasource的库名&dsName&ObjectType")
    public String updateDataSource(Long id, String tableName, String dbname, String dsName, String objectType){
        Datasource datasource = new Datasource();
        datasource.setId(id);
        if (StringUtils.isNoneBlank(tableName)){
            datasource.setTableName(tableName);
        }
        if (StringUtils.isNotBlank(dbname)){
            datasource.setDbName(dbname);
        }
        if (StringUtils.isNotBlank(dsName)){
            datasource.setDsName(dsName);
        }
        if (StringUtils.isNotBlank(objectType)){
            datasource.setObjectType(objectType);
        }
        int updateByPrimaryKey = datasourceMapper.updateByPrimaryKeySelective(datasource);
        return "updateSueccess"+updateByPrimaryKey;
    }

    @AteyeInvoker(description = "创建数据源", paraDesc = "" +
            "数据源name(obj_DEVATA__CUSTOMER)&应用code，日常(qanat_daily)、预发(qanat_pre)、正式(qanat)&" +
            "dbName(devata_rtdw)&数据源类型&数据源唯一标识别(DEVATA__CUSTOMER)&标识&表名&对象类型(cid)&租户id(默认1)&操作人工号&元数据描述")
    public String createDsInfo(String dsName ,String appName, String dbName,
                               String dsType, String dsUniqueName, String remark,
                               String tableName, String objectType,
                               String tenantId, String operateEmpid, String meta){
        DatasourceRequest datasourceRequest = new DatasourceRequest();
        datasourceRequest.setDbName(dbName);
        datasourceRequest.setDsName(dsName);
        datasourceRequest.setAppName(appName);
        datasourceRequest.setDsType(dsType);
        datasourceRequest.setDsUniqueName(dsUniqueName);
        datasourceRequest.setRemark(remark);
        datasourceRequest.setTableName(tableName);
        datasourceRequest.setObjectType(objectType);
        datasourceRequest.setTenantId(tenantId);
        datasourceRequest.setOperateEmpid(operateEmpid);
        datasourceRequest.setMeta(meta);
        DataResult<Long> datasource = dsInfoService.createDatasource(datasourceRequest);
        if (datasource.getSuccess()){
            return "success";
        }
        return datasource.getMessage();
    }

    @AteyeInvoker(description = "创建数据源", paraDesc = "数据库名称&租户id&db类型(mysql,ADB)&数据库描述&元数据&isDel")
    public String createDbInfo(String dbName, String tenantId, String dbType, String dbDesc, String meta, Long isDel) {
        DbInfo dbInfo = new DbInfo();
        dbInfo.setDbName(dbName);
        dbInfo.setTenantId(tenantId);
        dbInfo.setDbType(dbType);
        dbInfo.setDbDesc(dbDesc);
        dbInfo.setMeta(meta);
        dbInfo.setIsDeleted(isDel);
        dbInfo.setGmtCreate(new Date());
        dbInfo.setGmtModified(new Date());
        int insert = dbInfoMapper.insert(dbInfo);
        return "success"+insert;
    }

    @AteyeInvoker(description = "更新", paraDesc = "id&数据库名称&租户id&db类型(mysql,ADB)&数据库描述&元数据&是否删除")
    public String updateDbInfo(long id, String dbName, String tenantId, String dbType, String dbDesc, String meta, Long isdele) {
        DbInfo dbInfo = new DbInfo();
        dbInfo.setId(id);
        dbInfo.setDbName(dbName);
        dbInfo.setTenantId(tenantId);
        dbInfo.setDbType(dbType);
        dbInfo.setDbDesc(dbDesc);
        dbInfo.setMeta(meta);
        dbInfo.setGmtCreate(new Date());
        dbInfo.setGmtModified(new Date());
        dbInfo.setIsDeleted(isdele);
        int insert = dbInfoMapper.updateByPrimaryKeySelective(dbInfo);
        return "success"+insert;
    }

    @AteyeInvoker(description = "创建viewModel", paraDesc = "应用code，日常(qanat_daily)、预发(qanat_pre)、正式(qanat)&数据库名称(devata_rtdw)&备注&对象类型&viewModel名称&租户ID(默认1)&viewModel描述&操作人工号&viewModel")
    public String crateViewModel(String appName, String dbName, String remark,
                                 String objectType, String modelName, String tenantId,
                                 String modelDesc, String operateEmpid, String yaml){
        ViewModelRequest viewModelRequest = new ViewModelRequest();
        if (StringUtils.isBlank(appName)){
            return "应用名不能为空";
        }
        if (StringUtils.isBlank(dbName)){
            return "数据库名称不能为空";
        }
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(operateEmpid) || StringUtils.isBlank(yaml)){
            return "请检查必输项tenantId、操作人工号、viewmodel内容";
        }
        viewModelRequest.setAppName(appName);
        viewModelRequest.setRemark(remark);
        viewModelRequest.setObjectType(objectType);

//        viewModelRequest.setModelName(modelName);
        viewModelRequest.setTenantId(tenantId);
        viewModelRequest.setModelDesc(modelDesc);
        viewModelRequest.setOperateEmpid(operateEmpid);
        viewModelRequest.setYaml(yaml);
        DataResult<Long> viewModelFromYaml = rtdwViewModelTaskService.createViewModelFromYaml(viewModelRequest);
        if (viewModelFromYaml.getSuccess()){
            return "success";
        }
        return viewModelFromYaml.getMessage();
    }

    @AteyeInvoker(description = "升级ViewModel", paraDesc = "租户id,默认为1&定义viewModel的ID&当前操作人工号&新viewModel定义")
    public String modifyViewModel(String tenantId, Long modelId, String empId, String yaml){
        ViewModelRequest viewModelRequest = new ViewModelRequest();
        if (modelId == null || modelId == 0){
            return "modelId 不能为空";
        }
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(empId) || StringUtils.isBlank(yaml)){
            return "请检查必输项tenantId、操作人工号、变更内容";
        }
        viewModelRequest.setModelId(modelId);
        viewModelRequest.setTenantId(tenantId);
        viewModelRequest.setOperateEmpid(empId);
        viewModelRequest.setYaml(yaml);
        DataResult<Long> longDataResult = rtdwViewModelTaskService.modifyViewModel(viewModelRequest);
        return longDataResult.getMessage();
    }

    @AteyeInvoker
    public String deleDataSrouce(Long id){
        int i = datasourceMapper.deleteByPrimaryKey(id);
        return "deleSuccess"+i;
    }
    @AteyeInvoker
    public String updateDsInfo(Long id, String dbname){
        DsFieldInfo ds = new DsFieldInfo();
        ds.setId(id);
        ds.setDbName(dbname);
        int updateByPrimaryKey = dsFieldInfoMapper.updateByPrimaryKeySelective(ds);
        return "updateSuccess"+updateByPrimaryKey;
    }

    @Resource
    BlinkService blinkService ;
    @AteyeInvoker(description = "startBlinkJob", paraDesc = "tenantId&appName&jobName")
    public Long startBlinkJob(String tenantId, String appName, String jobName){
        return blinkService.startJob(tenantId, appName, jobName, new Date());
    }

    @Resource
    private TaskInstanceMapper taskInstanceMapper;
    @AteyeInvoker(description = "更新任务状态", paraDesc = "taskInstanceId&状态")
    public String changeTaskInstance(Long id, Byte status){
        TaskInstance taskInstance = new TaskInstance();
        taskInstance.setId(id);
        taskInstance.setStatus(status);
        int updateByPrimaryKeySelective = taskInstanceMapper.updateByPrimaryKeySelective(taskInstance);
        return "success"+updateByPrimaryKeySelective;
    }



}
