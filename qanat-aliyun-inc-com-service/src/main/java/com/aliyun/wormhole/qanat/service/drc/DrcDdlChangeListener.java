package com.aliyun.wormhole.qanat.service.drc;

import java.sql.Connection;
import java.sql.Statement;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfo;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsFieldInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TenantInfoMapper;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import com.taobao.ateye.util.reflect.StringUtils;
import com.taobao.metaq.client.MetaPushConsumer;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DrcDdlChangeListener {
    
    @Resource
    private DatasourceMapper dsInfoMapper;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private TenantInfoMapper tenantInfoMapper;

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private DsFieldInfoMapper dsFieldInfoMapper;
    
    @PostConstruct
    private void init() {
    	MetaPushConsumer consumer = new MetaPushConsumer("CID-datatube-drc-ddl-event");
    	try {
	        consumer.subscribe("TOPIC_DATATUBE_DDL_EVENT", "*");
			consumer.setConsumeThreadMin(1);
			consumer.setConsumeThreadMax(10);
			consumer.setPullInterval(500);
			
	        consumer.setMessageListener(new MessageListenerConcurrently() {
	            @Override
	            public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
	                for (MessageExt msg : msgs) {
	                    try {
	                        String msgBody = new String(msg.getBody(), "utf-8");
	                        String msgKey = msg.getKeys();
	                        log.info("key={}|msg={}", msgKey, msgBody);
	                        JSONObject json = JSON.parseObject(msgBody);
	                        String ddlSql = json.getJSONObject("fieldValues").getJSONObject("ddl").getString("newValue");
	                        String[] alterTokens = StringUtils.splitByWholeSeparator(ddlSql.trim(), " ");
	                        if (!"alter".equalsIgnoreCase(alterTokens[0])) {
		                        log.error("only alter is supported");
	                        	continue;
	                        }
	                        String tableName = alterTokens[2];
	                        String alterType = alterTokens[3];
	                        if (!("modify".equalsIgnoreCase(alterType)
	                        		|| "add".equalsIgnoreCase(alterType))) {
		                        log.error("only modify or add column is supported");
	                        	continue;
	                        }
	                        String target = alterTokens[4];
	                        if (!"column".equalsIgnoreCase(target)) {
		                        log.error("only alter column is supported");
	                        	continue;
	                        }
	                        String fieldName = alterTokens[5];
	                        String fieldType = null;
	                        if (alterTokens.length > 6) {
	                        	fieldType = alterTokens[6].split("\\(")[0];
	                        }

							//根据msgKey(drcTaskId)
	                        DatasourceExample example = new DatasourceExample();
	                        example.createCriteria().andTableNameEqualTo(msgKey).andIsDeletedEqualTo(0L);
	                        List<Datasource> dsInfos = dsInfoMapper.selectByExample(example);
	                        if (CollectionUtils.isNotEmpty(dsInfos)) {
		                        String tenantId = dsInfos.get(0).getTenantId();
		                        
		                        TenantInfoExample tiExample = new TenantInfoExample();
		                    	tiExample.createCriteria().andTenantIdEqualTo(tenantId);
		                    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
		                    	if (CollectionUtils.isEmpty(tenantList)) {
			                        log.error("tenantId={} is not configed", tenantId);
	                				throw new QanatBizException("tenantId:" + tenantId + " is note configed");
		                    	}
		                    	example = new DatasourceExample();
		                        example.createCriteria().andTableNameEqualTo(tableName).andIsDeletedEqualTo(0L).andDbNameEqualTo(tenantList.get(0).getDefaultDw()).andTenantIdEqualTo(tenantId);
		                        dsInfos = dsInfoMapper.selectByExample(example);
		                        if (CollectionUtils.isEmpty(dsInfos)) {
			                        log.error("tableName={} is not configed any dsInfos with dbName:{}", tableName, tenantList.get(0).getDefaultDw());
	                				continue;
		                    	}
		                        for (Datasource dsInfo : dsInfos) {
			                        executeDdlSql(dsInfo.getTenantId(), dsInfo.getDbName(), ddlSql, dsInfo.getDsName(), alterType, fieldName, fieldType);
		                        }
	                        } else {
		                        log.info("tableName={} is not configed as a drc dsInfo, then try to search dsInfo by table in ddl", msgKey);
		                        example = new DatasourceExample();
		                        example.createCriteria().andTableNameEqualTo(tableName).andIsDeletedEqualTo(0L).andDsTypeIn(Arrays.asList("adb3", "hologres"));
		                        dsInfos = dsInfoMapper.selectByExample(example);
		                        if (CollectionUtils.isEmpty(dsInfos)) {
			                        log.error("tableName={} is not configed any dsInfos", tableName);
	                				continue;
		                    	}
		                        for (Datasource dsInfo : dsInfos) {
			                        executeDdlSql(dsInfo.getTenantId(), dsInfo.getDbName(), ddlSql, dsInfo.getDsName(), alterType, fieldName, fieldType);
		                        }
	                        }
	                    } catch (Exception e) {
	                        log.error("reflect object field change failed, message={}", e.getMessage(), e);
	    	                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
	                    }
	                }
	                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
	            }
	        });

	        consumer.start();
    	} catch(Exception e) {
    		log.error("create consumer for object field change failed", e);
    	}
    }

	private void executeDdlSql(String tenantId, String dstDbName, String ddlSql, String dsName, String alterType, String fieldName, String fieldType) {
		log.info("executeDdlSql({},{})", dstDbName, ddlSql);
		RdsConnectionParam param = new RdsConnectionParam();
		JSONObject dbMetaJson = getAdbDbMeta(dstDbName);
		param.setUrl(dbMetaJson.getString("jdbcUrl"))
		    .setUserName(dbMetaJson.getString("username"))
		    .setPassword(dbMetaJson.getString("password"));
		Connection connection = null;
        Statement statement = null;
		try {
		    connection = dsHandler.connectToTable(param);
		    statement = connection.createStatement();
	        statement.execute(ddlSql);
	        
	        if ("add".equalsIgnoreCase(alterType)) {
                DsFieldInfo record = new DsFieldInfo();
                record.setCreateEmpid("ddl");
                record.setDbName(dstDbName);
                record.setDsName(dsName);
                record.setFieldName(fieldName);
                record.setFieldType(fieldType);
                record.setGmtCreate(new Date());
                record.setGmtModified(new Date());
                record.setIsDeleted(0L);
                record.setIsPk(Byte.valueOf("0"));
                record.setModifyEmpid("ddl");
                record.setTenantId(tenantId);
                dsFieldInfoMapper.insert(record);
            } else if ("modify".equalsIgnoreCase(alterType)) {
                DsFieldInfo record = new DsFieldInfo();
                record.setFieldName(fieldName);
                record.setFieldType(fieldType);
                record.setGmtModified(new Date());
                record.setModifyEmpid("ddl");
                
                DsFieldInfoExample example = new DsFieldInfoExample();
                example.createCriteria().andFieldNameEqualTo(fieldName).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsNameEqualTo(dsName);
            	dsFieldInfoMapper.updateByExampleSelective(record, example);
            }
		} catch (Exception e) {
		    log.error("alter table failed, error={}", e.getMessage(), e);
		} finally {
		    if (param != null) {
		    	dsHandler.closeDataSource(param);
		    }
		}
	}

    private JSONObject getAdbDbMeta(String dbName) {
        DbInfoExample example = new DbInfoExample();
        example.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("no db found");
        }
        String dbMeta = dbs.get(0).getMeta();
        JSONObject dbMetaJson = JSON.parseObject(dbMeta);
        return dbMetaJson;
    }
    
    public static String getLogicName(String realName) {
        String[] tokens = realName.split("_");
        if (tokens.length > 1 && tokens[tokens.length - 1].length() == 4 && StringUtils.isNumeric(tokens[tokens.length - 1])) {
            String[] targetTokens = new String[tokens.length - 1];
            System.arraycopy(tokens, 0, targetTokens, 0, tokens.length - 1);
            return StringUtils.join(targetTokens, "_");
        } else {
        	return realName;
        }
    }
}