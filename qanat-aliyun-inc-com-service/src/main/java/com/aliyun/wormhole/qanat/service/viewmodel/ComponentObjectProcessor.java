package com.aliyun.wormhole.qanat.service.viewmodel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastsql.DbType;
import com.alibaba.fastsql.sql.SQLUtils;
import com.alibaba.fastsql.sql.ast.SQLStatement;
import com.alibaba.fastsql.sql.ast.expr.SQLBinaryOpExpr;
import com.alibaba.fastsql.sql.ast.expr.SQLBinaryOperator;
import com.alibaba.fastsql.sql.ast.expr.SQLIdentifierExpr;
import com.alibaba.fastsql.sql.ast.statement.SQLExprTableSource;
import com.alibaba.fastsql.sql.ast.statement.SQLJoinTableSource;
import com.alibaba.fastsql.sql.ast.statement.SQLSelectQuery;
import com.alibaba.fastsql.sql.ast.statement.SQLSelectQueryBlock;
import com.alibaba.fastsql.sql.ast.statement.SQLSelectStatement;
import com.alibaba.fastsql.sql.ast.statement.SQLSubqueryTableSource;
import com.alibaba.fastsql.sql.ast.statement.SQLTableSource;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.ResourcePackage;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.FlowCtlService;
import com.aliyun.wormhole.qanat.api.service.KafkaManagementService;
import com.aliyun.wormhole.qanat.dal.domain.AppInfo;
import com.aliyun.wormhole.qanat.dal.domain.AppInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelation;
import com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsRelation;
import com.aliyun.wormhole.qanat.dal.domain.DsRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.Extension;
import com.aliyun.wormhole.qanat.dal.domain.ExtensionExample;
import com.aliyun.wormhole.qanat.dal.mapper.AppInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ComponentDsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ExtensionMapper;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.DataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.RelatedDataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Settings;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ComponentObjectProcessor {
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private KafkaManagementService kafkaManagementService;
    
    @Resource
    private AppInfoMapper appInfoMapper;
    
    @Resource
    private ExtensionMapper extensionMapper;
    
    @Resource
    private DatasourceService dsInfoService;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private DatasourceMapper dsInfoMapper;
    
    @Resource
    private ComponentDsRelationMapper componentDsRelationMapper;
    
    @Resource
    private FullLinkProcessor fullLinkProcessor;
	
	@Resource
	private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;
	
	@Resource
	private DsRelationMapper dsRelationMapper;
	
	@Resource
	private FlowCtlService limiterService;
    
    @Value("${datatube.codegen.version}")
    private String codegenVersion;
	
	public static String BLINK_INCR_CHECK_COMPONENT_OBJECT_TABLE_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--********************************************************************--\n" + 
			"%s\n" + 
			"\n" + 
			"create table check_result_mq_sink (\n" + 
			"    msg varchar,\n" + 
			"    id varchar,\n" + 
			"    __traceId__ varchar,\n" + 
			"    primary key(id)\n" + 
			") with (\n" + 
			"    type='QANAT_KAFKA010',\n" + 
			"    class='com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"    topic='%s',\n" + 
			"    dbName='%s'\n" + 
			");\n" + 
			"\n" + 
			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" + 
			"CREATE FUNCTION arrayCompare AS 'com.aliyun.wormhole.qanat.blink.udf.QanatArrayCompareUdf';\n" + 
			"CREATE FUNCTION delayMs AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDelayMsUdf';\n" + 
			"\n" + 
			"create view v_mq as\n" + 
			"select \n" + 
			"    id,\n" + 
			"    'drc' as src,\n" + 
			"    __traceId__\n" + 
			"from v_id\n" + 
			";\n" + 
			"\n" + 
			"create view v_delay as\n" + 
			"select\n" + 
			"    id,\n" + 
			"    src,\n" + 
			"    delayMs(%s, id) as delay_ms,\n" + 
			"    __traceId__\n" + 
			"from v_mq\n" + 
			";\n" + 
			"\n" + 
			"create view v_check_result as\n" + 
			"select \n" + 
			"    (CASE when arrayCompare(l_value, r_value)=false then concat_ws('|', '%s', cast(id as varchar), src, 'NG', l_value, r_value) else concat_ws('|', '%s', id, src, 'OK') END) as msg, cast(id as varchar) as id, __traceId__\n" + 
			"from v_check;\n" + 
			"\n" +
			"insert into check_result_mq_sink\n" + 
			"select * from v_check_result;\n" + 
			"\n" +
			"CREATE TABLE correct_mq_sink (\n" + 
			"    id varchar,\n" + 
			"    `key` varchar,\n" + 
			"    __traceId__ varchar,\n" + 
			"    primary key(`key`)\n" + 
			") WITH (\n" + 
			"    type='QANAT_KAFKA010',\n" + 
			"    class='com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"    topic='%s',\n" + 
			"    dbName='%s',\n" + 
			"    fieldDelimiter='`'\n" +  
			");\n" + 
			"\n" + 
			"insert into correct_mq_sink\n" + 
			"select\n" + 
			"  split_index (msg, '|', 1) as id,\n" + 
			"  split_index (msg, '|', 1) as `key`,\n" + 
			"  __traceId__\n" + 
			"from v_check_result\n" + 
			"where split_index (msg, '|', 3)<>'OK' and split_index (msg, '|', 1) is not null and split_index (msg, '|', 1) <> '';" +
			"\n" + 
			"CREATE TABLE full_link_sink (\n" + 
			"  trace_id varchar,\n" + 
			"  key varchar,\n" + 
			"  ts bigint,\n" + 
			"  db varchar,\n" + 
			"  msg varchar,\n" +
			"  gmt_create timestamp\n" + 
			") WITH (\n" + 
			"  %s" + 
			");\n" + 
			"\n" + 
			"insert into full_link_sink\n" + 
			"select\n" + 
			"  __traceId__ as trace_id,\n" + 
			"  split_index (msg, '|', 1) as key,\n" + 
			"  NOW() as ts,\n" + 
			"  '%s' as db,\n" + 
			"  msg,\n" + 
			"  CURRENT_TIMESTAMP as gmt_create\n" + 
			"from v_check_result;\n" +
			"\n" +
			"create view v_check AS\n" + 
			"select distinct\n" + 
			"    a.id,\n" + 
			"    JSON_VALUE (b.x, '$.%s') as l_value,\n" + 
			"    JSON_VALUE (c.x, '$.%s') as r_value,\n" + 
			"    a.src,\n" + 
			"    a.__traceId__\n" + 
			"from v_delay as a\n" + 
			"LEFT JOIN LATERAL TABLE (queryDim('%s', '%s', a.id)) as b(x) ON TRUE\n" + 
			"LEFT JOIN LATERAL TABLE (queryDim('%s', 'select %s from %s where %s=?', a.id)) as c(x) ON TRUE\n" + 
			";\n"
			;
	
	public static String BLINK_INCR_CHECK_COMPONENT_OBJECT_RELATE_TABLE_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--********************************************************************--\n" + 
			"%s\n" + 
			"\n" + 
			"create table check_result_mq_sink (\n" + 
			"    msg varchar,\n" + 
			"    id varchar,\n" + 
			"    __traceId__ varchar,\n" + 
			"    primary key(id)\n" + 
			") with (\n" + 
			"    type='QANAT_KAFKA010',\n" + 
			"    class='com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"    topic='%s',\n" + 
			"    dbName='%s'\n" + 
			");\n" + 
			"\n" + 
			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" + 
			"CREATE FUNCTION checkData AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDataCheckUdf';\n" + 
			"CREATE FUNCTION qanatConcat AS 'com.aliyun.wormhole.qanat.blink.udf.QanatConcatUdf';\n" + 
			"CREATE FUNCTION delayMs AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDelayMsUdf';\n" + 
			"\n" + 
			"create view v_mq as\n" + 
			"select \n" + 
			"    id,\n" + 
			"    'drc' as src,\n" + 
			"    __traceId__\n" + 
			"from v_id\n" + 
			";\n" + 
			"\n" + 
			"create view v_delay as\n" + 
			"select\n" + 
			"    id,\n" + 
			"    src,\n" + 
			"    delayMs(%s, id) as delay_ms,\n" + 
			"    __traceId__\n" + 
			"from v_mq\n" + 
			";\n" + 
			"\n" + 
			"create view v_check AS\n" + 
			"select distinct\n" + 
			"    a.id,\n" + 
			"    checkData('%s',\n" + 
			"    qanatConcat('|', %s),\n" + 
			"    qanatConcat('|', %s)) as msg,\n" + 
			"    a.src,\n" + 
			"    a.__traceId__\n" + 
			"from v_delay as a\n" + 
			"LEFT JOIN LATERAL TABLE (queryDim('%s', '%s', a.id)) as b(x) ON TRUE\n" + 
			"LEFT JOIN LATERAL TABLE (queryDim('%s', 'select %s from %s where %s=?', a.id)) as c(x) ON TRUE\n" + 
			";\n" +
			"\n" + 
			"create view v_check_result as\n" + 
			"select \n" + 
			"    (CASE when msg<>'' then concat_ws('|', '%s', cast(id as varchar), src, msg) else concat_ws('|', '%s', id, src, 'OK') END) as msg, cast(id as varchar) as id, __traceId__\n" + 
			"from v_check;\n" + 
			"\n" +
			"insert into check_result_mq_sink\n" + 
			"select * from v_check_result;\n" + 
			"\n" +
			"CREATE TABLE correct_mq_sink (\n" + 
			"    id varchar,\n" + 
			"    `key` varchar,\n" + 
			"    __traceId__ varchar,\n" + 
			"    primary key(`key`)\n" + 
			") WITH (\n" + 
			"    type='QANAT_KAFKA010',\n" + 
			"    class='com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"    topic='%s',\n" + 
			"    dbName='%s',\n" + 
			"    fieldDelimiter='`'\n" +  
			");\n" + 
			"\n" + 
			"insert into correct_mq_sink\n" + 
			"select\n" + 
			"  split_index (msg, '|', 1) as id,\n" + 
			"  split_index (msg, '|', 1) as `key`,\n" + 
			"  __traceId__\n" + 
			"from v_check_result\n" + 
			"where split_index (msg, '|', 3)<>'OK' and split_index (msg, '|', 1) is not null and split_index (msg, '|', 1) <> '';" +
			"\n" + 
			"CREATE TABLE full_link_sink (\n" + 
			"  trace_id varchar,\n" + 
			"  key varchar,\n" + 
			"  ts bigint,\n" + 
			"  db varchar,\n" + 
			"  msg varchar,\n" +
			"  gmt_create timestamp\n" + 
			") WITH (\n" + 
			"  %s" + 
			");\n" + 
			"\n" + 
			"insert into full_link_sink\n" + 
			"select\n" + 
			"  __traceId__ as trace_id,\n" + 
			"  split_index (msg, '|', 1) as key,\n" + 
			"  NOW() as ts,\n" + 
			"  '%s' as db,\n" + 
			"  msg,\n" + 
			"  CURRENT_TIMESTAMP as gmt_create\n" + 
			"from v_check_result;\n" 
			;
	
    public boolean processIncrSyncJob(String tenantId, String appName, String jobName, List<String> dbNames, String etlDbName, String tableName, RelatedDataObject object, String operateEmpid, Long versionId, JSONObject kafkaJson, List<JSONObject> drcSourceTopicInfos, ViewModel dataModel, Long datatubeInstId) {
    	String consumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "-incr_sync-" + object.getCode() + "-" + versionId;
    	boolean res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, drcSourceTopicInfos.get(0).getString("dbName"), consumerId);
		if (!res) {
			log.error("consumer:{} create is failed", consumerId);
		}
		limiterService.setFlowLimitIfNotExists(datatubeInstId, consumerId, 1.0);
		String pkField = dataModel.getObject().getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0).getCode();
		String sql = null;
		String componentSql = null;
		String fkFieldOriginName = null;
		String fkFieldName = null;
		String fkFieldType = "bigint";
		
		List<String> compFieldsDef = new ArrayList<>();
		List<String> compFieldsSel = new ArrayList<>();
		List<String> compFieldsSelOrig = new ArrayList<>();
		List<String> compFieldsJson = new ArrayList<>();
		for (ViewModel.Field field : object.getFields()) {
			if (field.isPk()) {
				fkFieldOriginName = field.getRef();
				continue;
			}
			compFieldsDef.add(field.getCode() + " " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
			compFieldsSel.add(field.getCode());
			compFieldsSelOrig.add(field.getRef());
			if ("varchar".equalsIgnoreCase(field.getType())) {
				compFieldsJson.add("JSON_VALUE(b.x, '$." + field.getRef() + "') AS " + field.getCode());
			} else {
				compFieldsJson.add("CAST(JSON_VALUE(b.x, '$." + field.getRef() + "') AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") AS " + field.getCode());
			}
		}
		if (CollectionUtils.isNotEmpty(object.getRelations())) {
			fkFieldName = object.getRelations().get(0).getRelatedField().split("\\.")[1];
			for (ViewModel.Field field : dataModel.getObject().getFields()) {
				if (field.getCode().equalsIgnoreCase(fkFieldName)) {
					fkFieldType = field.getType();
					break;
				}
			}
		}
		ExtensionExample example = new ExtensionExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + getDbType(tenantId, etlDbName)).andPluginEqualTo(object.getRef());
		List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isNotEmpty(exts)) {
			componentSql = getComponentSql(tenantId, etlDbName, fkFieldOriginName, compFieldsSelOrig, exts.get(0)).replace("'", "''");
		}
		if (StringUtils.isBlank(componentSql)) {
			log.error("no component conf found, objectCode:{}", object.getRef());
			throw new QanatBizException(object.getRef() + ":no component conf found");
		}

		List<String> drcSourceTopics = drcSourceTopicInfos.stream().map(e -> e.getString("topicName")).collect(Collectors.toList());
		String dsSourceSql =
					"create table mq_source (\n" + 
					"    msg varchar,\n" + 
					"    __traceId__ varchar header\n" + 
					") with (\n" + 
					"  type = 'custom',\n" + 
					"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
					"  topicPattern = '" + StringUtils.join(drcSourceTopics, "|") + "',\n" + 
					"  `group.id` = '" + consumerId + "',\n" + 
					"  `dbName` = '" + drcSourceTopicInfos.get(0).getString("dbName") + "',\n" +
					"  startupMode = 'TIMESTAMP',\n" +
					"  fieldDelimiter = '`'\n" +
					");\n" + 
					"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
					"create view v_id as\n" + 
					"select \n" + 
					("varchar".equalsIgnoreCase(fkFieldType) ? ("    COALESCE(JSON_VALUE(t.a, '$." + fkFieldOriginName + "'), JSON_VALUE(t.a, '$." + fkFieldOriginName + "_old')) as " + fkFieldName + ",\n"):("    CAST(COALESCE(JSON_VALUE(t.a, '$." + fkFieldOriginName + "'), JSON_VALUE(t.a, '$." + fkFieldOriginName + "_old')) as " + fkFieldType + ") as " + fkFieldName + ",\n")) + 
					"    __traceId__\n" + 
					"from mq_source, LATERAL TABLE(parseDrcFields(msg, '" + fkFieldOriginName + "')) as t (a)\n" +
					"where COALESCE(JSON_VALUE(t.a, '$." + fkFieldOriginName + "'), JSON_VALUE(t.a, '$." + fkFieldOriginName + "_old')) is not null\n" +
					";\n";
		
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sql = "--SQL\n" + 
    			"--********************************************************************--\n" + 
    			"--Author: " + operateEmpid + "\n" + 
    			"--CreateTime: " + sdf.format(new Date()) + "\n" + 
    			"--Comment: " + ("sync for " + object.getCode()) + "\n" + 
    			"--Version: " + codegenVersion + "\n" + 
    			"--********************************************************************--\n" + 
    			dsSourceSql + "\n" +
    			"\n" + 
    			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" + 
    			"\n" +
				"create view v_lookup as\n" + 
				"select\n" + 
				"  a." + fkFieldName + ",\n" + 
				"  " + StringUtils.join(compFieldsJson, ",") + ",\n" + 
				"  a.__traceId__\n" + 
				"from v_id as a \n" + 
				"left join lateral table(\n" + 
				"  queryDim(concat_ws('|',__traceId__,'" + exts.get(0).getDbName() + "'),\n" + 
				"    '" + componentSql + "'\n" + 
				"    , a." + fkFieldName + ")\n" + 
				") as b(x) on true;" +
				"\n";

    	String eventTopicName = "stream-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "-" + object.getCode();
    	boolean kfkRes = kafkaManagementService.createTopic(tenantId, appName, eventTopicName);
		if (!kfkRes) {
			log.error("topic:{} create is failed", eventTopicName);
		}
		
		String pkPart = "";
		if (fkFieldName.equalsIgnoreCase(pkField)) {
			pkPart = StringUtils.isNotBlank(dataModel.getSettings().getDistributeKey()) && !pkField.equalsIgnoreCase(dataModel.getSettings().getDistributeKey()) ? (pkField + "," + dataModel.getSettings().getDistributeKey()) : pkField;
		} else {
			pkPart = fkFieldName;
		}
		
        for (int i = 0; i < dbNames.size(); i++) {
    		sql += "create table adb_sink_" + i + " (\n" + 
    			"    " + fkFieldName + " " + fkFieldType + ",\n" + 
    			"    " + StringUtils.join(compFieldsDef, ",") + ",\n" + 
    			"    __trace_id__ varchar,\n" + 
    			"    primary key(" + pkPart + ")\n" + 
    			") with (\n" + 
    			"    type = 'QANAT_ADB30',\n" + 
    			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
    			"    dbName='" + dbNames.get(i) + "',\n" + 
    			"    tableName='" + tableName + "',\n" + 
    			"    replaceMode = '" + (fkFieldName.equalsIgnoreCase(pkField) ? "upsert" : "update_by_query") + "',\n" + 
    			"    writeMode = 'single',\n";
    		if (etlDbName.equalsIgnoreCase(dbNames.get(i))) {
    			sql += "    streamType = 'kafka',\n" + 
    			"    eventTopic = '" + eventTopicName + "',\n" + 
    			"    eventServer = '" + kafkaJson.getString("dbName") + "'\n";
    		} else {
    			sql += "    streamEvent = 'disable'\n";
    		}
    		sql += ");\n" + 
    			"\n" + 
    			"insert into adb_sink_" + i + "\n" + 
    			"select * from v_lookup;" +
    			"\n";
        }
		
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_KAFKA010), 
        		false);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        return true;
    }

	public boolean processIncrSyncJobForIdTopic(String tenantId, String appName, String jobName, List<String> dbNames, String etlDbName, String tableName, RelatedDataObject object, String operateEmpid, Long versionId, JSONObject kafkaJson, List<JSONObject> idSourceTopicInfos, ViewModel dataModel, Long datatubeInstId) {
		String consumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "-incr_sync-" + object.getCode() + "-" + versionId;
		boolean res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, idSourceTopicInfos.get(0).getString("dbName"), consumerId);
		if (!res) {
			log.error("consumer:{} create is failed", consumerId);
		}
		limiterService.setFlowLimitIfNotExists(datatubeInstId, consumerId, 1.0);
		String pkField = dataModel.getObject().getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0).getCode();
		String sql = null;
		String componentSql = null;
		String fkFieldOriginName = null;
		String fkFieldName = null;
		String fkFieldType = "bigint";

		List<String> compFieldsDef = new ArrayList<>();
		List<String> compFieldsSel = new ArrayList<>();
		List<String> compFieldsSelOrig = new ArrayList<>();
		List<String> compFieldsJson = new ArrayList<>();
		for (ViewModel.Field field : object.getFields()) {
			if (field.isPk()) {
				fkFieldOriginName = field.getRef();
				continue;
			}
			compFieldsDef.add(field.getCode() + " " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
			compFieldsSel.add(field.getCode());
			compFieldsSelOrig.add(field.getRef());
			if ("varchar".equalsIgnoreCase(field.getType())) {
				compFieldsJson.add("JSON_VALUE(b.x, '$." + field.getRef() + "') AS " + field.getCode());
			} else {
				compFieldsJson.add("CAST(JSON_VALUE(b.x, '$." + field.getRef() + "') AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") AS " + field.getCode());
			}
		}
		if (CollectionUtils.isNotEmpty(object.getRelations())) {
			fkFieldName = object.getRelations().get(0).getRelatedField().split("\\.")[1];
			for (ViewModel.Field field : dataModel.getObject().getFields()) {
				if (field.getCode().equalsIgnoreCase(fkFieldName)) {
					fkFieldType = field.getType();
					break;
				}
			}
		}
		ExtensionExample example = new ExtensionExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + getDbType(tenantId, etlDbName)).andPluginEqualTo(object.getRef());
		List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isNotEmpty(exts)) {
			componentSql = getComponentSql(tenantId, etlDbName, fkFieldOriginName, compFieldsSelOrig, exts.get(0)).replace("'", "''");
		}
		if (StringUtils.isBlank(componentSql)) {
			log.error("no component conf found, objectCode:{}", object.getRef());
			throw new QanatBizException(object.getRef() + ":no component conf found");
		}

		List<String> idSourceTopics = idSourceTopicInfos.stream().map(e -> e.getString("topicName")).collect(Collectors.toList());
		String dsSourceSql =
				"create table mq_source (\n" +
						"    " + fkFieldOriginName + " " + fkFieldType + ",\n" +
						"    __traceId__ varchar header\n" +
						") with (\n" +
						"  type = 'custom',\n" +
						"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" +
						"  topic = '" + idSourceTopics.get(0) + "',\n" +
						"  `group.id` = '" + consumerId + "',\n" +
						"  `dbName` = '" + idSourceTopicInfos.get(0).getString("dbName") + "',\n" +
						"  startupMode = 'TIMESTAMP',\n" +
						"  fieldDelimiter = '`'\n" +
						");\n";

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		sql = "--SQL\n" +
				"--********************************************************************--\n" +
				"--Author: " + operateEmpid + "\n" +
				"--CreateTime: " + sdf.format(new Date()) + "\n" +
				"--Comment: " + ("sync for " + object.getCode()) + "\n" +
				"--Version: " + codegenVersion + "\n" +
				"--********************************************************************--\n" +
				dsSourceSql + "\n" +
				"\n" +
				"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" +
				"\n" +
				"create view v_lookup as\n" +
				"select\n" +
				"  a." + fkFieldName + ",\n" +
				"  " + StringUtils.join(compFieldsJson, ",") + ",\n" +
				"  a.__traceId__\n" +
				"from mq_source as a \n" +
				"left join lateral table(\n" +
				"  queryDim(concat_ws('|',__traceId__,'" + exts.get(0).getDbName() + "'),\n" +
				"    '" + componentSql + "'\n" +
				"    , a." + fkFieldName + ")\n" +
				") as b(x) on true;" +
				"\n";

		String eventTopicName = "stream-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "-" + object.getCode();
		boolean kfkRes = kafkaManagementService.createTopic(tenantId, appName, eventTopicName);
		if (!kfkRes) {
			log.error("topic:{} create is failed", eventTopicName);
		}

		String pkPart = "";
		if (fkFieldName.equalsIgnoreCase(pkField)) {
			pkPart = StringUtils.isNotBlank(dataModel.getSettings().getDistributeKey()) && !pkField.equalsIgnoreCase(dataModel.getSettings().getDistributeKey()) ? (pkField + "," + dataModel.getSettings().getDistributeKey()) : pkField;
		} else {
			pkPart = fkFieldName;
		}

		for (int i = 0; i < dbNames.size(); i++) {
			sql += "create table adb_sink_" + i + " (\n" +
					"    " + fkFieldName + " " + fkFieldType + ",\n" +
					"    " + StringUtils.join(compFieldsDef, ",") + ",\n" +
					"    __trace_id__ varchar,\n" +
					"    primary key(" + pkPart + ")\n" +
					") with (\n" +
					"    type = 'QANAT_ADB30',\n" +
					"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" +
					"    dbName='" + dbNames.get(i) + "',\n" +
					"    tableName='" + tableName + "',\n" +
					"    replaceMode = '" + (fkFieldName.equalsIgnoreCase(pkField) ? "upsert" : "update_by_query") + "',\n" +
					"    writeMode = 'single',\n";
			if (etlDbName.equalsIgnoreCase(dbNames.get(i))) {
				sql += "    streamType = 'kafka',\n" +
						"    eventTopic = '" + eventTopicName + "',\n" +
						"    eventServer = '" + kafkaJson.getString("dbName") + "'\n";
			} else {
				sql += "    streamEvent = 'disable'\n";
			}
			sql += ");\n" +
					"\n" +
					"insert into adb_sink_" + i + "\n" +
					"select * from v_lookup;" +
					"\n";
		}

		blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/",
				blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_KAFKA010),
				false);

		DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
		ditRecord.setCreateEmpid(operateEmpid);
		ditRecord.setDatatubeInstId(datatubeInstId);
		ditRecord.setGmtCreate(new Date());
		ditRecord.setGmtModified(new Date());
		ditRecord.setIsDeleted(0L);
		ditRecord.setModifyEmpid(operateEmpid);
		ditRecord.setTaskName(jobName);
		ditRecord.setTaskScript(sql);
		ditRecord.setTaskType("blink_stream");
		ditRecord.setTenantId(tenantId);
		ditRecord.setVersion(versionId.intValue());
		datatubeInstanceTaskMapper.insert(ditRecord);
		return true;
	}

	public String getComponentSql(String tenantId, String etlDbName, String fkFieldOriginName,
			List<String> compFieldsSelOrig, Extension ext) {
		String componentSql;
		if (!etlDbName.equalsIgnoreCase(ext.getDbName())) {
			String origTableName = null;
			ComponentDsRelationExample comDsRelExample = new ComponentDsRelationExample();
			comDsRelExample.createCriteria().andTenantIdEqualTo(tenantId).andComponentNameEqualTo(ext.getCode()).andRelationTypeEqualTo("drc_in");
			List<ComponentDsRelation> comRsRels = componentDsRelationMapper.selectByExample(comDsRelExample);
			if (CollectionUtils.isNotEmpty(comRsRels)) {
				String drcDsName = comRsRels.get(0).getDsName();
				
				DsRelationExample dsRelExample = new DsRelationExample();
				dsRelExample.createCriteria().andTenantIdEqualTo(tenantId).andSrcDsNameEqualTo(drcDsName).andRelationTypeEqualTo("incr");
				List<DsRelation> dsRels = dsRelationMapper.selectByExample(dsRelExample);
				if (CollectionUtils.isNotEmpty(dsRels)) {
					String origDsName = dsRels.get(0).getDstDsName();
					
					DatasourceExample dsExample = new DatasourceExample();
					dsExample.createCriteria().andDsNameEqualTo(origDsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
					List<Datasource> dsList = dsInfoMapper.selectByExampleWithBLOBs(dsExample);
					if (CollectionUtils.isNotEmpty(dsList)) {
						origTableName = dsList.get(0).getTableName();
					} else {
						log.error("no dsinfo found, dsName:{}", origDsName);
					}
				} else {
					log.error("no ds relation found, srcDsName:{} relType:{}", drcDsName, "incr");
				}
			} else {
				log.error("no component ds found, componentName:{} relType:{}", ext.getCode(), "drc_in");
			}
			componentSql = generateStreamSQL4Mysql(ext.getScript(), fkFieldOriginName, origTableName);
		} else {
			componentSql = "select " + fkFieldOriginName + "," + StringUtils.join(compFieldsSelOrig, ",") + " from (" + ext.getScript() + ") as _t0_ where " + fkFieldOriginName + "=?";
		}
		return componentSql;
	}

	public String generateStreamSQL4Mysql(String sql, String fkFieldOriginName, String tableName) {
		DbType mysqlDbType = DbType.mysql;
		List<SQLStatement> stmtList = SQLUtils.parseStatements(sql, mysqlDbType);
		stmtList.forEach(sqlStatement -> {
			SQLSelectStatement sqlSelectStatement = (SQLSelectStatement) sqlStatement;
			SQLSelectQuery sqlSelectQuery = sqlSelectStatement.getSelect().getQuery();
			SQLSelectQueryBlock sqlSelectQueryBlock = (SQLSelectQueryBlock) sqlSelectQuery;
			SQLTableSource from = sqlSelectQueryBlock.getFrom();
			if (from instanceof SQLExprTableSource) {
	            // 形如：select column_name(s) FROM table_name1 t1 where t1.id=xxx
//	            SQLExprTableSource sqlExpreTableSource = (SQLExprTableSource) from;
				sqlSelectQueryBlock.addWhere(new SQLBinaryOpExpr(new SQLIdentifierExpr(fkFieldOriginName), SQLBinaryOperator.Equality, new SQLIdentifierExpr("?")));
				if (sqlSelectQueryBlock.getGroupBy() != null) {
					sqlSelectQueryBlock.setGroupBy(null);
				}
	        } else if (from instanceof SQLSubqueryTableSource) {
	            // 形如：select * from (select * from temp) a，这里第一层from(...)是一个SQLSubqueryTableSource
	            SQLSubqueryTableSource sqlSubqueryTableSource = (SQLSubqueryTableSource) from;
				sqlSelectQueryBlock.addWhere(new SQLBinaryOpExpr(new SQLIdentifierExpr(fkFieldOriginName), SQLBinaryOperator.Equality, new SQLIdentifierExpr("?")));
				if (sqlSelectQueryBlock.getGroupBy() != null) {
					sqlSelectQueryBlock.setGroupBy(null);
				}
	        } else if (from instanceof SQLJoinTableSource) {
	            // 形如：select * from emp e inner join org o on e.org_id = o.id 其中left 'emp e' 是一个SQLExprTableSource，right 'org o'也是一个SQLExprTableSource
	            SQLJoinTableSource sqlJoinTableSource = (SQLJoinTableSource) from;
	            String alias = sqlJoinTableSource.getLeft().getAlias() == null ? sqlJoinTableSource.getRight().getAlias() : sqlJoinTableSource.getLeft().getAlias();
				sqlSelectQueryBlock.addWhere(new SQLBinaryOpExpr(new SQLIdentifierExpr(alias + "." + fkFieldOriginName), SQLBinaryOperator.Equality, new SQLIdentifierExpr("?")));
				if (sqlSelectQueryBlock.getGroupBy() != null) {
					sqlSelectQueryBlock.setGroupBy(null);
				}
	        }
		});
		return SQLUtils.toSQLString(stmtList, mysqlDbType, new SQLUtils.FormatOption(false, false));
	}

	public static void main(String[] args) {

		System.out.println(new ComponentObjectProcessor().generateStreamSQL4Mysql("SELECT\n" +
				"  q.id,\n" +
				"  SUM(y.sales_quantity) AS net_dev_cnt\n" +
				"FROM\n" +
				"  solution_commodity_spec x\n" +
				"  LEFT JOIN solution_commodity y ON x.commodity_id = y.id\n" +
				"  LEFT JOIN solution_config_sheet o ON y.manifest_id = o.id\n" +
				"  LEFT JOIN solution_quote p ON o.id = p.configsheet_id\n" +
				"  LEFT JOIN solution_configuration q ON p.quote_id = q.configuration_id\n" +
				"WHERE\n" +
				"  x.deleted = 0\n" +
				"  AND y.deleted = 0\n" +
				"  AND o.deleted = 0\n" +
				"  AND (\n" +
				"    p.deleted IS NULL\n" +
				"    OR p.deleted = 0\n" +
				"  )\n" +
				"  AND (\n" +
				"    q.deleted IS NULL\n" +
				"    OR q.deleted = 0\n" +
				"  )\n" +
				"  AND (\n" +
				"    (\n" +
				"      p.quote_id IS NULL\n" +
				"      AND q.configuration_id IS NULL\n" +
				"    )\n" +
				"    OR (\n" +
				"      p.quote_id IS NOT NULL\n" +
				"      AND q.configuration_id IS NOT NULL\n" +
				"    )\n" +
				"  )\n" +
				"  AND y.commodity_type = 1\n" +
				"  AND y.category = 2\n" +
				"group by\n" +
				"  q.id", "id", null));
	}
    
    private String getDbType(String tenantId, String dbName) {
    	DbInfoExample dbInfoExample = new DbInfoExample();
    	dbInfoExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
    	List<DbInfo> dbInfos = dbInfoMapper.selectByExample(dbInfoExample);
    	if (CollectionUtils.isEmpty(dbInfos)) {
    		throw new QanatBizException("DbInfo not found:" + dbName);
    	}
    	return dbInfos.get(0).getDbType();
    }
    
    public boolean processIncrSyncJob(String tenantId, String appName, String jobName, List<String> dbNames, String etlDbName, String tableName, String arrayFieldName, RelatedDataObject object, String operateEmpid, Long versionId, JSONObject appKafkaJson, JSONObject streamTopicInfo, List<JSONObject> idSourceTopicInfos, List<JSONObject> drcSourceTopicInfos, String pkField, ViewModel dataModel, Long datatubeInstId) {
    	String consumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "-incr_sync-" + arrayFieldName + "-" + versionId;
		limiterService.setFlowLimitIfNotExists(datatubeInstId, consumerId, 1.0);

		String sql = null;
		String componentSql = null;
		String fkFieldOriginName = null;
		String fkFieldName = null;
		String fkFieldType = "bigint";
		if (streamTopicInfo != null) {
			if (CollectionUtils.isNotEmpty(object.getRelations())) {
				fkFieldName = object.getRelations().get(0).getRelatedField().split("\\.")[1];
				for (ViewModel.Field field : dataModel.getObject().getFields()) {
					if (field.getCode().equalsIgnoreCase(fkFieldName)) {
						fkFieldType = field.getType();
						break;
					}
				}
			} else {
				fkFieldName = pkField;
				fkFieldType = "bigint";
			}
			boolean res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, streamTopicInfo.getString("dbName"), consumerId);
			if (!res) {
				log.error("consumer:{} create is failed", consumerId);
			}
			sql =
					"create table mq_source (\n" + 
					"    " + fkFieldName + " " + fkFieldType + ",\n" + 
				    "    " + arrayFieldName + " varchar,\n" +
					"    __traceId__ varchar header\n" + 
					") with (\n" + 
					"  type = 'custom',\n" + 
					"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
					"  topic = '" + streamTopicInfo.getString("topicName") + "',\n" + 
					"  `group.id` = '" + consumerId + "',\n" + 
					"  `dbName` = '" + streamTopicInfo.getString("dbName") + "',\n" +
					"  startupMode = 'TIMESTAMP',\n" +
					"  fieldDelimiter = '`'\n" +
					");\n" + 
					"\n";
        	String eventTopicName = "stream-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName);
        	boolean kfkRes = kafkaManagementService.createTopic(tenantId, appName, eventTopicName);
    		if (!kfkRes) {
    			log.error("topic:{} create is failed", eventTopicName);
    		}
			for (int i = 0; i < dbNames.size(); i++) {
				sql += "create table adb_sink_" + i + " (\n" + 
					"    " + fkFieldName + " " + fkFieldType + ",\n" + 
					"    " + arrayFieldName + " varchar,\n" + 
					"    __trace_id__ varchar,\n" + 
					"    primary key(" + fkFieldName + ")\n" + 
					") with (\n" + 
					"    type = 'QANAT_ADB30',\n" + 
					"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
					"    dbName='" + dbNames.get(i) + "',\n" + 
					"    tableName='" + tableName + "',\n" + 
					"    replaceMode = '"  + (fkFieldName.equalsIgnoreCase(pkField) ? "upsert" : "update_by_query") + "',\n" + 
					"    writeMode = 'single',\n";
		    		if (etlDbName.equalsIgnoreCase(dbNames.get(i))) {
						sql += "    streamType = 'kafka',\n" + 
						"    eventTopic = '" + eventTopicName + "',\n" + 
						"    eventServer = '" + appKafkaJson.getString("dbName") + "'\n";
		    		} else {
						sql += "    streamEvent = 'disable'\n";
		    		}
					sql += ");\n" + 
					"\n" + 
					"insert into adb_sink_" + i + "\n" + 
					"select\n" + 
					"  " + fkFieldName + ",\n" + 
					"  " + arrayFieldName + ",\n" + 
					"  __traceId__\n" + 
					"from mq_source;\n";
			}
		} else {
			ExtensionExample example = new ExtensionExample();
			example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + getDbType(tenantId, etlDbName)).andPluginEqualTo(object.getRef());
			List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
			if (CollectionUtils.isNotEmpty(exts)) {
				if (CollectionUtils.isNotEmpty(object.getRelations())) {
					fkFieldName = object.getRelations().get(0).getRelatedField().split("\\.")[1];
					fkFieldOriginName = object.getRelations().get(0).getField();
					for (ViewModel.Field field : dataModel.getObject().getFields()) {
						if (field.getCode().equalsIgnoreCase(fkFieldName)) {
							fkFieldType = field.getType();
							break;
						}
					}
				} else {
					fkFieldName = getObjectPk(tenantId, exts.get(0).getObjectType(), exts.get(0).getVersion());
					fkFieldType = "bigint";
				}
				componentSql = "select " + fkFieldOriginName + "," + arrayFieldName + " from (" + exts.get(0).getScript().replace("#pkField#", fkFieldName).replace("#arrayField#", arrayFieldName).replace("'", "''") + ") as _t0_ where " + fkFieldOriginName + "=?";
			}
			if (StringUtils.isBlank(componentSql)) {
				log.error("no component conf found, fieldName:{} objectCode:{}", arrayFieldName, object.getRef());
				throw new QanatBizException(object.getRef() + ":no component conf found");
			}
			
			String dsSourceSql = null;
			List<String> idSourceTopics = idSourceTopicInfos.stream().map(e -> e.getString("topicName")).collect(Collectors.toList());
			List<String> drcSourceTopics = drcSourceTopicInfos.stream().map(e -> e.getString("topicName")).collect(Collectors.toList());
			if (CollectionUtils.isNotEmpty(idSourceTopics)) {
				boolean res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, idSourceTopicInfos.get(0).getString("dbName"), consumerId);
				if (!res) {
					log.error("consumer:{} create is failed", consumerId);
				}
				dsSourceSql =
						"create table mq_source (\n" + 
						"    " + fkFieldName + " " + fkFieldType + ",\n" + 
						"    __traceId__ varchar header\n" + 
						") with (\n" + 
						"  type = 'custom',\n" + 
						"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
						"  topicPattern = '" + StringUtils.join(idSourceTopics, "|") + "',\n" + 
						"  `group.id` = '" + consumerId + "',\n" + 
						"  `dbName` = '" + idSourceTopicInfos.get(0).getString("dbName") + "',\n" +
						"  startupMode = 'TIMESTAMP',\n" +
						"  fieldDelimiter = '`'\n" +
						");\n" + 
						"create view v_id as select " + fkFieldName + ",__traceId__ from mq_source where " + fkFieldName + " is not null;\n" +
						"\n";
			} else if (CollectionUtils.isNotEmpty(drcSourceTopics)) {
				boolean res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, drcSourceTopicInfos.get(0).getString("dbName"), consumerId);
				if (!res) {
					log.error("consumer:{} create is failed", consumerId);
				}
				dsSourceSql =
						"create table mq_source (\n" + 
						"    msg varchar,\n" + 
						"    __traceId__ varchar header\n" + 
						") with (\n" + 
						"  type = 'custom',\n" + 
						"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
						"  topicPattern = '" + StringUtils.join(drcSourceTopics, "|") + "',\n" + 
						"  `group.id` = '" + consumerId + "',\n" + 
						"  `dbName` = '" + drcSourceTopicInfos.get(0).getString("dbName") + "',\n" +
						"  startupMode = 'TIMESTAMP',\n" +
						"  fieldDelimiter = '`'\n" +
						");\n" + 
						"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
						"create view v_id as\n" + 
						"select \n" + 
						("varchar".equalsIgnoreCase(fkFieldType) ? ("    COALESCE(JSON_VALUE(t.a, '$." + fkFieldOriginName + "'), JSON_VALUE(t.a, '$." + fkFieldOriginName + "_old')) as " + fkFieldName + ",\n"):("    CAST(COALESCE(JSON_VALUE(t.a, '$." + fkFieldOriginName + "'), JSON_VALUE(t.a, '$." + fkFieldOriginName + "_old')) as " + fkFieldType + ") as " + fkFieldName + ",\n")) + 
						"    __traceId__\n" + 
						"from mq_source, LATERAL TABLE(parseDrcFields(msg, '" + fkFieldOriginName + "')) as t (a)\n" +
						"where COALESCE(JSON_VALUE(t.a, '$." + fkFieldOriginName + "'), JSON_VALUE(t.a, '$." + fkFieldOriginName + "_old')) is not null\n" +
						";\n";
			} else {
				if (CollectionUtils.isNotEmpty(object.getRelations())) {
					String relObjectKey = object.getRelations().get(0).getRelatedField().split("\\.")[1];
					String relObjectKeyType = null;
					for (ViewModel.Field field : dataModel.getObject().getFields()) {
						if (field.getCode().equalsIgnoreCase(relObjectKey)) {
							relObjectKeyType = field.getType();
							break;
						}
					}
					String dsName = null;
			    	if ("metadata".equalsIgnoreCase(dataModel.getObject().getType())) {
			    		Datasource objDsInfo = getDsInfoByObjectCode(tenantId, dataModel.getObject().getRef());
				    	dsName = objDsInfo.getDsName();
			    	} else if ("table".equalsIgnoreCase(dataModel.getObject().getType())) {
				    	dsName = dataModel.getObject().getRef();
			    	}
					JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, dsName);
	            	String drcTopicName = srcDsMetaJson.getJSONObject("incrConf").getString("topicName");
					boolean res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, srcDsMetaJson.getJSONObject("incrConf").getString("dbName"), consumerId);
					if (!res) {
						log.error("consumer:{} create is failed", consumerId);
					}
					dsSourceSql =
							"create table mq_source (\n" + 
							"    msg varchar,\n" + 
							"    __traceId__ varchar header\n" + 
							") with (\n" + 
							"  type = 'custom',\n" + 
							"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
							"  topic = '" + drcTopicName + "',\n" + 
							"  `group.id` = '" + consumerId + "',\n" + 
							"  `dbName` = '" + srcDsMetaJson.getString("dbName") + "',\n" +
							"  startupMode = 'TIMESTAMP',\n" +
							"  fieldDelimiter = '`'\n" +
							");\n" + 
							"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
							"create view v_id as\n" + 
							"select \n" + 
							("varchar".equalsIgnoreCase(relObjectKeyType) ? ("    COALESCE(JSON_VALUE(t.a, '$." + relObjectKey + "'), JSON_VALUE(t.a, '$." + relObjectKey + "_old')) as " + relObjectKey + ",\n"):("    CAST(COALESCE(JSON_VALUE(t.a, '$." + relObjectKey + "'), JSON_VALUE(t.a, '$." + relObjectKey + "_old')) as " + relObjectKeyType + ") as " + relObjectKey + ",\n")) + 
							"    __traceId__\n" + 
							"from mq_source, LATERAL TABLE(parseDrcFields(msg, '" + relObjectKey + "')) as t (a)\n" + 
							"where COALESCE(JSON_VALUE(t.a, '$." + relObjectKey + "'), JSON_VALUE(t.a, '$." + relObjectKey + "_old')) is not null\n" +
							";\n";
				}
			}
			
	        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	        sql = "--SQL\n" + 
	    			"--********************************************************************--\n" + 
	    			"--Author: " + operateEmpid + "\n" + 
	    			"--CreateTime: " + sdf.format(new Date()) + "\n" + 
	    			"--Comment: " + ("sync for " + tableName + "." + arrayFieldName + " from " + object.getCode()) + "\n" + 
	    			"--Version: " + codegenVersion + "\n" + 
	    			"--********************************************************************--\n" + 
	    			dsSourceSql + "\n" +
	    			"\n" + 
	    			"create view v_lookup as\n" + 
	    			"select\n" + 
	    			"  a." + fkFieldName + ",\n" + 
	    			"  JSON_VALUE (b.x, '$." + arrayFieldName + "') as " + arrayFieldName + ",\n" + 
	    			"  a.__traceId__\n" + 
	    			"from v_id as a \n" + 
	    			"left join lateral table(\n" + 
	    			"  queryDim('" + exts.get(0).getDbName() + "',\n" + 
	    			"    '" + componentSql + "'\n" + 
	    			"    , a." + fkFieldName + ")\n" + 
	    			") as b(x) on true;" + 
	    			"\n" +
	    			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" + 
	    			"\n";

        	String eventTopicName = "stream-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "-" + object.getCode();
        	boolean kfkRes = kafkaManagementService.createTopic(tenantId, appName, eventTopicName);
    		if (!kfkRes) {
    			log.error("topic:{} create is failed", eventTopicName);
    		}
    		
	    	for (int i = 0; i < dbNames.size(); i++) {
	    		sql += "create table adb_sink_" + i + " (\n" + 
	    			"    " + fkFieldName + " " + fkFieldType + ",\n" + 
	    			"    " + arrayFieldName + " varchar,\n" + 
	    			"    __trace_id__ varchar,\n" + 
	    			"    primary key(" + fkFieldName + ")\n" + 
	    			") with (\n" + 
	    			"    type = 'QANAT_ADB30',\n" + 
	    			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
	    			"    dbName='" + dbNames.get(i) + "',\n" + 
	    			"    tableName='" + tableName + "',\n" + 
	    			"    replaceMode = '" + (fkFieldName.equalsIgnoreCase(pkField) ? "update" : "update_by_query") + "',\n" + 
	    			"    writeMode = 'single',\n" ;
		    		if (etlDbName.equalsIgnoreCase(dbNames.get(i))) {
		    			sql += "    streamType = 'kafka',\n" + 
		    			"    eventTopic = '" + eventTopicName + "',\n" + 
		    			"    eventServer = '" + appKafkaJson.getString("dbName") + "'\n";
		    		} else {
		    			sql += "    streamEvent = 'disable'\n";
		    		}
	    			sql += ");\n" + 
	    			"\n" + 
	    			"insert into adb_sink_" + i + "\n" + 
	    			"select * from v_lookup;\n" +
	    			"\n";
	    	}
		}
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_KAFKA010), 
        		false);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        return true;
    }
	
	private Datasource getDsInfoByObjectCode(String tenantId, String objectUniqueCode) {
		DatasourceExample example = new DatasourceExample();
		example.createCriteria().andTableNameEqualTo(objectUniqueCode).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsTypeEqualTo("obj");
		List<Datasource> dsList = dsInfoMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isEmpty(dsList)) {
			throw new QanatBizException(objectUniqueCode + " is not found");
		}
		return dsList.get(0);
	}
    
	public boolean processIncrCheckJob(String tenantId, String appName, String jobName, String dbName, String tableName, String arrayFieldName, RelatedDataObject object, String operateEmpid, Long versionId, JSONObject appKafkaJson, JSONObject streamTopicInfo, List<JSONObject> idSourceTopicInfos, List<JSONObject> drcSourceTopicInfos, Settings modelSettings, ViewModel dataModel, Long datatubeInstId, String etlDbName) {
		try {
			Long dstDsId = dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName);
	    	Long appId = getAppIdByName(tenantId, appName);
			String checkResultTopicName = "chk-" + appId + "-" + dstDsId + "-" + object.getCode();
	    	String consumerId = "GID-" + appId + "-" + dstDsId + "-incr_check-" + object.getCode() + "-" + versionId;
	    	boolean res = kafkaManagementService.createConsumerGroup(tenantId, appName, consumerId);
			if (!res) {
				log.error("consumer:{} create is failed", consumerId);
			}
	    	res = kafkaManagementService.createTopic(tenantId, appName, checkResultTopicName);
			if (!res) {
				log.error("topic:{} create is failed", checkResultTopicName);
			}
	    	String correctTopicName = "crt-" + appId + "-" + dstDsId + "-" + object.getCode();
	    	res = kafkaManagementService.createTopic(tenantId, appName, correctTopicName);
			if (!res) {
				log.error("topic:{} create is failed", correctTopicName);
			}
			String sql = null;
			String componentSql = null;
			String pkField = dataModel.getObject().getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0).getCode();
			String fkField = null;
			String fkFieldOriginName = null;
			String fkFieldType = null;
			if (streamTopicInfo != null) {
				if (CollectionUtils.isNotEmpty(object.getRelations())) {
					fkField = object.getRelations().get(0).getRelatedField().split("\\.")[1];
					fkFieldOriginName = object.getRelations().get(0).getField();
					for (ViewModel.Field field : dataModel.getObject().getFields()) {
						if (field.getCode().equalsIgnoreCase(fkField)) {
							fkFieldType = field.getType();
							break;
						}
					}
				} else {
					fkField = pkField;
					fkFieldType = "bigint";
				}
				res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, streamTopicInfo.getString("dbName"), consumerId);
				if (!res) {
					log.error("consumer:{} create is failed", consumerId);
				}
				sql =
						"create table mq_source (\n" + 
						"    id varchar,\n" + 
					    "    " + arrayFieldName + " varchar,\n" +
						"    __traceId__ varchar header\n" + 
						") with (\n" + 
						"  type = 'custom',\n" + 
						"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
						"  topic = '" + streamTopicInfo.getString("topicName") + "',\n" + 
						"  `group.id` = '" + consumerId + "',\n" + 
						"  `dbName` = '" + streamTopicInfo.getString("dbName") + "',\n" +
						"  startupMode = 'TIMESTAMP',\n" +
						"  fieldDelimiter = '`'\n" +
						");\n" + 
						"\n" + 
						"create table check_result_mq_sink (\n" + 
						"    msg varchar,\n" + 
						"    id varchar,\n" + 
						"    __traceId__ varchar,\n" + 
						"    primary key(id)\n" + 
						") with (\n" + 
						"    type='QANAT_KAFKA010',\n" + 
						"    class='com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
						"    topic='" + checkResultTopicName + "',\n" + 
						"    dbName='" + appKafkaJson.getString("dbName") + "'\n" + 
						");\n" + 
						"\n" + 
						"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" + 
						"CREATE FUNCTION arrayCompare AS 'com.aliyun.wormhole.qanat.blink.udf.QanatArrayCompareUdf';\n" + 
						"CREATE FUNCTION delayMs AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDelayMsUdf';\n" + 
						"\n" + 
						"create view v_mq as\n" + 
						"select \n" + 
						"    id,\n" + 
						"    " + arrayFieldName + " as l_value,\n" + 
						"    'drc' as src,\n" + 
						"    __traceId__\n" + 
						"from mq_source\n" + 
						";\n" + 
						"\n" + 
						"create view v_delay as\n" + 
						"select\n" + 
						"    id,\n" + 
						"    l_value,\n" + 
						"    src,\n" + 
						"    delayMs(" + modelSettings.getIncrCheckDelayMs() + ", id) as delay_ms,\n" + 
						"    __traceId__\n" + 
						"from v_mq\n" + 
						";\n" + 
						"\n" + 
						"create view v_check AS\n" + 
						"select distinct\n" + 
						"    a.id,\n" + 
						"    l_value,\n" + 
						"    JSON_VALUE (b.x, '$." + arrayFieldName + "') as r_value,\n" + 
						"    a.src,\n" + 
						"    a.__traceId__\n" + 
						"from v_delay as a\n" + 
						(!fkField.equalsIgnoreCase(pkField) ? 
								", LATERAL TABLE (queryDim('" + dbName + "', 'select " + arrayFieldName + " from " + tableName + " where " + fkField + "=?', a.id)) as b(x)\n"
								:
								"LEFT JOIN LATERAL TABLE (queryDim('" + dbName + "', 'select " + arrayFieldName + " from " + tableName + " where " + fkField + "=?', a.id)) as b(x) ON TRUE\n") + 
						";\n" + 
						"\n" + 
						"create view v_check_result as\n" + 
						"select \n" + 
						"    (CASE when arrayCompare(l_value, r_value)=false then concat_ws('|', '%s', cast(id as varchar), src, 'NG', l_value, r_value) else concat_ws('|', '%s', id, src, 'OK') END) as msg, cast(id as varchar) as id, __traceId__\n" + 
						"from v_check;\n" + 
						"\n" +
						"insert into check_result_mq_sink\n" + 
						"select * from v_check_result;\n" + 
						"\n" +
						"CREATE TABLE correct_mq_sink (\n" + 
						"    id varchar,\n" + 
						"    `key` varchar,\n" + 
						"    __traceId__ varchar,\n" + 
						"    primary key(`key`)\n" + 
						") WITH (\n" + 
						"    type='QANAT_KAFKA010',\n" + 
						"    class='com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
						"    topic='" + correctTopicName + "',\n" + 
						"    dbName='" + appKafkaJson.getString("dbName") + "',\n" + 
						"    fieldDelimiter='`'\n" +  
						");\n" + 
						"\n" + 
						"insert into correct_mq_sink\n" + 
						"select\n" + 
						"  split_index (msg, '|', 1) as id,\n" + 
						"  split_index (msg, '|', 1) as `key`,\n" + 
						"  __traceId__\n" + 
						"from v_check_result\n" + 
						"where split_index (msg, '|', 3)<>'OK' and split_index (msg, '|', 1) is not null and split_index (msg, '|', 1) <> '';" +
						"\n" + 
						"CREATE TABLE full_link_sink (\n" + 
						"  trace_id varchar,\n" + 
						"  key varchar,\n" + 
						"  ts bigint,\n" + 
						"  db varchar,\n" + 
						"  msg varchar,\n" +
						"  gmt_create timestamp\n" + 
						") WITH (\n" + 
						"  " + fullLinkProcessor.getFullLinkSinkWithClause(tenantId, appName) + "\n" +
						");\n" + 
						"\n" + 
						"insert into full_link_sink\n" + 
						"select\n" + 
						"  __traceId__ as trace_id,\n" + 
						"  split_index (msg, '|', 1) as key,\n" + 
						"  NOW() as ts,\n" + 
						"  '" + checkResultTopicName + "' as db,\n" + 
						"  msg,\n" + 
						"  CURRENT_TIMESTAMP as gmt_create\n" + 
						"from v_check_result;\n"
						;
			} else {
				ExtensionExample example = new ExtensionExample();
				example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + getDbType(tenantId, dbName)).andPluginEqualTo(object.getRef());
				List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
				if (CollectionUtils.isNotEmpty(exts)) {
					if (CollectionUtils.isNotEmpty(object.getRelations())) {
						fkField = object.getRelations().get(0).getRelatedField().split("\\.")[1];
						for (ViewModel.Field field : dataModel.getObject().getFields()) {
							if (field.getCode().equalsIgnoreCase(fkField)) {
								fkFieldType = field.getType();
								break;
							}
						}
					} else {
						fkField = getObjectPk(tenantId, exts.get(0).getObjectType(), exts.get(0).getVersion());
						fkFieldType = "bigint";
					}
					componentSql = "select " + fkFieldOriginName + "," + arrayFieldName + " from (" + exts.get(0).getScript().replace("#pkField#", fkField).replace("#arrayField#", arrayFieldName).replace("'", "''") + ") as _t0_ where " + fkFieldOriginName + "=?";
				}
				if (StringUtils.isBlank(componentSql)) {
					log.error("no component conf found, fieldName:{} objectCode:{}", arrayFieldName, object.getRef());
					throw new QanatBizException(object.getRef() + ":no component conf found");
				}
				
				String dsSourceSql = null;
				List<String> idSourceTopics = idSourceTopicInfos.stream().map(e -> e.getString("topicName")).collect(Collectors.toList());
				List<String> drcSourceTopics = drcSourceTopicInfos.stream().map(e -> e.getString("topicName")).collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(idSourceTopics)) {
					res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, idSourceTopicInfos.get(0).getString("dbName"), consumerId);
					if (!res) {
						log.error("consumer:{} create is failed", consumerId);
					}
					dsSourceSql =
							"create table mq_source (\n" + 
							"    " + fkField + " " + fkFieldType + ",\n" + 
							"    __traceId__ varchar header\n" + 
							") with (\n" + 
							"  type = 'custom',\n" + 
							"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
							"  topicPattern = '" + StringUtils.join(idSourceTopics, "|") + "',\n" + 
							"  `group.id` = '" + consumerId + "',\n" + 
							"  `dbName` = '" + idSourceTopicInfos.get(0).getString("dbName") + "',\n" +
							"  startupMode = 'TIMESTAMP',\n" +
							"  fieldDelimiter = '`'\n" +
							");\n" + 
							"create view v_id as select " + fkField + " as id, __traceId__ from mq_source where " + fkField + " is not null;\n" +
							"\n";
				} else if (CollectionUtils.isNotEmpty(drcSourceTopics)) {
					res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, drcSourceTopicInfos.get(0).getString("dbName"), consumerId);
					if (!res) {
						log.error("consumer:{} create is failed", consumerId);
					}
					dsSourceSql =
							"create table mq_source (\n" + 
							"    msg varchar,\n" + 
							"    __traceId__ varchar header\n" + 
							") with (\n" + 
							"  type = 'custom',\n" + 
							"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
							"  topicPattern = '" + StringUtils.join(drcSourceTopics, "|") + "',\n" + 
							"  `group.id` = '" + consumerId + "',\n" + 
							"  `dbName` = '" + drcSourceTopicInfos.get(0).getString("dbName") + "',\n" +
							"  startupMode = 'TIMESTAMP',\n" +
							"  fieldDelimiter = '`'\n" +
							");\n" + 
							"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
							"create view v_id as\n" + 
							"select \n" + 
							("varchar".equalsIgnoreCase(fkFieldType) ? ("    COALESCE(JSON_VALUE(t.a, '$." + fkFieldOriginName + "'), JSON_VALUE(t.a, '$." + fkFieldOriginName + "_old')) as id,\n"):("    CAST(COALESCE(JSON_VALUE(t.a, '$." + fkFieldOriginName + "'), JSON_VALUE(t.a, '$." + fkFieldOriginName + "_old')) as " + fkFieldType + ") as id,\n")) + 
							"    __traceId__\n" + 
							"from mq_source, LATERAL TABLE(parseDrcFields(msg, '" + fkFieldOriginName + "')) as t (a)\n" + 
							"where COALESCE(JSON_VALUE(t.a, '$." + fkFieldOriginName + "'), JSON_VALUE(t.a, '$." + fkFieldOriginName + "_old')) is not null\n" +
							";\n";
				} else {
					if (CollectionUtils.isNotEmpty(object.getRelations())) {
						String relObjectKey = object.getRelations().get(0).getRelatedField().split("\\.")[1];
						String relObjectKeyType = "bigint";
						for (ViewModel.Field field : dataModel.getObject().getFields()) {
							if (field.getCode().equalsIgnoreCase(relObjectKey)) {
								relObjectKeyType = field.getType();
								break;
							}
						}
				    	String dsName = null;
				    	if ("metadata".equalsIgnoreCase(dataModel.getObject().getType())) {
				    		Datasource objDsInfo = getDsInfoByObjectCode(tenantId, object.getRef());
					    	dsName = objDsInfo.getDsName();
				    	} else if ("table".equalsIgnoreCase(dataModel.getObject().getType())) {
					    	dsName = dataModel.getObject().getRef();
				    	}
						JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, dsName);
		            	String drcTopicName = srcDsMetaJson.getJSONObject("incrConf").getString("topicName");
						res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, srcDsMetaJson.getString("dbName"), consumerId);
						if (!res) {
							log.error("consumer:{} create is failed", consumerId);
						}
						dsSourceSql =
								"create table mq_source (\n" + 
								"    msg varchar,\n" + 
								"    __traceId__ varchar header\n" + 
								") with (\n" + 
								"  type = 'custom',\n" + 
								"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
								"  topic = '" + drcTopicName + "',\n" + 
								"  `group.id` = '" + consumerId + "',\n" + 
								"  `dbName` = '" + srcDsMetaJson.getString("dbName") + "',\n" +
								"  startupMode = 'TIMESTAMP',\n" +
								"  fieldDelimiter = '`'\n" +
								");\n" + 
								"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
								"create view v_id as\n" + 
								"select \n" + 
								("varchar".equalsIgnoreCase(relObjectKeyType) ? ("    COALESCE(JSON_VALUE(t.a, '$." + relObjectKey + "'), JSON_VALUE(t.a, '$." + relObjectKey + "_old')) as id,\n"):("    CAST(COALESCE(JSON_VALUE(t.a, '$." + relObjectKey + "'), JSON_VALUE(t.a, '$." + relObjectKey + "_old')) as " + relObjectKeyType + ") as id,\n")) + 
								"    __traceId__\n" + 
								"from mq_source, LATERAL TABLE(parseDrcFields(msg, '" + relObjectKey + "')) as t (a)\n" + 
								"where COALESCE(JSON_VALUE(t.a, '$." + relObjectKey + "'), JSON_VALUE(t.a, '$." + relObjectKey + "_old')) is not null\n" +
								";\n";
					}
				}

		        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		        sql = String.format(BLINK_INCR_CHECK_COMPONENT_OBJECT_TABLE_SQL
		            , operateEmpid
		            , sdf.format(new Date())
		            , "incr check for " + tableName + " from " + object.getCode()
		            , dsSourceSql
		            , checkResultTopicName
		            , appKafkaJson.getString("dbName")
		            , modelSettings.getIncrCheckDelayMs()
		            , tableName + "." + arrayFieldName
		            , tableName + "." + arrayFieldName
		            , correctTopicName
		            , appKafkaJson.getString("dbName")
		            , fullLinkProcessor.getFullLinkSinkWithClause(tenantId, appName)
		            , checkResultTopicName
		            , arrayFieldName
		            , arrayFieldName
		            , exts.get(0).getDbName()
		            , componentSql
		            , dbName
		            , arrayFieldName
		            , tableName
		            , fkField
		            );
			}
	        
	        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
	        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_KAFKA010, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_UDF, ResourcePackage.BLINK_ADB3), false);
	        
	        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
	        ditRecord.setCreateEmpid(operateEmpid);
	        ditRecord.setDatatubeInstId(datatubeInstId);
	        ditRecord.setGmtCreate(new Date());
	        ditRecord.setGmtModified(new Date());
	        ditRecord.setIsDeleted(0L);
	        ditRecord.setModifyEmpid(operateEmpid);
	        ditRecord.setTaskName(jobName);
	        ditRecord.setTaskScript(sql);
	        ditRecord.setTaskType("blink_stream");
	        ditRecord.setTenantId(tenantId);
	        ditRecord.setVersion(versionId.intValue());
	        datatubeInstanceTaskMapper.insert(ditRecord);
		} catch(Exception e) {
			log.error("processComponentArrayObjectBlinkIncrCheckJob failed, error={}", e.getMessage(), e);
		}
        return true;
    }
    
	public boolean processIncrCheckJob(String tenantId, String appName, String jobName, String dbName, String tableName, RelatedDataObject object, String operateEmpid, Long versionId, JSONObject kafkaJson, List<JSONObject> drcSourceTopicInfos, Settings modelSettings, ViewModel dataModel, Long datatubeInstId, String etlDbName) {
		try {
			Long dstDsId = dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName);
	    	Long appId = getAppIdByName(tenantId, appName);
			String checkResultTopicName = "chk-" + appId + "-" + dstDsId + "-" + object.getCode();
	    	String consumerId = "GID-" + appId + "-" + dstDsId + "-incr_check-" + object.getCode() + "-" + versionId;
	    	boolean res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, drcSourceTopicInfos.get(0).getString("dbName"), consumerId);
			if (!res) {
				log.error("consumer:{} create is failed", consumerId);
			}
	    	res = kafkaManagementService.createTopic(tenantId, appName, checkResultTopicName);
			if (!res) {
				log.error("topic:{} create is failed", checkResultTopicName);
			}
	    	String correctTopicName = "crt-" + appId + "-" + dstDsId + "-" + object.getCode();
	    	res = kafkaManagementService.createTopic(tenantId, appName, correctTopicName);
			if (!res) {
				log.error("topic:{} create is failed", correctTopicName);
			}
			String sql = null;
			String componentSql = null;
			String fkFieldName = null;
			String fkFieldOriginName = null;
			String fkFieldType = null;
			
			List<String> compFieldsDef = new ArrayList<>();
			List<String> compFieldsSel = new ArrayList<>();
			List<String> compFieldsSelOrig = new ArrayList<>();
			List<String> compFieldsJson = new ArrayList<>();
			List<String> colNameWithAliasBList = new ArrayList<>();
			List<String> colNameWithAliasCList = new ArrayList<>();
			for (ViewModel.Field field : object.getFields()) {
				if (field.isPk()) {
					fkFieldOriginName = field.getRef();
					continue;
				}
				compFieldsDef.add(field.getCode() + " " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
				compFieldsSel.add(field.getCode());
				compFieldsSelOrig.add(field.getRef());
				compFieldsJson.add("JSON_VALUE(b.x, '$." + field.getRef() + "') AS " + field.getCode());
	            colNameWithAliasBList.add("JSON_VALUE(b.x, '$." + field.getRef() + "')");
	            colNameWithAliasCList.add("JSON_VALUE(c.x, '$." + field.getCode() + "')");
			}
			if (CollectionUtils.isNotEmpty(object.getRelations())) {
				fkFieldName = object.getRelations().get(0).getRelatedField().split("\\.")[1];
				for (ViewModel.Field field : dataModel.getObject().getFields()) {
					if (field.getCode().equalsIgnoreCase(fkFieldName)) {
						fkFieldType = field.getType();
						break;
					}
				}
			}
			
			ExtensionExample example = new ExtensionExample();
			example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + getDbType(tenantId, dbName)).andPluginEqualTo(object.getRef());
			List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
			if (CollectionUtils.isNotEmpty(exts)) {
				componentSql = "select " + fkFieldOriginName + "," + StringUtils.join(compFieldsSelOrig, ",") + " from (" + exts.get(0).getScript().replace("'", "''") + ") as _t0_ where " + fkFieldOriginName + "=?";
			}
			if (StringUtils.isBlank(componentSql)) {
				log.error("no component conf found, objectCode:{}", object.getRef());
				throw new QanatBizException(object.getRef() + ":no component conf found");
			}

			List<String> drcSourceTopics = drcSourceTopicInfos.stream().map(e -> e.getString("topicName")).collect(Collectors.toList());
			String dsSourceSql =
						"create table mq_source (\n" + 
						"    msg varchar,\n" + 
						"    __traceId__ varchar header\n" + 
						") with (\n" + 
						"  type = 'custom',\n" + 
						"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
						"  topicPattern = '" + StringUtils.join(drcSourceTopics, "|") + "',\n" + 
						"  `group.id` = '" + consumerId + "',\n" + 
						"  `dbName` = '" + drcSourceTopicInfos.get(0).getString("dbName") + "',\n" +
						"  startupMode = 'TIMESTAMP',\n" +
						"  fieldDelimiter = '`'\n" +
						");\n" + 
						"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
						"create view v_id as\n" + 
						"select \n" + 
						("varchar".equalsIgnoreCase(fkFieldType) ? ("    COALESCE(JSON_VALUE(t.a, '$." + fkFieldOriginName + "'), JSON_VALUE(t.a, '$." + fkFieldOriginName + "_old')) as id,\n"):("    CAST(COALESCE(JSON_VALUE(t.a, '$." + fkFieldOriginName + "'), JSON_VALUE(t.a, '$." + fkFieldOriginName + "_old')) as " + fkFieldType + ") as id,\n")) + 
						"    __traceId__\n" + 
						"from mq_source, LATERAL TABLE(parseDrcFields(msg, '" + fkFieldOriginName + "')) as t (a)\n" + 
						"where COALESCE(JSON_VALUE(t.a, '$." + fkFieldOriginName + "'), JSON_VALUE(t.a, '$." + fkFieldOriginName + "_old')) is not null\n" +
						";\n";
			

	        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	        sql = String.format(BLINK_INCR_CHECK_COMPONENT_OBJECT_RELATE_TABLE_SQL
	            , operateEmpid
	            , sdf.format(new Date())
	            , "incr check for " + tableName + " from " + object.getCode()
	            , dsSourceSql
	            , checkResultTopicName
	            , kafkaJson.getString("dbName")
	            , modelSettings.getIncrCheckDelayMs()
	            , StringUtils.join(compFieldsSel, ",")
	            , StringUtils.join(colNameWithAliasBList, ",")
	            , StringUtils.join(colNameWithAliasCList, ",")
	            , exts.get(0).getDbName()
	            , componentSql
	            , dbName
	            , StringUtils.join(compFieldsSel, ",")
	            , tableName
	            , fkFieldName
	            , tableName + "." + object.getCode()
	            , tableName + "." + object.getCode()
	            , correctTopicName
	            , kafkaJson.getString("dbName")
	            , fullLinkProcessor.getFullLinkSinkWithClause(tenantId, appName)
	            , checkResultTopicName
	            );
			
	        
	        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
	        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_KAFKA010, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_UDF, ResourcePackage.BLINK_ADB3), false);
	        
	        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
	        ditRecord.setCreateEmpid(operateEmpid);
	        ditRecord.setDatatubeInstId(datatubeInstId);
	        ditRecord.setGmtCreate(new Date());
	        ditRecord.setGmtModified(new Date());
	        ditRecord.setIsDeleted(0L);
	        ditRecord.setModifyEmpid(operateEmpid);
	        ditRecord.setTaskName(jobName);
	        ditRecord.setTaskScript(sql);
	        ditRecord.setTaskType("blink_stream");
	        ditRecord.setTenantId(tenantId);
	        ditRecord.setVersion(versionId.intValue());
	        datatubeInstanceTaskMapper.insert(ditRecord);
		} catch(Exception e) {
			log.error("processComponentArrayObjectBlinkIncrCheckJob failed, error={}", e.getMessage(), e);
		}
        return true;
    }
	
	private Long getAppIdByName(String tenantId, String appName) {
		AppInfoExample example = new AppInfoExample();
		example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
		List<AppInfo> apps = appInfoMapper.selectByExample(example);
		return apps.get(0).getId();
	}
	
	private String getObjectPk(String tenantId, String objectType, String objectUniqueCode) {
		return dsInfoService.getPkFieldByObjectType(tenantId, objectType, objectUniqueCode);
	}

	public List<JSONObject> getTopicsByComponentObject(String tenantId, DataObject object, String relType, String dstDbType) {
    	List<JSONObject> topics = new ArrayList<>();
    	ExtensionExample example = new ExtensionExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + dstDbType).andPluginEqualTo(object.getRef());
		List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
		
		if (CollectionUtils.isEmpty(exts)) {
			return topics;
		}
		
		ComponentDsRelationExample comDsRelExample = new ComponentDsRelationExample();
		comDsRelExample.createCriteria().andTenantIdEqualTo(tenantId).andComponentNameEqualTo(exts.get(0).getCode()).andRelationTypeEqualTo(relType);
		List<ComponentDsRelation> comRsRels = componentDsRelationMapper.selectByExample(comDsRelExample);
		if (CollectionUtils.isEmpty(comRsRels)) {
			log.error("no component ds found, componentName:{} relType:{}", exts.get(0).getCode(), relType);
			return topics;
		}
		List<String> dsNames = comRsRels.stream().map(e->e.getDsName()).collect(Collectors.toList());
		
		DatasourceExample dsExample = new DatasourceExample();
		dsExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsNameIn(dsNames);
		List<Datasource> dsList = dsInfoMapper.selectByExample(dsExample);
		if (CollectionUtils.isEmpty(dsList) || dsList.size() != comRsRels.size()) {
			log.error("[{}] no ds found or ds conf missing", StringUtils.join(dsNames, ","));
			throw new QanatBizException("[" + StringUtils.join(dsNames, ",") + "] no ds found or ds conf missing");
		}
		for (Datasource dsInfo : dsList) {
			JSONObject incrConfJson = new JSONObject();
			incrConfJson.put("type", dsInfo.getDsType());
			incrConfJson.put("topicName", dsInfo.getTableName());
			incrConfJson.put("dbName", dsInfo.getDbName());
			DbInfo dbInfo = getDbInfoByName(dsInfo.getTenantId(),dsInfo.getDbName());
			if (JSON.parseObject(dbInfo.getMeta()) != null && StringUtils.isNotBlank(JSON.parseObject(dbInfo.getMeta()).getString("dbName"))) {
				incrConfJson.put("dbName", JSON.parseObject(dbInfo.getMeta()).getString("dbName"));
			}
			topics.add(incrConfJson);
		}
		return topics;
    }

    private DbInfo getDbInfoByName(String tenantId, String dbName) {
        DbInfoExample dbExample = new DbInfoExample();
        dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName).andTenantIdEqualTo(tenantId);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("db:" + dbName + " not found");
        }
        DbInfo dbInfo = dbs.get(0);
        return dbInfo;
    }
}
