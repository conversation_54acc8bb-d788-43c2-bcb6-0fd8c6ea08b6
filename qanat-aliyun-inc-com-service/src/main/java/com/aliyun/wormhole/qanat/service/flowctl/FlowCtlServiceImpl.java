package com.aliyun.wormhole.qanat.service.flowctl;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.aliyun.wormhole.qanat.api.service.DiamondService;
import com.aliyun.wormhole.qanat.api.service.FlowCtlService;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfo;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelTaskRelation;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelTaskRelationExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelTaskRelationMapper;
import com.taobao.ateye.util.reflect.StringUtils;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * Dimaond服务service
 * <AUTHOR>
 * 2019年7月23日
 */
@Slf4j
@Component
@HSFProvider(serviceInterface = FlowCtlService.class)
public class FlowCtlServiceImpl implements FlowCtlService {
	
	@Resource
	private DiamondService diamondService;
	
	@Resource
	private ViewModelInfoMapper viewModelInfoMapper;
	
	@Resource
	private ViewModelTaskRelationMapper viewModelTaskRelationMapper;
	
	@Resource
	private DatatubeInstanceMapper datatubeInstanceMapper;
	
	@Override
	public Boolean setFlowLimit(String gid, Double limit) {
		try {
			boolean result = diamondService.publishConfigPre(gid + "-flow", "DATATUBE-FLOW", "qanat-aliyun-inc-com", "{\"contentType\":\"drc\",\"rules\":[{\"name\":\"" + gid + "__" + limit + "\",\"filter\":{},\"limit\":" + limit + "}]}");
			log.info("setFlowLimit({},{}) finished, result={}", gid, limit, result);
			return result;
		} catch(Exception e) {
			log.error("setFlowLimit({},{}) failed, error={}", gid, limit, e.getMessage());
			return false;
		}
	}
	
	@Override
	public Double getFlowLimitV1(String gid) {
		try {
			String config = diamondService.getConfigPre(gid + "-flow", "DATATUBE-FLOW");
			return StringUtils.isBlank(config) ? null : JSON.parseObject(config).getJSONArray("rules").getJSONObject(0).getDoubleValue("limit");
		} catch(Exception e) {
			log.error("getFlowLimit({}) failed, error={}", gid, e.getMessage(), e);
			return null;
		}
	}
	
	@Override
	public Boolean setFlowLimitIfNotExists(Long datatubeInstId, String gid, Double limit) {
		String[] tokens = gid.split("-");
    	List<String> tokenList = new ArrayList<>();
    	for (int i = 0; i < tokens.length -1; i++) {
    		tokenList.add(tokens[i]);
    	}
    	String limitKeyBase = StringUtils.join(tokenList, "-") + "-";
		try {
			String config = diamondService.getConfigPre(gid + "-flow", "DATATUBE-FLOW");
			if (StringUtils.isBlank(config) || JSON.parseObject(config) == null || JSON.parseObject(config).getJSONArray("rules") == null) {
				DatatubeInstance datatubeInst = datatubeInstanceMapper.selectByPrimaryKey(datatubeInstId);
				
				ViewModelInfo viewModelInfo = viewModelInfoMapper.selectByPrimaryKey(datatubeInst.getProviderId());

				ViewModelTaskRelationExample modelTaskRelExample = new ViewModelTaskRelationExample();
		    	modelTaskRelExample.setOrderByClause("model_version_id desc");
		    	modelTaskRelExample.createCriteria().andTenantIdEqualTo(viewModelInfo.getTenantId()).andIsDeletedEqualTo(0L).andViewModelNameEqualTo(viewModelInfo.getModelName()).andModelVersionIdNotEqualTo(viewModelInfo.getVersionId());
		    	List<ViewModelTaskRelation> modelTaskRels = viewModelTaskRelationMapper.selectByExample(modelTaskRelExample);
		    	if (CollectionUtils.isNotEmpty(modelTaskRels)) {
			    	for (ViewModelTaskRelation modelTaskRel : modelTaskRels) {
			    		config = diamondService.getConfigPre(limitKeyBase + modelTaskRel.getModelVersionId() + "-flow", "DATATUBE-FLOW");
			    		break;
			    	}
		    	}
		    	if (StringUtils.isBlank(config) || JSON.parseObject(config) == null || JSON.parseObject(config).getJSONArray("rules") == null) {
					boolean result = diamondService.publishConfigPre(gid + "-flow", "DATATUBE-FLOW", "qanat-aliyun-inc-com", "{\"contentType\":\"drc\",\"rules\":[{\"name\":\"" + gid + "__" + limit + "\",\"filter\":{},\"limit\":" + limit + "}]}");
					log.info("setFlowLimit({},{}) finished, result={}", gid, limit, result);
					return result;
		    	} else {
		    		double oldLimit = JSON.parseObject(config).getJSONArray("rules").getJSONObject(0).getDoubleValue("limit");
		    		boolean result = diamondService.publishConfigPre(gid + "-flow", "DATATUBE-FLOW", "qanat-aliyun-inc-com", "{\"contentType\":\"drc\",\"rules\":[{\"name\":\"" + gid + "__" + oldLimit + "\",\"filter\":{},\"limit\":" + oldLimit + "}]}");
					log.info("setFlowLimit({},{}) from oldVersion finished, result={}", gid, oldLimit, result);
					return result;
		    	}
			} else {
				log.info("{} already has config:{}", gid, config);
			}
		} catch(Exception e) {
			log.error("setFlowLimit({},{}) failed, error={}", gid, limit, e.getMessage());
			return false;
		}
		return true;
	}
	
	@Override
	public Boolean setFlowControlIfNotExists(Long datatubeInstId, String fcid, Double limit) {
		try {
			String config = diamondService.getConfigPre(fcid, "DATATUBE-FLOW-V2");
	    	if (StringUtils.isBlank(config) || JSON.parseObject(config) == null || JSON.parseObject(config).getJSONArray("rules") == null) {
				boolean result = diamondService.publishConfigPre(fcid, "DATATUBE-FLOW-V2", "qanat-aliyun-inc-com", "{\"contentType\":\"drc\",\"rules\":[{\"name\":\"" + fcid + "__" + limit + "\",\"filter\":{},\"limit\":" + limit + "}]}");
				log.info("setFlowLimit({},{}) finished, result={}", fcid, limit, result);
				return result;
	    	} else {
				log.info("{} already has config:{}", fcid, config);
	    	}
		} catch(Exception e) {
			log.error("setFlowLimit({},{}) failed, error={}", fcid, limit, e.getMessage());
			return false;
		}
		return true;
	}
}