package com.aliyun.wormhole.qanat.service.viewmodel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.dto.ResourcePackage;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.FlowCtlService;
import com.aliyun.wormhole.qanat.api.service.KafkaManagementService;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.Extension;
import com.aliyun.wormhole.qanat.dal.domain.ExtensionExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ExtensionMapper;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.DataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.RelatedDataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Relation;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class FunctionObjectProcessor {
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private KafkaManagementService kafkaManagementService;
    
    @Resource
    private ExtensionMapper extensionMapper;
    
    @Resource
    private DatasourceService datasourceService;
    
    @Resource
    private ViewModelSqlBuilder viewModelSqlBuilder;
	
	@Resource
	private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;
	
	@Resource
	private FlowCtlService limiterService;
    
    @Value("${datatube.codegen.version}")
    private String codegenVersion;
	
	public static String BLINK_FUNCTION_BATCH_PLANJSON = "{\n" + 
			"  \"global\": [\n" + 
			"    {\n" + 
			"      \"jobManagerMinCpuCores\": 0.1,\n" + 
			"      \"jobManagerMinMemoryCores\": 1024,\n" + 
			"      \"jobManagerCpuCores\": 0.25,\n" + 
			"      \"jobManagerMemoryInMB\": 1024\n" + 
			"    }\n" + 
			"  ],\n" + 
			"  \"nodes\": [\n" + 
			"    {\n" + 
			"      \"id\": 1,\n" + 
			"      \"uid\": \"1\",\n" + 
			"      \"name\": \"MySQLScanTableSource-dwd_devata_bidding_project_info-Batch\",\n" + 
			"      \"pact\": \"Source\",\n" + 
			"      \"chainingStrategy\": \"HEAD\",\n" + 
			"      \"parallelism\": 1,\n" + 
			"      \"maxParallelism\": 1,\n" + 
			"      \"vcore\": 1,\n" + 
			"      \"heap_memory\": 4096,\n" + 
			"      \"direct_memory\": 0,\n" + 
			"      \"native_memory\": 0,\n" + 
			"      \"state_size\": 0,\n" + 
			"      \"targetRps\": 0\n" + 
			"    },\n" + 
			"    {\n" + 
			"      \"id\": 2,\n" + 
			"      \"uid\": \"2\",\n" + 
			"      \"name\": \"SourceConversion(table:[builtin, default, _DataStreamTable_0, source: [MySQLScanTableSource-dwd_devata_bidding_project_info]], fields:(f0))\",\n" + 
			"      \"pact\": \"Operator\",\n" + 
			"      \"parallelism\": 1,\n" + 
			"      \"maxParallelism\": 32768,\n" + 
			"      \"vcore\": 1,\n" + 
			"      \"heap_memory\": 4096,\n" + 
			"      \"direct_memory\": 0,\n" + 
			"      \"native_memory\": 0,\n" + 
			"      \"state_size\": 0,\n" + 
			"      \"chainingStrategy\": \"ALWAYS\",\n" + 
			"      \"targetRps\": 0\n" + 
			"    },\n" + 
			"    {\n" + 
			"      \"id\": 3,\n" + 
			"      \"uid\": \"3\",\n" + 
			"      \"name\": \"correlate: table(MySQLSourceParser0($cor0.f0)), select: bpid,hyb_project_price,pub_project_price,intel_project_price\",\n" + 
			"      \"pact\": \"Operator\",\n" + 
			"      \"parallelism\": 1,\n" + 
			"      \"maxParallelism\": 32768,\n" + 
			"      \"vcore\": 1,\n" + 
			"      \"heap_memory\": 4096,\n" + 
			"      \"direct_memory\": 0,\n" + 
			"      \"native_memory\": 0,\n" + 
			"      \"state_size\": 0,\n" + 
			"      \"chainingStrategy\": \"ALWAYS\",\n" + 
			"      \"targetRps\": 0\n" + 
			"    },\n" + 
			"    {\n" + 
			"      \"id\": 4,\n" + 
			"      \"uid\": \"4\",\n" + 
			"      \"name\": \"Calc(select: (bpid, CAST(('1' func 'compute_project_price' func hyb_project_price func pub_project_price func intel_project_price)) AS project_price))\",\n" + 
			"      \"pact\": \"Operator\",\n" + 
			"      \"parallelism\": 32,\n" + 
			"      \"maxParallelism\": 32768,\n" + 
			"      \"vcore\": 1,\n" + 
			"      \"heap_memory\": 4096,\n" + 
			"      \"direct_memory\": 0,\n" + 
			"      \"native_memory\": 0\n" + 
			"    },\n" + 
			"    {\n" + 
			"      \"id\": 5,\n" + 
			"      \"uid\": \"5\",\n" + 
			"      \"name\": \"SinkConversion to Tuple2\",\n" + 
			"      \"pact\": \"Operator\",\n" + 
			"      \"parallelism\": 32,\n" + 
			"      \"maxParallelism\": 32768,\n" + 
			"      \"vcore\": 1,\n" + 
			"      \"heap_memory\": 4096,\n" + 
			"      \"direct_memory\": 0,\n" + 
			"      \"native_memory\": 0\n" + 
			"    },\n" + 
			"    {\n" + 
			"      \"id\": 6,\n" + 
			"      \"uid\": \"6\",\n" + 
			"      \"name\": \"TupleOutputFormatAdapterSink:com.alibaba.blink.connectors.adb30.Adb30OutputFormat@28babeca\",\n" + 
			"      \"pact\": \"Sink\",\n" + 
			"      \"parallelism\": 32,\n" + 
			"      \"maxParallelism\": 32768,\n" + 
			"      \"vcore\": 1,\n" + 
			"      \"heap_memory\": 4096,\n" + 
			"      \"direct_memory\": 0,\n" + 
			"      \"native_memory\": 0\n" + 
			"    }\n" + 
			"  ],\n" + 
			"  \"links\": [\n" + 
			"    {\n" + 
			"      \"source\": 1,\n" + 
			"      \"target\": 2\n" + 
			"    },\n" + 
			"    {\n" + 
			"      \"source\": 2,\n" + 
			"      \"target\": 3\n" + 
			"    },\n" + 
			"    {\n" + 
			"      \"source\": 3,\n" + 
			"      \"target\": 4\n" + 
			"    },\n" + 
			"    {\n" + 
			"      \"source\": 4,\n" + 
			"      \"target\": 5\n" + 
			"    },\n" + 
			"    {\n" + 
			"      \"source\": 5,\n" + 
			"      \"target\": 6\n" + 
			"    }\n" + 
			"  ]\n" + 
			"}";

    public boolean processIncrSyncJob(String tenantId, Long appId, String appName, String jobName, ViewModel dataModel, List<String> dbNames, String etlDbName, String tableName, Long dstDsId, ViewModel.Field tgtField, Map<String, List<ViewModel.Field>> objFieldMap, String operateEmpid, Long versionId, JSONObject kafkaJson, String pkField, List<ViewModel.Field> funcFields, String funcCode, Long datatubeInstId) {
    	ExtensionExample example = new ExtensionExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andCodeEqualTo(funcCode);
		List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
		
		if (CollectionUtils.isEmpty(exts)) {
			log.error("funcCode:{} not found", funcCode);
			return false;
		}
		String funcUdfClass = null;
		if ("groovy".equalsIgnoreCase(exts.get(0).getPlugin())) {
			funcUdfClass = "com.aliyun.wormhole.qanat.blink.udf.QanatFunctionUdf";
		} else if ("dfaas".equalsIgnoreCase(exts.get(0).getPlugin())) {
			funcUdfClass = "com.aliyun.wormhole.qanat.blink.udf.QanatDfaasFunctionUdf";
		} else {
			log.error("funcCode:{} with plugin:{} not supported", funcCode, exts.get(0).getPlugin());
			return false;
		}
    	String logTopicNameNew = "stream-" + appId + "-" + dstDsId + "-" + tgtField.getCode();
    	boolean res = kafkaManagementService.createTopic(tenantId, appName, logTopicNameNew);
		if (!res) {
			log.error("topic:{} create is failed", logTopicNameNew);
		}
		String pkFieldType = dataModel.getObject().getFields().stream().filter(e->e.isPk()).map(e->e.getType()).collect(Collectors.toList()).get(0);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String funcDefSql = "--SQL\n" + 
    			"--********************************************************************--\n" + 
    			"--Author: " + operateEmpid + "\n" + 
    			"--CreateTime: " + sdf.format(new Date()) + "\n" + 
    			"--Comment: " + ("sync for " + tableName + " from " + dataModel.getObject().getCode()) + " lookup\n" + 
    			"--Version: " + codegenVersion + "\n" + 
    			"--********************************************************************--\n" + 
				"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\r\n" + 
				"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\r\n" + 
				"CREATE FUNCTION func AS '" + funcUdfClass + "';\r\n";

		String sinkDsSql = "";
		
		List<String> objSqls = new ArrayList<>();
		int sinkRepeat = 0;
		for (String objectCode : objFieldMap.keySet()) {
			DataObject object = null;
			if (objectCode.equalsIgnoreCase(dataModel.getObject().getCode())) {
				object = dataModel.getObject();
			} else {
				object = dataModel.getRelatedObjects().stream().filter(e->e.getCode().equalsIgnoreCase(objectCode)).collect(Collectors.toList()).get(0);
			}
        	JSONObject srcDsMetaJson = datasourceService.getTableMetaByDsName(tenantId, object.getRef());
	    	String consumerId = "GID-" + appId + "-" + dstDsId + "-incr_sync-" + object.getCode() + "_" + tgtField.getCode() + "-" + versionId;
	    	res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, srcDsMetaJson.getJSONObject("incrConf").getString("dbName"), consumerId);
			if (!res) {
				log.error("consumer:{} create is failed", consumerId);
			}
			limiterService.setFlowLimitIfNotExists(datatubeInstId, consumerId, 1.0);
        	String drcTopicName = srcDsMetaJson.getJSONObject("incrConf").getString("topicName");
			String dsIn = "create table " + object.getCode() + "_mq_source (\n" + 
					"    msg varchar,\n" + 
					"    __traceId__ varchar header\n" + 
					") with (\n" + 
					"  type = 'custom',\n" + 
					"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
					"  topic = '" + drcTopicName + "',\n" + 
					"  `group.id` = '" + consumerId + "',\n" + 
					"  `dbName` = '" + srcDsMetaJson.getJSONObject("incrConf").getString("dbName") + "',\n" +
					"  startupMode = 'TIMESTAMP',\n" +
					"  fieldDelimiter = '`'\n" +
					");\n";

			String objRefField = null;
			String relatedMainObjKeyField = null;
			String relatedMainObjKeyFieldType = "bigint";
			if (object instanceof RelatedDataObject) {
				for (Relation rel : ((RelatedDataObject)object).getRelations()) {
					if (!rel.getRelatedField().startsWith("exp#")) {
						objRefField = object.getFields().stream().filter(e->e.getCode().equalsIgnoreCase(rel.getField())).collect(Collectors.toList()).get(0).getRef();
		        		relatedMainObjKeyField = getMainObjectKeyField(dataModel, rel.getRelatedField().split("\\.")[0], rel.getRelatedField().split("\\.")[1]);
		        		for (ViewModel.Field field : dataModel.getObject().getFields()) {
							if (field.getCode().equalsIgnoreCase(relatedMainObjKeyField)) {
								relatedMainObjKeyFieldType = field.getType();
								break;
							}
						}
			        	break;
		        	}
				}
			} else {
				objRefField = object.getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0).getRef();
				relatedMainObjKeyField = object.getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0).getCode();
				relatedMainObjKeyFieldType = object.getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0).getType();
			}
			if (sinkRepeat == 0) {
				for (int i = 0; i < dbNames.size(); i++) {
					sinkDsSql += "create table adb_sink_" + i + " (\r\n" + 
						"    " + relatedMainObjKeyField + " " + pkFieldType + ",\r\n" + 
						"    " + tgtField.getCode() + " " + tgtField.getType() + ",\r\n" + 
						"    __trace_id__ varchar,\r\n" + 
						"    primary key(" + relatedMainObjKeyField + ")\r\n" + 
						") with (\r\n" + 
						"    type = 'QANAT_ADB30',\r\n" + 
						"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\r\n" + 
						"    dbName='" + dbNames.get(i) + "',\r\n" + 
						"    tableName='" + tableName + "',\r\n" + 
						"    replaceMode = " + (relatedMainObjKeyField.equalsIgnoreCase(pkField) ? "'update'" : "'update_by_query'") + ",\r\n" + 
						"    writeMode = 'single',\r\n";
					if (dbNames.get(i).equalsIgnoreCase(etlDbName)) {
						sinkDsSql += "    streamType = 'kafka',\r\n" + 
						"    eventTopic = '" + logTopicNameNew + "',\r\n" + 
						"    eventServer = '" + kafkaJson.getString("dbName") + "'\r\n";
					} else {
						sinkDsSql += "    streamEvent = 'disable'\r\n";
					}
					sinkDsSql += ");\n" +
					"\n";
				}
			}
			
			String dsInView = "create view v_" + object.getCode() + " as\r\n" + 
					"select \r\n" + 
					"    CAST((case when JSON_VALUE(b.x, '$." + objRefField + "') is null then JSON_VALUE(b.x, '$." + objRefField + "_old') else JSON_VALUE(b.x, '$." + objRefField + "') end) AS " + relatedMainObjKeyFieldType + ") as " + relatedMainObjKeyField + ",\r\n";
			List<String> objFuncFields = objFieldMap.get(objectCode).stream().map(e->e.getCode()).collect(Collectors.toList());
			for (ViewModel.Field field : objFieldMap.get(objectCode)) {
				if ("varchar".equalsIgnoreCase(field.getType())) {
					dsInView += "   (case when JSON_VALUE(b.x, '$." + field.getRef() + "') is null then JSON_VALUE(b.x, '$." + field.getRef() + "_old') else JSON_VALUE(b.x, '$." + field.getRef() + "') end) as " + field.getCode() + ",\r\n";
				} else {
					dsInView += "    CAST((case when JSON_VALUE(b.x, '$." + field.getRef() + "') is null then JSON_VALUE(b.x, '$." + field.getRef() + "_old') else JSON_VALUE(b.x, '$." + field.getRef() + "') end) AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") as " + field.getCode() + ",\r\n";
				}
			}
			dsInView += "    a.__traceId__\r\n" + 
					"from  " + object.getCode() + "_mq_source as a, LATERAL TABLE(parseDrcFields(a.msg, '" + objRefField + "','" + StringUtils.join(objFieldMap.get(objectCode).stream().map(e->e.getRef()).collect(Collectors.toList()), "','") + "')) as b(x)\r\n";
					List<String> newOldCompares = new ArrayList<>();
					for (ViewModel.Field field : objFieldMap.get(objectCode)) {
						newOldCompares.add("COALESCE(JSON_VALUE(b.x, '$." + field.getRef() + "'),'')<>COALESCE(JSON_VALUE(b.x, '$." + field.getRef() + "_old'),'')");
					}
			dsInView += (" where " + StringUtils.join(newOldCompares, " or "));
			dsInView += ";\r\n";

			List<String> funcFieldNames = funcFields.stream().filter(e->(!objFuncFields.contains(e.getCode()) && !(StringUtils.isNotBlank(e.getRef()) && e.getRef().startsWith("'") && e.getRef().endsWith("'")))).map(e->e.getCode()).collect(Collectors.toList());
			String fetchSql = null;
			if (CollectionUtils.isNotEmpty(funcFieldNames)) {
				if (dataModel.getSettings().isLookupOptimize()) {
					fetchSql = "select " + pkField + "," + StringUtils.join(funcFieldNames, ",") + " from " + tableName + " as t where " + relatedMainObjKeyField + "=?";
				} else {
					fetchSql = "select " + pkField + "," + StringUtils.join(funcFieldNames, ",") + " from (" + viewModelSqlBuilder.getSelectSql(tenantId, dataModel, null).replace("'", "''") + ") as t where " + relatedMainObjKeyField + "=?";
				}
			}
			
			List<String> insertSelectFields = new ArrayList<>();
			for (int i=0; i< funcFields.size(); i++) {
				ViewModel.Field field = funcFields.get(i);
				if (objFuncFields.contains(field.getCode())) {
					insertSelectFields.add("a." + field.getCode());
				} else if (StringUtils.isNotBlank(field.getRef()) && field.getRef().startsWith("'") && field.getRef().endsWith("'")) {
					insertSelectFields.add(field.getRef());
				} else {
					insertSelectFields.add("CAST(JSON_VALUE(b.x, '$." + field.getCode() + "')  as " + field.getType() + ")");
				}
			}
			
			String sinkSql = "create view v_join_" + object.getCode() + " as \r\n" + 
					"select \r\n" + 
					"    CAST(" + (StringUtils.isBlank(fetchSql) ? ("a." + relatedMainObjKeyField) : "JSON_VALUE(b.x, '$." + pkField + "')") + "  as " + pkFieldType + ") as " + relatedMainObjKeyField + ",\r\n" + 
					("varchar".equalsIgnoreCase(tgtField.getType()) ? ("    func('" + tenantId + "', '" + funcCode + "', " + StringUtils.join(insertSelectFields, ",")  + ") AS " + tgtField.getCode()) : ("    CAST(func('" + tenantId + "', '" + funcCode + "', " + StringUtils.join(insertSelectFields, ",") + ") as " + tgtField.getType() + ") AS " + tgtField.getCode())) + ",\r\n" +
					"    a.__traceId__\r\n" + 
					"from  v_" + object.getCode() + " as a " + (StringUtils.isBlank(fetchSql) ? "" : ", LATERAL TABLE (queryDim('" + etlDbName + "', '" + fetchSql + "', a." + relatedMainObjKeyField + ")) as b(x)\r\n") + 
					";\r\n";
			for (int i = 0; i < dbNames.size(); i++) {
				sinkSql += "insert into adb_sink_" + i + " select * from v_join_" + object.getCode() + ";\n";
			}
			objSqls.add(dsIn + "\r\n" + dsInView + "\r\n" + sinkSql);
			sinkRepeat++;
		}
        String sql = funcDefSql + "\r\n" + sinkDsSql + "\r\n" + StringUtils.join(objSqls, "\r\n");
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDF, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_KAFKA010, ResourcePackage.BLINK_HSF_UDF), 
        		false);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        return true;
    }

    public boolean processBatchSyncJob(String tenantId, Long appId, String appName, String jobName, ViewModel dataModel, String etlDbName, String tableName, String finalTableName, Long dstDsId, Set<ViewModel.Field> tgtFields, Map<ViewModel.Field, Map<String, List<ViewModel.Field>>> objFieldMap, String operateEmpid, Long versionId, JSONObject kafkaJson, String pkField, Map<ViewModel.Field, List<ViewModel.Field>> funcFieldsMap,  Map<ViewModel.Field, String> funcCodeMap, Long datatubeInstId) {
    	Map<ViewModel.Field, String> funcNameMap = new HashMap<>();
    	for (ViewModel.Field _field : funcCodeMap.keySet()) {
    		String funcCode = funcCodeMap.get(_field);
	    	ExtensionExample example = new ExtensionExample();
			example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andCodeEqualTo(funcCode);
			List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
			
			if (CollectionUtils.isEmpty(exts)) {
				log.error("funcCode:{} not found", funcCode);
				return false;
			}
			String funcUdfName = null;
			if ("groovy".equalsIgnoreCase(exts.get(0).getPlugin())) {
				funcUdfName = "localFunc";
			} else if ("dfaas".equalsIgnoreCase(exts.get(0).getPlugin())) {
				funcUdfName = "rpcFunc";
			} else {
				log.error("funcCode:{} with plugin:{} not supported", funcCode, exts.get(0).getPlugin());
				return false;
			}
			funcNameMap.put(_field, funcUdfName);
    	}
		Set<String> colDefs = new HashSet<>();
		Map<ViewModel.Field, List<String>> colsMap = new HashMap<>();
		for (ViewModel.Field _field : funcFieldsMap.keySet()) {
			List<ViewModel.Field> funcFields = funcFieldsMap.get(_field);
    		List<String> cols = new ArrayList<>();
			for (int i=0; i< funcFields.size(); i++) {
				ViewModel.Field field = funcFields.get(i);
				if (StringUtils.isNotBlank(field.getRef()) && field.getRef().startsWith("'") && field.getRef().endsWith("'")) {
					cols.add(field.getRef());
				} else {
					colDefs.add("`" + field.getCode() + "` " + (field.getType().equalsIgnoreCase("datetime")?"timestamp":field.getType()));
					cols.add(field.getCode());
				}
			}
			colsMap.put(_field, cols);
		}
		List<String> tgtFieldDefs = new ArrayList<>();
		List<String> tgtFieldFuncExps = new ArrayList<>();
		for (ViewModel.Field tgtField : tgtFields) {
			tgtFieldDefs.add(tgtField.getCode() + " " + tgtField.getType());
			tgtFieldFuncExps.add(("varchar".equalsIgnoreCase(tgtField.getType()) ? ("    " + funcNameMap.get(tgtField) + "('" + tenantId + "', '" + funcCodeMap.get(tgtField) + "', " + StringUtils.join(colsMap.get(tgtField), ",")  + ") AS " + tgtField.getCode()) : ("    CAST(" + funcNameMap.get(tgtField) + "('" + tenantId + "', '" + funcCodeMap.get(tgtField) + "', " + StringUtils.join(colsMap.get(tgtField), ",") + ") as " + tgtField.getType() + ") AS " + tgtField.getCode())) + "\r\n");
		}
		String pkFieldType = dataModel.getObject().getFields().stream().filter(e->e.isPk()).map(e->e.getType()).collect(Collectors.toList()).get(0);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String sourceDsSql =  "--SQL\n" + 
    			"--********************************************************************--\n" + 
    			"--Author: " + operateEmpid + "\n" + 
    			"--CreateTime: " + sdf.format(new Date()) + "\n" + 
    			"--Comment: " + ("sync for " + tableName + " from " + dataModel.getObject().getCode()) + " lookup\n" + 
    			"--Version: " + codegenVersion + "\n" + 
    			"--********************************************************************--\n" + 
				"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\r\n" + 
				"CREATE FUNCTION localFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatFunctionUdf';\r\n" +
				"CREATE FUNCTION rpcFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDfaasFunctionUdf';\r\n" +
				"CREATE TABLE adb_source (\r\n" + 
				"    " + pkField + " " + pkFieldType + ",\r\n" + 
				"    " + StringUtils.join(colDefs, ",") + ",\r\n" +
				"    primary key(" + pkField + ")\r\n" + 
				") WITH (\r\n" + 
				"    type = 'custom',\r\n" + 
				"    tableFactoryClass = 'com.aliyun.wormhole.qanat.blink.mysql.scan.MySQLScanTableFactory',\r\n" + 
				"    dbName='" + etlDbName + "',\r\n" + 
				"    tableName='" + tableName + "',\r\n" + 
				"    batchSize='" + dataModel.getSettings().getRdsScanBatchSize() + "'\r\n" + 
				");\n" +
				"\n" + 
				"create view v_func as\r\n" + 
				"select \r\n" + 
				"    " + pkField + ",\r\n" + 
				StringUtils.join(tgtFieldFuncExps, ",") +
				"from  adb_source\r\n" + 
				";\r\n";
		
		String sinkSql = "create table adb_sink (\r\n" + 
					"    " + pkField + " " + pkFieldType + ",\r\n" + 
					"    " + StringUtils.join(tgtFieldDefs, ",") + ",\r\n" + 
					"    primary key(" + pkField + ")\r\n" + 
					") with (\r\n" + 
					"    type = 'QANAT_ADB30',\r\n" + 
					"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\r\n" + 
					"    dbName='" + etlDbName + "',\r\n" + 
					"    tableName='" + tableName + "',\r\n" + 
					"    replaceMode = 'update',\r\n" + 
					"    writeMode = 'batch',\r\n" + 
					"    streamEvent = 'disable'\r\n" + 
					");\n" +
					"\n" +
					"insert into adb_sink select * from v_func;" +
					"\n";
		
        String sql = sourceDsSql + "\r\n" + sinkSql;
        String planJson = BLINK_FUNCTION_BATCH_PLANJSON;
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + finalTableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDF, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_MYSQL_SCAN, ResourcePackage.BLINK_HSF_UDF), 
        		true, planJson);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_batch");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        return true;
    }

	private String getMainObjectKeyField(ViewModel dataModel, String objCode, String objField) {
		if (dataModel.getObject().getCode().equalsIgnoreCase(objCode)) {
			return objField;
		} else {
			RelatedDataObject relObject = dataModel.getRelatedObjects().stream().filter(e->e.getCode().equalsIgnoreCase(objCode)).collect(Collectors.toList()).get(0);
			String relObjCode = null;
    		String relObjKeyField = null;
			for (Relation rel : relObject.getRelations()) {
				if (!rel.getRelatedField().startsWith("exp#")) {
					if (rel.getField().equalsIgnoreCase(objField)) {
						relObjCode = rel.getRelatedField().split("\\.")[0];
						relObjKeyField = rel.getRelatedField().split("\\.")[1];
					} else {
						return objField;
					}
		        	break;
	        	}
			}
			return getMainObjectKeyField(dataModel, relObjCode, relObjKeyField);
		}
	}
}
