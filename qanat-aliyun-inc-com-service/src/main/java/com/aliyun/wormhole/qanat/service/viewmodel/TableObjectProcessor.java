package com.aliyun.wormhole.qanat.service.viewmodel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.DagPolicy;
import com.aliyun.wormhole.qanat.api.dto.ResourcePackage;
import com.aliyun.wormhole.qanat.api.dto.TaskInfoRequest;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.FlowCtlService;
import com.aliyun.wormhole.qanat.api.service.KafkaManagementService;
import com.aliyun.wormhole.qanat.api.service.TaskService;
import com.aliyun.wormhole.qanat.dal.domain.AppInfo;
import com.aliyun.wormhole.qanat.dal.domain.AppInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.Extension;
import com.aliyun.wormhole.qanat.dal.domain.ExtensionExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelTaskRelation;
import com.aliyun.wormhole.qanat.dal.mapper.AppInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsFieldInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ExtensionMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelTaskRelationMapper;
import com.aliyun.wormhole.qanat.service.template.Adb3SyncTemplate;
import com.aliyun.wormhole.qanat.service.util.YamlUtil;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.DataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.RelatedDataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Relation;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Settings;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class TableObjectProcessor {
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private KafkaManagementService kafkaManagementService;
    
    @Resource
    private AppInfoMapper appInfoMapper;
    
    @Resource
    private DatasourceService dsInfoService;
    
    @Resource
    private ViewModelSqlBuilder viewModelSqlBuilder;
    
    @Resource
    private TaskService taskService;

    @Resource
    private ExtensionMapper extensionMapper;
    
    @Resource
    private DsFieldInfoMapper dsFieldInfoMapper;
    
    @Resource
    private ViewModelTaskRelationMapper viewModelTaskRelationMapper;
    
    @Resource
    private LookupProcessor lookupProcessor;
    
    @Resource
    private FullLinkProcessor fullLinkProcessor;
    
    @Resource
    private ViewModelOptimizer viewModelOptimizer;
	
	@Resource
	private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;
	
	@Resource
	private FlowCtlService limiterService;
    
    @Value("${datatube.codegen.version}")
    private String codegenVersion;
	
	public static String BLINK_INCR_CHECK_TABLE_OBJECT_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--********************************************************************--\n" + 
			"create table drc_source (\n" + 
			"    msg varchar,\n" + 
			"    __traceId__ varchar header\n" + 
			") with (\n" + 
			"  type = 'custom',\n" + 
			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"  topic = '%s',\n" + 
			"  `group.id` = '%s',\n" + 
			"  `dbName` = '%s',\n" + 
			"  startupMode = 'TIMESTAMP',\n" + 
			"  fieldDelimiter = '`'\n" + 
			");\n" + 
			"\n" + 
			"create table check_result_mq_sink (\n" + 
			"    msg varchar,\n" + 
			"    id varchar,\n" + 
			"    __traceId__ varchar,\n" + 
			"    primary key(id)\n" + 
			") with (\n" + 
			"    type='QANAT_KAFKA010',\n" + 
			"    class='com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"    topic='%s',\n" + 
			"    dbName='%s'\n" + 
			");\n" + 
			"\n" + 
			"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" + 
			"CREATE FUNCTION arrayCompare AS 'com.aliyun.wormhole.qanat.blink.udf.QanatArrayCompareUdf';\n" + 
			"CREATE FUNCTION delayMs AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDelayMsUdf';\n" + 
			"\n" + 
			"create view v_drc as\n" + 
			"select \n" + 
			"    (case when JSON_VALUE(t.a, '$.%s') is null then JSON_VALUE(t.a, '$.%s_old') else JSON_VALUE(t.a, '$.%s') end) as id,\n" + 
			"    'drc' as src,\n" + 
			"    __traceId__\n" + 
			"from drc_source, LATERAL TABLE (parseDrcFields (msg, '%s')) as t (a)\n" + 
			";\n" + 
			"\n" + 
			"create view v_delay as\n" + 
			"select\n" + 
			"    id,\n" + 
			"    delayMs(%s, src) as src,\n" + 
			"    __traceId__\n" + 
			"from v_drc\n" + 
			";\n" + 
			"\n" + 
			"create view v_check AS\n" + 
			"select distinct\n" + 
			"    a.id,\n" + 
			"    JSON_VALUE (b.x, '$.%s') as l_value,\n" + 
			"    JSON_VALUE (c.x, '$.%s') as r_value,\n" + 
			"    a.src,\n" + 
			"    a.__traceId__\n" + 
			"from v_delay as a\n" + 
			"LEFT JOIN LATERAL TABLE (queryDim('%s', '%s', a.id)) as b(x) ON TRUE\n" + 
			"LEFT JOIN LATERAL TABLE (queryDim('%s', 'select %s from %s where %s=?', a.id)) as c(x) ON TRUE\n" + 
			";\n" + 
			"\n" + 
			"insert into check_result_mq_sink\n" + 
			"select \n" + 
			"    (CASE when arrayCompare(l_value, r_value)=false then concat_ws('|', '%s', id, src, 'NG', l_value, r_value) else concat_ws('|', '%s', id, src, 'OK') END) as msg, id, __traceId__\n" + 
			"from v_check\n" + 
			";";
	
	public static String BLINK_CHECK_COMPUTE_FOR_MAIN_OBJECT_SQL = 
			"\n" + 
			"create view v_check as\n" + 
			"select \n" + 
			"    a.id,\n" + 
			"    a.src,\n" + 
			"    checkData('%s',\n" + 
			"    qanatConcat('|', %s),\n" + 
			"    qanatConcat('|', %s)) as msg,\n" + 
			"    JSON_VALUE(c.x, '$.%s') as pk,\n" + 
			"    a.eventType," +
			"    a.__traceId__\n" +
			"from v_check_id as a\n" + 
			"LEFT JOIN LATERAL TABLE (queryDim('%s', 'select %s from %s where %s=?', a.id)) as b(x) ON TRUE\n" + 
			"LEFT JOIN LATERAL TABLE (queryDim('%s', 'select %s from %s where %s=?', a.id)) as c(x) ON TRUE\n" + 
			"%s\n" + 
			";\n" + 
			"\n" + 
			"create view v_check_result as \n" + 
			"select (case when msg='' then concat_ws('|', '%s', id, src, 'OK') else concat_ws('|', '%s', id, src, msg) end) as msg, id as `key`, pk, eventType, __traceId__\n" + 
			"from v_check;\n" +  
			"\n" + 
			"CREATE TABLE full_link_sink (\n" + 
			"  trace_id varchar,\n" + 
			"  pk bigint,\n" +
			"  key varchar,\n" + 
			"  ts bigint,\n" + 
			"  db varchar,\n" + 
			"  msg varchar,\n" +
			"  gmt_create timestamp\n" + 
			") WITH (\n" + 
			"  %s" + 
			");\n" + 
			"\n" + 
			"insert into full_link_sink\n" + 
			"select\n" + 
			"  __traceId__ as trace_id,\n" + 
			"  cast(pk as bigint) as pk,\n" +
			"  split_index (msg, '|', 1) as key,\n" + 
			"  NOW() as ts,\n" + 
			"  '%s' as db,\n" + 
			"  msg,\n" + 
			"  CURRENT_TIMESTAMP as gmt_create\n" + 
			"from v_check_result;\n"
			;
	
	public static String BLINK_CHECK_COMPUTE_FOR_FULL_COLUMN_SQL = 
			"\n" + 
			"create view v_check as\n" + 
			"select \n" + 
			"    a.%s as id,\n" + 
			"    'fcbatch' as src,\n" + 
			"    checkData('%s',\n" + 
			"    qanatConcat('|', %s),\n" + 
			"    qanatConcat('|', %s)) as msg\n" + 
			"from v_correct as a\n" + 
			"LEFT JOIN LATERAL TABLE (queryDim('%s', 'select %s from %s where %s=?', a.%s)) as c(x) ON TRUE\n" + 
			";\n" + 
			"\n" + 
			"create view v_check_result as \n" + 
			"select (case when msg='' then concat_ws('|', '%s', id, src, 'OK') else concat_ws('|', '%s', id, src, msg) end) as msg, id as `key`\n" + 
			"from v_check;\n" +  
			"\n" + 
			"CREATE TABLE full_link_sink (\n" + 
			"  trace_id varchar,\n" + 
			"  pk bigint,\n" +
			"  key varchar,\n" + 
			"  ts bigint,\n" + 
			"  db varchar,\n" + 
			"  msg varchar,\n" +
			"  gmt_create timestamp\n" + 
			") WITH (\n" + 
			"  %s" + 
			");\n" + 
			"\n" + 
			"insert into full_link_sink\n" + 
			"select\n" + 
			"  UUID() as trace_id,\n" + 
			"  key as pk,\n" +
			"  split_index (msg, '|', 1) as key,\n" + 
			"  NOW() as ts,\n" + 
			"  '%s' as db,\n" + 
			"  msg,\n" + 
			"  CURRENT_TIMESTAMP as gmt_create\n" + 
			"from v_check_result;\n"
			;
	
	public static String BLINK_CHECK_COMPUTE_FOR_RELATED_OBJECT_SQL = 
			"\n" + 
			"create view v_check as\n" + 
			"select \n" + 
			"    a.id,\n" + 
			"    a.src,\n" + 
			"    checkData('%s',\n" + 
			"    qanatConcat('|', %s),\n" + 
			"    qanatConcat('|', %s)) as msg,\n" + 
			"    JSON_VALUE(c.x, '$.%s') as pk,\n" + 
			"    a.eventType," +
			"    a.__traceId__\n" +
			"from v_check_id as a\n" + 
			",LATERAL TABLE (queryDim('%s', 'select %s from %s where %s=?', a.id)) as b(x)\n" + 
			",LATERAL TABLE (queryDim('%s', 'select %s from %s where %s=?', a.id)) as c(x)\n" + 
			"%s\n" + 
			";\n" + 
			"\n" + 
			"create view v_check_result as \n" + 
			"select (case when msg='' then concat_ws('|', '%s', id, src, 'OK') else concat_ws('|', '%s', id, src, msg) end) as msg, id as `key`, pk, eventType, __traceId__\n" + 
			"from v_check;\n" + 
			"\n" + 
			"CREATE TABLE full_link_sink (\n" + 
			"  trace_id varchar,\n" + 
			"  pk bigint,\n" + 
			"  key varchar,\n" + 
			"  ts bigint,\n" + 
			"  db varchar,\n" + 
			"  msg varchar,\n" +
			"  gmt_create timestamp\n" + 
			") WITH (\n" + 
			"  %s" + 
			");\n" + 
			"\n" + 
			"insert into full_link_sink\n" + 
			"select\n" + 
			"  __traceId__ as trace_id,\n" + 
			"  cast(pk as bigint) as pk,\n" +
			"  split_index (msg, '|', 1) as key,\n" + 
			"  NOW() as ts,\n" + 
			"  '%s' as db,\n" + 
			"  msg,\n" + 
			"  CURRENT_TIMESTAMP as gmt_create\n" + 
			"from v_check_result;\n"
			;
	
	public static String BLINK_BATCH_CHECK_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--********************************************************************--\n" + 
			"CREATE FUNCTION checkData AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDataCheckUdf';\n" + 
			"CREATE FUNCTION qanatConcat AS 'com.aliyun.wormhole.qanat.blink.udf.QanatConcatUdf';\n" + 
			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" +
			"CREATE TABLE check_all_source (\n" + 
			"  %s bigint\n" + 
			"  primary key(%s)\n" +
			") WITH (\n" + 
			"    %s\n" + 
			");\n" + 
			"\n" + 
			"create view v_check_id as\n" + 
			"select \n" + 
			"    cast(%s as varchar) as id,\n" + 
			"    '%s' as src,\n" + 
			"    cast(%s as varchar) as `key`,\n" +
			"    'x' as eventType," +
			"    REGEXP_REPLACE(UUID(),'-','') as __traceId__\n" +
			"from check_all_source;" + 
			"\n" + 
			"%s";
	
	public static String BLINK_FULLCOLUMN_BATCH_CHECK_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--Version: %s\n" + 
			"--********************************************************************--\n" + 
			"CREATE FUNCTION checkData AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDataCheckUdf';\n" + 
			"CREATE FUNCTION qanatConcat AS 'com.aliyun.wormhole.qanat.blink.udf.QanatConcatUdf';\n" + 
			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" +
			"CREATE FUNCTION groovyFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatFunctionUdf';\r\n" +
			"CREATE FUNCTION dfaasFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDfaasFunctionUdf';\r\n" +
			"CREATE TABLE check_all_source (\n" + 
			"  %s,\n" + 
			"  primary key(%s)\n" +
			") WITH (\n" + 
			"    %s\n" + 
			");\n" + 
			"\n" + 
			"create view v_main_obj as\n" + 
			"select \n" + 
			"    %s\n" +
			"from check_all_source;\n" + 
			"\n" + 
			"create view v_correct as\n" + 
			"%s;\n" + 
			"%s";
    
    public boolean processMainObjectIncrSyncJob(String tenantId, String appName, String jobName, JSONObject srcDsMetaJson, List<String> dbNames, String etlDbName, String tableName, DataObject object, String operateEmpid, Long versionId, JSONObject appKafkaJson, JSONObject drcTopicInfo, String datatubeLevel, Long datatubeInstId, ViewModel dataModel) {
    	if (srcDsMetaJson.getJSONObject("incrConf") == null) {
    		log.info("no incr sync conf");
    		return false;
    	}
    	
    	String consumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "-incr_sync-" + object.getCode() + "-" + versionId;
    	boolean res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, drcTopicInfo.getString("dbName"), consumerId);
		if (!res) {
			log.error("consumer:{} create is failed", consumerId);
		}
		limiterService.setFlowLimitIfNotExists(datatubeInstId, consumerId, 1.0);
        int parallel = viewModelOptimizer.getParallel(tenantId, datatubeLevel, srcDsMetaJson.getInteger("qph"));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> columns = new ArrayList<>();
        List<String> selectColumns = new ArrayList<>();
        List<String> columnDefines = new ArrayList<>();
        List<String> drcParseColumns = new ArrayList<>();
        String pkColumn = null;
        for (ViewModel.Field field : object.getFields()) {
        	if (field.getObject() != null) {
        		continue;
        	}
        	if (field.isFunc()) {
        		continue;
        	}
        	if ("null".equalsIgnoreCase(field.getRef())) {
        		continue;
        	}
        	if (field.getRef() != null && field.isCompute()) {
        		List<String> tokens = lookupProcessor.parseFuncExpress(field.getRef());
    			String funcCode = null;
    			String[] cols = null;
        		if (CollectionUtils.isNotEmpty(tokens)) {
        			funcCode = tokens.get(0);
        			cols = tokens.get(1).split(",");
        		} else {
        			funcCode = field.getRef().split("\\(")[0];
        		}
        		if ("json_extract".equalsIgnoreCase(funcCode)) {
        			String funcFieldCode = field.getRef().split("\\(")[1].split(",")[0].trim();
        			String col = field.getRef().replace("json_extract", "JSON_VALUE").replace(funcFieldCode, ("JSON_VALUE(b.x, '$." + funcFieldCode + "')"));
	        		if ("varchar".equalsIgnoreCase(field.getType())) {
		        		drcParseColumns.add(col + " as " + field.getCode());
		        	} else {
		        		drcParseColumns.add("CAST(" + col + " AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") as " + field.getCode());
		        	}
        		} else if (field.getRef().toLowerCase().startsWith("concat_ws")) {
        			String[] colArray = field.getRefFields().split(",");
        			String ref = field.getRef();
        			for (int i = 0; i < colArray.length; i++) {
        				ref = ref.replace(" " + colArray[i].trim() + " ", "JSON_VALUE(b.x, '$." + colArray[i].trim() + "')");
        			}
	        		drcParseColumns.add(ref + " as " + field.getCode());
        		} else if ("from_unixtime".equalsIgnoreCase(funcCode) && "datetime".equalsIgnoreCase(field.getType())) {
        			String funcFieldCode = field.getCode();
        			String param0 = field.getRef().split("\\(")[1].split(",")[0];
        			String param1 = field.getRef().split("\\(")[1].split(",")[1];
        			String ref = "from_unixtime(CAST(" + param0.replace(funcFieldCode, "JSON_VALUE(b.x,'$." + funcFieldCode + "')") + " AS BIGINT)," + param1.replace("%Y", "yyyy").replace("%m", "MM").replace("%d", "dd").replace("%h", "HH").replace("%i", "mm").replace("%s", "ss");
		        	drcParseColumns.add("CAST(" + ref + " AS timestamp) as " + field.getCode());
        		}
	        	selectColumns.add("`" + field.getCode() + "`");
	        	columnDefines.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
        		continue;
        	}
        	if ("'null'".equalsIgnoreCase(field.getRef())) {
        		continue;
        	}
        	if (field.isPk()) {
        		pkColumn = field.getCode();
        	}
        	columns.add("'" + field.getRef() + "'");
        	if (StringUtils.isNotBlank(field.getExpr())) {
        		selectColumns.add(field.getExpr());
        	} else {
        		selectColumns.add("`" + field.getCode() + "`");
        	}
        	columnDefines.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));

        	if (field.isPk()) {
	        	if ("varchar".equalsIgnoreCase(field.getType())) {
	        		drcParseColumns.add("coalesce(JSON_VALUE(b.x, '$." + field.getRef() + "'),JSON_VALUE(b.x, '$." + field.getRef() + "_old')) as " + field.getCode());
	        	} else {
	        		drcParseColumns.add("CAST(coalesce(JSON_VALUE(b.x, '$." + field.getRef() + "'),JSON_VALUE(b.x, '$." + field.getRef() + "_old')) AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") as " + field.getCode());
	        	}
        	} else {
	        	if ("varchar".equalsIgnoreCase(field.getType())) {
	        		drcParseColumns.add("JSON_VALUE(b.x, '$." + field.getRef() + "') as " + field.getCode());
	        	} else {
	        		drcParseColumns.add("CAST(JSON_VALUE(b.x, '$." + field.getRef() + "') AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") as " + field.getCode());
	        	}
        	}
        }
        selectColumns.add("__traceId__");
        String sql = "--SQL\n" + 
    			"--********************************************************************--\n" + 
    			"--Author: " + operateEmpid + "\n" + 
    			"--CreateTime: " + sdf.format(new Date()) + "\n" + 
    			"--Comment: " + ("sync for " + tableName + " from " + object.getCode()) + "\n" + 
    			"--Version: " + codegenVersion + "\n" + 
    			"--********************************************************************--\n" + 
    			"create table drc_mq_source (\n" + 
    			"    msg varchar,\n" + 
    			"    __traceId__ varchar header\n" + 
    			") with (\n" + 
    			"  type = 'custom',\n" + 
    			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
    			"  topic = '" + drcTopicInfo.getString("topicName") + "',\n" + 
    			"  `group.id` = '" + consumerId + "',\n" + 
    			"  `dbName` = '" + drcTopicInfo.getString("dbName") + "',\n" +
    			"  startupMode = 'TIMESTAMP',\n" +
    			"  fieldDelimiter = '`'\n" +
    			");\n" +
    			"\n" + 
    			"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
    			"\n" + 
    			"create view v_drc as \n" + 
    			"select \n" + 
    			"    " + StringUtils.join(drcParseColumns, ",") + ",\n" + 
    			"    CAST(JSON_VALUE (b.x, '$.eventType') as integer) as eventType,\n" + 
    			"    a.__traceId__\n" + 
    			"from\n" + 
    			"    drc_mq_source as a,\n" + 
    			"    LATERAL TABLE (parseDrcFields (a.msg, " + StringUtils.join(columns, ",") + ")) as b(x)\n" + 
    			";\n" +  
    			"\n" ;

    	String eventTopicNameNew = "stream-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "-" + object.getCode();
    	boolean kfkRes = kafkaManagementService.createTopic(tenantId, appName, eventTopicNameNew, parallel);
		if (!kfkRes) {
			log.error("topic:{} create is failed", eventTopicNameNew);
		}
		
		for (int i = 0; i < dbNames.size(); i++) {
			sql += "create table adb_sink_" + i + " (\n" + 
    			"    " + StringUtils.join(columnDefines, ",") + ",\n" + 
    			"    __trace_id__ varchar,\n" + 
    			"    primary key(" + (StringUtils.isNotBlank(dataModel.getSettings().getDistributeKey()) && !pkColumn.equalsIgnoreCase(dataModel.getSettings().getDistributeKey()) ? (pkColumn + "," + dataModel.getSettings().getDistributeKey()) : pkColumn) + ")\n" + 
    			") with (\n" + 
    			"    type = 'QANAT_ADB30',\n" + 
    			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
    			"    dbName='" + dbNames.get(i) + "',\n" + 
    			"    tableName='" + tableName + "',\n" + 
    			"    replaceMode = 'upsert',\n" + 
    			"    writeMode = 'single',\n";
    			if (etlDbName.equalsIgnoreCase(dbNames.get(i))) {
	    			sql += "    streamType = 'kafka',\n" + 
	    			"    eventTopic = '" + eventTopicNameNew + "',\n" + 
	    			"    eventServer = '" + appKafkaJson.getString("dbName") + "'\n";
    			} else {
	    			sql += "    streamEvent = 'disable'\n";
    			}
    			sql += ");\n" + 
    			"\n" +
    			"create table del_adb_sink_" + i + " (\n" + 
    			"    " + pkColumn + " bigint,\n" + 
    			"    __trace_id__ varchar,\n" + 
    			"    primary key(" + pkColumn + ")\n" + 
    			") with (\n" + 
    			"    type = 'QANAT_ADB30',\n" + 
    			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
    			"    dbName='" + dbNames.get(i) + "',\n" + 
    			"    tableName='" + tableName + "',\n" + 
    			"    replaceMode = 'delete_by_pk',\n" + 
    			"    writeMode = 'single',\n";
    			if (etlDbName.equalsIgnoreCase(dbNames.get(i))) {
	    			sql += "    streamType = 'kafka',\n" + 
	    			"    eventTopic = '" + eventTopicNameNew + "',\n" + 
	    			"    eventServer = '" + appKafkaJson.getString("dbName") + "'\n";
    			} else {
	    			sql += "    streamEvent = 'disable'\n";
    			}
    			sql += ");\n" + 
    			"\n" + 
    			"insert into adb_sink_" + i + " select " + StringUtils.join(selectColumns, ",") + " from v_drc where eventType in (1,2);\n" +
    			"\n" + 
    			"insert into del_adb_sink_" + i + " select " + pkColumn + ",__traceId__ from v_drc where eventType=3 " + ((StringUtils.isNotBlank(object.getFilter()) ? ("or (" + object.getFilter() + ")=false") : " or 1=2") + (StringUtils.isNotBlank(srcDsMetaJson.getString("streamFilter")) ? (" or (" + srcDsMetaJson.getString("streamFilter") + ")=false") : " or 2=1")) + " ;\n" +
    			"\n";
		}
        
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName + "/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_KAFKA010), false);
        
        DatatubeInstanceTask record = new DatatubeInstanceTask();
        record.setCreateEmpid(operateEmpid);
        record.setDatatubeInstId(datatubeInstId);
        record.setGmtCreate(new Date());
        record.setGmtModified(new Date());
        record.setIsDeleted(0L);
        record.setModifyEmpid(operateEmpid);
        record.setTaskName(jobName);
        record.setTaskScript(sql);
        record.setTaskType("blink_stream");
        record.setTenantId(tenantId);
        record.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(record);
        
        return true;
    }

    public boolean processIncrCheckJob(String tenantId, String appName, String jobName, JSONObject srcDsMetaJson, String dbName, String tableName, DataObject object, String operateEmpid, Long versionId, JSONObject appKafkaJson, JSONObject drcTopicInfo, ViewModel dataModel, Long datatubeInstId) {
    	if (srcDsMetaJson.getJSONObject("incrConf") == null) {
    		log.info("no incr sync conf");
    		return false;
    	}
    	String checkResultTopicName = "chk-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-" + object.getCode();
    	String correctTopicName = "crt-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-" + object.getCode();
    	String consumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-incr_check-" + object.getCode() + "-" + versionId;
    	boolean res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, drcTopicInfo.getString("dbName"), consumerId);
		if (!res) {
			log.error("consumer:{} create is failed", consumerId);
		}
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String pkField = null;
        if (object instanceof RelatedDataObject) {
	        for (ViewModel.Relation rel : ((RelatedDataObject)object).getRelations()) {
	        	if (!rel.getRelatedField().startsWith("exp#")) {
	        		pkField = object.getFields().stream().filter(e->e.getCode().equalsIgnoreCase(rel.getField())).collect(Collectors.toList()).get(0).getRef();
		        	break;
	        	}
	        }
        } else {
        	for (ViewModel.Field field : object.getFields()) {
            	if (field.isPk()) {
            		pkField = field.getRef();
            		break;
            	}
        	}
        }
        String sql = String.format(Adb3SyncTemplate.BLINK_INCR_CHECK_SQL
            , operateEmpid
            , sdf.format(new Date())
            , "incr check for " + tableName + " from " + object.getCode()
            , drcTopicInfo.getString("topicName")
            , consumerId
            , drcTopicInfo.getString("dbName")
            , pkField
            , pkField
            , pkField
            , dataModel.getSettings().getIncrCheckDelayMs()
            , pkField
            , pkField
            , pkField
            , pkField
            , getCheckComputeSql(tenantId, appName, srcDsMetaJson, dbName, tableName, appKafkaJson, object, correctTopicName, checkResultTopicName, dataModel)
            );
        
        if ("offhand".equalsIgnoreCase(dataModel.getSettings().getCorrectPolicy())) {
	        String correctSQL = getOffhandCorrectSql(tenantId, srcDsMetaJson, dbName, tableName, object, appKafkaJson,
					dataModel, correctTopicName);
			sql += correctSQL;
        }
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_KAFKA010, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_UDF, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_HSF_UDF), false);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        
        return true;
    }

    public boolean processFullColumnBatchCheckJob(String tenantId, String appName, String jobName, String dstDbName, String tableName, String operateEmpid, Long versionId, JSONObject kafkaJson, ViewModel dataModel, Long datatubeInstId) {
    	JSONObject srcDsMetaJson = dsInfoService.getDbMetaByDsName(tenantId, dataModel.getObject().getRef());

    	String checkResultTopicName = "bchk-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dstDbName) + "-" + dataModel.getObject().getCode();
    	String correctTopicName = "bcrt-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dstDbName) + "-" + dataModel.getObject().getCode();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> mainObjColsDef = new ArrayList<>();
    	List<String> mainObjColsSel = new ArrayList<>();
        String pkField = dataModel.getObject().getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0).getRef();
    	for (ViewModel.Field field : dataModel.getObject().getFields()) {
    		if (field.getObject() != null || field.isFunc() || "null".equalsIgnoreCase(field.getRef())) {
    			continue;
    		}
    		if (field.getRef() != null && field.isCompute()) {
    			String funcCode = field.getRef().split("\\(")[0];
        		if ("json_extract".equalsIgnoreCase(funcCode)) {
        			if ("varchar".equalsIgnoreCase(field.getType())) {
    					mainObjColsSel.add(field.getRef().replace("json_extract", "JSON_VALUE") + " AS " + field.getCode());
    				} else {
    					mainObjColsSel.add("CAST(" + field.getRef().replace("json_extract", "JSON_VALUE") + " AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " ) AS " + field.getCode());
    				}
        		} else if (field.getRef().toLowerCase().startsWith("concat_ws")) {
        			if ("varchar".equalsIgnoreCase(field.getType())) {
    					mainObjColsSel.add(field.getRef() + " AS " + field.getCode());
    				} else {
    					mainObjColsSel.add("CAST(" + field.getRef() + " AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " ) AS " + field.getCode());
    				}
        		} else if ("from_unixtime".equalsIgnoreCase(funcCode) && "datetime".equalsIgnoreCase(field.getType())) {
        			String param0 = field.getRef().split("\\(")[1].split(",")[0];
        			String ref = field.getRef().replace(param0, "CAST(" + param0 + " AS BIGINT)");
        			mainObjColsSel.add("CAST(" + ref.replace("%Y", "yyyy").replace("%m", "MM").replace("%d", "dd").replace("%h", "HH").replace("%i", "mm").replace("%s", "ss") + " AS datetime) AS " + field.getCode());
        		}
    			continue;
    		}
			mainObjColsDef.add("`" + field.getRef() + "` " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
			if ("varchar".equalsIgnoreCase(field.getType())) {
				mainObjColsSel.add(field.getRef() + " AS " + field.getCode());
			} else {
				mainObjColsSel.add("CAST(" + field.getRef() + " AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " ) AS " + field.getCode());
			}
    	}
        String sql = String.format(BLINK_FULLCOLUMN_BATCH_CHECK_SQL
            , operateEmpid
            , sdf.format(new Date())
            , "full columns batch check for " + tableName + " from " + dataModel.getObject().getCode()
            , codegenVersion
            , StringUtils.join(mainObjColsDef, ",")
            , pkField
            , checkAllScan(dataModel.getSettings(), srcDsMetaJson, dstDbName)
            , StringUtils.join(mainObjColsSel, ",")
            , viewModelSqlBuilder.getStreamSelectSql(tenantId, dataModel, dstDbName)
            , getFullColumnCheckComputeSql(tenantId, appName, srcDsMetaJson, dstDbName, tableName, kafkaJson, correctTopicName, checkResultTopicName, dataModel)
            );
        
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_HSF_UDF, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_UDF, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_MYSQL_SCAN), true);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_batch");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        
    	return true;
    }
    
    private String getFullColumnCheckComputeSql(String tenantId, String appName, JSONObject srcDsMetaJson, String dbName, String tableName, JSONObject kafkaJson, String correctTopicName, String checkResultTopicName, ViewModel dataModel) {
        List<String> colNameList = new ArrayList<>();
        List<String> colNameSelList = new ArrayList<>();
        List<String> colDefList = new ArrayList<>();
        List<String> colNameWithAliasBList = new ArrayList<>();
        List<String> colNameWithAliasCList = new ArrayList<>();
        List<String> colNameWithAliasForCorrectList = new ArrayList<>();
        String pkField = dataModel.getObject().getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0).getCode();
        List<ViewModel.Field> columnList = new ArrayList<>();
		columnList.addAll(dataModel.getObject().getFields());
		if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
			for (ViewModel.RelatedDataObject object : dataModel.getRelatedObjects()) {
				List<String> joinOnFields = object.getRelations().stream().map(e -> e.getField()).collect(Collectors.toList());
				
				for (ViewModel.Field field : object.getFields()) {
					if (joinOnFields.contains(field.getCode())) {
						continue;
					}
					columnList.add(field);
				}
			}
		}
        for (ViewModel.Field field : columnList) {
        	if ("null".equalsIgnoreCase(field.getRef())) {
        		continue;
        	}
        	colDefList.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
        	colNameList.add(field.getCode());
        	colNameWithAliasForCorrectList.add("a." + field.getCode());
        	colNameSelList.add(("datetime".equalsIgnoreCase(field.getType()) ? ("DATE_FORMAT(" + field.getCode() + ",''%Y-%m-%d %H:%i:%s'')") : field.getCode()) + " AS " + field.getCode());
            colNameWithAliasBList.add("datetime".equalsIgnoreCase(field.getType()) ? ("DATE_FORMAT(a." + field.getCode() + ",'yyyy-MM-dd HH:mm:ss')") : ("a." + field.getCode()));
            colNameWithAliasCList.add("JSON_VALUE(c.x, '$." + field.getCode() + "')");
        }
    	String sql = String.format(BLINK_CHECK_COMPUTE_FOR_FULL_COLUMN_SQL
    		, pkField
            , StringUtils.join(colNameList, ",")
            , StringUtils.join(colNameWithAliasBList, ",")
            , StringUtils.join(colNameWithAliasCList, ",")
            , dbName
            , StringUtils.join(colNameSelList, ",")
            , tableName
            , pkField
            , pkField
            , tableName
            , tableName
            , fullLinkProcessor.getFullLinkSinkWithClause(tenantId, appName)
            , checkResultTopicName
        	);
    	sql += "\n" + 
				"create table correct_sink (\n" + 
				"    " + viewModelSqlBuilder.getStreamDdlSql(tenantId, dataModel) + ",\n" + 
				"    primary key(" + pkField + ")\n" + 
				") with (\n" + 
				"    type = 'QANAT_ADB30',\n" + 
				"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
				"    dbName='" + dbName + "',\n" + 
				"    tableName='" + tableName + "',\n" + 
				"    replaceMode = 'replace',\n" + 
			    "    writeMode = 'single',\n" +
				"    streamType = 'kafka',\n" + 
				"    eventTopic = '" + correctTopicName + "',\n" + 
				"    eventServer = '" + kafkaJson.getString("dbName") + "'\n" + 
				");\n" + 
				"\n" +
				"create view v_to_correct as \n" + 
				"select `key` as " + pkField + "\n" + 
				"from v_check_result\n" + 
				"where split_index (msg, '|', 3) <> 'OK';\n" +
				"\n" +
				"insert into correct_sink\n" + 
				"select " + StringUtils.join(colNameWithAliasForCorrectList, ",") + " \n" + 
				"from v_correct as a \n" + 
				"join v_to_correct as b \n" + 
				"on a." + pkField + "=b." + pkField + ";\n" +
				"\n" + 
				"insert\n" + 
				"  into full_link_sink\n" + 
				"select\n" + 
				"  UUID() as trace_id,\n" + 
				"  a." + pkField + " as pk,\n" +
				"  CAST(a." + pkField + " AS varchar) as key,\n" + 
				"  NOW() as ts,\n" + 
				"  '" + correctTopicName + "' as db,\n" + 
				"  '' as msg,\n" + 
				"  CURRENT_TIMESTAMP as gmt_create\n" + 
				"from v_correct as a \n" + 
				"join v_to_correct as b \n" + 
				"on a." + pkField + "=b." + pkField + ";\n";
    	
    	return sql;
    }

    public boolean processBatchCheckJob(String tenantId, String appName, String jobName, JSONObject srcDsMetaJson, String dbName, String tableName, DataObject object, String operateEmpid, Long versionId, JSONObject kafkaJson, ViewModel dataModel, Long datatubeInstId) {
    	if (srcDsMetaJson.getJSONObject("incrConf") == null) {
    		log.info("no incr sync conf");
    		return false;
    	}
    	String checkResultTopicName = "bchk-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-" + object.getCode();
    	String correctTopicName = "bcrt-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-" + object.getCode();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String pkField = null;
        if (object instanceof RelatedDataObject) {
	        for (ViewModel.Relation rel : ((RelatedDataObject)object).getRelations()) {
	        	if (!rel.getRelatedField().startsWith("exp#")) {
	        		pkField = object.getFields().stream().filter(e->e.getCode().equalsIgnoreCase(rel.getField())).collect(Collectors.toList()).get(0).getRef();
		        	break;
	        	}
	        }
        } else {
        	for (ViewModel.Field field : object.getFields()) {
            	if (field.isPk()) {
            		pkField = field.getRef();
            		break;
            	}
        	}
        }
        String sql = String.format(BLINK_BATCH_CHECK_SQL
            , operateEmpid
            , sdf.format(new Date())
            , "batch check for " + tableName + " from " + object.getCode()
            , pkField
            , pkField
            , checkAllScan(dataModel.getSettings(), srcDsMetaJson, dbName)
            , pkField
            , "all"
            , pkField 
            , getCheckComputeSql(tenantId, appName, srcDsMetaJson, dbName, tableName, kafkaJson, object, correctTopicName, checkResultTopicName, dataModel)
            );
        
        if ("offhand".equalsIgnoreCase(dataModel.getSettings().getCorrectPolicy())) {
	        String correctSQL = getOffhandCorrectSql(tenantId, srcDsMetaJson, dbName, tableName, object, kafkaJson,
					dataModel, correctTopicName);
			sql += correctSQL;
        }
        
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_HSF_UDF, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_UDF, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_MYSQL_SCAN), true);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_batch");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        
        return true;
    }

	private String checkAllScan(ViewModel.Settings setting, JSONObject srcDsMetaJson, String dstDbName) {
		
		if ("tddl".equalsIgnoreCase(srcDsMetaJson.getString("dsType"))) {
			return 
		            "    type = 'custom',\n" + 
		            "    tableFactoryClass = 'com.aliyun.wormhole.qanat.blink.tddl.scan.TddlScanTableFactory',\n" + 
		            "    dbName='" + srcDsMetaJson.getString("dbName") + "',\n" +
		            "    tableName='" + srcDsMetaJson.getString("table") + "',\n" +
		            "    batchSize='" + setting.getRdsScanBatchSize() + "'" +
		            (setting.getBatchCheckDays() == 0 ? "" : (",\n    whereClause='" + setting.getBatchCheckDateField() + ">=''${" + getBatchChechStartDate(setting.getBatchCheckDays()) + "} 00:00:00'' and " + setting.getBatchCheckDateField() + "<''${curdate} 00:00:00'' '"))
		            ;
		} else {
			return 
	            "    type = 'custom',\n" + 
	            "    tableFactoryClass = 'com.aliyun.wormhole.qanat.blink.mysql.scan.MySQLScanTableFactory',\n" + 
	            "    dbName='" + srcDsMetaJson.getString("dbName") + "',\n" +
	            "    tableName='" + srcDsMetaJson.getString("table") + "',\n" +
	            "    batchSize='" + setting.getRdsScanBatchSize() + "'" +
	            (setting.getBatchCheckDays() == 0 ? "" : (",\n    whereClause='" + setting.getBatchCheckDateField() + ">=''${" + getBatchChechStartDate(setting.getBatchCheckDays()) + "} 00:00:00'' and " + setting.getBatchCheckDateField() + "<''${curdate} 00:00:00'' '"))
	            ;
		}
	}
	
	private String getBatchChechStartDate(int batchCheckDays) {
		if (batchCheckDays == 1) {
			return "bizdate";
		} else if (batchCheckDays == 3) {
			return "bizdatePre2";
		} else {
			return "bizdate";
		}
	}

	private String getOffhandCorrectSql(String tenantId, JSONObject srcDsMetaJson, String dbName, String tableName,
			DataObject object, JSONObject kafkaJson, ViewModel dataModel, String correctTopicName) {
		List<String> columnDefines = new ArrayList<>();
		List<String> selectColumns = new ArrayList<>();
		List<String> drcParseColumns = new ArrayList<>();
		String pkFieldB = null;
		String pkColumn = null;
        String dwdPk = dataModel.getObject().getFields().stream().filter(e -> e.isPk()).map(e -> e.getCode()).collect(Collectors.toList()).get(0);
        String dwdPkType = dataModel.getObject().getFields().stream().filter(e -> e.isPk()).map(e -> e.getType()).collect(Collectors.toList()).get(0);
        boolean relatedObjectPkEqMainObjPk = false;
		for (ViewModel.Field field : object.getFields()) {
			if (field.getObject() != null) {
				continue;
			}
			if (field.isFunc()) {
				continue;
			}
			if ("null".equalsIgnoreCase(field.getRef())) {
				continue;
			}
			if (field.getRef() != null && field.isCompute()) {
        		List<String> tokens = lookupProcessor.parseFuncExpress(field.getRef());
    			String funcCode = null;
    			String[] cols = null;
        		if (CollectionUtils.isNotEmpty(tokens)) {
        			funcCode = tokens.get(0);
        			cols = tokens.get(1).split(",");
        		} else {
        			funcCode = field.getRef().split("\\(")[0];
        		}
        		if ("json_extract".equalsIgnoreCase(funcCode)) {
        			String funcFieldCode = field.getRef().split("\\(")[1].split(",")[0].trim();
        			String col = field.getRef().replace("json_extract", "JSON_VALUE").replace(funcFieldCode, ("JSON_VALUE(b.x, '$." + funcFieldCode + "')"));
        			if ("varchar".equalsIgnoreCase(field.getType())) {
        				drcParseColumns.add(col + " as " + field.getCode());
        			} else {
        				drcParseColumns.add("CAST(" + col + " AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") as " + field.getCode());
        			}
        		} else if (field.getRef().toLowerCase().startsWith("concat_ws")) {
        			String[] colArray = field.getRefFields().split(",");
        			String ref = field.getRef();
        			for (int i = 0; i < colArray.length; i++) {
        				ref = ref.replace(" " + colArray[i].trim() + " ", "JSON_VALUE(b.x, '$." + colArray[i].trim() + "')");
        			}
	        		drcParseColumns.add(ref + " as " + field.getCode());
        		} else if ("from_unixtime".equalsIgnoreCase(funcCode) && "datetime".equalsIgnoreCase(field.getType())) {
        			String funcFieldCode = field.getCode();
        			String param0 = field.getRef().split("\\(")[1].split(",")[0];
        			String param1 = field.getRef().split("\\(")[1].split(",")[1];
        			String ref = "from_unixtime(CAST(" + param0.replace(funcFieldCode, "JSON_VALUE(b.x,'$." + funcFieldCode + "')") + " AS BIGINT)," + param1.replace("%Y", "yyyy").replace("%m", "MM").replace("%d", "dd").replace("%h", "HH").replace("%i", "mm").replace("%s", "ss");
		        	drcParseColumns.add("CAST(" + ref + " AS timestamp) as " + field.getCode());
        		}
    			columnDefines.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
				continue;
			}
			if ("'null'".equalsIgnoreCase(field.getRef())) {
				continue;
			}
			if (field.isPk()) {
				pkColumn = field.getCode();
				pkFieldB = field.getRef();
			}
			if (dwdPk.equalsIgnoreCase(field.getCode())) {
				relatedObjectPkEqMainObjPk = true;
			}
			selectColumns.add("`" + field.getRef() + "`");
			columnDefines.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
			if ("varchar".equalsIgnoreCase(field.getType())) {
				drcParseColumns.add("JSON_VALUE(b.x, '$." + field.getRef() + "') as " + field.getCode());
			} else {
				drcParseColumns.add("CAST(JSON_VALUE(b.x, '$." + field.getRef() + "') AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") as " + field.getCode());
			}
		}
		if (object instanceof RelatedDataObject) {
		    for (ViewModel.Relation rel : ((RelatedDataObject)object).getRelations()) {
		    	if (!rel.getRelatedField().startsWith("exp#")) {
		    		pkColumn = object.getFields().stream().filter(e->e.getCode().equalsIgnoreCase(rel.getField())).collect(Collectors.toList()).get(0).getCode();
		    		pkFieldB = object.getFields().stream().filter(e->e.getCode().equalsIgnoreCase(rel.getField())).collect(Collectors.toList()).get(0).getRef();
		        	break;
		    	}
		    }
		}
		String correctSQL = "\n" + 
				"create table correct_sink (\n" + 
				"    " + StringUtils.join(columnDefines, ",") + ",\n" + 
				"    " + ((object instanceof RelatedDataObject && !relatedObjectPkEqMainObjPk) ? (dwdPk + " " + dwdPkType + ",\n") : "") +
				"    __trace_id__ varchar," +
				"    primary key(" + dwdPk + ")\n" + 
				") with (\n" + 
				"    type = 'QANAT_ADB30',\n" + 
				"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
				"    dbName='" + dbName + "',\n" + 
				"    tableName='" + tableName + "',\n" + 
				"    replaceMode = 'update',\n" + 
			    "    writeMode = 'single',\n" +
				"    streamType = 'kafka',\n" + 
				"    eventTopic = '" + correctTopicName + "',\n" + 
				"    eventServer = '" + kafkaJson.getString("dbName") + "'\n" + 
				");\n" + 
				"\n" + 
				"create view v_correct AS\n" + 
				"select \n" + 
				"	" + StringUtils.join(drcParseColumns, ",") + ",\n" + 
				"   " + ((object instanceof RelatedDataObject && !relatedObjectPkEqMainObjPk) ? "cast(a.pk as " + dwdPkType + ") as " + dwdPk + ",\n" : "") +
				"	__traceId__\n" +
				"from v_check_result as a\n" + 
				",LATERAL TABLE (queryDim('" + ("tddl".equalsIgnoreCase(srcDsMetaJson.getString("dsType")) ? dbName : srcDsMetaJson.getString("dbName")) + "', 'select " + StringUtils.join(selectColumns, ",") + " from " + srcDsMetaJson.getString("table") + " where " + pkFieldB + "=?', a.key)) as b(x)\n" + 
				"where split_index (a.msg, '|', 3)<>'OK' and split_index (a.msg, '|', 1) is not null and split_index (a.msg, '|', 1) <> ''\n" + 
				(object instanceof RelatedDataObject ? ";" : "and a.eventType<>'1';") + 
				"\n" + 
				"insert into correct_sink\n" + 
				"select * from v_correct;\n" + 
				"\n" + 
				"insert\n" + 
				"  into full_link_sink\n" + 
				"select\n" + 
				"  __traceId__ as trace_id,\n" + 
				"  cast(" + dwdPk + " as bigint) as pk,\n" +
				"  CAST(" + pkColumn + " AS varchar) as key,\n" + 
				"  NOW() as ts,\n" + 
				"  '" + correctTopicName + "' as db,\n" + 
				"  '' as msg,\n" + 
				"  CURRENT_TIMESTAMP as gmt_create\n" + 
				"from v_correct;";

		if (!(object instanceof RelatedDataObject)) {
			List<String> colDefList = new ArrayList<>();
			List<ViewModel.Field> columnList = YamlUtil.getColumnsByModel(dataModel);
			List<String> jsonParseColumnsAll = new ArrayList<>();
			List<ViewModel.Field> funcFields = new ArrayList<>();
			for (ViewModel.Field field : columnList) {
				if (field.isFunc()) {
					funcFields.add(field);
					continue;
				} 
		    	if ("varchar".equalsIgnoreCase(field.getType()) || field.getObject() != null) {
		    		jsonParseColumnsAll.add("JSON_VALUE (b.x, '$." + field.getCode() + "') as `" + field.getCode() + "`");
		    	} else {
		    		jsonParseColumnsAll.add("CAST(JSON_VALUE (b.x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") as `" + field.getCode() + "`");
		    	}
		    	String code = field.getCode();
				if (StringUtils.isBlank(code)) {
					code = field.getRef();
				}
				String type = StringUtils.isBlank(field.getType()) ? "varchar" : ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType());
				if (field.getObject() != null) {
					type = "varchar";
				}
			    colDefList.add("`" + code + "` " + type);
		    }

			Map<String, String> colMap = new HashMap<>();
			for (ViewModel.Field field : dataModel.getObject().getFields()) {
				if (field.isFunc()) {
					continue;
				} else {
					if ("varchar".equalsIgnoreCase(field.getType())) {
						colMap.put(field.getCode(), "JSON_VALUE(b.x, '$." + field.getCode() + "')");
					} else {
						colMap.put(field.getCode(), "CAST(JSON_VALUE(b.x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
					}
				}
			}
			if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
				for (RelatedDataObject relObj : dataModel.getRelatedObjects()) {
					Map<String, Relation> rels = relObj.getRelations().stream().filter(e->!e.getRelatedField().startsWith("exp#")).collect(Collectors.toMap(Relation::getField, Function.identity()));
					List<ViewModel.Field> fields = relObj.getFields().stream().filter(e->!rels.containsKey(e.getCode())).collect(Collectors.toList());
					for (ViewModel.Field field : fields) {
						if ("varchar".equalsIgnoreCase(field.getType())) {
							colMap.put(relObj.getCode() + "." + field.getCode(), "JSON_VALUE(b.x, '$." + field.getCode() + "')");
						} else {
							colMap.put(relObj.getCode() + "." + field.getCode(), "CAST(JSON_VALUE(b.x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
						}
					}
				}
			}
			for (ViewModel.Field field : funcFields) {
        		List<String> tokens = lookupProcessor.parseFuncExpress(field.getRef());
    			String funcCode = null;
    			String[] cols = null;
        		if (CollectionUtils.isNotEmpty(tokens)) {
        			funcCode = tokens.get(0);
        			cols = tokens.get(1).split(",");
        		}
				List<String> exps = new ArrayList<>();
				for (String col : cols) {
					if (col.startsWith("'") && col.endsWith("'")) {
						exps.add(col);
					} else {
						exps.add(colMap.get(col));
					}
				}
				ExtensionExample example = new ExtensionExample();
				example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andCodeEqualTo(funcCode);
				List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
				if (CollectionUtils.isEmpty(exts)) {
					log.error("funcCode:{} not found", funcCode);
					continue;
				}
				String func = exts.get(0).getPlugin().equalsIgnoreCase("groovy")?"groovyFunc":"dfaasFunc";
				String funcExp = null;
				if (field.getType().equalsIgnoreCase("varchar")) {
					funcExp = func + "('" + tenantId + "', '" + funcCode + "'," + StringUtils.join(exps, ",") + ") AS " + field.getCode();
				} else {
					funcExp = "CAST(" + func + "('" + tenantId + "', '" + funcCode + "'," + StringUtils.join(exps, ",") + ") AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + ") AS " + field.getCode();
				}
				jsonParseColumnsAll.add(funcExp);

		    	String code = field.getCode();
				if (StringUtils.isBlank(code)) {
					code = field.getRef();
				}
				String type = StringUtils.isBlank(field.getType()) ? "varchar" : ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType());
				if (field.getObject() != null) {
					type = "varchar";
				}
			    colDefList.add("`" + code + "` " + type);
			}
			
			correctSQL += "\n" +
				"CREATE FUNCTION groovyFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatFunctionUdf';\r\n" +
				"CREATE FUNCTION dfaasFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDfaasFunctionUdf';\r\n" +
				"create table correct_sink_all (\n" + 
				"    " + StringUtils.join(colDefList, ",") + ",\n" + 
				"    __trace_id__ varchar," +
				"    primary key(" + pkColumn + ")\n" + 
				") with (\n" + 
				"    type = 'QANAT_ADB30',\n" + 
				"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
				"    dbName='" + dbName + "',\n" + 
				"    tableName='" + tableName + "',\n" + 
				"    replaceMode = 'replace',\n" + 
			    "    writeMode = 'single',\n" +
				"    streamType = 'kafka',\n" + 
				"    eventTopic = '" + correctTopicName + "',\n" + 
				"    eventServer = '" + kafkaJson.getString("dbName") + "'\n" + 
				");\n" + 
				"\n" + 
				"create view v_correct_all AS\n" + 
				"select \n" + 
				"	" + StringUtils.join(jsonParseColumnsAll, ",") + ",\n" + 
				"	__traceId__\n" +
				"from v_check_result as a\n" + 
				",LATERAL TABLE (queryDim('" + dbName + "', '" + "select * from (" + viewModelSqlBuilder.getSelectSql(tenantId, dataModel, null).replace("'", "''") + ") as t where " + pkColumn + "=?" + "', a.key)) as b(x)\n" + 
				"where split_index (a.msg, '|', 3)<>'OK' and split_index (a.msg, '|', 1) is not null and split_index (a.msg, '|', 1) <> ''\n" + 
				"and a.eventType='1';" + 
				"\n" + 
				"insert into correct_sink_all\n" + 
				"select * from v_correct_all;\n" + 
				"\n" + 
				"insert\n" + 
				"  into full_link_sink\n" + 
				"select\n" + 
				"  __traceId__ as trace_id,\n" + 
				"  cast(" + dwdPk + " as bigint) as pk,\n" +
				"  CAST(" + pkColumn + " AS varchar) as key,\n" + 
				"  NOW() as ts,\n" + 
				"  '" + correctTopicName + "' as db,\n" + 
				"  '' as msg,\n" + 
				"  CURRENT_TIMESTAMP as gmt_create\n" + 
				"from v_correct_all;";
		}
		return correctSQL;
	}
    
    private String getCheckComputeSql(String tenantId, String appName, JSONObject srcDsMetaJson, String dbName, String tableName, JSONObject kafkaJson,  DataObject object, String correctTopicName, String checkResultTopicName, ViewModel dataModel) {
        List<String> colNameBList = new ArrayList<>();
        List<String> colNameCList = new ArrayList<>();
        List<String> colNameWithAliasBList = new ArrayList<>();
        List<String> colNameWithAliasCList = new ArrayList<>();
        String pkFieldB = null;
        String pkFieldC = null;
        for (ViewModel.Field field : object.getFields()) {
        	if (field.getObject() != null) {
        		continue;
        	}
        	if (field.isPk()) {
        		pkFieldB = field.getRef();
        		pkFieldC = field.getCode();
        	}
        	if (field.isFunc()) {
        		continue;
        	}
        	if ("null".equalsIgnoreCase(field.getRef())) {
        		continue;
        	}
        	if (field.getRef() != null && field.isCompute()) {
        		List<String> tokens = lookupProcessor.parseFuncExpress(field.getRef());
    			String funcCode = null;
    			String[] cols = null;
        		if (CollectionUtils.isNotEmpty(tokens)) {
        			funcCode = tokens.get(0);
        			cols = tokens.get(1).split(",");
        		} else {
        			funcCode = field.getRef().split("\\(")[0];
        		}
        		if ("json_extract".equalsIgnoreCase(funcCode)) {
        			String funcFieldCode = field.getRef().split("\\(")[1].split(",")[0].trim();
        			colNameWithAliasBList.add(field.getRef().replace("json_extract", "JSON_VALUE").replace(funcFieldCode, ("JSON_VALUE(b.x, '$." + funcFieldCode + "')")));
        		} else if (field.getRef().toLowerCase().startsWith("concat_ws")) {
        			String[] colArray = field.getRefFields().split(",");
        			String ref = field.getRef();
        			for (int i = 0; i < colArray.length; i++) {
        				ref = ref.replace(" " + colArray[i].trim() + " ", "JSON_VALUE(b.x, '$." + colArray[i].trim() + "')");
        			}
        			colNameWithAliasBList.add(ref);
        		} else if ("from_unixtime".equalsIgnoreCase(funcCode) && "datetime".equalsIgnoreCase(field.getType())) {
        			String funcFieldCode = field.getCode();
        			String param0 = field.getRef().split("\\(")[1].split(",")[0];
        			String param1 = field.getRef().split("\\(")[1].split(",")[1];
        			String ref = "from_unixtime(CAST(" + param0.replace(funcFieldCode, "JSON_VALUE(b.x,'$." + funcFieldCode + "')") + " AS BIGINT)," + param1.replace("%Y", "yyyy").replace("%m", "MM").replace("%d", "dd").replace("%h", "HH").replace("%i", "mm").replace("%s", "ss");
        			colNameWithAliasBList.add(ref);
        		}
        		colNameWithAliasCList.add("JSON_VALUE(c.x, '$." + field.getCode() + "')");
            	colNameBList.add(field.getCode());
            	colNameCList.add(field.getCode());
        		continue;
        	}
        	colNameBList.add(field.getRef());
        	colNameCList.add(field.getCode());
            colNameWithAliasBList.add("JSON_VALUE(b.x, '$." + field.getRef() + "')");
            colNameWithAliasCList.add("JSON_VALUE(c.x, '$." + field.getCode() + "')");
        }
        String dwdPk = dataModel.getObject().getFields().stream().filter(e -> e.isPk()).map(e -> e.getCode()).collect(Collectors.toList()).get(0);
        if (!colNameCList.contains(dwdPk)) {
        	colNameCList.add(dwdPk);
        }
		if (object instanceof RelatedDataObject) {
	        for (ViewModel.Relation rel : ((RelatedDataObject)object).getRelations()) {
	        	if (!rel.getRelatedField().startsWith("exp#")) {
	        		pkFieldB = object.getFields().stream().filter(e->e.getCode().equalsIgnoreCase(rel.getField())).collect(Collectors.toList()).get(0).getRef();
	        		pkFieldC = rel.getRelatedField().split("\\.")[1];
		        	break;
	        	}
	        }
		}
    	String sql = String.format(object instanceof RelatedDataObject ? BLINK_CHECK_COMPUTE_FOR_RELATED_OBJECT_SQL : BLINK_CHECK_COMPUTE_FOR_MAIN_OBJECT_SQL
            , StringUtils.join(colNameBList, ",")
            , StringUtils.join(colNameWithAliasBList, ",")
            , StringUtils.join(colNameWithAliasCList, ",")
            , dwdPk
            , "tddl".equalsIgnoreCase(srcDsMetaJson.getString("dsType")) ? dbName : srcDsMetaJson.getString("dbName")
            , StringUtils.join(colNameBList, ",")
            , srcDsMetaJson.getString("table")
            , pkFieldB
            , dbName
            , StringUtils.join(colNameCList, ",")
            , tableName
            , pkFieldC
            , object instanceof RelatedDataObject ? " WHERE JSON_VALUE(c.x, '$." + pkFieldC + "') is not null " : ""
            , tableName
            , tableName
            , fullLinkProcessor.getFullLinkSinkWithClause(tenantId, appName)
            , checkResultTopicName
        	);
    	return sql;
    }
    
    public String processBatchSyncJob(String tenantId, String appName, List<String> dbNames, String etlDbName, String tableName, DataObject object, String operateEmpid, Long versionId, String viewModelName, JSONObject kafkaJson, boolean isUpsert, Settings modelSettings, Long datatubeInstId) {
    	String jobName = "fullsync_" + getAppIdByName(tenantId, appName) + "_" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "_" + object.getCode() + "_v" + versionId;
    	JSONObject dsMetaJson = null;
    	String sql = null;
    	try {
    		dsMetaJson = dsInfoService.getOdpsTableMetaByOdsDsName(tenantId, object.getRef());
    	} catch(Exception e) {}
    	if (dsMetaJson == null) {
    		dsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, object.getRef());
    	}
    	String eventTopicName = "stream-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "-" + object.getCode();
		boolean res = kafkaManagementService.createTopic(tenantId, appName, eventTopicName);
		if (!res) {
			log.error("topic:{} create is failed", eventTopicName);
		}

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if ("odps".equalsIgnoreCase(dsMetaJson.getString("dsType"))) {
        	DsFieldInfoExample example = new DsFieldInfoExample();
        	example.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsNameEqualTo(dsMetaJson.getString("dsName"));
        	List<DsFieldInfo> dsFields = dsFieldInfoMapper.selectByExample(example);
        	if (CollectionUtils.isEmpty(dsFields)) {
        		throw new QanatBizException(dsMetaJson.getString("dsName") + " has no fields found");
        	}
        	Map<String, DsFieldInfo> dsFieldMap = dsFields.stream().collect(Collectors.toMap(DsFieldInfo::getFieldName, Function.identity()));
        	
        	List<String> columnDefines = new ArrayList<>();
	        String updateKeyField = null;
	        Map<String, String> pkMap = new HashMap<>();
	        for (ViewModel.Relation rel : ((RelatedDataObject)object).getRelations()) {
	        	if (!rel.getRelatedField().startsWith("exp#")) {
	        		updateKeyField = rel.getRelatedField().split("\\.")[1];
	        		pkMap.put(rel.getField(), updateKeyField);
	        	}
	        	break;
	        }
	        for (ViewModel.Field field : object.getFields()) {
				if (columnDefines.contains("`" + field.getRef() + "` " + getBlinkType(dsFieldMap.get(field.getRef().toLowerCase()).getFieldType()))) {
					continue;
				}
	        	columnDefines.add("`" + field.getRef() + "` " + getBlinkType(dsFieldMap.get(field.getRef().toLowerCase()).getFieldType()));
	        }
	        List<String> columnDefines1 = new ArrayList<>();
	        List<String> columnSels = new ArrayList<>();
	        String pkColumn1 = ((RelatedDataObject)object).getRelations().get(0).getRelatedField().split("\\.")[1];
	        for (ViewModel.Field field : object.getFields()) {
	        	columnDefines1.add("`" + (pkMap.get(field.getCode()) != null ? pkMap.get(field.getCode()) : field.getCode()) + "` " + getBlinkType(field.getType()));
	        	String fieldType = field.getType();
	        	String odpsFieldType = dsFieldMap.get(field.getRef().toLowerCase()).getFieldType();
	        	if (getOdpsType(fieldType).equalsIgnoreCase(odpsFieldType) ) {
	        		columnSels.add(field.getRef() + " AS `" + (pkMap.get(field.getCode()) != null ? pkMap.get(field.getCode()) : field.getCode()) + "`");
	        	} else {
		        	columnSels.add("CAST(" + field.getRef() + " AS " + ("datetime".equalsIgnoreCase(fieldType) ? "timestamp" : fieldType) + ") AS `" + (pkMap.get(field.getCode()) != null ? pkMap.get(field.getCode()) : field.getCode()) + "`");
	        	}
	        }
	        sql = "CREATE TABLE odps_source (\n" + 
	    			"    " + StringUtils.join(columnDefines, ",") + "\n" + 
	    			") WITH (\n" + 
	    			"    type = 'odps',\n" + 
	    			"    endPoint = '" + dsMetaJson.getString("odpsServer") + "',\n" + 
	    			"    project='" + dsMetaJson.getString("project") + "',\n" + 
	    			"    tableName='" + dsMetaJson.getString("table") + "',\n" + 
	    			"    accessId='" + dsMetaJson.getString("accessId") + "',\n" + 
	    			"    accessKey='" + dsMetaJson.getString("accessKey")  + "',\n" + 
	    			"    `partition`='max_pt()'\n" + 
	    			");\n" + 
	    			"\n";
	        for (int i = 0; i < dbNames.size(); i++) {
	    		sql += "create table adb_sink_" + i + " (\n" + 
	    			"    " + StringUtils.join(columnDefines1, ",") + ",\n" + 
	    			"    primary key(" + pkColumn1 + ")\n" + 
	    			") with (\n" + 
	    			"    type = 'QANAT_ADB30',\n" + 
	    			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
	    			"    dbName='" + dbNames.get(i) + "',\n" + 
	    			"    tableName='" + tableName + "',\n" + 
	    			"    replaceMode = '" + (isUpsert ? "upsert" : "update") + "',\n" + 
	    			"    writeMode = 'single',\n" + 
	    			"    streamEvent = 'disable'\n" +
	    			");\n" + 
	    			"\n" + 
	    			"insert into adb_sink_" + i + "\n" + 
	    			"select \n" +
	    			StringUtils.join(columnSels, ",") + "\n" +
	    			"from odps_source;\n" +
	    			"\n";
	        }
	        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName + "/" + tableName + "/", 
	        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3), true, blinkService.getBatchPlanJson4OdpsSource(tableName, pkColumn1, dbNames.size()));
        } else {
	        List<String> columnDefines = new ArrayList<>();
	        String pkColumn = null;
	        String updateKeyField = null;
	        Map<String, String> pkMap = new HashMap<>();
	        for (ViewModel.Relation rel : ((RelatedDataObject)object).getRelations()) {
	        	if (!rel.getRelatedField().startsWith("exp#")) {
	        		updateKeyField = rel.getRelatedField().split("\\.")[1];
	        		pkMap.put(rel.getField(), updateKeyField);
	        	}
	        	break;
	        }
	        for (ViewModel.Field field : object.getFields()) {
	        	if (field.getObject() != null) {
	        		continue;
	        	}
	        	if (field.isPk()) {
	        		pkColumn = field.getRef();
	        	}
				if (columnDefines.contains("`" + field.getRef() + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()))) {
					continue;
				}
	        	columnDefines.add("`" + field.getRef() + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
	        }
	        List<String> columnDefines1 = new ArrayList<>();
	        String pkColumn1 = ((RelatedDataObject)object).getRelations().get(0).getRelatedField().split("\\.")[1];
	        for (ViewModel.Field field : object.getFields()) {
	        	columnDefines1.add("`" + (pkMap.get(field.getCode()) != null ? pkMap.get(field.getCode()) : field.getCode()) + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
	        }
	        sql = "--********************************************************************--\n" + 
	    			"--Author: " + operateEmpid + "\n" + 
	    			"--CreateTime: " + sdf.format(new Date()) + "\n" + 
	    			"--Comment: " + ("sync for " + tableName + " from " + object.getCode()) + "\n" + 
	    			"--********************************************************************--\n" + 
	    			"CREATE TABLE adb_source (\n" + 
	    			"    " + StringUtils.join(columnDefines, ",") + ",\n" + 
	    			"    primary key(" + pkColumn + ")\n" + 
	    			") WITH (\n" + 
	    			"    type = 'custom',\n" + 
	    			"    tableFactoryClass = 'com.aliyun.wormhole.qanat.blink.mysql.scan.MySQLScanTableFactory',\n" + 
	    			"    dbName='" + etlDbName + "',\n" + 
	    			"    tableName='" + dsMetaJson.getString("table") + "',\n" + 
	    			"    batchSize='" + modelSettings.getRdsScanBatchSize() + "'\n" + 
	    			");\n" + 
	    			"\n";
	        for (int i = 0; i < dbNames.size(); i++) {
	    		sql += "create table adb_sink_" + i + " (\n" + 
	    			"    " + StringUtils.join(columnDefines1, ",") + ",\n" + 
	    			"    primary key(" + pkColumn1 + ")\n" + 
	    			") with (\n" + 
	    			"    type = 'QANAT_ADB30',\n" + 
	    			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
	    			"    dbName='" + dbNames.get(i) + "',\n" + 
	    			"    tableName='" + tableName + "',\n" + 
	    			"    replaceMode = '" + (isUpsert ? "upsert" : "update") + "',\n" + 
	    			"    writeMode = 'single',\n" + 
	    			"    streamEvent = 'disable'\n" +
	    			");\n" + 
	    			"\n" + 
	    			"insert into adb_sink_" + i + "\n" + 
	    			"select * from adb_source;\n" +
	    			"\n";
	        }
	        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName + "/" + tableName + "/", 
	        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_MYSQL_SCAN), true, blinkService.getBatchPlanJson4DwSource(tableName, pkColumn1, dbNames.size()));
        }
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_batch");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
    	
    	if (StringUtils.isNotBlank(dsMetaJson.getString("timeExpression"))) {
    		String taskName = "DAG_refresh_" + jobName;
            Long taskId = taskService.isTaskExists(tenantId, taskName);
            if (taskId == null) {
	        	String taskTemplate = "Dag dag = new Dag(\"DAG_refresh_%s\");\r\n" + 
						"dag.setTimeExpression(\"%s\");\r\n" +
						"BlinkBatchNode fullSync = new BlinkBatchNode(\"BlinkBatch_%s\", dag);\r\n" + 
						"fullSync.setJobName(\"%s\");\r\n" +
						"return dag;";
	
				String dagScript = String.format(taskTemplate
												, jobName
												, dsMetaJson.getString("timeExpression")
												, jobName
												, jobName);
				TaskInfoRequest taskInfo = new TaskInfoRequest();
				taskInfo.setDagScript(dagScript);
				taskInfo.setName(taskName);
				taskInfo.setOperateEmpid(operateEmpid);
				taskInfo.setTaskDesc(taskName);
				taskInfo.setPolicy(DagPolicy.ODS.toString());
				taskInfo.setTenantId(tenantId);
				taskInfo.setAppName(appName);
				taskId = taskService.createDAGTask(taskInfo);
				
				ViewModelTaskRelation record = new ViewModelTaskRelation();
	            record.setCreateEmpid(operateEmpid);
	            record.setGmtCreate(new Date());
	            record.setGmtModified(new Date());
	            record.setIsDeleted(0L);
	            record.setModelVersionId(versionId);
	            record.setModifyEmpid(operateEmpid);
	            record.setTaskId(taskId);
	            record.setTenantId(tenantId);
	            record.setViewModelName(viewModelName);
	            viewModelTaskRelationMapper.insert(record);
				log.info("[{}]create qanat timer task[{}] finished", "", taskId);
            }
    	}
        return jobName;
    }
    
    private String getOdpsType(String type) {
        if ("varchar".equalsIgnoreCase(type)) {
            return "string";
        } else if (type.equalsIgnoreCase("tinyint")) {
            return "int";
        }
        return type;
    }
    
    private String getBlinkType(String type) {
        if ("string".equalsIgnoreCase(type)) {
            return "varchar";
        } else if ("datetime".equalsIgnoreCase(type)) {
        	return "timestamp";
        }
        return type;
    }

    public boolean processRelatedObjectIncrSyncJob(String tenantId, String appName, String jobName, List<String> dbNames, String etlDbName, String tableName, DataObject object, String operateEmpid, Long versionId, JSONObject kafkaJson, String datatubeLevel, Long datatubeInstId, String pkField, ViewModel dataModel) {
    	JSONObject dsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, object.getRef());
    	if (dsMetaJson.getJSONObject("incrConf") == null) {
    		log.info("no incr sync conf");
    		return false;
    	}
    	String topicName = dsMetaJson.getJSONObject("incrConf").getString("topicName");
    	String consumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "-incr_sync-" + object.getCode() + "-" + versionId;
    	boolean res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, dsMetaJson.getJSONObject("incrConf").getString("dbName"), consumerId);
		if (!res) {
			log.error("consumer:{} create is failed", consumerId);
		}
		limiterService.setFlowLimitIfNotExists(datatubeInstId, consumerId, 1.0);
        int parallel = viewModelOptimizer.getParallel(tenantId, datatubeLevel, dsMetaJson.getInteger("qph"));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> columns = new ArrayList<>();
        List<String> selectColumns = new ArrayList<>();
        List<String> columnDefines = new ArrayList<>();
        List<String> drcParseColumns = new ArrayList<>();
        List<String> columnChangeInWhere = new ArrayList<>();
        String updateKeyField = null;
        Map<String, String> pkMap = new HashMap<>();
        for (ViewModel.Relation rel : ((RelatedDataObject)object).getRelations()) {
        	if (!rel.getRelatedField().startsWith("exp#")) {
        		updateKeyField = rel.getRelatedField().split("\\.")[1];
        		pkMap.put(rel.getField(), updateKeyField);
        	}
        	break;
        }
        for (ViewModel.Field field : object.getFields()) {
        	if (field.getObject() != null) {
        		continue;
        	}
        	if (field.isFunc()) {
        		continue;
        	}
        	if (field.getRef() != null && field.isCompute()) {
        		List<String> tokens = lookupProcessor.parseFuncExpress(field.getRef());
    			String funcCode = null;
    			String[] cols = null;
        		if (CollectionUtils.isNotEmpty(tokens)) {
        			funcCode = tokens.get(0);
        			cols = tokens.get(1).split(",");
        		} else {
        			funcCode = field.getRef().split("\\(")[0];
        		}
        		if ("json_extract".equalsIgnoreCase(funcCode)) {
        			String funcFieldCode = field.getRef().split("\\(")[1].split(",")[0].trim();
        			String col = field.getRef().replace("json_extract", "JSON_VALUE").replace(funcFieldCode, ("JSON_VALUE(b.x, '$." + funcFieldCode + "')"));
	        		if ("varchar".equalsIgnoreCase(field.getType())) {
		        		drcParseColumns.add(col + " as " + field.getCode());
		        	} else {
		        		drcParseColumns.add("CAST(" + col + " AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") as " + field.getCode());
		        	}
        		} else if (field.getRef().toLowerCase().startsWith("concat_ws")) {
        			String[] colArray = field.getRefFields().split(",");
        			String ref = field.getRef();
        			for (int i = 0; i < colArray.length; i++) {
        				ref = ref.replace(" " + colArray[i].trim() + " ", "JSON_VALUE(b.x, '$." + colArray[i].trim() + "')");
        			}
	        		drcParseColumns.add(ref + " as " + field.getCode());
        		} else if ("from_unixtime".equalsIgnoreCase(funcCode) && "datetime".equalsIgnoreCase(field.getType())) {
        			String funcFieldCode = field.getCode();
        			String param0 = field.getRef().split("\\(")[1].split(",")[0];
        			String param1 = field.getRef().split("\\(")[1].split(",")[1];
        			String ref = "from_unixtime(CAST(" + param0.replace(funcFieldCode, "JSON_VALUE(b.x,'$." + funcFieldCode + "')") + " AS BIGINT)," + param1.replace("%Y", "yyyy").replace("%m", "MM").replace("%d", "dd").replace("%h", "HH").replace("%i", "mm").replace("%s", "ss");
		        	drcParseColumns.add("CAST(" + ref + " AS timestamp) as " + field.getCode());
        		}
	        	selectColumns.add("`" + field.getCode() + "`");
	        	columnDefines.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
        		continue;
        	}
        	columns.add("'" + field.getRef() + "'");
        	if (StringUtils.isNotBlank(field.getExpr())) {
        		selectColumns.add(field.getExpr());
        	} else {
        		selectColumns.add("`" + field.getRef() + "`");
        	}
        	columnDefines.add("`" + (pkMap.get(field.getCode()) != null ? pkMap.get(field.getCode()) : field.getCode()) + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
        	if ("varchar".equalsIgnoreCase(field.getType())) {
        		if (pkMap.containsKey(field.getCode())) {
        			drcParseColumns.add("COALESCE(JSON_VALUE(b.x, '$." + field.getRef() + "'),JSON_VALUE(b.x, '$." + field.getRef() + "_old')) as " + field.getRef());
        		} else {
        			drcParseColumns.add("JSON_VALUE(b.x, '$." + field.getRef() + "') as " + field.getRef());
        		}
        	} else {
        		if (pkMap.containsKey(field.getCode())) {
        			drcParseColumns.add("CAST(COALESCE(JSON_VALUE(b.x, '$." + field.getRef() + "'),JSON_VALUE (b.x, '$." + field.getRef() + "_old')) AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") as " + field.getRef());
        		} else {
        			drcParseColumns.add("CAST(JSON_VALUE(b.x, '$." + field.getRef() + "') AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") as " + field.getRef());
        		}
        	}
        	columnChangeInWhere.add("COALESCE(JSON_VALUE(b.x, '$." + field.getRef() + "'),'') <> COALESCE(JSON_VALUE(b.x, '$." + field.getRef() + "_old'),'')");
        }
        selectColumns.add("__traceId__");
        String sql = "--SQL\n" + 
    			"--********************************************************************--\n" + 
    			"--Author: " + operateEmpid + "\n" + 
    			"--CreateTime: " + sdf.format(new Date()) + "\n" + 
    			"--Comment: " + ("sync for " + tableName + " from " + object.getCode()) + "\n" +
    			"--Version: " + codegenVersion + "\n" +  
    			"--********************************************************************--\n" + 
    			"create table drc_mq_source (\n" + 
    			"    msg varchar,\n" + 
    			"    __traceId__ varchar header\n" + 
    			") with (\n" + 
    			"  type = 'custom',\n" + 
    			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
    			"  topic = '" + topicName + "',\n" + 
    			"  `group.id` = '" + consumerId + "',\n" + 
    			"  `dbName` = '" + dsMetaJson.getJSONObject("incrConf").getString("dbName") + "',\n" +
    			"  startupMode = 'TIMESTAMP',\n" +
    			"  fieldDelimiter = '`'\n" +
    			");\n" + 
    			"\n" + 
    			"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
    			"\n" + 
    			"create view v_drc as \n" + 
    			"select \n" + 
    			"    " + StringUtils.join(drcParseColumns, ",") + ",\n" + 
    			"    CAST(JSON_VALUE (b.x, '$.eventType') as integer) as eventType,\n" + 
    			"    a.__traceId__\n" + 
    			"from\n" + 
    			"    drc_mq_source as a,\n" + 
    			"    LATERAL TABLE (parseDrcFields (a.msg, " + StringUtils.join(columns, ",") + ")) as b(x)\n" + 
    			"where " + StringUtils.join(columnChangeInWhere, " or ") + "\n" +
    			";\n" + 
    			"\n";
        
		String eventTopicName = "stream-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "-" + object.getCode();
    	boolean kfkRes = kafkaManagementService.createTopic(tenantId, appName, eventTopicName, parallel);
		if (!kfkRes) {
			log.error("topic:{} create is failed", eventTopicName);
		}
		
		String pkPart = "";
		if (updateKeyField.equalsIgnoreCase(pkField)) {
			pkPart = StringUtils.isNotBlank(dataModel.getSettings().getDistributeKey()) && !pkField.equalsIgnoreCase(dataModel.getSettings().getDistributeKey()) ? (pkField + "," + dataModel.getSettings().getDistributeKey()) : pkField;
		} else {
			pkPart = updateKeyField;
		}
		
		for (int i = 0; i < dbNames.size(); i++) {
			sql += "create table upd_adb_sink_" + i + " (\n" + 
    			"    " + StringUtils.join(columnDefines, ",") + ",\n" + 
    			"    __trace_id__ varchar,\n" + 
    			"    primary key(" + pkPart + ")\n" + 
    			") with (\n" + 
    			"    type = 'QANAT_ADB30',\n" + 
    			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
    			"    dbName='" + dbNames.get(i) + "',\n" + 
    			"    tableName='" + tableName + "',\n" + 
    			"    replaceMode = '" + (updateKeyField.equalsIgnoreCase(pkField) ? "upsert" : "update_by_query") + "',\n" + 
    			"    writeMode = 'single',\n";
			if (etlDbName.equalsIgnoreCase(dbNames.get(i))) {
    			sql += "    streamType = 'kafka',\n" + 
    			"    eventTopic = '" + eventTopicName + "',\n" + 
    			"    eventServer = '" + kafkaJson.getString("dbName") + "'\n";
			} else {
    			sql += "    streamEvent = 'disable'\n";
			}
    		sql += ");\n" + 
    			"\n" + 
    			"insert into upd_adb_sink_" + i + " select " + StringUtils.join(selectColumns, ",") + " from v_drc where 1=1 " + ((StringUtils.isNotBlank(object.getFilter()) ? ("and " + object.getFilter()) : "") + (StringUtils.isNotBlank(dsMetaJson.getString("streamFilter")) ? (" and " + dsMetaJson.getString("streamFilter")) : "")) + ";" +
    			"\n";
		}
        
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_KAFKA010), false);
        
        DatatubeInstanceTask record = new DatatubeInstanceTask();
        record.setCreateEmpid(operateEmpid);
        record.setDatatubeInstId(datatubeInstId);
        record.setGmtCreate(new Date());
        record.setGmtModified(new Date());
        record.setIsDeleted(0L);
        record.setModifyEmpid(operateEmpid);
        record.setTaskName(jobName);
        record.setTaskScript(sql);
        record.setTaskType("blink_stream");
        record.setTenantId(tenantId);
        record.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(record);
        return true;
    }
	
	private Long getAppIdByName(String tenantId, String appName) {
		AppInfoExample example = new AppInfoExample();
		example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
		List<AppInfo> apps = appInfoMapper.selectByExample(example);
		return apps.get(0).getId();
	}
}