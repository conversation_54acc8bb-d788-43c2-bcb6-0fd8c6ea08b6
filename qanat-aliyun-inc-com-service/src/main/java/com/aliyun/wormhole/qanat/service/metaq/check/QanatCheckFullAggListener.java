package com.aliyun.wormhole.qanat.service.metaq.check;

import com.aliyun.wormhole.qanat.service.metaq.producer.CheckProducer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class QanatCheckFullAggListener extends QanatCheckAggListener{

    @Resource(name = "checkFullProducer")
    private CheckProducer checkProducer;

    @Override
    public CheckProducer getCheckProducer() {
        return checkProducer;
    }
}
