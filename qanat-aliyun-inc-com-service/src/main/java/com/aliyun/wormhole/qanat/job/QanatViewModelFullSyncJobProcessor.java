package com.aliyun.wormhole.qanat.job;

import java.util.Map;

import javax.annotation.Resource;

import com.aliyun.wormhole.qanat.api.dag.DataTubeFullSyncNode;
import com.aliyun.wormhole.qanat.api.service.RtdwViewModelTaskService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * ViewModel创建及全量同步执行任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatViewModelFullSyncJobProcessor extends AbstractQanatNodeJobProcessor<DataTubeFullSyncNode> {
    
    @Resource
    private RtdwViewModelTaskService rtdwViewModelTaskService;

    @Override
    void doProcess(Map<String, Object> instParamsMap, DataTubeFullSyncNode node) {
    	rtdwViewModelTaskService.createTableAndFullSync((String)instParamsMap.get("tenantId"), node.getModelId(), node.getBatchJobs());
    }
}