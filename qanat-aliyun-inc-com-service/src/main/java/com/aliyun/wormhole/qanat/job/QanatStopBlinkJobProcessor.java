package com.aliyun.wormhole.qanat.job;

import java.util.Map;

import javax.annotation.Resource;

import com.aliyun.wormhole.qanat.api.dag.StopBlinkJobNode;
import com.aliyun.wormhole.qanat.api.service.BlinkService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Stop Blink任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatStopBlinkJobProcessor extends AbstractQanatNodeJobProcessor<StopBlinkJobNode> {
    
    @Resource
    private BlinkService blinkService;
    
    @Override
    void doProcess(Map<String, Object> instParamsMap, StopBlinkJobNode node) {
        String tenantId = String.valueOf(instParamsMap.get("tenantId"));
        String appName = String.valueOf(instParamsMap.get("appName"));
        String[] jobNames = node.getJobNames().split(",");
        for (String jobName : jobNames) {
        	try {
        		blinkService.stopJob(tenantId, appName, jobName);
        	} catch (Exception e) {
        		log.error("stopJob({}) failed", jobName, e);
        	}
        }
    }
}