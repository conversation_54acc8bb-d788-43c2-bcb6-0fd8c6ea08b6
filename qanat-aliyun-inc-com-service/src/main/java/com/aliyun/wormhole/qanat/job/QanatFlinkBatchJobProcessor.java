package com.aliyun.wormhole.qanat.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.FlinkBatchNode;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import com.aliyun.wormhole.qanat.service.flink.FlinkService;
import com.taobao.ateye.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Flink任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatFlinkBatchJobProcessor extends AbstractQanatNodeJobProcessor<FlinkBatchNode> {
    
    @Resource
    private FlinkService flinkService;
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Override
    void doProcess(Map<String, Object> instParamsMap, FlinkBatchNode flink) {
        try {
            Long taskInstId = Long.valueOf(String.valueOf(instParamsMap.get("taskInstId")));
            JSONObject execParamJson = null;
            if (taskInstId != null) {
            	TaskInstance taskInst = taskInstanceMapper.selectByPrimaryKey(taskInstId);
            	execParamJson = JSON.parseObject(taskInst.getExecParam());
            }
            String tenantId = String.valueOf(instParamsMap.get("tenantId"));
            String appName = String.valueOf(instParamsMap.get("appName"));

            Map<String, String> flinkParams = null;
            if (execParamJson != null) {
                JSONObject dataJson = execParamJson.getJSONObject("data");
                if (dataJson != null && CollectionUtils.isNotEmpty(dataJson.keySet())) {
                    flinkParams = new HashMap<>();
                    for (String key : dataJson.keySet()) {
                        flinkParams.put(key, dataJson.getString(key));
                    }
                }
            }
            log.info("blinkPamams={}", JSON.toJSONString(flinkParams));

            flinkService.stopJob(tenantId, appName, flink.getJobName());
            log.info("Flink任务:{} 已停止", flink.getJobName());
            flinkService.startBatchJob(tenantId, appName, flink.getJobName(), flinkParams);
            log.info("Flink任务:{} 已启动，变量:{}", flink.getJobName(), JSON.toJSONString(flinkParams));
        } catch (QanatBizException e) {
            log.error("Flink任务调度异常:{}", e.getMessage());
            throw new QanatBizException(e.getMessage());
        } catch (Exception e) {
            log.error("Flink任务调度异常", e);
            throw new QanatBizException(e.getMessage());
        }
    }

    @Override
    public void doKill(Map<String, Object> instParamsMap, FlinkBatchNode flink) {
        try {
            String tenantId = String.valueOf(instParamsMap.get("tenantId"));
            String appName = String.valueOf(instParamsMap.get("appName"));

            flinkService.stopJob(tenantId, appName, flink.getJobName());
        } catch (Exception e) {
            log.error("Kill Flink任务异常:{}", e.getMessage(), e);
            throw new QanatBizException(e.getMessage());
        }
    }
}