package com.aliyun.wormhole.qanat.service.mdp;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.aliyun.tag.api.request.ListTagByObjectRequest;
import com.aliyun.tag.api.response.ListResponse;
import com.aliyun.tag.api.service.IQueryTagMetaService;
import com.aliyun.tag.api.vo.SimpleTagVO;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.service.adapter.RtdwViewModelTaskServiceAdapter;
import com.aliyun.wormhole.qanat.service.metaq.MetaQProducer;
import com.taobao.ateye.util.reflect.StringUtils;
import com.taobao.metaq.client.MetaPushConsumer;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MdpObjectFieldChangeListener {
    
    @Resource
    private RtdwViewModelTaskServiceAdapter rtdwViewModelTaskService;
    
    @Resource
    private MetaQProducer metaQProducer;
    
    @Value("${qanat.unit.id}")
    private String tenantId;

    @HSFConsumer(clientTimeout=30000)
    private IQueryTagMetaService mdpService;
    
    @PostConstruct
    private void init() {
    	MetaPushConsumer consumer = new MetaPushConsumer("CID-qanat-mdp");
    	try {
	        consumer.subscribe("TOPIC_META_DATA_CHANGE", "TAG");
			consumer.setConsumeThreadMin(1);
			consumer.setConsumeThreadMax(10);
			consumer.setPullInterval(500);
			
	        consumer.setMessageListener(new MessageListenerConcurrently() {
	            @Override
	            public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
	                for (MessageExt msg : msgs) {
	                    try {
	                        String msgBody = new String(msg.getBody(), "utf-8");
	                        log.info("msg={}", msgBody);
	                        JSONObject json = JSON.parseObject(msgBody);
	                        String objectUniqueCode = json.getJSONObject("extParam").getString("objectUniqueCode");
	                        String fieldUniqueCode = json.getString("uniqueCode");
	                        String operateType = json.getJSONObject("extParam").getString("operate");
	                        String objectType = json.getJSONObject("extParam").getString("objectType");
	                        Integer isRef = json.getJSONObject("extParam").getJSONObject("simpleTagVO").getInteger("isQuote");
	                        String tagMarkerSelector = json.getJSONObject("extParam").getJSONObject("simpleTagVO").getString("tagMarkerSelector");
	                        if (StringUtils.isBlank(tagMarkerSelector) || !tagMarkerSelector.contains("ADB")) {
	                        	continue;
	                        }
	                        if ("CREATE".equalsIgnoreCase(operateType) || "UPDATE".equalsIgnoreCase(operateType) || "ACTIVE".equalsIgnoreCase(operateType)
	                        		|| "ACTIVATE".equalsIgnoreCase(operateType)) {
	                			ListTagByObjectRequest req = new ListTagByObjectRequest();
	                			req.setObjectType(objectType);
	                			req.setObjectUniqueCode(objectUniqueCode);
	                			ListResponse<SimpleTagVO> mdpResult = mdpService.listTagByObject(req);
	                			log.info("listTagByObject({},{})={}",objectType, objectUniqueCode, JSON.toJSONString(mdpResult));
	                			if (mdpResult != null && CollectionUtils.isNotEmpty(mdpResult.getData())) {
	                				List<SimpleTagVO> tagVoList = mdpResult.getData().stream().filter(item -> fieldUniqueCode.equals(item.getUniqueCode())).collect(Collectors.toList());
	                				if (CollectionUtils.isEmpty(tagVoList)) {
	                					throw new QanatBizException("field is not found from mdp");
	                				}
									SimpleTagVO tag = tagVoList.get(0);
									if (isRef == 1) {
										if (org.apache.commons.lang3.StringUtils.isNotBlank(tag.getExtInfo())) {
											JSONObject extInfoJson = JSON.parseObject(tag.getExtInfo());
											if (extInfoJson == null || org.apache.commons.lang3.StringUtils.isBlank(extInfoJson.getString("dsName"))) {
												isRef = 0;
											}
										} else {
											isRef = 0;
										}
									}
			                        DataResult<Boolean> result = rtdwViewModelTaskService.reflectObjectFieldChange(tenantId, objectUniqueCode, fieldUniqueCode, isRef, operateType, JSON.toJSONString(tag));
			                        log.info("reflect object field change result:{}", result != null ? result.getData() : null);
		                			if (result == null || !result.getSuccess() || result.getData() == null || !result.getData()) {
		                				throw new QanatBizException("object:" + objectUniqueCode + " field:" + fieldUniqueCode + " failed");
		                			}
	                			}
	                        }
	                        //透传给下游
	                        metaQProducer.sendMsg("TOPIC_QANAT_DS_META_CHANGE", objectUniqueCode, fieldUniqueCode, msgBody);
	                    } catch (Exception e) {
	                        log.error("reflect object field change failed, message={}", e.getMessage(), e);
	    	                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
	                    }
	                }
	                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
	            }
	        });

	        consumer.start();
    	} catch(Exception e) {
    		log.error("create consumer for object field change failed", e);
    	}
    }
}