package com.aliyun.wormhole.qanat.process;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.dal.domain.Extension;
import com.aliyun.wormhole.qanat.dal.domain.ExtensionExample;
import com.aliyun.wormhole.qanat.dal.mapper.ExtensionMapper;
import com.aliyuncs.foas.model.v20181111.GetJobResponse;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 流任务资源包升级
 * <AUTHOR>
 * 2022年8月2日
 */
@Slf4j
@Component
public class StreamResPackRollingUpgradeProcessor extends JavaProcessor {
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private ExtensionMapper extensionMapper;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
            String tenantId = paramsJson.getString("tenantId");
            String appName = paramsJson.getString("appName");
            Boolean isRestart = paramsJson.getBoolean("restart");
            isRestart = isRestart == null ? false : isRestart;
            Integer backMins = paramsJson.getInteger("backMins");
            backMins = backMins == null ? 1 : backMins;
            String jobNames = paramsJson.getString("jobNames");
            String upgradeMode = paramsJson.getString("upgradeMode");
            JSONObject packJson = paramsJson.getJSONObject("packages");
            
            String blinkVersion = paramsJson.getString("blinkVersion");
            String clusterId = paramsJson.getString("clusterId");
            String queueName = paramsJson.getString("queueName");
            
        	List<String> jobNameList = Arrays.asList(jobNames.split(","));
        	
            if (CollectionUtils.isNotEmpty(jobNameList)) {
            	for (String jobName : jobNameList) {
            		log.info("start to process:{}", jobName);

            		try {
	                	String newPackages = null;
	            		if (packJson != null && CollectionUtils.isNotEmpty(packJson.keySet())) {
		            		String jobDetail = blinkService.getJobDetail(tenantId, appName, jobName);
		            		if (StringUtils.isBlank(jobDetail) || JSON.parseObject(jobDetail) == null) {
		            			log.error("failed to process:{} due to get empty details", jobName);
		            			continue;
		            		}
		            		GetJobResponse.Job job = JSON.parseObject(jobDetail, GetJobResponse.Job.class);
		            		String packages = job.getPackages();
		            		if (StringUtils.isBlank(packages)) {
		            			log.error("failed to process:{} due to get empty packages", jobName);
		            			continue;
		            		}
		            		String[] packArray = packages.split(",");
		            		List<String> newPackList = new ArrayList<>();
		            		for (String oldPack : packArray) {
		            			if ("overwrite".equalsIgnoreCase(upgradeMode)) {
		            				ExtensionExample example = new ExtensionExample();
		            				example.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andVersionEqualTo(oldPack).andTypeEqualTo("blink");
		            				List<Extension> exts = extensionMapper.selectByExample(example);
		            				if (CollectionUtils.isNotEmpty(exts)) {
			            				String code = exts.get(0).getCode();
			            				if (packJson.containsKey(code)) {
				            				newPackList.add(packJson.getString(code));
				            			} else {
				            				newPackList.add(oldPack);
				            			}
		            				} else {
				            			log.error("extension:{} is not found so skip", oldPack);
			            				newPackList.add(oldPack);
		            				}
		            			} else {
			            			if (packJson.containsKey(oldPack)) {
			            				newPackList.add(packJson.getString(oldPack));
			            			} else {
			            				newPackList.add(oldPack);
			            			}
		            			}
		            		}
		            		newPackages = StringUtils.join(newPackList, ",");
		            		log.info("job:{} oldPacks:{} to newPacks:{}", jobName, packages, newPackages);
	            		}

	            		if (StringUtils.isNotBlank(blinkVersion) || StringUtils.isNotBlank(newPackages)) {
	            			blinkService.updateJob(tenantId, appName, jobName, null, null, blinkVersion, null, newPackages);
	            			log.info("job:{} has been updated by newBlinkVersion:{}  newPacks:{}", jobName, blinkVersion, newPackages);
	            		}
	            		
	            		if (StringUtils.isNotBlank(queueName) && StringUtils.isNotBlank(clusterId)) {
	            			blinkService.modifyClusterQueue(tenantId, appName, jobName, clusterId, queueName);
		            		log.info("job:{} has been migrate to new cluster:{} queue:{}", jobName, clusterId, queueName);
	            		}
	            		
	
	            		boolean flag = blinkService.commitJob(tenantId, appName, jobName);
	        	        if (!flag) {
	                		log.error("failed to process:{} due to commit error", jobName);
	                		continue;
	        	        }
	            		log.info("job:{} has been committed", jobName);
	            		
	            		if (isRestart) {
	            			Calendar cal = Calendar.getInstance();
	            			cal.add(Calendar.MINUTE, -1*backMins);
	            			blinkService.restartJob(tenantId, appName, jobName, cal.getTime(), false);
	                		log.info("job:{} has been restarted from ts:{}", jobName, cal.getTime());
	            		}

	            		log.info("finish to process:{}", jobName);
            		} catch(Exception e) {
            			log.error("failed to process:{} due to {}", jobName, e.getMessage(), e);
            		}
            	}
            }
        } catch (Exception e) {
            log.error("StreamResPackRollingUpgradeProcessor任务调度异常", e);
            return new ProcessResult(false, e.getMessage());
        }
        return new ProcessResult(true);
    }
}