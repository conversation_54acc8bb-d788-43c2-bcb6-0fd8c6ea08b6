package com.aliyun.wormhole.qanat.job;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.DataSourceType;
import com.aliyun.wormhole.qanat.api.dag.HoloExtTblNode;
import com.aliyun.wormhole.qanat.api.dto.DatasourceRequest;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.dal.domain.*;
import com.aliyun.wormhole.qanat.dal.mapper.*;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.service.odps.OdpsClient;
import com.aliyun.wormhole.qanat.service.util.HoloUtils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;

/**
 * DataX任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatHoloExternalTableJobProcessor extends AbstractQanatNodeJobProcessor<HoloExtTblNode> {
    
    @Resource
    private DatasourceMapper datasourceMapper;
	
	@Resource
	private AppResourceRelationMapper appResourceRelationMapper;
	
	@Resource
	private ResourceMapper resourceMapper;

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private DsFieldInfoMapper dsFieldInfoMapper;
    
    @Resource
    private DatasourceService dsInfoService;

    @Resource
    private TaskInstanceMapper taskInstanceMapper;



    private void createHoloTable(String schemaName, String tableName, Map<String, String> colNameTypeMap, String pk, String url, String username, String password, String tableGroup) {
        List<String> colDefList = new ArrayList<>();
        for (String fieldName : colNameTypeMap.keySet()) {
            colDefList.add(fieldName + " " + HoloUtils.getHoloTypeFromOdps(colNameTypeMap.get(fieldName)));
        }
        String sql = null;
        if (pk == null) {
        	sql = String.format("Create Table %s ( %s );", schemaName + "." + tableName, StringUtils.join(colDefList, ","));
            sql += "call set_table_property('" + schemaName + "." + tableName + "', 'orientation', 'column');";
        } else {
        	sql = String.format("Create Table %s ( %s , primary key (%s) );", schemaName + "." + tableName, StringUtils.join(colDefList, ","), pk);
            sql += "call set_table_property('" + schemaName + "." + tableName + "', 'orientation', 'row,column');" +
            		"call set_table_property('" + schemaName + "." + tableName + "', 'distribution_key', '" + pk + "');";
        }
        if (StringUtils.isNotBlank(tableGroup)) {
            sql += "call set_table_property('" + schemaName + "." + tableName + "', 'table_group', '" + tableGroup + "');";
        }
        
        RdsConnectionParam param = new RdsConnectionParam();
        param.setUrl(url);
        param.setUserName(username);
        param.setPassword(password);
        Connection connection = null;
        Statement statement = null;
        try {
            connection = dsHandler.connectToTable(param);
            connection.setAutoCommit(false);
            statement = connection.createStatement();
            log.info("create table ddl:{}", sql);
            String[] subSqls = sql.split(";");
            for (String subSql : subSqls) {
                log.info("start to exec subSql:{}", subSql);
            	statement.execute(subSql);
            }
            connection.commit();
        } catch (Exception e) {
        	if (connection != null) {
	        	try {
					connection.rollback();
				} catch (SQLException e1) {
				}
        	}
            log.error("create holo table failed", e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {}
                statement = null;
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {}
                connection = null;
            }
        }
    }
    
    private void execHoloSql(String sql, String jdbcUrl, String username, String password) {
        RdsConnectionParam param = new RdsConnectionParam();
        param.setUrl(jdbcUrl);
        param.setUserName(username);
        param.setPassword(password);
        Connection connection = null;
        Statement statement = null;
        try {
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement();
            log.info("dml:{}", sql);
            statement.execute(sql);
        } catch (Exception e) {
            log.error("exec dml failed:{}", e.getMessage(), e);
            throw new QanatBizException("sql exec failed:" + e.getMessage());
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {}
                statement = null;
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {}
                connection = null;
            }
        }
    }
    
    private boolean createHoloExternalTable(String tableName, Map<String, String> colNameTypeMap, JSONObject srcDsMetaJson, JSONObject dstDbMetaJson) {
        List<String> colDefList = new ArrayList<>();
        for (String fieldName : colNameTypeMap.keySet()) {
            colDefList.add(fieldName + " " + HoloUtils.getHoloTypeFromOdps(colNameTypeMap.get(fieldName)));
        }
        if (srcDsMetaJson.getBoolean("noPartition") != null && srcDsMetaJson.getBoolean("noPartition")) {
        } else {
            colDefList.add((StringUtils.isNotBlank(srcDsMetaJson.getString("partitionKey")) ? srcDsMetaJson.getString("partitionKey") : "ds") + " text");
        }
        String dropSql = "drop FOREIGN table " + tableName + ";";
        String sql = "CREATE FOREIGN TABLE " + tableName + " ( " + StringUtils.join(colDefList, ",") + " ) SERVER odps_server " + 
        		" OPTIONS (project_name '" + srcDsMetaJson.getString("project") + "', table_name '" + srcDsMetaJson.getString("table") + "');"
        		;
        RdsConnectionParam param = new RdsConnectionParam();
        param.setUrl(dstDbMetaJson.getString("jdbcUrl"));
        param.setUserName(srcDsMetaJson.getString("accessId"));
        param.setPassword(srcDsMetaJson.getString("accessKey"));
        Connection connection = null;
        Statement statement = null;
        try {
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement();
            try {
	            log.info("drop table ddl:{}", dropSql);
	            statement.execute(dropSql);
            } catch(Exception e) {}
            log.info("create table ddl:{}", sql);
            statement.execute(sql);
            return true;
        } catch (Exception e) {
            log.error("create adb table failed", e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {}
                statement = null;
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {}
                connection = null;
            }
        }
        return false;
    }

	@Override
	void doProcess(Map<String, Object> instParamsMap, HoloExtTblNode node) {
        String tenantId = String.valueOf(instParamsMap.get("tenantId"));
        Long taskInstId = Long.valueOf(String.valueOf(instParamsMap.get("taskInstId")));

        //从主任务实例参数信息里获取是否有传递过来的分区信息
        TaskInstance taskInst = taskInstanceMapper.selectByPrimaryKey(taskInstId);
        JSONObject execParam = JSON.parseObject(taskInst.getExecParam());
        String notifyPt = null;
        if (execParam != null && execParam.containsKey("partition")) {
            String partition = execParam.getString("partition");
            notifyPt = partition.split("\\=")[1];
            log.info("notifyPt from odps partition event is {}", notifyPt);
        }
        
        String srcDsName = node.getSrcDsName();
        String dstDbName = node.getDstDbName();
        DatasourceExample example = new DatasourceExample();
        example.createCriteria().andDsNameEqualTo(srcDsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
        Datasource srcDs = datasourceMapper.selectByExampleWithBLOBs(example).get(0);
        String srcDsMeta = srcDs.getMeta();
        JSONObject srcDsMetaJson = JSON.parseObject(srcDsMeta);
        if (srcDsMetaJson != null && srcDsMetaJson.containsKey("maxPt")) {
            String partition = srcDsMetaJson.getString("maxPt");
            notifyPt = partition.split("\\=")[1];
            log.info("notifyPt from odps partition event is {}", notifyPt);
        }

        if (StringUtils.isBlank(srcDs.getDbName())) {
            throw new QanatBizException("dbName of ds:" + srcDsName + " is not config");
        }
        DbInfoExample dbExample = new DbInfoExample();
        dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(srcDs.getDbName()).andTenantIdEqualTo(tenantId);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("db:" + srcDs.getDbName() + " is not found");
        }
        DbInfo srcDbInfo = dbs.get(0);
        JSONObject srcDbMetaJson = JSON.parseObject(srcDbInfo.getMeta());
        if (srcDs.getDsType().equalsIgnoreCase(DataSourceType.ODPS.toString())) {
            srcDsMetaJson.put("project", srcDbMetaJson.getString("project"));
            srcDsMetaJson.put("accessId", srcDbMetaJson.getString("accessId"));
            srcDsMetaJson.put("accessKey", srcDbMetaJson.getString("accessKey"));
            srcDsMetaJson.put("odpsServer", srcDbMetaJson.getString("odpsServer"));
            srcDsMetaJson.put("table", srcDs.getTableName());
        } else {
        	log.info("only odps dsInfo supported");
            return;
        }

        DbInfoExample dstDbExample = new DbInfoExample();
        dstDbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dstDbName).andTenantIdEqualTo(tenantId);
        List<DbInfo> dstDbs = dbInfoMapper.selectByExampleWithBLOBs(dstDbExample);
        if (CollectionUtils.isEmpty(dstDbs)) {
            throw new QanatBizException("db:" + dstDbName + " is not found");
        }
        DbInfo dstDbInfo = dstDbs.get(0);
        JSONObject dstDbMetaJson = JSON.parseObject(dstDbInfo.getMeta());

        //刷新源表元数据
        DatasourceRequest dsInfoModReq = new DatasourceRequest();
        dsInfoModReq.setTenantId(tenantId);
        dsInfoModReq.setDsName(srcDsName);
        dsInfoModReq.setOperateEmpid("schedulerx2");
        dsInfoService.modifyDatasource(dsInfoModReq);
        
        DsFieldInfoExample fieldExample = new DsFieldInfoExample();
        fieldExample.createCriteria().andDsNameEqualTo(srcDsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
        List<DsFieldInfo> dsFields = dsFieldInfoMapper.selectByExample(fieldExample);

        if (CollectionUtils.isEmpty(dsFields)) {
        	throw new QanatBizException("dsFields of ds:" + srcDsName + " is empty");
        }
        
        List<String> pkList = new ArrayList<>();
        Map<String, String> colNameTypeMap = new HashMap<>();
        Map<String, String> colNameDescMap = new HashMap<>();
    	pkList = dsFields.stream().filter(item -> item.getIsPk() == 1).map(DsFieldInfo::getFieldName).collect(Collectors.toList());
    	List<String> colSelList = new ArrayList<>();
    	for (DsFieldInfo field : dsFields) {
    		colNameTypeMap.put(field.getFieldName(), field.getFieldType());
    		colNameDescMap.put(field.getFieldName(), field.getFieldDesc());
            colSelList.add(field.getFieldName());
    	}
        
        String srcTableName = srcDs.getTableName();
        String srcSchemaName = "public";
        String [] tokens = srcTableName.split("\\.");
        if (tokens.length == 2) {
            srcSchemaName = tokens[0];
            srcTableName = tokens[1];
        }
        String adbExtTblName = srcSchemaName + ".odps_" + srcTableName + "_external_table";
        
        OdpsClient client = new OdpsClient(srcDsMetaJson.getString("odpsServer"), srcDsMetaJson.getString("accessId"), srcDsMetaJson.getString("accessKey"),
	    		srcDsMetaJson.getString("project"), srcDsMetaJson.getString("mcUrl"), srcDsMetaJson.getString("mcToken"));
	    String maxPt = client.getMaxPt(srcTableName);
        if (notifyPt != null && maxPt.compareTo(notifyPt) < 0) {
            log.info("maxPt:{} is older than notifyPt:{}", maxPt, notifyPt);
            maxPt = notifyPt;
        }
        
        //创建外部表
        if (!createHoloExternalTable(adbExtTblName, colNameTypeMap, srcDsMetaJson, dstDbMetaJson)) {
        	throw new QanatBizException("External Table:" + adbExtTblName + " create failed");
        }
        String dstTableName = StringUtils.isNotBlank(node.getDstTableName()) ? node.getDstTableName() : srcDs.getTableName();
        String dstSchemaName = "public";
        tokens = dstTableName.split("\\.");
        if (tokens.length == 2) {
            dstSchemaName = tokens[0];
            dstTableName = tokens[1];
        }
    	SimpleDateFormat tsSdf = new SimpleDateFormat("yyMMddHHmm");
        String ts = tsSdf.format(new Date());
        String tmpDstTableName = "tmp_" + dstTableName + "_" + ts;
        String bakDstTableName = "bak_" + dstTableName + "_" + ts;
        
        //创建最终表
        createHoloTable(dstSchemaName, dstTableName, colNameTypeMap, CollectionUtils.isNotEmpty(pkList) ? pkList.get(0) : (StringUtils.isNotBlank(srcDs.getPkFields()) ? srcDs.getPkFields() : null), getDbConnectionUrl(dstDbMetaJson), dstDbMetaJson.getString("username"), dstDbMetaJson.getString("password"), dstDbMetaJson.getString("tableGroup"));
        //创建临时表表
        createHoloTable(dstSchemaName, tmpDstTableName, colNameTypeMap, CollectionUtils.isNotEmpty(pkList) ? pkList.get(0) : (StringUtils.isNotBlank(srcDs.getPkFields()) ? srcDs.getPkFields() : null), getDbConnectionUrl(dstDbMetaJson), dstDbMetaJson.getString("username"), dstDbMetaJson.getString("password"), dstDbMetaJson.getString("tableGroup"));
        
        log.info("srcTableName{},adbExtTblName{},dstTableName:{},tmpDstTableName:{},bakDstTableName:{}", srcTableName, adbExtTblName, dstTableName, tmpDstTableName, bakDstTableName);
            
        
        //执行外表数据load入
//        String bizDate = getBizDate(srcDsMetaJson.getString("Policy"));
        String dsExp = "";
        if (srcDsMetaJson.getBoolean("noPartition") != null && srcDsMetaJson.getBoolean("noPartition")) {
        	dsExp = " 1=1 ";
        } else {
        	dsExp = (srcDsMetaJson.containsKey("partitionKey") ? srcDsMetaJson.getString("partitionKey") : "ds") + "='" + maxPt + "'";
        }
        String sql = String.format("insert into %s ( %s ) select %s from %s where %s", dstSchemaName + "." + tmpDstTableName , StringUtils.join(colSelList, ","), StringUtils.join(colSelList, ","), adbExtTblName, dsExp);
        long beforeExecTs = System.currentTimeMillis();
        log.info("srcDbMetaJson={}",srcDbMetaJson);
        execHoloSql(sql, getDbConnectionUrl(dstDbMetaJson), srcDbMetaJson.getString("accessId"), srcDbMetaJson.getString("accessKey"));
        log.info("sql:{} exec cost:{} ms", sql, System.currentTimeMillis() - beforeExecTs);
        
        //Load执行结果校验
        int cnt = countTable(dstSchemaName + "." + tmpDstTableName, dstDbMetaJson);
		if (cnt == 0) {
			log.error("load 0 records into {} from {} with partition:{}", tmpDstTableName, adbExtTblName, dsExp);
			throw new QanatBizException("load 0 records into " + tmpDstTableName + " from " + adbExtTblName + " with partition:" + dsExp);
		}
        
		//临时表切换线上表
        Connection connection = null;
        Statement statement = null;
        try {
            RdsConnectionParam param = new RdsConnectionParam();
            param.setUrl(getDbConnectionUrl(dstDbMetaJson));
            param.setUserName(dstDbMetaJson.getString("username"));
            param.setPassword(dstDbMetaJson.getString("password"));
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement();
            try {
                statement.execute("ALTER TABLE " + dstSchemaName + "." + dstTableName + " RENAME TO " + bakDstTableName);
            } catch(Exception e) {}
            try {
                statement.execute("ALTER TABLE " + dstSchemaName + "." + tmpDstTableName + " RENAME TO " + dstTableName);
            } catch(Exception e) {}
            try {
                statement.execute("ALTER TABLE " + dstSchemaName + "." + bakDstTableName + " RENAME TO " + tmpDstTableName);
            } catch(Exception e) {}

            //刷新目标表元数据
            dsInfoService.updateDsInfoMeta(tenantId, dstDbName, dstTableName, "schedulerx2");
        } catch (Exception e) {
            log.error("AdbSql任务调度异常", e);
            throw new QanatBizException("ADB表操作失败");
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                }
                connection = null;
            }
        }
        log.info("Full Data Sync from {} to {} is finished", srcDsName, dstDbName);
	}
	
	private Integer countTable(String tableName, JSONObject dstDbMetaJson) {
    	int cnt = 0;
    	String sql = "select count(1) as total from " + tableName;
        log.info("before exec sql={}", sql);
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            RdsConnectionParam param = new RdsConnectionParam();
            param.setUrl(getDbConnectionUrl(dstDbMetaJson));
            param.setPassword(dstDbMetaJson.getString("password"));
            param.setUserName(dstDbMetaJson.getString("username"));
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
            	cnt = resultSet.getInt("total");
            }
            log.info("after exec sql cnt={}", cnt);
        } catch(Exception e) {
            log.error("execSql failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (Exception e) {
                }
                resultSet = null;
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (Exception e) {
                }
                statement = null;
            }
            if (connection != null) {
                try {
                	connection.close();
                } catch (Exception e) {
                }
                connection = null;
            }
        }
        return cnt;
    }
}