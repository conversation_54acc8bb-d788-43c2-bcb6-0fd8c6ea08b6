package com.aliyun.wormhole.qanat.process;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 管道实例ODS数据稽核任务
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class DatatubeAuthCheckProcessor extends JavaProcessor {

	@Resource
    private DbInfoMapper dbInfoMapper;

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource 
    private BlinkService blinkService;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            log.info("DatatubeInstanceOdsCheckProcessor, param=[]", context.getJobParameters());
            
            JSONObject paramsJson = JSON.parseObject(context.getJobParameters());
            String tenantId = paramsJson.getString("tenantId");
            String dbName = paramsJson.getString("dbName");
            JSONArray ruleArray = paramsJson.getJSONArray("rules");
            Integer intervalMins = paramsJson.getInteger("intervalMins");
            
            for (int i = 0; i < ruleArray.size(); i++) {
            	JSONObject ruleJson = ruleArray.getJSONObject(i);
        	    try {
                	log.info("start to process:{}", ruleJson.getString("jobName"));
                	
        	        Long posiResult = querySql(dbName, ruleJson.getString("posiSql"));
        	        Long nagResult = querySql(dbName, ruleJson.getString("nagSql"));
                	log.info("job:{} posiResult:{} nagResult:{}", ruleJson.getString("jobName"), posiResult, nagResult);

        	        if (posiResult > ruleJson.getLong("posiGapCnt") || nagResult > ruleJson.getLong("nagGapCnt")) {
                		log.error("datatube_stream_monitor|auth_sync_error|{}|鉴权数据同步疑似出现严重问题", ruleJson.getString("jobName"));
        				Calendar cal = Calendar.getInstance();
            			cal.add(Calendar.MINUTE, -1*intervalMins - 5);
            			blinkService.restartJob(tenantId, "qanat_pre", ruleJson.getString("jobName"), cal.getTime(), false);
            			continue;
        	        }
        	        if (nagResult > 0 || posiResult > 0) {
                		log.error("datatube_stream_monitor|auth_sync_warn|{}|鉴权数据同步疑似出现问题, posiResult:{} nagResult:{}，请关注", ruleJson.getString("jobName"), posiResult, nagResult);
        	        }
                	log.info("finish to process:{}", ruleJson.getString("jobName"));
        	    } catch (Exception e) {
        	        log.error("failed to process:{}, error:{}", ruleJson.getString("jobName"), e.getMessage(), e);
        	    }
            }
        } catch (QanatBizException e) {
            log.error("DatatubeAuthCheckProcessor任务调度异常:{}", e.getMessage());
            return new ProcessResult(false, e.getMessage());
        } catch (Exception e) {
            log.error("DatatubeAuthCheckProcessor任务调度异常", e);
            return new ProcessResult(false, e.getMessage());
        }
        return new ProcessResult(true);
    }

    private JSONObject getAdbDbMeta(String dbName) {
        DbInfoExample example = new DbInfoExample();
        example.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("no db found");
        }
        String dbMeta = dbs.get(0).getMeta();
        JSONObject dbMetaJson = JSON.parseObject(dbMeta);
        return dbMetaJson;
    }

    private Long querySql(String dbName, String sql) {
        Map<String, Long> data = new HashMap<>();
        log.info("before exec sql={}", sql);
        Statement statement = null;
        ResultSet resultSet = null;
	    Connection connection = null;
        try {
    	    JSONObject dbMetaJson = getAdbDbMeta(dbName);
        	RdsConnectionParam param = new RdsConnectionParam();
    	    param.setUrl(dbMetaJson.getString("jdbcUrl"))
    	        .setUserName(dbMetaJson.getString("username"))
    	        .setPassword(dbMetaJson.getString("password"));
        	connection = dsHandler.connectToTable(param);
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            Long startTs = System.currentTimeMillis();
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                return resultSet.getLong(1);
            }
            log.info("after exec sql data={} cost={}", JSON.toJSONString(data), System.currentTimeMillis() - startTs);
        } catch(Exception e) {
            log.error("querySql failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                }
                resultSet = null;
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
            if (connection != null) {
                try {
                	connection.close();
                } catch (SQLException e) {
                }
                connection = null;
            }
        }
        return null;
    }
    
    @Override
    public void kill(JobContext context) {
        
    }
}