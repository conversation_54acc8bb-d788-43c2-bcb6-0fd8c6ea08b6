package com.aliyun.wormhole.qanat.service.metaq.producer;

import com.alibaba.rocketmq.client.exception.MQClientException;
import com.taobao.metaq.client.MetaProducer;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Component
public class CheckFullProducer extends CheckProducer{

    @Override
    public String getGroup() {
        return "CID_qanat_check_full_topic";
    }

    @Override
    public String getInstanceName() {
        return "qanat_check_full_task";
    }

    @Override
    public String getTopic() {
        return "qanat_check_full_topic";
    }
}
