package com.aliyun.wormhole.qanat.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.BlinkBatchV3Node;
import com.aliyun.wormhole.qanat.api.dag.NodeAction;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelation;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.ResourceExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.mapper.AppResourceRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ResourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import com.aliyun.wormhole.qanat.service.blink.BlinkClient;
import com.aliyun.wormhole.qanat.service.blink.BlinkConf;
import com.taobao.ateye.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Blink任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatBlinkBatchV3JobProcessor extends AbstractQanatNodeJobProcessor<BlinkBatchV3Node> {

    @Resource
    private BlinkService blinkService;

    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Resource
    private AppResourceRelationMapper appResourceRelationMapper;

    @Resource
    private ResourceMapper resourceMapper;

    @Override
    void doProcess(Map<String, Object> instParamsMap, BlinkBatchV3Node blink) {
        try {
        	String operator = (String)instParamsMap.get("operator");
            Date startTime = new Date();
            String tenantId = String.valueOf(instParamsMap.get("tenantId"));
            String appName = String.valueOf(instParamsMap.get("appName"));

            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            if (!blinkClient.stopBatchJob(blink.getJobName())) {
                throw new QanatBizException("Blink批任务停止失败");
            }
            log.info("blink job[{}] has been stopped", blink.getJobName());
            
            Map<String, String> blinkPamams = new HashMap<>();
            Long taskInstId = Long.valueOf(String.valueOf(instParamsMap.get("taskInstId")));
            if (taskInstId != null) {
                TaskInstance taskInst = taskInstanceMapper.selectByPrimaryKey(taskInstId);
                JSONObject execParamJson = JSON.parseObject(taskInst.getExecParam());
                if (execParamJson != null) {
                    JSONObject dataJson = execParamJson.getJSONObject("data");
                    if (dataJson != null && CollectionUtils.isNotEmpty(dataJson.keySet())) {
                        blinkPamams = new HashMap<>();
                        for (String key : dataJson.keySet()) {
                            blinkPamams.put(key, dataJson.getString(key));
                        }
                    }
                }
            }
            log.info("blinkPamams={}", JSON.toJSONString(blinkPamams));

            Long blinkInstId = blinkClient.startBatchJob(blink.getJobName(), blinkPamams);
            while (true) {
                String instState = blinkService.getInstanceActualState(tenantId, appName, blink.getJobName(), blinkInstId);
                if ("SUCCESS".equalsIgnoreCase(instState)) {
                    break;
                } else if ("TERMINATED".equalsIgnoreCase(instState)
                		|| "FAILED".equalsIgnoreCase(instState)) {
                	throw new QanatBizException("Blink批任务执行失败或手动停止");
                }
                Thread.sleep(60000);//60s
            }
        } catch (QanatBizException e) {
            log.error("Blink批任务调度异常:{}", e.getMessage());
            throw new QanatBizException(e.getMessage());
        } catch (Exception e) {
            log.error("Blink批任务调度异常:{}", e.getMessage(), e);
            throw new QanatBizException(e.getMessage());
        }
    }

	@Override
    public void doKill(Map<String, Object> instParamsMap, BlinkBatchV3Node blink) {
        try {
            String tenantId = String.valueOf(instParamsMap.get("tenantId"));
            String appName = String.valueOf(instParamsMap.get("appName"));
            
            if (blink.getNodeAction().equals(NodeAction.STREAM)) {
                log.info("blink stream job[{}] skipped", blink.getJobName());
            	return;
            }

            BlinkClient blinkClient = new BlinkClient(getBlinkConfByAppName(tenantId, appName));
            blinkClient.stopBatchJob(blink.getJobName());
            log.info("blink job[{}] has been killed", blink.getJobName());
        } catch (Exception e) {
            log.error("Kill Blink批任务异常:{}", e.getMessage(), e);
            throw new QanatBizException(e.getMessage());
        }
    }

    private BlinkConf getBlinkConfByAppName(String tenantId, String appName) {
        AppResourceRelationExample example = new AppResourceRelationExample();
        example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andRelationTypeEqualTo("blink");
        List<AppResourceRelation> rels = appResourceRelationMapper.selectByExample(example);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(rels)) {
            throw new QanatBizException("no app resouces");
        }
        AppResourceRelation ref = rels.get(0);
        ResourceExample example1 = new ResourceExample();
        example1.createCriteria().andResourceNameEqualTo(ref.getResourceName()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L);
        List<com.aliyun.wormhole.qanat.dal.domain.Resource> resources = resourceMapper.selectByExampleWithBLOBs(example1);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(resources)) {
            throw new QanatBizException("no app resouces");
        }
        com.aliyun.wormhole.qanat.dal.domain.Resource resource = resources.get(0);
        JSONObject metaJson = new JSONObject();
        if (resource.getParentResourceId() != null) {
            com.aliyun.wormhole.qanat.dal.domain.Resource parentResource = resourceMapper.selectByPrimaryKey(resource.getParentResourceId());
            JSONObject parentMetaJson = JSON.parseObject(parentResource.getMeta());
            metaJson.putAll(parentMetaJson);
        }
        metaJson.putAll(JSON.parseObject(resource.getMeta()));
        BlinkConf conf = new BlinkConf();
        conf.setAccessId(metaJson.getString("accessId"));
        conf.setAccessKey(metaJson.getString("accessKey"));
        conf.setClusterId(metaJson.getString("clusterId"));
        conf.setEngineVersion(metaJson.getString("engineVersion"));
        conf.setPopRegionId(metaJson.getString("popRegionId"));
        conf.setProjectName(metaJson.getString("projectName"));
        conf.setQueueName(metaJson.getString("queueName"));
        conf.setRegionId(metaJson.getString("regionId"));
        conf.setUserId(metaJson.getString("userId"));
        conf.setProtocolType(metaJson.getString("protocolType"));
        conf.setIsAutoScaleOn(metaJson.getBoolean("isAutoScaleOn"));
        conf.setMaxCuPerJob(metaJson.getInteger("maxCuPerJob"));
        conf.setFetchDelaySec(metaJson.getInteger("fetchDelaySec"));
        return conf;
    }
}