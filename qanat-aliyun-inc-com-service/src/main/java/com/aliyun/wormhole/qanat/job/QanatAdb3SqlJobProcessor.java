package com.aliyun.wormhole.qanat.job;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastsql.DbType;
import com.alibaba.fastsql.sql.SQLUtils;
import com.alibaba.fastsql.sql.ast.SQLStatement;
import com.alibaba.fastsql.sql.dialect.hive.ast.HiveInsertStatement;
import com.alibaba.schedulerx.worker.domain.JobContext;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.Adb3SqlNode;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskScript;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskScriptMapper;
import com.aliyun.wormhole.qanat.service.dag.DagService;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * ADB3 SQL执行任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatAdb3SqlJobProcessor extends AbstractQanatNodeJobProcessor<Adb3SqlNode> {
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;
    
    @Resource
    private DatasourceMapper datasourceMapper;

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private DagService dagService;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private TaskScriptMapper taskScriptMapper;



	@Override
	void doProcess(Map<String, Object> instParamsMap, Adb3SqlNode node) {
        Connection connection = null;
        Statement statement = null;
        try {
            String tenantId = (String)instParamsMap.get("tenantId");
            String dbName = node.getDbName();
            if (StringUtils.isBlank(dbName) && StringUtils.isNotBlank(node.getDsName())) {
	            DatasourceExample example = new DatasourceExample();
	            example.createCriteria().andDsNameEqualTo(node.getDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
	            Datasource dstDs = datasourceMapper.selectByExampleWithBLOBs(example).get(0);
	            dbName = dstDs.getDbName();
            }

            if (StringUtils.isBlank(dbName)) {
            	throw new QanatBizException("dbName not exists");
            }
            DbInfoExample dbExample = new DbInfoExample();
            dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName).andTenantIdEqualTo(tenantId);
            List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
            if (CollectionUtils.isEmpty(dbs)) {
                throw new QanatBizException("db not found");
            }
            DbInfo dbInfo = dbs.get(0);
            JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
            
            RdsConnectionParam param = new RdsConnectionParam();
            param.setUrl(getDbConnectionUrl(dbMetaJson));
            param.setPassword(dbMetaJson.getString("password"));
            param.setUserName(dbMetaJson.getString("username"));
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement();
            SimpleDateFormat bizDateSdf = new SimpleDateFormat("yyyyMMdd");
            String bizDate = bizDateSdf.format(DateUtils.addDays(new Date(), -1));
            String bizDate1 = bizDateSdf.format(DateUtils.addDays(new Date(), -2));
            String sql = null;
            if (StringUtils.isNotBlank(node.getSql())) {
            	sql = node.getSql();
            } else if (node.getSqlId() != null) {
            	Long sqlId = node.getSqlId();
            	TaskScript tsInfo = taskScriptMapper.selectByPrimaryKey(sqlId);
            	if (tsInfo == null) {
                	throw new QanatBizException("even sql or sqlId is not configed");
                }
            	sql = tsInfo.getScript();
            } else {
            	throw new QanatBizException("even sql or sqlId is not configed");
            }
            sql = sql.replace("#bizDate#", bizDate).replace("#bizDate1#", bizDate1);
            log.info("dsName={},sql={}", node.getDsName(), sql);
            long startTs = System.currentTimeMillis();
            statement.execute(sql);
            log.info("sql exec finished using {} ms", System.currentTimeMillis()-startTs);
            
            boolean isSuccess = true;
            try {
            	DbType dbType = DbType.odps;
                List<SQLStatement> stmtList = SQLUtils.parseStatements(sql, dbType);
                if (stmtList.get(0) instanceof HiveInsertStatement) {
	                HiveInsertStatement stmt = (HiveInsertStatement)stmtList.get(0);
	                String targetTable = stmt.getTableName().getSimpleName();
	                if (StringUtils.isNotBlank(targetTable)) {
		                int total = countTable(connection, targetTable);
		                if (total == 0) {
		                	isSuccess = false;
		                }
	                }
                }
            } catch(Exception e) {
            	log.error("post check error:{}", e.getMessage());
            }
            if (!isSuccess) {
            	throw new QanatBizException("后置判断失败");
            }
        } catch (QanatBizException e) {
            log.error("AdbSql任务调度异常:{}", e.getMessage());
            throw new QanatBizException(e.getMessage());
        } catch (Exception e) {
            log.error("AdbSql任务调度异常", e);
            throw new QanatBizException(e.getMessage());
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                }
                connection = null;
            }
        }
    }
    
    private Integer countTable(Connection connection, String tableName) {
    	int cnt = 0;
    	String sql = "select count(1) as total from " + tableName;
        log.info("before exec sql={}", sql);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
            	cnt = resultSet.getInt("total");
            }
            log.info("after exec sql {} cnt={}", tableName, cnt);
        } catch(Exception e) {
            log.error("querySql failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                }
                resultSet = null;
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
        }
        return cnt;
    }
    
    @Override
    public void kill(JobContext context) {
        
    }
    
    public static void main(String[] args) {
    	String sql = "truncate table tmp_tag_meta_tag_object_biz_relation_note";

    	DbType dbType = DbType.odps;
        List<SQLStatement> stmtList = SQLUtils.parseStatements(sql, dbType);
        if (stmtList.get(0) instanceof HiveInsertStatement) {
	        HiveInsertStatement stmt = (HiveInsertStatement)stmtList.get(0);
	        System.out.println(stmt.getTableName().getSimpleName());
        } else {
	        System.out.println("null");
        }
    }
}