package com.aliyun.wormhole.qanat.service.viewmodel.v2;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.DagPolicy;
import com.aliyun.wormhole.qanat.api.dto.ResourcePackage;
import com.aliyun.wormhole.qanat.api.dto.TaskInfoRequest;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.FlowCtlService;
import com.aliyun.wormhole.qanat.api.service.TaskService;
import com.aliyun.wormhole.qanat.dal.domain.AppInfo;
import com.aliyun.wormhole.qanat.dal.domain.AppInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelTaskRelation;
import com.aliyun.wormhole.qanat.dal.mapper.AppInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsFieldInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelTaskRelationMapper;
import com.aliyun.wormhole.qanat.service.viewmodel.FullLinkProcessor;
import com.aliyun.wormhole.qanat.service.viewmodel.LookupProcessor;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.DataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.RelatedDataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Settings;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelOptimizer;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelSqlBuilder;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class TableObjectProcessorV2 {
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private AppInfoMapper appInfoMapper;
    
    @Resource
    private DatasourceService dsInfoService;
    
    @Resource
    private ViewModelSqlBuilder viewModelSqlBuilder;
    
    @Resource
    private TaskService taskService;
    
    @Resource
    private DsFieldInfoMapper dsFieldInfoMapper;
    
    @Resource
    private ViewModelTaskRelationMapper viewModelTaskRelationMapper;
    
    @Resource
    private LookupProcessor lookupProcessor;
    
    @Resource
    private FullLinkProcessor fullLinkProcessor;
    
    @Resource
    private ViewModelOptimizer viewModelOptimizer;
	
	@Resource
	private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;
	
	@Resource
	private FlowCtlService flowCtlService;
    
    @Value("${datatube.codegen.version}")
    private String codegenVersion;
	
	public static String BLINK_CHECK_COMPUTE_FOR_FULL_COLUMN_SQL = 
			"\n" + 
			"create view v_check as\n" + 
			"select \n" + 
			"    a.%s as id,\n" + 
			"    'fcbatch' as src,\n" + 
			"    checkData('%s',\n" + 
			"    qanatConcat('|', %s),\n" + 
			"    qanatConcat('|', %s)) as msg\n" + 
			"from v_correct as a\n" + 
			"LEFT JOIN LATERAL TABLE (queryDim('%s', 'select %s from %s where %s=?', a.%s)) as c(x) ON TRUE\n" + 
			";\n" + 
			"\n" + 
			"create view v_check_result as \n" + 
			"select (case when msg='' then concat_ws('|', '%s', id, src, 'OK') else concat_ws('|', '%s', id, src, msg) end) as msg, id as `key`\n" + 
			"from v_check;\n" +  
			"\n" + 
			"CREATE TABLE full_link_sink (\n" + 
			"  trace_id varchar,\n" + 
			"  pk bigint,\n" +
			"  key varchar,\n" + 
			"  ts bigint,\n" + 
			"  db varchar,\n" + 
			"  msg varchar,\n" +
			"  gmt_create timestamp\n" + 
			") WITH (\n" + 
			"  %s" + 
			");\n" + 
			"\n" + 
			"insert into full_link_sink\n" + 
			"select\n" + 
			"  UUID() as trace_id,\n" + 
			"  key as pk,\n" +
			"  split_index (msg, '|', 1) as key,\n" + 
			"  NOW() as ts,\n" + 
			"  '%s' as db,\n" + 
			"  msg,\n" + 
			"  CURRENT_TIMESTAMP as gmt_create\n" + 
			"from v_check_result;\n"
			;
	
	public static String BLINK_FULLCOLUMN_BATCH_CHECK_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--********************************************************************--\n" + 
			"CREATE FUNCTION checkData AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDataCheckUdf';\n" + 
			"CREATE FUNCTION qanatConcat AS 'com.aliyun.wormhole.qanat.blink.udf.QanatConcatUdf';\n" + 
			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" +
			"CREATE FUNCTION groovyFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatFunctionUdf';\r\n" +
			"CREATE FUNCTION dfaasFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDfaasFunctionUdf';\r\n" +
			"CREATE TABLE check_all_source (\n" + 
			"  %s,\n" + 
			"  primary key(%s)\n" +
			") WITH (\n" + 
			"    %s\n" + 
			");\n" + 
			"\n" + 
			"create view v_main_obj as\n" + 
			"select \n" + 
			"    %s\n" +
			"from check_all_source;\n" + 
			"\n" + 
			"create view v_correct as\n" + 
			"%s;\n" + 
			"%s";
    
    public boolean processMainObjectIncrSyncJob(String tenantId, String appName, String jobName, JSONObject srcDsMetaJson, String etlDbName, String tableName, DataObject object, String operateEmpid, Long versionId, String datatubeLevel, Long datatubeInstId, ViewModel dataModel) {
    	if (srcDsMetaJson.getJSONObject("odsConf") == null) {
    		log.info("{} has no ods conf", srcDsMetaJson.getString("dsName"));
    		return false;
    	}
		flowCtlService.setFlowControlIfNotExists(datatubeInstId, jobName, 1.0);
        int parallel = viewModelOptimizer.getParallel(tenantId, datatubeLevel, srcDsMetaJson.getInteger("qph"));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> columns = new ArrayList<>();
        List<String> refColumnsDefines = new ArrayList<>();
        List<String> selectColumns = new ArrayList<>();
        List<String> columnDefines = new ArrayList<>();
        String pkColumn = null;
        for (ViewModel.Field field : object.getFields()) {
        	if (field.getObject() != null) {
        		continue;
        	}
        	if (field.isFunc()) {
        		continue;
        	}
        	if ("null".equalsIgnoreCase(field.getRef())) {
        		continue;
        	}
        	if (field.getRef() != null && field.isCompute()) {
        		List<String> tokens = lookupProcessor.parseFuncExpress(field.getRef());
    			String funcCode = null;
        		if (CollectionUtils.isNotEmpty(tokens)) {
        			funcCode = tokens.get(0);
        		} else {
        			funcCode = field.getRef().split("\\(")[0];
        		}
        		if ("json_extract".equalsIgnoreCase(funcCode)) {
        			String col = field.getRef().replace("json_extract", "JSON_VALUE");
	        		if ("varchar".equalsIgnoreCase(field.getType())) {
	        			refColumnsDefines.add(col + " as " + field.getCode());
		        	} else {
		        		refColumnsDefines.add("CAST(" + col + " AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") as " + field.getCode());
		        	}
        		} else if (field.getRef().toLowerCase().startsWith("concat_ws")) {
        			String ref = field.getRef();
        			refColumnsDefines.add(ref + " as " + field.getCode());
        		} else if ("from_unixtime".equalsIgnoreCase(funcCode) && "datetime".equalsIgnoreCase(field.getType())) {
        			String param0 = field.getRef().split("\\(")[1].split(",")[0];
        			String param1 = field.getRef().split("\\(")[1].split(",")[1];
        			String ref = "from_unixtime(CAST(" + param0 + " AS BIGINT)," + param1.replace("%Y", "yyyy").replace("%m", "MM").replace("%d", "dd").replace("%h", "HH").replace("%i", "mm").replace("%s", "ss");
        			refColumnsDefines.add("CAST(" + ref + " AS timestamp) as " + field.getCode());
        		}
	        	selectColumns.add("`" + field.getCode() + "`");
	        	columnDefines.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
        		continue;
        	}
        	if ("'null'".equalsIgnoreCase(field.getRef())) {
        		continue;
        	}
        	if (field.isPk()) {
        		pkColumn = field.getCode();
        	}
        	columns.add("`" + field.getRef() + "`");
        	refColumnsDefines.add("`" + field.getRef() + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
        	selectColumns.add("`" + field.getRef() + "` AS `" + field.getCode() + "`");
        	columnDefines.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
        }
        String sql = "--SQL\n" + 
    			"--********************************************************************--\n" + 
    			"--Author: " + operateEmpid + "\n" + 
    			"--CreateTime: " + sdf.format(new Date()) + "\n" + 
    			"--Comment: " + ("sync for " + tableName + " from " + object.getCode()) + "\n" + 
    			"--Version: " + codegenVersion + "\n" + 
    			"--********************************************************************--\n" + 
    			"create table holobinlog_source (\n" + 
    			"    hg_binlog_lsn BIGINT,\n" +
    			"    hg_binlog_event_type BIGINT,\n" +
    			"    hg_binlog_timestamp_us BIGINT,\n" +
    			"   " + StringUtils.join(refColumnsDefines, ",\n") + "\n" + 
    			") with (\n" + 
    			"  type = 'hologres',\n" + 
    			"  dbname = '" + srcDsMetaJson.getJSONObject("odsConf").getString("database") + "',\n" + 
    			"  tablename = '" + srcDsMetaJson.getJSONObject("odsConf").getString("tableName") + "',\n" + 
    			"  username = '" + srcDsMetaJson.getJSONObject("odsConf").getString("username") + "',\n" +
    			"  password = '" + srcDsMetaJson.getJSONObject("odsConf").getString("password") + "',\n" +
    			"  endpoint = '" + srcDsMetaJson.getJSONObject("odsConf").getString("endpoint") + "',\n" +
    			"  binlog = 'true',\n" +
    			"  binlogMaxRetryTimes = '10',\n" +
    			"  binlogRetryIntervalMs = '500',\n" +
    			"  binlogBatchReadSize = '256'\n" +
    			");\n" +
    			"\n" +
    			"CREATE FUNCTION trace AS 'com.aliyun.wormhole.qanat.blink.udf.QanatTraceUdf';\n" +
    			"CREATE FUNCTION flowCtl AS 'com.aliyun.wormhole.qanat.blink.udf.QanatFlowControlUdf';\n" +
    			"CREATE FUNCTION hololog AS 'com.aliyun.wormhole.qanat.blink.udf.QanatHoloLogTUdf';\n" +
    			"\n" + 
    			"create view v_source as \n" + 
    			"select \n" + 
    			"    hg_binlog_lsn,\n" + 
    			"    hg_binlog_event_type,\n" + 
    			"    hg_binlog_timestamp_us,\n" + 
    			"    " + StringUtils.join(columns, ",\n") + ",\n" + 
    			"    trace(UUID(),hg_binlog_lsn,hg_binlog_event_type,hg_binlog_timestamp_us," + pkColumn + ",'" + jobName + "','holobinlog') as __trace_id__\n" + 
    			"from holobinlog_source;\n" + 
    			"\n" + 
    			"create view v_sink as \n" + 
    			"select \n" + 
    			"    hg_binlog_lsn,\n" + 
    			"    hg_binlog_event_type,\n" + 
    			"    hg_binlog_timestamp_us,\n" + 
    			"    " + StringUtils.join(columns, ",\n") + ",\n" + 
    			"    flowCtl(__trace_id__,'" + jobName + "',cast(" + pkColumn + " as varchar)) as __trace_id__,\n" +
    			"    hololog(__trace_id__,hg_binlog_lsn,hg_binlog_event_type,hg_binlog_timestamp_us,'rowdata'," + StringUtils.join(columns, ",") + ") as __haslog__\n" +
    			"from v_source;\n" + 
    			"\n" ;
		
			sql += "create table upsert_sink (\n" + 
    			"    " + StringUtils.join(columnDefines, ",") + ",\n" + 
    			"    __trace_id__ varchar,\n" + 
    			"    primary key(" + (StringUtils.isNotBlank(dataModel.getSettings().getDistributeKey()) && !pkColumn.equalsIgnoreCase(dataModel.getSettings().getDistributeKey()) ? (pkColumn + "," + dataModel.getSettings().getDistributeKey()) : pkColumn) + ")\n" + 
    			") with (\n" + 
    			"    type = 'QANAT_ADB30',\n" + 
    			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
    			"    dbName='" + etlDbName + "',\n" + 
    			"    tableName='" + tableName + "',\n" + 
    			"    replaceMode = 'upsert',\n" + 
    			"    writeMode = 'single',\n" +
	    		"    streamEvent = 'disable'\n" +
    			");\n" + 
    			"\n" +
    			"create table del_sink (\n" + 
    			"    " + pkColumn + " bigint,\n" + 
    			"    __trace_id__ varchar,\n" + 
    			"    primary key(" + pkColumn + ")\n" + 
    			") with (\n" + 
    			"    type = 'QANAT_ADB30',\n" + 
    			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
    			"    dbName='" + etlDbName + "',\n" + 
    			"    tableName='" + tableName + "',\n" + 
    			"    replaceMode = 'delete_by_pk',\n" + 
    			"    writeMode = 'single',\n" + 
    			"    streamEvent = 'disable'\n" + 
    			");" +
    			"\n" + 
    			"insert into upsert_sink select " + StringUtils.join(selectColumns, ",") + ",__trace_id__ from v_sink where hg_binlog_event_type in (5,7);\n" +
    			"\n" + 
    			"insert into del_sink select " + pkColumn + ",__trace_id__ from v_sink where hg_binlog_event_type=2 " + ((StringUtils.isNotBlank(object.getFilter()) ? ("or (" + object.getFilter() + ")=false") : " or 1=2") + (StringUtils.isNotBlank(srcDsMetaJson.getString("streamFilter")) ? (" or (" + srcDsMetaJson.getString("streamFilter") + ")=false") : " or 2=1")) + " ;\n" +
    			"\n";
        
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName + "/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDF), parallel);
        
        DatatubeInstanceTask record = new DatatubeInstanceTask();
        record.setCreateEmpid(operateEmpid);
        record.setDatatubeInstId(datatubeInstId);
        record.setGmtCreate(new Date());
        record.setGmtModified(new Date());
        record.setIsDeleted(0L);
        record.setModifyEmpid(operateEmpid);
        record.setTaskName(jobName);
        record.setTaskScript(sql);
        record.setTaskType("blink_stream");
        record.setTenantId(tenantId);
        record.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(record);
        
        return true;
    }

    public boolean processFullColumnBatchCheckJob(String tenantId, String appName, String jobName, String dstDbName, String tableName, String operateEmpid, Long versionId, JSONObject kafkaJson, ViewModel dataModel, Long datatubeInstId) {
    	JSONObject srcDsMetaJson = dsInfoService.getDbMetaByDsName(tenantId, dataModel.getObject().getRef());

    	String checkResultTopicName = "bchk-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dstDbName) + "-" + dataModel.getObject().getCode();
    	String correctTopicName = "bcrt-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dstDbName) + "-" + dataModel.getObject().getCode();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> mainObjColsDef = new ArrayList<>();
    	List<String> mainObjColsSel = new ArrayList<>();
        String pkField = dataModel.getObject().getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0).getRef();
    	for (ViewModel.Field field : dataModel.getObject().getFields()) {
    		if (field.getObject() != null || field.isFunc() || "null".equalsIgnoreCase(field.getRef())) {
    			continue;
    		}
    		if (field.getRef() != null && field.isCompute()) {
    			String funcCode = field.getRef().split("\\(")[0];
        		if ("json_extract".equalsIgnoreCase(funcCode)) {
        			if ("varchar".equalsIgnoreCase(field.getType())) {
    					mainObjColsSel.add(field.getRef().replace("json_extract", "JSON_VALUE") + " AS " + field.getCode());
    				} else {
    					mainObjColsSel.add("CAST(" + field.getRef().replace("json_extract", "JSON_VALUE") + " AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " ) AS " + field.getCode());
    				}
        		} else if (field.getRef().toLowerCase().startsWith("concat_ws")) {
        			if ("varchar".equalsIgnoreCase(field.getType())) {
    					mainObjColsSel.add(field.getRef() + " AS " + field.getCode());
    				} else {
    					mainObjColsSel.add("CAST(" + field.getRef() + " AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " ) AS " + field.getCode());
    				}
        		} else if ("from_unixtime".equalsIgnoreCase(funcCode) && "datetime".equalsIgnoreCase(field.getType())) {
        			String param0 = field.getRef().split("\\(")[1].split(",")[0];
        			String ref = field.getRef().replace(param0, "CAST(" + param0 + " AS BIGINT)");
        			mainObjColsSel.add("CAST(" + ref.replace("%Y", "yyyy").replace("%m", "MM").replace("%d", "dd").replace("%h", "HH").replace("%i", "mm").replace("%s", "ss") + " AS datetime) AS " + field.getCode());
        		}
    			continue;
    		}
			mainObjColsDef.add("`" + field.getRef() + "` " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
			if ("varchar".equalsIgnoreCase(field.getType())) {
				mainObjColsSel.add(field.getRef() + " AS " + field.getCode());
			} else {
				mainObjColsSel.add("CAST(" + field.getRef() + " AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " ) AS " + field.getCode());
			}
    	}
        String sql = String.format(BLINK_FULLCOLUMN_BATCH_CHECK_SQL
            , operateEmpid
            , sdf.format(new Date())
            , "full columns batch check for " + tableName + " from " + dataModel.getObject().getCode()
            , StringUtils.join(mainObjColsDef, ",")
            , pkField
            , checkAllScan(dataModel.getSettings(), srcDsMetaJson, dstDbName)
            , StringUtils.join(mainObjColsSel, ",")
            , viewModelSqlBuilder.getStreamSelectSql(tenantId, dataModel, dstDbName)
            , getFullColumnCheckComputeSql(tenantId, appName, srcDsMetaJson, dstDbName, tableName, kafkaJson, correctTopicName, checkResultTopicName, dataModel)
            );
        
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_HSF_UDF, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_UDF, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_MYSQL_SCAN), true);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_batch");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        
    	return true;
    }
    
    private String getFullColumnCheckComputeSql(String tenantId, String appName, JSONObject srcDsMetaJson, String dbName, String tableName, JSONObject kafkaJson, String correctTopicName, String checkResultTopicName, ViewModel dataModel) {
        List<String> colNameList = new ArrayList<>();
        List<String> colNameSelList = new ArrayList<>();
        List<String> colDefList = new ArrayList<>();
        List<String> colNameWithAliasBList = new ArrayList<>();
        List<String> colNameWithAliasCList = new ArrayList<>();
        List<String> colNameWithAliasForCorrectList = new ArrayList<>();
        String pkField = dataModel.getObject().getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0).getCode();
        List<ViewModel.Field> columnList = new ArrayList<>();
		columnList.addAll(dataModel.getObject().getFields());
		if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
			for (ViewModel.RelatedDataObject object : dataModel.getRelatedObjects()) {
				List<String> joinOnFields = object.getRelations().stream().map(e -> e.getField()).collect(Collectors.toList());
				
				for (ViewModel.Field field : object.getFields()) {
					if (joinOnFields.contains(field.getCode())) {
						continue;
					}
					columnList.add(field);
				}
			}
		}
        for (ViewModel.Field field : columnList) {
        	if ("null".equalsIgnoreCase(field.getRef())) {
        		continue;
        	}
        	colDefList.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
        	colNameList.add(field.getCode());
        	colNameWithAliasForCorrectList.add("a." + field.getCode());
        	colNameSelList.add(("datetime".equalsIgnoreCase(field.getType()) ? ("DATE_FORMAT(" + field.getCode() + ",''%Y-%m-%d %H:%i:%s'')") : field.getCode()) + " AS " + field.getCode());
            colNameWithAliasBList.add("datetime".equalsIgnoreCase(field.getType()) ? ("DATE_FORMAT(a." + field.getCode() + ",'yyyy-MM-dd HH:mm:ss')") : ("a." + field.getCode()));
            colNameWithAliasCList.add("JSON_VALUE(c.x, '$." + field.getCode() + "')");
        }
    	String sql = String.format(BLINK_CHECK_COMPUTE_FOR_FULL_COLUMN_SQL
    		, pkField
            , StringUtils.join(colNameList, ",")
            , StringUtils.join(colNameWithAliasBList, ",")
            , StringUtils.join(colNameWithAliasCList, ",")
            , dbName
            , StringUtils.join(colNameSelList, ",")
            , tableName
            , pkField
            , pkField
            , tableName
            , tableName
            , fullLinkProcessor.getFullLinkSinkWithClause(tenantId, appName)
            , checkResultTopicName
        	);
    	sql += "\n" + 
				"create table correct_sink (\n" + 
				"    " + viewModelSqlBuilder.getStreamDdlSql(tenantId, dataModel) + ",\n" + 
				"    primary key(" + pkField + ")\n" + 
				") with (\n" + 
				"    type = 'QANAT_ADB30',\n" + 
				"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
				"    dbName='" + dbName + "',\n" + 
				"    tableName='" + tableName + "',\n" + 
				"    replaceMode = 'replace',\n" + 
			    "    writeMode = 'single',\n" +
				"    streamType = 'kafka',\n" + 
				"    eventTopic = '" + correctTopicName + "',\n" + 
				"    eventServer = '" + kafkaJson.getString("dbName") + "'\n" + 
				");\n" + 
				"\n" +
				"create view v_to_correct as \n" + 
				"select `key` as " + pkField + "\n" + 
				"from v_check_result\n" + 
				"where split_index (msg, '|', 3) <> 'OK';\n" +
				"\n" +
				"insert into correct_sink\n" + 
				"select " + StringUtils.join(colNameWithAliasForCorrectList, ",") + " \n" + 
				"from v_correct as a \n" + 
				"join v_to_correct as b \n" + 
				"on a." + pkField + "=b." + pkField + ";\n" +
				"\n" + 
				"insert\n" + 
				"  into full_link_sink\n" + 
				"select\n" + 
				"  UUID() as trace_id,\n" + 
				"  a." + pkField + " as pk,\n" +
				"  CAST(a." + pkField + " AS varchar) as key,\n" + 
				"  NOW() as ts,\n" + 
				"  '" + correctTopicName + "' as db,\n" + 
				"  '' as msg,\n" + 
				"  CURRENT_TIMESTAMP as gmt_create\n" + 
				"from v_correct as a \n" + 
				"join v_to_correct as b \n" + 
				"on a." + pkField + "=b." + pkField + ";\n";
    	
    	return sql;
    }

	private String checkAllScan(ViewModel.Settings setting, JSONObject srcDsMetaJson, String dstDbName) {
		
		if ("tddl".equalsIgnoreCase(srcDsMetaJson.getString("dsType"))) {
			return 
		            "    type = 'custom',\n" + 
		            "    tableFactoryClass = 'com.aliyun.wormhole.qanat.blink.tddl.scan.TddlScanTableFactory',\n" + 
		            "    dbName='" + srcDsMetaJson.getString("dbName") + "',\n" +
		            "    tableName='" + srcDsMetaJson.getString("table") + "',\n" +
		            "    batchSize='" + setting.getRdsScanBatchSize() + "'" +
		            (setting.getBatchCheckDays() == 0 ? "" : (",\n    whereClause='" + setting.getBatchCheckDateField() + ">=''${" + getBatchChechStartDate(setting.getBatchCheckDays()) + "} 00:00:00'' and " + setting.getBatchCheckDateField() + "<''${curdate} 00:00:00'' '"))
		            ;
		} else {
			return 
	            "    type = 'custom',\n" + 
	            "    tableFactoryClass = 'com.aliyun.wormhole.qanat.blink.mysql.scan.MySQLScanTableFactory',\n" + 
	            "    dbName='" + srcDsMetaJson.getString("dbName") + "',\n" +
	            "    tableName='" + srcDsMetaJson.getString("table") + "',\n" +
	            "    batchSize='" + setting.getRdsScanBatchSize() + "'" +
	            (setting.getBatchCheckDays() == 0 ? "" : (",\n    whereClause='" + setting.getBatchCheckDateField() + ">=''${" + getBatchChechStartDate(setting.getBatchCheckDays()) + "} 00:00:00'' and " + setting.getBatchCheckDateField() + "<''${curdate} 00:00:00'' '"))
	            ;
		}
	}
	
	private String getBatchChechStartDate(int batchCheckDays) {
		if (batchCheckDays == 1) {
			return "bizdate";
		} else if (batchCheckDays == 3) {
			return "bizdatePre2";
		} else {
			return "bizdate";
		}
	}
    
    public String processBatchSyncJob(String tenantId, String appName, List<String> dbNames, String etlDbName, String tableName, DataObject object, String operateEmpid, Long versionId, String viewModelName, JSONObject kafkaJson, boolean isUpsert, Settings modelSettings, Long datatubeInstId) {
    	String jobName = "fullsync_" + getAppIdByName(tenantId, appName) + "_" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "_" + object.getCode() + "_v" + versionId;
    	JSONObject dsMetaJson = null;
    	String sql = null;
    	try {
    		dsMetaJson = dsInfoService.getOdpsTableMetaByOdsDsName(tenantId, object.getRef());
    	} catch(Exception e) {}
    	if (dsMetaJson == null) {
    		dsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, object.getRef());
    	}

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if ("odps".equalsIgnoreCase(dsMetaJson.getString("dsType"))) {
        	DsFieldInfoExample example = new DsFieldInfoExample();
        	example.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsNameEqualTo(dsMetaJson.getString("dsName"));
        	List<DsFieldInfo> dsFields = dsFieldInfoMapper.selectByExample(example);
        	if (CollectionUtils.isEmpty(dsFields)) {
        		throw new QanatBizException(dsMetaJson.getString("dsName") + " has no fields found");
        	}
        	Map<String, DsFieldInfo> dsFieldMap = dsFields.stream().collect(Collectors.toMap(DsFieldInfo::getFieldName, Function.identity()));
        	
        	List<String> columnDefines = new ArrayList<>();
	        String updateKeyField = null;
	        Map<String, String> pkMap = new HashMap<>();
	        for (ViewModel.Relation rel : ((RelatedDataObject)object).getRelations()) {
	        	if (!rel.getRelatedField().startsWith("exp#")) {
	        		updateKeyField = rel.getRelatedField().split("\\.")[1];
	        		pkMap.put(rel.getField(), updateKeyField);
	        	}
	        	break;
	        }
	        for (ViewModel.Field field : object.getFields()) {
	        	columnDefines.add("`" + field.getRef() + "` " + getBlinkType(dsFieldMap.get(field.getRef().toLowerCase()).getFieldType()));
	        }
	        List<String> columnDefines1 = new ArrayList<>();
	        List<String> columnSels = new ArrayList<>();
	        String pkColumn1 = ((RelatedDataObject)object).getRelations().get(0).getRelatedField().split("\\.")[1];
	        for (ViewModel.Field field : object.getFields()) {
	        	columnDefines1.add("`" + (pkMap.get(field.getCode()) != null ? pkMap.get(field.getCode()) : field.getCode()) + "` " + getBlinkType(field.getType()));
	        	String fieldType = field.getType();
	        	String odpsFieldType = dsFieldMap.get(field.getRef().toLowerCase()).getFieldType();
	        	if (getOdpsType(fieldType).equalsIgnoreCase(odpsFieldType) ) {
	        		columnSels.add(field.getRef() + " AS `" + (pkMap.get(field.getCode()) != null ? pkMap.get(field.getCode()) : field.getCode()) + "`");
	        	} else {
		        	columnSels.add("CAST(" + field.getRef() + " AS " + ("datetime".equalsIgnoreCase(fieldType) ? "timestamp" : fieldType) + ") AS `" + (pkMap.get(field.getCode()) != null ? pkMap.get(field.getCode()) : field.getCode()) + "`");
	        	}
	        }
	        sql = "CREATE TABLE odps_source (\n" + 
	    			"    " + StringUtils.join(columnDefines, ",") + "\n" + 
	    			") WITH (\n" + 
	    			"    type = 'odps',\n" + 
	    			"    endPoint = '" + dsMetaJson.getString("odpsServer") + "',\n" + 
	    			"    project='" + dsMetaJson.getString("project") + "',\n" + 
	    			"    tableName='" + dsMetaJson.getString("table") + "',\n" + 
	    			"    accessId='" + dsMetaJson.getString("accessId") + "',\n" + 
	    			"    accessKey='" + dsMetaJson.getString("accessKey")  + "',\n" + 
	    			"    `partition`='max_pt()'\n" + 
	    			");\n" + 
	    			"\n";
	        for (int i = 0; i < dbNames.size(); i++) {
	    		sql += "create table adb_sink_" + i + " (\n" + 
	    			"    " + StringUtils.join(columnDefines1, ",") + ",\n" + 
	    			"    primary key(" + pkColumn1 + ")\n" + 
	    			") with (\n" + 
	    			"    type = 'QANAT_ADB30',\n" + 
	    			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
	    			"    dbName='" + dbNames.get(i) + "',\n" + 
	    			"    tableName='" + tableName + "',\n" + 
	    			"    replaceMode = '" + (isUpsert ? "upsert" : "update") + "',\n" + 
	    			"    writeMode = 'single',\n" + 
	    			"    streamEvent = 'disable'\n" +
	    			");\n" + 
	    			"\n" + 
	    			"insert into adb_sink_" + i + "\n" + 
	    			"select \n" +
	    			StringUtils.join(columnSels, ",") + "\n" +
	    			"from odps_source;\n" +
	    			"\n";
	        }
	        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName + "/" + tableName + "/", 
	        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3), true, blinkService.getBatchPlanJson4OdpsSource(tableName, pkColumn1, dbNames.size()));
        } else {
	        List<String> columnDefines = new ArrayList<>();
	        String pkColumn = null;
	        String updateKeyField = null;
	        Map<String, String> pkMap = new HashMap<>();
	        for (ViewModel.Relation rel : ((RelatedDataObject)object).getRelations()) {
	        	if (!rel.getRelatedField().startsWith("exp#")) {
	        		updateKeyField = rel.getRelatedField().split("\\.")[1];
	        		pkMap.put(rel.getField(), updateKeyField);
	        	}
	        	break;
	        }
	        for (ViewModel.Field field : object.getFields()) {
	        	if (field.getObject() != null) {
	        		continue;
	        	}
	        	if (field.isPk()) {
	        		pkColumn = field.getRef();
	        	}
	        	columnDefines.add("`" + field.getRef() + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
	        }
	        List<String> columnDefines1 = new ArrayList<>();
	        String pkColumn1 = ((RelatedDataObject)object).getRelations().get(0).getRelatedField().split("\\.")[1];
	        for (ViewModel.Field field : object.getFields()) {
	        	columnDefines1.add("`" + (pkMap.get(field.getCode()) != null ? pkMap.get(field.getCode()) : field.getCode()) + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
	        }
	        sql = "--********************************************************************--\n" + 
	    			"--Author: " + operateEmpid + "\n" + 
	    			"--CreateTime: " + sdf.format(new Date()) + "\n" + 
	    			"--Comment: " + ("sync for " + tableName + " from " + object.getCode()) + "\n" + 
	    			"--********************************************************************--\n" + 
	    			"CREATE TABLE adb_source (\n" + 
	    			"    " + StringUtils.join(columnDefines, ",") + ",\n" + 
	    			"    primary key(" + pkColumn + ")\n" + 
	    			") WITH (\n" + 
	    			"    type = 'custom',\n" + 
	    			"    tableFactoryClass = 'com.aliyun.wormhole.qanat.blink.mysql.scan.MySQLScanTableFactory',\n" + 
	    			"    dbName='" + etlDbName + "',\n" + 
	    			"    tableName='" + dsMetaJson.getString("table") + "',\n" + 
	    			"    batchSize='" + modelSettings.getRdsScanBatchSize() + "'\n" + 
	    			");\n" + 
	    			"\n";
	        for (int i = 0; i < dbNames.size(); i++) {
	    		sql += "create table adb_sink_" + i + " (\n" + 
	    			"    " + StringUtils.join(columnDefines1, ",") + ",\n" + 
	    			"    primary key(" + pkColumn1 + ")\n" + 
	    			") with (\n" + 
	    			"    type = 'QANAT_ADB30',\n" + 
	    			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
	    			"    dbName='" + dbNames.get(i) + "',\n" + 
	    			"    tableName='" + tableName + "',\n" + 
	    			"    replaceMode = '" + (isUpsert ? "upsert" : "update") + "',\n" + 
	    			"    writeMode = 'single',\n" + 
	    			"    streamEvent = 'disable'\n" +
	    			");\n" + 
	    			"\n" + 
	    			"insert into adb_sink_" + i + "\n" + 
	    			"select * from adb_source;\n" +
	    			"\n";
	        }
	        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName + "/" + tableName + "/", 
	        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_MYSQL_SCAN), true, blinkService.getBatchPlanJson4DwSource(tableName, pkColumn1, dbNames.size()));
        }
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_batch");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
    	
    	if (StringUtils.isNotBlank(dsMetaJson.getString("timeExpression"))) {
    		String taskName = "DAG_refresh_" + jobName;
            Long taskId = taskService.isTaskExists(tenantId, taskName);
            if (taskId == null) {
	        	String taskTemplate = "Dag dag = new Dag(\"DAG_refresh_%s\");\r\n" + 
						"dag.setTimeExpression(\"%s\");\r\n" +
						"BlinkBatchNode fullSync = new BlinkBatchNode(\"BlinkBatch_%s\", dag);\r\n" + 
						"fullSync.setJobName(\"%s\");\r\n" +
						"return dag;";
	
				String dagScript = String.format(taskTemplate
												, jobName
												, dsMetaJson.getString("timeExpression")
												, jobName
												, jobName);
				TaskInfoRequest taskInfo = new TaskInfoRequest();
				taskInfo.setDagScript(dagScript);
				taskInfo.setName(taskName);
				taskInfo.setOperateEmpid(operateEmpid);
				taskInfo.setTaskDesc(taskName);
				taskInfo.setPolicy(DagPolicy.ODS.toString());
				taskInfo.setTenantId(tenantId);
				taskInfo.setAppName(appName);
				taskId = taskService.createDAGTask(taskInfo);
				
				ViewModelTaskRelation record = new ViewModelTaskRelation();
	            record.setCreateEmpid(operateEmpid);
	            record.setGmtCreate(new Date());
	            record.setGmtModified(new Date());
	            record.setIsDeleted(0L);
	            record.setModelVersionId(versionId);
	            record.setModifyEmpid(operateEmpid);
	            record.setTaskId(taskId);
	            record.setTenantId(tenantId);
	            record.setViewModelName(viewModelName);
	            viewModelTaskRelationMapper.insert(record);
				log.info("[{}]create qanat timer task[{}] finished", "", taskId);
            }
    	}
        return jobName;
    }
    
    private String getOdpsType(String type) {
        if ("varchar".equalsIgnoreCase(type)) {
            return "string";
        } else if (type.equalsIgnoreCase("tinyint")) {
            return "int";
        }
        return type;
    }
    
    private String getBlinkType(String type) {
        if ("string".equalsIgnoreCase(type)) {
            return "varchar";
        } else if ("datetime".equalsIgnoreCase(type)) {
        	return "timestamp";
        }
        return type;
    }

    public boolean processRelatedObjectIncrSyncJob(String tenantId, String appName, String jobName, String etlDbName, String tableName, DataObject object, String operateEmpid, Long versionId, String datatubeLevel, Long datatubeInstId, String pkField, ViewModel dataModel) {
    	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, object.getRef());
    	if (srcDsMetaJson.getJSONObject("odsConf") == null) {
    		log.info("{} has no ods conf", srcDsMetaJson.getString("dsName"));
    		return false;
    	}
		flowCtlService.setFlowControlIfNotExists(datatubeInstId, jobName, 1.0);
        int parallel = viewModelOptimizer.getParallel(tenantId, datatubeLevel, srcDsMetaJson.getInteger("qph"));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> columns = new ArrayList<>();
        List<String> columnsOld = new ArrayList<>();
        List<String> newColumns = new ArrayList<>();
        List<String> oldColumns = new ArrayList<>();
        Set<String> refColumnsDefines = new HashSet<>();
        List<String> selectColumns = new ArrayList<>();
        List<String> columnDefines = new ArrayList<>();
        List<String> columnChangeInWhere = new ArrayList<>();
        String updateKeyField = null;
        Map<String, String> pkMap = new HashMap<>();
        for (ViewModel.Relation rel : ((RelatedDataObject)object).getRelations()) {
        	if (!rel.getRelatedField().startsWith("exp#")) {
        		updateKeyField = rel.getRelatedField().split("\\.")[1];
        		pkMap.put(rel.getField(), updateKeyField);
        	}
        	break;
        }
        for (ViewModel.Field field : object.getFields()) {
        	if (field.getObject() != null) {
        		continue;
        	}
        	if (field.isFunc()) {
        		continue;
        	}
        	if (field.getRef() != null && field.isCompute()) {
        		List<String> tokens = lookupProcessor.parseFuncExpress(field.getRef());
    			String funcCode = null;
        		if (CollectionUtils.isNotEmpty(tokens)) {
        			funcCode = tokens.get(0);
        		} else {
        			funcCode = field.getRef().split("\\(")[0];
        		}
        		if ("json_extract".equalsIgnoreCase(funcCode)) {
        			String col = field.getRef().replace("json_extract", "JSON_VALUE");
	        		if ("varchar".equalsIgnoreCase(field.getType())) {
	        			refColumnsDefines.add(col + " as " + field.getCode());
		        	} else {
		        		refColumnsDefines.add("CAST(" + col + " AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") as " + field.getCode());
		        	}
        		} else if (field.getRef().toLowerCase().startsWith("concat_ws")) {
        			String ref = field.getRef();
        			refColumnsDefines.add(ref + " as " + field.getCode());
        		} else if ("from_unixtime".equalsIgnoreCase(funcCode) && "datetime".equalsIgnoreCase(field.getType())) {
        			String param0 = field.getRef().split("\\(")[1].split(",")[0];
        			String param1 = field.getRef().split("\\(")[1].split(",")[1];
        			String ref = "from_unixtime(CAST(" + param0 + " AS BIGINT)," + param1.replace("%Y", "yyyy").replace("%m", "MM").replace("%d", "dd").replace("%h", "HH").replace("%i", "mm").replace("%s", "ss");
        			refColumnsDefines.add("CAST(" + ref + " AS timestamp) as " + field.getCode());
        		}
	        	selectColumns.add("`" + field.getCode() + "`");
	        	columnDefines.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
        		continue;
        	}
        	columns.add("`" + field.getRef() + "`");
        	columnsOld.add("`" + field.getRef() + "__old`");
        	newColumns.add("n.`" + field.getRef() + "`");
        	oldColumns.add("o.`" + field.getRef() + "` AS " + field.getRef() + "__old");
        	refColumnsDefines.add("`" + field.getRef() + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
        	selectColumns.add("`" + field.getRef() + "`");
        	columnDefines.add("`" + (pkMap.get(field.getCode()) != null ? pkMap.get(field.getCode()) : field.getCode()) + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
        	columnChangeInWhere.add("COALESCE(" + field.getRef() + ",'') <> COALESCE(" + field.getRef() + "__old,'')");
        }
        String sql = "--SQL\n" + 
    			"--********************************************************************--\n" + 
    			"--Author: " + operateEmpid + "\n" + 
    			"--CreateTime: " + sdf.format(new Date()) + "\n" + 
    			"--Comment: " + ("sync for " + tableName + " from " + object.getCode()) + "\n" + 
    			"--Version: " + codegenVersion + "\n" + 
    			"--********************************************************************--\n" + 
    			"create table holobinlog_source (\n" + 
    			"    hg_binlog_lsn BIGINT,\n" +
    			"    hg_binlog_event_type BIGINT,\n" +
    			"    hg_binlog_timestamp_us BIGINT,\n" +
    			"   " + StringUtils.join(refColumnsDefines, ",\n") + "\n" + 
    			") with (\n" + 
    			"  type = 'hologres',\n" + 
    			"  dbname = '" + srcDsMetaJson.getJSONObject("odsConf").getString("database") + "',\n" + 
    			"  tablename = '" + srcDsMetaJson.getJSONObject("odsConf").getString("tableName") + "',\n" + 
    			"  username = '" + srcDsMetaJson.getJSONObject("odsConf").getString("username") + "',\n" +
    			"  password = '" + srcDsMetaJson.getJSONObject("odsConf").getString("password") + "',\n" +
    			"  endpoint = '" + srcDsMetaJson.getJSONObject("odsConf").getString("endpoint") + "',\n" +
    			"  binlog = 'true',\n" +
    			"  binlogMaxRetryTimes = '10',\n" +
    			"  binlogRetryIntervalMs = '500',\n" +
    			"  binlogBatchReadSize = '256'\n" +
    			");\n" + 
    			"\n" + 
    			"CREATE FUNCTION trace AS 'com.aliyun.wormhole.qanat.blink.udf.QanatTraceUdf';\n" +
    			"CREATE FUNCTION flowCtl AS 'com.aliyun.wormhole.qanat.blink.udf.QanatFlowControlUdf';\n" +
    			"CREATE FUNCTION hololog AS 'com.aliyun.wormhole.qanat.blink.udf.QanatHoloLogTUdf';\n" +
    			"\n" +
    			"create view v_holobinlog AS SELECT *," +
    			"    trace(UUID(),hg_binlog_lsn,hg_binlog_event_type,hg_binlog_timestamp_us," + pkField + ",'" + jobName + "','holobinlog') as __trace_id__\n" + 
    			"from holobinlog_source;\n" +
    			"\n" +
    			"create view v_source_new AS SELECT * from v_holobinlog where hg_binlog_event_type in (5,2,7);\n" + 
    			"create view v_source_old AS SELECT * from v_holobinlog where hg_binlog_event_type=3;\n" +
    			"\n" + 
    			"create view v_source as \n" + 
    			"select \n" + 
    			"    n.hg_binlog_lsn,\n" + 
    			"    n.hg_binlog_event_type,\n" + 
    			"    n.hg_binlog_timestamp_us,\n" + 
    			"    o.hg_binlog_lsn as hg_binlog_lsn__old,\n" + 
    			"    o.hg_binlog_event_type as hg_binlog_event_type__old,\n" + 
    			"    o.hg_binlog_timestamp_us as hg_binlog_timestamp_us__old,\n" + 
    			"    " + StringUtils.join(newColumns, ",\n") + ",\n" + 
    			"    " + StringUtils.join(oldColumns, ",\n") + ",\n" + 
    			"    n.__trace_id__\n" + 
    			"from v_source_new as n left join v_source_old as o on n.hg_binlog_lsn=(o.hg_binlog_lsn+1) and n.hg_binlog_timestamp_us=o.hg_binlog_timestamp_us\n" + 
    			"where n.hg_binlog_event_type in (5,2) or (n.hg_binlog_event_type=7 and o.hg_binlog_event_type=3)\n" +
    			";\n" +
    			"\n" + 
    			"create view v_sink as \n" + 
    			"select \n" + 
    			"    hg_binlog_lsn,\n" + 
    			"    hg_binlog_event_type,\n" + 
    			"    hg_binlog_timestamp_us,\n" + 
    			"    " + StringUtils.join(columns, ",\n") + ",\n" + 
    			"    flowCtl(__trace_id__,'" + jobName + "',cast(" + pkField + " as varchar)) as __trace_id__,\n" + 
    			"    hololog(__trace_id__,hg_binlog_lsn,hg_binlog_event_type,hg_binlog_timestamp_us,'rowdata'," + StringUtils.join(columns, ",") + ") as __newhaslog__,\n" +
    			"    (case when hg_binlog_lsn__old is null then 'nobeforeupdate' else hololog(__trace_id__,hg_binlog_lsn__old,hg_binlog_event_type__old,hg_binlog_timestamp_us__old,'rowdata_before_update'," + StringUtils.join(columnsOld, ",") + ") end) as __oldhaslog__\n" +
    			"from v_source\n where " + StringUtils.join(columnChangeInWhere, " or ") + "\n" +
    			";\n" +
    			"\n";
		
		String pkPart = "";
		if (updateKeyField.equalsIgnoreCase(pkField)) {
			pkPart = StringUtils.isNotBlank(dataModel.getSettings().getDistributeKey()) && !pkField.equalsIgnoreCase(dataModel.getSettings().getDistributeKey()) ? (pkField + "," + dataModel.getSettings().getDistributeKey()) : pkField;
		} else {
			pkPart = updateKeyField;
		}
		
		sql += "create table upsert_sink(\n" + 
			"    " + StringUtils.join(columnDefines, ",") + ",\n" + 
			"    __trace_id__ varchar,\n" + 
			"    primary key(" + pkPart + ")\n" + 
			") with (\n" + 
			"    type = 'QANAT_ADB30',\n" + 
			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
			"    dbName='" + etlDbName + "',\n" + 
			"    tableName='" + tableName + "',\n" + 
			"    replaceMode = '" + (updateKeyField.equalsIgnoreCase(pkField) ? "upsert" : "update_by_query") + "',\n" + 
			"    writeMode = 'single',\n" +
			"    streamEvent = 'disable'\n" +
			");\n" + 
			"\n" + 
			"insert into upsert_sink select " + StringUtils.join(selectColumns, ",") + ",__trace_id__ from v_sink where 1=1 and (__oldhaslog__ is not null and __newhaslog__ is not null) " + ((StringUtils.isNotBlank(object.getFilter()) ? ("and " + object.getFilter()) : "") + (StringUtils.isNotBlank(srcDsMetaJson.getString("streamFilter")) ? (" and " + srcDsMetaJson.getString("streamFilter")) : "")) + ";" +
			"\n";
        
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDF), parallel);
        
        DatatubeInstanceTask record = new DatatubeInstanceTask();
        record.setCreateEmpid(operateEmpid);
        record.setDatatubeInstId(datatubeInstId);
        record.setGmtCreate(new Date());
        record.setGmtModified(new Date());
        record.setIsDeleted(0L);
        record.setModifyEmpid(operateEmpid);
        record.setTaskName(jobName);
        record.setTaskScript(sql);
        record.setTaskType("blink_stream");
        record.setTenantId(tenantId);
        record.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(record);
        return true;
    }
	
	private Long getAppIdByName(String tenantId, String appName) {
		AppInfoExample example = new AppInfoExample();
		example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
		List<AppInfo> apps = appInfoMapper.selectByExample(example);
		return apps.get(0).getId();
	}
}