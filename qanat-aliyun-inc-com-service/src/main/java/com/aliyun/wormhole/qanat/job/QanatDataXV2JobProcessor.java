package com.aliyun.wormhole.qanat.job;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.DataSourceType;
import com.aliyun.wormhole.qanat.api.dag.DataXV2Node;
import com.aliyun.wormhole.qanat.api.datax.DataXHoloWriter;
import com.aliyun.wormhole.qanat.api.datax.DataXMySQLReader;
import com.aliyun.wormhole.qanat.api.datax.DataXMySQLWriter;
import com.aliyun.wormhole.qanat.api.datax.DataXOdpsReader;
import com.aliyun.wormhole.qanat.api.datax.DataXOdpsWriter;
import com.aliyun.wormhole.qanat.api.datax.DataXReader;
import com.aliyun.wormhole.qanat.api.datax.DataXSetting;
import com.aliyun.wormhole.qanat.api.datax.DataXTddlReader;
import com.aliyun.wormhole.qanat.api.datax.DataXWriter;
import com.aliyun.wormhole.qanat.api.dto.ColumnInfo;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.DatasourceRequest;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.SyncDataService;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelation;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ResourceExample;
import com.aliyun.wormhole.qanat.dal.mapper.AppResourceRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsFieldInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ResourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.service.odps.OdpsClient;
import com.aliyun.wormhole.qanat.service.util.HoloUtils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;

/**
 * DataX任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatDataXV2JobProcessor extends AbstractQanatNodeJobProcessor<DataXV2Node> {
    
    @Resource
    private SyncDataService syncDataService;
    
    @Resource
    private DatasourceMapper datasourceMapper;
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;
    
    @Resource
    private TaskInfoMapper taskInfoMapper;
	
	@Resource
	private AppResourceRelationMapper appResourceRelationMapper;
	
	@Resource
	private ResourceMapper resourceMapper;

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private DsFieldInfoMapper dsFieldInfoMapper;
    
    @Resource
    private DatasourceService dsInfoService;


    
    private void createAdbTable(String tableName, Map<String, String> colNameTypeMap, String pk, String url, String username, String password) {
        List<String> colDefList = new ArrayList<>();
        for (String fieldName : colNameTypeMap.keySet()) {
            colDefList.add(fieldName + " " + colNameTypeMap.get(fieldName));
        }
        String sql = null;
        if (pk == null) {
        	sql = String.format("Create Table %s ( %s ) INDEX_ALL='Y';", tableName, StringUtils.join(colDefList, ","));
        } else {
        	sql = String.format("Create Table %s ( %s , primary key (%s) ) DISTRIBUTE BY HASH(%s) INDEX_ALL='Y';", tableName, StringUtils.join(colDefList, ","), pk, pk);
        }
        RdsConnectionParam param = new RdsConnectionParam();
        param.setUrl(url);
        param.setUserName(username);
        param.setPassword(password);
        Connection connection = null;
        Statement statement = null;
        try {
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement();
            log.info("create table ddl:{}", sql);
            statement.execute(sql);
        } catch (Exception e) {
            log.error("create adb table failed", e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {}
                statement = null;
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {}
                connection = null;
            }
        }
    }

    private void createOdpsTable(String tenantId, String appName, String tableName, String tableComment, List<ColumnInfo> columns) {
    	AppResourceRelationExample example = new AppResourceRelationExample();
    	example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andRelationTypeEqualTo("odps");
    	List<AppResourceRelation> rels = appResourceRelationMapper.selectByExample(example);
    	if (CollectionUtils.isEmpty(rels)) {
    		throw new QanatBizException("no app resouces");
    	}
    	AppResourceRelation ref = rels.get(0);
    	ResourceExample example1 = new ResourceExample();
    	example1.createCriteria().andResourceNameEqualTo(ref.getResourceName()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L);
    	List<com.aliyun.wormhole.qanat.dal.domain.Resource> resources = resourceMapper.selectByExampleWithBLOBs(example1);
    	if (CollectionUtils.isEmpty(resources)) {
    		throw new QanatBizException("no app resouces");
    	}
    	com.aliyun.wormhole.qanat.dal.domain.Resource resource = resources.get(0);
    	JSONObject metaJson = JSON.parseObject(resource.getMeta());
	    OdpsClient client = new OdpsClient(metaJson.getString("endpoint"), metaJson.getString("accessId"), metaJson.getString("accessKey"),
	    		metaJson.getString("project"), metaJson.getString("mcUrl"), metaJson.getString("mcToken"));

        try {
        	SimpleDateFormat sdf = new SimpleDateFormat("yyMMddHHmm");
        	String ts = sdf.format(new Date());
    	    client.queryOdpsSql("ALTER TABLE " + metaJson.getString("project") + "." + tableName + " RENAME TO " + metaJson.getString("project") + ".bak_" + tableName + "_" + ts + ";");
            log.info("逻辑删除ODPS表[{}]成功", tableName);
        } catch (Exception e) {
            log.error("逻辑删除ODPS表[{}.{}]失败", tableName, e);
        }
        try {
            Map<String, String> columnTypeMap = buildColumnTypeMap(columns);
            Map<String, String> columnCommentMap = buildColumnCommentMap(columns);
            client.createOdpsTable(tableName, columnTypeMap, columnCommentMap, tableComment, 3600L);
            log.info("ODPS建表[{}]成功", tableName);
        } catch (Exception e) {
            log.error("ODPS建表[{}]失败", tableName, e);
        }
    }

    /**
     * 获取字段类型对应关系
     *
     * @param columns columns
     * @return 返回结果
     */
    private Map<String, String> buildColumnTypeMap(List<ColumnInfo> columns) {
        //字段与字段类型Map
        Map<String, String> columnTypeMap = new LinkedHashMap<String, String>(16);
        if (CollectionUtils.isNotEmpty(columns)) {
            String dsKey="ds";
            for (ColumnInfo column : columns) {
                String colName = column.getColumnName();
                if (dsKey.equals(colName)) {
                    continue;
                }
                columnTypeMap.put(colName, StringUtils.trimToEmpty(column.getColumnType()));
            }
        }
        return columnTypeMap;
    }

    /**
     * 获取字段描述对应关系
     *
     * @param columns columns
     * @return 返回结果
     */
    private Map<String, String> buildColumnCommentMap(List<ColumnInfo> columns) {
        //字段与字段类型Map
        Map<String, String> columnCommentMap = new LinkedHashMap<String, String>(16);
        String dsKey="ds";
        if (CollectionUtils.isNotEmpty(columns)) {
            for (ColumnInfo column : columns) {
                String colName = column.getColumnName();
                if (dsKey.equals(colName)) {
                    continue;
                }
                columnCommentMap.put(colName, StringUtils.trimToEmpty(column.getColumnComment()));
            }
        }
        return columnCommentMap;
    }

	@Override
	void doProcess(Map<String, Object> instParamsMap, DataXV2Node node) {
        String tenantId = String.valueOf(instParamsMap.get("tenantId"));
        String appName = String.valueOf(instParamsMap.get("appName"));
        
        DataXSetting setting = new DataXSetting(node.getParallism() == null ? 10 : node.getParallism());
        DataXReader reader = null;
        DataXWriter writer = null;
        String srcDsName = node.getSrcDsName();
        String dstDbName = node.getDstDbName();
        String where = node.getWhere();;
        DatasourceExample example = new DatasourceExample();
        example.createCriteria().andDsNameEqualTo(srcDsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
        Datasource srcDs = datasourceMapper.selectByExampleWithBLOBs(example).get(0);
        String srcDsMeta = srcDs.getMeta();
        JSONObject srcDsMetaJson = JSON.parseObject(srcDsMeta);

        if (StringUtils.isBlank(srcDs.getDbName())) {
            throw new QanatBizException("dbName for ds:" + srcDsName + " is not config");
        }
        DbInfoExample dbExample = new DbInfoExample();
        dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(srcDs.getDbName()).andTenantIdEqualTo(tenantId);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("db:" + srcDs.getDbName() + " is not found");
        }
        DbInfo srcDbInfo = dbs.get(0);
        JSONObject srcDbMetaJson = JSON.parseObject(srcDbInfo.getMeta());
        if (srcDs.getDsType().equalsIgnoreCase(DataSourceType.MYSQL.toString())
            || srcDs.getDsType().equalsIgnoreCase(DataSourceType.ADB3.toString())
            || srcDs.getDsType().equalsIgnoreCase(DataSourceType.POSTGRESQL.toString())
            || srcDs.getDsType().equalsIgnoreCase(DataSourceType.HOLOGRES.toString())) {
            srcDsMetaJson.put("jdbcUrl", srcDbMetaJson.getString("jdbcUrl"));
            srcDsMetaJson.put("username", srcDbMetaJson.getString("username"));
            srcDsMetaJson.put("password", srcDbMetaJson.getString("password"));
            srcDsMetaJson.put("version", srcDbMetaJson.getString("version"));
        } else if (srcDs.getDsType().equalsIgnoreCase(DataSourceType.ODPS.toString())) {
            srcDsMetaJson.put("project", srcDbMetaJson.getString("project"));
            srcDsMetaJson.put("accessId", srcDbMetaJson.getString("accessId"));
            srcDsMetaJson.put("accessKey", srcDbMetaJson.getString("accessKey"));
            srcDsMetaJson.put("odpsServer", srcDbMetaJson.getString("odpsServer"));
        } else if (srcDs.getDsType().equalsIgnoreCase(DataSourceType.TDDL.toString())) {
            srcDsMetaJson.put("appName", srcDbMetaJson.getString("appName"));
        }
        //刷新源表元数据
        DatasourceRequest dsInfoModReq = new DatasourceRequest();
        dsInfoModReq.setTenantId(tenantId);
        dsInfoModReq.setDsName(srcDsName);
        dsInfoModReq.setOperateEmpid("schedulerx2");
        dsInfoService.modifyDatasource(dsInfoModReq);

        dbExample = new DbInfoExample();
        dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dstDbName).andTenantIdEqualTo(tenantId);
        dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("db:" + dstDbName + " is not found");
        }
        DbInfo dstDbInfo = dbs.get(0);
        JSONObject dstDbMetaJson = JSON.parseObject(dstDbInfo.getMeta());
        
        DsFieldInfoExample fieldExample = new DsFieldInfoExample();
        fieldExample.createCriteria().andDsNameEqualTo(srcDsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
        List<DsFieldInfo> dsFields = dsFieldInfoMapper.selectByExample(fieldExample);

        if (CollectionUtils.isEmpty(dsFields)) {
        	throw new QanatBizException("dsFields of ds:" + srcDsName + " is empty");
        }
    	String dstTableComment = srcDs.getDsDesc();
        
        List<String> columns = new ArrayList<>();
        List<String> pkList = new ArrayList<>();
        Map<String, String> colNameTypeMap = new HashMap<>();
        Map<String, String> colNameDescMap = new HashMap<>();
        List<String> dstColumns = new ArrayList<>();
    	columns = dsFields.stream().map(item -> item.getFieldName()).collect(Collectors.toList());
    	pkList = dsFields.stream().filter(item -> item.getIsPk() == 1).map(DsFieldInfo::getFieldName).collect(Collectors.toList());
    	for (DsFieldInfo field : dsFields) {
    		colNameTypeMap.put(field.getFieldName(), field.getFieldType());
    		colNameDescMap.put(field.getFieldName(), field.getFieldDesc());
    	}
        
    	SimpleDateFormat tsSdf = new SimpleDateFormat("yyMMddHHmm");
        String ts = tsSdf.format(new Date());
        String srcTableName = srcDs.getTableName();
		if (srcDs.getDsType().equalsIgnoreCase(DataSourceType.MYSQL.toString())
				|| srcDs.getDsType().equalsIgnoreCase(DataSourceType.ADB3.toString())) {
            DataXMySQLReader mysqlReader = new DataXMySQLReader();
            mysqlReader.setColumns(columns);
            mysqlReader.setJdbcUrl(getDbConnectionUrl(srcDsMetaJson));
            mysqlReader.setPassword(srcDsMetaJson.getString("password"));
            mysqlReader.setSplitPk(pkList.get(0));
            mysqlReader.setTable(srcTableName);
            mysqlReader.setUsername(srcDsMetaJson.getString("username"));
            mysqlReader.setWhere(where);
            mysqlReader.setVersion(srcDsMetaJson.getString("version"));
            reader = mysqlReader;
        } else if (srcDs.getDsType().equalsIgnoreCase(DataSourceType.TDDL.toString())) {
            DataXTddlReader tddlReader = new DataXTddlReader();
            tddlReader.setColumns(columns);
            tddlReader.setAppName(srcDsMetaJson.getString("appName"));
            tddlReader.setTable(srcTableName);
            tddlReader.setWhere(where);
            reader = tddlReader;
        } else if (srcDs.getDsType().equalsIgnoreCase(DataSourceType.ODPS.toString())) {
            DataXOdpsReader odpsReader = new DataXOdpsReader();
            odpsReader.setAccessId(srcDsMetaJson.getString("accessId"));
            odpsReader.setAccessKey(srcDsMetaJson.getString("accessKey"));
            odpsReader.setAccountProvider("aliyun");
            odpsReader.setOdpsServer(srcDsMetaJson.getString("odpsServer"));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String bizDate = sdf.format(DateUtils.addDays(new Date(), -1));
            odpsReader.setPartition(srcDsMetaJson.getString("partition").replace("#bizDate#", bizDate));
            odpsReader.setPackageAuthorizedProject(srcDsMetaJson.getString("project"));
            odpsReader.setProject(srcDsMetaJson.getString("project"));
    	    OdpsClient client = new OdpsClient(srcDsMetaJson.getString("odpsServer"), srcDsMetaJson.getString("accessId"), srcDsMetaJson.getString("accessKey"),
    	    		srcDsMetaJson.getString("project"), null, null);
    	    Map<String, String> columnsMap = client.getTableColumnType(srcTableName);
    	    columns = columnsMap.keySet().stream().collect(Collectors.toList());
            odpsReader.setColumns(columns);

            odpsReader.setTable(srcTableName);
            odpsReader.setWhere(where);
            reader = odpsReader;
        }

        String tmpDstTableName = null;
        String dstTableName = StringUtils.isNotBlank(node.getDstTableName()) ? node.getDstTableName() : srcDs.getTableName();
        if (dstDbInfo.getDbType().equalsIgnoreCase(DataSourceType.ADB3.toString())) {
            tmpDstTableName = "tmp_" + dstTableName + "_" + ts;
            DataXMySQLWriter adbWriter = new DataXMySQLWriter();
            adbWriter.setColumns(CollectionUtils.isNotEmpty(dstColumns)?dstColumns:columns);
            adbWriter.setJdbcUrl(getDbConnectionUrl(dstDbMetaJson));
            adbWriter.setPassword(dstDbMetaJson.getString("password"));
            adbWriter.setTable(tmpDstTableName);
            adbWriter.setUsername(dstDbMetaJson.getString("username"));
            adbWriter.setWriteMode("insert");
            if (null != node.getBatchSize()) {
                adbWriter.setBatchSize(node.getBatchSize());
            }
            if (StringUtils.isNotBlank(node.getPreSql())) {
                adbWriter.setPreSql(node.getPreSql());
            } else {
                adbWriter.setPreSql("TRUNCATE TABLE " + tmpDstTableName);
            }
            writer = adbWriter;
            //try to create table in adb
            createAdbTable(dstTableName, colNameTypeMap, CollectionUtils.isNotEmpty(pkList) ? pkList.get(0) : null, getDbConnectionUrl(dstDbMetaJson), 
            		dstDbMetaJson.getString("username"), 
            		dstDbMetaJson.getString("password"));
            createAdbTable(tmpDstTableName, colNameTypeMap, CollectionUtils.isNotEmpty(pkList) ? pkList.get(0) : null, getDbConnectionUrl(dstDbMetaJson), 
            		dstDbMetaJson.getString("username"), 
            		dstDbMetaJson.getString("password"));
        } else if (dstDbInfo.getDbType().equalsIgnoreCase(DataSourceType.HOLOGRES.toString())) {
        	if (dstTableName.split("\\.").length == 2) {
        		tmpDstTableName = dstTableName.split("\\.")[0] + ".tmp_" + dstTableName.split("\\.")[1] + "_" + ts;
        	} else {
                tmpDstTableName = "tmp_" + dstTableName + "_" + ts;
        	}
        	DataXHoloWriter holoWriter = new DataXHoloWriter();
            holoWriter.setColumns(CollectionUtils.isNotEmpty(dstColumns)?dstColumns:columns);
            holoWriter.setEndpoint(dstDbMetaJson.getString("endpoint"));
            holoWriter.setAccessKey(dstDbMetaJson.getString("password"));
            holoWriter.setTable(tmpDstTableName);
            holoWriter.setAccessId(dstDbMetaJson.getString("username"));
            holoWriter.setWriteMode("sdk");
            holoWriter.setDatabase(dstDbMetaJson.getString("dbName"));
            if (null != node.getBatchSize()) {
                holoWriter.setBatchSize(node.getBatchSize());
            }
            if (StringUtils.isNotBlank(node.getPreSql())) {
                holoWriter.setPreSql(node.getPreSql());
            } else {
                holoWriter.setPreSql("TRUNCATE TABLE " + tmpDstTableName);
            }
            writer = holoWriter;
            //try to create table in adb
            createHoloTable(dstTableName, colNameTypeMap, CollectionUtils.isNotEmpty(pkList) ? pkList.get(0) : null, getDbConnectionUrl(dstDbMetaJson), 
            		dstDbMetaJson.getString("username"), 
            		dstDbMetaJson.getString("password"));
            createHoloTable(tmpDstTableName, colNameTypeMap, CollectionUtils.isNotEmpty(pkList) ? pkList.get(0) : null, getDbConnectionUrl(dstDbMetaJson), 
            		dstDbMetaJson.getString("username"), 
                dstDbMetaJson.getString("password"));
        } else if (dstDbInfo.getDbType().equalsIgnoreCase(DataSourceType.ODPS.toString())) {
            List<ColumnInfo> colInfoList = new ArrayList<>();
            for (String fieldName : colNameTypeMap.keySet()) {
                ColumnInfo colInfo = new ColumnInfo();
                colInfo.setColumnName(fieldName);
                colInfo.setColumnType(HoloUtils.getHoloTypeFromMysql(colNameTypeMap.get(fieldName)));
                colInfo.setColumnComment(colNameDescMap.get(fieldName));
                colInfoList.add(colInfo);
            }
            //加分区字段
            ColumnInfo dsColInfo = new ColumnInfo();
            dsColInfo.setColumnName("ds");
            dsColInfo.setColumnType("string");
            dsColInfo.setColumnComment("yyyymmdd");
            colInfoList.add(dsColInfo);

            dstTableName = "ods_" + dstTableName + "_df";
            createOdpsTable(tenantId, appName, dstTableName, dstTableComment, colInfoList);
            
            DataXOdpsWriter odpsWriter = new DataXOdpsWriter();
            odpsWriter.setColumns(columns);
            odpsWriter.setAccessId(dstDbMetaJson.getString("accessId"));
            odpsWriter.setAccessKey(dstDbMetaJson.getString("accessKey"));
            odpsWriter.setAccountType("aliyun");
            odpsWriter.setOdpsServer(dstDbMetaJson.getString("odpsServer"));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String bizDate = sdf.format(DateUtils.addDays(new Date(), -1));
            odpsWriter.setPartition(dstDbMetaJson.getString("partition").replace("#bizDate#", bizDate));
            odpsWriter.setTable(dstTableName);
            odpsWriter.setProject(dstDbMetaJson.getString("project"));
            odpsWriter.setTruncate(true);
            writer = odpsWriter;
        }
        //调用数据同步
        DataResult<Map<String, String>> result = syncDataService.syncData(srcDs.getDsName(), setting, reader, writer);
        if (result.getData() != null && result.getSuccess()) {
            if (StringUtils.isNotBlank(tmpDstTableName)) {
                Connection connection = null;
                Statement statement = null;
                try {
                    RdsConnectionParam param = new RdsConnectionParam();
                    param.setUrl(getDbConnectionUrl(dstDbMetaJson));
                    param.setUserName(dstDbMetaJson.getString("username"));
                    param.setPassword(dstDbMetaJson.getString("password"));
                    connection = dsHandler.connectToTable(param);
                    statement = connection.createStatement();
                    
                    boolean isSyncSuccess = true;
                	if (StringUtils.isNotBlank(result.getData().get("datax_record_cnt")) && StringUtils.isNumeric(result.getData().get("datax_record_cnt"))) {
                		int newCnt = Integer.valueOf(result.getData().get("datax_record_cnt"));
                		int oldCnt = countTable(connection, dstTableName);
                		if (oldCnt > 0 && newCnt < oldCnt && BigDecimal.valueOf(newCnt).divide(BigDecimal.valueOf(oldCnt)).compareTo(new BigDecimal("0.5")) < 0) {
                			log.info("newCnt:{} is less then 50% of oldCnt:{}", newCnt, oldCnt);
                			isSyncSuccess = false;
                		}
                	}
                	if (isSyncSuccess) {
	                    try {
	                        statement.execute("ALTER TABLE " + dstTableName + " RENAME TO bak_" + dstTableName + "_" + ts);
	                    } catch(Exception e) {}
	                    try {
	                        statement.execute("ALTER TABLE " + tmpDstTableName + " RENAME TO " + dstTableName);
	                    } catch(Exception e) {}
                	}
                } catch (Exception e) {
                    log.error("AdbSql任务调度异常", e);
                    throw new QanatBizException("ADB表操作失败");
                } finally {
                    if (statement != null) {
                        try {
                            statement.close();
                        } catch (SQLException e) {
                        }
                        statement = null;
                    }
                    if (connection != null) {
                        try {
                            connection.close();
                        } catch (SQLException e) {
                        }
                        connection = null;
                    }
                }
            }
        }
	}
    private void createHoloTable(String tableName, Map<String, String> colNameTypeMap, String pk, String url, String username, String password) {
        List<String> colDefList = new ArrayList<>();
        for (String fieldName : colNameTypeMap.keySet()) {
            colDefList.add(fieldName.toLowerCase() + " " + HoloUtils.getHoloTypeFromMysql(colNameTypeMap.get(fieldName)));
        }
        String sql = null;
        if (pk == null) {
        	sql = String.format("Create Table %s ( %s );", tableName, StringUtils.join(colDefList, ","));
            sql += "call set_table_property('" + tableName + "', 'orientation', 'column');";
        } else {
        	sql = String.format("Create Table %s ( %s , primary key (%s) );", tableName, StringUtils.join(colDefList, ","), pk);
            sql += "call set_table_property('" + tableName + "', 'orientation', 'row');" + 
            		"call set_table_property('" + tableName + "', 'distribution_key', '" + pk + "');" + 
            		"call set_table_property('" + tableName + "', 'clustering_key', '" + pk + "');" + 
    				"call set_table_property('" + tableName + "', 'binlog.level', 'replica');\n" + 
    				"call set_table_property('" + tableName + "', 'binlog.ttl', '86400');";
        }
        
        RdsConnectionParam param = new RdsConnectionParam();
        param.setUrl(url);
        param.setUserName(username);
        param.setPassword(password);
        Connection connection = null;
        Statement statement = null;
        try {
            connection = dsHandler.connectToTable(param);
            connection.setAutoCommit(false);
            statement = connection.createStatement();
            log.info("create table ddl:{}", sql);
            String[] subSqls = sql.split(";");
            for (String subSql : subSqls) {
                log.info("start to exec subSql:{}", subSql);
            	statement.execute(subSql);
            }
            connection.commit();
        } catch (Exception e) {
        	if (connection != null) {
	        	try {
					connection.rollback();
				} catch (SQLException e1) {
				}
        	}
            log.error("create holo table failed", e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {}
                statement = null;
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {}
                connection = null;
            }
        }
    }
	
	private Integer countTable(Connection connection, String tableName) {
    	int cnt = 0;
    	String sql = "select count(1) as total from " + tableName;
        log.info("before exec sql={}", sql);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
            	cnt = resultSet.getInt("total");
            }
            log.info("after exec sql cnt={}", cnt);
        } catch(Exception e) {
            log.error("querySql failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                }
                resultSet = null;
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
        }
        return cnt;
    }
}