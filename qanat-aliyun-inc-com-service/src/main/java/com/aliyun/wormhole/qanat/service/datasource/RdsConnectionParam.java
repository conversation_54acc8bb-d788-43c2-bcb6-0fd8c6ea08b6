/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.aliyun.wormhole.qanat.service.datasource;

import com.alibaba.druid.pool.DruidDataSource;

import java.io.Serializable;

public class RdsConnectionParam implements Serializable {

	private static final long serialVersionUID = -764510949279683113L;

	private String url;
	private String tableName;
	private String userName;
	private String password;

	private int maxRetryTime = 3;

	private String driverClassName = "com.mysql.jdbc.Driver";
	private int connectionMaxActive = 30;
	private int connectionInitialSize = 1;
	private int connectionMinIdle = 0;
	private boolean connectionTestWhileIdle = false;
	private int maxWait = 15000;
	private int removeAbandonedTimeout = 60 * 10;
	private int maxFetchResult = 1024;

	public RdsConnectionParam setMaxWait(int maxWait) {
		this.maxWait = maxWait;
		return this;
	}

	public RdsConnectionParam setRemoveAbandonedTimeout(int removeAbandonedTimeout) {
		this.removeAbandonedTimeout = removeAbandonedTimeout;
		return this;
	}

	public RdsConnectionParam setUrl(String url) {
		this.url = url;
		return this;
	}

	public RdsConnectionParam setTableName(String tableName) {
		this.tableName = tableName;
		return this;
	}

	public RdsConnectionParam setUserName(String userName) {
		this.userName = userName;
		return this;
	}

	public RdsConnectionParam setPassword(String password) {
		this.password = password;
		return this;
	}

	public RdsConnectionParam setMaxRetryTime(int maxRetryTime) {
		this.maxRetryTime = maxRetryTime;
		return this;
	}

	public int getMaxRetryTime() {
		return maxRetryTime;
	}

	public RdsConnectionParam setDriverClassName(String driverClassName) {
		this.driverClassName = driverClassName;
		return this;
	}

	public RdsConnectionParam setConnectionMaxActive(int connectionMaxActive) {
		this.connectionMaxActive = connectionMaxActive;
		return this;
	}

	public RdsConnectionParam setConnectionInitialSize(int connectionInitialSize) {
		this.connectionInitialSize = connectionInitialSize;
		return this;
	}

	public RdsConnectionParam setConnectionMinIdle(int connectionMinIdle) {
		this.connectionMinIdle = connectionMinIdle;
		return this;
	}

	public RdsConnectionParam setConnectionTestWhileIdle(boolean connectionTestWhileIdle) {
		this.connectionTestWhileIdle = connectionTestWhileIdle;
		return this;
	}

	public String getTableName() {
		return tableName;
	}

    public String getUrl() {
        return url;
    }

	public String getUsername() {
		return userName;
	}

	private String getDriverClassName() {
		if (url.startsWith("jdbc:postgresql")) {
			return "org.postgresql.Driver";
		} else {
			return driverClassName;
		}
	}

	public int getMaxFetchResult() {
		return maxFetchResult;
	}

	public RdsConnectionParam setMaxFetchResult(int maxFetchResult) {
		this.maxFetchResult = maxFetchResult;
		return this;
	}

	public DruidDataSource buildDataSource() {
		DruidDataSource ds = new DruidDataSource();
		ds.setUrl(url);
		ds.setUsername(userName);
		ds.setPassword(password);
		ds.setDriverClassName(getDriverClassName());
		ds.setMaxActive(connectionMaxActive);
		ds.setInitialSize(connectionInitialSize);
		ds.setMaxWait(maxWait);//默认为15s
		ds.setMinIdle(connectionMinIdle);
		ds.setTestWhileIdle(connectionTestWhileIdle);
		ds.setRemoveAbandonedTimeout(removeAbandonedTimeout); //对于可能存在连接泄露的情况，10min强制回收一次空闲的连接
		return ds;
	}
}
