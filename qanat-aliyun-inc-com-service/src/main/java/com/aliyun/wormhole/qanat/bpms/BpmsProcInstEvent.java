package com.aliyun.wormhole.qanat.bpms;

import org.springframework.context.ApplicationEvent;

/**
 * BPMS流程实例事件
 * <AUTHOR>
 * 2017年8月8日
 */
public class BpmsProcInstEvent extends ApplicationEvent {
    /**
     * 
     */
    private static final long serialVersionUID = 7483878900545623807L;

    /**
     * 流程实例id
     */
    private String processInstanceId;
    
    /**
     * 事件类型
     */
    private String eventType;
    
    /**
     * 请求标识
     */
    private String requestId;
    /**
     * 请求code
     */
    private String bpmsProcessCode;

    public BpmsProcInstEvent(Object source, String processInstanceId, String eventType, String requestId,String bpmsProcessCode) {
        super(source);
        this.processInstanceId = processInstanceId;
        this.eventType = eventType;
        this.requestId = requestId;
        this.bpmsProcessCode = bpmsProcessCode;
    }

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    public String getBpmsProcessCode() {
    	return bpmsProcessCode;
    }
    
    public void setBpmsProcessCode(String bpmsProcessCode) {
    	this.bpmsProcessCode = bpmsProcessCode;
    }

}
