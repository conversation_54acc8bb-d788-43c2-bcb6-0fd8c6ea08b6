package com.aliyun.wormhole.qanat.process;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfo;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelVersionWithBLOBs;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelVersionMapper;
import com.aliyun.wormhole.qanat.service.datatube.DatatubeHandler;
import com.aliyun.wormhole.qanat.service.util.YamlUtil;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelHandler;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelOptimizer;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * ViewModel多库Sinkebuild任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class ViewModelMultiDbRebuildProcessor extends JavaProcessor {
    
    @Resource
    private ViewModelInfoMapper viewModelInfoMapper;
    
    @Resource
    private ViewModelHandler viewModelHandler;
    
    @Resource
    private DatatubeHandler datatubeHandler;
    
    @Resource
    private DatatubeInstanceMapper datatubeInstanceMapper;
    
    @Resource
    private ViewModelVersionMapper viewModelVersionMapper;
    
    @Resource
    private ViewModelOptimizer viewModelOptimizer;
    
    @Resource
    private DatasourceMapper dsInfoMapper;
    
    @Resource
    private DatasourceService dsInfoService;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
            log.info("start to refresh vm version, param=[]", context.getJobParameters());
            String tenantId = paramsJson.getString("tenantId");
            if (StringUtils.isBlank(tenantId)) {
            	log.info("tenantId is empty");
                return new ProcessResult(false);
            }
            JSONArray vmIdJsonArray = paramsJson.getJSONArray("vmIds");
        	List<Long> vmIds = new ArrayList<>();
            if (vmIdJsonArray != null && vmIdJsonArray.size() > 0) {
            	for (int i = 0; i < vmIdJsonArray.size(); i++) {
            		vmIds.add(vmIdJsonArray.getLong(i));
            	}
            }
        	ViewModelInfoExample example = new ViewModelInfoExample();
        	ViewModelInfoExample.Criteria criteria = example.createCriteria();
        	criteria.andTenantIdEqualTo(tenantId);
        	criteria.andIsDeletedEqualTo(0L);
        	if (CollectionUtils.isNotEmpty(vmIds)) {
        		criteria.andIdIn(vmIds);
        	}
			List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(example);
			if (CollectionUtils.isNotEmpty(viewModelInfos)) {
				for (ViewModelInfo vm : viewModelInfos) {
					try {
						ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(vm.getVersionId());

				        ViewModel originModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
				    	ViewModel sysModel = null;
				    	if (originModel.isDynamic()) {
				    		sysModel = viewModelOptimizer.getOptimizedViewModel(tenantId, modelVersion.getUserYaml());
				    	} else {
				    		sysModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
				    	}
				    	ViewModel dataModel = new ViewModel();
				    	BeanUtils.copyProperties(sysModel, dataModel);
				    	
				    	if ("metadata".equalsIgnoreCase(dataModel.getObject().getType()) && dsInfoService.isObjectDirectWriteByMdp(tenantId, dataModel.getObject().getRef())) {
				    		String objDsName = dsInfoService.getDsNameByObjectCode(tenantId, dataModel.getObject().getRef());
				    		dsInfoService.modifyDsInfoMdpSlot(tenantId, objDsName, viewModelHandler.getEtlDbName(tenantId));
				    	}
						
						log.info("begin to procees vm[{}:{}]", vm.getId(), vm.getModelName());
						viewModelHandler.createBatchStreamTasks(tenantId, vm.getId(), "schedulerx2");
						
				        DatatubeInstanceExample diExample = new DatatubeInstanceExample();
				        diExample.createCriteria().andTenantIdEqualTo(tenantId).andProviderEqualTo("viewmodel").andProviderIdEqualTo(vm.getId()).andIsTestEqualTo(0L).andIsDeletedEqualTo(0L);
				        List<DatatubeInstance> insts = datatubeInstanceMapper.selectByExample(diExample);
				        if (CollectionUtils.isNotEmpty(insts)) {
				        	datatubeHandler.refreshViewModelDsRelations(tenantId, insts.get(0).getId(), "schedulerx2");
				        }
				        
						log.info("finish to procees vm[{}:{}]", vm.getId(), vm.getModelName());
					} catch(Exception e) {
						log.info("failed to procees vm[{}:{}], error-{}", vm.getId(), vm.getModelName(), e.getMessage(), e);
					}
				}
			}
        } catch (Exception e) {
            log.error("ViewModelMultiDbRebuildProcessor任务调度异常", e);
            return new ProcessResult(false, e.getMessage());
        }
        return new ProcessResult(true);
    }
			
	private Datasource getDsInfoByObjectCode(String tenantId, String objectUniqueCode) {
		DatasourceExample example = new DatasourceExample();
		example.createCriteria().andTableNameEqualTo(objectUniqueCode).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsTypeEqualTo("obj");
		List<Datasource> dsList = dsInfoMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isEmpty(dsList)) {
			throw new QanatBizException(objectUniqueCode + " is not found");
		}
		return dsList.get(0);
	}
    
    @Override
    public void kill(JobContext context) {
        
    }
}