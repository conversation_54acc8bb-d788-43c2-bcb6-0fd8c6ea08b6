package com.aliyun.wormhole.qanat.service.datax;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.security.SecurityUtil;
import com.aliyun.wormhole.qanat.api.datax.DataXConfigUtils;
import com.aliyun.wormhole.qanat.api.datax.DataXReader;
import com.aliyun.wormhole.qanat.api.datax.DataXSetting;
import com.aliyun.wormhole.qanat.api.datax.DataXWriter;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.service.SyncDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Writer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据同步服务
 * <AUTHOR>
 * 2019年7月23日
 */
@Slf4j
@Component
@HSFProvider(serviceInterface = SyncDataService.class)
public class SyncDataServiceImpl implements SyncDataService {
    private static final String USER_HOME = "/home/<USER>";
    private static final String DATAX_HOME = USER_HOME + "/datax3";

    @Override
    public String getDataXConfig(String requestId, DataXSetting setting, DataXReader reader, DataXWriter writer) {
        return DataXConfigUtils.getDataXConfig(setting, reader, writer);
    }

    @Override
    public DataResult<Map<String, String>> syncData(String requestId, DataXSetting setting, DataXReader reader, DataXWriter writer) {
        String dataXConfig = getDataXConfig(requestId, setting, reader, writer);
        log.info("[{}]dataXConfig={}", requestId, dataXConfig);
        return this.syncData(requestId, dataXConfig);
    }

    private boolean mkConfigFile(String fileName, String config) {
        	// 写入的文本不附加在原来的后面而是直接覆盖
        	try (Writer fileWriter=new FileWriter(fileName, false)) {
        	    fileWriter.write(config);
        	} catch(IOException e) {
        		log.error(e.getMessage(), e);
        		return false;
        	}
        return true;
    }
    
    private boolean deleteConfigFile(String fileName) {
        File file = new File(fileName);
        if (!file.exists()) {
            log.error("删除文件失败:" + fileName + "不存在！");
            return false;
        } else {
            if (file.delete()) {
                return true;
            } else {
                log.error("删除文件失败:" + fileName + "删除未成功！");
                return false;
            }
        }
    }

    @Override
    public DataResult<Map<String, String>> syncData(String requestId, String dataXConfig) {
        DataResult<Map<String, String>> result = new DataResult<>();
        Map<String, String> rtnMap = new HashMap<>();
        String fileName = DATAX_HOME + "/job/" + SecurityUtil.getSafeCommandLine(requestId) + ".json";

        if (mkConfigFile(fileName, dataXConfig)) {
            List<String> commands = new ArrayList<>();
            final String withResult = " with result:";
            commands.add(DATAX_HOME + "/bin/datax.py");
            commands.add(fileName);
            ProcessBuilder processBuilder = new ProcessBuilder(commands);
            try {
                File homeDir = new File(USER_HOME);
                processBuilder.directory(homeDir);
                processBuilder.redirectErrorStream(true);
                Process process = processBuilder.start();
                StringBuilder shellResult = new StringBuilder();
                final BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    shellResult.append(line);
                    if (line.startsWith("任务启动时刻")) {
                        line = StringUtils.trimToEmpty(line.replace("任务启动时刻", "")).substring(1);
                        rtnMap.put("datax_start_time", StringUtils.trimToEmpty(line));
                    } else if (line.startsWith("任务结束时刻")) {
                        line = StringUtils.trimToEmpty(line.replace("任务结束时刻", "")).substring(1);
                        rtnMap.put("datax_end_time", StringUtils.trimToEmpty(line));
                    } else if (line.startsWith("读出记录总数")) {
                        String[] tokenArray = line.split(":");
                        if (tokenArray.length == 2) {
                            rtnMap.put("datax_record_cnt", StringUtils.trimToEmpty(tokenArray[1]));
                        }
                    } else if (line.startsWith("读写失败总数")) {
                        String[] tokenArray = line.split(":");
                        if (tokenArray.length == 2) {
                            rtnMap.put("datax_failed_cnt", StringUtils.trimToEmpty(tokenArray[1]));
                        }
                    }
                    log.info("[{}]datax out--->:{}", requestId, line);
                }
                process.waitFor();
                int exit = process.exitValue();
                if (exit != 0) {
                    result.setSuccess(false);
                    result.setMessage("syncData failed to execute:" + processBuilder.command() + withResult + shellResult);
                    log.error("syncData failed to execute:" + processBuilder.command() + withResult + shellResult);
                } else {
                    result.setSuccess(true);
                    result.setData(rtnMap);
                    result.setMessage("syncData to execute:" + processBuilder.command() + withResult + shellResult);
                }
                IOUtils.closeQuietly(bufferedReader);

                if (!deleteConfigFile(fileName)) {
                    result.setSuccess(false);
                    result.setMessage(
                            "delete config file faild:" + processBuilder.command() + withResult + shellResult);
                }
            } catch (Exception ex) {
                result.setSuccess(false);
                log.error(ex.getMessage());
            }
        }
        return result;
    }
}
