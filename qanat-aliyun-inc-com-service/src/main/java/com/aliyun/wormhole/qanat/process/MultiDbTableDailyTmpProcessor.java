package com.aliyun.wormhole.qanat.process;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.security.SecurityUtil;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelHandler;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 多库数据稽核任务
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class MultiDbTableDailyTmpProcessor extends JavaProcessor {

	@Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private ViewModelHandler viewModelHandler;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            log.info("MultiDbTableCheckProcessor, param=[]", context.getJobParameters());
            JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
            String tenantId = paramsJson.getString("tenantId");
            String tableNames = paramsJson.getString("tableNames");
            String [] tableNameArray = tableNames.split(",");
            
            String etlDbName = viewModelHandler.getEtlDbName(tenantId);
            List<String> extDbNames = viewModelHandler.getDstDbNames(tenantId);
            extDbNames.remove(etlDbName);
            
        	for (String tableName : tableNameArray) {
            	log.info("start to process {}.{}", etlDbName, tableName);
            	try {
                	String ddl = getCreateTableDdl(etlDbName, tableName);
                	if (StringUtils.isBlank(ddl)) {
                    	log.error("get ddl from {}.{} failed", etlDbName, tableName);
                		continue;
                	}
                	for (int i = 0; i < extDbNames.size(); i++) {
	                	JSONObject dbMetaJson = getAdbDbMeta(extDbNames.get(i));
	        	    	RdsConnectionParam param = new RdsConnectionParam();
	                    param.setUrl(dbMetaJson.getString("jdbcUrl"))
	            	        .setUserName(dbMetaJson.getString("username"))
	            	        .setPassword(dbMetaJson.getString("password"));
	                    Statement statement = null;
	                    Connection connection = null;
	                    try {
	                        connection = dsHandler.connectToTable(param);
	                        connection.setAutoCommit(false);
	                        statement = connection.createStatement();
	                        
	                        String tmpTableName = "tmp_" + tableName;
	                        
	                        try {
	                        	String dropSql = "drop table if exists " + tmpTableName;
		                        log.info("drop table ddl:{}", dropSql);
		                        statement.execute(dropSql);
	                        } catch(Exception e) {}
	                        
	                        String createSql = ddl.replace(tableName, tmpTableName);
	                        log.info("create table ddl:{}", createSql);
	                        statement.execute(createSql);
		                	log.info("tmpTable {}.{} is created", extDbNames.get(i), tableName);
                        	connection.commit();
	                    } catch (Exception e) {
	                        log.error("create adb table failed", e);
	                        if (connection != null) {
	                        	try {
	            					connection.rollback();
	            				} catch (SQLException e1) {
	            				}
	                        }
	                    } finally {
	                        if (statement != null) {
	                            try {
	                                statement.close();
	                            } catch (SQLException e) {
	                            } finally {
	                            	statement = null;
	                            }
	                        }
	                        if (connection != null) {
	                            try {
	                                connection.close();
	                            } catch (SQLException e) {
	                            } finally {
	                            	connection = null;
	                            }
	                        }
	                    }
	                	log.info("start to process {}.{}", etlDbName, tableName);
            		}
                	log.info("finish to process {}.{}", etlDbName, tableName);
            	} catch(Exception e) {
                	log.error("failed to process {}.{}, error={}", etlDbName, tableName, e.getMessage() ,e);
            	}
        	}
        } catch (QanatBizException e) {
            log.error("MultiDbTableCheckProcessor任务调度异常:{}", e.getMessage());
            return new ProcessResult(false, e.getMessage());
        } catch (Exception e) {
            log.error("MultiDbTableCheckProcessor任务调度异常", e);
            return new ProcessResult(false, e.getMessage());
        }
        return new ProcessResult(true);
    }

    private JSONObject getAdbDbMeta(String dbName) {
        DbInfoExample example = new DbInfoExample();
        example.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("no db found");
        }
        String dbMeta = dbs.get(0).getMeta();
        JSONObject dbMetaJson = JSON.parseObject(dbMeta);
        dbMetaJson.put("dbType", dbs.get(0).getDbType());
        return dbMetaJson;
    }

	private String getCreateTableDdl(String dbName, String tableName) {
		String ddl = null;
		Connection connection = null;
		Statement statement = null;
		ResultSet resultSet = null;
		String sql = null;
		try {
		    JSONObject dbMetaJson = getAdbDbMeta(dbName);
			if (tableName.startsWith("view_") && tableName.equalsIgnoreCase("view_telesale_solution_test")) {
				sql = "SHOW CREATE MATERIALIZED VIEW " + SecurityUtil.trimSql(tableName) + ";";
			} else if (tableName.startsWith("view_") || tableName.endsWith("_view")) {
				sql = "SHOW CREATE VIEW " + SecurityUtil.trimSql(tableName) + ";";
			} else {
				sql = "SHOW CREATE TABLE " + SecurityUtil.trimSql(tableName) + ";";
			}
		    log.info("show create table sql:{}", sql);
		    RdsConnectionParam param = new RdsConnectionParam();
		    param.setUrl(dbMetaJson.getString("jdbcUrl"))
			    .setUserName(SecurityUtil.trimSql(dbMetaJson.getString("username")))
			    .setPassword(dbMetaJson.getString("password"));
		    connection = dsHandler.connectToTable(param);
		    statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
		    resultSet = statement.executeQuery(sql);
		    if(resultSet.next()) {
		        ddl = resultSet.getString(2);
			    log.info("ddl:{}", ddl);
		    }
		} catch(Exception e) {
		    log.error("sql exec failed, e={}", e.getMessage());
		} finally {
		    if (resultSet != null) {
		        try {
		            resultSet.close();
		        } catch (Exception e) {
		        }
	            resultSet = null;
		    }
		    if (statement != null) {
		        try {
		            statement.close();
		        } catch (Exception e) {
		        }
	            statement = null;
		    }
		    if (connection != null) {
		        try {
		            connection.close();
		        } catch (Exception e) {
		        }
	            connection = null;
		    }
		}
		return ddl;
	}
    
    @Override
    public void kill(JobContext context) {
        
    }
}