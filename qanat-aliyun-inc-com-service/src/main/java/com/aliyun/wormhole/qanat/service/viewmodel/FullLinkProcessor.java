package com.aliyun.wormhole.qanat.service.viewmodel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.ResourcePackage;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.KafkaManagementService;
import com.aliyun.wormhole.qanat.dal.domain.AppInfo;
import com.aliyun.wormhole.qanat.dal.domain.AppInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelation;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelation;
import com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsRelation;
import com.aliyun.wormhole.qanat.dal.domain.DsRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.Extension;
import com.aliyun.wormhole.qanat.dal.domain.ExtensionExample;
import com.aliyun.wormhole.qanat.dal.domain.ResourceExample;
import com.aliyun.wormhole.qanat.dal.mapper.AppInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.AppResourceRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ComponentDsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ExtensionMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ResourceMapper;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.DataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.RelatedDataObject;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class FullLinkProcessor {
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private KafkaManagementService kafkaManagementService;
    
    @Resource
    private AppInfoMapper appInfoMapper;
    
    @Resource
    private DatasourceService dsInfoService;
    
    @Resource
    private AppResourceRelationMapper appResourceRelationMapper;
    
    @Resource
    private ResourceMapper resourceMapper;
	
	@Resource
	private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;
	
	@Resource
	private DatatubeInstanceMapper datatubeInstanceMapper;
	@Resource
	private DsRelationMapper dsRelationMapper;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private ExtensionMapper extensionMapper;
    
    @Resource
    private ComponentDsRelationMapper componentDsRelationMapper;
    
    @Value("${datatube.codegen.version}")
    private String codegenVersion;
	
	public static String BLINK_FULL_LINK_KV_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--Version: %s\n" + 
			"--********************************************************************--\n" + 
			"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
			"create table drc_source (\n" + 
			"    k varchar,\n" + 
			"    v varchar,\n" + 
			"    __ts__ bigint header,\n" + 
			"    __traceId__ varchar header\n" + 
			") with (\n" + 
			"  type = 'custom',\n" + 
			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"  topicPattern = '%s',\n" + 
			"  `group.id` = '%s',\n" + 
			"  `dbName` = '%s',\n" +
			"  startupMode = 'TIMESTAMP',\n" +
			"  fieldDelimiter = '`'\n" +
			");\n" + 
			"\n" + 
			"create table log_source (\n" + 
			"    msg varchar,\n" + 
			"    __ts__ bigint header,\n" + 
			"    __traceId__ varchar header  \n" + 
			") with (\n" + 
			"  type = 'custom',\n" + 
			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"  topicPattern = '%s',\n" + 
			"  `group.id` = '%s',\n" + 
			"  `dbName` = '%s',\n" +
			"  startupMode = 'TIMESTAMP',\n" +
			"  fieldDelimiter = '`'\n" +
			");\n" + 
			"\n" + 
			"CREATE TABLE common_data_full_link_log_adb_sink (\n" + 
			"  trace_id varchar,\n" + 
			"  pk bigint,\n" + 
			"  key varchar,\n" + 
			"  ts bigint,\n" + 
			"  msg varchar,\n" + 
			"  db varchar,\n" + 
			"  ext1 varchar,\n" + 
			"  gmt_create timestamp\n" + 
			") WITH (\n" + 
			"  %s" + 
			");\n" + 
			"\n" + 
			"insert\n" + 
			"  into common_data_full_link_log_adb_sink\n" + 
			"select\n" + 
			"  __traceId__ as trace_id,\n" + 
			"  cast(0 AS bigint) as pk,\n" + 
			"  k as key,\n" + 
			"  __ts__ as ts,\n" + 
			"  concat(k, '`', v) as msg,\n" + 
			"  '%s' as db,\n" + 
			"  '%s' as ext1,\n" + 
			"  CURRENT_TIMESTAMP as gmt_create\n" + 
			"from\n" + 
			"  drc_source;\n" + 
			"\n" + 
			"insert\n" + 
			"  into common_data_full_link_log_adb_sink\n" + 
			"select\n" + 
			"  __traceId__ as trace_id,\n" + 
			"  %s as pk,\n" + 
			"  (case when JSON_VALUE (t.a, '$.%s') is null then JSON_VALUE (t.a, '$.%s_old') else JSON_VALUE (t.a, '$.%s') END) as key,\n" + 
			"  __ts__ as ts,\n" + 
			"  msg,\n" + 
			"  '%s' as db,\n" + 
			"  '%s' as ext1,\n" + 
			"  CURRENT_TIMESTAMP as gmt_create\n" + 
			"from\n" + 
			"  log_source, LATERAL TABLE (parseDrcFields (msg, 'id')) as t (a);";
	
	public static String BLINK_FULL_LINK_ID_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--Version: %s\n" + 
			"--********************************************************************--\n" + 
			"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
			"create table drc_source (\n" + 
			"    id bigint,\n" + 
			"    __ts__ bigint header,\n" + 
			"    __traceId__ varchar header\n" + 
			") with (\n" + 
			"  type = 'custom',\n" + 
			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"  topicPattern = '%s',\n" + 
			"  `group.id` = '%s',\n" + 
			"  `dbName` = '%s',\n" +
			"  startupMode = 'TIMESTAMP',\n" +
			"  fieldDelimiter = '`'\n" +
			");\n" + 
			"\n" + 
			"create table log_source (\n" + 
			"    msg varchar,\n" + 
			"    __ts__ bigint header,\n" + 
			"    __traceId__ varchar header  \n" + 
			") with (\n" + 
			"  type = 'custom',\n" + 
			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"  topicPattern = '%s',\n" + 
			"  `group.id` = '%s',\n" + 
			"  `dbName` = '%s',\n" +
			"  startupMode = 'TIMESTAMP',\n" +
			"  fieldDelimiter = '`'\n" +
			");\n" + 
			"\n" + 
			"CREATE TABLE common_data_full_link_log_adb_sink (\n" + 
			"  trace_id varchar,\n" + 
			"  pk bigint,\n" + 
			"  key varchar,\n" + 
			"  ts bigint,\n" + 
			"  msg varchar,\n" + 
			"  db varchar,\n" + 
			"  ext1 varchar,\n" + 
			"  gmt_create timestamp\n" + 
			") WITH (\n" + 
			"  %s" + 
			");\n" + 
			"\n" + 
			"insert\n" + 
			"  into common_data_full_link_log_adb_sink\n" + 
			"select\n" + 
			"  __traceId__ as trace_id,\n" + 
			"  id as pk,\n" + 
			"  cast(id as varchar) as key,\n" + 
			"  __ts__ as ts,\n" + 
			"  cast(id as varchar) as msg,\n" + 
			"  '%s' as db,\n" + 
			"  '%s' as ext1,\n" + 
			"  CURRENT_TIMESTAMP as gmt_create\n" + 
			"from\n" + 
			"  drc_source;\n" + 
			"\n" + 
			"insert\n" + 
			"  into common_data_full_link_log_adb_sink\n" + 
			"select\n" + 
			"  __traceId__ as trace_id,\n" + 
			"  %s as pk,\n" + 
			"  (case when JSON_VALUE (t.a, '$.%s') is null then JSON_VALUE (t.a, '$.%s_old') else JSON_VALUE (t.a, '$.%s') END) as key,\n" + 
			"  __ts__ as ts,\n" + 
			"  msg,\n" + 
			"  '%s' as db,\n" + 
			"  '%s' as ext1,\n" + 
			"  CURRENT_TIMESTAMP as gmt_create\n" + 
			"from\n" + 
			"  log_source, LATERAL TABLE (parseDrcFields (msg, 'id')) as t (a);";
	
	public static String BLINK_FULL_LINK_DRC_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--Version: %s\n" + 
			"--********************************************************************--\n" + 
			"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
			"CREATE FUNCTION containsKey AS 'com.aliyun.wormhole.qanat.blink.udf.QanatContainsKeyUdf';\n" +
			"\n" +
			"create table drc_source (\n" + 
			"    msg varchar,\n" + 
			"    __ts__ bigint header,\n" + 
			"    __traceId__ varchar header  \n" + 
			") with (\n" + 
			"  type = 'custom',\n" + 
			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"  topic = '%s',\n" + 
			"  `group.id` = '%s',\n" + 
			"  `dbName` = '%s',\n" + 
			"  startupMode = 'TIMESTAMP',\n" + 
			"  fieldDelimiter = '`'\n" + 
			");\n" +
			"\n" +
			"create table log_source (\n" + 
			"    msg varchar,\n" + 
			"    __ts__ bigint header,\n" + 
			"    __traceId__ varchar header  \n" + 
			") with (\n" + 
			"  type = 'custom',\n" + 
			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"  topic = '%s',\n" + 
			"  `group.id` = '%s',\n" + 
			"  `dbName` = '%s',\n" +
			"  startupMode = 'TIMESTAMP',\n" +
			"  fieldDelimiter = '`'\n" +
			");\n" +  
			"\n" + 
			"CREATE TABLE drc_sls_sink (\n" + 
			"  datatube_inst_id bigint,\n" + 
			"  trace_id varchar,\n" + 
			"  key varchar,\n" + 
			"  ts bigint,\n" + 
			"  msg varchar,\n" + 
			"  object_type varchar,\n" + 
			"  db varchar,\n" + 
			"  table_name varchar,\n" + 
			"  obj varchar,\n" + 
			"  log_type varchar,\n" + 
			"  scene varchar,\n" + 
			"  gmt_create timestamp\n" + 
			") WITH (\n" + 
			"  %s" + 
			");\n" + 
			"\n" + 
			"insert\n" + 
			"  into drc_sls_sink\n" + 
			"select\n" + 
			"  cast(%s as bigint) as datatube_inst_id,\n" + 
			"  a.__traceId__ as trace_id,\n" + 
			"  COALESCE(JSON_VALUE(t.a, '$.%s'), JSON_VALUE(t.a, '$.%s_old')) as key,\n" + 
			"  cast(JSON_VALUE (a.msg, '$.ts') as bigint) as ts,\n" + 
			"  a.msg as msg,\n" +  
			"  '%s' as object_type,\n" + 
			"  '%s' as db,\n" + 
			"  '%s' as table_name,\n" + 
			"  '%s' as obj,\n" + 
			"  'drc' as log_type,\n" + 
			"  'metric' as scene,\n" +  
			"  CURRENT_TIMESTAMP as gmt_create\n" +
			"from\n" + 
			"  drc_source as a, LATERAL TABLE (parseDrcFields (a.msg, '%s')) as t (a)\n" +
			";\n" + 
			"\n" +
			"CREATE TABLE dwd_sls_sink (\n" + 
			"  datatube_inst_id bigint,\n" + 
			"  trace_id varchar,\n" + 
			"  key varchar,\n" + 
			"  cost bigint,\n" + 
			"  ts bigint,\n" + 
			"  msg varchar,\n" + 
			"  object_type varchar,\n" + 
			"  db varchar,\n" + 
			"  table_name varchar,\n" + 
			"  obj varchar,\n" + 
			"  op_type varchar,\n" + 
			"  log_type varchar,\n" + 
			"  scene varchar,\n" + 
			"  gmt_create timestamp\n" + 
			") WITH (\n" + 
			"  %s" + 
			");\n" + 
			"\n" + 
			"insert\n" + 
			"  into dwd_sls_sink\n" + 
			"select\n" + 
			"  cast(%s as bigint) as datatube_inst_id,\n" + 
			"  a.__traceId__ as trace_id,\n" + 
			"  COALESCE(JSON_VALUE(t.a, '$.%s'), JSON_VALUE(t.a, '$.%s_old')) as key,\n" + 
			"  cast(JSON_VALUE (b.msg, '$.ts') as bigint) - cast(JSON_VALUE (a.msg, '$.ts') as bigint)*1000 as cost,\n" + 
			"  cast(JSON_VALUE (b.msg, '$.ts') as bigint) as ts,\n" + 
			"  b.msg as msg,\n" +  
			"  '%s' as object_type,\n" + 
			"  '%s' as db,\n" + 
			"  '%s' as table_name,\n" + 
			"  '%s' as obj,\n" + 
			"  '%s' as op_type,\n" +
			"  'dwd' as log_type,\n" +  
			"  'metric' as scene,\n" +  
			"  CURRENT_TIMESTAMP as gmt_create\n" +
			"from\n" + 
			"  drc_source as a join log_source as b on a.__traceId__=b.__traceId__, LATERAL TABLE (parseDrcFields (a.msg, '%s')) as t (a)\n" +
			";\n" +
			"\n" +
			"%s\n" +
			"\n"
			;
	
	public static String BLINK_OBJ_FULL_LINK_DRC_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--Version: %s\n" + 
			"--********************************************************************--\n" + 
			"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
			"CREATE FUNCTION containsKey AS 'com.aliyun.wormhole.qanat.blink.udf.QanatContainsKeyUdf';\n" +
			"\n" +
			"create table drc_source (\n" + 
			"    msg varchar,\n" + 
			"    __ts__ bigint header,\n" + 
			"    __traceId__ varchar header\n" + 
			") with (\n" + 
			"  type = 'custom',\n" + 
			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"  topic = '%s',\n" + 
			"  `group.id` = '%s',\n" + 
			"  `dbName` = '%s',\n" +
			"  startupMode = 'TIMESTAMP',\n" +
			"  fieldDelimiter = '`'\n" +
			");\n" + 
			"\n" + 
			"create table log_source (\n" + 
			"    msg varchar,\n" + 
			"    __ts__ bigint header,\n" + 
			"    __traceId__ varchar header  \n" + 
			") with (\n" + 
			"  type = 'custom',\n" + 
			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"  topic = '%s',\n" + 
			"  `group.id` = '%s',\n" + 
			"  `dbName` = '%s',\n" +
			"  startupMode = 'TIMESTAMP',\n" +
			"  fieldDelimiter = '`'\n" +
			");\n" +  
			"\n" + 
			"CREATE TABLE drc_sls_sink (\n" + 
			"  datatube_inst_id bigint,\n" + 
			"  trace_id varchar,\n" + 
			"  key varchar,\n" + 
			"  ts bigint,\n" + 
			"  msg varchar,\n" + 
			"  object_type varchar,\n" + 
			"  db varchar,\n" + 
			"  table_name varchar,\n" +  
			"  obj varchar,\n" + 
			"  log_type varchar,\n" + 
			"  scene varchar,\n" + 
			"  gmt_create timestamp\n" + 
			") WITH (\n" + 
			"  %s" + 
			");\n" + 
			"\n" + 
			"insert\n" + 
			"  into drc_sls_sink\n" + 
			"select\n" + 
			"  cast(%s as bigint) as datatube_inst_id,\n" + 
			"  a.__traceId__ as trace_id,\n" + 
			"  JSON_VALUE(a.msg, '$.extParam.objectInstanceVO.objectBizId') as key,\n" + 
			"  a.__ts__ as ts,\n" + 
			"  a.msg as msg,\n" +  
			"  '%s' as object_type,\n" + 
			"  '%s' as db,\n" + 
			"  '%s' as table_name,\n" +  
			"  '%s' as obj,\n" + 
			"  'mdp' as log_type,\n" + 
			"  'metric' as scene,\n" +  
			"  CURRENT_TIMESTAMP as gmt_create\n" +
			"from\n" + 
			"  drc_source as a\n" +
			";\n" + 
			"\n" + 
			"CREATE TABLE dwd_sls_sink (\n" + 
			"  datatube_inst_id bigint,\n" + 
			"  trace_id varchar,\n" + 
			"  key varchar,\n" + 
			"  cost bigint,\n" + 
			"  ts bigint,\n" + 
			"  msg varchar,\n" + 
			"  object_type varchar,\n" + 
			"  db varchar,\n" + 
			"  table_name varchar,\n" + 
			"  obj varchar,\n" + 
			"  op_type varchar,\n" + 
			"  log_type varchar,\n" + 
			"  scene varchar,\n" + 
			"  gmt_create timestamp\n" + 
			") WITH (\n" + 
			"  %s" + 
			");\n" + 
			"\n" + 
			"insert\n" + 
			"  into dwd_sls_sink\n" + 
			"select\n" + 
			"  cast(%s as bigint) as datatube_inst_id,\n" + 
			"  a.__traceId__ as trace_id,\n" + 
			"  JSON_VALUE(a.msg, '$.extParam.objectInstanceVO.objectBizId') as key,\n" + 
			"  cast(JSON_VALUE (b.msg, '$.ts') as bigint) - a.__ts__ as cost,\n" + 
			"  cast(JSON_VALUE (b.msg, '$.ts') as bigint) as ts,\n" + 
			"  b.msg as msg,\n" + 
			"  '%s' as object_type,\n" + 
			"  '%s' as db,\n" + 
			"  '%s' as table_name,\n" + 
			"  '%s' as obj,\n" + 
			"  '%s' as op_type,\n" + 
			"  'dwd' as log_type,\n" +  
			"  'metric' as scene,\n" +  
			"  CURRENT_TIMESTAMP as gmt_create\n" + 
			"from\n" + 
			"  drc_source as a join log_source as b on a.__traceId__=b.__traceId__\n" + 
			";\n" +
			"\n" + 
			"%s\n"
			;

    public boolean processFullLinkJob4Drc(String tenantId, String appName, String jobName, String dbName, String tableName, String operateEmpid, Long versionId, DataObject object, JSONObject appKafkaJson, ViewModel dataModel, Long datatubeInstId, JSONObject drcTopicInfo) {
        JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, object.getRef());
    	if (srcDsMetaJson.getJSONObject("incrConf") == null) {
    		log.info("no incr sync conf");
    		return false;
    	}
    	String logTopicName = "stream-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-" + object.getCode();
        return processFullLinkJob4Drc(tenantId, appName, jobName, dbName, tableName, operateEmpid, versionId, object,
        		appKafkaJson, logTopicName, dataModel, datatubeInstId, drcTopicInfo == null ? srcDsMetaJson.getJSONObject("incrConf") : drcTopicInfo);
    }

	public boolean processFullLinkJob4Drc(String tenantId, String appName, String jobName, String dbName,
			String tableName, String operateEmpid, Long versionId, DataObject object, JSONObject appKafkaJson,
			String logTopicName, ViewModel dataModel, Long datatubeInstId, JSONObject drcTopicInfo) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        String pkField = null;
        if (object instanceof RelatedDataObject) {
	        for (ViewModel.Relation rel : ((RelatedDataObject)object).getRelations()) {
	        	if (!rel.getRelatedField().startsWith("exp#")) {
	        		if (CollectionUtils.isEmpty(object.getFields())) {
		        		pkField = dataModel.getObject().getFields().stream().filter(e->e.getCode().equalsIgnoreCase(rel.getField())).collect(Collectors.toList()).get(0).getRef();
	        		} else {
		        		pkField = object.getFields().stream().filter(e->e.getCode().equalsIgnoreCase(rel.getField())).collect(Collectors.toList()).get(0).getRef();
	        		}
	        		break;
	        	}
	        }
        } else {
        	for (ViewModel.Field field : object.getFields()) {
            	if (field.isPk()) {
            		pkField = field.getCode();
            		break;
            	}
        	}
        }
        String consumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-link-" + object.getCode();
    	boolean res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, drcTopicInfo.getString("dbName"), consumerId);
		if (!res) {
			log.error("consumer:{} create is failed", consumerId);
		}
    	res = kafkaManagementService.createConsumerGroup(tenantId, appName, consumerId);
		if (!res) {
			log.error("consumer:{} create is failed", consumerId);
		}
        
		DatatubeInstance datatubeInst = datatubeInstanceMapper.selectByPrimaryKey(datatubeInstId);
		JSONObject srcDsMetaJson = new JSONObject();
		if ("component".equalsIgnoreCase(object.getType())) {
			ExtensionExample example = new ExtensionExample();
			example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + getDbType(tenantId, dbName)).andPluginEqualTo(object.getRef());
			List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
			if (CollectionUtils.isNotEmpty(exts)) {
				ComponentDsRelationExample comDsRelExample = new ComponentDsRelationExample();
				comDsRelExample.createCriteria().andTenantIdEqualTo(tenantId).andComponentNameEqualTo(exts.get(0).getCode()).andRelationTypeEqualTo("drc_in");
				List<ComponentDsRelation> comRsRels = componentDsRelationMapper.selectByExample(comDsRelExample);
				if (CollectionUtils.isNotEmpty(comRsRels)) {
					String drcDsName = comRsRels.get(0).getDsName();
					
					DsRelationExample dsRelExample = new DsRelationExample();
					dsRelExample.createCriteria().andTenantIdEqualTo(tenantId).andSrcDsNameEqualTo(drcDsName).andRelationTypeEqualTo("incr");
					List<DsRelation> dsRels = dsRelationMapper.selectByExample(dsRelExample);
					if (CollectionUtils.isNotEmpty(dsRels)) {
						String origDsName = dsRels.get(0).getDstDsName();
						srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, origDsName);
					}
				}
			}
		} else {
			srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, object.getRef());
		}
		
		boolean kfkRes = kafkaManagementService.createTopic(tenantId, appName, logTopicName, 1);
		if (!kfkRes) {
			log.error("topic:{} create is failed", logTopicName);
		}
		String logLookupTopicName = logTopicName + "_lookup";
    	kfkRes = kafkaManagementService.createTopic(tenantId, appName, logLookupTopicName, 1);
		if (!kfkRes) {
			log.error("topic:{} create is failed", logLookupTopicName);
		}
		
		List<String> lookupObjTraceSqls = new ArrayList<>();
		String lookUpSourceSql = "create table lookup_log_source (\n" + 
				"    msg varchar,\n" + 
				"    __ts__ bigint header,\n" + 
				"    __traceId__ varchar header  \n" + 
				") with (\n" + 
				"  type = 'custom',\n" + 
				"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
				"  topic = '" + logTopicName + "_lookup',\n" + 
				"  `group.id` = '" + consumerId + "',\n" + 
				"  `dbName` = '" + appKafkaJson.getString("dbName") + "',\n" +
				"  startupMode = 'TIMESTAMP',\n" +
				"  fieldDelimiter = '`'\n" +
				");\n" +  
				"\n" ;
		lookupObjTraceSqls.add(lookUpSourceSql);
		
		if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects()) && dataModel.getObject().getCode().equalsIgnoreCase(object.getCode())) {
			for (RelatedDataObject relObj : dataModel.getRelatedObjects()) {
				if ("none".equalsIgnoreCase(relObj.getLookupFrom())) {
    				continue;
    			}
				List<String> joinKeys = new ArrayList<>();
				for (ViewModel.Relation rel : relObj.getRelations()) {
					joinKeys.add(rel.getField());
		        }
				String sql = 
						"insert into dwd_sls_sink\n" + 
						"select\n" + 
						"  cast(" + datatubeInstId + " as bigint) as datatube_inst_id,\n" + 
						"  a.__traceId__ as trace_id,\n" + 
						"  COALESCE(JSON_VALUE(t.a, '$." + pkField + "'), JSON_VALUE(t.a, '$." + pkField + "_old')) as key,\n" + 
						"  cast(JSON_VALUE (b.msg, '$.ts') as bigint) - cast(JSON_VALUE (a.msg, '$.ts') as bigint)*1000 as cost,\n" + 
						"  cast(JSON_VALUE (b.msg, '$.ts') as bigint) as ts,\n" + 
						"  a.msg as msg,\n" + 
						"  '" + datatubeInst.getObjectType() + "' as obj_type,\n" + 
						"  '" + dbName + "' as db,\n" + 
						"  '" + dataModel.getCode() + "' as table_name,\n" + 
						"  '" + relObj.getCode() + "' as obj,\n" + 
						"  'mainobj_lookup' as op_type,\n" + 
						"  'dwd' as log_type,\n" + 
						"  'metric' as scene,\n" + 
						"  CURRENT_TIMESTAMP as gmt_create\n" + 
						"from drc_source as a\n" + 
						"  join lookup_log_source as b on a.__traceId__=b.__traceId__, LATERAL TABLE (parseDrcFields (a.msg, '" + pkField + "')) as t (a)\n" + 
						";\n";
				lookupObjTraceSqls.add(sql);
			}
		}
		
        String sql = String.format(BLINK_FULL_LINK_DRC_SQL
            , operateEmpid
            , sdf.format(new Date())
            , "fullLink for " + tableName + " from " + object.getCode()
			, codegenVersion
            , drcTopicInfo.getString("topicName")
            , consumerId
            , drcTopicInfo.getString("dbName")
            , logTopicName
            , consumerId
            , appKafkaJson.getString("dbName")
            , this.getFullLinkSinkWithClause(tenantId, appName)
            , datatubeInstId
            , pkField
            , pkField
            , srcDsMetaJson.getString("objectType")
            , srcDsMetaJson.getString("dbName")
            , srcDsMetaJson.getString("table")
            , object.getCode()
            , pkField
            , this.getFullLinkSinkWithClause(tenantId, appName)
            , datatubeInstId
            , pkField
            , pkField
            , datatubeInst.getObjectType()
            , dbName
            , dataModel.getCode()
            , object.getCode()
            , dataModel.getObject().getCode().equalsIgnoreCase(object.getCode()) ? "mainobj_upsert" : "relobj_update"
            , pkField
            , StringUtils.join(lookupObjTraceSqls, "\n")
            );

        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, 
        		"/" + appName + "/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_KAFKA010, ResourcePackage.BLINK_UDF), 
        		false);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        return true;
	}
    
    private String getDbType(String tenantId, String dbName) {
    	DbInfoExample dbInfoExample = new DbInfoExample();
    	dbInfoExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
    	List<DbInfo> dbInfos = dbInfoMapper.selectByExample(dbInfoExample);
    	if (CollectionUtils.isEmpty(dbInfos)) {
    		throw new QanatBizException("DbInfo not found:" + dbName);
    	}
    	return dbInfos.get(0).getDbType();
    }
	
	public boolean processObjectFullLinkJob4Drc(String tenantId, String appName, String jobName, String dbName, String tableName, String operateEmpid, Long versionId, DataObject object, JSONObject kafkaJson, ViewModel dataModel, Long datatubeInstId, ViewModel originModel) {
        JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsUniqueName(tenantId, object.getRef());
    	if (srcDsMetaJson.getJSONObject("incrConf") == null) {
    		log.info("no incr sync conf");
    		return false;
    	}
    	String logTopicName = "stream-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-" + object.getCode();
    	JSONObject drcTopicInfo = srcDsMetaJson.getJSONObject("incrConf");
        return processObjectFullLinkJob4Drc(tenantId, appName, jobName, dbName, tableName, operateEmpid, versionId, object,
				kafkaJson, logTopicName, drcTopicInfo, dataModel, datatubeInstId, originModel);
    }

	public boolean processObjectFullLinkJob4Drc(String tenantId, String appName, String jobName, String dbName,
			String tableName, String operateEmpid, Long versionId, DataObject object, JSONObject kafkaJson, String logTopicName, JSONObject drcTopicInfo, ViewModel sysModel, Long datatubeInstId, ViewModel originModel) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
 
		String consumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-link-" + object.getCode();
		boolean res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, drcTopicInfo.getString("dbName"), consumerId);
		if (!res) {
			log.error("consumer:{} create is failed", consumerId);
		}
    	res = kafkaManagementService.createConsumerGroup(tenantId, appName, consumerId);
		if (!res) {
			log.error("consumer:{} create is failed", consumerId);
		}
		
		DatatubeInstance datatubeInst = datatubeInstanceMapper.selectByPrimaryKey(datatubeInstId);
		JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsUniqueName(tenantId, object.getRef());
		
    	boolean kfkRes = kafkaManagementService.createTopic(tenantId, appName, logTopicName, 1);
		if (!kfkRes) {
			log.error("topic:{} create is failed", logTopicName);
		}
		String logLookupTopicName = logTopicName + "_lookup";
    	kfkRes = kafkaManagementService.createTopic(tenantId, appName, logLookupTopicName, 1);
		if (!kfkRes) {
			log.error("topic:{} create is failed", logLookupTopicName);
		}
		
		List<String> lookupObjTraceSqls = new ArrayList<>();
		String lookUpSourceSql = "create table lookup_log_source (\n" + 
				"    msg varchar,\n" + 
				"    __ts__ bigint header,\n" + 
				"    __traceId__ varchar header  \n" + 
				") with (\n" + 
				"  type = 'custom',\n" + 
				"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
				"  topic = '" + logTopicName + "_lookup',\n" + 
				"  `group.id` = '" + consumerId + "',\n" + 
				"  `dbName` = '" + kafkaJson.getString("dbName") + "',\n" +
				"  startupMode = 'TIMESTAMP',\n" +
				"  fieldDelimiter = '`'\n" +
				");\n" +  
				"\n" ;
		lookupObjTraceSqls.add(lookUpSourceSql);
		if (CollectionUtils.isNotEmpty(sysModel.getRelatedObjects()) && sysModel.getObject().getCode().equalsIgnoreCase(object.getCode())) {
			for (RelatedDataObject relObj : sysModel.getRelatedObjects()) {
				List<String> joinKeys = new ArrayList<>();
				for (ViewModel.Relation rel : relObj.getRelations()) {
					joinKeys.add(rel.getField());
		        }
				String sql = 
						"insert into dwd_sls_sink\n" + 
						"select\n" + 
						"  cast(" + datatubeInstId + " as bigint) as datatube_inst_id,\n" + 
						"  a.__traceId__ as trace_id,\n" + 
						"  JSON_VALUE(a.msg, '$.extParam.objectInstanceVO.objectBizId') as key,\n" + 
						"  cast(JSON_VALUE (b.msg, '$.ts') as bigint) - a.__ts__ as cost,\n" + 
						"  cast(JSON_VALUE (b.msg, '$.ts') as bigint) as ts,\n" + 
						"  a.msg as msg,\n" + 
						"  '" + datatubeInst.getObjectType() + "' as obj_type,\n" + 
						"  '" + dbName + "' as db,\n" + 
						"  '" + sysModel.getCode() + "' as table_name,\n" + 
						"  '" + relObj.getCode() + "' as obj,\n" + 
						"  'mainobj_lookup' as op_type,\n" + 
						"  'dwd' as log_type,\n" + 
						"  'metric' as scene,\n" + 
						"  CURRENT_TIMESTAMP as gmt_create\n" + 
						"from drc_source as a join lookup_log_source as b on a.__traceId__=b.__traceId__\n" +
						";\n";
				lookupObjTraceSqls.add(sql);
			}
		}
        
        String sql = String.format(BLINK_OBJ_FULL_LINK_DRC_SQL
            , operateEmpid
            , sdf.format(new Date())
            , "fullLink for " + tableName + " from " + object.getCode()
			, codegenVersion 
            , drcTopicInfo.getString("topicName")
            , consumerId
            , drcTopicInfo.getString("dbName")
            , logTopicName
            , consumerId
            , kafkaJson.getString("dbName")
            , this.getFullLinkSinkWithClause(tenantId, appName)
            , datatubeInstId
            , srcDsMetaJson.getString("objectType")
            , srcDsMetaJson.getString("dbName")
            , srcDsMetaJson.getString("table")
            , object.getCode()
            , this.getFullLinkSinkWithClause(tenantId, appName)
            , datatubeInstId
            , datatubeInst.getObjectType()
            , dbName
            , sysModel.getCode()
            , object.getCode()
            , sysModel.getObject().getCode().equalsIgnoreCase(object.getCode()) ? "mainobj_upsert" : "relobj_update"
            , StringUtils.join(lookupObjTraceSqls, "\n")
            );

        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, 
        		"/" + appName + "/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_UDF, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_KAFKA010), 
        		false);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        return true;
	}

	public boolean processFullLinkJob4KV(String tenantId, String appName, String jobName, String dbName,
			String tableName, String operateEmpid, Long versionId, RelatedDataObject object, JSONObject appKafkaJson,
			JSONObject streamTopicInfo, String logTopicName, ViewModel dataModel, String pkField, Long datatubeInstId) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		String fkFieldName = null;
		String fkFieldType = null;
		if (CollectionUtils.isNotEmpty(object.getRelations())) {
			fkFieldName = object.getRelations().get(0).getRelatedField().split("\\.")[1];
			for (ViewModel.Field field : dataModel.getObject().getFields()) {
				if (field.getCode().equalsIgnoreCase(fkFieldName)) {
					fkFieldType = field.getType();
					break;
				}
			}
		} else {
			fkFieldName = pkField;
			fkFieldType = "bigint";
		}
        String drcConsumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-link_drc-" + object.getCode() + "-" + versionId;
    	boolean res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, streamTopicInfo.getString("dbName"), drcConsumerId);
		if (!res) {
			log.error("consumer:{} create is failed", drcConsumerId);
		}
		String logConsumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-link_log-" + object.getCode() + "-" + versionId;
    	res = kafkaManagementService.createConsumerGroup(tenantId, appName, logConsumerId);
		if (!res) {
			log.error("consumer:{} create is failed", logConsumerId);
		}
        
        String sql = String.format(BLINK_FULL_LINK_KV_SQL
            , operateEmpid
            , sdf.format(new Date())
            , "fullLink for " + tableName + " from " + object.getCode()
            , codegenVersion
            , streamTopicInfo.getString("topicName")
            , drcConsumerId
            , streamTopicInfo.getString("dbName")
            , logTopicName
            , logConsumerId
            , appKafkaJson.getString("dbName")
            , this.getFullLinkSinkWithClause(tenantId, appName)
            , streamTopicInfo.getString("topicName")
            , ""
            , ("bigint".equalsIgnoreCase(fkFieldType)) ? ("CAST((case when JSON_VALUE (t.a, '$." + fkFieldName + "') is null then JSON_VALUE (t.a, '$." + fkFieldName + "_old') else JSON_VALUE (t.a, '$." + fkFieldName + "') END) AS BIGINT)") : "cast(0 AS bigint)"
            , fkFieldName, fkFieldName, fkFieldName
            , logTopicName
            , ""
            );

        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, 
        		"/" + appName + "/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_KAFKA010), 
        		false);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        return true;
	}

	public boolean processFullLinkJob4Id(String tenantId, String appName, String jobName, String dbName,
			String tableName, String operateEmpid, Long versionId, RelatedDataObject object, JSONObject appKafkaJson,
			List<JSONObject> drcTopicInfos, String logTopicName, ViewModel dataModel, String pkField, Long datatubeInstId) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
		String fkFieldName = null;
		String fkFieldType = null;
		if (CollectionUtils.isNotEmpty(object.getRelations())) {
			fkFieldName = object.getRelations().get(0).getRelatedField().split("\\.")[1];
			for (ViewModel.Field field : dataModel.getObject().getFields()) {
				if (field.getCode().equalsIgnoreCase(fkFieldName)) {
					fkFieldType = field.getType();
					break;
				}
			}
		} else {
			fkFieldName = pkField;
			fkFieldType = "bigint";
		}
        String drcConsumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-link_drc-" + object.getCode() + "-" + versionId;
    	boolean res = kafkaManagementService.createConsumerGroupFromDbInfo(tenantId, drcTopicInfos.get(0).getString("dbName"), drcConsumerId);
		if (!res) {
			log.error("consumer:{} create is failed", drcConsumerId);
		}
		String logConsumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-link_log-" + object.getCode() + "-" + versionId;
    	res = kafkaManagementService.createConsumerGroup(tenantId, appName, logConsumerId);
		if (!res) {
			log.error("consumer:{} create is failed", logConsumerId);
		}
        String drcTopicName = StringUtils.join(drcTopicInfos.stream().map(e -> e.getString("topicName")).collect(Collectors.toList()), "|");
        String sql = String.format(BLINK_FULL_LINK_ID_SQL
            , operateEmpid
            , sdf.format(new Date())
            , "fullLink for " + tableName + " from " + object.getCode()
            , codegenVersion
            , drcTopicName
            , drcConsumerId
            , drcTopicInfos.get(0).getString("dbName")
            , logTopicName
            , logConsumerId
            , appKafkaJson.getString("dbName")
            , this.getFullLinkSinkWithClause(tenantId, appName)
            , drcTopicName
            , ""
            , ("bigint".equalsIgnoreCase(fkFieldType)) ? ("CAST((case when JSON_VALUE (t.a, '$." + fkFieldName + "') is null then JSON_VALUE (t.a, '$." + fkFieldName + "_old') else JSON_VALUE (t.a, '$." + fkFieldName + "') END) AS BIGINT)") : "cast(0 AS bigint)"
            , fkFieldName, fkFieldName, fkFieldName
            , logTopicName
            , ""
            );

        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, 
        		"/" + appName + "/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_KAFKA010), 
        		false);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        return true;
	}
	
	private Long getAppIdByName(String tenantId, String appName) {
		AppInfoExample example = new AppInfoExample();
		example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
		List<AppInfo> apps = appInfoMapper.selectByExample(example);
		return apps.get(0).getId();
	}
	
	public JSONObject getSlsConfByAppName(String tenantId, String appName) {
		AppResourceRelationExample example = new AppResourceRelationExample();
    	example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andRelationTypeEqualTo("sls");
    	List<AppResourceRelation> rels = appResourceRelationMapper.selectByExample(example);
    	if (CollectionUtils.isEmpty(rels)) {
    		throw new QanatBizException("no app resouces");
    	}
    	AppResourceRelation ref = rels.get(0);
    	ResourceExample example1 = new ResourceExample();
    	example1.createCriteria().andResourceNameEqualTo(ref.getResourceName()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andResourceNameEqualTo(ref.getResourceName());
    	List<com.aliyun.wormhole.qanat.dal.domain.Resource> resources = resourceMapper.selectByExampleWithBLOBs(example1);
    	if (CollectionUtils.isEmpty(resources)) {
    		throw new QanatBizException("no app resouces");
    	}
    	com.aliyun.wormhole.qanat.dal.domain.Resource resource = resources.get(0);
    	JSONObject metaJson = JSON.parseObject(resource.getMeta());
		return metaJson;
	}
	
	public String getFullLinkSinkWithClause(String tenantId, String appName) {
		JSONObject json = getSlsConfByAppName(tenantId, appName);
		String withClause = "\n" + 
				"  type ='sls',\n" + 
				"  endPoint ='" + json.getString("endPoint") + "',\n" + 
				"  accessId ='" + json.getString("accessId") + "',\n" + 
				"  accessKey ='" + json.getString("accessKey") + "',\n" + 
				"  project ='" + json.getString("project") + "',\n" + 
				"  logStore ='" + json.getString("logStore") + "'\n";
		return withClause;
	}
}