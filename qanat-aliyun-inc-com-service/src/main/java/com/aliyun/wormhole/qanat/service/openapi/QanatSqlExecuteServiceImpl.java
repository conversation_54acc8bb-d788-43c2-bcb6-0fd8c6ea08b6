package com.aliyun.wormhole.qanat.service.openapi;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.security.SecurityUtil;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.openapi.QanatSqlExecuteService;
import com.aliyun.wormhole.qanat.openapi.model.ApiResult;
import com.aliyun.wormhole.qanat.openapi.model.SqlExecuteRequest;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@HSFProvider(serviceInterface = QanatSqlExecuteService.class)
public class QanatSqlExecuteServiceImpl extends OpenApiBase implements QanatSqlExecuteService {

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private DbInfoMapper dbInfoMapper;

    @Override
    public ApiResult<List<Map<String, Object>>> execute(SqlExecuteRequest request) {
        log.info("begin execute({})", JSON.toJSONString(request));
        long startTs = System.currentTimeMillis();
        ApiResult<List<Map<String, Object>>> result = new ApiResult<>();
        List<Map<String, Object>> dataList = new ArrayList<>();
        result.setCode("200");
        result.setSuccess(true);
        result.setData(dataList);
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        DateFormat format = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            if (!checkAccessKey(request)) {
                throw new QanatBizException("AK failed");
            }
            JSONObject dbMetaJson = this.getAdbDbMeta(request.getDbName());
            if (dbMetaJson == null) {
                return null;
            }
            long connStartTs = System.currentTimeMillis();
            RdsConnectionParam param = new RdsConnectionParam();
            param.setUrl(dbMetaJson.getString("jdbcUrl")).setUserName(dbMetaJson.getString("username")).setPassword(dbMetaJson.getString("password"));
            connection = dsHandler.connectToTable(param);
            log.info("[{}]get db conn cost:{}", request.getRequestId(), System.currentTimeMillis() - connStartTs);
            statement = connection.prepareStatement(SecurityUtil.escapeSql(request.getSql()));
            if (request.getParams() != null && request.getParams().size() > 0) {
                for (int i = 0; i < request.getParams().size(); i++) {
                    statement.setObject(i + 1, request.getParams().get(i));
                }
            }
            long execStartTs = System.currentTimeMillis();
            resultSet = statement.executeQuery();
            log.info("[{}]db exec cost:{}", request.getRequestId(), System.currentTimeMillis() - execStartTs);
            while (resultSet.next()) {
                Map<String, Object> data = new HashMap<>();
                dataList.add(data);
                int count = resultSet.getMetaData().getColumnCount();
                for (int idx = 1; idx <= count; idx++) {
                    String val, columnClassName = resultSet.getMetaData().getColumnClassName(idx);

                    if (StringUtils.equals("java.sql.Timestamp", columnClassName)) {
                        Timestamp timestamp = resultSet.getTimestamp(idx);

                        if (null != timestamp) {
                            val = format.format(timestamp);
                        } else {
                            val = "";
                        }
                    } else {
                        val = resultSet.getString(idx);
                    }

                    if (null == val) {
                        val = "";
                    }
                    data.put(resultSet.getMetaData().getColumnLabel(idx), val);
                }
            }
            log.info("[{}]sql exec finished, cost:{}", request.getRequestId(), System.currentTimeMillis() - startTs);
        } catch (Exception e) {
            log.error("[{}]sql exec failed, error:{}", request.getRequestId(), e.getMessage(), e);
            result.setCode("500");
            result.setSuccess(false);
            result.setData(null);
            result.setMessage(e.getMessage());
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                    resultSet = null;
                }
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    statement = null;
                }
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    connection = null;
                }
            }
        }
        return result;
    }

    private JSONObject getAdbDbMeta(String dbName) {
        DbInfoExample example = new DbInfoExample();
        example.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("no db found");
        }
        String dbMeta = dbs.get(0).getMeta();
        JSONObject dbMetaJson = JSON.parseObject(dbMeta);
        return dbMetaJson;
    }
}
