package com.aliyun.wormhole.qanat.process;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.security.util.StringUtils;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTaskExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyuncs.foas.model.v20181111.GetJobResponse;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 流计算任务成本
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class StreamTaskComputeCostProcessor extends JavaProcessor {
    
    @Resource 
    private DatatubeInstanceMapper datatubeInstanceMapper;
    
    @Resource 
    private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;
    
    @Resource 
    private BlinkService blinkService;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
            log.info("start StreamTaskComputeCostProcessor, param=[]", context.getJobParameters());
            String tenantId = paramsJson.getString("tenantId");
            DatatubeInstanceExample diExample = new DatatubeInstanceExample();
            diExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L);
            List<DatatubeInstance> diList = datatubeInstanceMapper.selectByExample(diExample);
            Date now = new Date();
            if (CollectionUtils.isNotEmpty(diList)) {
            	for (DatatubeInstance inst : diList) {
	            	DatatubeInstanceTaskExample example = new DatatubeInstanceTaskExample();
	            	example.createCriteria().andTenantIdEqualTo(inst.getTenantId()).andDatatubeInstIdEqualTo(inst.getId()).andIsDeletedEqualTo(0L);
	            	List<DatatubeInstanceTask> ditList = datatubeInstanceTaskMapper.selectByExample(example);
	            	if (CollectionUtils.isNotEmpty(ditList)) {
	            		BigDecimal sumCUs = ditList.stream().filter(e -> e.getTaskCu() != null).map(e -> e.getTaskCu()).reduce(new BigDecimal("0"), BigDecimal::add);
	            		DatatubeInstance updInst = new DatatubeInstance();
	            		updInst.setId(inst.getId());
	            		updInst.setGmtModified(now);
	            		updInst.setComputeCost(sumCUs);
	            		datatubeInstanceMapper.updateByPrimaryKeySelective(updInst);
	            		for (DatatubeInstanceTask dit : ditList) {
	            			if ("blink_stream".equalsIgnoreCase(dit.getTaskType())
	            					|| "blink_restart".equalsIgnoreCase(dit.getTaskType())) {
	            				Map<String, Object> resource = blinkService.getInstanceResource(inst.getTenantId(), inst.getAppName(), dit.getTaskName());
	            				if (resource != null) {
		            				BigDecimal vcoreCU = resource.get("allocatedVirtualCores") != null ? new BigDecimal(resource.get("allocatedVirtualCores").toString()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");
		            				BigDecimal mbCU = resource.get("allocatedMB") != null ? new BigDecimal(resource.get("allocatedMB").toString()).divide(new BigDecimal("4096"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");
		            				DatatubeInstanceTask record = new DatatubeInstanceTask();
		            				record.setId(dit.getId());
		            				record.setGmtModified(now);
		            				record.setModifyEmpid("schedulerx");
		            				record.setTaskCu(vcoreCU.compareTo(mbCU) > 0 ? vcoreCU : mbCU);

		            	    		GetJobResponse.Job job = JSON.parseObject(blinkService.getJobDetail(inst.getTenantId(), inst.getAppName(), dit.getTaskName()), GetJobResponse.Job.class);
		                    		if (job != null && StringUtils.isNotBlank(job.getPlanJson()) && CollectionUtils.isNotEmpty((JSON.parseObject(job.getPlanJson()).getJSONArray("nodes")))) {
		                    			record.setParallel(JSON.parseObject(job.getPlanJson()).getJSONArray("nodes").getJSONObject(0).getInteger("parallelism"));
		                    		}
		                     		if (job != null) {
		                     			record.setTaskScript(job.getCode());
		                     		}
		            				datatubeInstanceTaskMapper.updateByPrimaryKeySelective(record);
	            				}
	            			} else if ("blink_batch".equalsIgnoreCase(dit.getTaskType())) {
								DatatubeInstanceTask record = new DatatubeInstanceTask();
								record.setId(dit.getId());
								record.setGmtModified(now);
								record.setModifyEmpid("schedulerx");

								Map<String, Object> resource = blinkService.getInstanceResource(inst.getTenantId(), inst.getAppName(), dit.getTaskName());
								if (resource != null) {
									BigDecimal vcoreCU = resource.get("allocatedVirtualCores") != null ? new BigDecimal(resource.get("allocatedVirtualCores").toString()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");
									BigDecimal mbCU = resource.get("allocatedMB") != null ? new BigDecimal(resource.get("allocatedMB").toString()).divide(new BigDecimal("4096"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");
									record.setTaskCu(vcoreCU.compareTo(mbCU) > 0 ? vcoreCU : mbCU);
								}
								GetJobResponse.Job job = JSON.parseObject(blinkService.getJobDetail(inst.getTenantId(), inst.getAppName(), dit.getTaskName()), GetJobResponse.Job.class);
								if (job != null && StringUtils.isNotBlank(job.getPlanJson()) && CollectionUtils.isNotEmpty((JSON.parseObject(job.getPlanJson()).getJSONArray("nodes")))) {
									record.setParallel(JSON.parseObject(job.getPlanJson()).getJSONArray("nodes").getJSONObject(0).getInteger("parallelism"));
								}
								if (job != null) {
									record.setTaskScript(job.getCode());
								}
								datatubeInstanceTaskMapper.updateByPrimaryKeySelective(record);
							}
	            		}
	            	}
            	}
            }
        } catch (Exception e) {
            log.error("StreamTaskComputeCostProcessor任务调度异常", e);
            return new ProcessResult(false);
        }
        return new ProcessResult(true);
    }
}