package com.aliyun.wormhole.qanat.service.metaq.common;

import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerOrderly;
import com.alibaba.rocketmq.common.consumer.ConsumeFromWhere;
import com.alibaba.rocketmq.common.protocol.heartbeat.MessageModel;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.metaq.client.MetaPushConsumer;
import com.taobao.mtop.commons.utils.CollectionUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public abstract class AbstractConsumer {

    private static Logger log = LoggerFactory.getLogger(AbstractConsumer.class);
    protected volatile MetaPushConsumer consumer;
    protected volatile List<MetaPushConsumer> consumerList;
    protected static final Integer DEFAULT_CONSUMER_THREAD_NUM = 20;

    public AbstractConsumer() {
    }

    public synchronized void start(Integer maxConsumeThread) {
        if (this.consumer != null) {
            log.error("metaq消费者客户端只需启动一次");
        } else if (CollectionUtils.isNotEmpty(this.consumerList)) {
            log.error("metaq多分组消费者客户端只需启动一次");
        } else {
            if (this.consumerByTag()) {
                this.consumerList = this.createConsumerByGroup(maxConsumeThread, (Integer)null, (Integer)null);
            } else {
                this.consumer = this.createConsumer(maxConsumeThread);
            }

        }
    }

    protected MetaPushConsumer createConsumer(Integer maxConsumeThread) {
        return this.createConsumer(maxConsumeThread, (Integer)null, (Integer)null);
    }

    protected List<MetaPushConsumer> createConsumerByGroup(Integer maxConsumeThread, Integer batchSize, Integer pullSize) {
        List<MetaPushConsumer> metaqConsumerList = new ArrayList();
        Map<String, Map<String, String>> group_topic = this.getTopicGroupConfig();
        if (CollectionUtil.isEmpty(group_topic)) {
            log.error("metaq多分组客户端group分组为空!");
            throw new IllegalStateException("没有设置点对点group,topic");
        } else {
            Iterator var6 = group_topic.entrySet().iterator();

            while(var6.hasNext()) {
                Map.Entry<String, Map<String, String>> entry = (Map.Entry)var6.next();
                String group = (String)entry.getKey();

                try {
                    MetaPushConsumer consumer = new MetaPushConsumer(group);
                    consumer.setMessageModel(this.getMessageModel());
                    consumer.setConsumeFromWhere(this.getConsumeFromWhere());
                    if (batchSize != null && batchSize > 0) {
                        consumer.setConsumeMessageBatchMaxSize(batchSize);
                    }

                    if (pullSize != null && pullSize > 0) {
                        consumer.setPullBatchSize(pullSize);
                    }

                    consumer.setConsumeMessageBatchMaxSize(1);
                    this.setConsumeThread(maxConsumeThread, consumer);
                    Map<String, String> topicConfig = (Map)entry.getValue();
                    if (CollectionUtil.isEmpty(new Map[]{topicConfig})) {
                        throw new IllegalStateException("没有设置topic");
                    }

                    Iterator var11 = topicConfig.entrySet().iterator();

                    while(var11.hasNext()) {
                        Map.Entry<String, String> entryTopic = (Map.Entry)var11.next();
                        consumer.subscribe((String)entryTopic.getKey(), (String)entryTopic.getValue());
                    }

                    consumer.setInstanceName(this.getInstanceName() + "_" + group);
                    consumer.registerMessageListener(this.getMessageListener());
                    log.error("开始启动点对点metaq客户端group={},topic={},config={}", new Object[]{group, topicConfig, consumer});
                    consumer.start();
                    log.error("metaq点对点客户端启动完成");
                    metaqConsumerList.add(consumer);
                } catch (Exception var13) {
                    log.error("启动" + this.getClass().getName() + "失败：", var13);
                    throw new RuntimeException("metaq客户端启动失败", var13);
                }
            }

            return metaqConsumerList;
        }
    }

    private void setConsumeThread(Integer maxConsumeThread, MetaPushConsumer consumer) {
        if (maxConsumeThread != null) {
            consumer.setConsumeThreadMax(maxConsumeThread);
            consumer.setConsumeThreadMin(maxConsumeThread);
        } else if (this.getMaxConsumerThread() > 0) {
            consumer.setConsumeThreadMax(this.getMaxConsumerThread());
            consumer.setConsumeThreadMin(this.getMaxConsumerThread());
        }

    }

    protected MetaPushConsumer createConsumer(Integer maxConsumeThread, Integer batchSize, Integer pullSize) {
        try {
            MetaPushConsumer consumer = new MetaPushConsumer(this.getGroup());
            consumer.setInstanceName(this.getInstanceName());
            consumer.setMessageModel(this.getMessageModel());
            consumer.setConsumeFromWhere(this.getConsumeFromWhere());
            if (batchSize != null && batchSize > 0) {
                consumer.setConsumeMessageBatchMaxSize(batchSize);
            }

            if (pullSize != null && pullSize > 0) {
                consumer.setPullBatchSize(pullSize);
            }

            consumer.setConsumeMessageBatchMaxSize(1);
            this.setConsumeThread(maxConsumeThread, consumer);
            Map<String, String> topicConfig = this.getTopicConfig();
            if (CollectionUtil.isEmpty(new Map[]{topicConfig})) {
                throw new IllegalStateException("没有设置topic");
            } else {
                Iterator var6 = topicConfig.entrySet().iterator();

                while(var6.hasNext()) {
                    Map.Entry<String, String> entry = (Map.Entry)var6.next();
                    consumer.subscribe((String)entry.getKey(), (String)entry.getValue());
                }

                if (this.useOrderlyMessageListener()) {
                    if (null == this.getOrderlyMessageListener()) {
                        throw new Exception("getOrderlyMessageListener return null..");
                    }

                    consumer.registerMessageListener(this.getOrderlyMessageListener());
                } else {
                    if (null == this.getMessageListener()) {
                        throw new Exception("getMessageListener return null..");
                    }

                    consumer.registerMessageListener(this.getMessageListener());
                }

                log.info("开始启动metaq客户端group={},topic={},config={}", new Object[]{this.getGroup(), topicConfig, consumer});
                consumer.start();
                log.info("metaq客户端启动完成");
                return consumer;
            }
        } catch (Exception var8) {
            log.error("启动" + this.getClass().getName() + "失败：", var8);
            throw new RuntimeException("metaq客户端启动失败", var8);
        }
    }

    @AteyeInvoker(description = "设置metaq拉取数据量", paraDesc = "batchSize&pullSize")
    public void setPullSize(int batchSize, int pullSize) {
        this.consumer.shutdown();
        this.consumer = this.createConsumer(this.getMaxConsumerThread(), batchSize, pullSize);
    }

    public synchronized void start() {
        this.start((Integer)null);
    }

    public synchronized boolean isStart() {
        return this.consumer != null || CollectionUtils.isNotEmpty(this.consumerList);
    }

    @AteyeInvoker(description = "暂停metaq消费客户端")
    public synchronized void suspend() {
        try {
            if (this.consumerByGroupAndTag()) {
                Iterator iterator = this.consumerList.iterator();
                while(iterator.hasNext()) {
                    MetaPushConsumer mpc = (MetaPushConsumer)iterator.next();
                    mpc.suspend();
                }
            } else {
                this.consumer.suspend();
            }
        } catch (Exception e) {
            log.error("metaq消息接收客户端暂停失败", e);
        }

    }

    @AteyeInvoker(description = "恢复metaq消费客户端")
    public synchronized void resume() {
        try {
            if (this.consumerByGroupAndTag()) {
                Iterator iterator = this.consumerList.iterator();

                while(iterator.hasNext()) {
                    MetaPushConsumer mpc = (MetaPushConsumer)iterator.next();
                    mpc.resume();
                }
            } else {
                this.consumer.resume();
            }
        } catch (Exception e) {
            log.error("metaq消息接收客户端恢复失败", e);
        }

    }

    @AteyeInvoker(description = "关闭metaq消费客户端")
    public synchronized void shutdown() {
        try {
            if (this.consumerByGroupAndTag()) {
                Iterator var1 = this.consumerList.iterator();

                while(var1.hasNext()) {
                    MetaPushConsumer mpc = (MetaPushConsumer)var1.next();
                    mpc.shutdown();
                }
            } else {
                this.consumer.shutdown();
            }
        } catch (Exception e) {
            log.error("关闭metaq消费客户端失败", e);
        }

    }

    public abstract Map<String, String> getTopicConfig();

    public boolean consumerByGroupAndTag() {
        return false;
    }

    protected boolean useOrderlyMessageListener() {
        return false;
    }

    public Map<String, Map<String, String>> getTopicGroupConfig() {
        return new HashMap();
    }

    public abstract String getGroup();

    public abstract MessageListenerConcurrently getMessageListener();

    public MessageListenerOrderly getOrderlyMessageListener() {
        return null;
    }

    protected abstract String getInstanceName();

    protected abstract MessageModel getMessageModel();

    public abstract String getUniqueName();

    protected abstract ConsumeFromWhere getConsumeFromWhere();

    protected int getMaxConsumerThread() {
        return DEFAULT_CONSUMER_THREAD_NUM;
    }

    @AteyeInvoker(description = "设置客户端消费线程个数")
    public void setConsumerThread(Integer consumerThread) {
        if (consumerThread != null && consumerThread > 0) {
            if (this.consumerByGroupAndTag()) {
                Iterator iterator = this.consumerList.iterator();

                while(iterator.hasNext()) {
                    MetaPushConsumer mpc = (MetaPushConsumer)iterator.next();
                    mpc.shutdown();
                }
                this.consumerList = this.createConsumerByGroup(consumerThread, (Integer)null, (Integer)null);
            } else if (this.consumer != null) {
                this.consumer.shutdown();
                this.consumer = this.createConsumer(consumerThread);
            }
        }

    }
    public boolean consumerByTag() {
        return false;
    }
}
