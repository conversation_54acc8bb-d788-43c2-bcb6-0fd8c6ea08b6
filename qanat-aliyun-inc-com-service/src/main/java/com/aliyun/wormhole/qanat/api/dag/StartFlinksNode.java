package com.aliyun.wormhole.qanat.api.dag;

import lombok.Data;

@Data
public class StartFlinksNode extends Node {
    private String jobNames;
    private String startTimePolicy;
    private boolean isRestart = false;

    public StartFlinksNode() {};

    public StartFlinksNode(String id, Dag dag) {
        super(id, dag);
        this.setNodeAction(NodeAction.STREAM);
        this.setAction("com.aliyun.wormhole.qanat.job.QanatStartFlinksJobProcessor");
    }
}
