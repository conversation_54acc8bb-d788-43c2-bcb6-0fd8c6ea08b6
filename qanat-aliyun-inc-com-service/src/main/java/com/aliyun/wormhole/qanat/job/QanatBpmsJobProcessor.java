package com.aliyun.wormhole.qanat.job;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.alipmc.api.ProcessInstanceService;
import com.alibaba.alipmc.api.model.bpm.ProcessInstance;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.BpmsNode;
import com.aliyun.wormhole.qanat.api.dag.DagInstStatus;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * BPMS任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatBpmsJobProcessor extends JavaProcessor {
    
    @Resource
    private ProcessInstanceService processInstanceService;
    
    @Resource
    private TaskInfoMapper taskInfoMapper;
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Value("${qanat.bpms.authKey}")
    private String bpmsAuthKey;

    @Override
    public ProcessResult process(JobContext context) {
        String taskName = context.getTaskName();
        log.info("Qanat BPMS Job[{}] start.", taskName);
        try {
            Map<String, Object> instParamsMap = null;
            if (StringUtils.isNotBlank(context.getInstanceParameters())) {
                instParamsMap = (Map<String, Object>)JSON.parseObject(context.getInstanceParameters(), Map.class);
            }
            if (instParamsMap == null) {
                instParamsMap = (Map<String, Object>)JSON.parseObject(context.getJobParameters(), Map.class);
            }
            BpmsNode bpms = ((JSONObject)instParamsMap.get("node")).toJavaObject(BpmsNode.class);
            String operator = (String)instParamsMap.get("operator");
            String requestId = (String)instParamsMap.get("requestId");
            String subTaskInstId = String.valueOf(instParamsMap.get("subTaskInstId"));
            String orginatorId = operator.length() < 6 ? String.format("%06d", Integer.valueOf(operator)) : operator;

            TaskInstance taskInstUpd = new TaskInstance();
            taskInstUpd.setId(Long.valueOf(subTaskInstId));
            taskInstUpd.setGmtModified(new Date());
            taskInstUpd.setModifyEmpid(operator);
            taskInstUpd.setExternalInstId(context.getJobInstanceId() + "");//SchedulerX任务实例id
            taskInstUpd.setStatus(DagInstStatus.EXECUTING.getCode().byteValue());
            taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
            
            Map<String, String> initData = new HashMap<String, String>(16);
            ProcessInstance processInstance = processInstanceService.startProcessInstance(bpms.getBpmsCode(), "Qanat人工审核流程",
                orginatorId, initData, bpmsAuthKey);
            if (processInstance == null || StringUtils.isBlank(processInstance.getProcessInstanceId())) {
                log.error("[{}]启动Qanat人工审核流程[{}]失败", requestId, bpms.getBpmsCode());
                throw new QanatBizException("启动Qanat人工审核流程[" + bpms.getBpmsCode() + "]失败");
            }
            
            TaskInstance taskUpd = new TaskInstance();
            taskUpd.setId(Long.valueOf(subTaskInstId));
            taskUpd.setTaskCommand("BPMS:" + processInstance.getProcessInstanceId());
            taskUpd.setGmtModified(new Date());
            taskInstanceMapper.updateByPrimaryKeySelective(taskUpd);

            while (true) {
                TaskInstance subTaskInst = taskInstanceMapper.selectByPrimaryKey(taskUpd.getId());
                if (DagInstStatus.SUCCESS.getCode().byteValue() == subTaskInst.getStatus()) {
                    break;
                } else if (DagInstStatus.FAILED.getCode().byteValue() == subTaskInst.getStatus()) {
                    return new ProcessResult(false);
                }
                Thread.sleep(30000);//30刷新
            }
            taskInstUpd = new TaskInstance();
            taskInstUpd.setId(Long.valueOf(subTaskInstId));
            taskInstUpd.setGmtModified(new Date());
            taskInstUpd.setEndTime(new Date());
            taskInstUpd.setModifyEmpid(operator);
            taskInstUpd.setStatus(DagInstStatus.SUCCESS.getCode().byteValue());
            taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
            log.info("[{}]启动Qanat人工审核流程[{}]成功", requestId, bpms.getBpmsCode());
        } catch (QanatBizException e) {
            log.error("BPMS任务调度异常:{}", e.getMessage());
            return new ProcessResult(false);
        } catch (Exception e) {
            log.error("BPMS任务调度异常", e);
            return new ProcessResult(false);
        }
        return new ProcessResult(true);
    }
    
    @Override
    public void kill(JobContext context) {
        // TODO Auto-generated method stub
    }
}