package com.aliyun.wormhole.qanat.service.viewmodel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.dto.ResourcePackage;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.FlowCtlService;
import com.aliyun.wormhole.qanat.api.service.KafkaManagementService;
import com.aliyun.wormhole.qanat.dal.domain.AppInfo;
import com.aliyun.wormhole.qanat.dal.domain.AppInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.Extension;
import com.aliyun.wormhole.qanat.dal.domain.ExtensionExample;
import com.aliyun.wormhole.qanat.dal.mapper.AppInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ExtensionMapper;
import com.aliyun.wormhole.qanat.service.util.YamlUtil;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.DataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Field;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.RelatedDataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Relation;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class MdpObjectProcessor {
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private KafkaManagementService kafkaManagementService;
    
    @Resource
    private AppInfoMapper appInfoMapper;
    
    @Resource
    private DatasourceService dsInfoService;
    
    @Resource
    private ViewModelSqlBuilder viewModelSqlBuilder;
    
    @Resource
    private FullLinkProcessor fullLinkProcessor;

    @Resource
    private ExtensionMapper extensionMapper;
    
    @Resource
    private LookupProcessor lookupProcessor;
    
    @Resource
    private ViewModelOptimizer viewModelOptimizer;
	
	@Resource
	private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;
	
	@Resource
	private FlowCtlService limiterService;
    
    @Value("${datatube.codegen.version}")
    private String codegenVersion;

	public static String BLINK_MDP_CORRECT_SQL = 
			"create table correct_mq_source_%s (\n" + 
			"    id varchar,\n" + 
			"    __traceId__ varchar header\n" + 
			") with (\n" + 
			"  type = 'custom',\n" + 
			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"  topic = '%s',\n" + 
			"  `group.id` = '%s',\n" + 
			"  `dbName` = '%s',\n" + 
			"  startupMode = 'TIMESTAMP',\n" + 
			"  fieldDelimiter = '`'\n" + 
			");\n" + 
			"\n" + 
			"create view v_correct_%s as\n" + 
			"select\n" + 
			"    %s\n" + 
			"from\n" + 
			"      correct_mq_source_%s as a\n" + 
			"      LEFT JOIN LATERAL TABLE (queryDim('%s', '%s', a.id)) as b(x) ON TRUE\n" + 
			";\n" + 
			"\n" + 
			"insert into adb_sink\n" + 
			"select * from v_correct_%s;\n" +
			"\n" + 
			"insert\n" + 
			"  into full_link_sink\n" + 
			"select\n" + 
			"  __traceId__ as trace_id,\n" + 
			"  id as key,\n" + 
			"  NOW() as ts,\n" + 
			"  '%s' as db,\n" + 
			"  CURRENT_TIMESTAMP as gmt_create\n" + 
			"from correct_mq_source_%s;\n";

	public static String BLINK_SYNC_ARRAY_OBJECT_MDP_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--Version: %s\n" + 
			"--********************************************************************--\n" + 
			"create table mq_source (\n" + 
            "    msg varchar,\r\n" + 
            "    __traceId__ varchar header\r\n" +
            ") with (\r\n" + 
            "  type = 'custom',\r\n" + 
            "  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\r\n" + 
            "  topic = '%s',\r\n" + 
            "  `group.id` = '%s',\r\n" + 
            "  `dbName` = '%s',\r\n" + 
            "  startupMode = 'TIMESTAMP',\r\n" + 
            "  fieldDelimiter = '`'\r\n" + 
			");\n" + 
			"\n" + 
			"create table adb_sink (\n" +
			"    %s,\n" + 
			"    __trace_id__ varchar,\n" + 
			"    primary key(%s)\n" + 
			") with (\n" + 
			"    type = 'QANAT_ADB30',\n" + 
			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
			"    dbName='%s',\n" + 
			"    tableName='%s',\n" + 
			"    replaceMode = 'update_by_pk',\n" + 
			"    streamType = 'kafka',\n" + 
			"    eventTopic = '%s',\n" + 
			"    eventServer = '%s'\n" + 
			");\n" + 
			"\n" + 
			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" + 
			"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
			"\n" + 
			"create view v_mq as \n" + 
			"select \n" + 
			"    JSON_VALUE (t.x, '$.tag_code') AS tag_code,\n" + 
			"    JSON_VALUE (t.x, '$.tag_value') AS tag_value,\n" + 
			"    JSON_VALUE (t.x, '$.object_unique_code') AS object_unique_code,\n" + 
			"    JSON_VALUE (t.x, '$.object_biz_id') AS object_biz_id,\n" + 
			"    eagleTraceId as __trace_id__\n" + 
			"from\n" + 
			"    mq_source as s,\n" + 
			"    LATERAL TABLE (parseDrcFields (msg, 'tag_code', 'tag_value', 'object_unique_code', 'object_biz_id')) as t(x)\n" + 
			"where JSON_VALUE(t.x, '$.tag_code')='%s' and JSON_VALUE(t.x, '$.object_unique_code')='%s'\n" + 
			";\n" + 
			"create view v_fk as \n" + 
			"select\n" + 
			"  %s,\n" +
			"  a.__trace_id__\n" + 
			"from\n" + 
			"      v_mq as a, LATERAL TABLE (queryDim('%s', 'select object_biz_id,%s from tag_meta_tag_object_biz_relation where is_deleted=0 and object_unique_cod=''%s'' and object_biz_id=? group by object_biz_id', a.object_biz_id)) as b (x)\n" +  
			";\n" + 
			";\n" + 
			"create view v_group as \n" + 
			"select\n" + 
			"  %s,\n" + 
			"  JSON_VALUE (b.x, '$.%s') AS %s,\n" + 
			"  a.__trace_id__\n" + 
			"from\n" + 
			"      v_fk as a\n" + 
			"      LEFT JOIN LATERAL TABLE (queryDim('%s', '%s', %s)) as b (x) ON TRUE\n" +  
			";" + 
			"\n" + 
			"insert into adb_sink\n" + 
			"select * from v_group;";
	
	public static String BLINK_OBJ_INCR_CHECK_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--********************************************************************--\n" + 
            "CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
            "CREATE FUNCTION delayMs AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDelayMsUdf';\n" +
			"CREATE FUNCTION checkData AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDataCheckUdf';\n" + 
			"CREATE FUNCTION qanatConcat AS 'com.aliyun.wormhole.qanat.blink.udf.QanatConcatUdf';\n" + 
			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" +
            "\n" + 
            "%s\n" + 
			"\n" + 
            "create view v_check_id AS\n" + 
            "select \n" + 
            "    JSON_VALUE(msg, '$.extParam.objectInstanceVO.objectBizId') as id,\n" + 
            "    'drc' as src,\n" + 
            "    delayMs(%s, msg) as msg,\n" + 
            "    JSON_VALUE(msg, '$.extParam.objectInstanceVO.objectBizId') as `key`,\n" + 
            "    JSON_VALUE(msg, '$.operateType') as eventType,\n" +
            "    __traceId__\n" +
            "from drc_source\n" + 
            ";" +
            "\n" +
			"%s";
	
	public static String BLINK_CHECK_COMPUTE_FOR_MAIN_OBJECT_SQL = 
			"\n" + 
			"create view v_check as\n" + 
			"select \n" + 
			"    a.id,\n" + 
			"    a.src,\n" + 
			"    checkData('%s',\n" + 
			"    qanatConcat('|', %s),\n" + 
			"    qanatConcat('|', %s)) as msg,\n" + 
			"    JSON_VALUE(c.x, '$.%s') as pk,\n" + 
			"    a.eventType," +
			"    a.__traceId__\n" +
			"from v_check_id as a\n" + 
			"LEFT JOIN LATERAL TABLE (queryDim('%s', '%s', a.id)) as b(x) ON TRUE\n" + 
			"LEFT JOIN LATERAL TABLE (queryDim('%s', 'select %s from %s where %s=?', a.id)) as c(x) ON TRUE\n" + 
			";\n" + 
			"\n" + 
			"create view v_check_result as \n" + 
			"select (case when msg='' then concat_ws('|', '%s', id, src, 'OK') else concat_ws('|', '%s', id, src, msg) end) as msg, id as `key`, pk, eventType, __traceId__\n" + 
			"from v_check;\n" +  
			"\n" + 
			"CREATE TABLE full_link_sink (\n" + 
			"  trace_id varchar,\n" + 
			"  pk bigint,\n" +
			"  key varchar,\n" + 
			"  ts bigint,\n" + 
			"  db varchar,\n" + 
			"  msg varchar,\n" +
			"  gmt_create timestamp\n" + 
			") WITH (\n" + 
			"  %s" + 
			");\n" + 
			"\n" + 
			"insert into full_link_sink\n" + 
			"select\n" + 
			"  __traceId__ as trace_id,\n" + 
			"  cast(pk as bigint) as pk,\n" +
			"  split_index (msg, '|', 1) as key,\n" + 
			"  NOW() as ts,\n" + 
			"  '%s' as db,\n" + 
			"  msg,\n" + 
			"  CURRENT_TIMESTAMP as gmt_create\n" + 
			"from v_check_result;\n"
			;

	public static String BLINK_OBJ_FULLCOLUMN_BATCH_CHECK_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--Version: %s\n" + 
			"--********************************************************************--\n" + 
			"CREATE FUNCTION checkData AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDataCheckUdf';\n" + 
			"CREATE FUNCTION qanatConcat AS 'com.aliyun.wormhole.qanat.blink.udf.QanatConcatUdf';\n" + 
			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" +
			"CREATE FUNCTION groovyFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatFunctionUdf';\r\n" +
			"CREATE FUNCTION dfaasFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDfaasFunctionUdf';\r\n" +
			"CREATE TABLE check_all_source (\n" + 
			"  %s,\n" + 
			"  primary key(%s)\n" +
			") WITH (\n" + 
			"    %s\n" + 
			");\n" + 
			"\n" + 
			"create view v_main_obj as\n" + 
			"select \n" + 
			"    %s\n" +
			"from check_all_source as a, LATERAL TABLE (queryDim('%s', '%s', a.%s)) as b(x);\n" + 
			"\n" + 
			"create view v_correct as\n" + 
			"%s;\n" + 
			"%s";
	
	public static String BLINK_CHECK_COMPUTE_FOR_FULL_COLUMN_SQL = 
			"\n" + 
			"create view v_check as\n" + 
			"select \n" + 
			"    a.%s as id,\n" + 
			"    'fcbatch' as src,\n" + 
			"    checkData('%s',\n" + 
			"    qanatConcat('|', %s),\n" + 
			"    qanatConcat('|', %s)) as msg\n" + 
			"from v_correct as a\n" + 
			"LEFT JOIN LATERAL TABLE (queryDim('%s', 'select %s from %s where %s=?', a.%s)) as c(x) ON TRUE\n" + 
			";\n" + 
			"\n" + 
			"create view v_check_result as \n" + 
			"select (case when msg='' then concat_ws('|', '%s', id, src, 'OK') else concat_ws('|', '%s', id, src, msg) end) as msg, id as `key`\n" + 
			"from v_check;\n" +  
			"\n" + 
			"CREATE TABLE full_link_sink (\n" + 
			"  trace_id varchar,\n" + 
			"  pk bigint,\n" +
			"  key varchar,\n" + 
			"  ts bigint,\n" + 
			"  db varchar,\n" + 
			"  msg varchar,\n" +
			"  gmt_create timestamp\n" + 
			") WITH (\n" + 
			"  %s" + 
			");\n" + 
			"\n" + 
			"insert into full_link_sink\n" + 
			"select\n" + 
			"  UUID() as trace_id,\n" + 
			"  key as pk,\n" +
			"  split_index (msg, '|', 1) as key,\n" + 
			"  NOW() as ts,\n" + 
			"  '%s' as db,\n" + 
			"  msg,\n" + 
			"  CURRENT_TIMESTAMP as gmt_create\n" + 
			"from v_check_result;\n"
			;

    public boolean processIncrSyncJob(String tenantId, String appName, String jobName, JSONObject srcDsMetaJson, List<String> dbNames, String etlDbName, String tableName, DataObject object, String operateEmpid, Boolean isAllFields, Long versionId, JSONObject kafkaJson, String drcTopicName, String logTopicName, ViewModel dataModel, String datatubeLevel, Long datatubeInstId) {
    	if (srcDsMetaJson.getJSONObject("incrConf") == null) {
    		log.info("no incr sync conf");
    		return false;
    	}
    	String consumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "-incr_sync-" + object.getCode() + "-" + versionId;
    	boolean flag = kafkaManagementService.createConsumerGroup(tenantId, appName, consumerId);
    	if (!flag) {
    		log.error("creat consumer[{}] failed", consumerId);
    	}
		limiterService.setFlowLimitIfNotExists(datatubeInstId, consumerId, 1.0);
        int parallel = viewModelOptimizer.getParallel(tenantId, datatubeLevel, srcDsMetaJson.getInteger("qph"));
    	flag = kafkaManagementService.createTopic(tenantId, appName, logTopicName, parallel);
    	if (!flag) {
    		log.error("creat topic[{}] failed", logTopicName);
    	}

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String sql = "--SQL\r\n" + 
                "--********************************************************************--\r\n" + 
                "--Author: " + operateEmpid + "\r\n" + 
                "--CreateTime: " + sdf.format(new Date()) + "\r\n" + 
                "--Comment: " + ("sync for " + tableName + " from " + object.getCode()) + "\r\n" + 
    			"--Version: " + codegenVersion + "\n" + 
                "--********************************************************************--\r\n" + 
                "create table drc_source (\r\n" + 
                "    msg varchar,\r\n" + 
                "    __traceId__ varchar header\r\n" +
                ") with (\r\n" + 
                "  type = 'custom',\r\n" + 
                "  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\r\n" + 
                "  topic = '" + drcTopicName + "',\r\n" + 
                "  `group.id` = '" + consumerId + "',\r\n" + 
                "  `dbName` = '" + kafkaJson.getString("dbName") + "',\r\n" + 
                "  startupMode = 'TIMESTAMP',\r\n" + 
                "  fieldDelimiter = '`'\r\n" + 
                ");\r\n" + 
                "\r\n";
        for (int i = 0; i < dbNames.size(); i++) {
        	sql += "create table adb3_sink_" + i + " (\r\n" + 
                "    __msg__ varchar,\r\n" + 
                "    __trace_id__ varchar\r\n" +
                ") with (\r\n" + 
                "    type='custom',\r\n" + 
                "    class = '" + ("object".equalsIgnoreCase(srcDsMetaJson.getJSONObject("incrConf").getString("src")) ? "com.aliyun.wormhole.qanat.blink.sink.MdpObjectInstanceSink" : (isAllFields ? "com.aliyun.wormhole.qanat.blink.sink.OdsMdpObjectDataModelSink" : "com.aliyun.wormhole.qanat.blink.sink.OdsMdpYamlDataModelSink")) + "',\r\n" + 
                "    dbName='" + dbNames.get(i) + "',\r\n" + 
                "    tableName='" + tableName + "',\r\n" + 
                "    datamodel='" + (("object".equalsIgnoreCase(srcDsMetaJson.getJSONObject("incrConf").getString("src")) || isAllFields) ? "" : YamlUtil.getYaml(object)) + "',\r\n" + 
                "    object_unique_code='" + object.getRef() + "',\r\n" + 
                "    object_pk='" + object.getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0).getCode() + "',\r\n";
        		if (!dataModel.isDynamic() && CollectionUtils.isNotEmpty(object.getFields())) {
        			sql += "    limit_fields='" + StringUtils.join(object.getFields().stream().filter(e->!"null".equalsIgnoreCase(e.getRef())).map(e->e.getCode()).collect(Collectors.toList()), ",") + "',\r\n";
        		}
        		if (StringUtils.isNotBlank(dataModel.getSettings().getDistributeKey())) {
        			sql += "    distribute_key='" + dataModel.getSettings().getDistributeKey() + "',\r\n";
        		}
        	if (dbNames.get(i).equalsIgnoreCase(etlDbName)) {
    			sql += "    streamType = 'kafka',\n" + 
	    			"    eventTopic = '" + logTopicName + "',\n" + 
	    			"    eventServer = '" + kafkaJson.getString("dbName") + "'\n";
        	} else {
        		sql += "    streamEvent = 'disable'\n";
        	}
        	sql += ");\r\n" + 
                "\r\n" + 
                "insert into adb3_sink_" + i + "\r\n" + 
                "select * from drc_source;\n" +
                "\n";
        }
        
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_MDP_SINK, ResourcePackage.BLINK_KAFKA010), false);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        
        return true;
    }

    public boolean processIncrCheckJob(String tenantId, String appName, String jobName, JSONObject srcDsMetaJson, String dbName, String tableName, DataObject object, String operateEmpid, Long versionId, JSONObject kafkaJson, String topicName, ViewModel dataModel, Long datatubeInstId) {
    	if (srcDsMetaJson.getJSONObject("incrConf") == null) {
    		log.info("no incr sync conf");
    		return false;
    	}
    	String checkResultTopicName = "chk-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-" + object.getCode();
    	String correctTopicName = "crt-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-" + object.getCode();
    	String consumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-incr_check-" + object.getCode() + "-" + versionId;
    	boolean res = kafkaManagementService.createConsumerGroup(tenantId, appName, consumerId);
		if (!res) {
			log.error("consumer:{} create is failed", consumerId);
		}
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String sql = String.format(BLINK_OBJ_INCR_CHECK_SQL
            , operateEmpid
            , sdf.format(new Date())
            , "incr check for " + tableName + " from " + object.getCode()
            , getObjectSourceSql(topicName, consumerId, kafkaJson.getString("dbName"))
            , dataModel.getSettings().getIncrCheckDelayMs()
            , getCheckComputeSql(tenantId, appName, srcDsMetaJson, dbName, tableName, kafkaJson, object, correctTopicName, checkResultTopicName, dataModel)
            );
        
        if ("offhand".equalsIgnoreCase(dataModel.getSettings().getCorrectPolicy())) {
	        String correctSQL = getOffhandCorrectSql(tenantId, srcDsMetaJson, dbName, tableName, object, kafkaJson,
					dataModel, correctTopicName);
			sql += correctSQL;
        }
        
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_KAFKA010, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_UDF, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_HSF_UDF), false);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        return true;
    }

    public boolean processFullColumnBatchCheckJob(String tenantId, String appName, String jobName, String dstDbName, String tableName, String operateEmpid, Long versionId, JSONObject kafkaJson, ViewModel dataModel, Long datatubeInstId) {
    	JSONObject srcDsMetaJson = dsInfoService.getDbMetaByDsName(tenantId, dataModel.getObject().getRef());
    	String checkResultTopicName = "bchk-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dstDbName) + "-" + dataModel.getObject().getCode();
    	String correctTopicName = "bcrt-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dstDbName) + "-" + dataModel.getObject().getCode();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> mainObjColsDef = new ArrayList<>();
    	List<String> mainObjColsSel = new ArrayList<>();
        String pkField = dataModel.getObject().getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0).getRef();
    	for (ViewModel.Field field : dataModel.getObject().getFields()) {
    		if (field.getObject() != null || field.isFunc()) {
    			continue;
    		} 
			mainObjColsDef.add("`" + field.getRef() + "` " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
			if ("varchar".equalsIgnoreCase(field.getType())) {
				mainObjColsSel.add("JSON_VALUE(b.x, '" + field.getCode() + "') AS " + field.getCode());
			} else {
				mainObjColsSel.add("CAST(JSON_VALUE(b.x, '" + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " ) AS " + field.getCode());
			}
    	}
        String sql = String.format(BLINK_OBJ_FULLCOLUMN_BATCH_CHECK_SQL
            , operateEmpid
            , sdf.format(new Date())
            , "full columns batch check for " + tableName + " from " + dataModel.getObject().getCode()
			, codegenVersion
            , pkField + " bigint"
            , pkField
            , checkAllScan(dataModel.getSettings(), dstDbName, tableName)
            , srcDsMetaJson.getString("dbName")
            , viewModelSqlBuilder.getSubQueryFromObject(tenantId, dataModel.getObject())
            , pkField
            , StringUtils.join(mainObjColsSel, ",")
            , viewModelSqlBuilder.getStreamSelectSql(tenantId, dataModel, dstDbName)
            , getFullColumnCheckComputeSql(tenantId, appName, srcDsMetaJson, dstDbName, tableName, kafkaJson, correctTopicName, checkResultTopicName, dataModel)
            );
        
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_HSF_UDF, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_UDF, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_MYSQL_SCAN), true);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_batch");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        return true;
    }
    
    private String getFullColumnCheckComputeSql(String tenantId, String appName, JSONObject srcDsMetaJson, String dbName, String tableName, JSONObject kafkaJson, String correctTopicName, String checkResultTopicName, ViewModel dataModel) {
        List<String> colNameList = new ArrayList<>();
        List<String> colNameSelList = new ArrayList<>();
        List<String> colDefList = new ArrayList<>();
        List<String> colNameWithAliasBList = new ArrayList<>();
        List<String> colNameWithAliasCList = new ArrayList<>();
        String pkField = dataModel.getObject().getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0).getCode();
        List<ViewModel.Field> columnList = new ArrayList<>();
		columnList.addAll(dataModel.getObject().getFields());
		if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
			for (ViewModel.RelatedDataObject object : dataModel.getRelatedObjects()) {
				List<String> joinOnFields = object.getRelations().stream().map(e -> e.getField()).collect(Collectors.toList());
				
				for (ViewModel.Field field : object.getFields()) {
					if (joinOnFields.contains(field.getCode())) {
						continue;
					}
					columnList.add(field);
				}
			}
		}
        for (ViewModel.Field field : columnList) {
        	colDefList.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
        	colNameList.add(field.getCode());
        	colNameSelList.add(("datetime".equalsIgnoreCase(field.getType()) ? ("DATE_FORMAT(" + field.getCode() + ",''%Y-%m-%d %H:%i:%s'')") : field.getCode()) + " AS " + field.getCode());
            colNameWithAliasBList.add("datetime".equalsIgnoreCase(field.getType()) ? ("DATE_FORMAT(a." + field.getCode() + ",'yyyy-MM-dd HH:mm:ss')") : ("a." + field.getCode()));
            colNameWithAliasCList.add("JSON_VALUE(c.x, '$." + field.getCode() + "')");
        }
    	String sql = String.format(BLINK_CHECK_COMPUTE_FOR_FULL_COLUMN_SQL
    		, pkField
            , StringUtils.join(colNameList, ",")
            , StringUtils.join(colNameWithAliasBList, ",")
            , StringUtils.join(colNameWithAliasCList, ",")
            , dbName
            , StringUtils.join(colNameSelList, ",")
            , tableName
            , pkField
            , pkField
            , tableName
            , tableName
            , fullLinkProcessor.getFullLinkSinkWithClause(tenantId, appName)
            , checkResultTopicName
        	);
    	sql += "\n" + 
				"create table correct_sink (\n" + 
				"    " + viewModelSqlBuilder.getStreamDdlSql(tenantId, dataModel) + ",\n" + 
				"    primary key(" + pkField + ")\n" + 
				") with (\n" + 
				"    type = 'QANAT_ADB30',\n" + 
				"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
				"    dbName='" + dbName + "',\n" + 
				"    tableName='" + tableName + "',\n" + 
				"    replaceMode = 'replace',\n" + 
			    "    writeMode = 'single',\n" +
				"    streamType = 'kafka',\n" + 
				"    eventTopic = '" + correctTopicName + "',\n" + 
				"    eventServer = '" + kafkaJson.getString("dbName") + "'\n" + 
				");\n" + 
				"\n" + 
				"insert into correct_sink\n" + 
				"select * from v_correct;\n" + 
				"\n" + 
				"insert\n" + 
				"  into full_link_sink\n" + 
				"select\n" + 
				"  UUID() as trace_id,\n" + 
				"  " + pkField + " as pk,\n" +
				"  CAST(" + pkField + " AS varchar) as key,\n" + 
				"  NOW() as ts,\n" + 
				"  '" + correctTopicName + "' as db,\n" + 
				"  '' as msg,\n" + 
				"  CURRENT_TIMESTAMP as gmt_create\n" + 
				"from v_correct;";
    	
    	return sql;
    }

	private Object checkAllScan(ViewModel.Settings setting, String dbName, String tableName) {
		String clause = 
	            "    type = 'custom',\n" + 
	            "    tableFactoryClass = 'com.aliyun.wormhole.qanat.blink.mysql.scan.MySQLScanTableFactory',\n" + 
	            "    dbName='" + dbName + "',\n" +
	            "    tableName='" + tableName + "',\n" +
	            "    batchSize='" + setting.getRdsScanBatchSize() + "'" 
	            ;
		return clause;
	}

	private String getOffhandCorrectSql(String tenantId, JSONObject srcDsMetaJson, String dbName, String tableName,
			DataObject object, JSONObject kafkaJson, ViewModel dataModel, String correctTopicName) {
		List<String> columnDefines = new ArrayList<>();
		List<String> selectColumns = new ArrayList<>();
		List<String> drcParseColumns = new ArrayList<>();
		String pkColumn = null;
        String dwdPk = dataModel.getObject().getFields().stream().filter(e -> e.isPk()).map(e -> e.getCode()).collect(Collectors.toList()).get(0);
        String dwdPkType = dataModel.getObject().getFields().stream().filter(e -> e.isPk()).map(e -> e.getType()).collect(Collectors.toList()).get(0);
        boolean relatedObjectPkEqMainObjPk = false;
		for (ViewModel.Field field : object.getFields()) {
			if (field.getObject() != null) {
				continue;
			}
			if (field.getRef() != null && field.isCompute()) {
				continue;
			}
			if ("'null'".equalsIgnoreCase(field.getRef())) {
				continue;
			}
			if (field.isPk()) {
				pkColumn = field.getCode();
			}
			if (dwdPk.equalsIgnoreCase(field.getCode())) {
				relatedObjectPkEqMainObjPk = true;
			}
			selectColumns.add("`" + field.getRef() + "`");
			columnDefines.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
			if ("varchar".equalsIgnoreCase(field.getType())) {
				drcParseColumns.add("JSON_VALUE(b.x, '$." + field.getCode() + "') as " + field.getCode());
			} else {
				drcParseColumns.add("CAST(JSON_VALUE(b.x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") as " + field.getCode());
			}
		}
		if (object instanceof RelatedDataObject) {
		    for (ViewModel.Relation rel : ((RelatedDataObject)object).getRelations()) {
		    	if (!rel.getRelatedField().startsWith("exp#")) {
		    		pkColumn = object.getFields().stream().filter(e->e.getCode().equalsIgnoreCase(rel.getField())).collect(Collectors.toList()).get(0).getCode();
		        	break;
		    	}
		    }
		}
		String correctSQL = "\n" + 
				"create table correct_sink (\n" + 
				"    " + StringUtils.join(columnDefines, ",") + ",\n" + 
				"    " + ((object instanceof RelatedDataObject && !relatedObjectPkEqMainObjPk) ? (dwdPk + " " + dwdPkType + ",\n") : "") +
				"    __trace_id__ varchar," +
				"    primary key(" + dwdPk + ")\n" + 
				") with (\n" + 
				"    type = 'QANAT_ADB30',\n" + 
				"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
				"    dbName='" + dbName + "',\n" + 
				"    tableName='" + tableName + "',\n" + 
				"    replaceMode = 'update',\n" + 
			    "    writeMode = 'single',\n" +
				"    streamType = 'kafka',\n" + 
				"    eventTopic = '" + correctTopicName + "',\n" + 
				"    eventServer = '" + kafkaJson.getString("dbName") + "'\n" + 
				");\n" + 
				"\n" + 
				"create view v_correct AS\n" + 
				"select \n" + 
				"	" + StringUtils.join(drcParseColumns, ",") + ",\n" + 
				"   " + (object instanceof RelatedDataObject ? "cast(a.pk as " + dwdPkType + ") as " + dwdPk + ",\n" : "") +
				"	__traceId__\n" +
				"from v_check_result as a\n" + 
				",LATERAL TABLE (queryDim('" + srcDsMetaJson.getString("dbName") + "', '" + viewModelSqlBuilder.getSubQueryFromObject(tenantId, object).replaceAll("'", "''") + "', a.key)) as b(x)\n" + 
				"where split_index (a.msg, '|', 3)<>'OK' and split_index (a.msg, '|', 1) is not null and split_index (a.msg, '|', 1) <> ''\n" + 
				(object instanceof RelatedDataObject ? ";" : "and a.eventType<>'CREATE';") + 
				"\n" + 
				"insert into correct_sink\n" + 
				"select * from v_correct;\n" + 
				"\n" + 
				"insert\n" + 
				"  into full_link_sink\n" + 
				"select\n" + 
				"  __traceId__ as trace_id,\n" + 
				"  cast(" + dwdPk + " as bigint) as pk,\n" +
				"  CAST(" + pkColumn + " AS varchar) as key,\n" + 
				"  NOW() as ts,\n" + 
				"  '" + correctTopicName + "' as db,\n" + 
				"  '' as msg,\n" + 
				"  CURRENT_TIMESTAMP as gmt_create\n" + 
				"from v_correct;";

		if (!(object instanceof RelatedDataObject)) {
			List<String> colDefList = new ArrayList<>();
			List<ViewModel.Field> columnList = YamlUtil.getColumnsByModel(dataModel);
			List<String> jsonParseColumnsAll = new ArrayList<>();
			List<ViewModel.Field> funcFields = new ArrayList<>();
			for (ViewModel.Field field : columnList) {
				if (field.isFunc()) {
					funcFields.add(field);
					continue;
				} 
		    	if ("varchar".equalsIgnoreCase(field.getType()) || field.getObject() != null) {
		    		jsonParseColumnsAll.add("JSON_VALUE (b.x, '$." + field.getCode() + "') as `" + field.getCode() + "`");
		    	} else {
		    		jsonParseColumnsAll.add("CAST(JSON_VALUE (b.x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") as `" + field.getCode() + "`");
		    	}
		    	String code = field.getCode();
				if (StringUtils.isBlank(code)) {
					code = field.getRef();
				}
				String type = StringUtils.isBlank(field.getType()) ? "varchar" : ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType());
				if (field.getObject() != null) {
					type = "varchar";
				}
			    colDefList.add("`" + code + "` " + type);
		    }

			Map<String, String> colMap = new HashMap<>();
			for (ViewModel.Field field : dataModel.getObject().getFields()) {
				if (field.isFunc()) {
					continue;
				} else {
					if ("varchar".equalsIgnoreCase(field.getType())) {
						colMap.put(field.getCode(), "JSON_VALUE(b.x, '$." + field.getCode() + "')");
					} else {
						colMap.put(field.getCode(), "CAST(JSON_VALUE(b.x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
					}
				}
			}
			if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
				for (RelatedDataObject relObj : dataModel.getRelatedObjects()) {
					Map<String, Relation> rels = relObj.getRelations().stream().filter(e->!e.getRelatedField().startsWith("exp#")).collect(Collectors.toMap(Relation::getField, Function.identity()));
					List<ViewModel.Field> fields = relObj.getFields().stream().filter(e->!rels.containsKey(e.getCode())).collect(Collectors.toList());
					for (ViewModel.Field field : fields) {
						if ("varchar".equalsIgnoreCase(field.getType())) {
							colMap.put(relObj.getCode() + "." + field.getCode(), "JSON_VALUE(b.x, '$." + field.getCode() + "')");
						} else {
							colMap.put(relObj.getCode() + "." + field.getCode(), "CAST(JSON_VALUE(b.x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
						}
					}
				}
			}
			for (ViewModel.Field field : funcFields) {
				String funcExpress = field.getRef();
				String funcCode = lookupProcessor.parseFuncExpress(funcExpress).get(0);
				String[] cols = lookupProcessor.parseFuncExpress(funcExpress).get(1).split(",");
				List<String> exps = new ArrayList<>();
				for (String col : cols) {
					if (col.startsWith("'") && col.endsWith("'")) {
						exps.add(col);
					} else {
						exps.add(colMap.get(col));
					}
				}
				ExtensionExample example = new ExtensionExample();
				example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andCodeEqualTo(funcCode);
				List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
				if (CollectionUtils.isEmpty(exts)) {
					log.error("funcCode:{} not found", funcCode);
					continue;
				}
				String func = exts.get(0).getPlugin().equalsIgnoreCase("groovy")?"groovyFunc":"dfaasFunc";
				String funcExp = null;
				if (field.getType().equalsIgnoreCase("varchar")) {
					funcExp = func + "('" + tenantId + "', '" + funcCode + "'," + StringUtils.join(exps, ",") + ") AS " + field.getCode();
				} else {
					funcExp = "CAST(" + func + "('" + tenantId + "', '" + funcCode + "'," + StringUtils.join(exps, ",") + ") AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + ") AS " + field.getCode();
				}
				jsonParseColumnsAll.add(funcExp);

		    	String code = field.getCode();
				if (StringUtils.isBlank(code)) {
					code = field.getRef();
				}
				String type = StringUtils.isBlank(field.getType()) ? "varchar" : ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType());
				if (field.getObject() != null) {
					type = "varchar";
				}
			    colDefList.add("`" + code + "` " + type);
			}
			
			correctSQL += "\n" +
				"CREATE FUNCTION groovyFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatFunctionUdf';\r\n" +
				"CREATE FUNCTION dfaasFunc AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDfaasFunctionUdf';\r\n" +
				"create table correct_sink_all (\n" + 
				"    " + StringUtils.join(colDefList, ",") + ",\n" + 
				"    __trace_id__ varchar," +
				"    primary key(" + pkColumn + ")\n" + 
				") with (\n" + 
				"    type = 'QANAT_ADB30',\n" + 
				"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
				"    dbName='" + dbName + "',\n" + 
				"    tableName='" + tableName + "',\n" + 
				"    replaceMode = 'replace',\n" + 
			    "    writeMode = 'single',\n" +
				"    streamType = 'kafka',\n" + 
				"    eventTopic = '" + correctTopicName + "',\n" + 
				"    eventServer = '" + kafkaJson.getString("dbName") + "'\n" + 
				");\n" + 
				"\n" + 
				"create view v_correct_all AS\n" + 
				"select \n" + 
				"	" + StringUtils.join(jsonParseColumnsAll, ",") + ",\n" + 
				"	__traceId__\n" +
				"from v_check_result as a\n" + 
				",LATERAL TABLE (queryDim('" + srcDsMetaJson.getString("dbName") + "', '" + "select * from (" + viewModelSqlBuilder.getSelectSql(tenantId, dataModel, null).replace("'", "''") + ") as t where " + pkColumn + "=?" + "', a.key)) as b(x)\n" + 
				"where split_index (a.msg, '|', 3)<>'OK' and split_index (a.msg, '|', 1) is not null and split_index (a.msg, '|', 1) <> ''\n" + 
				"and a.eventType='CREATE';" + 
				"\n" + 
				"insert into correct_sink_all\n" + 
				"select * from v_correct_all;\n" + 
				"\n" + 
				"insert\n" + 
				"  into full_link_sink\n" + 
				"select\n" + 
				"  __traceId__ as trace_id,\n" + 
				"  cast(" + dwdPk + " as bigint) as pk,\n" +
				"  CAST(" + pkColumn + " AS varchar) as key,\n" + 
				"  NOW() as ts,\n" + 
				"  '" + correctTopicName + "' as db,\n" + 
				"  '' as msg,\n" + 
				"  CURRENT_TIMESTAMP as gmt_create\n" + 
				"from v_correct_all;";
		}
		return correctSQL;
	}
    
    private String getCheckComputeSql(String tenantId, String appName, JSONObject srcDsMetaJson, String dbName, String tableName, JSONObject kafkaJson,  DataObject object, String correctTopicName, String checkResultTopicName, ViewModel dataModel) {
        List<String> colNameBList = new ArrayList<>();
        List<String> colNameCList = new ArrayList<>();
        List<String> colNameWithAliasBList = new ArrayList<>();
        List<String> colNameWithAliasCList = new ArrayList<>();
        String pkFieldC = null;
        for (ViewModel.Field field : object.getFields()) {
        	if (field.getObject() != null) {
        		continue;
        	}
        	if (field.getRef() != null && field.isCompute()) {
        		continue;
        	}
        	if (field.isPk()) {
        		pkFieldC = field.getCode();
        	}
        	colNameBList.add(field.getCode());
        	colNameCList.add(field.getCode());
            colNameWithAliasBList.add("JSON_VALUE(b.x, '$." + field.getCode() + "')");
            colNameWithAliasCList.add("JSON_VALUE(c.x, '$." + field.getCode() + "')");
        }
        String dwdPk = dataModel.getObject().getFields().stream().filter(e -> e.isPk()).map(e -> e.getCode()).collect(Collectors.toList()).get(0);
        if (!colNameCList.contains(dwdPk)) {
        	colNameCList.add(dwdPk);
        }
		if (object instanceof RelatedDataObject) {
	        for (ViewModel.Relation rel : ((RelatedDataObject)object).getRelations()) {
	        	if (!rel.getRelatedField().startsWith("exp#")) {
	        		pkFieldC = rel.getRelatedField().split("\\.")[1];
		        	break;
	        	}
	        }
		}
    	String sql = String.format(BLINK_CHECK_COMPUTE_FOR_MAIN_OBJECT_SQL
            , StringUtils.join(colNameBList, ",")
            , StringUtils.join(colNameWithAliasBList, ",")
            , StringUtils.join(colNameWithAliasCList, ",")
            , dwdPk
            , srcDsMetaJson.getString("dbName")
            , viewModelSqlBuilder.getSubQueryFromObject(tenantId, object).replaceAll("'", "''")
            , dbName
            , StringUtils.join(colNameCList, ",")
            , tableName
            , pkFieldC
            , tableName
            , tableName
            , fullLinkProcessor.getFullLinkSinkWithClause(tenantId, appName)
            , checkResultTopicName
        	);
    	return sql;
    }
    
    private String getObjectSourceSql(String topic, String consumerId, String dbName) {
    	return "create table drc_source (\n" + 
                "    msg varchar,\n" + 
                "    __traceId__ varchar header\n" +
                ") with (\n" + 
                "  type = 'custom',\n" + 
                "  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
                "  topic = '" + topic + "',\n" + 
                "  `group.id` = '" + consumerId + "',\n" + 
                "  `dbName` = '" + dbName + "',\n" + 
                "  startupMode = 'TIMESTAMP',\n" + 
                "  fieldDelimiter = '`'\n" + 
                ");\r\n";
    }

    public String processCorrectJob(String tenantId, String appName, String dbName, String tableName, String operateEmpid, Long versionId, ViewModel dataModel, Map<String, Field> checkTopicNames, JSONObject kafkaJson, Long datatubeInstId) {
    	String jobName = "correct_" + getAppIdByName(tenantId, appName) + "_" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "_v" + versionId;
    	String eventTopicName = "correct-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName);
    	boolean flag = kafkaManagementService.createTopic(tenantId, appName, eventTopicName);
		if (!flag) {
			log.error("topic:{} create is failed", eventTopicName);
		}

    	String pkField = viewModelSqlBuilder.getPkField(dataModel.getObject()).getCode();
		List<String> colDefList = YamlUtil.getModelColNameTypes(dataModel);

		List<ViewModel.Field> columnList = YamlUtil.getColumnsByModel(dataModel);
		List<String> jsonParseColumns = new ArrayList<>();
		for (ViewModel.Field field : columnList) {
        	if ("varchar".equalsIgnoreCase(field.getType()) || field.getObject() != null) {
        		jsonParseColumns.add("JSON_VALUE (b.x, '$." + field.getCode() + "') as `" + field.getCode() + "`");
        	} else {
        		jsonParseColumns.add("CAST(JSON_VALUE (b.x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()) + ") as `" + field.getCode() + "`");
        	}
        }
    	
    	String sinkSql = 
    					"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" + 
    				    "\n" +
    					"create table adb_sink (\n" + 
    					"    " + StringUtils.join(colDefList, ",") + ",\n" + 
    					"    primary key(" + pkField + ")\n" + 
    					") with (\n" + 
    					"    type = 'QANAT_ADB30',\n" + 
    					"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
    					"    dbName='" + dbName + "',\n" + 
    					"    tableName='" + tableName + "',\n" + 
    					"    replaceMode = 'replace',\n" + 
    					"    streamType = 'kafka',\n" + 
    					"    eventTopic = '" + eventTopicName + "',\n" + 
    					"    eventServer = '" + kafkaJson.getString("dbName") + "'\n" + 
    					");\n" + 
    	    			"\n" + 
    	    			"CREATE TABLE full_link_sink (\n" + 
    	    			"  trace_id varchar,\n" + 
    	    			"  key varchar,\n" + 
    	    			"  ts bigint,\n" + 
    	    			"  db varchar,\n" + 
    	    			"  gmt_create timestamp\n" + 
    	    			") WITH (\n" + 
    	    			"  " + fullLinkProcessor.getFullLinkSinkWithClause(tenantId, appName) + "\n" +  
    	    			");\n" + 
    	    			"\n";
    	
    	StringBuffer sqlBuffer = new StringBuffer();
    	sqlBuffer.append(sinkSql);
        int i = 0;
        for (String topic : checkTopicNames.keySet()) {
	    	String consumerId = "GID-" + topic + "-correct-" + versionId;
	    	flag = kafkaManagementService.createConsumerGroup(tenantId, appName, consumerId);
	    	if (!flag) {
	    		log.error("creat consumer[{}] failed", consumerId);
	    	}
	        String sql = String.format(BLINK_MDP_CORRECT_SQL
	        	, i
	            , topic
	            , consumerId
	            , kafkaJson.getString("dbName")
	            , i
	            , StringUtils.join(jsonParseColumns, ",")
	            , i
	            , dbName
	            , "select * from (" + viewModelSqlBuilder.getSelectSql(tenantId, dataModel, null).replace("'", "''") + ") as t where " + checkTopicNames.get(topic).getCode() + "=?"
	            , i
	            , topic
	            , i
	            );
	        i++;
	        sqlBuffer.append(sql);
        }
        
        blinkService.buildBlinkJob(tenantId, appName, jobName, sqlBuffer.toString(), "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_KAFKA010), false);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sqlBuffer.toString());
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        return jobName;
    }

	public boolean processAggrIncrSyncJob(String tenantId, String appName, String jobName, String dbName, String tableName, String arrayFieldName, RelatedDataObject object, String operateEmpid, Long versionId, JSONObject kafkaJson, Long datatubeInstId) {
		JSONObject dsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, object.getRef());
		if (dsMetaJson.getJSONObject("incrConf") == null) {
			log.info("no incr sync conf");
			return false;
		}
		String eventTopicName = "stream-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-" + object.getCode();
		String topicName = dsMetaJson.getJSONObject("incrConf").getString("topicName");
		String consumerId = "GID-" + getAppIdByName(tenantId, appName) + "-" + dsInfoService.getDsIdByTableName(tenantId, tableName, dbName) + "-incr_sync-" + object.getCode() + "-" + versionId;
		boolean res = kafkaManagementService.createConsumerGroup(tenantId, appName, consumerId);
		if (!res) {
			log.error("consumer:{} create is failed", consumerId);
		}
    	res = kafkaManagementService.createTopic(tenantId, appName, eventTopicName);
		if (!res) {
			log.error("topic:{} create is failed", eventTopicName);
		}

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> columnDefines = new ArrayList<>();
        List<String> fkColumns = new ArrayList<>();
        List<String> fkColumns1 = new ArrayList<>();
        List<ViewModel.Field> fkColumns2 = new ArrayList<>();
        String arrayColumn = null;
        for (ViewModel.Relation rel : object.getRelations()) {
        	if (!rel.getRelatedField().startsWith("exp#")) {
        		fkColumns.add(rel.getField());
        		fkColumns1.add("'" + rel.getField() + "'");
        	}
        	break;
        }
        for (ViewModel.Field field : object.getFields()) {
        	if (fkColumns.contains(field.getCode())) {
        		columnDefines.add("`" + field.getCode() + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
        		fkColumns2.add(field);
        	}
        }
        for (ViewModel.Field field : object.getFields()) {
        	if (!object.getRelations().stream().map(e -> e.getField()).collect(Collectors.toList()).contains(field.getCode())) {
        		arrayColumn = field.getCode();
        	}
        }
		columnDefines.add("`" + arrayFieldName + "` varchar");
		List<String> fkColSelects = new ArrayList<>();
		List<String> fkCols = new ArrayList<>();
		List<String> fkWithCast= new ArrayList<>();
		for (ViewModel.Field fkColumn : fkColumns2) {
			fkCols.add("JSON_VALUE(b.x, '$." + fkColumn.getCode() + "') AS " + fkColumn.getCode());
			fkColSelects.add("MAX(CASE WHEN tag_unique_code = ''" + fkColumn.getRef() + "'' THEN tag_value ELSE NULL END) AS " + fkColumn.getCode());
			if ("varchar".equalsIgnoreCase(fkColumn.getType())) {
				fkWithCast.add(fkColumn.getCode());
			} else {
				fkWithCast.add("CAST(" + fkColumn.getCode() + " AS " + fkColumn.getType() + ") AS " + fkColumn.getCode());
			}
		}
        String sql = String.format(BLINK_SYNC_ARRAY_OBJECT_MDP_SQL
                , operateEmpid
                , sdf.format(new Date())
                , "sync for " + tableName + " from " + object.getCode()
                , codegenVersion
                , topicName
                , consumerId
                , kafkaJson.getString("dbName")
                , StringUtils.join(columnDefines, ",")
                , StringUtils.join(fkColumns, ",")
                , dbName
                , tableName
                , eventTopicName
                , kafkaJson.getString("dbName")
                , arrayColumn
                , object.getRef()
                , StringUtils.join(fkCols, ",")
                , dbName
                , StringUtils.join(fkColSelects, ",")
                , object.getRef()
                , StringUtils.join(fkWithCast, ",")
                , arrayFieldName
                , arrayFieldName
                , dbName
                , viewModelSqlBuilder.getSqlFromAggrField(tenantId, arrayFieldName, object, fkColumns, null).replace("'", "''")
                , StringUtils.join(fkColumns, ",")
            );
        
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDTF), 
        		false);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        return true;
    }
	
	private Long getAppIdByName(String tenantId, String appName) {
		AppInfoExample example = new AppInfoExample();
		example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
		List<AppInfo> apps = appInfoMapper.selectByExample(example);
		return apps.get(0).getId();
	}
}