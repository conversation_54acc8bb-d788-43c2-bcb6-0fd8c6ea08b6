package com.aliyun.wormhole.qanat.process;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.odps.Event;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.BlinkRestartNode;
import com.aliyun.wormhole.qanat.api.dag.BlinkStreamNode;
import com.aliyun.wormhole.qanat.api.dag.Dag;
import com.aliyun.wormhole.qanat.api.dag.Node;
import com.aliyun.wormhole.qanat.api.dag.NodeAction;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.RedisService;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsRelation;
import com.aliyun.wormhole.qanat.dal.domain.DsRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.DsTaskRelation;
import com.aliyun.wormhole.qanat.dal.domain.DsTaskRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfoWithBLOBs;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsFieldInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsTaskRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInfoMapper;
import com.aliyun.wormhole.qanat.service.dag.DagService;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.QanatTddlDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.service.datasource.TddlConnectionParam;

import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Slf4j
@Component
public class DrcDispatchRestartProcessor extends JavaProcessor {

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private TaskInfoMapper taskInfoMapper;
    
    @Resource
    private DsTaskRelationMapper dsTaskRelationMapper;
    
    @Resource
    private DsRelationMapper dsRelationMapper;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private DatasourceMapper dsInfoMapper;
    
    @Resource
    private DsFieldInfoMapper dsFieldInfoMapper;
    
    @Resource
    private DagService dagService;
    
    @Resource 
    private BlinkService blinkService;
    
    @Resource
    private RedisService redisService;
	
	@Override
    public ProcessResult process(JobContext context) {
        log.info("start DrcDispatchRestartProcessor, params={}", context.getJobParameters());
	    TaskInfoExample example = new TaskInfoExample();
	    example.createCriteria().andIsDeletedEqualTo(0L).andNameLike("DAG_drc_%");
	    List<TaskInfoWithBLOBs> taskInfos = taskInfoMapper.selectByExampleWithBLOBs(example);
	    if (CollectionUtils.isEmpty(taskInfos)) {
	    	return new ProcessResult(false, "no records found");
	    }

        JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
        Integer intervalMins = paramsJson.getInteger("intervalMins");
        intervalMins = intervalMins == null ? 10 : intervalMins;
		Integer errorLevelMultiple = paramsJson.getInteger("errorLevelMultiple");
		errorLevelMultiple = errorLevelMultiple == null ? 3 : errorLevelMultiple;
	    
	    for (TaskInfoWithBLOBs taskInfo : taskInfos) {
	    	Dag dag = dagService.getDagByJson(taskInfo.getDag());
	    	for (Node node : dag.getNodeList()) {
	    		try {
		    		if ("com.aliyun.wormhole.qanat.job.QanatBlinkJobProcessor".equalsIgnoreCase(node.getAction()) && node.getNodeAction().equals(NodeAction.STREAM)) {
		    			String jobName = ((BlinkStreamNode)node).getJobName();
		    			
		    			log.info("start to process:{}", jobName);
	            		
	            		//任务状态失败立即重启
	            		if (checkJobRunning(taskInfo.getTenantId(), taskInfo.getAppName(), jobName)) {
	        				Calendar cal = Calendar.getInstance();
	            			cal.add(Calendar.MINUTE, -1*intervalMins - 10);
	            			blinkService.startJob(taskInfo.getTenantId(), taskInfo.getAppName(), jobName, cal.getTime());
	                		log.info("datatube_drc_monitor|job_not_run_recover|{}|任务从未运行状态下重启恢复", jobName);
	            			break;//跳过后面所有check
	            		}
	            		
	            		//任务近5mis写出量
						boolean jobSinkOutCheckFailed = checkJobSinkOut(taskInfo.getTenantId(), taskInfo.getAppName(), jobName, intervalMins, "job_sink_tps_0_low", taskInfo);//warn
	            		if (jobSinkOutCheckFailed) {
	            			jobSinkOutCheckFailed = checkJobSinkOut(taskInfo.getTenantId(), taskInfo.getAppName(), jobName, intervalMins*errorLevelMultiple, "job_sink_tps_0_high", taskInfo);//error
	            		}
	            		
	            		boolean jobStartTimeBeforeToday = checkJobStartTimeBeforeToday(taskInfo.getTenantId(), taskInfo.getAppName(), jobName, jobSinkOutCheckFailed);
	            		//如果任务启动位点早于今天并且输出流量为0则重启
	            		if (jobSinkOutCheckFailed && jobStartTimeBeforeToday) {
	                		log.error("datatube_drc_monitor|job_start_before_today_and_sink_tps_0|{}|任务启动位点早于今天并输出持续为0", jobName);
	        				Calendar cal = Calendar.getInstance();
	            			cal.add(Calendar.MINUTE, -1*intervalMins - 10);
	            			blinkService.restartJob(taskInfo.getTenantId(), taskInfo.getAppName(), jobName, cal.getTime(), false);
	                		log.info("datatube_drc_monitor|job_start_before_today_and_sink_tps_0_recover|{}|任务从异常并输出为0状态下重启恢复", jobName);
	            			break;//跳过后面所有check
	            		}
	            		
	            		//任务Failover
	            		boolean jobFailoverCheckFailed = checkJobException(taskInfo.getTenantId(), taskInfo.getAppName(), jobName);
	            		
	            		//如果任务failover并且输出流量为0则重启
	            		if (jobSinkOutCheckFailed && jobFailoverCheckFailed) {
	                		log.error("datatube_drc_monitor|job_exception_and_sink_tps_0|{}|任务存在异常并输出持续为0", jobName);
	        				Calendar cal = Calendar.getInstance();
	            			cal.add(Calendar.MINUTE, -1*intervalMins - 10);
	            			blinkService.restartJob(taskInfo.getTenantId(), taskInfo.getAppName(), jobName, cal.getTime(), false);
	                		log.info("datatube_drc_monitor|job_failover_recover|{}|任务从异常并输出为0状态下重启恢复", jobName);
	            		}
	
	            		log.info("finish to process:{}", jobName);
		                break;
		        	}
		    	} catch (Exception e) {
		    		log.error("DAG:{} check failed, error={}", dag.getId(), e.getMessage(), e);
		    	}
		    }
	    }
	    
	    example = new TaskInfoExample();
	    example.createCriteria().andIsDeletedEqualTo(0L).andNameEqualTo("DAG_restart_drc_dispatch_old_daily_v1");
	    taskInfos = taskInfoMapper.selectByExampleWithBLOBs(example);
	    if (CollectionUtils.isEmpty(taskInfos)) {
	    	return new ProcessResult(false, "no records found");
	    }
	    TaskInfoWithBLOBs taskInfo = taskInfos.get(0);
    	Dag dag = dagService.getDagByJson(taskInfos.get(0).getDag());
    	for (Node node : dag.getNodeList()) {
    		if ("com.aliyun.wormhole.qanat.job.QanatRestartBlinkJobProcessor".equalsIgnoreCase(node.getAction()) && node.getNodeAction().equals(NodeAction.STREAM)) {
    			String jobName = ((BlinkRestartNode)node).getJobName();
    			
    			log.info("start to process:{}", jobName);
        		
        		//任务状态失败立即重启
        		if (checkJobRunning(taskInfo.getTenantId(), taskInfo.getAppName(), jobName)) {
    				Calendar cal = Calendar.getInstance();
        			cal.add(Calendar.MINUTE, -1*intervalMins - 10);
        			blinkService.startJob(taskInfo.getTenantId(), taskInfo.getAppName(), jobName, cal.getTime());
            		log.info("datatube_drc_monitor|job_not_run_recover|{}|job has recovered from not_run status", jobName);
        			break;//跳过后面所有check
        		}
        		
        		//任务近5mis写出量
				boolean jobSinkOutCheckFailed = checkJobSinkOut(taskInfo.getTenantId(), taskInfo.getAppName(), jobName, intervalMins, "job_sink_tps_0_low", null);//warn
        		if (jobSinkOutCheckFailed) {
        			jobSinkOutCheckFailed = checkJobSinkOut(taskInfo.getTenantId(), taskInfo.getAppName(), jobName, intervalMins*errorLevelMultiple, "job_sink_tps_0_high", null);//error
        		}     		
        		//任务Failover
        		boolean jobFailoverCheckFailed = checkJobException(taskInfo.getTenantId(), taskInfo.getAppName(), jobName);
        		
//        		//如果任务failover并且输出流量为0则重启
        		if (jobSinkOutCheckFailed && jobFailoverCheckFailed) {
            		log.error("datatube_drc_monitor|job_exception_and_sink_tps_0|{}|任务存在异常并输出持续为0", jobName);
    				Calendar cal = Calendar.getInstance();
        			cal.add(Calendar.MINUTE, -1*intervalMins - 10);
        			blinkService.restartJob(taskInfo.getTenantId(), taskInfo.getAppName(), jobName, cal.getTime(), false);
            		log.info("datatube_drc_monitor|job_failover_recover|{}|任务从异常并输出为0状态下重启恢复", jobName);
        		}

        		log.info("finish to process:{}", jobName);
                break;
        	}
	    }
	    
        return new ProcessResult(true);
	}

	private boolean checkJobStartTimeBeforeToday(String tenantId, String appName, String jobName, boolean jobSinkOutCheckFailed) throws ParseException {
		boolean jobStartTimeBeforeToday = false;
		String instConfJsonStr = blinkService.getInstanceConfig(tenantId, appName, jobName);
		if (StringUtils.isNotBlank(instConfJsonStr)) {
			JSONObject instConfJson = JSON.parseObject(instConfJsonStr);
			if (instConfJson != null
					&& instConfJson.getJSONObject("execution-config") != null
					&& instConfJson.getJSONObject("execution-config").getJSONObject("user-config") != null
					&& StringUtils.isNotBlank(instConfJson.getJSONObject("execution-config").getJSONObject("user-config").getString("blink.job.source.startTime"))) {
				Long srcStartTime = Long.valueOf(instConfJson.getJSONObject("execution-config").getJSONObject("user-config").getString("blink.job.source.startTime.mills"));
				String instDtlJsonStr = blinkService.getInstanceDetail(tenantId, appName, jobName);
				if (StringUtils.isNotBlank(instDtlJsonStr)) {
					JSONObject instDtlJson = JSON.parseObject(instDtlJsonStr);
					if (instDtlJson != null && instDtlJson.getLong("start-time") != null) {
						Long jobStartTime = instDtlJson.getLong("start-time");

				        log.info("job:{} srcStartTime={}, jobStartTime={}", jobName, srcStartTime, jobStartTime);
						
						//获取当天0点时间戳，做兜底判断
				        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				        String dateStr = sdf.format(new Date());
				        String todayStr = dateStr + " 00:00:00";
				        sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				        Date todayDatetime = sdf.parse(todayStr);
				        
				        //如果任务启动位点早于当天0点，并且任务启动时间大于当天0点
				        if (srcStartTime < todayDatetime.getTime() && jobStartTime > todayDatetime.getTime()) {
				        	jobStartTimeBeforeToday = true;
				        } else
				        //如果任务启动位点与任务启动时间早于当天0点,且drc消费检查失败的情况下
				        if (jobSinkOutCheckFailed && srcStartTime < todayDatetime.getTime() && jobStartTime < todayDatetime.getTime()) {
				        	jobStartTimeBeforeToday = true;
				        }
					}
				}
			}
		}
		return jobStartTimeBeforeToday;
	}

	private boolean checkJobException(String tenantId, String appName, String jobName) {
		boolean jobFailoverCheckFailed = false;
		String jobExceptions = blinkService.getJobExceptions(tenantId, appName, jobName);
		log.info("getJobExceptions({})={}", jobName, jobExceptions);
		if (StringUtils.isNotBlank(jobExceptions)) {
			JSONObject jobExceptionsJson = JSON.parseObject(jobExceptions);
			if (jobExceptionsJson != null 
					&& StringUtils.isNotBlank(jobExceptionsJson.getString("root-exception"))) {
//        		log.error("datatube_drc_monitor|job_failover|{}|job has exceptions", jobName);
    			jobFailoverCheckFailed = true;
			}
		}
		return jobFailoverCheckFailed;
	}

	private boolean checkJobRunning(String tenantId, String appName, String jobName) {
		String jobRunSummary = null;
		int retries = 0;
		while (retries < 3) {
			jobRunSummary = blinkService.getJobRunSummary(tenantId, appName, jobName);
			log.info("getJobRunSummary({})={}", jobName, jobRunSummary);
			if (StringUtils.isBlank(jobRunSummary)) {
				retries++;
				try {
					Thread.sleep(500*retries);
				} catch (InterruptedException e) {
				}
			} else {
				break;
			}
		}
		
		boolean jobStateCheckFailed = false;
		if (StringUtils.isBlank(jobRunSummary)) {
			if (!blinkService.isJobCommitted(tenantId, appName, jobName)) {
				log.error("job:{} is not committed", jobName);
				log.error("datatube_drc_monitor|job_not_run|{}|任务未运行", jobName);
				jobStateCheckFailed = true;
			}
		} else {
			JSONObject jobRunSummaryJson = JSON.parseObject(jobRunSummary);
			if (jobRunSummaryJson == null 
					|| jobRunSummaryJson.getJSONObject("runSummary") == null 
					|| StringUtils.isBlank(jobRunSummaryJson.getJSONObject("runSummary").getString("actualState"))) {
				log.error("datatube_drc_monitor|job_not_run|{}|任务未运行", jobName);
				jobStateCheckFailed = true;
			} else {
				if (!"RUNNING".equalsIgnoreCase(jobRunSummaryJson.getJSONObject("runSummary").getString("actualState"))) {
		    		log.error("datatube_drc_monitor|job_not_run|{}|任务未运行", jobName);
		    		jobStateCheckFailed = true;
				}
			}
		}
		return jobStateCheckFailed;
	}

	private Boolean checkJobSinkOut(String tenantId, String appName, String jobName, int intervalMins, String errorType, TaskInfoWithBLOBs taskInfo) {
		boolean jobSinkOutCheckFailed = false;
		JSONObject metricJson = new JSONObject();
		long offset = 60*1000;
		long tsEnd = System.currentTimeMillis() - offset;
		long tsStart = tsEnd - intervalMins*60*1000;
		metricJson.put("start", tsStart);
		metricJson.put("end", tsEnd);
		JSONArray metricQueriesJson = new JSONArray();
		metricJson.put("queries", metricQueriesJson);
		JSONObject queryJson = new JSONObject();
		queryJson.put("metric", "blink.alywormhole." + jobName + ".sink.outTps.rate");
		queryJson.put("aggregator", "max");
		metricQueriesJson.add(queryJson);
		String instanceMetric = blinkService.getInstanceMetric(tenantId, appName, jobName, metricJson.toJSONString());
		log.info("getInstanceMetric({},{})={}", jobName, metricJson.toJSONString(), instanceMetric);
		if (StringUtils.isNotBlank(instanceMetric)) {
			JSONArray instanceMetricJsonArray = JSON.parseArray(instanceMetric);
			if (instanceMetricJsonArray != null && instanceMetricJsonArray.size() > 0
					&& instanceMetricJsonArray.getJSONObject(0).getJSONObject("dps") != null
					&& CollectionUtils.isNotEmpty(instanceMetricJsonArray.getJSONObject(0).getJSONObject("dps").values())) {
				BigDecimal bd = new BigDecimal("0.0");
				for (Object value : instanceMetricJsonArray.getJSONObject(0).getJSONObject("dps").values()) {
					bd = bd.add(new BigDecimal(value.toString()));
				}
				if (bd.compareTo(new BigDecimal("0.0")) == 0) {
					jobSinkOutCheckFailed = true;
				}
			}
		}
		//尝试验证源表是否就没有更新
		if (jobSinkOutCheckFailed && taskInfo != null) {
			DsTaskRelationExample dtrExample = new DsTaskRelationExample();
			dtrExample.createCriteria().andTaskIdEqualTo(taskInfo.getId()).andIsDeletedEqualTo(0L).andRelationTypeEqualTo("drc").andTenantIdEqualTo(taskInfo.getTenantId());
			List<DsTaskRelation> dsTaskRels = dsTaskRelationMapper.selectByExample(dtrExample);
			if (CollectionUtils.isNotEmpty(dsTaskRels)) {
				DatasourceExample dsInfoExample = new DatasourceExample();
				dsInfoExample.createCriteria().andDsNameEqualTo(dsTaskRels.get(0).getDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(taskInfo.getTenantId());
				List<Datasource> dsInfos = dsInfoMapper.selectByExample(dsInfoExample);
				if (CollectionUtils.isNotEmpty(dsInfos)) {
                	Map<String, Long> dsMetric = this.getDsMetrics(taskInfo.getTenantId(), dsInfos.get(0));
                	if (dsMetric != null) {
	                	Long origionLastestUpdateTs = dsMetric.get("latest_update");
	                	Long currentTs = System.currentTimeMillis();
	                	Long stdGap =  intervalMins*60*1000L;
	            		log.info("jobName:{} currentTs:{},origion_lastestUpdateTs:{}, gap={}, std:{}, isMoreThanStd:{}", jobName, currentTs, origionLastestUpdateTs, currentTs - origionLastestUpdateTs, stdGap, currentTs - origionLastestUpdateTs > stdGap);
	                	if (currentTs - origionLastestUpdateTs > stdGap) {
		            		log.info("Drc任务:{} 在{}分钟无更新，通过探查源库表最后更新时间也在{}分钟前，不报警", jobName, intervalMins, intervalMins);
	                		jobSinkOutCheckFailed = false;
	                	} else {
		            		log.info("Drc任务:{} 在{}分钟无更新，而源库表最后更新时间在{}分钟之内，进一步探查ods表的更新情况", jobName, intervalMins, intervalMins);
	                		//源库已有新数据的情况下，尝试验证ODS表是否也已经更新了，如果已更新体现在ods和源库之间的时间戳相聚很近，说用drc消费也正常，就不进行报警
	            			DsRelationExample drExample = new DsRelationExample();
	            			drExample.createCriteria().andSrcDsNameEqualTo(dsInfos.get(0).getDsName()).andIsDeletedEqualTo(0L).andRelationTypeEqualTo("ods").andTenantIdEqualTo(taskInfo.getTenantId());
	            			List<DsRelation> dsRels = dsRelationMapper.selectByExample(drExample);
	            			if (CollectionUtils.isNotEmpty(dsRels)) {
	            				dsInfoExample = new DatasourceExample();
	            				dsInfoExample.createCriteria().andDsNameEqualTo(dsRels.get(0).getDstDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(taskInfo.getTenantId());
	            				dsInfos = dsInfoMapper.selectByExample(dsInfoExample);
	            				if (CollectionUtils.isNotEmpty(dsInfos)) {
	                            	dsMetric = this.getDsMetrics(taskInfo.getTenantId(), dsInfos.get(0));
	                            	if (dsMetric != null) {
	            	                	Long odsLastestUpdateTs = dsMetric.get("latest_update");
	            	                	Long origionOdsMins=  1L;
	            	                	Long origionOdsStdGap =  origionOdsMins*30*1000L;
	            	            		log.info("jobName:{} currentTs:{},origion_lastestUpdateTs:{}, ods_lastestUpdateTs:{}, origionOdsGap={}, origionOdsStd:{}, isMoreThanStd:{}", jobName, currentTs, origionLastestUpdateTs, odsLastestUpdateTs, Math.abs(origionLastestUpdateTs - odsLastestUpdateTs), origionOdsStdGap, Math.abs(origionLastestUpdateTs - odsLastestUpdateTs) < origionOdsStdGap);
	            	                	if (Math.abs(origionLastestUpdateTs - odsLastestUpdateTs) < origionOdsStdGap) {
	            		            		log.info("Drc任务:{} 在{}分钟无更新，而源库表最后更新时间在{}分钟之内，通过探查ods表与源库表最后更新时间差也在{}分钟之内，不报警", jobName, intervalMins, intervalMins, origionOdsMins);
	            	                		jobSinkOutCheckFailed = false;
	            	                	}
	                            	}
	            				}
	            			}
	                	}
                	} else {
                		log.info("Drc任务:{} table:{} has no gmt_modified", jobName, dsInfos.get(0).getTableName());
                		
                		if (!checkBydDrcConsumer(tenantId, appName, jobName, intervalMins, dsInfos.get(0).getDsName())) {
                			jobSinkOutCheckFailed = false;
                		}
                	}
				}
			}
			if (jobSinkOutCheckFailed) {
				log.error("datatube_drc_monitor|{}|{}|任务在过去 {} 分钟内输出记录数为0", errorType, jobName, intervalMins);
			}
		}
		return jobSinkOutCheckFailed;
	}

	private boolean checkBydDrcConsumer(String tenantId, String appName, String jobName, int intervalMins, String dsName) {
		boolean isFailed = false;
		String key = "DRC_" + jobName + "_" + 0;
		String val = redisService.get(tenantId, appName, key);
		log.info("Drc任务:{} cacheKey:{} cachedTs:{}", jobName, key, val);
		Long currentTs = System.currentTimeMillis();
		Long stdGap =  intervalMins*60*1000L;
		if (StringUtils.isNotBlank(val)) {
			Long cachedTs = Long.valueOf(val)*1000;
			log.info("jobName:{} currentTs:{},cachedTs:{}, gap={}, std:{}, isMoreThanStd:{}", jobName, currentTs, cachedTs, currentTs - cachedTs, stdGap, currentTs - cachedTs > stdGap);
			if (currentTs - cachedTs > stdGap) {
				log.info("Drc任务:{} 在{}分钟无更新，通过探查DRC[缓存]最新消费位点时间也在{}分钟前，不报警", jobName, intervalMins, intervalMins);
				isFailed = checkByDrcCheckpoint(tenantId, jobName, intervalMins, dsName, currentTs, stdGap);
			} else {
				isFailed = true;
			}
		} else {
			isFailed = checkByDrcCheckpoint(tenantId, jobName, intervalMins, dsName, currentTs, stdGap);
		}
		return isFailed;
	}

	private Boolean checkByDrcCheckpoint(String tenantId, String jobName, int intervalMins, String dsName, Long currentTs, Long stdGap) {
		Boolean isFailed = false;
		String drcTaskId = getDrcTaskId(tenantId, dsName);
		if (StringUtils.isNotBlank(drcTaskId)) {
			long drcCheckpointEndMs = 0L;
		    JSONObject topicInfo = getDrcTopic("RM_aly_qanat", "alyqanat", drcTaskId, "http://140.205.137.192:8080");
		    if (topicInfo != null) {
		        Long checkPointEnd = topicInfo.getLong("checkpoint-end");
		        drcCheckpointEndMs = checkPointEnd*1000;

				log.info("jobName:{} currentTs:{},drcCheckpointEndMs:{}, gap={}, std:{}, isMoreThanStd:{}", jobName, currentTs, drcCheckpointEndMs, currentTs - drcCheckpointEndMs, stdGap, currentTs - drcCheckpointEndMs > stdGap);
				if (currentTs - drcCheckpointEndMs > stdGap) {
					log.info("Drc任务:{} 在{}分钟无更新，通过探查DRC消费最新位点时间也在{}分钟前，不报警", jobName, intervalMins, intervalMins);
				} else {
					isFailed = true;
				}
			}
		}
		return isFailed;
	}

	private String getDrcTaskId(String tenantId, String dsName) {
		String drcTaskId = null;
		DsRelationExample drExample = new DsRelationExample();
		drExample.createCriteria().andDstDsNameEqualTo(dsName).andIsDeletedEqualTo(0L).andRelationTypeEqualTo("incr").andTenantIdEqualTo(tenantId);
		List<DsRelation> dsRels = dsRelationMapper.selectByExample(drExample);
		if (CollectionUtils.isNotEmpty(dsRels)) {
			drExample = new DsRelationExample();
			drExample.createCriteria().andDstDsNameEqualTo(dsRels.get(0).getSrcDsName()).andIsDeletedEqualTo(0L).andRelationTypeEqualTo("drc").andTenantIdEqualTo(tenantId);
			dsRels = dsRelationMapper.selectByExample(drExample);
			if (CollectionUtils.isNotEmpty(dsRels)) {
				DatasourceExample dsInfoExample = new DatasourceExample();
				dsInfoExample.createCriteria().andDsNameEqualTo(dsRels.get(0).getSrcDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
				List<Datasource> dsInfos = dsInfoMapper.selectByExample(dsInfoExample);
				if (CollectionUtils.isNotEmpty(dsInfos)) {
					drcTaskId = dsInfos.get(0).getTableName();
				}
			}
		}
		return drcTaskId;
	}
	
	private JSONObject getDrcTopic(String username, String password, String taskId, String endpoint) {
        String url = endpoint + "/api/auth";
        String token = null;
        String data = String.format("taskParam={\"user\":\"%s\",\"password\":\"%s\"}", username, password);
        String resp = doHttpPost(url, data);
        if (StringUtils.isNotBlank(resp)) {
            JSONObject respJson = JSON.parseObject(resp);
            if (respJson != null && respJson.getBoolean("success") && respJson.getJSONObject("data") != null) {
                token = respJson.getJSONObject("data").getString("token");
                
                data = String.format("taskParam={\"taskId\":\"%s\",\"token\":\"%s\"}", taskId, token);
                url = endpoint + "/api/query";
                resp = doHttpPost(url, data);
                if (StringUtils.isNotBlank(resp)) {
                    respJson = JSON.parseObject(resp);
                    if (respJson != null && respJson.getBoolean("success") && respJson.getJSONObject("data") != null) {
                        JSONArray cfgArray =  respJson.getJSONObject("data").getJSONArray("cfg");
                        if (cfgArray != null) {
                            for (int i = 0; i < cfgArray.size(); i++) {
                                JSONObject cfg = cfgArray.getJSONObject(i);
                            	return cfg;
                            }
                        }
                    }
                }
            }
        }
        return null;
    }
	
	private String doHttpPost(String url, String data) {
        String resp = null;
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(mediaType, data))
                .build();
        OkHttpClient okHttpClient = new OkHttpClient();
        Response response = null;
        try {
        	log.info("req:post {} -d'{}'", url, data);
            response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                resp = response.body().string(); 
                log.info("resp:{}", resp); 
            }
        } catch (IOException e) {
        	log.error("http request failed", e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return resp;
    }
	
	private Map<String, Long> getDsMetrics(String tenantId, Datasource dsInfo) {
    	String gmtModifiedField = "gmt_modified";
    	String sql = "select max(" + gmtModifiedField + ") as latest_update from " + dsInfo.getTableName();
    	
    	DsFieldInfoExample dfiExample = new DsFieldInfoExample();
    	dfiExample.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsNameEqualTo(dsInfo.getDsName()).andFieldNameEqualTo(gmtModifiedField);
    	int cnt = dsFieldInfoMapper.countByExample(dfiExample);
    	if (cnt == 0) {
    		return null;
    	}

	    Connection connection = null;
	    try {
		    JSONObject dbMetaJson = getAdbDbMeta(dsInfo.getDbName());
		    String hint = "";
	        if (dbMetaJson.containsKey("appName")) {
	        	TddlConnectionParam param = new TddlConnectionParam();
	        	param.setAppName(dbMetaJson.getString("appName"))
	        		.setAccessKey("accessKey")
	        		.setSecretKey("secretKey");
	        	connection = QanatTddlDatasourceHandler.connectToTable(param);
	        	hint = "/*+TDDL({\"extra\":{\"ALLOW_FULL_TABLE_SCAN\":\"TRUE\"}})*//*+TDDL_GROUP({groupIndex:1})*//*+TDDL({'extra':{'SOCKET_TIMEOUT':'10000'}})*/";
	        } else {
		    	RdsConnectionParam param = new RdsConnectionParam();
			    param.setUrl(dbMetaJson.getString("jdbcUrl"))
			        .setUserName(dbMetaJson.getString("username"))
			        .setPassword(dbMetaJson.getString("password"));
		        connection = dsHandler.connectToTable(param);
	        }

	        return querySql(connection, dsInfo.getDbName(), hint + sql);
        	
	    } catch (Exception e) {
	        log.error("get table data monitor failed, error={}", e.getMessage(), e);
	        throw new QanatBizException(e.getMessage());
	    } finally {
            if (connection != null) {
                try {
                	connection.close();
                } catch (SQLException e) {
                }
                connection = null;
            }
	    }
    }

    private JSONObject getAdbDbMeta(String dbName) {
        DbInfoExample example = new DbInfoExample();
        example.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("no db found");
        }
        String dbMeta = dbs.get(0).getMeta();
        JSONObject dbMetaJson = JSON.parseObject(dbMeta);
        return dbMetaJson;
    }

    private Map<String, Long> querySql(Connection connection, String dbName, String sql) {
        Map<String, Long> data = new HashMap<>();
        log.info("before exec dbName={} sql={}", dbName, sql);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            Long startTs = System.currentTimeMillis();
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                data.put("latest_update", resultSet.getTimestamp("latest_update").getTime());
            }
            log.info("after exec sql data={} cost={}", JSON.toJSONString(data), System.currentTimeMillis() - startTs);
        } catch(Exception e) {
            log.error("querySql failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (Exception e) {
                }
                resultSet = null;
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (Exception e) {
                }
                statement = null;
            }
        }
        return data;
    }
    
    @Override
    public void kill(JobContext context) {
        
    }
    
    static Event buildEvent(String eventName, String tableName, String callbackUri, String comment) throws URISyntaxException {
        Event event = new Event();
        event.setName(eventName);   // 指定事件名称
        event.setComment(comment);    // 事件注释
        event.setType(Event.SourceType.TABLE);   // 指定事件类型，目前支持 TABLE
        Event.Config config = new Event.Config();
        config.setName("source");
        config.setValue(tableName);   // 指定事件源(即 表名). "*" 表示所有表.
        event.setConfig(config);
        event.setUri(new URI(callbackUri));   // 指定了一个回调地址
        return event;
    }
    
    public static void main(String[] args) throws ParseException {
    	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dateStr = sdf.format(new Date());
        String todayStr = dateStr + " 00:00:00";
        sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date todayDatetime = sdf.parse(todayStr);
        
        //如果任务启动位点早于当天0点，并且任务启动时间大于当天0点
        boolean result = false;
        long srcStartTime=1652772353241l, jobStartTime=1653753322687l;
        if (srcStartTime < todayDatetime.getTime() && jobStartTime > todayDatetime.getTime()) {
        	result = true;
        }
    	System.out.println(result);
    }
}