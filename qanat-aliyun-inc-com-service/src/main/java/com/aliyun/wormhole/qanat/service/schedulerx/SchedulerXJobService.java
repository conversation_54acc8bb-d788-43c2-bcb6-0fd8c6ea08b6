package com.aliyun.wormhole.qanat.service.schedulerx;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.common.domain.ContactInfo;
import com.alibaba.schedulerx.common.sdk.common.JavaJobConfig;
import com.alibaba.schedulerx.common.sdk.common.JobConfigInfo;
import com.alibaba.schedulerx.common.sdk.common.JobInstanceDetail;
import com.alibaba.schedulerx.common.sdk.common.JobMonitorInfo;
import com.alibaba.schedulerx.common.sdk.common.MonitorConfig;
import com.alibaba.schedulerx.common.sdk.common.SchedulerXResult;
import com.alibaba.schedulerx.common.sdk.common.TimeConfig;
import com.alibaba.schedulerx.common.sdk.request.CreateJavaJobRequest;
import com.alibaba.schedulerx.common.sdk.request.DeleteJobRequest;
import com.alibaba.schedulerx.common.sdk.request.ExecuteJobRequest;
import com.alibaba.schedulerx.common.sdk.request.GetJobInfoRequest;
import com.alibaba.schedulerx.common.sdk.request.GetJobInstanceListRequest;
import com.alibaba.schedulerx.common.sdk.request.GetJobInstanceRequest;
import com.alibaba.schedulerx.common.sdk.request.KillInstanceRequest;
import com.alibaba.schedulerx.common.sdk.request.UpdateJobRequest;
import com.alibaba.schedulerx.common.sdk.response.CreateJobResponse;
import com.alibaba.schedulerx.common.sdk.response.DeleteJobResponse;
import com.alibaba.schedulerx.common.sdk.response.ExecuteJobResponse;
import com.alibaba.schedulerx.common.sdk.response.GetJobInfoResponse;
import com.alibaba.schedulerx.common.sdk.response.GetJobInstanceListResponse;
import com.alibaba.schedulerx.common.sdk.response.GetJobInstanceResponse;
import com.alibaba.schedulerx.common.sdk.response.KillInstanceResponse;
import com.alibaba.schedulerx.common.sdk.response.UpdateJobResponse;
import com.alibaba.schedulerx.sdk.OpenApiClient;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.Dag;
import com.aliyun.wormhole.qanat.api.dag.Node;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelation;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.ResourceExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfoWithBLOBs;
import com.aliyun.wormhole.qanat.dal.mapper.AppResourceRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ResourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SchedulerXJobService {
    
    @Resource
    private TaskInfoMapper taskInfoMapper;
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;
	
	@Resource
	private AppResourceRelationMapper appResourceRelationMapper;
	
	@Resource
	private ResourceMapper resourceMapper;

    public DataResult<Long> createDagJob(String tenantId, String appName, TaskInfoWithBLOBs task, Dag dag) {
        DataResult<Long> result = new DataResult<>();
        result.setSuccess(true);
        result.setCode("200");
    	OpenApiClient client = getClientFromAppName(tenantId, appName);
        CreateJavaJobRequest createJobRequest = new CreateJavaJobRequest();
        
        Map<String, Object> jobParamsMap = new HashMap<>();
        jobParamsMap.put("taskId", task.getId());
        
        // 任务基础信息
        JavaJobConfig jobConfig = new JavaJobConfig("com.aliyun.wormhole.qanat.job.QanatDagJobProcessor");
        jobConfig.setName(dag.getId());
        jobConfig.setDescription(task.getTaskDesc());
        jobConfig.setParameters(JSON.toJSONString(jobParamsMap));
        jobConfig.setExecuteMode("parallel");
        jobConfig.setMaxConcurrency(1);
        jobConfig.setMaxAttempt(1);
        

        if (StringUtils.isNotBlank(dag.getTimeExpression())) {
            TimeConfig timeConfig = new TimeConfig(TimeConfig.CRORN_TYPE);
            timeConfig.setTimeExpression(dag.getTimeExpression());
            jobConfig.setTimeConfig(timeConfig);
        } else {
            TimeConfig timeConfig = new TimeConfig(TimeConfig.API_TYPE);
            jobConfig.setTimeConfig(timeConfig);
        }
        
        // 报警配置
        JobMonitorInfo jobMonitorConfig = new JobMonitorInfo();
        MonitorConfig monitorConfig = new MonitorConfig();
        monitorConfig.setFailEnable(true);
        monitorConfig.setTimeoutEnable(true);
        monitorConfig.setTimeout(3600);
        monitorConfig.setTimeoutKillEnable(false);
        monitorConfig.setSendChannel("ding");
        jobMonitorConfig.setMonitorConfig(monitorConfig);
        List<ContactInfo> contactInfos = new ArrayList<>();
        ContactInfo contactInfo = new ContactInfo();
        contactInfo.setEmpId(task.getCreateEmpid());
        contactInfos.add(contactInfo);
        jobMonitorConfig.setContactInfo(contactInfos);
        jobConfig.setJobMonitorInfo(jobMonitorConfig);
        createJobRequest.setJavaJobConfig(jobConfig);
        // 发送请求
        try {
            SchedulerXResult<CreateJobResponse> createJobResponseResult = client.getResponse(createJobRequest);
            // 请求解析
            if (createJobResponseResult.isSuccess()) {
                log.info("SchedulerX JobId:{}", createJobResponseResult.getData().getJobId());
                result.setData(createJobResponseResult.getData().getJobId());
            } else {
                log.error("createJobRequest error code: {} message: {}, requestId: {}",
                    createJobResponseResult.getCode(),
                    createJobResponseResult.getMessage(), createJobResponseResult.getRequestId());
                result.setSuccess(false);
                result.setCode(createJobResponseResult.getCode() + "");
                result.setMessage(createJobResponseResult.getMessage());
            }
        } catch (Exception e) {
            log.error("createJobRequest error.", e);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
        }
        return result;
    }

	private OpenApiClient getClientFromAppName(String tenantId, String appName) {
		AppResourceRelationExample example = new AppResourceRelationExample();
    	example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andRelationTypeEqualTo("schedulerx2");
    	List<AppResourceRelation> rels = appResourceRelationMapper.selectByExample(example);
    	if (CollectionUtils.isEmpty(rels)) {
    		throw new QanatBizException("no app resouces");
    	}
    	AppResourceRelation ref = rels.get(0);
    	ResourceExample example1 = new ResourceExample();
    	example1.createCriteria().andResourceNameEqualTo(ref.getResourceName()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L);
    	List<com.aliyun.wormhole.qanat.dal.domain.Resource> resources = resourceMapper.selectByExampleWithBLOBs(example1);
    	if (CollectionUtils.isEmpty(resources)) {
    		throw new QanatBizException("no app resouces");
    	}
    	com.aliyun.wormhole.qanat.dal.domain.Resource resource = resources.get(0);
    	JSONObject metaJson = JSON.parseObject(resource.getMeta());
        OpenApiClient client = new OpenApiClient(metaJson.getString("appKey"), metaJson.getString("groupId"), metaJson.getString("domainName"));
		return client;
	}

    public DataResult<Long> createNodeJob(String tenantId, String appName, TaskInfoWithBLOBs task, Node node) {
        DataResult<Long> result = new DataResult<>();
        result.setSuccess(true);
        result.setCode("200");
    	OpenApiClient client = getClientFromAppName(tenantId, appName);
        CreateJavaJobRequest createJobRequest = new CreateJavaJobRequest();

        Map<String, Object> jobParamsMap = new HashMap<>();
        jobParamsMap.put("taskId", task.getId());
        
        // 任务基础信息
        JavaJobConfig jobConfig = new JavaJobConfig(node.getAction());
        jobConfig.setName(node.getId());
        jobConfig.setDescription(node.getId());
        jobConfig.setParameters(JSON.toJSONString(jobParamsMap));
        jobConfig.setExecuteMode("standalone");
        jobConfig.setMaxConcurrency(1);
        jobConfig.setMaxAttempt(1);
        
        if (StringUtils.isNotBlank(node.getTimeExpression())) {
            TimeConfig timeConfig = new TimeConfig(TimeConfig.CRORN_TYPE);
            timeConfig.setTimeExpression(node.getTimeExpression());
            jobConfig.setTimeConfig(timeConfig);
        } else {
            TimeConfig timeConfig = new TimeConfig(TimeConfig.API_TYPE);
            jobConfig.setTimeConfig(timeConfig);
        }
        
        // 报警配置
        JobMonitorInfo jobMonitorConfig = new JobMonitorInfo();
        MonitorConfig monitorConfig = new MonitorConfig();
        monitorConfig.setFailEnable(false);
        monitorConfig.setTimeoutEnable(false);
        monitorConfig.setTimeout(3600);
        monitorConfig.setTimeoutKillEnable(false);
        monitorConfig.setSendChannel("ding");
        jobMonitorConfig.setMonitorConfig(monitorConfig);
        List<ContactInfo> contactInfos = new ArrayList<>();
        ContactInfo contactInfo = new ContactInfo();
        contactInfo.setEmpId(task.getCreateEmpid());
        contactInfos.add(contactInfo);
        jobMonitorConfig.setContactInfo(contactInfos);
        jobConfig.setJobMonitorInfo(jobMonitorConfig);
        createJobRequest.setJavaJobConfig(jobConfig);
        // 发送请求
        try {
            SchedulerXResult<CreateJobResponse> createJobResponseResult = client.getResponse(createJobRequest);
            // 请求解析
            if (createJobResponseResult.isSuccess()) {
                log.info("SchedulerX JobId:{}", createJobResponseResult.getData().getJobId());
                result.setData(createJobResponseResult.getData().getJobId());
            } else {
                log.error("createJobRequest error code: {} message: {}, requestId: {}",
                    createJobResponseResult.getCode(),
                    createJobResponseResult.getMessage(), createJobResponseResult.getRequestId());
                result.setSuccess(false);
                result.setCode(createJobResponseResult.getCode() + "");
                result.setMessage(createJobResponseResult.getMessage());
            }
        } catch (Exception e) {
            log.error("createJobRequest error.", e);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
        }
        return result;
    }

    public DataResult<Long> createDrcJob(String tenantId, String appName, Map<String, Object> jobParamsMap, String taskName) {
        DataResult<Long> result = new DataResult<>();
        result.setSuccess(true);
        result.setCode("200");
    	OpenApiClient client = getClientFromAppName(tenantId, appName);
        CreateJavaJobRequest createJobRequest = new CreateJavaJobRequest();
        
        // 任务基础信息
        JavaJobConfig jobConfig = new JavaJobConfig("com.aliyun.wormhole.qanat.job.QanatDrcJobProcessor");
        jobConfig.setName(taskName);
        jobConfig.setDescription(taskName);
        jobConfig.setParameters(JSON.toJSONString(jobParamsMap));
        jobConfig.setExecuteMode("standalone");
        
        TimeConfig timeConfig = new TimeConfig(TimeConfig.API_TYPE);
        jobConfig.setTimeConfig(timeConfig);
        
        // 报警配置
        JobMonitorInfo jobMonitorConfig = new JobMonitorInfo();
        MonitorConfig monitorConfig = new MonitorConfig();
        monitorConfig.setFailEnable(false);
        monitorConfig.setTimeoutEnable(false);
        monitorConfig.setTimeout(36000);
        monitorConfig.setTimeoutKillEnable(false);
        monitorConfig.setSendChannel("ding,sms");
        jobMonitorConfig.setMonitorConfig(monitorConfig);
        jobConfig.setJobMonitorInfo(jobMonitorConfig);
        createJobRequest.setJavaJobConfig(jobConfig);
        // 发送请求
        try {
            SchedulerXResult<CreateJobResponse> createJobResponseResult = client.getResponse(createJobRequest);
            // 请求解析
            if (createJobResponseResult.isSuccess()) {
                log.info("SchedulerX JobId:{}", createJobResponseResult.getData().getJobId());
                result.setData(createJobResponseResult.getData().getJobId());
            } else {
                log.error("createDrcJob error code: {} message: {}, requestId: {}",
                    createJobResponseResult.getCode(),
                    createJobResponseResult.getMessage(), createJobResponseResult.getRequestId());
                result.setSuccess(false);
                result.setCode(createJobResponseResult.getCode() + "");
                result.setMessage(createJobResponseResult.getMessage());
            }
        } catch (Exception e) {
            log.error("createDrcJob error.", e);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
        }
        return result;
    }

    public DataResult<Long> updateNodeJob(String tenantId, String appName, Long jobId, Map<String, Object> jobParamsMap) {
        DataResult<Long> result = new DataResult<>();
        result.setSuccess(true);
        result.setCode("200");
    	OpenApiClient client = getClientFromAppName(tenantId, appName);
        // 获取需要修改任务信息
        GetJobInfoRequest getJobInfoRequest = new GetJobInfoRequest();
        getJobInfoRequest.setJobId(jobId);
        
        // 发送请求
        try {
            SchedulerXResult<GetJobInfoResponse> getJobInfoResponseResult = client.getResponse(getJobInfoRequest);
            // 请求解析
            if (getJobInfoResponseResult.isSuccess()) {
                GetJobInfoResponse jobInfoResponse = getJobInfoResponseResult.getData();
                JobConfigInfo jobConfigInfo = jobInfoResponse.getJobConfigInfo();
                if (jobConfigInfo != null) {
                    // 构造请求
                    UpdateJobRequest updateJobRequest = new UpdateJobRequest();
                    jobConfigInfo.setParameters(JSON.toJSONString(jobParamsMap));
                    updateJobRequest.setJobConfigInfo(jobConfigInfo);
                    SchedulerXResult<UpdateJobResponse> updateJobResponseResult = client.getResponse(updateJobRequest);
                    if (updateJobResponseResult.isSuccess()) {
                        log.info("SchedulerX JobId:{}", updateJobResponseResult.getData().getJobId());
                        result.setData(updateJobResponseResult.getData().getJobId());
                    } else {
                        log.error("updateNodeJob error code: {} message: {}, requestId: {}",
                            updateJobResponseResult.getCode(),
                            updateJobResponseResult.getMessage(), updateJobResponseResult.getRequestId());
                        result.setSuccess(false);
                        result.setCode(updateJobResponseResult.getCode() + "");
                        result.setMessage(updateJobResponseResult.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.error("updateNodeJob error.", e);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
        }
        return result;
    }

    public DataResult<Long> runJob(String tenantId, String appName, Long jobId, Map<String, Object> instParamsMap) {
        DataResult<Long> result = new DataResult<>();
        result.setSuccess(true);
        result.setCode("200");
    	OpenApiClient client = getClientFromAppName(tenantId, appName);
        // 创建任务请求类内容填充
        ExecuteJobRequest executeJobRequest = new ExecuteJobRequest();
        executeJobRequest.setJobId(jobId);
        executeJobRequest.setInstanceParameters(JSON.toJSONString(instParamsMap));
        try {
            SchedulerXResult<ExecuteJobResponse> executeJobResponseResult = client.getResponse(executeJobRequest);
            if (executeJobResponseResult.isSuccess()) {
                result.setData(executeJobResponseResult.getData().getJobInstanceId());
            } else {
                log.error("executeJobRequest error code: {} message: {}, requestId: {}",
                    executeJobResponseResult.getCode(),
                    executeJobResponseResult.getMessage(), executeJobResponseResult.getRequestId());
                result.setSuccess(false);
                result.setCode(executeJobResponseResult.getCode() + "");
                result.setMessage(executeJobResponseResult.getMessage());
            }
        } catch (Exception e) {
            log.error("executeJobRequest error.", e);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
        }
        return result;
    }

    public DataResult<Boolean> deleteJob(String tenantId, String appName, Long jobId) {
        DataResult<Boolean> result = new DataResult<>();
        result.setSuccess(true);
        result.setCode("200");
    	OpenApiClient client = getClientFromAppName(tenantId, appName);
        // 创建任务请求类内容填充
        DeleteJobRequest deleteRequest = new DeleteJobRequest();
        deleteRequest.setJobId(jobId);
        try {
            SchedulerXResult<DeleteJobResponse> deleteJobResponseResult = client.getResponse(deleteRequest);
            if (deleteJobResponseResult.isSuccess()) {
                result.setData(true);
            } else {
                result.setData(false);
                log.error("deleteJobRequest error code: {} message: {}, requestId: {}", deleteJobResponseResult.getCode(),
                    deleteJobResponseResult.getMessage(), deleteJobResponseResult.getRequestId());
                result.setCode(deleteJobResponseResult.getCode() + "");
                result.setMessage(deleteJobResponseResult.getMessage());
            }
        } catch (Exception e) {
            log.error("deleteJob error.", e);
            result.setData(false);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
        }
        return result;
    }

    public DataResult<Boolean> killJob(String tenantId, String appName, Long jobId, Long instanceId) {
        DataResult<Boolean> result = new DataResult<>();
        result.setSuccess(true);
        result.setCode("200");
    	OpenApiClient client = getClientFromAppName(tenantId, appName);
        // 创建任务请求类内容填充
        KillInstanceRequest killInstanceRequest = new KillInstanceRequest();
        killInstanceRequest.setInstanceId(instanceId);
        killInstanceRequest.setJobId(jobId);
        try {
            SchedulerXResult<KillInstanceResponse> killInstanceResponseResult = client.getResponse(killInstanceRequest);
            if (killInstanceResponseResult.isSuccess()) {
                result.setData(true);
            } else {
                result.setData(false);
                log.error("killInstanceRequest error code: {} message: {}, requestId: {}", killInstanceResponseResult.getCode(),
                    killInstanceResponseResult.getMessage(), killInstanceResponseResult.getRequestId());
                result.setCode(killInstanceResponseResult.getCode() + "");
                result.setMessage(killInstanceResponseResult.getMessage());
            }
        } catch (Exception e) {
            log.error("killInstanceRequest error.", e);
            result.setData(false);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
        }
        return result;
    }

    public DataResult<List<JobInstanceDetail>> getJobInstList(String tenantId, String appName, Long jobId) {
        DataResult<List<JobInstanceDetail>> result = new DataResult<>();
        result.setSuccess(true);
        result.setCode("200");
    	OpenApiClient client = getClientFromAppName(tenantId, appName);
        // 创建任务请求类内容填充
        GetJobInstanceListRequest request = new GetJobInstanceListRequest();
        request.setJobId(jobId);
        try {
            SchedulerXResult<GetJobInstanceListResponse> respResult = client.getResponse(request);
            if (respResult.isSuccess()) {
                result.setData(respResult.getData().getJobInstanceDetails());
            } else {
                log.error("GetJobInstanceListRequest error code: {} message: {}, requestId: {}", respResult.getCode(),
                    respResult.getMessage(), respResult.getRequestId());
                result.setCode(respResult.getCode() + "");
                result.setMessage(respResult.getMessage());
            }
        } catch (Exception e) {
            log.error("GetJobInstanceListRequest error.", e);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
        }
        return result;
    }


    public DataResult<JobConfigInfo> getJobInfo(String tenantId, String appName, Long jobId) {
        DataResult<JobConfigInfo> result = new DataResult<>();
        result.setSuccess(true);
        result.setCode("200");
    	OpenApiClient client = getClientFromAppName(tenantId, appName);
        // 创建任务请求类内容填充
        GetJobInfoRequest request = new GetJobInfoRequest();
        request.setJobId(jobId);
        try {
            SchedulerXResult<GetJobInfoResponse> respResult = client.getResponse(request);
            if (respResult.isSuccess()) {
                result.setData(respResult.getData().getJobConfigInfo());
            } else {
                log.error("GetJobInstanceListRequest error code: {} message: {}, requestId: {}", respResult.getCode(),
                    respResult.getMessage(), respResult.getRequestId());
                result.setCode(respResult.getCode() + "");
                result.setMessage(respResult.getMessage());
            }
        } catch (Exception e) {
            log.error("GetJobInstanceListRequest error.", e);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
        }
        return result;
    }

    public DataResult<JobInstanceDetail> getJobInstDetail(String tenantId, String appName, Long jobId, Long jobInstId) {
        DataResult<JobInstanceDetail> result = new DataResult<>();
        result.setSuccess(true);
        result.setCode("200");
    	OpenApiClient client = getClientFromAppName(tenantId, appName);
        // 创建任务请求类内容填充
        GetJobInstanceRequest request = new GetJobInstanceRequest();
        request.setJobId(jobId);
        request.setJobInstanceId(jobInstId);
        try {
            SchedulerXResult<GetJobInstanceResponse> respResult = client.getResponse(request);
            if (respResult.isSuccess()) {
                result.setData(respResult.getData().getJobInstanceDetail());
            } else {
                log.error("GetJobInstanceRequest error code: {} message: {}, requestId: {}", respResult.getCode(),
                    respResult.getMessage(), respResult.getRequestId());
                result.setCode(respResult.getCode() + "");
                result.setMessage(respResult.getMessage());
            }
        } catch (Exception e) {
            log.error("GetJobInstanceRequest error.", e);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
        }
        return result;
    }
}