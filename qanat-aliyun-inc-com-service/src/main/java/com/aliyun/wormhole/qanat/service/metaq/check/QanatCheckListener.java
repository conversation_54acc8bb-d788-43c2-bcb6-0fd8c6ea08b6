package com.aliyun.wormhole.qanat.service.metaq.check;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.aliyun.wormhole.qanat.service.ateye.SystemSwitch;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.enumerate.EventType;
import com.aliyun.wormhole.qanat.service.metaq.common.BaseListener;
import com.aliyun.wormhole.qanat.service.metaq.common.TaskRetryException;
import com.aliyun.wormhole.qanat.service.util.RateLimitHelper;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.ateye.monitor.TripMonitor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.*;


/**
 * <AUTHOR>
 */
@Component
public class QanatCheckListener extends BaseListener {

    @Switch(name = "metaFields", description = "对比忽略字段")

    private String metaFields = "__traceId__,dbName,tableName,query_cloumn,op";

    private final String QUERY_TEMPLATE = "SELECT * FROM %s where %s = %s";

    private final String VARCHAR = "varchar";

    private static Logger log = LoggerFactory.getLogger(QanatCheckListener.class);

    private static final String FULL_TOPIC = "qanat_check_full_topic";

    Gson gson = new Gson();

    /**
     * //TIME： 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
     * //LEVEL：1  2  3   4   5  6  7  8  9  10 11 12 13 14  15  16  17 18
     */
    static Map<Integer,String> delayLevel = new HashMap<Integer,String>(){{
        put(1,"1s");
        put(2,"5s");
        put(3,"10s");
        put(4,"30s");
        put(5,"1m");
        put(6,"2m");
        put(7,"3m");
        put(8,"4m");
        put(9,"5m");
        put(10,"6m");
        put(11,"7m");
        put(12,"8m");
        put(13,"9m");
        put(14,"10m");
        put(15,"20m");
        put(16,"30m");
        put(17,"1h");
        put(18,"2h");
    }};

    @Resource
    QanatDatasourceHandler qanatDatasourceHandler;

    @Resource
    RateLimitHelper rateLimitHelper;

    @Override
    protected void processMessage(MessageExt msg) throws TaskRetryException {
        if (SystemSwitch.openLog) {
            log.info("QanatCheckListener meg : {}", new String(msg.getBody()));
        }
//        if (FULL_TOPIC.equalsIgnoreCase(msg.getTopic()) && rateLimitHelper.isUseRateLimiter()){
//            rateLimitHelper.acquire();
//            TripMonitor.common("rateLimitUseSeconds", msg.getTopic(), "").setValue1(1);
//        }
        JSONObject jsonObject = this.gson.fromJson(new String(msg.getBody()), JSONObject.class);
        String eventType = jsonObject.getString("op");
        String tags = msg.getTags();
        String dbName = jsonObject.getString("dbName");
        String tableName = jsonObject.getString("tableName");
        String queryCloumn = jsonObject.getString("query_cloumn");
        String[] cloumn = queryCloumn.split(":");

        int delayTime = getDeleteRetryTimes(msg.getReconsumeTimes());
        EventType event = EventType.codeOf(Integer.valueOf(eventType));
        if (msg.getReconsumeTimes() == 0){
            TripMonitor.succRate(tags, event.getName(), delayLevel.get(delayTime)).incrTotal();
        }
        if(msg.getReconsumeTimes() >= retryTimes){
            TripMonitor.failRate(tags, event.getName(), delayLevel.get(delayTime)).incrFail();
            return;
        }

        Map<String,Object> result = getResult(dbName, tableName, cloumn[0], jsonObject.getString(cloumn[0]), cloumn[1]);
        if (!compare(eventType, jsonObject, result, msg.getReconsumeTimes())){
                log.info("compare fail source:{} :{}",jsonObject.toJSONString(), JSONObject.toJSONString(result));
            throw new TaskRetryException(delayTime);
        }
        TripMonitor.succRate(tags, event.getName(), delayLevel.get(delayTime)).incrSucc();
    }

    private boolean compare(String eventType, JSONObject jsonObject, Map<String,Object> result, int reconsumeTimes) {
        if (EventType.DELETE == EventType.codeOf(Integer.valueOf(eventType))){
            if (SystemSwitch.openLog){
                log.info("compare Delete msg :{}",JSONObject.toJSONString(result));
            }
            if (CollectionUtils.isEmpty(result)){
                return true;
            }else{
                return false;
            }
        }
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()){
            try {
                if (SystemSwitch.openLog){
                    log.info("compare UPDATE INSERT msg :{}",JSONObject.toJSONString(result));
                }
                if (CollectionUtils.isEmpty(result)){
                    return false;
                }
                if (metaFields.contains(entry.getKey())){
                    continue;
                }
                Object dbValue = result.get(entry.getKey());
                if (dbValue == null){
                    if (entry.getValue() == null || StringUtils.isBlank(entry.getValue().toString())){
                        return true;
                    }
                    return false;
                }
                String dbValueString = dbValue.toString();
                if (StringUtils.isBlank(dbValueString) && (entry.getValue() == null || StringUtils.isBlank(entry.getValue().toString()))){
                    continue;
                }
                if (!entry.getValue().toString().equalsIgnoreCase(dbValue.toString())){
                    return false;
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return true;
    }

    private String getQuerySql(String tableName, String cloumnName, String cloumnValue, String cloumnType){
        if (VARCHAR.equalsIgnoreCase(cloumnType)) {
            cloumnValue = "\"" + cloumnValue + "\"";
        }
        return String.format(QUERY_TEMPLATE, tableName, cloumnName, cloumnValue);
    }

    @AteyeInvoker(description = "getResult", paraDesc = "dbName&tableName&cloumnName&CloumnValue&cloumnType")
    private Map<String,Object> getResult(String dbName, String tableName, String cloumnName, String cloumnValue, String cloumnType ){
        Map<String,String> result = Maps.newHashMap();
        DataSource dataSource = qanatDatasourceHandler.getDatasourceByDbName(dbName);
        String querySql = getQuerySql(tableName, cloumnName, cloumnValue, cloumnType);
        List<Map<String, Object>> results = new JdbcTemplate(dataSource).queryForList(querySql);
        if (CollectionUtils.isEmpty(results)){
            return null;
        }
        return results.get(0);
    }

    /**
     * //TIME： 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
     * //LEVEL：1  2  3   4   5  6  7  8  9  10 11 12 13 14  15  16  17 18
     * @param retryTimes
     * @return
     */
    @Switch(description = "最后一次重试的步长" +
            "//TIME： 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h\n" +
            "//LEVEL：1  2  3   4   5  6  7  8  9  10 11 12 13 14  15  16  17 18")
    public int finalRetryTime = 14;
    private int getDeleteRetryTimes(int retryTimes){
        switch (retryTimes){
            case 0:
                return 1;
            case 1 :
                return 3;
            case 2 :
                return 4;
            case 3 :
                return 12;
            case 4 :
                return finalRetryTime;
            default:
                return 11;
        }
    }
}
