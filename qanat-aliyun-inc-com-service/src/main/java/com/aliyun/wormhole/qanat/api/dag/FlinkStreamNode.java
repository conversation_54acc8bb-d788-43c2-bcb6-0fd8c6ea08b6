package com.aliyun.wormhole.qanat.api.dag;

import lombok.Data;

@Data
public class FlinkStreamNode extends Node {
    private String jobName;

    private String startTimePolicy;

    public FlinkStreamNode() {};

    public FlinkStreamNode(String id, Dag dag) {
        super(id, dag);
        this.setNodeAction(NodeAction.STREAM);
        this.setAction("com.aliyun.wormhole.qanat.job.QanatFlinkJobProcessor");
    }
}
