package com.aliyun.wormhole.qanat.service.ods;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastsql.DbType;
import com.alibaba.fastsql.sql.SQLUtils;
import com.alibaba.fastsql.sql.ast.SQLStatement;
import com.alibaba.fastsql.sql.visitor.SchemaStatVisitor;
import com.alibaba.fastsql.stat.TableStat.Column;

import com.alicloud.openservices.tablestore.TunnelClient;
import com.alicloud.openservices.tablestore.model.tunnel.CreateTunnelRequest;
import com.alicloud.openservices.tablestore.model.tunnel.CreateTunnelResponse;
import com.alicloud.openservices.tablestore.model.tunnel.TunnelType;
import com.aliyun.odps.Instance;
import com.aliyun.odps.Odps;
import com.aliyun.odps.task.SQLTask;
import com.aliyun.tag.api.vo.SimpleTagVO;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.*;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.DatasourceRequest;
import com.aliyun.wormhole.qanat.api.dto.OdsSyncTaskRequest;
import com.aliyun.wormhole.qanat.api.dto.ResourcePackage;
import com.aliyun.wormhole.qanat.api.dto.ConsumerInfo;
import com.aliyun.wormhole.qanat.api.dto.CreateOdsRequest;
import com.aliyun.wormhole.qanat.api.dto.OdsSyncTaskResponse;
import com.aliyun.wormhole.qanat.api.dto.TaskInfoRequest;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.DrcService;
import com.aliyun.wormhole.qanat.api.service.KafkaManagementService;
import com.aliyun.wormhole.qanat.api.service.TaskService;
import com.aliyun.wormhole.qanat.dal.domain.*;
import com.aliyun.wormhole.qanat.dal.mapper.AppInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsFieldInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsTaskRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TenantInfoMapper;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.service.flink.FlinkService;
import com.aliyun.wormhole.qanat.service.odps.OdpsService;
import com.aliyun.wormhole.qanat.service.template.Adb3SyncTemplate;
import com.aliyun.wormhole.qanat.service.util.YamlUtil;
import com.aliyun.wormhole.qanat.service.viewmodel.FullLinkProcessor;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelOptimizer;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * ADB实时数仓同步服务
 * <AUTHOR>
 * 2019年7月23日
 */
@Slf4j
@Component
public class OdsHandler {

	@Resource
    private DatasourceService dsInfoService;
    
    @Resource
    private DatasourceMapper datasourceMapper;
    
    @Resource
    private DsFieldInfoMapper dsFieldInfoMapper;
    
    @Resource
    private TaskInfoMapper taskInfoMapper;
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private DsRelationMapper dsRelationMapper;
    
    @Resource
    private TaskService taskService;
    
    @Resource
    private DrcService drcService;
    
    @Resource
    private KafkaManagementService kafkaManagementService;
    
    @Resource
    private AppInfoMapper appInfoMapper;
    
    @Resource
    private FullLinkProcessor fullLinkProcessor;
    
    @Resource
    private TenantInfoMapper tenantInfoMapper;
    
    @Resource
    private ViewModelOptimizer viewModelOptimizer;

	@Resource
    private DsTaskRelationMapper dsTaskRelationMapper;
	
	@Resource
	private OdpsService odpsService;

	@Resource
	private FlinkService flinkService;

	@Resource
	protected QanatDatasourceHandler dsHandler;

	private static final String BIZ_ID_PREFIX="124299^aliyun_tag^id^";
	private static final String BIZ_ID_KEY="biz_id";
	private Odps odps;

    @Transactional(propagation = Propagation.REQUIRED)
    public Map<String, Long> createDsInfoAndOdsTask(CreateOdsRequest createOdsReq) {
        log.info("start createDsInfoAndOdsTask({})", JSON.toJSONString(createOdsReq));
        
    	if (StringUtils.isBlank(createOdsReq.getTenantId())
    			|| StringUtils.isBlank(createOdsReq.getAppName())
    			|| StringUtils.isBlank(createOdsReq.getDbName())
    			|| StringUtils.isBlank(createOdsReq.getTableName())
    			|| StringUtils.isBlank(createOdsReq.getDatatubeLevel())
    			|| null == createOdsReq.getPredictQph()
    			|| null == createOdsReq.getPredictSize()) {
    		throw new QanatBizException("tenantId/appName/dbName/tableName/predictQph/predictSize/datatubeLevel is neccesary");
    	}
    	Map<String, Long> data = new HashMap<>();
    	//校验dbInfo
    	DbInfoExample example = new DbInfoExample();
    	example.createCriteria().andDbNameEqualTo(createOdsReq.getDbName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(createOdsReq.getTenantId());
    	List<DbInfo> dbInfos = dbInfoMapper.selectByExample(example);
    	if (CollectionUtils.isEmpty(dbInfos)) {
    		throw new QanatBizException("dbName:" + createOdsReq.getDbName() + " is not exists");
    	}
    	if (!"mysql".equalsIgnoreCase(dbInfos.get(0).getDbType())
    			&& !"tddl".equalsIgnoreCase(dbInfos.get(0).getDbType())
    			&& !"odps".equalsIgnoreCase(dbInfos.get(0).getDbType())) {
    		throw new QanatBizException("only mysql/tddl/odps srcDbType is supported");
    	}
    	
    	String srcDsName = dsInfoService.getDsName(createOdsReq.getTenantId(), createOdsReq.getAppName(), createOdsReq.getDbName(), createOdsReq.getTableName());
    	
    	DatasourceExample dsInfoExample = new DatasourceExample();
    	dsInfoExample.createCriteria().andIsDeletedEqualTo(0L)
								    	.andTenantIdEqualTo(createOdsReq.getTenantId())
								    	.andTableNameEqualTo(createOdsReq.getTableName())
								    	.andDbNameEqualTo(createOdsReq.getDbName());
    	List<Datasource> dsInfos = datasourceMapper.selectByExample(dsInfoExample);
    	if (CollectionUtils.isNotEmpty(dsInfos)) {
    		srcDsName = dsInfos.get(0).getDsName();
    		data.put("dsId", dsInfos.get(0).getId());
    	} else {
	    	//创建DsInfo
	    	DatasourceRequest dsInfoReq = new DatasourceRequest();
	    	dsInfoReq.setTenantId(createOdsReq.getTenantId());
	    	dsInfoReq.setDbName(createOdsReq.getDbName());
	    	dsInfoReq.setDsName(dsInfoService.getDsName(createOdsReq.getTenantId(), createOdsReq.getAppName(), createOdsReq.getDbName(), createOdsReq.getTableName()));
	    	dsInfoReq.setDsType(dbInfos.get(0).getDbType());
	    	dsInfoReq.setTableName(createOdsReq.getTableName());
	    	dsInfoReq.setOperateEmpid(createOdsReq.getOperateEmpid());
	    	dsInfoReq.setObjectType(createOdsReq.getObjectType());
	    	if ("odps".equalsIgnoreCase(dbInfos.get(0).getDbType()) && StringUtils.isNoneBlank(createOdsReq.getTimeExpression())) {
	    		dsInfoReq.setMeta("{\"partitionPolicy\":\"T-1D\",\"timeExpression\":\"" + createOdsReq.getTimeExpression() + "\"}");
	    	}
	    	dsInfoReq.setPkField(createOdsReq.getPkField());
	    	dsInfoReq.setPredictQph(createOdsReq.getPredictQph());
	    	dsInfoReq.setPredictSize(createOdsReq.getPredictSize());
	    	dsInfoReq.setWorkTime(createOdsReq.getWorkTime());
	    	dsInfoReq.setDatatubeLevel(createOdsReq.getDatatubeLevel());
	    	
	    	JSONObject dsMetaJson = null;
	    	if (StringUtils.isBlank(dsInfoReq.getMeta())) {
	    		dsMetaJson = new JSONObject();
	    	} else {
	    		dsMetaJson = JSON.parseObject(dsInfoReq.getMeta());
	    		if (dsMetaJson == null) {
		    		dsMetaJson = new JSONObject();
	    		}
	    	}
	    	if (StringUtils.isNotBlank(createOdsReq.getDistributeKey())) {
	    		dsMetaJson.put("distributeKey", createOdsReq.getDistributeKey());
	    	}
	    	if (createOdsReq.getBroadcast() != null) {
	    		dsMetaJson.put("broadcast", createOdsReq.getBroadcast());
	    	}
	    	if (createOdsReq.getNoPartition() != null) {
	    		dsMetaJson.put("noPartition", createOdsReq.getNoPartition());
	    	}
	    	if (StringUtils.isNotBlank(createOdsReq.getPartitionPolicy())) {
	    		dsMetaJson.put("partitionPolicy", createOdsReq.getPartitionPolicy());
	    	}
	    	if (StringUtils.isNotBlank(createOdsReq.getSyncMode())) {
	    		dsMetaJson.put("syncMode", createOdsReq.getSyncMode());
	    	}
			if (StringUtils.isNotBlank(createOdsReq.getPartitionKey())) {
				dsMetaJson.put("partitionKey", createOdsReq.getPartitionKey());
			}
	    	if (StringUtils.isNotBlank(createOdsReq.getExtConf())) {
	    		try {
	    			dsMetaJson.put("extConf", JSON.parseObject(createOdsReq.getExtConf()));
	    		} catch(Exception e) {}
	    	}
	    	dsInfoReq.setMeta(dsMetaJson.toJSONString());
	    	
	    	DataResult<Long> createDsInfoResult = dsInfoService.createDatasource(dsInfoReq);
	    	log.info("createDsInfoResult={}", JSON.toJSONString(createDsInfoResult));
	    	if (createDsInfoResult != null) {
	    		data.put("dsId", createDsInfoResult.getData());
	    	}
	    	
	    	//创建DRC任务
	    	if (("mysql".equalsIgnoreCase(dbInfos.get(0).getDbType())
	    			|| "tddl".equalsIgnoreCase(dbInfos.get(0).getDbType())) && !createOdsReq.isFullSyncOnly()) {
	        	DataResult<Map<String, Long>> drcResult = drcService.createDrcTaskForDs(createOdsReq.getTenantId(), createOdsReq.getAppName(), srcDsName, createOdsReq.getOperateEmpid(), createOdsReq.getDatatubeLevel());
	        	if (drcResult != null && drcResult.getSuccess() && drcResult.getData() != null) {
	        		data.put("drcId", drcResult.getData().get("drcTaskId"));
	        		data.put("drcTaskId", drcResult.getData().get("taskId"));
	        	} else {
	        		throw new QanatBizException("create Drc Task failed");
	        	}
	    	}
    	}
    	TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(createOdsReq.getTenantId());
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + createOdsReq.getTenantId() + " is not configured");
    	}
        //创建数据同步任务
    	OdsSyncTaskRequest taskReq = new OdsSyncTaskRequest();
    	taskReq.setAppName(createOdsReq.getAppName());
    	List<String> dstDbNames = new ArrayList<>();
    	if (StringUtils.isNotBlank(createOdsReq.getDstDbName())) {
    		dstDbNames.add(createOdsReq.getDstDbName());
    	} else {
	    	dstDbNames.add(tenantList.get(0).getDefaultDw());
	    	if (StringUtils.isNotBlank(tenantList.get(0).getBackupDw())) {
	    		dstDbNames.addAll(Arrays.asList(tenantList.get(0).getBackupDw().split(",")));
	    	}
    	}
    	taskReq.setEtlDbName(tenantList.get(0).getEtlDw());
    	taskReq.setDstDbNames(dstDbNames);
    	taskReq.setEnableCheck(false);
    	taskReq.setEnableFullLink(false);
    	taskReq.setEnableIncrSync(createOdsReq.isFullSyncOnly() ? false : true);
    	taskReq.setFullSyncBatchSize(204800);
    	taskReq.setFullSyncParallelism("16");
    	taskReq.setOperateEmpid(createOdsReq.getOperateEmpid());
    	taskReq.setRecordSizeW(createOdsReq.getPredictSize() == null || createOdsReq.getPredictSize() < 10000 ? 1 : (createOdsReq.getPredictSize().intValue()/10000));
    	taskReq.setSrcDsName(srcDsName);
    	taskReq.setTableName(createOdsReq.getTableName());
    	taskReq.setTenantId(createOdsReq.getTenantId());
    	taskReq.setTimeExpression(createOdsReq.getTimeExpression());
    	taskReq.setDatatubeLevel(createOdsReq.getDatatubeLevel());
    	taskReq.setPredictQph(createOdsReq.getPredictQph());
    	taskReq.setPredictSize(createOdsReq.getPredictSize());
 
    	OdsSyncTaskResponse createResult = null;
    	try {
	    	createResult = createBatchStreamTasks(taskReq);
	    	log.info("createBatchStreamTasks result:{}", JSON.toJSONString(createResult));
	    	if (createResult != null && createResult.getDataSyncTaskId() != null) {
	    		data.put("taskId", createResult.getDataSyncTaskId());
	    		
	    		DataResult<Long> runResult = taskService.runTask(createOdsReq.getTenantId(), createOdsReq.getOperateEmpid(), createResult.getDataSyncTaskId());
	    		if (runResult != null) {
	    			data.put("taskInstId", runResult.getData());
	    		}
	    	}
    	} catch(QanatBizException e) {
    		if ("501".equals(e.getCode()) && createOdsReq.isForceRebuildOnDuplicate()) {
    			DataResult<Boolean> stopResult = taskService.stopAndDeleteScheduleTask(createOdsReq.getTenantId(), createOdsReq.getOperateEmpid(), e.getBizId());
    			if (stopResult != null && stopResult.getData()) {
    				log.info("finish to stopAndDelete task:{} and retry to create again", e.getBizId());
    				createResult = createBatchStreamTasks(taskReq);
    	        	if (createResult != null && createResult.getDataSyncTaskId() != null) {
    	        		data.put("taskId", createResult.getDataSyncTaskId());
    	        		
    	        		DataResult<Long> runResult = taskService.runTask(createOdsReq.getTenantId(), createOdsReq.getOperateEmpid(), createResult.getDataSyncTaskId());
    	        		if (runResult != null) {
    	        			data.put("taskInstId", runResult.getData());
    	        		}
    	        	}
    			}
    		} else {
    			throw new QanatBizException("create Ods Task failed:" + e.getMessage());
    		}
    	}
    	
		DsTaskRelationExample delExample = new DsTaskRelationExample();
		delExample.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(createOdsReq.getTenantId()).andDsNameEqualTo(srcDsName);
		dsTaskRelationMapper.deleteByExample(delExample);
		
    	DsTaskRelation record = new DsTaskRelation();
    	record.setCreateEmpid(createOdsReq.getOperateEmpid());
    	record.setDsName(srcDsName);
    	record.setGmtCreate(new Date());
    	record.setGmtModified(new Date());
    	record.setIsDeleted(0L);
    	record.setModifyEmpid(createOdsReq.getOperateEmpid());
    	record.setRelationType("ods");
    	record.setTaskId(data.get("taskId"));
    	record.setTenantId(createOdsReq.getTenantId());
    	dsTaskRelationMapper.insert(record);
    	
    	if (data.get("drcTaskId") != null) {
    		record = new DsTaskRelation();
        	record.setCreateEmpid(createOdsReq.getOperateEmpid());
        	record.setDsName(srcDsName);
        	record.setGmtCreate(new Date());
        	record.setGmtModified(new Date());
        	record.setIsDeleted(0L);
        	record.setModifyEmpid(createOdsReq.getOperateEmpid());
        	record.setRelationType("drc");
        	record.setTaskId(data.get("drcTaskId"));
        	record.setTenantId(createOdsReq.getTenantId());
        	dsTaskRelationMapper.insert(record);
    	}
    	
    	if ("odps".equalsIgnoreCase(dbInfos.get(0).getDbType())
    			&& StringUtils.isBlank(createOdsReq.getTimeExpression())) {
    		odpsService.registerOdpsMetaEvent(createOdsReq.getTenantId(), srcDsName);
    	}
    	
    	return data;
    }

    public OdsSyncTaskResponse createBatchStreamTasks(OdsSyncTaskRequest taskReq) {
        log.info("start createBatchStreamTasks({})", JSON.toJSONString(taskReq));
        
    	if (StringUtils.isBlank(taskReq.getTenantId())) {
    		throw new QanatBizException("tenantId is empty");
    	}
    	if (StringUtils.isBlank(taskReq.getAppName())) {
    		throw new QanatBizException("appName is empty");
    	}
    	if (StringUtils.isBlank(taskReq.getSrcDsName())) {
    		throw new QanatBizException("srcDsName is empty");
    	}
    	if (StringUtils.isBlank(taskReq.getDstDsName()) && StringUtils.isBlank(taskReq.getDstDbName()) && StringUtils.isBlank(taskReq.getTableName())
    			&& CollectionUtils.isEmpty(taskReq.getDstDbNames())) {
    		throw new QanatBizException("dstDsName or (dstDbName and tableName) or dstDbNames must be not empty");
    	}
        OdsSyncTaskResponse resp = new OdsSyncTaskResponse();
        //处理src数据源，如果没有则创建一个数据源
        String srcDsName = taskReq.getSrcDsName();
        JSONObject srcDsMetaJson  = getDsMeta(taskReq.getTenantId(), srcDsName);
    	JSONObject kafkaJson = kafkaManagementService.getKafkaConfByAppName(taskReq.getTenantId(), taskReq.getAppName());
        
        //处理dst数据源，如果没有则创建一个数据源
    	List<String> dstDsNames = new ArrayList<>();
    	Map<String, JSONObject> dstDsMetaMap = new HashMap<>();
        String dstIncrTopic = "stream-" + getAppIdByName(taskReq.getTenantId(), taskReq.getAppName()) + "-ods-" + taskReq.getTableName();
        if (StringUtils.isNotBlank(taskReq.getDstDsName())) {
            JSONObject dstDsMetaJson = getDsMeta(taskReq.getTenantId(), taskReq.getDstDsName());
        	if (StringUtils.isBlank(taskReq.getTaskName())) {
        		taskReq.setTaskName("DAG_ods_" + getAppIdByName(taskReq.getTenantId(), taskReq.getAppName()) + "_" + srcDsMetaJson.getLong("dsId") + "_" + dstDsMetaJson.getLong("dsId") + "_"  + taskReq.getSrcDsName());
        	}
        	dstDsNames.add(taskReq.getDstDsName());
        	dstDsMetaMap.put(taskReq.getDstDsName(), dstDsMetaJson);
        } else {
        	if (StringUtils.isNotBlank(taskReq.getDstDbName())) {
	            DbInfo dbInfo = getDbInfoByName(taskReq.getTenantId(), taskReq.getDstDbName());
	        	if (StringUtils.isBlank(taskReq.getTaskName())) {
	        		taskReq.setTaskName("DAG_ods_" + getAppIdByName(taskReq.getTenantId(), taskReq.getAppName()) + "_" + srcDsMetaJson.getLong("dsId") + "_" + dbInfo.getId() + "_"  + taskReq.getSrcDsName());
	        	}
	            String dstDsName = dsInfoService.getDsName(dbInfo.getId(), dbInfo.getDbType(), taskReq.getTenantId(), taskReq.getAppName(), taskReq.getTableName());
	            JSONObject dstDsMetaJson = getDsMetaByDbAndTableName(taskReq.getTenantId(), taskReq.getTableName(), dstDsName, taskReq.getOperateEmpid(), dbInfo, taskReq.getAppName(), dstIncrTopic, srcDsMetaJson);
	        	
	            dstDsNames.add(dstDsName);
	        	dstDsMetaMap.put(dstDsName, dstDsMetaJson);
        	} else if (CollectionUtils.isNotEmpty(taskReq.getDstDbNames())) {
        		for (String dstDbName : taskReq.getDstDbNames()) {
        			DbInfo dbInfo = getDbInfoByName(taskReq.getTenantId(), dstDbName);
    	        	if (StringUtils.isBlank(taskReq.getTaskName())) {
    	        		taskReq.setTaskName("DAG_ods_" + getAppIdByName(taskReq.getTenantId(), taskReq.getAppName()) + "_" + srcDsMetaJson.getLong("dsId") + "_" + dbInfo.getId() + "_"  + taskReq.getSrcDsName());
    	        	}
    	            String dstDsName = dsInfoService.getDsName(dbInfo.getId(), dbInfo.getDbType(), taskReq.getTenantId(), taskReq.getAppName(), taskReq.getTableName());
    	            JSONObject dstDsMetaJson = getDsMetaByDbAndTableName(taskReq.getTenantId(), taskReq.getTableName(), dstDsName, taskReq.getOperateEmpid(), dbInfo, taskReq.getAppName(), dstIncrTopic, srcDsMetaJson);
    	        	
    	            dstDsNames.add(dstDsName);
    	        	dstDsMetaMap.put(dstDsName, dstDsMetaJson);
        		}
        	}
        }
        if ("odps".equalsIgnoreCase(srcDsMetaJson.getString("dsType"))) {
			Dag dag = new Dag(taskReq.getTaskName());
			if (StringUtils.isNotBlank(taskReq.getTimeExpression())) {
				dag.setTimeExpression(taskReq.getTimeExpression());
			}
			List<Node> nodes = new ArrayList<>();
			for (int i = 0; i < dstDsNames.size(); i++) {
				JSONObject dstDsMetaJson = dstDsMetaMap.get(dstDsNames.get(i));
				try {
					DsRelation newDsRel = new DsRelation();
					newDsRel.setCreateEmpid(taskReq.getOperateEmpid());
					newDsRel.setDstDsName(dstDsNames.get(i));
					newDsRel.setGmtCreate(new Date());
					newDsRel.setGmtModified(new Date());
					newDsRel.setIsDeleted(0L);
					newDsRel.setModifyEmpid(taskReq.getOperateEmpid());
					newDsRel.setRelationType("ods");
					newDsRel.setSrcDsName(srcDsName);
					newDsRel.setTenantId(taskReq.getTenantId());
					int insCnt = dsRelationMapper.insert(newDsRel);
					log.info("insert [{}] dsRelations[{},{},{}]", insCnt, srcDsName, dstDsNames.get(i), "ods");
				} catch (Exception e) {
				}
				try {
					DsRelation newDsRel = new DsRelation();
					newDsRel.setCreateEmpid(taskReq.getOperateEmpid());
					newDsRel.setDstDsName(srcDsName);
					newDsRel.setGmtCreate(new Date());
					newDsRel.setGmtModified(new Date());
					newDsRel.setIsDeleted(0L);
					newDsRel.setModifyEmpid(taskReq.getOperateEmpid());
					newDsRel.setRelationType("odps");
					newDsRel.setSrcDsName(dstDsNames.get(i));
					newDsRel.setTenantId(taskReq.getTenantId());
					int insCnt = dsRelationMapper.insert(newDsRel);
					log.info("insert [{}] dsRelations[{},{},{}]", insCnt, dstDsNames.get(i), srcDsName, "odps");
				} catch (Exception e) {
				}
				if ("adb3".equalsIgnoreCase(dstDsMetaJson.getString("dsType"))) {
					Adb3ExtTblNode fullSync = new Adb3ExtTblNode("Adb3ExtTbl_" + srcDsName + "_" + dstDsMetaJson.getString("dbName"), dag);
					fullSync.setSrcDsName(srcDsName);
					fullSync.setDstDbName(dstDsMetaJson.getString("dbName"));

					nodes.add(fullSync);
					if (i > 0) {
						nodes.get(i - 1).setNext(fullSync);
					}
				} else if ("hologres".equalsIgnoreCase(dstDsMetaJson.getString("dsType"))) {
					HoloExtTblNode fullSync = new HoloExtTblNode("HoloExtTbl_" + srcDsName + "_" + dstDsMetaJson.getString("dbName"), dag);
					fullSync.setSrcDsName(srcDsName);
					fullSync.setDstDbName(dstDsMetaJson.getString("dbName"));

					nodes.add(fullSync);
					if (i > 0) {
						nodes.get(i - 1).setNext(fullSync);
					}
				}
			}

			TaskInfoExample taskExp = new TaskInfoExample();
			taskExp.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(taskReq.getTenantId()).andNameEqualTo(taskReq.getTaskName());
			List<TaskInfo> taskInfos = taskInfoMapper.selectByExample(taskExp);
			if (CollectionUtils.isEmpty(taskInfos)) {
				TaskInfoRequest taskInfo = new TaskInfoRequest();
				taskInfo.setName(taskReq.getTaskName());
				taskInfo.setOperateEmpid(taskReq.getOperateEmpid());
				taskInfo.setPolicy(DagPolicy.ODS.toString());
				taskInfo.setTenantId(taskReq.getTenantId());
				taskInfo.setAppName(taskReq.getAppName());
				Long taskId = taskService.createDAGTask(taskInfo, dag);
				if (taskId == null) {
					throw new QanatBizException("任务创建失败");
				}
				resp.setDataSyncTaskId(taskId);
			} else {
				if (taskService.updateTaskDag(taskInfos.get(0).getId(), taskReq.getOperateEmpid(), dag, false)) {
					resp.setDataSyncTaskId(taskInfos.get(0).getId());
					log.info("任务[{}:{}]更新DAG成功", taskInfos.get(0).getId(), taskInfos.get(0).getName());
				} else {
					throw new QanatBizException("任务:" + taskInfos.get(0).getId() + " 更新DAG失败");
				}
			}
		} else if ("obj".equalsIgnoreCase(srcDsMetaJson.getString("dsType"))) {
			//默认OTS
			DsFieldInfoExample dsFieldExample = new DsFieldInfoExample();
			dsFieldExample.createCriteria().andTenantIdEqualTo(taskReq.getTenantId()).andIsDeletedEqualTo(0l).andDsNameEqualTo(srcDsName);
			List<DsFieldInfo> dsFieldInfoList = dsFieldInfoMapper.selectByExample(dsFieldExample);

			DatasourceExample example = new DatasourceExample();
			example.createCriteria().andTenantIdEqualTo(taskReq.getTenantId()).andIsDeletedEqualTo(0l).andDsNameEqualTo(srcDsName);
			List<Datasource> dsInfoList = datasourceMapper.selectByExample(example);
			Datasource srcDsInfo = dsInfoList.get(0);

			//查询对象元数据
			JSONObject objMetaJson = dsInfoService.getObjectMeta(taskReq.getTenantId(), srcDsInfo.getObjectType(), srcDsInfo.getDsUniqueName());

			if (srcDsInfo.getPredictSize() > 500 * 10000) {
				//如果记录数大于500w，只能选择ODPS全量写入策略
				StringBuffer sql = new StringBuffer();
				String objectUniqueCode = srcDsInfo.getDsUniqueName();
				String pkField = srcDsInfo.getPkFields();
				JSONObject odpsDsMeta = dsInfoService.getOdpsTableMetaByObjectCode("1", objectUniqueCode);
				String tableName = "s_tag_meta_tag_object_biz_relation_aliyun_tag_m_app_new";
				if (odpsDsMeta != null && odpsDsMeta.getString("table") != null) {
					tableName = odpsDsMeta.getString("table");
				}
				String dstTableName = " aliyun_tag.tmp_" + srcDsInfo.getTableName();
				sql.append("CREATE TABLE " + dstTableName + " LIFECYCLE 1 AS ");
				if (odpsDsMeta.containsKey("storeType") && "slot".equalsIgnoreCase(odpsDsMeta.getString("storeType")) && odpsDsMeta.getJSONObject("slotMeta") != null) {
					tableName = odpsDsMeta.getJSONObject("slotMeta").getString("table");
					List<String> fieldList = dsInfoService.getObjectSlotFieldList(odpsDsMeta.getString("objectType"), objectUniqueCode, null);
					sql.append("SELECT object_id as " + pkField + ",");
					for (int i = 0; i < fieldList.size(); i++) {
						SimpleTagVO field = JSON.parseObject(fieldList.get(i), SimpleTagVO.class);
						sql.append(field.getSlotFieldCode() + " AS " + field.getCode());
						if (i < (fieldList.size() - 1)) {
							sql.append(",");
						}
					}
					sql.append(" FROM ");
					sql.append(tableName);
					sql.append(" WHERE pt=MAX_PT('" + tableName + "') AND is_deleted = 0 AND tenant_id = 'aliyun' AND object_code = '" + objectUniqueCode + "';");
				} else {
					sql.append("SELECT object_biz_id as " + pkField + ",");
					for (int i = 0; i < dsFieldInfoList.size(); i++) {
						DsFieldInfo field = dsFieldInfoList.get(i);
						sql.append("MAX(CASE WHEN tag_unique_code = '" + field.getFieldUniqueName() + "' THEN tag_value ELSE NULL END) AS " + field.getFieldName() + "");
						if (i < (dsFieldInfoList.size() - 1)) {
							sql.append(",");
						}
					}
					sql.append(" FROM aliyun_tag." + tableName);
					sql.append(" WHERE ds=MAX_PT('aliyun_tag." + tableName + "')");
					sql.append(" AND is_deleted = 0");
					sql.append(" AND object_unique_code = '" + objectUniqueCode + "'");
					sql.append(" GROUP BY object_biz_id;");
				}

				Dag dag = new Dag(taskReq.getTaskName());
				OdpsSqlNode dropNode = new OdpsSqlNode("OdpsSql_dropTmp_" + srcDsInfo.getTableName(), dag);
				dropNode.setDbName("mdp_odps");
				dropNode.setOdpsSql("DROP TABLE " + dstTableName + ";");
				dropNode.setDataBaseline(true);
				OdpsSqlNode crasNode = new OdpsSqlNode("OdpsSql_crasTmp_" + srcDsInfo.getTableName(), dag);
				crasNode.setDbName("mdp_odps");
				crasNode.setOdpsSql(sql.toString());
				dropNode.setNext(crasNode);
				HoloExtTblNode extNode = new HoloExtTblNode("HoloExt_" + srcDsInfo.getTableName(), dag);
				extNode.setDstDbName(taskReq.getDstDbName());
				extNode.setSrcDsName(srcDsName);
				crasNode.setNext(extNode);
				FlinkNode flinkNode = new FlinkNode("Flink_" + srcDsInfo.getTableName(), dag);
				String jobName = "incrsync-" + srcDsInfo.getTableName();
				JSONObject srcDsMeta = dsInfoService.getTableMetaByDsName(srcDsInfo.getTenantId(), srcDsInfo.getDsName());
				String objKafkaTopic = srcDsMeta.getJSONObject("incrConf").getString("topicName");
				String objKafkaDbName = srcDsMeta.getJSONObject("incrConf").getString("dbName");

				JSONObject kafkaDbMeta = dsInfoService.getDbMetaByName(objKafkaDbName);

				TenantInfoExample tiExample = new TenantInfoExample();
				tiExample.createCriteria().andTenantIdEqualTo(taskReq.getTenantId());
				List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
				TenantInfo tenantInfo = tenantList.get(0);

				JSONObject dwDbMeta = dsInfoService.getDbMetaByName(tenantInfo.getDefaultDw());

				String flinkSql = "CREATE Temporary TABLE mdp_source\n" +
						"(\n" +
						"    `msg` STRING\n" +
						")\n" +
						"WITH (\n" +
						"    'connector' = 'kafka'\n" +
						"    ,'topic' = '" + objKafkaTopic + "'\n" +
						"    ,'properties.bootstrap.servers' = '" + kafkaDbMeta.getString("bootstrap.servers") + "'\n" +
						"    ,'format' = 'raw'\n" +
						"    ,'scan.startup.mode' = 'timestamp'\n" +
						"    ,'scan.startup.timestamp-millis' = '1706756442000'\n" +
						")\n" +
						";\n" +
						"\n" +
						"CREATE TEMPORARY TABLE beiming_rtdw_sink\n" +
						"(\n" +
						"    __msg__       varchar\n" +
						"    ,__trace_id__ varchar\n" +
						")\n" +
						"WITH (\n" +
						"    'connector' = 'beiming-rtdw'\n" +
						"    ,'jdbcUrl' = '" + dwDbMeta.getString("jdbcUrl") + "'\n" +
						"    ,'username' = '" + dwDbMeta.getString("username") + "'\n" +
						"    ,'password' = '" + dwDbMeta.getString("password") + "'\n" +
						"    ,'table_name' = '" + srcDsInfo.getTableName() + "'\n" +
						"    ,'object_unique_code' = '" + srcDsInfo.getDsUniqueName() + "'\n" +
						"    ,'object_pk' = '" + srcDsInfo.getPkFields() + "'\n" +
						"    ,'distribute_key' = '" + srcDsMeta.getString("distributeKey") + "'\n" +
						")\n" +
						";\n" +
						"\n" +
						"insert into beiming_rtdw_sink\n" +
						"select\n" +
						"    JSON_VALUE(msg, '$.data') as __msg__\n" +
						"    ,JSON_VALUE(msg, '$.traceId') as __trace_id__\n" +
						"from mdp_source\n" +
						";";
				flinkService.createJob(taskReq.getTenantId(), taskReq.getAppName(), jobName, flinkSql, false);
				flinkNode.setJobName(jobName);
				extNode.setNext(flinkNode);

				TaskInfoExample taskExp = new TaskInfoExample();
				taskExp.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(taskReq.getTenantId()).andNameEqualTo(taskReq.getTaskName());
				List<TaskInfo> taskInfos = taskInfoMapper.selectByExample(taskExp);
				if (CollectionUtils.isEmpty(taskInfos)) {
					TaskInfoRequest taskInfo = new TaskInfoRequest();
					taskInfo.setName(taskReq.getTaskName());
					taskInfo.setOperateEmpid(taskReq.getOperateEmpid());
					taskInfo.setPolicy(DagPolicy.ODS.toString());
					taskInfo.setTenantId(taskReq.getTenantId());
					taskInfo.setAppName(taskReq.getAppName());
					Long taskId = taskService.createDAGTask(taskInfo, dag);
					if (taskId == null) {
						throw new QanatBizException("任务创建失败");
					}
					resp.setDataSyncTaskId(taskId);
				} else {
					if (taskService.updateTaskDag(taskInfos.get(0).getId(), taskReq.getOperateEmpid(), dag, false)) {
						resp.setDataSyncTaskId(taskInfos.get(0).getId());
						log.info("任务[{}:{}]更新DAG成功", taskInfos.get(0).getId(), taskInfos.get(0).getName());
					} else {
						throw new QanatBizException("任务:" + taskInfos.get(0).getId() + " 更新DAG失败");
					}
				}
			} else if (objMetaJson != null && "OTS".equalsIgnoreCase(objMetaJson.getString("storeEngine"))) {
				String jobName = "cdc-" + srcDsInfo.getTableName();

				JSONObject otsDbMeta = dsInfoService.getDbMetaByName("mdp-ots");
				//获取OTS tunnelName
				String tunnelName = "cdc_" + srcDsInfo.getTableName() + "_" + new SimpleDateFormat("yyyyMMddHH").format(new Date());
				try {
					CreateTunnelRequest request = new CreateTunnelRequest(srcDsInfo.getTableName(), tunnelName, TunnelType.BaseAndStream);
					TunnelClient tunnelClient = new TunnelClient(otsDbMeta.getString("endpoint"), otsDbMeta.getString("accessId"), otsDbMeta.getString("accessKey"), otsDbMeta.getString("instanceName"));
					CreateTunnelResponse response = tunnelClient.createTunnel(request);
					log.info("Tunnel id:{} name:{}", response.getTunnelId(), tunnelName);
				} catch(Exception e) {
					log.error("createOtsTunnel Failed", e);
				}

				TenantInfoExample tiExample = new TenantInfoExample();
				tiExample.createCriteria().andTenantIdEqualTo(taskReq.getTenantId());
				List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
				TenantInfo tenantInfo = tenantList.get(0);

				JSONObject dwDbMeta = dsInfoService.getDbMetaByName(tenantInfo.getDefaultDw());

				String flinkSql = "CREATE TEMPORARY TABLE ots_source\n" +
						"(\n" +
						"  msg       varchar\n" +
						"  ,trace_id STRING METADATA FROM 'trace_id'\n" +
						")\n" +
						"WITH (\n" +
						"  'connector' = 'qanat-ots'\n" +
						"  ,'endPoint' = '" + otsDbMeta.getString("endpoint") + "'\n" +
						"  ,'instanceName' = '" + otsDbMeta.getString("instanceName") + "'\n" +
						"  ,'tableName' = '" + srcDsInfo.getDsUniqueName() + "'\n" +
						"  ,'tunnelName' = '" + tunnelName + "'\n" +
						"  ,'accessId' = '" + otsDbMeta.getString("accessId") + "'\n" +
						"  ,'accessKey' = '" + otsDbMeta.getString("accessKey") + "'\n" +
						"  ,'ignoreDelete' = 'false'\n" +
						")\n" +
						";\n" +
						"\n" +
						"CREATE TEMPORARY TABLE beiming_rtdw_sink\n" +
						"(\n" +
						"  __msg__       varchar\n" +
						"  ,__trace_id__ varchar\n" +
						")\n" +
						"WITH (\n" +
						"  'connector' = 'beiming-rtdw'\n" +
						"  ,'jdbcUrl' = '" + dwDbMeta.getString("jdbcUrl") + "'\n" +
						"  ,'username' = '" + dwDbMeta.getString("username") + "'\n" +
						"  ,'password' = '" + dwDbMeta.getString("password") + "'\n" +
						"  ,'table_name' = '" + srcDsInfo.getTableName() + "'\n" +
						"  ,'object_unique_code' = '" + srcDsInfo.getDsUniqueName() + "'\n" +
						"  ,'object_pk' = '" + srcDsInfo.getPkFields() + "'\n" +
						")\n" +
						";\n" +
						"\n" +
						"insert into beiming_rtdw_sink\n" +
						"select\n" +
						"  *\n" +
						"from ots_source\n" +
						";";

				Dag dag = new Dag(taskReq.getTaskName());
				FlinkNode flinkNode = new FlinkNode("Flink_" + srcDsInfo.getTableName(), dag);
				flinkNode.setJobName(jobName);

				TaskInfoExample taskExp = new TaskInfoExample();
				taskExp.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(taskReq.getTenantId()).andNameEqualTo(taskReq.getTaskName());
				List<TaskInfo> taskInfos = taskInfoMapper.selectByExample(taskExp);
				if (CollectionUtils.isEmpty(taskInfos)) {
					TaskInfoRequest taskInfo = new TaskInfoRequest();
					taskInfo.setName(taskReq.getTaskName());
					taskInfo.setOperateEmpid(taskReq.getOperateEmpid());
					taskInfo.setPolicy(DagPolicy.ODS.toString());
					taskInfo.setTenantId(taskReq.getTenantId());
					taskInfo.setAppName(taskReq.getAppName());
					Long taskId = taskService.createDAGTask(taskInfo, dag);
					if (taskId == null) {
						throw new QanatBizException("任务创建失败");
					}
					resp.setDataSyncTaskId(taskId);
				} else {
					if (taskService.updateTaskDag(taskInfos.get(0).getId(), taskReq.getOperateEmpid(), dag, false)) {
						resp.setDataSyncTaskId(taskInfos.get(0).getId());
						log.info("任务[{}:{}]更新DAG成功", taskInfos.get(0).getId(), taskInfos.get(0).getName());
					} else {
						throw new QanatBizException("任务:" + taskInfos.get(0).getId() + " 更新DAG失败");
					}
				}
			}

        } else {
            dealWithDsRelations(taskReq, srcDsName, dstDsNames, dstIncrTopic);
            
            //获取任务执行需要的consumerId
            Map<String, ConsumerInfo> consumerMap = null;
            if (taskReq.getEnableIncrSync() != null && taskReq.getEnableIncrSync()) {
	            consumerMap = getConsumerMapForOdsSync(taskReq, srcDsMetaJson);
            }
                
            //处理Blink Sync任务
            String jobName = null;
            if (taskReq.getEnableIncrSync() != null && taskReq.getEnableIncrSync()) {
                Map<String, String> jobNameMap = new HashMap<>();
                jobName = "incrsync_" + getAppIdByName(taskReq.getTenantId(), taskReq.getAppName()) + "_" + taskReq.getTableName();
                jobNameMap.put("jobName", jobName);
            	JSONObject dsMetaJson = dsInfoService.getTableMetaByDsName(taskReq.getTenantId(), srcDsMetaJson.getString("dsName"));
                processBlinkSyncJob(jobNameMap, taskReq, consumerMap.get("incrSync"), kafkaJson, taskReq.getDstDbNames(), taskReq.getEtlDbName(), dsMetaJson);
                
                //新增数据同步DAG任务
                try {
	                Long taskId = processSyncDAGTask(taskReq, srcDsName, dstDsNames, taskReq.getEtlDbName(), jobName);
	                if (taskId == null) {
	                    throw new QanatBizException("任务创建失败");
	                }
	                resp.setDataSyncTaskId(taskId);
                } catch (QanatBizException e) {
                    log.error("processSyncDAGTask failed, error={}", e.getMessage());
                	TaskInfoExample example = new TaskInfoExample();
                    example.createCriteria().andNameEqualTo(taskReq.getTaskName()).andTenantIdEqualTo(taskReq.getTenantId()).andIsDeletedEqualTo(0L);
                    List<TaskInfo> taskList = taskInfoMapper.selectByExample(example);
                    if (CollectionUtils.isNotEmpty(taskList)) {
                        throw new QanatBizException("501", "DAG任务:" + taskList.get(0).getId() + " 已存在", taskList.get(0).getId());
                    } else {
                    	throw new QanatBizException("DAG任务创建失败："  + e.getCode() + e.getMessage());
                    }
                }
            }
            if (jobName == null) {
            	//新增数据同步DAG任务
                Long taskId = processFullSyncOnlyDAGTask(taskReq, srcDsName, dstDsNames);
                if (taskId == null) {
                    throw new QanatBizException("任务创建失败");
                }
                resp.setDataSyncTaskId(taskId);
            }
            
            //处理blink check任务
            if (taskReq.getEnableCheck() != null && taskReq.getEnableCheck()) {
                Long checkAllTaskId = processCheckAllDAGTask(taskReq);
                if (checkAllTaskId == null) {
                    throw new QanatBizException("任务创建失败");
                }
                resp.setCheckAllTaskId(checkAllTaskId);
            }
            
        }
        return resp;
    }

	private void dealWithDsRelations(OdsSyncTaskRequest taskReq, String srcDsName, List<String> dstDsNames,
			String dstIncrTopic) {

        for (String dstDsName : dstDsNames) {
			try {
				//先删后增, ods类型dsRel唯一
				DsRelationExample dsRelExp = new DsRelationExample();
				dsRelExp.createCriteria().andTenantIdEqualTo(taskReq.getTenantId())
										.andIsDeletedEqualTo(0L)
										.andRelationTypeEqualTo("ods")
										.andDstDsNameEqualTo(dstDsName);
				int delCnt = dsRelationMapper.deleteByExample(dsRelExp);
				log.info("delete [{}] exists [{}] dsRelations for dstDsName[{}]", delCnt, "ods", dstDsName);
				
				DsRelation newDsRel = new DsRelation();
				newDsRel.setCreateEmpid(taskReq.getOperateEmpid());
				newDsRel.setDstDsName(dstDsName);
				newDsRel.setGmtCreate(new Date());
				newDsRel.setGmtModified(new Date());
				newDsRel.setIsDeleted(0L);
				newDsRel.setModifyEmpid(taskReq.getOperateEmpid());
				newDsRel.setRelationType("ods");
				newDsRel.setSrcDsName(srcDsName);
				newDsRel.setTenantId(taskReq.getTenantId());
				int insCnt = dsRelationMapper.insert(newDsRel);
				log.info("insert [{}] dsRelations[{},{},{}]", insCnt, srcDsName, dstDsName, "ods");
	
	        	JSONObject appKafkaConf = kafkaManagementService.getKafkaConfByAppName(taskReq.getTenantId(), taskReq.getAppName());
				Datasource drcDs = new Datasource();
				drcDs.setDsName("kafka_" + dstIncrTopic);
				drcDs.setDsType("kafka");
				drcDs.setTableName(dstIncrTopic);
				drcDs.setIsDeleted(0L);
				drcDs.setDbName(appKafkaConf.getString("dbName"));
				drcDs.setGmtModified(new Date());
				drcDs.setModifyEmpid(taskReq.getOperateEmpid());
				drcDs.setGmtCreate(new Date());
				drcDs.setCreateEmpid(taskReq.getOperateEmpid());
				drcDs.setTenantId(taskReq.getTenantId());
				try {
					datasourceMapper.insert(drcDs);
				} catch(Exception e) {
					log.error("insert kafka dsInfo[{}] failed, error={}", drcDs.getDsName(), e.getMessage());
				}
				
				dsRelExp = new DsRelationExample();
				dsRelExp.createCriteria().andTenantIdEqualTo(taskReq.getTenantId())
										.andIsDeletedEqualTo(0L)
										.andRelationTypeEqualTo("post_ods")
										.andDstDsNameEqualTo(dstDsName);
				delCnt = dsRelationMapper.deleteByExample(dsRelExp);
				log.info("delete [{}] exists [{}] dsRelations for dstDsName[{}]", delCnt, "post_ods", dstDsName);
				
				newDsRel = new DsRelation();
				newDsRel.setCreateEmpid(taskReq.getOperateEmpid());
				newDsRel.setDstDsName(dstDsName);
				newDsRel.setGmtCreate(new Date());
				newDsRel.setGmtModified(new Date());
				newDsRel.setIsDeleted(0L);
				newDsRel.setModifyEmpid(taskReq.getOperateEmpid());
				newDsRel.setRelationType("post_ods");
				newDsRel.setSrcDsName("kafka_" + dstIncrTopic);
				newDsRel.setTenantId(taskReq.getTenantId());
				insCnt = dsRelationMapper.insert(newDsRel);
				log.info("insert [{}] dsRelations[{},{},{}]", insCnt, "kafka_" + dstIncrTopic, dstDsName, "post_ods");
				
				dsRelExp = new DsRelationExample();
				dsRelExp.createCriteria().andTenantIdEqualTo(taskReq.getTenantId())
										.andIsDeletedEqualTo(0L)
										.andRelationTypeEqualTo("incr")
										.andDstDsNameEqualTo(srcDsName);
				List<DsRelation> dsRels = dsRelationMapper.selectByExample(dsRelExp);
				if (CollectionUtils.isNotEmpty(dsRels)) {
					dsRelExp = new DsRelationExample();
				    dsRelExp.createCriteria().andTenantIdEqualTo(taskReq.getTenantId())
				    						.andIsDeletedEqualTo(0L)
				    						.andRelationTypeEqualTo("pre_ods")
				    						.andDstDsNameEqualTo(dstDsName);
				    delCnt = dsRelationMapper.deleteByExample(dsRelExp);
				    log.info("delete [{}] exists dsRelations[{},{},{}]", delCnt, dstDsName, "pre_ods");
				    
				    newDsRel = new DsRelation();
				    newDsRel.setCreateEmpid(taskReq.getOperateEmpid());
				    newDsRel.setDstDsName(dstDsName);
				    newDsRel.setGmtCreate(new Date());
				    newDsRel.setGmtModified(new Date());
				    newDsRel.setIsDeleted(0L);
				    newDsRel.setModifyEmpid(taskReq.getOperateEmpid());
				    newDsRel.setRelationType("pre_ods");
				    newDsRel.setSrcDsName(dsRels.get(0).getSrcDsName());
				    newDsRel.setTenantId(taskReq.getTenantId());
				    insCnt = dsRelationMapper.insert(newDsRel);
				    log.info("insert [{}] dsRelations[{},{},{}]", insCnt, dsRels.get(0).getSrcDsName(), dstDsName, "pre_ods");
				} else {
					log.error("not find [{}] dsRelations for dstDsName[{}]", "incr", srcDsName);
				}
			} catch(Exception e) {
				log.error("dsRelation failed, error={}", e.getMessage(), e);
			}
        }
	}

    private DbInfo getDbInfoByName(String tenantId, String dbName) {
        DbInfoExample dbExample = new DbInfoExample();
        dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName).andTenantIdEqualTo(tenantId);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("db not found");
        }
        DbInfo dbInfo = dbs.get(0);
        return dbInfo;
    }

    private Map<String, ConsumerInfo> getConsumerMapForOdsSync(OdsSyncTaskRequest taskReq, JSONObject srcDsMetaJson) {
        String tableName = taskReq.getTableName();
		Long appId = getAppIdByName(taskReq.getTenantId(), taskReq.getAppName());
        String srcIncrTopic = srcDsMetaJson.getJSONObject("incrConf").getString("topicName");
        String dstIncrTopic = "stream-" + appId + "-ods-" + taskReq.getTableName();
        int parallel = viewModelOptimizer.getParallel(taskReq.getTenantId(), taskReq.getDatatubeLevel(), taskReq.getPredictQph());
    	boolean res = kafkaManagementService.createTopic(taskReq.getTenantId(), taskReq.getAppName(), dstIncrTopic, parallel);
		if (!res) {
			log.error("topic:{} create is failed", dstIncrTopic);
		}
        
        Map<String, ConsumerInfo> consumerMap = new HashMap<>();

        if (taskReq.getEnableIncrSync() != null && taskReq.getEnableIncrSync()) {
        	JSONObject dsMetaJson = dsInfoService.getTableMetaByDsName(taskReq.getTenantId(), srcDsMetaJson.getString("dsName"));
        	
        	String incrSyncConsumerId = "GID-" + appId + "-ods_incrsync-" + tableName;
        	res = kafkaManagementService.createConsumerGroupFromDbInfo(taskReq.getTenantId(), dsMetaJson.getJSONObject("incrConf").getString("dbName"), incrSyncConsumerId);
    		if (!res) {
    			log.error("consumer:{} create is failed", incrSyncConsumerId);
    		}
            ConsumerInfo incrSyncConsumer = new ConsumerInfo(srcIncrTopic, incrSyncConsumerId);
            consumerMap.put("incrSync", incrSyncConsumer);
        }

        if (taskReq.getEnableFullLink() != null && taskReq.getEnableFullLink()) {
        	String fullLinkSrcDrcConsumerId = "GID-" + appId + "-ods_linksrc-" + tableName;
        	res = kafkaManagementService.createConsumerGroup(taskReq.getTenantId(), taskReq.getAppName(), fullLinkSrcDrcConsumerId);
    		if (!res) {
    			log.error("consumer:{} create is failed", fullLinkSrcDrcConsumerId);
    		}
            ConsumerInfo fullLinkSrcDrcConsumer = new ConsumerInfo(srcIncrTopic, fullLinkSrcDrcConsumerId);
            consumerMap.put("fullLinkSrcDrc", fullLinkSrcDrcConsumer);
    
        	String fullLinkDstDrcConsumerId = "GID-" + appId + "-ods_linkdst-" + tableName;
        	res = kafkaManagementService.createConsumerGroup(taskReq.getTenantId(), taskReq.getAppName(), fullLinkDstDrcConsumerId);
    		if (!res) {
    			log.error("consumer:{} create is failed", fullLinkSrcDrcConsumerId);
    		}
            ConsumerInfo fullLinkDstDrcConsumer = new ConsumerInfo(dstIncrTopic, fullLinkDstDrcConsumerId);
            consumerMap.put("fullLinkDstDrc", fullLinkDstDrcConsumer);
        }

        if (taskReq.getEnableCheck() != null && taskReq.getEnableCheck()) {
        	String checkSrcDrcConsumerId = "GID-" + appId + "-ods_checksrc-" + tableName;
        	res = kafkaManagementService.createConsumerGroup(taskReq.getTenantId(), taskReq.getAppName(), checkSrcDrcConsumerId);
    		if (!res) {
    			log.error("consumer:{} create is failed", checkSrcDrcConsumerId);
    		}
            ConsumerInfo checkSrcDrcConsumer = new ConsumerInfo(srcIncrTopic, checkSrcDrcConsumerId);
            consumerMap.put("incrCheck", checkSrcDrcConsumer);

        	String correctTopicName = "crt-" + appId + "-" + taskReq.getTableName();
        	String correctConsumerId = "GID-" + appId + "-ods_correct-" + tableName;
        	res = kafkaManagementService.createConsumerGroup(taskReq.getTenantId(), taskReq.getAppName(), correctConsumerId);
    		if (!res) {
    			log.error("consumer:{} create is failed", checkSrcDrcConsumerId);
    		}
            ConsumerInfo correctConsumer = new ConsumerInfo(correctTopicName, correctConsumerId);
            consumerMap.put("correct", correctConsumer);
        }
        
        return consumerMap;
    }

    private Long processSyncDAGTask(OdsSyncTaskRequest taskReq, String srcDsName, List<String> dstDsNames, String mainDbName, String jobName) {
        Dag dag = new Dag(taskReq.getTaskName());
        if (StringUtils.isNotBlank(taskReq.getTimeExpression())) {
        	dag.setTimeExpression(taskReq.getTimeExpression());
        }
        BlinkStreamNode incrSync = new BlinkStreamNode("BlinkStream_" + jobName, dag);
        incrSync.setJobName(jobName);
        
        List<DataXNode> nodes = new ArrayList<>();
        for (int i = 0; i < dstDsNames.size(); i++) {
            DataXNode fullSync = new DataXNode("DataX_" + taskReq.getTableName() + "_" + dstDsNames.get(i), dag);
            fullSync.setSrcDsName(srcDsName);
            fullSync.setDstDsName(dstDsNames.get(i));
            if (i == 0) {
            	fullSync.setDataBaseline(true);
            }
            fullSync.setParallism(taskReq.getFullSyncParallelism() == null ? 10 : Integer.parseInt(taskReq.getFullSyncParallelism()));
            fullSync.setBatchSize(taskReq.getFullSyncBatchSize() == null ? 20480 : taskReq.getFullSyncBatchSize());
            if (StringUtils.isNotBlank(taskReq.getFilter())) {
            	fullSync.setWhere(taskReq.getFilter());
            }
            nodes.add(fullSync);
            if (i > 0) {
            	nodes.get(i - 1).setNext(fullSync);
            }
            if (i == (dstDsNames.size() - 1)) {
                fullSync.setNext(incrSync);
            }
        }
        
        TaskInfoExample taskExp = new TaskInfoExample();
        taskExp.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(taskReq.getTenantId()).andNameEqualTo(taskReq.getTaskName());
        List<TaskInfo> taskInfos = taskInfoMapper.selectByExample(taskExp);
	    if (CollectionUtils.isEmpty(taskInfos)) {
	        TaskInfoRequest taskInfo = new TaskInfoRequest();
	        taskInfo.setName(taskReq.getTaskName());
	        taskInfo.setOperateEmpid(taskReq.getOperateEmpid());
	        taskInfo.setTaskDesc(taskReq.getTaskDesc());
	        taskInfo.setPolicy(DagPolicy.ODS.toString());
	        taskInfo.setTenantId(taskReq.getTenantId());
	        taskInfo.setAppName(taskReq.getAppName());
	        Long taskId = taskService.createDAGTask(taskInfo, dag);
	        log.info("[{}]create qanat task[{}] finished", taskReq.getRequestId(), taskId);
	        return taskId;
	    } else {
	    	if (taskService.updateTaskDag(taskInfos.get(0).getId(), taskReq.getOperateEmpid(), dag, false)) {
	    		log.info("任务[{}:{}]更新DAG成功", taskInfos.get(0).getId(), taskInfos.get(0).getName());
	    		return taskInfos.get(0).getId();
	    	} else {
                throw new QanatBizException("任务:" + taskInfos.get(0).getId() + " 更新DAG失败");
	    	}
	    }
    }

    private Long processFullSyncOnlyDAGTask(OdsSyncTaskRequest taskReq, String srcDsName, List<String> dstDsNames) {
    	Dag dag = new Dag(taskReq.getTaskName());
        if (StringUtils.isNotBlank(taskReq.getTimeExpression())) {
        	dag.setTimeExpression(taskReq.getTimeExpression());
        }
        List<DataXNode> nodes = new ArrayList<>();
        for (int i = 0; i < dstDsNames.size(); i++) {
            DataXNode fullSync = new DataXNode("DataX_" + taskReq.getTableName() + "_" + dstDsNames.get(i), dag);
            fullSync.setSrcDsName(srcDsName);
            fullSync.setDstDsName(dstDsNames.get(i));
            fullSync.setParallism(taskReq.getFullSyncParallelism() == null ? 10 : Integer.parseInt(taskReq.getFullSyncParallelism()));
            fullSync.setBatchSize(taskReq.getFullSyncBatchSize() == null ? 20480 : taskReq.getFullSyncBatchSize());
            if (StringUtils.isNotBlank(taskReq.getFilter())) {
            	fullSync.setWhere(taskReq.getFilter());
            }
            nodes.add(fullSync);
            if (i > 0) {
            	nodes.get(i - 1).setNext(fullSync);
            }
        }
        
        TaskInfoExample taskExp = new TaskInfoExample();
        taskExp.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(taskReq.getTenantId()).andNameEqualTo(taskReq.getTaskName());
        List<TaskInfo> taskInfos = taskInfoMapper.selectByExample(taskExp);
	    if (CollectionUtils.isEmpty(taskInfos)) {
	        TaskInfoRequest taskInfo = new TaskInfoRequest();
	        taskInfo.setName(taskReq.getTaskName());
	        taskInfo.setOperateEmpid(taskReq.getOperateEmpid());
	        taskInfo.setTaskDesc(taskReq.getTaskDesc());
	        taskInfo.setPolicy(DagPolicy.ODS.toString());
	        taskInfo.setTenantId(taskReq.getTenantId());
	        taskInfo.setAppName(taskReq.getAppName());
	        Long taskId = taskService.createDAGTask(taskInfo, dag);
	        log.info("[{}]create qanat task[{}] finished", taskReq.getRequestId(), taskId);
	        return taskId;
	    } else {
	    	if (taskService.updateTaskDag(taskInfos.get(0).getId(), taskReq.getOperateEmpid(), dag, false)) {
	    		log.info("任务[{}:{}]更新DAG成功", taskInfos.get(0).getId(), taskInfos.get(0).getName());
	    		return taskInfos.get(0).getId();
	    	} else {
                throw new QanatBizException("任务:" + taskInfos.get(0).getId() + " 更新DAG失败");
	    	}
	    }
    }

    private Long processCheckAllDAGTask(OdsSyncTaskRequest taskReq) {
        //先判断该表是否有odps数据源，没有则生成一个
        String jobName = "checkall_" + getAppIdByName(taskReq.getTenantId(), taskReq.getAppName()) + "_" + taskReq.getTableName();
        String dagScript = String.format(
            Adb3SyncTemplate.CHECK_ALL_DAG_SCRIPT
            , taskReq.getTableName()
            , jobName
            , jobName);
        TaskInfoRequest taskInfo = new TaskInfoRequest();
        taskInfo.setDagScript(dagScript);
        taskInfo.setName("DAG_CheckAll_" + taskReq.getTableName() + System.currentTimeMillis());
        taskInfo.setOperateEmpid(taskReq.getOperateEmpid());
        taskInfo.setTaskDesc("DAG_CheckAll_" + taskReq.getTableName());
        taskInfo.setPolicy(DagPolicy.ODS.toString());
        taskInfo.setTenantId(taskReq.getTenantId());
        taskInfo.setAppName(taskReq.getAppName());
        Long taskId = taskService.createDAGTask(taskInfo);
        log.info("[{}]create qanat task[{}] finished", taskReq.getRequestId(), taskId);
        return taskId;
    }

    private Long getAppIdByName(String tenantId, String appName) {
    	AppInfoExample example = new AppInfoExample();
    	example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
    	List<AppInfo> apps = appInfoMapper.selectByExample(example);
    	return apps.get(0).getId();
    }

    private void processBlinkSyncJob(Map<String, String>  jobNameMap, OdsSyncTaskRequest taskReq, ConsumerInfo consumerInfo, JSONObject kafkaJson, List<String> dstDbNames, String etlDbName, JSONObject dsMetaJson) {
        String jobName = jobNameMap.get("jobName");
        String eventTopicName = "stream-" + getAppIdByName(taskReq.getTenantId(), taskReq.getAppName()) + "-ods-" + taskReq.getTableName();
    	boolean res = kafkaManagementService.createTopic(taskReq.getTenantId(), taskReq.getAppName(), eventTopicName);
		if (!res) {
			log.error("topic:{} create is failed", eventTopicName);
		}
		DsFieldInfoExample example = new DsFieldInfoExample();
		example.createCriteria().andDsNameEqualTo(taskReq.getSrcDsName()).andIsDeletedEqualTo(0L);
        List<DsFieldInfo> dsFields = dsFieldInfoMapper.selectByExample(example);
        List<String> cols = null;
        if (CollectionUtils.isNotEmpty(dsFields)) {
        	cols = dsFields.stream().map(e->e.getFieldName()).collect(Collectors.toList());
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String sql = "--SQL\r\n" + 
                "--********************************************************************--\r\n" + 
                "--Author: " + taskReq.getOperateEmpid() + "\r\n" + 
                "--CreateTime: " + sdf.format(new Date()) + "\r\n" + 
                "--Comment: " + taskReq.getTaskDesc() + "\r\n" + 
                "--********************************************************************--\r\n" + 
    			"create table mq_source (\n" + 
    			"    msg varchar,\n" + 
    			"    __traceId__ varchar header\n" + 
    			") with (\n" + 
    			"  type = 'custom',\n" + 
    			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
    			"  topic = '" + consumerInfo.getTopicName() + "',\n" + 
    			"  `group.id` = '" + consumerInfo.getConsumerId() + "',\n" + 
    			"  `dbName` = '" + dsMetaJson.getJSONObject("incrConf").getString("dbName")+ "',\n" +
    			"  startupMode = 'TIMESTAMP',\n" +
    			"  fieldDelimiter = '`'\n" +
    			");\n" + 
                "\r\n";
		for (int i = 0; i < dstDbNames.size(); i++) {
            sql += "create table adb3_sink_" + i + " (\r\n" + 
                "    msg varchar,\r\n" + 
                "    trace_id varchar\r\n" +
                ") with (\r\n" + 
                "    type='custom',\r\n" + 
                "    class = 'com.aliyun.wormhole.qanat.blink.sink.QanatAdb3Sink',\r\n" + 
                "    dbName='" + dstDbNames.get(i) + "',\r\n" + 
                "    tableName='" + taskReq.getTableName() + "',\r\n" + 
                "    " + ((CollectionUtils.isNotEmpty(cols) ? ("cols='" + StringUtils.join(cols, ",") + "',") : "")) + "\n" ;
            if (dstDbNames.get(i).equalsIgnoreCase(etlDbName)) {
    			sql += "    streamType = 'kafka',\n" + 
    			"    eventTopic = '" + eventTopicName + "',\n" + 
    			"    eventServer = '" + kafkaJson.getString("dbName") + "'\n";
            } else {
    			sql += "    streamEvent = 'disable'\n";
            }
            sql += ");\r\n" + 
                "\r\n" + 
                "insert into adb3_sink_" + i + "\r\n" + 
                "select * from mq_source;\n" +
                "\n";
		}
    
        blinkService.buildBlinkJob(taskReq.getTenantId(), taskReq.getAppName(), jobName, sql, 
        		"/" + taskReq.getAppName() + "/" + taskReq.getTableName() + "/", 
        		blinkService.getBlinkExtensionsByPackage(taskReq.getTenantId(), ResourcePackage.BLINK_CUSTOM_SINK, ResourcePackage.BLINK_KAFKA010), 
        		false,
        		null);
    }

    private JSONObject getDsMeta(String tenantId, String dsName) {
        JSONObject srcDsMetaJson = null;
        DatasourceExample example = new DatasourceExample();
        example.createCriteria().andDsNameEqualTo(dsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
        List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(dsList)) {
            throw new QanatBizException("Datasource:" + dsName + " doesn't exists!");
        }
        Datasource ds = dsList.get(0);
        srcDsMetaJson = JSON.parseObject(ds.getMeta());
		srcDsMetaJson = srcDsMetaJson == null ? new JSONObject() : srcDsMetaJson;
        srcDsMetaJson.put("dsType", ds.getDsType());
        srcDsMetaJson.put("table", ds.getTableName());
        srcDsMetaJson.put("dbName", ds.getDbName());
        srcDsMetaJson.put("dsId", ds.getId());
        srcDsMetaJson.put("dsName", dsName);
        
        DsRelationExample drExp = new DsRelationExample();
        drExp.createCriteria().andDstDsNameEqualTo(dsName).andIsDeletedEqualTo(0L).andRelationTypeEqualTo("incr").andTenantIdEqualTo(tenantId);
        List<DsRelation> dsRelations = dsRelationMapper.selectByExample(drExp);
        
        if (CollectionUtils.isNotEmpty(dsRelations)) {
	        String incrDsName = dsRelations.get(0).getSrcDsName();
	        example = new DatasourceExample();
	        example.createCriteria().andDsNameEqualTo(incrDsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
	        dsList = datasourceMapper.selectByExampleWithBLOBs(example);
	        JSONObject confJson = new JSONObject();
	        srcDsMetaJson.put("incrConf", confJson);
	        confJson.put("topicName", dsList.get(0).getTableName());
        }
        
        DbInfo dbInfo = getDbInfoByName(tenantId, ds.getDbName());
        JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
        srcDsMetaJson.put("jdbcUrl", dbMetaJson.getString("jdbcUrl"));
        srcDsMetaJson.put("username", dbMetaJson.getString("username"));
        srcDsMetaJson.put("password", dbMetaJson.getString("password"));
       
        String pk = "id";
        String ddlSql = srcDsMetaJson.getString("create_ddl");
        if (StringUtils.isNotBlank(ddlSql)) {
            ddlSql = ddlSql.replaceAll("`", "");
            DbType dbType = null;
            if("mysql".equalsIgnoreCase(ds.getDsType())
            		|| "tddl".equalsIgnoreCase(ds.getDsType())) {
            	dbType = DbType.mysql;
            } else if ("odps".equalsIgnoreCase(ds.getDsType())) {
            	dbType = DbType.odps;
            }
            
            List<SQLStatement> stmtList = SQLUtils.parseStatements(ddlSql, dbType);
            SQLStatement stmt = stmtList.get(0);
            SchemaStatVisitor statVisitor = SQLUtils.createSchemaStatVisitor(dbType);
            stmt.accept(statVisitor);
            List<String> pkList = statVisitor.getColumns().stream().filter(column -> column.isPrimaryKey()).map(Column::getName).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(pkList)) {
            	pk = pkList.get(0);
            }
        }
        srcDsMetaJson.put("pk", pk);
        return srcDsMetaJson;
    }

    private JSONObject getDsMetaByDbAndTableName(String tenantId, String tableName, String dsName, String operateEmpid, DbInfo dbInfo, String appName, String dstIncrTopic, JSONObject srcDsMetaJson) {
        JSONObject dstDsMetaJson = null;
        dstDsMetaJson = JSON.parseObject(dbInfo.getMeta());
        dstDsMetaJson.put("table", tableName);
        dstDsMetaJson.put("dsType", dbInfo.getDbType());
        dstDsMetaJson.put("dbName", dbInfo.getDbName());
        
        DatasourceExample example = new DatasourceExample();
        example.createCriteria().andDsNameEqualTo(dsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
        List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
        Datasource dsInfo = null;
        if (CollectionUtils.isEmpty(dsList)) {
            DatasourceRequest dsReq = new DatasourceRequest();
            dsReq.setDsName(dsName);
            dsReq.setDsDesc(dsName);
            dsReq.setDsType(dbInfo.getDbType());
            JSONObject dsMetaJson = new JSONObject();
            dsReq.setMeta(dsMetaJson.toJSONString());
            dsReq.setTableName(tableName);
            dsReq.setOperateEmpid(operateEmpid);
            dsReq.setRemark(dsName);
            dsReq.setTenantId(tenantId);
            dsReq.setDbName(dbInfo.getDbName());
            if ("odps".equalsIgnoreCase(srcDsMetaJson.getString("dsType"))) {
            	dsReq.setSysType("metric");
            }
            DataResult<Long> result = dsInfoService.createDatasource(dsReq);
            log.info("[{}]create Ds[{}] finished", tenantId, dsName);
            
            dsInfo = datasourceMapper.selectByPrimaryKey(result.getData());
        } else {
        	log.info("find existed dsInfo[{}]", dsName);
            dsInfo = dsList.get(0);
            JSONObject metaJson = JSON.parseObject(dsInfo.getMeta());
            if (metaJson == null) {
            	metaJson = new JSONObject();
            }
            JSONObject incrConfJson = new JSONObject();
            metaJson.put("incrConf", incrConfJson);
            incrConfJson.put("topicName", dstIncrTopic);
            incrConfJson.put("type", "kafka");
            DatasourceRequest request = new DatasourceRequest();
            request.setTenantId(tenantId);
            request.setDsName(dsName);
            request.setOperateEmpid(operateEmpid);
            request.setMeta(JSON.toJSONString(metaJson));
            dsInfoService.modifyDatasource(request);
        }
        
        String pk = "id";
        JSONObject dsMetaJson = JSON.parseObject(dsInfo.getMeta());
        dstDsMetaJson.putAll(dsMetaJson);
        if (StringUtils.isNotBlank(srcDsMetaJson.getString("pk"))) {
        	pk = srcDsMetaJson.getString("pk");
        } else {
	        String ddlSql = dsMetaJson.getString("create_ddl");
	        if (StringUtils.isNotBlank(ddlSql)) {
	            ddlSql = ddlSql.replaceAll("`", "");
	            DbType dbType = null;
	            if("mysql".equalsIgnoreCase(dsInfo.getDsType())) {
	            	dbType = DbType.mysql;
	            } else if ("odps".equalsIgnoreCase(dsInfo.getDsType())) {
	            	dbType = DbType.odps;
	            }
	            List<SQLStatement> stmtList = SQLUtils.parseStatements(ddlSql, dbType);
	            SQLStatement stmt = stmtList.get(0);
	            SchemaStatVisitor statVisitor = SQLUtils.createSchemaStatVisitor(dbType);
	            stmt.accept(statVisitor);
	            List<String> pkList = statVisitor.getColumns().stream().filter(column -> column.isPrimaryKey()).map(Column::getName).collect(Collectors.toList());
	            if (CollectionUtils.isNotEmpty(pkList)) {
	            	pk = pkList.get(0);
	            }
	        }
        }
        dstDsMetaJson.put("pk", pk);
        return dstDsMetaJson;
    }
    
    public String generateMultiDbSinkBatchTask(String tenantId, String appName, String srcDsName, List<String> dstDbNames, String dstTableName, int batchSize, int parallism, String multiDbSyncFilter, String pkColumn) {
    	String jobName = "fullsync_" + this.getAppIdByName(tenantId, appName) + "_" + srcDsName;
    	
    	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, srcDsName);
    	if (jobName.length() > 64) {
    		jobName = "fullsync_" + this.getAppIdByName(tenantId, appName) + "_" + srcDsMetaJson.getLong("dsId");
    	}
    	
    	//刷新字段元数据
    	DatasourceRequest request = new DatasourceRequest();
    	request.setDsName(srcDsName);
    	request.setOperateEmpid("schedulerx");
    	request.setTenantId(tenantId);
    	dsInfoService.modifyDatasource(request);
    	
    	DsFieldInfoExample example = new DsFieldInfoExample();
    	example.createCriteria().andTenantIdEqualTo(tenantId).andDsNameEqualTo(srcDsName).andIsDeletedEqualTo(0L);
    	List<DsFieldInfo> dsFields = dsFieldInfoMapper.selectByExample(example);
    	List<String> columnDefines = new ArrayList<>();
//    	String pkColumn = "id";
    	for (DsFieldInfo dsField : dsFields) {
    		columnDefines.add("`" + dsField.getFieldName() + "` " + getBlinkType(dsField.getFieldType()));
//    		if (dsField.getIsPk().intValue() == 1) {
//    			pkColumn = dsField.getFieldName();
//    		}
    	}
    	String sql = "";
    	for (int i = 0; i < dstDbNames.size(); i++) {
    		sql += "create table adb_sink_" + i + " (\n" + 
    			"    " + StringUtils.join(columnDefines, ",") + ",\n" + 
    			"    primary key(" + pkColumn + ")\n" + 
    			") with (\n" + 
    			"    type = 'QANAT_ADB30',\n" + 
    			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
    			"    dbName='" + dstDbNames.get(i) + "',\n" + 
    			"    tableName='" + dstTableName + "',\n" + 
    			"    replaceMode = 'replace',\n" + 
    			"    streamEvent = 'disable'\n" +
    			");\n" + 
    			"\n";
    	}
    	if (parallism == 1) {
    		sql += "CREATE TABLE adb_source (\n" + 
    			"    " + StringUtils.join(columnDefines, ",") + ",\n" + 
    			"    primary key(" + pkColumn + ")\n" + 
    			") WITH (\n" + 
    			"    type = 'custom',\n" + 
    			"    tableFactoryClass = 'com.aliyun.wormhole.qanat.blink.mysql.scan.MySQLScanTableFactory',\r\n" + 
    			"    dbName='" + srcDsMetaJson.getString("dbName") + "',\r\n" + 
    			"    tableName='" + srcDsMetaJson.getString("table") + "',\r\n" + 
    			"    batchSize='" + batchSize + "'" + 
    			");\n" + 
    			"\n";
    		for (int i = 0; i < dstDbNames.size(); i++) {
    			sql += "insert into adb_sink_" + i + "\n" + 
    			"select * from adb_source;\n" +
    			"\n";
    		}
    	} else {
    		for (int i = 0; i < parallism; i++) {
        		sql += "CREATE TABLE adb_source_" + i + " (\n" + 
            			"    " + StringUtils.join(columnDefines, ",") + ",\n" + 
            			"    primary key(" + pkColumn + ")\n" + 
            			") WITH (\n" + 
            			"    type = 'custom',\n" + 
            			"    tableFactoryClass = 'com.aliyun.wormhole.qanat.blink.mysql.scan.MySQLScanTableFactory',\r\n" + 
            			"    dbName='" + srcDsMetaJson.getString("dbName") + "',\r\n" + 
            			"    tableName='" + srcDsMetaJson.getString("table") + "',\r\n" + 
            			"    whereClause='" + (StringUtils.isNotBlank(multiDbSyncFilter) ? (multiDbSyncFilter + " AND ") : "") + pkColumn + "%" + parallism + "=" + i + "',\r\n" +
            			"    batchSize='" + batchSize + "'" + 
            			");\n" + 
            			"\n";

        		for (int j = 0; j < dstDbNames.size(); j++) {
        			sql += "insert into adb_sink_" + j + " select * from adb_source_" + i + ";\n" +
            			"\n";
        		}
    		}
    	}
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName + "/" + srcDsMetaJson.getString("table") + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_MYSQL_SCAN), true,
        		blinkService.getBatchPlanJson4DwSource(srcDsMetaJson.getString("table"), pkColumn, parallism, dstDbNames.size()));
    	
    	return jobName;
    }
    
    public String generateBatchTask(String tenantId, String appName, String srcDsName, String dstDbName, String dstTableName, int batchSize, int parallism, String multiDbSyncFilter) {
    	String jobName = "fullsync_" + this.getAppIdByName(tenantId, appName) + "_" + this.dsInfoService.getDbIdByName(tenantId, dstDbName) + "_" + dstTableName;
    	
    	DatasourceRequest request = new DatasourceRequest();
    	request.setTenantId(tenantId);
    	request.setDsName(srcDsName);
    	request.setOperateEmpid("schedulerx");
    	dsInfoService.modifyDatasource(request);
    	
    	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, srcDsName);
    	if (jobName.length() > 64) {
    		jobName = "fullsync_" + this.getAppIdByName(tenantId, appName) + "_" + this.dsInfoService.getDbIdByName(tenantId, dstDbName) + "_" + srcDsMetaJson.getLong("dsId");
    	}
    	
    	DsFieldInfoExample example = new DsFieldInfoExample();
    	example.createCriteria().andTenantIdEqualTo(tenantId).andDsNameEqualTo(srcDsName).andIsDeletedEqualTo(0L);
    	List<DsFieldInfo> dsFields = dsFieldInfoMapper.selectByExample(example);
    	List<String> columnDefines = new ArrayList<>();
    	String pkColumn = "id";
    	for (DsFieldInfo dsField : dsFields) {
    		columnDefines.add("`" + dsField.getFieldName() + "` " + getBlinkType(dsField.getFieldType()));
    		if (dsField.getIsPk().intValue() == 1) {
    			pkColumn = dsField.getFieldName();
    		}
    	}
    	String sql = 
    			"create table adb_sink (\n" + 
    			"    " + StringUtils.join(columnDefines, ",") + ",\n" + 
    			"    primary key(" + pkColumn + ")\n" + 
    			") with (\n" + 
    			"    type = 'QANAT_ADB30',\n" + 
    			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
    			"    dbName='" + dstDbName + "',\n" + 
    			"    tableName='" + dstTableName + "',\n" + 
    			"    replaceMode = 'replace',\n" + 
    			"    streamEvent = 'disable'\n" +
    			");\n" + 
    			"\n";
    	if (parallism == 1) {
    		sql += "CREATE TABLE adb_source (\n" + 
    			"    " + StringUtils.join(columnDefines, ",") + ",\n" + 
    			"    primary key(" + pkColumn + ")\n" + 
    			") WITH (\n" + 
    			"    type = 'custom',\n" + 
    			"    tableFactoryClass = 'com.aliyun.wormhole.qanat.blink.mysql.scan.MySQLScanTableFactory',\r\n" + 
    			"    dbName='" + srcDsMetaJson.getString("dbName") + "',\r\n" + 
    			"    tableName='" + srcDsMetaJson.getString("table") + "',\r\n" + 
    			"    batchSize='" + batchSize + "'" + 
    			");\n" + 
    			"\n" + 
    			"insert into adb_sink\n" + 
    			"select * from adb_source;\n";
    	} else {
    		for (int i = 0; i < parallism; i++) {
        		sql += "CREATE TABLE adb_source_" + i + " (\n" + 
            			"    " + StringUtils.join(columnDefines, ",") + ",\n" + 
            			"    primary key(" + pkColumn + ")\n" + 
            			") WITH (\n" + 
            			"    type = 'custom',\n" + 
            			"    tableFactoryClass = 'com.aliyun.wormhole.qanat.blink.mysql.scan.MySQLScanTableFactory',\r\n" + 
            			"    dbName='" + srcDsMetaJson.getString("dbName") + "',\r\n" + 
            			"    tableName='" + srcDsMetaJson.getString("table") + "',\r\n" + 
            			"    whereClause='" + (StringUtils.isNotBlank(multiDbSyncFilter) ? (multiDbSyncFilter + " AND ") : "") + pkColumn + "%" + parallism + "=" + i + "',\r\n" +
            			"    batchSize='" + batchSize + "'" + 
            			");\n" + 
            			"\n" + 
            			"insert into adb_sink select * from adb_source_" + i + ";\n" +
            			"\n";
    		}
    	}
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName + "/" + srcDsMetaJson.getString("table") + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_MYSQL_SCAN), true, blinkService.getBatchPlanJson4DwSource2(srcDsMetaJson.getString("table"), pkColumn, parallism, 1));
    	
    	return jobName;
    }

	private String getBlinkType(String fieldType) {
		if ("datetime".equalsIgnoreCase(fieldType)) {
			return "timestamp";
		} else if ("multivalue".equalsIgnoreCase(fieldType)) {
			return "varchar";
		} else {
			return fieldType;
		}
	}

	public Boolean reflectObjectFieldChange(String tenantId, String dsUniqueName, String fieldName, Integer isRef, String operateType, String tagJson) {
		log.info("reflectObjectFieldChange({},{},{},{},{},{}) start", tenantId, dsUniqueName, fieldName, isRef, operateType, tagJson);

		Statement statement = null;
		Connection connection = null;
		try {
			//根据对象code获取对应的表定义
			DatasourceExample objDsExample = new DatasourceExample();
			objDsExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsUniqueNameEqualTo(dsUniqueName).andDsTypeEqualTo("obj");
			List<Datasource> objDsList = datasourceMapper.selectByExample(objDsExample);
			if (CollectionUtils.isEmpty(objDsList)) {
				throw new QanatBizException("no dsInfo found, dusUniqueName:" + dsUniqueName);
			}

			//遍历相关目标表进行表结构变更
			for (int i = 0; i < objDsList.size(); i++) {
				Datasource objDsInfo = objDsList.get(i);

				DsRelationExample dsRelExp = new DsRelationExample();
				dsRelExp.createCriteria().andTenantIdEqualTo(tenantId)
						.andIsDeletedEqualTo(0L)
						.andRelationTypeEqualTo("ods")
						.andSrcDsNameEqualTo(objDsInfo.getDsName());
				List<DsRelation> dsRels = dsRelationMapper.selectByExample(dsRelExp);
				if (CollectionUtils.isEmpty(dsRels)) {
					throw new QanatBizException("no_ods_ds", "the ods dsInfo of " + objDsInfo.getDsName() + " is not found");
				}
				DatasourceExample example = new DatasourceExample();
				example.createCriteria().andDsNameEqualTo(dsRels.get(0).getDstDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
				List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
				if (CollectionUtils.isEmpty(dsList)) {
					throw new QanatBizException(dsRels.get(0).getDstDsName() + " is not found");
				}
				Datasource toDsInfo = dsList.get(0);

				//获取目标DB连接
				log.info("start to ddl:{}", toDsInfo.getDbName());
				JSONObject dbMetaJson = dsInfoService.getDbMetaByName(toDsInfo.getDbName());
				String dstDbType = dbMetaJson.getString("dbType");
				RdsConnectionParam param = new RdsConnectionParam();
				param.setUrl(dbMetaJson.getString("jdbcUrl"))
						.setUserName(dbMetaJson.getString("username"))
						.setPassword(dbMetaJson.getString("password"));
				connection = dsHandler.connectToTable(param);

				//对与此对象相关的源表及目标表调整表结构，且晋档model中的对象为默认引用全字段时进行表结构调整，注：因为是单方面对象增减字段，不产生新的model version
				if ("CREATE".equalsIgnoreCase(operateType)) {
					addDsFieldInfoAndTableColumn(tenantId, dsUniqueName, fieldName, statement, connection,
							objDsInfo, objDsInfo.getDsName(), toDsInfo, tagJson, dstDbType);
				} else if ("UPDATE".equalsIgnoreCase(operateType)) {
					//obj表字段更新描述信息
					dsInfoService.updObjectField(tenantId, objDsInfo.getObjectType(), dsUniqueName, fieldName, tagJson);
					//容错处理
					try {
						addDsFieldInfoAndTableColumn(tenantId, dsUniqueName, fieldName, statement, connection,
								objDsInfo, objDsInfo.getDsName(), toDsInfo, tagJson, dstDbType);
					} catch(Exception e) {
						log.info("try to add field again failed,{}", e.getMessage());
					}

					DsFieldInfoExample dsFieldExample= new DsFieldInfoExample();
					dsFieldExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsUniqueNameEqualTo(dsUniqueName).andFieldUniqueNameEqualTo(fieldName);
					List<DsFieldInfo> dsFieldInfos = dsFieldInfoMapper.selectByExample(dsFieldExample);
					if (CollectionUtils.isEmpty(dsFieldInfos)) {
						throw new QanatBizException("upd field is not found");
					}
					DsFieldInfo updField = dsFieldInfos.get(0);

					//adb目标表更改字段描述
					String tableName = toDsInfo.getTableName();
					statement = connection.createStatement();
					try {
						String sql = null;
						if ("hologres".equalsIgnoreCase(dstDbType) || "postgresql".equalsIgnoreCase(dstDbType)) {
							sql = "alter table " + tableName + " MODIFY COLUMN " + updField.getFieldName() + " " + updField.getFieldType();
						} else {
							sql = "alter table " + tableName + " MODIFY COLUMN " + updField.getFieldName() + " " + updField.getFieldType() + " COMMENT '" + updField.getFieldDesc() + "'";
						}
						log.info("alter sql={}", sql);
						statement.execute(sql);
					} catch (Exception e) {
						log.error("alter table failed:{}", e.getMessage());
					}
				} else if ("DELETE".equalsIgnoreCase(operateType)) {
					DsFieldInfoExample dsFieldExample= new DsFieldInfoExample();
					dsFieldExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsUniqueNameEqualTo(dsUniqueName).andFieldUniqueNameEqualTo(fieldName);
					List<DsFieldInfo> dsFieldInfos = dsFieldInfoMapper.selectByExample(dsFieldExample);
					if (CollectionUtils.isEmpty(dsFieldInfos)) {
						throw new QanatBizException("upd field is not found:" + dsUniqueName + ":" + fieldName);
					}
					DsFieldInfo updField = dsFieldInfos.get(0);
					//obj表模型清字段(逻辑删)
					dsInfoService.deleteObjectField(tenantId, dsUniqueName, fieldName);

					//adb目标表清理字段
					String tableName = toDsInfo.getTableName();
					statement = connection.createStatement();
					try {
						String sql = "alter table " + tableName + " drop column " + updField.getFieldName();
						log.info("alter sql={}", sql);
						statement.execute(sql);
					} catch (Exception e) {
						log.error("alter table failed:{}", e.getMessage());
					}
				}
				log.info("finish to ddl:{}", objDsList.get(i).getDbName());
			}
			return true;
		} catch (Exception e) {
			log.error("reflectObjectFieldChange failed", e);
			throw new QanatBizException("reflectObjectFieldChange failed: " + e.getMessage());
		} finally {
			if (statement != null) {
				try {
					statement.close();
				} catch (SQLException e) {
				} finally {
					statement = null;
				}
			}
			if (connection != null) {
				try {
					connection.close();
				} catch (SQLException e) {
				} finally {
					connection = null;
				}
			}
		}
	}

	private void addDsFieldInfoAndTableColumn(String tenantId, String dsUniqueName, String fieldName,
											  Statement statement, Connection connection, Datasource objDsInfo, String objDsName,
											  Datasource toDsInfo, String tagJson, String dstDbType) throws SQLException {
		//obj表模型加字段
		dsInfoService.addObjectField(tenantId, objDsInfo.getDbName(), objDsName, objDsInfo.getObjectType(), dsUniqueName, fieldName, tagJson);

		DsFieldInfoExample dsFieldExample= new DsFieldInfoExample();
		dsFieldExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsUniqueNameEqualTo(dsUniqueName).andFieldUniqueNameEqualTo(fieldName);
		List<DsFieldInfo> dsFieldInfos = dsFieldInfoMapper.selectByExample(dsFieldExample);
		if (CollectionUtils.isEmpty(dsFieldInfos)) {
			throw new QanatBizException("add field is not found");
		}
		DsFieldInfo newField = dsFieldInfos.get(0);
		String tableName = toDsInfo.getTableName();
		log.info("tableName={}", tableName);
		statement = connection.createStatement();
		String objFieldType = JSON.parseObject(tagJson).getString("dataType");
		String seperator = (StringUtils.isNotBlank(newField.getExtInfo()) && JSON.parseObject(newField.getExtInfo()) != null) ? JSON.parseObject(newField.getExtInfo()).getString("separator") : ",";

		String testSql = "select " + newField.getFieldName() + " from " + tableName + " limit 1";
		log.info("alter testSql={}", testSql);
		try {
			statement.execute(testSql);
			log.info("field:{} is already in table:{}", newField.getFieldName(), tableName);
			return;
		} catch(Exception e) {

		}
		String sql = null;
		if ("hologres".equalsIgnoreCase(dstDbType) || "postgresql".equalsIgnoreCase(dstDbType)) {
			sql = "alter table " + tableName + " add column " + newField.getFieldName() + " " + ("ENUMS".equalsIgnoreCase(objFieldType) || "multivalue".equalsIgnoreCase(newField.getDataType()) ? "text" : newField.getFieldType());
		} else {
			sql = "alter table " + tableName + " add column " + newField.getFieldName() + " " + ("ENUMS".equalsIgnoreCase(objFieldType) || "multivalue".equalsIgnoreCase(newField.getDataType()) ? "multivalue delimiter_tokenizer '" + seperator + "' value_type '" + newField.getFieldType() + "'" : newField.getFieldType()) + " COMMENT '" + newField.getFieldDesc() + "'";
		}
		log.info("alter sql={}", sql);
		statement.execute(sql);

		try {
			String trySql = null;
			if ("hologres".equalsIgnoreCase(dstDbType) || "postgresql".equalsIgnoreCase(dstDbType)) {
				trySql = "alter table tmp_" + tableName + " add column " + newField.getFieldName() + " " + ("ENUMS".equalsIgnoreCase(objFieldType) || "multivalue".equalsIgnoreCase(newField.getDataType()) ? "text" : newField.getFieldType());
			} else {
				trySql = "alter table tmp_" + tableName + " add column " + newField.getFieldName() + " " + ("ENUMS".equalsIgnoreCase(objFieldType) || "multivalue".equalsIgnoreCase(newField.getDataType()) ? "multivalue delimiter_tokenizer '" + seperator + "' value_type '" + newField.getFieldType() + "'" : newField.getFieldType()) + " COMMENT '" + newField.getFieldDesc() + "'";
			}
			statement.execute(trySql);
		} catch(Exception e) {

		}
	}
}