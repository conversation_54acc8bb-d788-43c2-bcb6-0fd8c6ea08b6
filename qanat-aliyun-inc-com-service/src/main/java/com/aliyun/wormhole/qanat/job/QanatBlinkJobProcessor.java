package com.aliyun.wormhole.qanat.job;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.BlinkNode;
import com.aliyun.wormhole.qanat.api.dag.DagInstStatus;
import com.aliyun.wormhole.qanat.api.dag.NodeAction;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTaskExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import com.taobao.ateye.util.CollectionUtils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * Blink任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatBlinkJobProcessor extends JavaProcessor {
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private TaskInfoMapper taskInfoMapper;
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;
    
    @Resource
    private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;

    @Override
    public ProcessResult process(JobContext context) {
        String taskName = context.getTaskName();
        log.info("Qanat Blink Job[{}] start.", taskName);
        String subTaskInstId = null;
        String operator = null;
        try {
            Map<String, Object> instParamsMap = null;
            if (StringUtils.isNotBlank(context.getInstanceParameters())) {
                instParamsMap = (Map<String, Object>)JSON.parseObject(context.getInstanceParameters(), Map.class);
            }
            if (instParamsMap == null) {
                instParamsMap = (Map<String, Object>)JSON.parseObject(context.getJobParameters(), Map.class);
            }
            BlinkNode blink = ((JSONObject)instParamsMap.get("node")).toJavaObject(BlinkNode.class);
            subTaskInstId = String.valueOf(instParamsMap.get("subTaskInstId"));
            operator = (String)instParamsMap.get("operator");
            Long taskInstId = Long.valueOf(String.valueOf(instParamsMap.get("taskInstId")));
            JSONObject execParamJson = null;
            if (taskInstId != null) {
            	TaskInstance taskInst = taskInstanceMapper.selectByPrimaryKey(taskInstId);
            	execParamJson = JSON.parseObject(taskInst.getExecParam());
            }
            Date startTime = new Date();
            String incrSyncStartTime = "";
            String tenantId = String.valueOf(instParamsMap.get("tenantId"));
            String appName = String.valueOf(instParamsMap.get("appName"));
            
            //通过主任务实例的全局参数获取blink流任务的开启时间点
            if (blink.getNodeAction().equals(NodeAction.STREAM)) {
            	if (blink.getStartTimePolicy() != null) {
            		startTime = getStartTimeFromPolicy(blink.getStartTimePolicy());
            	} else {
	                if (execParamJson != null) {
	                    incrSyncStartTime = execParamJson.getString("incr_sync_start_time");
	                }
	                if (StringUtils.isNotBlank((String)instParamsMap.get("incr_sync_start_time"))) {//JOB参数如果设置优先级更高
	                    incrSyncStartTime = (String)instParamsMap.get("startConsumeTime");
	                }
	                if (StringUtils.isNotBlank(incrSyncStartTime)) {
	                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	                    startTime = sdf.parse(incrSyncStartTime);
	                }
            	}
            }
            
            TaskInstance taskInstUpd = new TaskInstance();
            taskInstUpd.setId(Long.valueOf(subTaskInstId));
            taskInstUpd.setGmtModified(new Date());
            taskInstUpd.setModifyEmpid(operator);
            taskInstUpd.setExternalInstId(context.getJobInstanceId() + "");//SchedulerX任务实例id
            taskInstUpd.setStatus(DagInstStatus.EXECUTING.getCode().byteValue());
            taskInstUpd.setHostAddr(InetAddress.getLocalHost().getHostAddress());
            taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
            
            blinkService.stopJob(tenantId, appName, blink.getJobName());
            log.info("blink job[{}] has been stopped", blink.getJobName());
            
            Map<String, String> blinkPamams = null;
            if (execParamJson != null) {
               	JSONObject dataJson = execParamJson.getJSONObject("data");
               	if (dataJson != null && CollectionUtils.isNotEmpty(dataJson.keySet())) {
               		blinkPamams = new HashMap<>();
               		for (String key : dataJson.keySet()) {
               			blinkPamams.put(key, dataJson.getString(key));
               		}
               	}
            }
            
            Long blinkInstId = blinkService.startJob(tenantId, appName, blink.getJobName(), startTime, blinkPamams);
            Date endTime = null;
            boolean isGetCU = false;
            if (blinkInstId != null && blink.getNodeAction().equals(NodeAction.BATCH)) {
                while (true) {
                    String instState = blinkService.getInstanceActualState(tenantId, appName, blink.getJobName(), blinkInstId);
                    if ("SUCCESS".equalsIgnoreCase(instState)) {
                        break;
                    } else if ("RUNNING".equalsIgnoreCase(instState) && !isGetCU) {
                    	try {
	                		Map<String, Object> resource = blinkService.getInstanceResource(tenantId, appName, blink.getJobName());
	        				if (resource != null && CollectionUtils.isNotEmpty(resource.keySet())) {
	            				BigDecimal vcoreCU = resource.get("allocatedVirtualCores") != null ? new BigDecimal(resource.get("allocatedVirtualCores").toString()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");
	            				BigDecimal mbCU = resource.get("allocatedMB") != null ? new BigDecimal(resource.get("allocatedMB").toString()).divide(new BigDecimal("4096"), 2, RoundingMode.HALF_UP) : new BigDecimal("0");
	
	                        	DatatubeInstanceTaskExample example = new DatatubeInstanceTaskExample();
	                        	example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTaskNameEqualTo(blink.getJobName()).andTaskTypeEqualTo("blink_batch");
	                        	
	                        	DatatubeInstanceTask record = new DatatubeInstanceTask();
	                    		record.setGmtModified(new Date());
	                    		record.setModifyEmpid(operator);
	            				record.setTaskCu(vcoreCU.compareTo(mbCU) > 0 ? vcoreCU : mbCU);
	                        	datatubeInstanceTaskMapper.updateByExampleSelective(record, example);
	                        	
	                        	isGetCU = true;
	        				}
                    	} catch (Exception e) {
                    		log.error("{} get cu failed:{}", blink.getJobName(), e.getMessage());
                        	isGetCU = true;
                    	}
                    } else if ("TERMINATED".equalsIgnoreCase(instState)
                    		|| "FAILED".equalsIgnoreCase(instState)) {
                    	throw new QanatBizException("Blink任务执行失败或手动停止");
                    }
                    Thread.sleep(60000);//60s
                }
                endTime = new Date();

                //全局任务参数更新到主任务实例的参数中
                if (blink.isDataBaseline()) {
                    TaskInstance taskInst = taskInstanceMapper.selectByPrimaryKey(taskInstId);
                    JSONObject execParam = JSON.parseObject(taskInst.getExecParam());
                    if (execParam == null) {
                    	execParam = new JSONObject();
                    }
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    execParam.put("incr_sync_start_time", sdf.format(startTime));
                    taskInstUpd = new TaskInstance();
                    taskInstUpd.setId(taskInstId);
                    taskInstUpd.setExecParam(JSON.toJSONString(execParam));
                    taskInstUpd.setGmtModified(new Date());
                    taskInstUpd.setModifyEmpid(operator);
                    taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
                }
            }
            taskInstUpd = new TaskInstance();
            taskInstUpd.setId(Long.valueOf(subTaskInstId));
            taskInstUpd.setGmtModified(new Date());
            taskInstUpd.setEndTime(endTime);
            taskInstUpd.setModifyEmpid(operator);
            taskInstUpd.setStatus(DagInstStatus.SUCCESS.getCode().byteValue());
            taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
        } catch (QanatBizException e) {
            log.error("Blink任务调度异常:{}", e.getMessage());
            
            TaskInstance taskInstUpd = new TaskInstance();
            taskInstUpd.setId(Long.valueOf(subTaskInstId));
            taskInstUpd.setGmtModified(new Date());
            taskInstUpd.setEndTime(new Date());
            taskInstUpd.setModifyEmpid(operator);
            taskInstUpd.setStatus(DagInstStatus.FAILED.getCode().byteValue());
            taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
            
            return new ProcessResult(false, e.getMessage());
        } catch (Exception e) {
            log.error("Blink任务调度异常", e);
            
            TaskInstance taskInstUpd = new TaskInstance();
            taskInstUpd.setId(Long.valueOf(subTaskInstId));
            taskInstUpd.setGmtModified(new Date());
            taskInstUpd.setEndTime(new Date());
            taskInstUpd.setModifyEmpid(operator);
            taskInstUpd.setStatus(DagInstStatus.FAILED.getCode().byteValue());
            taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
            
            return new ProcessResult(false, e.getMessage());
        }
        return new ProcessResult(true);
    }
    
    private Date getStartTimeFromPolicy(String startTimePolicy) {
		if ("everyday_00_00_00".equalsIgnoreCase(startTimePolicy)) {
			SimpleDateFormat fromSdf = new SimpleDateFormat("yyyy-MM-dd");
			String date = fromSdf.format(new Date());
			date = date + " 00:00:00";
			SimpleDateFormat toSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			try {
				return toSdf.parse(date);
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	@Override
    public void kill(JobContext context) {
		String taskName = context.getTaskName();
        log.info("begin to kill Job[{}]", taskName);
        String subTaskInstId = null;
        String operator = null;
        try {
            Map<String, Object> instParamsMap = null;
            if (StringUtils.isNotBlank(context.getInstanceParameters())) {
                instParamsMap = (Map<String, Object>)JSON.parseObject(context.getInstanceParameters(), Map.class);
            }
            if (instParamsMap == null) {
                instParamsMap = (Map<String, Object>)JSON.parseObject(context.getJobParameters(), Map.class);
            }
            BlinkNode blink = ((JSONObject)instParamsMap.get("node")).toJavaObject(BlinkNode.class);
            subTaskInstId = String.valueOf(instParamsMap.get("subTaskInstId"));
            operator = (String)instParamsMap.get("operator");
            String tenantId = String.valueOf(instParamsMap.get("tenantId"));
            String appName = String.valueOf(instParamsMap.get("appName"));
            
            if (blink.getNodeAction().equals(NodeAction.STREAM)) {
                log.info("blink stream job[{}] skipped", blink.getJobName());
            	return;
            }
            
            blinkService.stopJob(tenantId, appName, blink.getJobName());
            log.info("blink job[{}] has been killed", blink.getJobName());
        } catch (Exception e) {
            log.error("Kill Blink任务异常:{}", e.getMessage(), e);
        } finally {
        	TaskInstance taskInstUpd = new TaskInstance();
            taskInstUpd.setId(Long.valueOf(subTaskInstId));
            taskInstUpd.setGmtModified(new Date());
            taskInstUpd.setEndTime(new Date());
            taskInstUpd.setModifyEmpid(operator);
            taskInstUpd.setStatus(DagInstStatus.FAILED.getCode().byteValue());
            taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
        }
    }
}