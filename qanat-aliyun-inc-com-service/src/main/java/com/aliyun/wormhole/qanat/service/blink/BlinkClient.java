package com.aliyun.wormhole.qanat.service.blink;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.BlinkJobRequest;
import com.aliyuncs.AcsRequest;
import com.aliyuncs.AcsResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.foas.model.v20181111.CheckRawPlanJsonRequest;
import com.aliyuncs.foas.model.v20181111.CheckRawPlanJsonResponse;
import com.aliyuncs.foas.model.v20181111.CommitJobRequest;
import com.aliyuncs.foas.model.v20181111.CommitJobResponse;
import com.aliyuncs.foas.model.v20181111.CreateFolderRequest;
import com.aliyuncs.foas.model.v20181111.CreateFolderResponse;
import com.aliyuncs.foas.model.v20181111.CreateJobRequest;
import com.aliyuncs.foas.model.v20181111.CreateJobResponse;
import com.aliyuncs.foas.model.v20181111.DeleteJobRequest;
import com.aliyuncs.foas.model.v20181111.DeleteJobResponse;
import com.aliyuncs.foas.model.v20181111.GetFolderRequest;
import com.aliyuncs.foas.model.v20181111.GetFolderResponse;
import com.aliyuncs.foas.model.v20181111.GetInstanceConfigRequest;
import com.aliyuncs.foas.model.v20181111.GetInstanceConfigResponse;
import com.aliyuncs.foas.model.v20181111.GetInstanceDetailRequest;
import com.aliyuncs.foas.model.v20181111.GetInstanceDetailResponse;
import com.aliyuncs.foas.model.v20181111.GetInstanceExceptionsRequest;
import com.aliyuncs.foas.model.v20181111.GetInstanceExceptionsResponse;
import com.aliyuncs.foas.model.v20181111.GetInstanceMetricRequest;
import com.aliyuncs.foas.model.v20181111.GetInstanceMetricResponse;
import com.aliyuncs.foas.model.v20181111.GetInstanceMetricResponse.Metric;
import com.aliyuncs.foas.model.v20181111.GetInstanceRequest;
import com.aliyuncs.foas.model.v20181111.GetInstanceResourceRequest;
import com.aliyuncs.foas.model.v20181111.GetInstanceResourceResponse;
import com.aliyuncs.foas.model.v20181111.GetInstanceResponse;
import com.aliyuncs.foas.model.v20181111.GetInstanceRunSummaryRequest;
import com.aliyuncs.foas.model.v20181111.GetInstanceRunSummaryResponse;
import com.aliyuncs.foas.model.v20181111.GetJobRequest;
import com.aliyuncs.foas.model.v20181111.GetJobResponse;
import com.aliyuncs.foas.model.v20181111.GetRawPlanJsonRequest;
import com.aliyuncs.foas.model.v20181111.GetRawPlanJsonResponse;
import com.aliyuncs.foas.model.v20181111.ListInstanceRequest;
import com.aliyuncs.foas.model.v20181111.ListInstanceResponse;
import com.aliyuncs.foas.model.v20181111.ListJobRequest;
import com.aliyuncs.foas.model.v20181111.ListJobResponse;
import com.aliyuncs.foas.model.v20181111.ListProjectBindQueueRequest;
import com.aliyuncs.foas.model.v20181111.ListProjectBindQueueResourceRequest;
import com.aliyuncs.foas.model.v20181111.ListProjectBindQueueResourceResponse;
import com.aliyuncs.foas.model.v20181111.ListProjectBindQueueResourceResponse.Queue;
import com.aliyuncs.foas.model.v20181111.ListProjectBindQueueResponse;
import com.aliyuncs.foas.model.v20181111.ModifyInstanceStateRequest;
import com.aliyuncs.foas.model.v20181111.ModifyInstanceStateResponse;
import com.aliyuncs.foas.model.v20181111.OfflineJobRequest;
import com.aliyuncs.foas.model.v20181111.OfflineJobResponse;
import com.aliyuncs.foas.model.v20181111.StartJobRequest;
import com.aliyuncs.foas.model.v20181111.StartJobResponse;
import com.aliyuncs.foas.model.v20181111.UpdateJobRequest;
import com.aliyuncs.foas.model.v20181111.UpdateJobResponse;
import com.aliyuncs.foas.model.v20181111.ValidateJobRequest;
import com.aliyuncs.foas.model.v20181111.ValidateJobResponse;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.google.gson.Gson;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
public class BlinkClient {

    private BlinkConf conf;
    private DefaultAcsClient client;

	public BlinkClient(BlinkConf conf) {
		log.debug("blinkConfig={}", JSON.toJSONString(conf));
        DefaultProfile.addEndpoint(conf.getRegionId(), "foas", "foas.aliyuncs.com");
        IClientProfile profile = DefaultProfile.getProfile(conf.getRegionId(), conf.getAccessId(), conf.getAccessKey());
        client = new DefaultAcsClient(profile);
        client.setAutoRetry(false);
        this.conf = conf;
    }
    
    public Boolean createJob(BlinkJobRequest req) {
        log.debug("begin to createJob({})", req);
        try {
        	if (isJobExists(req.getJobName())) {
        		throw new QanatBizException("Blink job[" + req.getJobName() + "] is already existed");
        	}
        	//应用根文件夹不存在就创建
            GetFolderRequest getFolderRequest = new GetFolderRequest();
            getFolderRequest.setPath("/" + req.getAppName());
            getFolderRequest.setProjectName(conf.getProjectName());
            GetFolderResponse getFolderResponse = getResponse(client, getFolderRequest);
            if (getFolderResponse == null|| getFolderResponse.getFolder() == null || getFolderResponse.getFolder().getFolderId() == null) {
                //创建文件夹
                CreateFolderRequest createFolderRequest = new CreateFolderRequest();
                createFolderRequest.setPath("/" + req.getAppName());
                createFolderRequest.setProjectName(conf.getProjectName());
                CreateFolderResponse createFolderResponse = getResponse(client, createFolderRequest);
                log.debug("createFolderResponse:{}", JSON.toJSONString(createFolderResponse));
            }
            //获取文件夹
            getFolderRequest = new GetFolderRequest();
            getFolderRequest.setPath(req.getFolderName());
            getFolderRequest.setProjectName(conf.getProjectName());
            getFolderResponse = getResponse(client, getFolderRequest);
            Long folderId = null;
            if (getFolderResponse == null|| getFolderResponse.getFolder() == null || getFolderResponse.getFolder().getFolderId() == null) {
                //创建文件夹
                CreateFolderRequest createFolderRequest = new CreateFolderRequest();
                createFolderRequest.setPath(req.getFolderName());
                createFolderRequest.setProjectName(conf.getProjectName());
                CreateFolderResponse createFolderResponse = getResponse(client, createFolderRequest);
                log.debug("createFolderResponse:{}", JSON.toJSONString(createFolderResponse));
                folderId = createFolderResponse == null ? null : createFolderResponse.getFolderId();
                
                int retries = 3;
                while (createFolderResponse == null && retries > 0) {
                	getFolderRequest = new GetFolderRequest();
                    getFolderRequest.setPath(req.getFolderName());
                    getFolderRequest.setProjectName(conf.getProjectName());
                    getFolderResponse = getResponse(client, getFolderRequest);
                    if (getFolderResponse == null|| getFolderResponse.getFolder() == null || getFolderResponse.getFolder().getFolderId() == null) {
                    	Thread.sleep(3000);
                    	retries--;
                    } else {
                        folderId = getFolderResponse.getFolder().getFolderId();
                    	break;
                    }
                }
            } else {
                folderId = getFolderResponse.getFolder().getFolderId();
            }
            CreateJobRequest createJobRequest = new CreateJobRequest();
            createJobRequest.setProjectName(conf.getProjectName());
            createJobRequest.setJobName(req.getJobName());
            createJobRequest.setCode(req.getSql());
            createJobRequest.setQueueName(conf.getQueueName());
            createJobRequest.setClusterId(conf.getClusterId());
            if (StringUtils.isNoneBlank(req.getPlanJson())) {
                createJobRequest.setPlanJson(req.getPlanJson());
            }
            if (StringUtils.isNoneBlank(req.getProperties())) {
                createJobRequest.setProperties(req.getProperties());//无则不用设置
            }
            if (StringUtils.isNotBlank(conf.getEngineVersion())) {
                createJobRequest.setEngineVersion(conf.getEngineVersion());//engineVersion可以不用配置，不配置，会使用current版本
            }
            createJobRequest.setDescription(req.getJobName() + " created from api");
            if (StringUtils.isNotBlank(req.getPackages())) {
                createJobRequest.setPackages(req.getPackages());//多个package之间用英文逗号分隔
            }
            createJobRequest.setJobType(req.getIsBatch() ? "flink_batch" : "flink_stream");//flink_stream/flink_bacth 大小写不敏感
            createJobRequest.setApiType("sql");
            createJobRequest.setFolderId(folderId);
            CreateJobResponse createJobResponse = getResponse(client, createJobRequest);
            log.debug("createFolderResponse:{}", JSON.toJSONString(createJobResponse));
            log.debug("job[{}] is created.", req.getJobName());
            return true;
        } catch (Exception e) {
            log.error("createJob({}) failed:{}", req.getJobName(), e.getMessage());
            return false;
        }
    }
    
    public GetJobResponse.Job getJob(String jobName) {
        log.debug("begin to getJob({})", jobName);
        try {
	    	GetJobRequest getJobRequest = new GetJobRequest();
	        getJobRequest.setProjectName(conf.getProjectName());
	        getJobRequest.setJobName(jobName);
	        GetJobResponse getJobResponse = getResponse(client, getJobRequest);
	        log.debug("getJob:{}", JSON.toJSONString(getJobResponse));
	        return getJobResponse == null ? null : getJobResponse.getJob();
        } catch(Exception e) {
        	log.error("getJob failed, error={}", e.getMessage());
        }
        return null;
    }
    
    public List<ListJobResponse.Job> listJob() {
        log.debug("begin to listJob()");
        List<ListJobResponse.Job> result = new ArrayList<>();
        try {
	    	ListJobRequest getJobRequest = new ListJobRequest();
	        getJobRequest.setProjectName(conf.getProjectName());
	        getJobRequest.setPageSize(100);
	        ListJobResponse getJobResponse = getResponse(client, getJobRequest);
	        if (getJobResponse == null) {
	        	return result;
	        }
	        log.debug("listJob:{}", JSON.toJSONString(getJobResponse));
	        result.addAll(getJobResponse.getJobs());
	        int total = getJobResponse.getTotalPage();
	        int i = 2;
	        while (i <= total) {
	        	getJobRequest.setPageIndex(i);
		        getJobRequest.setPageSize(100);
		        getJobResponse = getResponse(client, getJobRequest);
		        if (getJobResponse == null) {
		        	return result;
		        }
		        result.addAll(getJobResponse.getJobs());
		        i++;
		        log.info("listJob:{}", JSON.toJSONString(getJobResponse));
	        }
	        return result;
        } catch(Exception e) {
        	log.error("listJob failed, error={}", e.getMessage());
        }
        return result;
    }
    
    public Boolean validateJob(String jobName) {
        log.debug("begin to validateJob({})", jobName);
        ValidateJobRequest validateJobRequest = new ValidateJobRequest();
        validateJobRequest.setProjectName(conf.getProjectName());
        validateJobRequest.setJobName(jobName);
        ValidateJobResponse validateJobResponse = getResponse(client, validateJobRequest);
        log.debug("validateJob:{}", JSON.toJSONString(validateJobResponse));
        if (validateJobResponse != null && validateJobResponse.getJobInOut() != null) {
        	return true;
        } else {
        	return false;
        }
    }
    
    public String getPlanJson(String jobName, Integer expectedCUs, Boolean isAutoconfEnable) {
        log.debug("begin to getPlanJson({},{},{})", jobName, expectedCUs, isAutoconfEnable);
        String planJson = null;
        try {
            	if (!isJobExists(jobName)) {
            		throw new QanatBizException("Blink job[" + jobName + "] is not existed");
            	}
            	//获取job的planjson，该接口是异步接口，提交完成之后，需要调用下面的CheckRawPlanJson接口来获取结果:Session in run/success/fail
	        GetRawPlanJsonRequest getRawPlanJsonRequest = new GetRawPlanJsonRequest();
	        getRawPlanJsonRequest.setProjectName(conf.getProjectName());
	        getRawPlanJsonRequest.setJobName(jobName);
	        getRawPlanJsonRequest.setAutoconfEnable(isAutoconfEnable == null ? false : isAutoconfEnable);//可以不填，默认为false
	        //设置这个job预期使用多少资源来运行，这个参数会影响到生成plan的大小，不提供的话，底层引擎会默认生成资源量
	        if (expectedCUs != null) {
        	        getRawPlanJsonRequest.setExpectedCore(1f * expectedCUs);//这个参数与下面的参数可以同时提供，也可以同时不提供，不允许一个提供，一个不提供
        	        getRawPlanJsonRequest.setExpectedGB(4f * expectedCUs);//
	        }
	        GetRawPlanJsonResponse getRawPlanJsonResponse = getResponse(client, getRawPlanJsonRequest);
	        if (getRawPlanJsonResponse == null) {
        		throw new QanatBizException("Blink job[" + jobName + "] getRawPlanJson failed");
	        }
            log.debug("getRawPlanJsonResponse={}",  JSON.toJSONString(getRawPlanJsonResponse));
	        CheckRawPlanJsonRequest checkRawPlanJsonRequest = new CheckRawPlanJsonRequest();
	        checkRawPlanJsonRequest.setProjectName(conf.getProjectName());
	        checkRawPlanJsonRequest.setJobName(jobName);
	        checkRawPlanJsonRequest.setSessionId(getRawPlanJsonResponse.getSessionId());
	        CheckRawPlanJsonResponse checkRawPlanJsonResponse;
	        int errorCnt = 0;
	        while (true && errorCnt < 180) {
	            checkRawPlanJsonResponse = getResponse(client, checkRawPlanJsonRequest);
	            log.debug("checkRawPlanJsonResponse={}",  JSON.toJSONString(checkRawPlanJsonResponse));
	            if (checkRawPlanJsonResponse.getPlanJsonInfo().getStatus().equals("success")) {
	                planJson = checkRawPlanJsonResponse.getPlanJsonInfo().getPlanJson();
	                break;
	            } else if (checkRawPlanJsonResponse.getPlanJsonInfo().getStatus().equals("fail")) {
	                errorCnt++;
	                log.debug("lastErrorMessage[{}]",
	                		checkRawPlanJsonResponse.getPlanJsonInfo().getErrorMessage());
	            }
	            Thread.sleep(10000);//每10s查询一次，不用过快
	        }
        } catch (Exception e) {
            log.error("getPlanJson({}) failed:{}", jobName, e.getMessage());
        } 
        return planJson;
    }
    
    /**
     * 启动任务
     * @param jobName
     * @param startTime
     */
    public Long startJob(String jobName, Date startTime) {
    	return startJob(jobName, startTime, null);
    }
    
    /**
     * 启动任务
     * @param jobName
     * @param startTime
     */
    public Long startJob(String jobName, Date startTime, Map<String, String> params) {
        log.debug("begin to startJob({},{},{})", jobName, startTime, JSON.toJSONString(params));
        Long instanceId = -1L;
        try {
        	if (!isJobExists(jobName)) {
        		throw new QanatBizException("Blink job[" + jobName + "] is not existed");
        	}
            //获取实例
            GetInstanceRequest getInstanceRequest = new GetInstanceRequest();
            getInstanceRequest.setProjectName(conf.getProjectName());
            getInstanceRequest.setJobName(jobName);

            //-1表示获取流作业的当前运行实例，注意批作业不可这样填，批作业需要填实际的运行实例id
            GetInstanceResponse.Instance instance =  null;
            boolean isBatch = isBatchJob(jobName);
            if (!isBatch) {
                getInstanceRequest.setInstanceId(-1L);
            } else {
                getInstanceRequest.setInstanceId(getBatchJobInstanceId(jobName));
            }
            GetInstanceResponse getInstanceResponse = getResponse(client, getInstanceRequest);
            log.debug("getInstanceResponse:{}", JSON.toJSONString(getInstanceResponse));
            instance = getInstanceResponse == null ? null : getInstanceResponse.getInstance();

            //启动作业，实例状态机参考文档
            if (instance == null || "UNKNOWN".equals(instance.getActualState()) || "TERMINATED".equals(instance.getActualState())
                || "SUCCESS".equals(instance.getActualState())) {
                StartJobRequest startJobRequest = new StartJobRequest();
                startJobRequest.setProjectName(conf.getProjectName());
                startJobRequest.setJobName(jobName);

                Map<String, String> map = new HashMap<String, String>();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Calendar cal = Calendar.getInstance();
                map.put("curdate", sdf.format(cal.getTime()));
                cal = Calendar.getInstance();
                cal.add(Calendar.DAY_OF_MONTH, -1);
                map.put("bizdate", sdf.format(cal.getTime()));
                cal = Calendar.getInstance();
                cal.add(Calendar.DAY_OF_MONTH, -3);
                map.put("bizdatePre2", sdf.format(cal.getTime()));
                
                //startOffset表示启动点位
                if (!isBatch) {
                    map.put("startOffset", String.valueOf(startTime == null ? System.currentTimeMillis() : startTime.getTime()));
                }
            	if (params != null) {
            		map.putAll(params);
            	}
                startJobRequest.setParameterJson(new Gson().toJson(map));//json格式参数
                StartJobResponse startJobResponse = getResponse(client, startJobRequest);
                log.debug("startJobResponse: {}", JSON.toJSONString(startJobResponse));
                
                //retry 3 times
                int reties = 3;
                while (startJobResponse == null && reties > 0) {
                    startJobResponse = getResponse(client, startJobRequest);
                    reties--;
                    log.debug("startJobResponse[{}]: {}", reties, JSON.toJSONString(startJobResponse));
                    Thread.sleep(300);
                }

                //等待启动成功
                while (true) {
                    GetInstanceRunSummaryResponse getInstanceRunSummaryResponse
                        = getInstanceRunSummary(jobName, startJobResponse == null || startJobResponse.getInstanceId() == null ? -1 : startJobResponse.getInstanceId());
                    instanceId = startJobResponse.getInstanceId();
                    if ("WAITING".equals(getInstanceRunSummaryResponse.getRunSummary().getActualState())) {
                        log.debug("lastErrorTime[{}], lastErrorMessage[{}]",
                        		getInstanceRunSummaryResponse.getRunSummary().getLastErrorTime(),
                                getInstanceRunSummaryResponse.getRunSummary().getLastErrorMessage());
                        Thread.sleep(1000);
                    } else {
                        break;
                    }
                }
            }
            log.debug("job[{}] is started.", jobName);
        } catch (Exception e) {
            log.error("startJob({}) failed:{}", jobName, e.getMessage());
        } 
        return instanceId;
    }

    /**
     * 启动任务
     * @param jobName
     * @param params
     */
    public Long startBatchJob(String jobName, Map<String, String> params) {
        log.info("begin to startBatchJob({},{})", jobName, JSON.toJSONString(params));
        Long instanceId = null;
        try {
            if (!isJobExists(jobName)) {
                throw new QanatBizException("Blink job[" + jobName + "] is not existed");
            }
            //获取实例
            GetInstanceRequest getInstanceRequest = new GetInstanceRequest();
            getInstanceRequest.setProjectName(conf.getProjectName());
            getInstanceRequest.setJobName(jobName);
            getInstanceRequest.setInstanceId(getBatchJobInstanceId(jobName));
            GetInstanceResponse getInstanceResponse = getResponse(client, getInstanceRequest);
            log.info("getInstanceResponse:{}", JSON.toJSONString(getInstanceResponse));
            GetInstanceResponse.Instance instance = getInstanceResponse == null ? null : getInstanceResponse.getInstance();

            //启动作业，实例状态机参考文档
            if (instance == null || "UNKNOWN".equals(instance.getActualState()) || "TERMINATED".equals(instance.getActualState())
                    || "SUCCESS".equals(instance.getActualState())) {
                StartJobRequest startJobRequest = new StartJobRequest();
                startJobRequest.setProjectName(conf.getProjectName());
                startJobRequest.setJobName(jobName);

                Map<String, String> map = new HashMap<String, String>();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Calendar cal = Calendar.getInstance();
                map.put("curdate", sdf.format(cal.getTime()));
                cal = Calendar.getInstance();
                cal.add(Calendar.DAY_OF_MONTH, -1);
                map.put("bizdate", sdf.format(cal.getTime()));
                cal = Calendar.getInstance();
                cal.add(Calendar.DAY_OF_MONTH, -3);
                map.put("bizdatePre2", sdf.format(cal.getTime()));
                if (params != null) {
                    map.putAll(params);
                }
                startJobRequest.setParameterJson(new Gson().toJson(map));//json格式参数
                StartJobResponse startJobResponse = getResponse(client, startJobRequest);
                log.info("startJobResponse: {}", JSON.toJSONString(startJobResponse));

                //retry 3 times
                int reties = 3;
                while (startJobResponse == null && reties > 0) {
                    startJobResponse = getResponse(client, startJobRequest);
                    reties--;
                    log.info("startJobResponse[{}]: {}", reties, JSON.toJSONString(startJobResponse));
                    Thread.sleep(500);
                }

                //等待启动成功
                boolean startResult = false;
                int waitTimes = 0;
                while (waitTimes < 300) {
                    GetInstanceRunSummaryResponse getInstanceRunSummaryResponse
                            = getInstanceRunSummary(jobName, startJobResponse.getInstanceId());
                    log.info("getInstanceRunSummaryResponse={}", JSON.toJSONString(getInstanceRunSummaryResponse));
                    if ("RUNNING".equals(getInstanceRunSummaryResponse.getRunSummary().getActualState())) {
                        instanceId = startJobResponse.getInstanceId();
                        startResult = true;
                        break;
                    } else {
                        log.info("waitfor {}times lastErrorTime[{}], lastErrorMessage[{}]",
                                waitTimes,
                                getInstanceRunSummaryResponse.getRunSummary().getLastErrorTime(),
                                getInstanceRunSummaryResponse.getRunSummary().getLastErrorMessage());
                    }
                    Thread.sleep(1000);
                    waitTimes++;
                }
                if (!startResult) {
                    log.error("startBatchJob({}) failed:{}", jobName, "startJobResponse is null");
                    throw new QanatBizException(jobName + " is not started");
                }
            }
            log.info("batchjob[{}] is started instancdId:{}", jobName, instanceId);
        } catch (Exception e) {
            log.error("startBatchJob({}) failed:{}", jobName, e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        return instanceId;
    }

    public GetInstanceRunSummaryResponse getInstanceRunSummary(String jobName, Long instanceId) {
        GetInstanceRunSummaryRequest getInstanceRunSummaryRequest = new GetInstanceRunSummaryRequest();
        getInstanceRunSummaryRequest.setProjectName(conf.getProjectName());
        getInstanceRunSummaryRequest.setJobName(jobName);
        getInstanceRunSummaryRequest.setInstanceId(instanceId);
        GetInstanceRunSummaryResponse getInstanceRunSummaryResponse = getResponse(client, getInstanceRunSummaryRequest);
        log.debug("getInstanceRunSummaryResponse:{}", JSON.toJSONString(getInstanceRunSummaryResponse));
        return getInstanceRunSummaryResponse;
    }

    public Long getBatchJobInstanceId(String jobName) {
        ListInstanceRequest listInstanceRequest = new ListInstanceRequest();
        listInstanceRequest.setProjectName(conf.getProjectName());
        listInstanceRequest.setJobName(jobName);
        listInstanceRequest.setJobType("FLINK_BATCH");
        listInstanceRequest.setActualState("RUNNING");
        ListInstanceResponse listInstanceResponse = getResponse(client, listInstanceRequest);
        log.debug("listInstanceResponse:{}", JSON.toJSONString(listInstanceResponse));
        if (listInstanceResponse != null && CollectionUtils.isNotEmpty(listInstanceResponse.getInstances())) {
            return listInstanceResponse.getInstances().get(0).getId();
        }
        return null;
    }
    
    /**
     * 停止任务
     * @param jobName
     */
    public void stopJob(String jobName) {
        log.debug("begin to stopJob({})", jobName);
        try {
        	if (!isJobExists(jobName)) {
        		throw new QanatBizException("Blink job[" + jobName + "] is not existed");
        	}
        	//停止作业，实例状态机参考文档
            GetInstanceRunSummaryRequest getInstanceRunSummaryRequest = new GetInstanceRunSummaryRequest();
            getInstanceRunSummaryRequest.setProjectName(conf.getProjectName());
            getInstanceRunSummaryRequest.setJobName(jobName);
            if (!isBatchJob(jobName)) {
            	getInstanceRunSummaryRequest.setInstanceId(-1L);
            } else {
            	getInstanceRunSummaryRequest.setInstanceId(getBatchJobInstanceId(jobName));
            }
            
            if (getInstanceRunSummaryRequest.getInstanceId() == null) {
                log.debug("job[{}] has 0 instances.", jobName);
                return;
            }
            
            GetInstanceRunSummaryResponse getInstanceRunSummaryResponse = getResponse(client, getInstanceRunSummaryRequest);

            if (getInstanceRunSummaryResponse != null && getInstanceRunSummaryResponse.getRunSummary() != null && "RUNNING".equals(getInstanceRunSummaryResponse.getRunSummary().getActualState())) {
                ModifyInstanceStateRequest modifyInstanceStateRequest = new ModifyInstanceStateRequest();
                modifyInstanceStateRequest.setProjectName(conf.getProjectName());
                modifyInstanceStateRequest.setJobName(jobName);
                modifyInstanceStateRequest.setInstanceId(getInstanceRunSummaryRequest.getInstanceId());
                modifyInstanceStateRequest.setExpectState("TERMINATED");
                ModifyInstanceStateResponse modifyInstanceStateResponse = getResponse(client, modifyInstanceStateRequest);
                log.debug("modifyInstanceStateResponse:{}", JSON.toJSONString(modifyInstanceStateResponse));

                //等待停止成功
                while (true) {
                    getInstanceRunSummaryResponse = getInstanceRunSummary(jobName, -1L);
                    log.debug("getInstanceRunSummaryResponse:{}", JSON.toJSONString(getInstanceRunSummaryResponse));

                    if ("RUNNING".equals(getInstanceRunSummaryResponse.getRunSummary().getActualState())) {
                        log.debug("lastErrorTime[{}], lastErrorMessage[{}]",
                                getInstanceRunSummaryResponse.getRunSummary().getLastErrorTime(),
                                getInstanceRunSummaryResponse.getRunSummary().getLastErrorMessage());
                        Thread.sleep(1000);
                    } else {
                        break;
                    }
                }
            }
            log.debug("job[{}] is terminated.", jobName);
        } catch (Exception e) {
            log.error("stopJob({}) failed:{}", jobName, e.getMessage());
        }
        
    }

    /**
     * 停止任务
     * @param jobName
     */
    public boolean stopBatchJob(String jobName) {
        log.info("begin to stopBatchJob({})", jobName);
        try {
            if (!isJobExists(jobName)) {
                throw new QanatBizException("Blink job[" + jobName + "] is not existed");
            }
            //停止作业，实例状态机参考文档
            GetInstanceRunSummaryRequest getInstanceRunSummaryRequest = new GetInstanceRunSummaryRequest();
            getInstanceRunSummaryRequest.setProjectName(conf.getProjectName());
            getInstanceRunSummaryRequest.setJobName(jobName);
            getInstanceRunSummaryRequest.setInstanceId(getBatchJobInstanceId(jobName));

            if (getInstanceRunSummaryRequest.getInstanceId() == null) {
                log.info("job[{}] has 0 instances.", jobName);
                return true;
            }

            GetInstanceRunSummaryResponse getInstanceRunSummaryResponse = getResponse(client, getInstanceRunSummaryRequest);

            if (getInstanceRunSummaryResponse != null && getInstanceRunSummaryResponse.getRunSummary() != null && "RUNNING".equals(getInstanceRunSummaryResponse.getRunSummary().getActualState())) {
                ModifyInstanceStateRequest modifyInstanceStateRequest = new ModifyInstanceStateRequest();
                modifyInstanceStateRequest.setProjectName(conf.getProjectName());
                modifyInstanceStateRequest.setJobName(jobName);
                modifyInstanceStateRequest.setInstanceId(getInstanceRunSummaryRequest.getInstanceId());
                modifyInstanceStateRequest.setExpectState("TERMINATED");
                ModifyInstanceStateResponse modifyInstanceStateResponse = getResponse(client, modifyInstanceStateRequest);
                log.info("modifyInstanceStateResponse:{}", JSON.toJSONString(modifyInstanceStateResponse));

                //等待停止成功
                while (true) {
                    getInstanceRunSummaryResponse = getInstanceRunSummary(jobName, getInstanceRunSummaryRequest.getInstanceId());
                    log.info("getInstanceRunSummaryResponse:{}", JSON.toJSONString(getInstanceRunSummaryResponse));

                    if ("RUNNING".equals(getInstanceRunSummaryResponse.getRunSummary().getActualState())) {
                        log.info("lastErrorTime[{}], lastErrorMessage[{}]",
                                getInstanceRunSummaryResponse.getRunSummary().getLastErrorTime(),
                                getInstanceRunSummaryResponse.getRunSummary().getLastErrorMessage());
                        Thread.sleep(1000);
                    } else {
                        break;
                    }
                }
            }
            log.info("batchjob[{}] is terminated.", jobName);
            return true;
        } catch (Exception e) {
            log.error("stopBatchJob({}) failed:{}", jobName, e.getMessage());
        }
        return false;
    }

    public Boolean commitJob(String jobName, Boolean isAutoScaleOn, Integer maxCU, Integer fetchDelaySec) {
        log.debug("begin to commitJob({})", jobName);
        try {
        	if (!isJobExists(jobName)) {
        		throw new QanatBizException("Blink job[" + jobName + "] is not existed");
        	}
            CommitJobRequest commitJobRequest = new CommitJobRequest();
            commitJobRequest.setProjectName(conf.getProjectName());
            commitJobRequest.setJobName(jobName);
            if (isAutoScaleOn != null && isAutoScaleOn) {
	            commitJobRequest.setIsOnOff(true);
	            commitJobRequest.setMaxCU(Float.valueOf(maxCU));
	            commitJobRequest.setConfigure("{\"fetch_delay\":" + fetchDelaySec + "}");
            }
            CommitJobResponse commitJobResponse = getResponse(client, commitJobRequest);
            log.debug("commitJobResponse:{}", JSON.toJSONString(commitJobResponse));
            return true;
        } catch (Exception e) {
            log.error("commitJob({}) failed:{}", jobName, e.getMessage());
            return false;
        }
    }

    public void pauseJob(String jobName) {
        log.debug("begin to pauseJob({})", jobName);
        try {
        	if (!isJobExists(jobName)) {
        		throw new QanatBizException("Blink job[" + jobName + "] is not existed");
        	}
        	if (isBatchJob(jobName)) {
        		throw new QanatBizException("Batch Blink job[" + jobName + "] doesn't support pause");
        	}
        	//停止作业，实例状态机参考文档
            GetInstanceRunSummaryRequest getInstanceRunSummaryRequest = new GetInstanceRunSummaryRequest();
            getInstanceRunSummaryRequest.setProjectName(conf.getProjectName());
            getInstanceRunSummaryRequest.setJobName(jobName);
            getInstanceRunSummaryRequest.setInstanceId(-1L);
            GetInstanceRunSummaryResponse getInstanceRunSummaryResponse = getResponse(client, getInstanceRunSummaryRequest);

            if ("RUNNING".equals(getInstanceRunSummaryResponse.getRunSummary().getActualState())) {
                ModifyInstanceStateRequest modifyInstanceStateRequest = new ModifyInstanceStateRequest();
                modifyInstanceStateRequest.setProjectName(conf.getProjectName());
                modifyInstanceStateRequest.setJobName(jobName);
                modifyInstanceStateRequest.setInstanceId(-1L);
                modifyInstanceStateRequest.setExpectState("PAUSED");
                ModifyInstanceStateResponse modifyInstanceStateResponse = getResponse(client, modifyInstanceStateRequest);
                log.debug("modifyInstanceStateResponse:{}", JSON.toJSONString(modifyInstanceStateResponse));

                //等待停止成功
                while (true) {
                    getInstanceRunSummaryResponse = getInstanceRunSummary(jobName, -1L);
                    log.debug("getInstanceRunSummaryResponse:{}", JSON.toJSONString(getInstanceRunSummaryResponse));

                    if ("RUNNING".equals(getInstanceRunSummaryResponse.getRunSummary().getActualState())) {
                        log.debug("lastErrorTime[{}], lastErrorMessage[{}]",
                                getInstanceRunSummaryResponse.getRunSummary().getLastErrorTime(),
                                getInstanceRunSummaryResponse.getRunSummary().getLastErrorMessage());
                        Thread.sleep(1000);
                    } else {
                        break;
                    }
                }
            }
            log.debug("job[{}] is paused.", jobName);
        } catch (Exception e) {
            log.error("pauseJob({}) failed:{}", jobName, e.getMessage());
        }
        
    }

    public void resumeJob(String jobName) {
        log.debug("begin to resumeJob({})", jobName);
        try {
        	if (!isJobExists(jobName)) {
        		throw new QanatBizException("Blink job[" + jobName + "] is not existed");
        	}
        	if (isBatchJob(jobName)) {
        		throw new QanatBizException("Batch Blink job[" + jobName + "] doesn't support resume");
        	}
        	//停止作业，实例状态机参考文档
            GetInstanceRunSummaryRequest getInstanceRunSummaryRequest = new GetInstanceRunSummaryRequest();
            getInstanceRunSummaryRequest.setProjectName(conf.getProjectName());
            getInstanceRunSummaryRequest.setJobName(jobName);
            getInstanceRunSummaryRequest.setInstanceId(-1L);
            GetInstanceRunSummaryResponse getInstanceRunSummaryResponse = getResponse(client, getInstanceRunSummaryRequest);

            if ("PAUSED".equals(getInstanceRunSummaryResponse.getRunSummary().getActualState())) {
                ModifyInstanceStateRequest modifyInstanceStateRequest = new ModifyInstanceStateRequest();
                modifyInstanceStateRequest.setProjectName(conf.getProjectName());
                modifyInstanceStateRequest.setJobName(jobName);
                modifyInstanceStateRequest.setInstanceId(-1L);
                modifyInstanceStateRequest.setExpectState("RUNNING");
                ModifyInstanceStateResponse modifyInstanceStateResponse = getResponse(client, modifyInstanceStateRequest);
                log.debug("modifyInstanceStateResponse:{}", JSON.toJSONString(modifyInstanceStateResponse));

                //等待停止成功
                while (true) {
                    getInstanceRunSummaryResponse = getInstanceRunSummary(jobName, -1L);
                    log.debug("getInstanceRunSummaryResponse:{}", JSON.toJSONString(getInstanceRunSummaryResponse));

                    if ("PAUSED".equals(getInstanceRunSummaryResponse.getRunSummary().getActualState())) {
                        log.debug("lastErrorTime[{}], lastErrorMessage[{}]",
                        		getInstanceRunSummaryResponse.getRunSummary().getLastErrorTime(),
                                getInstanceRunSummaryResponse.getRunSummary().getLastErrorMessage());
                        Thread.sleep(1000);
                    } else {
                        break;
                    }
                }
            }
            log.debug("job[{}] is resumed.", jobName);
        } catch (Exception e) {
            log.error("resumeJob({}) failed:{}", jobName, e.getMessage());
        }
        
    }

    public void offlineJob(String jobName) {
        log.debug("begin to offlineJob({})", jobName);
        try {
        	if (!isJobExists(jobName)) {
        		throw new QanatBizException("Blink job[" + jobName + "] is not existed");
        	}
        	//停止作业，实例状态机参考文档
            GetInstanceRunSummaryRequest getInstanceRunSummaryRequest = new GetInstanceRunSummaryRequest();
            getInstanceRunSummaryRequest.setProjectName(conf.getProjectName());
            getInstanceRunSummaryRequest.setJobName(jobName);
            if (!isBatchJob(jobName)) {
                getInstanceRunSummaryRequest.setInstanceId(-1L);
            } else {
                getInstanceRunSummaryRequest.setInstanceId(getBatchJobInstanceId(jobName));
            }
            GetInstanceRunSummaryResponse getInstanceRunSummaryResponse = getResponse(client, getInstanceRunSummaryRequest);
            log.debug("getInstanceRunSummaryResponse:{}", JSON.toJSONString(getInstanceRunSummaryResponse));

            if (getInstanceRunSummaryResponse == null || "TERMINATED".equals(getInstanceRunSummaryResponse.getRunSummary().getActualState())
                || "UNKNOWN".equals(getInstanceRunSummaryResponse.getRunSummary().getActualState())) {
                OfflineJobRequest offlineJobRequest = new OfflineJobRequest();
                offlineJobRequest.setProjectName(conf.getProjectName());
                offlineJobRequest.setJobName(jobName);
                OfflineJobResponse offlineJobResponse = getResponse(client, offlineJobRequest);
                log.debug("offlineJobResponse:{}", JSON.toJSONString(offlineJobResponse));
            }
            log.debug("job[{}] is offlined.", jobName);
        } catch (Exception e) {
            log.error("offlineJob({}) failed:{}", jobName, e.getMessage());
        }
        
    }

    public void deleteJob(String jobName) {
        log.debug("begin to deleteJob({})", jobName);
        try {
            if (!isJobExists(jobName)) {
                throw new QanatBizException("Blink job[" + jobName + "] is not existed");
            }
            //停止作业，实例状态机参考文档
            DeleteJobRequest deleteJobRequest = new DeleteJobRequest();
            deleteJobRequest.setProjectName(conf.getProjectName());
            deleteJobRequest.setJobName(jobName);
            DeleteJobResponse deleteJobResponse = getResponse(client, deleteJobRequest);
            log.debug("deleteJobResponse:{}", JSON.toJSONString(deleteJobResponse));
            log.debug("job[{}] is deleted.", jobName);
        } catch (Exception e) {
            log.error("deleteJob({}) failed:{}", jobName, e.getMessage());
        }
        
    }

    public void updateJob(BlinkJobRequest req) {
        log.debug("updateJob({})", req);
        try {
        	if (!isJobExists(req.getJobName())) {
        		throw new QanatBizException("Blink job[" + req.getJobName() + "] is not existed");
        	}
            UpdateJobRequest updateJobRequest = new UpdateJobRequest();
            updateJobRequest.setProjectName(conf.getProjectName());
            updateJobRequest.setJobName(req.getJobName());
            if (StringUtils.isNotBlank(req.getSql())) {
                updateJobRequest.setCode(req.getSql());
            }
            if (StringUtils.isNotBlank(req.getProperties())) {
                updateJobRequest.setProperties(req.getProperties());
            }
            if (StringUtils.isNotBlank(req.getEngineVersion())) {
                updateJobRequest.setEngineVersion(req.getEngineVersion());
            }
            if (StringUtils.isNotBlank(req.getPlanJson())) {
                updateJobRequest.setPlanJson(req.getPlanJson());
            }
            if (StringUtils.isNotBlank(req.getQueueName())) {
                updateJobRequest.setQueueName(req.getQueueName());
            }
            if (StringUtils.isNotBlank(req.getClusterId())) {
                updateJobRequest.setClusterId(req.getClusterId());
            }
            if (StringUtils.isNotBlank(req.getPackages())) {
                updateJobRequest.setPackages(req.getPackages());
            }
            UpdateJobResponse updateJobResponse = getResponse(client, updateJobRequest);
            log.debug("updateJobResponse:{}", JSON.toJSONString(updateJobResponse));
        } catch (Exception e) {
            log.error("updateJob({}) failed:{}", req.getJobName(), e.getMessage());
        } 
    }
    
    public List<Queue> listProjectBindQueueResource(String clusterId, String queueName) {
        log.debug("listProjectBindQueueResource({},{})", clusterId, queueName);
        try {
        	ListProjectBindQueueResourceRequest request = new ListProjectBindQueueResourceRequest();
            request.setProjectName(conf.getProjectName());
            if (StringUtils.isNoneBlank(queueName)) {
            	request.setQueueName(queueName);
            }
            if (StringUtils.isNoneBlank(clusterId)) {
            	request.setClusterId(clusterId);
            }
            ListProjectBindQueueResourceResponse response = getResponse(client, request);
            log.debug("listProjectBindQueueResource:{}", JSON.toJSONString(response));
            return response.getQueues();
        } catch (Exception e) {
            log.error("listProjectBindQueueResource({}) failed:{}", queueName, e.getMessage());
        } 
        return null;
    }
    
    public List<ListProjectBindQueueResponse.Queue> listProjectBindQueue() {
        log.debug("ListProjectBindQueue()");
        try {
        	ListProjectBindQueueRequest request = new ListProjectBindQueueRequest();
            request.setProjectName(conf.getProjectName());
            ListProjectBindQueueResponse response = getResponse(client, request);
            log.debug("listProjectBindQueueResource:{}", JSON.toJSONString(response));
            return response.getQueues();
        } catch (Exception e) {
            log.error("listProjectBindQueueResource() failed:{}", e.getMessage());
        } 
        return null;
    }
    
    public boolean isJobExists(String jobName) {
        log.debug("[{}]begin to isJobExists({})", jobName);
        GetJobResponse.Job job = getJob(jobName);
        if (job != null && StringUtils.isNotBlank(job.getJobName())) {
        	return true;
        } else {
        	return false;
        }
    }
    
    private boolean isBatchJob(String jobName) {
        log.debug("begin to isBatchJob({},{})", jobName);
        GetJobResponse.Job job = getJob(jobName);
        if (job != null && "FLINK_BATCH".equalsIgnoreCase(job.getJobType())) {
        	return true;
        } else {
        	return false;
        }
    }
    
    private <T extends AcsResponse> T getResponse(IAcsClient client, AcsRequest<T> request) {
        AcsResponse response = null;
        try {
            //cn-hangzhou-internal      集团生产
            //cn-hangzhou-internal-pre  集团预发
            //cn-hangzhou-pre           公共云预发
            request.putHeaderParameter("RegionId", conf.getRegionId());
            request.setProtocol("http".equalsIgnoreCase(conf.getProtocolType()) ? ProtocolType.HTTP : ProtocolType.HTTPS);//日常环境需要修改为HTTP协议（如果失败，请使用ProtocolType.HTTPS）
            request.putHeaderParameter("x-acs-bayes-user", conf.getUserId());//六位工号，根据实际填写，必填
            //必须设置为json
            request.setHttpContentType(FormatType.JSON);
            request.setAcceptFormat(FormatType.JSON);

            response = client.getAcsResponse(request);
        } catch (Exception e) {
            //处理自己的异常逻辑，请注意异常中的requestId字段，排查问题时，需要提供该值给服务端
            if (e instanceof ServerException) {
                ServerException serverException = (ServerException) e;
                log.error("bayes requestId:{} errorCode:{} message:{}", serverException.getRequestId(), serverException.getErrCode(), serverException.getMessage());
            } else if (e instanceof ClientException) {
            	ClientException clientException = (ClientException) e;
                log.error("bayes requestId:{} errorCode:{} message:{}", clientException.getRequestId(), clientException.getErrCode(), clientException.getMessage());
            } else {
            	log.error("request bayes failed, error:{}", e.getMessage());
            }
        }
        return (T) response;
    }

	public List<Metric> getInstanceMetric(String jobName, String metricJson) {
		log.debug("getInstanceMetric({},{})", jobName, metricJson);
        try {
        	GetInstanceMetricRequest request = new GetInstanceMetricRequest();
            request.setProjectName(conf.getProjectName());
            request.setJobName(jobName);
            request.setMetricJson(metricJson);
            GetInstanceMetricResponse response = getResponse(client, request);
            log.debug("GetInstanceMetricResponse:{}", JSON.toJSONString(response));
            return response.getMetrics();
        } catch (Exception e) {
            log.error("GetInstanceMetricResponse({}) failed:{}", jobName, e.getMessage());
        } 
        return null;
	}

	public String getInstanceExceptions(String jobName) {
		log.debug("getInstanceExceptions({})", jobName);
        try {
        	GetInstanceExceptionsRequest request = new GetInstanceExceptionsRequest();
            request.setProjectName(conf.getProjectName());
            request.setJobName(jobName);
            request.setInstanceId(-1L);
            GetInstanceExceptionsResponse response = getResponse(client, request);
            log.debug("GetInstanceExceptions:{}", JSON.toJSONString(response));
            return response.getExceptions();
        } catch (Exception e) {
            log.error("GetInstanceExceptions({}) failed:{}", jobName, e.getMessage());
        } 
        return null;
	}

	public GetInstanceResourceResponse.Resource getInstanceResource(String jobName) {
		log.debug("getInstanceResource({})", jobName);
        try {
        	GetInstanceResourceRequest request = new GetInstanceResourceRequest();
            request.setProjectName(conf.getProjectName());
            request.setJobName(jobName);
            if (!isBatchJob(jobName)) {
                request.setInstanceId(-1L);
            } else {
                request.setInstanceId(getBatchJobInstanceId(jobName));
            }
            GetInstanceResourceResponse response = getResponse(client, request);
            log.debug("getInstanceResource:{}", JSON.toJSONString(response));
            return response.getResource();
        } catch (Exception e) {
            log.error("getInstanceResource({}) failed:{}", jobName, e.getMessage());
        } 
        return null;
	}

	public String getInstanceDetail(String jobName) {
		log.debug("getInstanceDetail({})", jobName);
        try {
        	GetInstanceDetailRequest request = new GetInstanceDetailRequest();
            request.setProjectName(conf.getProjectName());
            request.setJobName(jobName);
            request.setInstanceId(-1L);
            GetInstanceDetailResponse response = getResponse(client, request);
            log.debug("getInstanceDetail:{}", JSON.toJSONString(response));
            return response.getDetail();
        } catch (Exception e) {
            log.error("getInstanceDetail({}) failed:{}", jobName, e.getMessage());
        } 
        return null;
	}

	public String getInstanceConfig(String jobName) {
		log.debug("getInstanceConfig({})", jobName);
        try {
        	GetInstanceConfigRequest request = new GetInstanceConfigRequest();
            request.setProjectName(conf.getProjectName());
            request.setJobName(jobName);
            request.setInstanceId(-1L);
            GetInstanceConfigResponse response = getResponse(client, request);
            log.debug("getInstanceDetail:{}", JSON.toJSONString(response));
            return response.getConfig();
        } catch (Exception e) {
            log.error("getInstanceConfig({}) failed:{}", jobName, e.getMessage());
        } 
        return null;
	}
}
