package com.aliyun.wormhole.qanat.process;


import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.odps.OdpsClient;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class OdpsPartitionCheckProcessor extends JavaProcessor {

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
	
	@Override
    public ProcessResult process(JobContext context) {
        JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
        log.info("start to add fields, param=[]", context.getJobParameters());

        String tenantId = paramsJson.getString("tenantId");
        String dbName = paramsJson.getString("dbName");
        String tableName = paramsJson.getString("tableName");
        String partitionPattern = paramsJson.getString("partitionPattern");
        
        DbInfoExample dbExample = new DbInfoExample();
        dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName).andTenantIdEqualTo(tenantId);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("db:" + dbName + " is not found");
        }
        DbInfo dbInfo = dbs.get(0);
        JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
        
        OdpsClient client = new OdpsClient(dbMetaJson.getString("odpsServer"), dbMetaJson.getString("accessId"), dbMetaJson.getString("accessKey"),
        		dbMetaJson.getString("project"), null, null);
	    String maxPt = client.getMaxPt(tableName);
	    
	    String expectBizDate = null;
	    if ("T-1D".equalsIgnoreCase(partitionPattern)) {
		    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		    Calendar cal = Calendar.getInstance();
		    cal.add(Calendar.DAY_OF_YEAR, -1);
		    expectBizDate = sdf.format(cal.getTime());
	    }
        log.info("table:{} maxPt:{} expectBizDate:{}", dbMetaJson.getString("project") + "." + tableName, maxPt, expectBizDate);
        
        if (!maxPt.equalsIgnoreCase(expectBizDate)) {
        	log.error("odps_partition_check {} ds={}", dbMetaJson.getString("project") + "." + tableName, maxPt);
        }
            
            
        return new ProcessResult(true);
	}
    
    @Override
    public void kill(JobContext context) {
        
    }
}