package com.aliyun.wormhole.qanat.service.flink;

import java.util.Date;
import java.util.Map;

public interface FlinkService {

	boolean startJob(String tenantId, String appName, String jobName, Date startTime);

	boolean startJob(String tenantId, String appName, String jobName, Date startTime, Map<String, String> params);

	String getJobStatus(String tenantId, String appName, String jobName);

	boolean startBatchJob(String tenantId, String appName, String jobName, Map<String, String> params);

	void stopJob(String tenantId, String appName, String jobName) throws Exception;

	void restartJob(String tenantId, String appName, String jobName, Date startTime, Boolean isBatch) throws Exception;

	void restartJob(String tenantId, String appName, String jobName, Map<String, String> params);

	void createJob(String tenantId, String appName, String jobName, String sql, Boolean isBatch);
}
