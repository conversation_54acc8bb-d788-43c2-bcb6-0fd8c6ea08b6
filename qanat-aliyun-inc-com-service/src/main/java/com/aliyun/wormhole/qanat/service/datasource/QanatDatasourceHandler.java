package com.aliyun.wormhole.qanat.service.datasource;

import java.sql.Connection;
import java.util.List;

import com.alibaba.druid.pool.DruidDataSource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.sql.DataSource;

@Slf4j
@Component
public class QanatDatasourceHandler {

    @Resource
    private DbInfoMapper dbInfoMapper;

    @Value("${region.type:}")
    private String regionType;

    @Value("${environment.type:}")
    private String environmentType;

    @Value("${qanat.db.oxs.enabled:false}")
    private boolean oxsEnabled;

    private static final ConnectionPool<DruidDataSource> dataSourcePool = new ConnectionPool<>();
    
    public Connection connectToTable(RdsConnectionParam param) {
        DruidDataSource dataSource = null;
        try {
            synchronized (QanatDatasourceHandler.class) {
                String key = param.getUrl() + "@" + param.getUsername();
                if (dataSourcePool.contains(key)) {
                    dataSource = dataSourcePool.get(key);
                } else {
                    dataSource = param.buildDataSource();
                    dataSourcePool.put(key, dataSource);
                }
                return dataSource.getConnection();
            }
        } catch (Exception e) {
            try {
                dataSource = param.buildDataSource();
                dataSourcePool.put(param.getUrl(), dataSource);
                return dataSource.getConnection();
            } catch(Exception ex) {
            	throw new RuntimeException("failed to get connection for db:" + param.getUrl(), ex);
            }
        }
    }

    public void closeDataSource(RdsConnectionParam param) {
        if (dataSourcePool.get(param.getUrl()) != null) {
            dataSourcePool.get(param.getUrl()).close();
            dataSourcePool.remove(param.getUrl());
        }
    }

    public DataSource getDatasourceByDbName(String dbName){
        DbInfoExample dbExample = new DbInfoExample();
        dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName).andTenantIdEqualTo("1");
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new RuntimeException("db not found");
        }
        DbInfo dbInfo = dbs.get(0);
        JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
        
        RdsConnectionParam param = new RdsConnectionParam();
        
        String jdbcUrl = selectJdbcUrlByRegion(dbMetaJson);
        param.setUrl(jdbcUrl);
        
        param.setPassword(dbMetaJson.getString("password"));
        param.setUserName(dbMetaJson.getString("username"));
        param.setRemoveAbandonedTimeout(60*10);
        param.setMaxWait(60000);
        return getDataSource(param);
    }

    private String selectJdbcUrlByRegion(JSONObject dbMetaJson) {
        if (oxsEnabled && "singapore".equalsIgnoreCase(regionType) && 
            StringUtils.containsIgnoreCase(environmentType, "oxs") && 
            dbMetaJson.containsKey("oxsJdbcUrl")) {
            
            String oxsJdbcUrl = dbMetaJson.getString("oxsJdbcUrl");
            if (StringUtils.isNotBlank(oxsJdbcUrl)) {
                log.info("Selected OXS JDBC URL for region: {} env: {}", regionType, environmentType);
                return oxsJdbcUrl;
            }
        }
        return dbMetaJson.getString("jdbcUrl");
    }

    private DataSource getDataSource(RdsConnectionParam param) {
        DruidDataSource dataSource = null;
        try {
            synchronized (QanatDatasourceHandler.class) {
                if (dataSourcePool.contains(param.getUrl())) {
                    dataSource = dataSourcePool.get(param.getUrl());
                } else {
                    dataSource = param.buildDataSource();
                    dataSourcePool.put(param.getUrl(), dataSource);
                }
                return dataSource;
            }
        } catch (Exception e) {
            try {
                dataSource = param.buildDataSource();
                dataSourcePool.put(param.getUrl(), dataSource);
                return dataSource;
            } catch(Exception ex) {
                throw new RuntimeException("failed to get connection for db:" + param.getUrl(), ex);
            }
        }
    }
}
