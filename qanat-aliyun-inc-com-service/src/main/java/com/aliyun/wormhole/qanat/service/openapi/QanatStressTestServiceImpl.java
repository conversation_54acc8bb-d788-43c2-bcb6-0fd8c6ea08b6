package com.aliyun.wormhole.qanat.service.openapi;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.security.SecurityUtil;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.openapi.QanatSqlExecuteService;
import com.aliyun.wormhole.qanat.openapi.model.ApiResult;
import com.aliyun.wormhole.qanat.openapi.model.SqlExecuteRequest;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.*;
import java.text.DateFormat;
import java.util.*;

@Slf4j
@Component
@HSFProvider(serviceInterface = QanatStressTestService.class)
public class QanatStressTestServiceImpl extends OpenApiBase implements QanatStressTestService {

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private DbInfoMapper dbInfoMapper;

    private Map<String, List<Object>> testDataMap = Collections.synchronizedMap(new HashMap<>());

    @Override
    public ApiResult<List<Map<String, Object>>> execute(StressTestRequest request) {
        log.info("begin execute({})", JSON.toJSONString(request));
        long startTs = System.currentTimeMillis();
        ApiResult<List<Map<String, Object>>> result = new ApiResult<>();
        List<Map<String, Object>> dataList = new ArrayList<>();
        result.setCode("200");
        result.setSuccess(true);
        result.setData(dataList);
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        DateFormat format = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            if (!checkAccessKey(request)) {
                throw new QanatBizException("AK failed");
            }
            JSONObject dbMetaJson = this.getAdbDbMeta(request.getDbName());
            if (dbMetaJson == null) {
                return null;
            }
            long connStartTs = System.currentTimeMillis();
            RdsConnectionParam param = new RdsConnectionParam();
            param.setUrl(dbMetaJson.getString("jdbcUrl")).setUserName(dbMetaJson.getString("username")).setPassword(dbMetaJson.getString("password"));
            connection = dsHandler.connectToTable(param);
            log.info("[{}]get db conn cost:{}", request.getRequestId(), System.currentTimeMillis() - connStartTs);
            statement = connection.prepareStatement(SecurityUtil.escapeSql(request.getSql()));

            if (StringUtils.isNotBlank(request.getTestDataSql())) {
                if (CollectionUtils.isEmpty(testDataMap.get(request.getTestDataSql()))) {
                    List<Object> testDataList = Collections.synchronizedList(new ArrayList<>());
                    testDataMap.put(request.getTestDataSql(), testDataList);
                    Long dataInitTs = System.currentTimeMillis();
                    PreparedStatement dataPrepareStatement = connection.prepareStatement(SecurityUtil.escapeSql(request.getTestDataSql()));
                    ResultSet dataPrepareResultSet = dataPrepareStatement.executeQuery();
                    while (dataPrepareResultSet.next()) {
                        testDataList.add(dataPrepareResultSet.getObject(1));
                    }
                    log.info("testDataList is inited:{}, cost:{}", testDataList.size(), System.currentTimeMillis() - dataInitTs);
                    dataPrepareResultSet.close();
                    dataPrepareStatement.close();
                }
                Object val = testDataMap.get(request.getTestDataSql()).get(new Random().nextInt(testDataMap.get(request.getTestDataSql()).size() - 1));
                log.info("sql:{}, val:{}", request.getSql(), val);
                statement.setObject(1, val);
            } else if (request.getParams() != null && request.getParams().size() > 0) {
                for (int i = 0; i < request.getParams().size(); i++) {
                    statement.setObject(i + 1, request.getParams().get(i));
                }
            }
            long execStartTs = System.currentTimeMillis();
            resultSet = statement.executeQuery();
            log.info("[{}]db exec cost:{}", request.getRequestId(), System.currentTimeMillis() - execStartTs);
            while (resultSet.next()) {
                Map<String, Object> data = new HashMap<>();
                dataList.add(data);
                int count = resultSet.getMetaData().getColumnCount();
                for (int idx = 1; idx <= count; idx++) {
                    String val, columnClassName = resultSet.getMetaData().getColumnClassName(idx);

                    if (StringUtils.equals("java.sql.Timestamp", columnClassName)) {
                        Timestamp timestamp = resultSet.getTimestamp(idx);

                        if (null != timestamp) {
                            val = format.format(timestamp);
                        } else {
                            val = "";
                        }
                    } else {
                        val = resultSet.getString(idx);
                    }

                    if (null == val) {
                        val = "";
                    }
                    data.put(resultSet.getMetaData().getColumnLabel(idx), val);
                }
            }
            log.info("[{}]sql exec finished, cost:{}", request.getRequestId(), System.currentTimeMillis() - startTs);
        } catch (Exception e) {
            log.error("[{}]sql exec failed, error:{}", request.getRequestId(), e.getMessage(), e);
            result.setCode("500");
            result.setSuccess(false);
            result.setData(null);
            result.setMessage(e.getMessage());
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                    resultSet = null;
                }
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    statement = null;
                }
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    connection = null;
                }
            }
        }
        return result;
    }

    private JSONObject getAdbDbMeta(String dbName) {
        DbInfoExample example = new DbInfoExample();
        example.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("no db found");
        }
        String dbMeta = dbs.get(0).getMeta();
        JSONObject dbMetaJson = JSON.parseObject(dbMeta);
        return dbMetaJson;
    }
}
