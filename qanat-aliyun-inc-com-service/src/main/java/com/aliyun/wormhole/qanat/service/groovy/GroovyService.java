package com.aliyun.wormhole.qanat.service.groovy;

import com.alibaba.fastjson.JSON;
import com.aliyun.securitysdk.sandbox.Matcher;
import com.aliyun.securitysdk.sandbox.Sandbox;
import com.aliyun.securitysdk.sandbox.SandboxCallable;
import com.aliyun.securitysdk.sandbox.SandboxConfiguration;

import groovy.lang.Binding;
import groovy.lang.GroovyShell;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GroovyService {
    
    public String evalDag(String script) {
        log.info("evalDag script={}", script);
        
        SandboxConfiguration conf = new SandboxConfiguration();
        conf.allowFileOperation(Matcher.of("/home/<USER>/qanat-aliyun-inc-com/target/qanat-aliyun-inc-com/BOOT-INF/lib/groovy-all-2.4.16.jar"));
        Sandbox sandbox = new Sandbox(conf);
        String json = "";
        try {
            GroovyShell shell = new GroovyShell();
            Object result = sandbox.execute(new SandboxCallable<Object>() {
                @Override
                public Object call() throws Exception {
		            Object obj = shell.evaluate(
		                "import com.aliyun.wormhole.qanat.api.dag.*;\n" + 
		                "import com.alibaba.fastjson.*;\n" + 
		                "import java.util.*;\n" + 
		                " \n" +
		                script);
		            return obj;
                }
            });
            json = JSON.toJSONString(result);
            log.info("evalDag json={}", json);
            return json;
        } catch (SecurityException e) {
            log.error("evalDag failed in sandbox, e={}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("evalDag failed, e={}", e.getMessage(), e);
        }
        return json;
    }
}
