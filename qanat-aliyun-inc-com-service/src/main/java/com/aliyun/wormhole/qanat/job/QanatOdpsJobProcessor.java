package com.aliyun.wormhole.qanat.job;

import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.DagInstStatus;
import com.aliyun.wormhole.qanat.api.dag.OdpsNode;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelation;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.ResourceExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.mapper.AppResourceRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ResourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import com.aliyun.wormhole.qanat.service.dag.DagService;
import com.aliyun.wormhole.qanat.service.odps.OdpsClient;
import com.taobao.unifiedsession.core.commons.utils.DateUtils;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

/**
 * Blink任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatOdpsJobProcessor extends JavaProcessor {
    
    @Resource
    private TaskInfoMapper taskInfoMapper;
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;
	
	@Resource
	private AppResourceRelationMapper appResourceRelationMapper;
	
	@Resource
	private ResourceMapper resourceMapper;
    
    @Resource
    private DagService dagService;

    @Override
    public ProcessResult process(JobContext context) {
        String taskName = context.getTaskName();
        log.info("Qanat Odps Job[{}] start.", taskName);
        try {
            Map<String, Object> instParamsMap = null;
            if (StringUtils.isNotBlank(context.getInstanceParameters())) {
                instParamsMap = (Map<String, Object>)JSON.parseObject(context.getInstanceParameters(), Map.class);
            }
            if (instParamsMap == null) {
                instParamsMap = (Map<String, Object>)JSON.parseObject(context.getJobParameters(), Map.class);
            }
            JSONObject nodeJson = (JSONObject)instParamsMap.get("node");
            OdpsNode odpsNode = (OdpsNode)dagService.getNodeByJSONObject(nodeJson);
            String operator = (String)instParamsMap.get("operator");
            String requestId = (String)instParamsMap.get("requestId");
            Long taskInstId = Long.valueOf(String.valueOf(instParamsMap.get("taskInstId")));
            String subTaskInstId = String.valueOf(instParamsMap.get("subTaskInstId"));
            String tenantId = String.valueOf(instParamsMap.get("tenantId"));
            String appName = String.valueOf(instParamsMap.get("appName"));
            
            TaskInstance taskInstUpd = new TaskInstance();
            taskInstUpd.setId(Long.valueOf(subTaskInstId));
            taskInstUpd.setGmtModified(new Date());
            taskInstUpd.setModifyEmpid(operator);
            taskInstUpd.setExternalInstId(context.getJobInstanceId() + "");//SchedulerX任务实例id
            taskInstUpd.setStatus(DagInstStatus.EXECUTING.getCode().byteValue());
            taskInstUpd.setHostAddr(InetAddress.getLocalHost().getHostAddress());
            taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);

            String dsFormat = odpsNode.getDstDsFormat() == null ? "yyyyMMdd" : odpsNode.getDstDsFormat();
            String bizDate = null;
            Date today = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat(dsFormat);
            bizDate = sdf.format(DateUtils.addDay(today, -1));
            DateTime dt = context.getDataTime();
            if (dt != null) {
                bizDate = dt.toString(dsFormat);
            }
            
            AppResourceRelationExample example = new AppResourceRelationExample();
        	example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andRelationTypeEqualTo("odps");
        	List<AppResourceRelation> rels = appResourceRelationMapper.selectByExample(example);
        	if (CollectionUtils.isEmpty(rels)) {
        		throw new QanatBizException("no app resouces");
        	}
        	AppResourceRelation ref = rels.get(0);
        	ResourceExample example1 = new ResourceExample();
        	example1.createCriteria().andResourceNameEqualTo(ref.getResourceName()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L);
        	List<com.aliyun.wormhole.qanat.dal.domain.Resource> resources = resourceMapper.selectByExampleWithBLOBs(example1);
        	if (CollectionUtils.isEmpty(resources)) {
        		throw new QanatBizException("no app resouces");
        	}
        	com.aliyun.wormhole.qanat.dal.domain.Resource resource = resources.get(0);
        	JSONObject metaJson = JSON.parseObject(resource.getMeta());
    	    OdpsClient client = new OdpsClient(metaJson.getString("endpoint"), metaJson.getString("accessId"), metaJson.getString("accessKey"),
    	    		metaJson.getString("project"), metaJson.getString("mcUrl"), metaJson.getString("mcToken"));
    	    
            log.info("[{}]sql={}", requestId, odpsNode.getOdpsSql());
            long startTs = System.currentTimeMillis();
            String logview = client.queryOdpsSql(odpsNode.getOdpsSql().replaceAll("#bizdate#", bizDate));
            log.info("[{}]sql exec finished using {} ms", requestId, System.currentTimeMillis()-startTs);
            log.info("Qanat Odps logview:{}", logview);
            
            //全局任务参数更新到主任务实例的参数中
            if (odpsNode.isDataBaseline()) {
                TaskInstance taskInst = taskInstanceMapper.selectByPrimaryKey(taskInstId);
                JSONObject execParam = JSON.parseObject(taskInst.getExecParam());
                SimpleDateFormat tsSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                execParam.put("incr_sync_start_time", tsSdf.format(new Date(startTs)));
                TaskInstance mainTaskInstUpd = new TaskInstance();
                mainTaskInstUpd.setId(taskInstId);
                mainTaskInstUpd.setExecParam(JSON.toJSONString(execParam));
                mainTaskInstUpd.setGmtModified(new Date());
                mainTaskInstUpd.setModifyEmpid(operator);
                taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
            }
            
            taskInstUpd = new TaskInstance();
            taskInstUpd.setId(Long.valueOf(subTaskInstId));
            taskInstUpd.setGmtModified(new Date());
            taskInstUpd.setEndTime(new Date());
            taskInstUpd.setModifyEmpid(operator);
            taskInstUpd.setStatus(DagInstStatus.SUCCESS.getCode().byteValue());
            taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
        } catch (QanatBizException e) {
            log.error("Odps任务调度异常:{}", e.getMessage());
            return new ProcessResult(false);
        } catch (Exception e) {
            log.error("Odps任务调度异常", e);
            return new ProcessResult(false);
        }
        return new ProcessResult(true);
    }
    
    @Override
    public void kill(JobContext context) {
        // TODO Auto-generated method stub
    }
}