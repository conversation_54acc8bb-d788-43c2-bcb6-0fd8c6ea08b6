package com.aliyun.wormhole.qanat.service.viewmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.Extension;
import com.aliyun.wormhole.qanat.dal.domain.ExtensionExample;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfo;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ExtensionMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TenantInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelVersionMapper;
import com.aliyun.wormhole.qanat.service.util.HoloUtils;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.DataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.RelatedDataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Relation;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ViewModelSqlBuilder {
    
    @Resource
    private DatasourceMapper datasourceMapper;
    
    @Resource
    private ExtensionMapper extensionMapper;
    
    @Resource
    private DatasourceService datasourceService;
    
    @Resource
    private DsRelationMapper dsRelationMapper;
    
    @Resource
    private ViewModelInfoMapper viewModelInfoMapper;
    
    @Resource
    private ViewModelVersionMapper viewModelVersionMapper;
    
    @Resource
    private ViewModelOptimizer viewModelOptimizer;
    
    @Resource
    private LookupProcessor lookupProcessor;
    
    @Resource
    private DatasourceService dsInfoService;
    
    @Resource
    private TenantInfoMapper tenantInfoMapper;
    
    @Resource
    private DbInfoMapper dbInfoMapper;

	public String getLoadDataSql4Adb(String tenantId, ViewModel dataModel, String tableName, String dbType) {
		log.info("getLoadDataSql4Adb({},{},{})", tenantId, tableName, dbType);
		tableName = StringUtils.isBlank(tableName) ? dataModel.getCode() : tableName;
		List<ViewModel.Field> columnList = getColumnsByModel(dataModel);
        List<String> fieldList = new ArrayList<>();
        for (int i = 0; i < columnList.size(); i++) {
        	ViewModel.Field field = columnList.get(i);
			if (field.isFunc()) {
				continue;
			}
            fieldList.add(getDbFieldEscape(dbType) + field.getCode() + getDbFieldEscape(dbType));
        }
		StringBuffer sql = new StringBuffer();
		if (dataModel.getSettings().isSqlOptimize() && "adb3".equalsIgnoreCase(dbType)) {
			sql.append("/*+reorder_joins=false,join_distribution_type=replicated*/");
		}
		sql.append("INSERT INTO " + tableName + " (" + StringUtils.join(fieldList, ",") + ") ");
		sql.append(getSelectSql(tenantId, dataModel, dbType));
		return sql.toString();
	}

	private static String getDbFieldEscape(String dbType) {
		if ("adb3".equalsIgnoreCase(dbType)) {
			return "`";
		} else if ("hologres".equalsIgnoreCase(dbType)) {
			return "\"";
		} else {
			return "";
		}
	}
    
    public String getSelectSql(String tenantId, ViewModel dataModel, String dbType) {
        StringBuffer sql = new StringBuffer();
        List<String> fields = getColumnsByModelWithAlias(dataModel, dbType);
        sql.append("SELECT DISTINCT ");
        sql.append(StringUtils.join(fields, ","));
        sql.append(" FROM ");
        
        sql.append(getSubQueryFromObject(tenantId, dataModel.getObject(), dbType));
		List<ViewModel.Field> mainOjbectFields = dataModel.getObject().getFields();
        Map<String, ViewModel.RelatedDataObject> filedRefObjects = new LinkedHashMap<>();
		for (int i = 0; i < mainOjbectFields.size(); i++) {
        	ViewModel.Field field = mainOjbectFields.get(i);
        	if (field.getObject() != null) {
        		filedRefObjects.put(field.getCode(), field.getObject());
        	}
		}
		
		if (CollectionUtils.isNotEmpty(filedRefObjects.keySet())) {
        	for (String fieldName : filedRefObjects.keySet()) {
        		sql.append(" LEFT JOIN ");
        		sql.append(getSubQueryFromFieldWithRefObj(tenantId, fieldName, filedRefObjects.get(fieldName), dbType));
        		sql.append(getJoinOnSql(tenantId, filedRefObjects.get(fieldName), dataModel.getObject()));
        	}
        }
        
        if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
        	for (ViewModel.RelatedDataObject relatedObject : dataModel.getRelatedObjects()) {
        		sql.append(" " + relatedObject.getRelationType() + " ");
        		sql.append(getSubQueryFromObject(tenantId, relatedObject, dbType));
        		sql.append(getJoinOnSql(tenantId, relatedObject, dataModel.getObject()));
        	}
        }
        
        return sql.toString();
    }
    
    public String getSubQeuryFromMainObject(String tenantId, ViewModel.DataObject object, String dbType) {
    	StringBuffer sql = new StringBuffer();
		List<ViewModel.Field> mainOjbectFields = object.getFields();
		List<String> selectColumns = new ArrayList<>();
		selectColumns.addAll(getSelectFieldsWithAliasForMainObject(object, dbType));
		
		sql.append(" ( SELECT " + StringUtils.join(selectColumns, ",") + " FROM ");
		
		sql.append(getSubQueryFromObject(tenantId, object, dbType));
        
        Map<String, ViewModel.RelatedDataObject> filedRefObjects = new LinkedHashMap<>();
		for (int i = 0; i < mainOjbectFields.size(); i++) {
        	ViewModel.Field field = mainOjbectFields.get(i);
        	if (field.getObject() != null) {
        		filedRefObjects.put(field.getCode(), field.getObject());
        	}
		}
		
		if (CollectionUtils.isNotEmpty(filedRefObjects.keySet())) {
        	for (String fieldName : filedRefObjects.keySet()) {
        		sql.append(" LEFT JOIN ");
        		sql.append(getSubQueryFromFieldWithRefObj(tenantId, fieldName, filedRefObjects.get(fieldName), dbType));
        		sql.append(getJoinOnSql(tenantId, filedRefObjects.get(fieldName), object));
        	}
        }
		sql.append(" ) AS " + object.getCode() + " ");
		return sql.toString();
	}
    
    private static List<String> getSelectFieldsWithAliasForMainObject(ViewModel.DataObject object, String dbType) {
    	List<ViewModel.Field> mainOjbectFields = object.getFields();
		List<String> selectColumns = new ArrayList<>();
	    for (int i = 0; i < mainOjbectFields.size(); i++) {
	    	ViewModel.Field field = mainOjbectFields.get(i);
			if (field.isFunc()) {
				continue;
			}
	    	if (field.getObject() != null) {
	    		selectColumns.add(field.getObject().getCode() + "." + getDbFieldEscape(dbType) + field.getCode() + getDbFieldEscape(dbType));
	    	} else {
	    		selectColumns.add(object.getCode() + "." + getDbFieldEscape(dbType) + field.getCode() + getDbFieldEscape(dbType));
	    	}
	    }
	    return selectColumns;
    }

	public String getArrayField(ViewModel.RelatedDataObject object, String dbType) {
		List<ViewModel.Field> arrayFields = new ArrayList<>();
		List<String> joinOnColumns = object.getRelations().stream().map(e -> e.getField()).collect(Collectors.toList());
		for (ViewModel.Field field : object.getFields()) {
			if (joinOnColumns.contains(field.getCode())) {
				continue;
			}
			arrayFields.add(field);
		}
		if (arrayFields.size() == 1) {
			return arrayFields.get(0).getCode();
		} else {
			String clause = "CONCAT(";
			for (int i=0; i < arrayFields.size(); i++) {
				ViewModel.Field field = arrayFields.get(i);
				clause += "IFNULL(" + field.getCode() + ",'')";
				if (i < (arrayFields.size() - 1)) {
					clause += ",'" + getDbFieldEscape(dbType) + "',";
				}
			}
			clause += ")";
			return clause;
		}
	}

	private String getJoinOnSql(String tenantId, RelatedDataObject relatedObject, DataObject mainObject) {
		List<String> joinOnConditions = new ArrayList<>();
		if ("component".equals(relatedObject.getType()) && CollectionUtils.isEmpty(relatedObject.getRelations())) {
			joinOnConditions.add(relatedObject.getCode() + "." + getJoinField(tenantId, relatedObject.getRef()) + " = " + mainObject.getCode() + "." + getPkField(mainObject).getCode());
		} else {
			for (ViewModel.Relation relation : relatedObject.getRelations()) {
				joinOnConditions.add(relatedObject.getCode() + "." + relation.getField() + " " + (StringUtils.isBlank(relation.getOp()) ? "=" : relation.getOp()) + " " + relation.getRelatedField().replace("exp#", ""));
			}
		}
		return " ON " + StringUtils.join(joinOnConditions, " AND ");
	}

	private String getJoinField(String tenantId, String componentCode) {
		TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(tenantId);
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + tenantId + " is not configured");
    	}

    	DbInfoExample dbInfoExample = new DbInfoExample();
    	dbInfoExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDbNameEqualTo(tenantList.get(0).getDefaultDw());
    	List<DbInfo> dbInfos = dbInfoMapper.selectByExample(dbInfoExample);
    	if (CollectionUtils.isEmpty(dbInfos)) {
    		throw new QanatBizException("DbInfo not found:" + tenantList.get(0).getDefaultDw());
    	}
    	String dbType = dbInfos.get(0).getDbType();
		ExtensionExample example = new ExtensionExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + dbType).andPluginEqualTo(componentCode);
		List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isNotEmpty(exts)) {
			return getObjectPk(tenantId, exts.get(0).getObjectType(), exts.get(0).getVersion());
		}
		log.error("no component conf found, objectCode:{}", componentCode);
		throw new QanatBizException("no component conf found");
	}

	private String getObjectPk(String tenantId, String objectType, String objectUniqueCode) {
		return datasourceService.getPkFieldByObjectType(tenantId, objectType, objectUniqueCode);
	}

	public String getSubQueryFromObject(String tenantId, ViewModel.DataObject object, String dbType) {
    	StringBuffer sql = new StringBuffer();
    	if ("metadata".equalsIgnoreCase(object.getType())) {
    		Datasource objDsInfo = getDsInfoByObjectCode(tenantId, object.getRef());
    		JSONObject odsDsMetaJson = datasourceService.getOdsTableMetaByDsName(tenantId, objDsInfo.getDsName());
    		if (odsDsMetaJson.containsKey("storeType") && "slot".equalsIgnoreCase(odsDsMetaJson.getString("storeType")) && odsDsMetaJson.getJSONObject("slotMeta") != null) {
    			String tableName = odsDsMetaJson.getString("table");
    			JSONObject fieldsJson = odsDsMetaJson.getJSONObject("slotMeta").getJSONObject("fields");
	    		List<String> objPkFields = object.getFields().stream().filter(item -> item.isPk()).map(item -> item.getCode()).collect(Collectors.toList());
		    	sql.append(" (SELECT object_id AS ").append(objPkFields.get(0));
		        List<ViewModel.Field> fields = object.getFields();
		        for (int i = 0; i < fields.size(); i++) {
		        	ViewModel.Field field = fields.get(i);
		        	if (field.getObject() != null || field.isPk()) {
		        		continue;
		        	}
		            if (i > 0) {
		                sql.append(",");
		            }
		            if (StringUtils.isNotBlank(field.getType())) {
		                sql.append("CAST(");
		            }
		            sql.append(fieldsJson.getString(field.getRef()));
		            if (StringUtils.isNotBlank(field.getType())) {
		                sql.append(" AS " + field.getType() + ")");
		            }
		            sql.append(" AS " + getDbFieldEscape(dbType) + field.getCode() + getDbFieldEscape(dbType));
		        }
		        sql.append(" FROM ");
		        sql.append(tableName);
		        sql.append(" WHERE ");
		        sql.append("object_code = '" + object.getRef() + "'");
		        sql.append(" AND is_deleted = 0 AND tenant_id = 'aliyun' ");
		        sql.append(") AS " + object.getCode() + " ");
    		} else {
	    		String tableName = "tag_meta_tag_object_biz_relation";
	    		if (odsDsMetaJson != null && odsDsMetaJson.getString("table") != null) {
	    			tableName = odsDsMetaJson.getString("table");
	    		}
	    		List<String> objPkFields = object.getFields().stream().filter(item -> item.isPk()).map(item -> item.getCode()).collect(Collectors.toList());
		    	sql.append(" (SELECT object_biz_id AS ").append(objPkFields.get(0));
		        List<ViewModel.Field> fields = object.getFields();
		        for (int i = 0; i < fields.size(); i++) {
		        	ViewModel.Field field = fields.get(i);
		        	if (field.getObject() != null || field.isPk()) {
		        		continue;
		        	}
		            if (i > 0) {
		                sql.append(",");
		            }
		            if (StringUtils.isNotBlank(field.getType())) {
		                sql.append("CAST(");
		            }
		            if (StringUtils.isNotBlank(field.getRef())) {
		                sql.append("MAX(CASE WHEN tag_unique_code = '" + field.getRef() + "' THEN tag_value ELSE NULL END)");
		            } else {
		                sql.append("MAX(CASE WHEN tag_code = '" + field.getCode() + "' THEN tag_value ELSE NULL END)");
		            }
		            if (StringUtils.isNotBlank(field.getType())) {
		                sql.append(" AS " + field.getType() + ")");
		            }
		            sql.append(" AS " + getDbFieldEscape(dbType) + field.getCode() + getDbFieldEscape(dbType));
		            if (field.isEnums()) {
		                if (StringUtils.isNotBlank(field.getRef())) {
		                    sql.append(",MAX(CASE WHEN tag_unique_code = '" + field.getRef() + "' THEN tag_value_zh ELSE NULL END) AS " + field.getCode() + "_desc");
		                } else {
		                    sql.append(",MAX(CASE WHEN tag_code = '" + field.getCode() + "' THEN tag_value_zh ELSE NULL END) AS " + field.getCode() + "_desc");
		                }
		            }
		        }
		        sql.append(" FROM (");
		        sql.append("SELECT a.* FROM " + tableName + " as a inner join tag_domain_meta_permission as b on a.object_unique_code=b.object_unique_code AND a.tag_unique_code=b.tag_unique_code and b.is_deleted=0 AND a.is_deleted = 0");
		        sql.append(" AND a.object_unique_code = '" + object.getRef() + "'");
		        sql.append(") GROUP BY object_biz_id) AS " + object.getCode() + " ");
    		}
    	} else if ("table".equalsIgnoreCase(object.getType())) {
    		sql.append(" (SELECT ");
	        List<ViewModel.Field> fields = object.getFields();
	        for (int i = 0; i < fields.size(); i++) {
	        	ViewModel.Field field = fields.get(i);
	        	if (field.getObject() != null) {
	        		continue;
	        	}
	            if (i > 0) {
	                sql.append(",");
	            }
	            if (field.isFunc()) {
	            	sql.append(" NULL AS " + field.getCode() + " ");
	            } else if ("datetime".equalsIgnoreCase(field.getType()) && "adb3".equalsIgnoreCase(dbType)) {
	            	sql.append("DATE_FORMAT(" + (StringUtils.isNotBlank(field.getRef()) ? field.getRef() : field.getCode()) + ",'%Y-%m-%d %H:%i:%s') AS " + field.getCode() + " ");
	            } else if (StringUtils.isNotBlank(field.getType())) {
	            	if (dbType == null || "adb3".equalsIgnoreCase(dbType)) {
	            		sql.append("CAST(" + (StringUtils.isNotBlank(field.getExpr()) ? field.getExpr() : (StringUtils.isNotBlank(field.getRef()) ? field.getRef() : field.getCode())) + " AS " +field.getType() + " ) AS " + field.getCode() + " ");
	            	} else if ("hologres".equalsIgnoreCase(dbType)) {
	            		sql.append((StringUtils.isNotBlank(field.getExpr()) ? field.getExpr() : (StringUtils.isNotBlank(field.getRef()) ? field.getRef() : field.getCode())) + "::" +  HoloUtils.getHoloTypeFromMysql(field.getType()) + " AS " + field.getCode() + " ");
	            	}
	            } else {
	            	sql.append((StringUtils.isNotBlank(field.getRef()) ? field.getRef() : field.getCode()) + " AS " + field.getCode() + " ");
	            }
	        }
	        sql.append(" FROM ");
	        sql.append(getOdsTableName(tenantId, object.getRef()) + (StringUtils.isNotBlank(object.getFilter()) ? (" WHERE " + object.getFilter()) : "") + " ) AS " + object.getCode());
    	} else if ("component".equalsIgnoreCase(object.getType())) {
    		sql.append(" (SELECT ");
	        List<ViewModel.Field> fields = object.getFields();
	        for (int i = 0; i < fields.size(); i++) {
	        	ViewModel.Field field = fields.get(i);
	        	if (field.getObject() != null) {
	        		continue;
	        	}
	            if (i > 0) {
	                sql.append(",");
	            }
	            if (field.isFunc()) {
	            	sql.append(" NULL AS " + field.getCode() + " ");
	            } else if ("datetime".equalsIgnoreCase(field.getType()) && "adb3".equalsIgnoreCase(dbType)) {
	            	sql.append("DATE_FORMAT(" + (StringUtils.isNotBlank(field.getRef()) ? field.getRef() : field.getCode()) + ",'%Y-%m-%d %H:%i:%s') AS " + field.getCode() + " ");
	            } else {
	            	sql.append((StringUtils.isNotBlank(field.getRef()) ? field.getRef() : field.getCode()) + " AS " + field.getCode());
	            }
	        }
	        sql.append(" FROM (");
	        sql.append(getSqlFromComponenet(tenantId, object.getRef(), dbType) + (StringUtils.isNotBlank(object.getFilter()) ? (" WHERE " + object.getFilter()) : "") + " ) AS _" + object.getCode() + ") AS " + object.getCode());
    	}
        return sql.toString();
    }

	public String getSubQueryFromObjectForLookup(String tenantId, ViewModel.DataObject object, String dbType) {
    	StringBuffer sql = new StringBuffer();
    	if ("metadata".equalsIgnoreCase(object.getType())) {
    		Datasource objDsInfo = getDsInfoByObjectCode(tenantId, object.getRef());
    		JSONObject odsDsMetaJson = datasourceService.getOdsTableMetaByDsName(tenantId, objDsInfo.getDsName());
    		if (odsDsMetaJson.containsKey("storeType") && "slot".equalsIgnoreCase(odsDsMetaJson.getString("storeType")) && odsDsMetaJson.getJSONObject("slotMeta") != null) {
    			String tableName = odsDsMetaJson.getString("table");
    			JSONObject fieldsJson = odsDsMetaJson.getJSONObject("slotMeta").getJSONObject("fields");
	    		List<String> objPkFields = object.getFields().stream().filter(item -> item.isPk()).map(item -> item.getCode()).collect(Collectors.toList());
		    	sql.append(" SELECT object_id AS ").append(objPkFields.get(0));
		        List<ViewModel.Field> fields = object.getFields();
		        for (int i = 0; i < fields.size(); i++) {
		        	ViewModel.Field field = fields.get(i);
		        	if (field.getObject() != null || field.isPk()) {
		        		continue;
		        	}
		            if (i > 0) {
		                sql.append(",");
		            }
		            if (StringUtils.isNotBlank(field.getType())) {
		                sql.append("CAST(");
		            }
		            sql.append(fieldsJson.getString(field.getRef()));
		            if (StringUtils.isNotBlank(field.getType())) {
		                sql.append(" AS " + field.getType() + ")");
		            }
		            sql.append(" AS " + getDbFieldEscape(dbType) + field.getCode() + getDbFieldEscape(dbType));
		        }
		        sql.append(" FROM ");
		        sql.append(tableName);
		        sql.append(" WHERE ");
		        sql.append("object_code = '" + object.getRef() + "'");
		        sql.append(" AND is_deleted = 0 AND tenant_id = 'aliyun' ");
    		} else {
	    		String tableName = "tag_meta_tag_object_biz_relation";
	    		if (odsDsMetaJson != null && odsDsMetaJson.getString("table") != null) {
	    			tableName = odsDsMetaJson.getString("table");
	    		}
	    		List<String> objPkFields = object.getFields().stream().filter(item -> item.isPk()).map(item -> item.getCode()).collect(Collectors.toList());
		    	sql.append(" SELECT object_biz_id AS ").append(objPkFields.get(0));
		        List<ViewModel.Field> fields = object.getFields();
		        for (int i = 0; i < fields.size(); i++) {
		        	ViewModel.Field field = fields.get(i);
		        	if (field.getObject() != null || field.isPk()) {
		        		continue;
		        	}
		            if (i > 0) {
		                sql.append(",");
		            }
		            if (StringUtils.isNotBlank(field.getType())) {
		                sql.append("CAST(");
		            }
		            if (StringUtils.isNotBlank(field.getRef())) {
		                sql.append("MAX(CASE WHEN tag_unique_code = '" + field.getRef() + "' THEN tag_value ELSE NULL END)");
		            } else {
		                sql.append("MAX(CASE WHEN tag_code = '" + field.getCode() + "' THEN tag_value ELSE NULL END)");
		            }
		            if (StringUtils.isNotBlank(field.getType())) {
		                sql.append(" AS " + field.getType() + ")");
		            }
		            sql.append(" AS " + getDbFieldEscape(dbType) + field.getCode() + getDbFieldEscape(dbType));
		            if (field.isEnums()) {
		                if (StringUtils.isNotBlank(field.getRef())) {
		                    sql.append(",MAX(CASE WHEN tag_unique_code = '" + field.getRef() + "' THEN tag_value_zh ELSE NULL END) AS " + field.getCode() + "_desc");
		                } else {
		                    sql.append(",MAX(CASE WHEN tag_code = '" + field.getCode() + "' THEN tag_value_zh ELSE NULL END) AS " + field.getCode() + "_desc");
		                }
		            }
		        }
		        sql.append(" FROM (");
		        sql.append("SELECT a.* FROM " + tableName + " as a inner join tag_domain_meta_permission as b on a.object_unique_code=b.object_unique_code AND a.tag_unique_code=b.tag_unique_code and b.is_deleted=0 AND a.is_deleted = 0");
		        sql.append(" AND a.object_unique_code = '" + object.getRef() + "'");
		        sql.append(") GROUP BY object_biz_id ");
    		}
    	} else if ("table".equalsIgnoreCase(object.getType())) {
    		sql.append(" SELECT ");
	        List<ViewModel.Field> fields = object.getFields();
	        for (int i = 0; i < fields.size(); i++) {
	        	ViewModel.Field field = fields.get(i);
	        	if (field.getObject() != null) {
	        		continue;
	        	}
	            if (i > 0) {
	                sql.append(",");
	            }
	            if (field.isFunc()) {
	            	sql.append(" NULL AS " + field.getCode() + " ");
	            } else if ("datetime".equalsIgnoreCase(field.getType()) && "adb3".equalsIgnoreCase(dbType)) {
	            	sql.append("DATE_FORMAT(" + (StringUtils.isNotBlank(field.getRef()) ? field.getRef() : field.getCode()) + ",'%Y-%m-%d %H:%i:%s') AS " + field.getCode() + " ");
	            } else {
	            	sql.append((StringUtils.isNotBlank(field.getExpr()) ? field.getExpr() : (StringUtils.isNotBlank(field.getRef()) ? field.getRef() : field.getCode()))+ " AS " + field.getCode() + " ");
	            }
	        }
	        sql.append(" FROM ");
	        sql.append(getOdsTableName(tenantId, object.getRef()));
    	} else if ("component".equalsIgnoreCase(object.getType())) {
    		sql.append(" SELECT ");
	        List<ViewModel.Field> fields = object.getFields();
	        for (int i = 0; i < fields.size(); i++) {
	        	ViewModel.Field field = fields.get(i);
	        	if (field.getObject() != null) {
	        		continue;
	        	}
	            if (i > 0) {
	                sql.append(",");
	            }
	            if (field.isFunc()) {
	            	sql.append(" NULL AS " + field.getCode() + " ");
	            } else if ("datetime".equalsIgnoreCase(field.getType()) && "adb3".equalsIgnoreCase(dbType)) {
	            	sql.append("DATE_FORMAT(" + (StringUtils.isNotBlank(field.getRef()) ? field.getRef() : field.getCode()) + ",'%Y-%m-%d %H:%i:%s') AS " + field.getCode() + " ");
	            } else {
	            	sql.append((StringUtils.isNotBlank(field.getRef()) ? field.getRef() : field.getCode()) + " AS " + field.getCode());
	            }
	        }
	        sql.append(" FROM (");
	        sql.append(getSqlFromComponenet(tenantId, object.getRef(), dbType) + (StringUtils.isNotBlank(object.getFilter()) ? (" WHERE " + object.getFilter()) : "") + " ) AS _" + object.getCode());
    	}
        return sql.toString();
    }
	
	private Datasource getDsInfoByObjectCode(String tenantId, String objectUniqueCode) {
		DatasourceExample example = new DatasourceExample();
		example.createCriteria().andTableNameEqualTo(objectUniqueCode).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsTypeEqualTo("obj");
		List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isEmpty(dsList)) {
			throw new QanatBizException(objectUniqueCode + " is not found");
		}
		return dsList.get(0);
	}

	public String getSubQueryFromObject(String tenantId, ViewModel.DataObject object) {
    	TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(tenantId);
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + tenantId + " is not configured");
    	}

    	DbInfoExample dbInfoExample = new DbInfoExample();
    	dbInfoExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDbNameEqualTo(tenantList.get(0).getDefaultDw());
    	List<DbInfo> dbInfos = dbInfoMapper.selectByExample(dbInfoExample);
    	if (CollectionUtils.isEmpty(dbInfos)) {
    		throw new QanatBizException("DbInfo not found:" + tenantList.get(0).getDefaultDw());
    	}
    	String dbType = dbInfos.get(0).getDbType();
    	StringBuffer sql = new StringBuffer();
    	if ("metadata".equalsIgnoreCase(object.getType())) {
    		Datasource objDsInfo = getDsInfoByObjectCode(tenantId, object.getRef());
    		JSONObject odsDsMetaJson = datasourceService.getOdsTableMetaByDsName(tenantId, objDsInfo.getDsName());
    		if (odsDsMetaJson.containsKey("storeType") && "slot".equalsIgnoreCase(odsDsMetaJson.getString("storeType")) && odsDsMetaJson.getJSONObject("slotMeta") != null) {
    			String tableName = odsDsMetaJson.getString("table");
    			JSONObject fieldsJson = odsDsMetaJson.getJSONObject("slotMeta").getJSONObject("fields");
	    		List<String> objPkFields = object.getFields().stream().filter(item -> item.isPk()).map(item -> item.getCode()).collect(Collectors.toList());
		    	sql.append("  SELECT * FROM (SELECT object_id AS ").append(objPkFields.get(0));
		        List<ViewModel.Field> fields = object.getFields();
		        for (int i = 0; i < fields.size(); i++) {
		        	ViewModel.Field field = fields.get(i);
		        	if (field.getObject() != null || field.isPk()) {
		        		continue;
		        	}
		            if (i > 0) {
		                sql.append(",");
		            }
		            if (StringUtils.isNotBlank(field.getType())) {
		                sql.append("CAST(");
		            }
		            sql.append(fieldsJson.getString(field.getRef()));
		            if (StringUtils.isNotBlank(field.getType())) {
		                sql.append(" AS " + field.getType() + ")");
		            }
		            sql.append(" AS " + field.getCode());
		        }
		        sql.append(" FROM ");
		        sql.append(tableName);
		        sql.append(" WHERE ");
		        sql.append("object_code = '" + object.getRef() + "'");
		        sql.append(" AND is_deleted = 0 AND tenant_id = 'aliyun' ");
		        sql.append(") AS " + object.getCode() + " WHERE 1=1 ");
    		} else {
	    		String tableName = "tag_meta_tag_object_biz_relation";
	    		if (odsDsMetaJson != null && odsDsMetaJson.getString("table") != null) {
	    			tableName = odsDsMetaJson.getString("table");
	    		}
	    		List<String> objPkFields = object.getFields().stream().filter(item -> item.isPk()).map(item -> item.getCode()).collect(Collectors.toList());
		    	sql.append(" SELECT * FROM (SELECT object_biz_id AS ").append(objPkFields.get(0));
		        List<ViewModel.Field> fields = object.getFields();
		        for (int i = 0; i < fields.size(); i++) {
		        	ViewModel.Field field = fields.get(i);
		        	if (field.getObject() != null || field.isPk()) {
		        		continue;
		        	}
		            if (i > 0) {
		                sql.append(",");
		            }
		            if (StringUtils.isNotBlank(field.getType())) {
		                sql.append("CAST(");
		            }
		            if (StringUtils.isNotBlank(field.getRef())) {
		                sql.append("MAX(CASE WHEN tag_unique_code = '" + field.getRef() + "' THEN tag_value ELSE NULL END)");
		            } else {
		                sql.append("MAX(CASE WHEN tag_code = '" + field.getCode() + "' THEN tag_value ELSE NULL END)");
		            }
		            if (StringUtils.isNotBlank(field.getType())) {
		                sql.append(" AS " + field.getType() + ")");
		            }
		            sql.append(" AS " + field.getCode());
		            if (field.isEnums()) {
		                if (StringUtils.isNotBlank(field.getRef())) {
		                    sql.append(",MAX(CASE WHEN tag_unique_code = '" + field.getRef() + "' THEN tag_value_zh ELSE NULL END) AS " + field.getCode() + "_desc");
		                } else {
		                    sql.append(",MAX(CASE WHEN tag_code = '" + field.getCode() + "' THEN tag_value_zh ELSE NULL END) AS " + field.getCode() + "_desc");
		                }
		            }
		        }
		        sql.append(" FROM (");
		        sql.append("SELECT * FROM " + tableName);
		        sql.append(" WHERE object_biz_id=? and is_deleted = 0 and object_unique_code = '" + object.getRef() + "'");
		        sql.append(") GROUP BY object_biz_id) AS " + object.getCode() + " WHERE 1=1 ");
    		}
    	} else if ("table".equalsIgnoreCase(object.getType())) {
    		sql.append(" SELECT ");
	        List<ViewModel.Field> fields = object.getFields();
	        for (int i = 0; i < fields.size(); i++) {
	        	ViewModel.Field field = fields.get(i);
	        	if (field.getObject() != null) {
	        		continue;
	        	}
	            if (i > 0) {
	                sql.append(",");
	            }
	            if (field.isFunc()) {
	            	sql.append(" NULL AS " + field.getCode() + " ");
	            } else if ("datetime".equalsIgnoreCase(field.getType()) && "adb3".equalsIgnoreCase(dbType)) {
	            	sql.append("DATE_FORMAT(" + (StringUtils.isNotBlank(field.getRef()) ? field.getRef() : field.getCode()) + ",'%Y-%m-%d %H:%i:%s') AS " + field.getCode() + " ");
	            } else {
	            	sql.append((StringUtils.isNotBlank(field.getRef()) ? field.getRef() : field.getCode()) + " AS " + field.getCode());
	            }
	        }
	        sql.append(" FROM ");
	        sql.append(getOdsTableName(tenantId, object.getRef()) + " WHERE " + (StringUtils.isNotBlank(object.getFilter()) ? object.getFilter() : " 1=1 "));
    	} else if ("component".equalsIgnoreCase(object.getType())) {
    		sql.append(" SELECT ");
	        List<ViewModel.Field> fields = object.getFields();
	        for (int i = 0; i < fields.size(); i++) {
	        	ViewModel.Field field = fields.get(i);
	        	if (field.getObject() != null) {
	        		continue;
	        	}
	            if (i > 0) {
	                sql.append(",");
	            }
	            if (field.isFunc()) {
	            	sql.append(" NULL AS " + field.getCode() + " ");
	            } else if ("datetime".equalsIgnoreCase(field.getType()) && "adb3".equalsIgnoreCase(dbType)) {
		            sql.append("DATE_FORMAT(" + (StringUtils.isNotBlank(field.getRef()) ? field.getRef() : field.getCode()) + ",'%Y-%m-%d %H:%i:%s') AS " + field.getCode() + " ");
	            } else {
	            	sql.append((StringUtils.isNotBlank(field.getRef()) ? field.getRef() : field.getCode()) + " AS " + field.getCode());
	            }
	        }
	        sql.append(" FROM (");
	        sql.append(getSqlFromComponenet(tenantId, object.getRef(), dbInfos.get(0).getDbType()) + ") AS _t0_ WHERE " + (StringUtils.isNotBlank(object.getFilter()) ? object.getFilter() : " 1=1 "));
    	}
        return sql.toString();
    }

	private String getOdsTableName(String tenantId, String dsName) {
		JSONObject dsMetaJson = datasourceService.getOdsTableMetaByDsName(tenantId, dsName);
		return dsMetaJson.getString("table");
	}

	private static List<ViewModel.Field> getColumnsByModel(ViewModel dataModel) {
		List<ViewModel.Field> columnList = new ArrayList<>();
		columnList.addAll(dataModel.getObject().getFields());
		
		if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
			for (ViewModel.RelatedDataObject object : dataModel.getRelatedObjects()) {
				List<String> joinOnFields = object.getRelations().stream().map(e -> e.getField()).collect(Collectors.toList());
				
				for (ViewModel.Field field : object.getFields()) {
					if (joinOnFields.contains(field.getCode())) {
						continue;
					}
					columnList.add(field);
				}
			}
		}
		return columnList;
	}


	private List<String> getColumnsByModelWithAlias(ViewModel dataModel, String dbType) {
		List<String> columnListWithPk = new ArrayList<>();
		List<String> columnList = new ArrayList<>();
		Map<String, String> pkMap = new HashMap<>();
		String pkFieldWithTableAlias = null;
		String pkField = null;
		for (ViewModel.Field field : dataModel.getObject().getFields()) {
			if (field.isFunc()) {
				continue;
			}
			if (field.isPk()) {
				pkMap.put(dataModel.getObject().getCode(), field.getCode());
				pkFieldWithTableAlias = dataModel.getObject().getCode() + "." + field.getCode();
				pkField = field.getCode();
				continue;
			}
			if (field.getObject() != null) {
				columnList.add(field.getObject().getCode() + "." + field.getCode());
			} else {
				if (field.getRef() != null && field.getRef().startsWith("exp#")) {
					columnList.add(field.getRef().replace("exp#", "") + " AS " + field.getCode());
				} else {
					columnList.add(dataModel.getObject().getCode() + "." + field.getCode());
				}
			}
		}
		
		if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
			for (ViewModel.RelatedDataObject object : dataModel.getRelatedObjects()) {
				List<String> joinOnFields = object.getRelations().stream().map(e -> e.getField()).collect(Collectors.toList());
				Map<String, Relation> relatedFieldMap = object.getRelations().stream().collect(Collectors.toMap(Relation::getRelatedField, Function.identity()));
				if ("full join".equalsIgnoreCase(object.getRelationType()) && relatedFieldMap.get(pkFieldWithTableAlias) != null) {
					pkMap.put(object.getCode(), relatedFieldMap.get(pkFieldWithTableAlias).getField());
				}
				for (ViewModel.Field field : object.getFields()) {
					if (joinOnFields.contains(field.getCode())) {
						continue;
					}
					if (field.getRef() != null && field.getRef().startsWith("exp#")) {
						columnList.add(field.getRef().replace("exp#", "") + " AS " + getDbFieldEscape(dbType) + field.getCode() + getDbFieldEscape(dbType));
					} else {
						columnList.add(object.getCode() + "." + getDbFieldEscape(dbType) + field.getCode() + getDbFieldEscape(dbType));
					}
				}
			}
		}
		if (pkMap.keySet().size() > 1) {
			StringBuffer pkBuffer = new StringBuffer();
			pkBuffer.append("(CASE ");
			for (String key : pkMap.keySet()) {
				pkBuffer.append(" WHEN ").append(key + "." + pkMap.get(key)).append(" IS NOT NULL THEN ").append(key + "." + pkMap.get(key));
			}
			pkBuffer.append(" END) AS ").append(pkField);
			columnListWithPk.add(pkBuffer.toString());
		} else {
			columnListWithPk.add(pkFieldWithTableAlias);
		}
		columnListWithPk.addAll(columnList);
		return columnListWithPk;
	}

	public ViewModel.Field getPkField(ViewModel.DataObject dataObject) {
        for (ViewModel.Field field : dataObject.getFields()) {
        	if (field.isPk()) {
        		return field;
        	}
        }
        throw new QanatBizException("no pk fields found in main object");
	}

	public String getSubQueryFromFieldWithRefObj(String tenantId, String fieldName, ViewModel.RelatedDataObject object, String dbType) {
    	StringBuffer sql = new StringBuffer();
    	sql.append(" ( ");
    	sql.append(getSqlFromFieldWithRefObj(tenantId, fieldName, object, dbType));
        sql.append(") AS " + object.getCode() + " ");
        return sql.toString();
    }

	public String getSqlFromFieldWithRefObj(String tenantId, String fieldName, ViewModel.RelatedDataObject object, String dbType) {
		if ("component".equals(object.getType())) {
			return getSqlFromComponenet(tenantId, fieldName, object.getRef(), dbType);
		} else {
			return getSqlFromAggrField(tenantId, fieldName, object, null, dbType);
		}
	}

	private String getSqlFromComponenet(String tenantId, String fieldName, String objectCode, String dbType) {
		ExtensionExample example = new ExtensionExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + (dbType == null ? "adb3" : dbType)).andPluginEqualTo(objectCode);
		List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isNotEmpty(exts)) {
			return exts.get(0).getScript().replace("#arrayField#", fieldName).replace("#pkField#", getObjectPk(tenantId, exts.get(0).getObjectType(), exts.get(0).getVersion()));
		}
		log.error("no component conf found, filedName:{} objectCode:{} dbType:{}", fieldName, objectCode, dbType);
		throw new QanatBizException("no component conf found");
	}

	private String getSqlFromComponenet(String tenantId, String objectCode, String dbType) {
		ExtensionExample example = new ExtensionExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-" + (dbType == null ? "adb3" : dbType)).andPluginEqualTo(objectCode);
		List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
		if (CollectionUtils.isNotEmpty(exts)) {
			return exts.get(0).getScript();
		}
		log.error("no component conf found, objectCode:{} dbType:{}", objectCode, dbType);
		throw new QanatBizException("no component conf found");
	}

	public String getSqlFromAggrField(String tenantId, String fieldName, ViewModel.RelatedDataObject object, List<String> fkFields, String dbType) {
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT ");
    	sql.append(StringUtils.join(object.getRelations().stream().map(e -> e.getField()).collect(Collectors.toList()), ","));
    	if ("odps".equalsIgnoreCase(dbType)) {
    		sql.append(", " + getAggrSql(object, dbType) + " AS " + fieldName);
    	} else {
    		sql.append(", "+ getAggrSql(object, dbType) + " AS " + fieldName);
    	}
        sql.append(" FROM ");
        sql.append(getSubQeuryFromMainObject(tenantId, object, dbType));
        if (CollectionUtils.isNotEmpty(fkFields)) {
            sql.append(" WHERE ");
        	List<String> filters = new ArrayList<>();
        	for (ViewModel.Relation rel : object.getRelations()) {
        		if (fkFields.contains(rel.getField())) {
        			filters.add(rel.getField() + "=?");
        		} else {
        			filters.add(rel.getField() + (rel.getOp() != null ? rel.getOp() : "=") + rel.getRelatedField().replace("exp#", ""));
        		}
        	}
        	sql.append(StringUtils.join(filters, " AND "));
        }
        sql.append(" GROUP BY ");
        sql.append(StringUtils.join(object.getRelations().stream().map(e -> e.getField()).collect(Collectors.toList()), ","));
        return sql.toString();
	}
	
	private String getAggrSql(ViewModel.RelatedDataObject object, String dbType) {
		if ("odps".equalsIgnoreCase(dbType)) {
			if ("GROUP_CONCAT".equalsIgnoreCase(object.getAggrFunc()) || StringUtils.isBlank(object.getAggrFunc())) {
				return "WM_CONCAT(',', " + getArrayField(object, dbType) + ")";
			}
		} else if ("hologres".equalsIgnoreCase(dbType)) {
			if ("GROUP_CONCAT".equalsIgnoreCase(object.getAggrFunc()) || StringUtils.isBlank(object.getAggrFunc())) {
				return "string_agg(" + getArrayField(object, dbType) + ", ',')";
			}
		} else {
			if ("GROUP_CONCAT".equalsIgnoreCase(object.getAggrFunc()) || StringUtils.isBlank(object.getAggrFunc())) {
				return "GROUP_CONCAT(DISTINCT " + getArrayField(object, dbType) +" SEPARATOR ',')";
			}
		}
		return object.getAggrFunc() + "(" + getArrayField(object, dbType) + ")";
	}

	public String getCreateTableByModel(ViewModel dataModel, String tableName, String objectType, JSONObject dbMetaJson) {
		ViewModel.Field pkField = getPkField(dataModel.getObject());
		
		if ("adb3".equalsIgnoreCase(dbMetaJson.getString("dbType"))) {
			List<String> colDefList = getModelColDefs4Adb3(dataModel, objectType);
			String distributePart = "";
        	String pkPart = pkField.getCode();
        	if (StringUtils.isNotBlank(dataModel.getSettings().getDistributeKey()) && !pkField.getCode().equalsIgnoreCase(dataModel.getSettings().getDistributeKey())) {
        		distributePart = "HASH(" + dataModel.getSettings().getDistributeKey() + ")";
            	pkPart = pkField.getCode() + "," + dataModel.getSettings().getDistributeKey();
        	} else {
        		distributePart = "HASH(" + pkField.getCode() + ")";
        	}
			return String.format("Create Table %s ( %s , primary key (%s) ) DISTRIBUTE BY %s INDEX_ALL='Y';", tableName, StringUtils.join(colDefList, ","), pkPart, distributePart);
		} else if ("hologres".equalsIgnoreCase(dbMetaJson.getString("dbType"))) {
			List<String> colDefList = getModelColDefs4Holo(dataModel, objectType);
			String distributePart = "";
        	String pkPart = pkField.getCode();
        	if (StringUtils.isNotBlank(dataModel.getSettings().getDistributeKey()) && !pkField.getCode().equalsIgnoreCase(dataModel.getSettings().getDistributeKey())) {
        		distributePart = dataModel.getSettings().getDistributeKey();
            	pkPart = pkField.getCode() + "," + dataModel.getSettings().getDistributeKey();
        	} else {
        		distributePart = pkField.getCode();
        	}
			return "Create Table " + tableName + " ( " + StringUtils.join(colDefList, ",") + " , primary key (" + pkPart + ") );call set_table_property('" + tableName + "', 'orientation', 'row,column');call set_table_property('" + tableName + "', 'distribution_key', '" + distributePart + "');\n" + 
					"call set_table_property('" + tableName + "', 'binlog.level', 'replica');\n" + 
					"call set_table_property('" + tableName + "', 'binlog.ttl', '86400');";
		} else {
			throw new QanatBizException("Unsupported dbType:" + dbMetaJson.getString("dbType"));
		}
	}

	private List<String> getModelColDefs4Adb3(ViewModel dataModel, String objectType) {
		List<ViewModel.Field> columnList = getColumnsByModel(dataModel, objectType);
		List<String> colDefList = new ArrayList<>();
		for (ViewModel.Field field : columnList) {
			String code = field.getCode();
			if (StringUtils.isBlank(code)) {
				code = field.getRef();
			}
			String type = StringUtils.isBlank(field.getType()) ? "varchar" : field.getType();
			if (field.isMultivalue()) {
				String relatedType = StringUtils.isBlank(field.getType()) ? "varchar" : field.getType();
				type = "multivalue delimiter_tokenizer '" + (StringUtils.isNotBlank(field.getMvToken()) ? field.getMvToken() : ",") + "' value_type '" + relatedType + "'";
			}
			String comment = StringUtils.isBlank(field.getName()) ? code : field.getName();
		    colDefList.add(getDbFieldEscape("adb3") + code + getDbFieldEscape("adb3") + " " + type + " COMMENT '" + comment+ "'");
		    if (field.isEnums()) {
		        colDefList.add(code+ "_desc varchar COMMENT '" + comment + "_描述'");
		    }
		}
		return colDefList;
	}

	private List<String> getModelColDefs4Holo(ViewModel dataModel, String objectType) {
		List<ViewModel.Field> columnList = getColumnsByModel(dataModel, objectType);
		List<String> colDefList = new ArrayList<>();
		for (ViewModel.Field field : columnList) {
			String code = field.getCode();
			if (StringUtils.isBlank(code)) {
				code = field.getRef();
			}
			String type = HoloUtils.getHoloTypeFromMysql(StringUtils.isBlank(field.getType()) ? "text" : field.getType());
			if (field.isMultivalue()) {
				String relatedType = StringUtils.isBlank(field.getType()) ? "text" : field.getType();
				type = relatedType + "[]";
			}
		    colDefList.add(getDbFieldEscape("hologres") + code + getDbFieldEscape("hologres") + " " + type);
		}
		colDefList.add(getDbFieldEscape("hologres") + "__trace_id__" + getDbFieldEscape("hologres") + " text");
		colDefList.add(getDbFieldEscape("hologres") + "__gmt_modified__" + getDbFieldEscape("hologres") + " timestamptz");
		return colDefList;
	}
	
	public List<ViewModel.Field> getColumnsByModel(ViewModel dataModel, String objectType) {
		List<ViewModel.Field> columnList = new ArrayList<>();
		columnList.addAll(dataModel.getObject().getFields());
		if ("metadata".equalsIgnoreCase(dataModel.getObject().getType())) {
//			Map<String, String> fieldTypes = datasourceService.getObjectFieldTypes(objectType, dataModel.getObject().getRef());
//			if (fieldTypes != null) {
//				for (ViewModel.Field field : columnList) {
//					if (fieldTypes.containsKey(field.getCode()) && "ENUMS".equalsIgnoreCase(fieldTypes.get(field.getCode()))) {
//						field.setType("multivalue delimiter_tokenizer ',' value_type 'varchar'");
//					}
//				}
//			}
		}
		
		if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
			for (ViewModel.RelatedDataObject object : dataModel.getRelatedObjects()) {
				List<String> joinOnFields = object.getRelations().stream().map(e -> e.getField()).collect(Collectors.toList());
				
				for (ViewModel.Field field : object.getFields()) {
					if (joinOnFields.contains(field.getCode())) {
						continue;
					}
					columnList.add(field);
				}
			}
		}
		return columnList;
	}
    
    public String getStreamSelectSql(String tenantId, ViewModel dataModel, String dstDbName) {
    	String pkField = dataModel.getObject().getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0).getCode();
    	List<String> relCols = new ArrayList<>();
		relCols.add(dataModel.getObject().getCode() + "." + pkField + " AS " + pkField);
    	List<String> mainObjColsExPkDrcParse = new ArrayList<>();
    	List<String> mainObjColsExPkDrc = new ArrayList<>();
    	List<String> relColDefs = new ArrayList<>();
    	Map<String, String> colMap = new HashMap<>();
    	List<String> compJoinList = new ArrayList<>();
    	List<String> tableJoinList = new ArrayList<>();
    	List<ViewModel.Field> funcFields = new ArrayList<>();
    	JSONObject dbMetaJson = dsInfoService.getDbMetaByName(dstDbName);
    	for (ViewModel.Field field : dataModel.getObject().getFields()) {
    		if (field.getObject() != null) {
				if ("varchar".equalsIgnoreCase(field.getType())) {
					relCols.add("JSON_VALUE(" + field.getObject().getCode() + ".x, '$." + field.getCode() + "') AS " + field.getCode());
				} else {
					relCols.add("CAST(JSON_VALUE(" + field.getObject().getCode() + ".x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " ) AS " + field.getCode());
				}
    			relColDefs.add(getDbFieldEscape(dbMetaJson.getString("dbType")) + field.getCode() + getDbFieldEscape(dbMetaJson.getString("dbType")) + " " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
				colMap.put(field.getCode(), "CAST(JSON_VALUE(" + field.getObject().getCode() + ".x, '$." + field.getCode() + "') AS " + field.getType() + ")");
				
				List<String> joinOnCondsLeft = new ArrayList<>();
				List<String> joinOnCondsRight = new ArrayList<>();
				for (ViewModel.Relation relation : field.getObject().getRelations()) {
					 String refObj = relation.getRelatedField().split("\\.")[0];
					 String refField = relation.getRelatedField().split("\\.")[1];
					 String refValue = null;
					 if (refObj.equalsIgnoreCase(dataModel.getObject().getCode())) {
						 refValue = relation.getRelatedField();
					 } else {
						 refValue = "JSON_VALUE(" + refObj + ".x, '$." + refField + "')";
					 }
					joinOnCondsLeft.add(field.getObject().getCode() + "." + relation.getField() + (StringUtils.isBlank(relation.getOp()) ? "=" : relation.getOp()) + "?");
					joinOnCondsRight.add(refValue);
				}
				ExtensionExample example = new ExtensionExample();
				example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-adb3").andPluginEqualTo(field.getObject().getRef());
				List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
				String dbName = "origion".equalsIgnoreCase(field.getObject().getLookupFrom()) ? exts.get(0).getDbName() : dstDbName;
				String selectSql = "select * from " + getSubQueryFromFieldWithRefObj(tenantId, field.getCode(), field.getObject(), dbMetaJson.getString("dbType")) + " where " + StringUtils.join(joinOnCondsLeft, " and ");
				String blinkSql = null;
				if ("inner join".equalsIgnoreCase( field.getObject().getRelationType())) {
					blinkSql = " , lateral table(queryDim('" + dbName + "','" + selectSql.replace("'", "''") + "'," + StringUtils.join(joinOnCondsRight, ",") + ")) as " + field.getObject().getCode() + "(x)\n";
				} else {
					blinkSql = " left join lateral table(queryDim('" + dbName + "','" + selectSql.replace("'", "''") + "'," + StringUtils.join(joinOnCondsRight, ",") + ")) as " + field.getObject().getCode() + "(x) on true\n";
				}
				compJoinList.add(blinkSql);
    		} else if (field.isFunc()) {
    			funcFields.add(field);
    			continue;
    		} else if (field.isPk()) {
    			continue;
    		} else if ("null".equalsIgnoreCase(field.getRef())) {
    			continue;
    		} else {
				if ("varchar".equalsIgnoreCase(field.getType())) {
					colMap.put(field.getCode(), dataModel.getObject().getCode() + "." + field.getCode() );
		    		mainObjColsExPkDrcParse.add("JSON_VALUE(b.x, '$." + field.getCode() + "') AS " + field.getCode());
				} else {
					colMap.put(field.getCode(), "CAST(" + dataModel.getObject().getCode() + "." + field.getCode() + " AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
		    		mainObjColsExPkDrcParse.add("CAST(JSON_VALUE(b.x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " ) AS " + field.getCode());
				}
				mainObjColsExPkDrc.add("'" + field.getCode() + "'");
				relCols.add(dataModel.getObject().getCode() + "." + field.getCode() + " AS " + field.getCode());
			}
    	}
    	if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
    		for (RelatedDataObject relObj : dataModel.getRelatedObjects()) {
    			String srcDsType = null;
    			Map<String, Relation> rels = relObj.getRelations().stream().filter(e->!e.getRelatedField().startsWith("exp#")).collect(Collectors.toMap(Relation::getField, Function.identity()));
    			List<ViewModel.Field> fields = relObj.getFields().stream().filter(e->!rels.containsKey(e.getCode())).collect(Collectors.toList());
    			for (ViewModel.Field field : fields) {
    				if ("varchar".equalsIgnoreCase(field.getType())) {
        				relCols.add("JSON_VALUE(" + relObj.getCode() + ".x, '$." + field.getCode() + "') AS " + field.getCode());
    					colMap.put(relObj.getCode() + "." + field.getCode(), "JSON_VALUE(" + relObj.getCode() + ".x, '$." + field.getCode() + "')");
    				} else {
        				relCols.add("CAST(JSON_VALUE(" + relObj.getCode() + ".x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " ) AS " + field.getCode());
    					colMap.put(relObj.getCode() + "." + field.getCode(), "CAST(JSON_VALUE(" + relObj.getCode() + ".x, '$." + field.getCode() + "') AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + " )");
    				}
        			relColDefs.add(getDbFieldEscape(dbMetaJson.getString("dbType")) + field.getCode() + getDbFieldEscape(dbMetaJson.getString("dbType")) + " " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
    			}
    			
    			List<String> joinOnCondsLeft = new ArrayList<>();
				List<String> joinOnCondsRight = new ArrayList<>();
				for (ViewModel.Relation relation : relObj.getRelations()) {
					 String refObj = relation.getRelatedField().split("\\.")[0];
					 String refField = relation.getRelatedField().split("\\.")[1];
					 String refValue = null;
					 if (refObj.equalsIgnoreCase(dataModel.getObject().getCode())) {
						 refValue = relation.getRelatedField();
					 } else {
						 refValue = "JSON_VALUE(" + refObj + ".x, '$." + refField + "')";
					 }
					joinOnCondsLeft.add(relObj.getFields().stream().filter(e->e.getCode().equalsIgnoreCase(relation.getField())).collect(Collectors.toList()).get(0).getRef() + (StringUtils.isBlank(relation.getOp()) ? "=" : relation.getOp()) + "?");
					joinOnCondsRight.add(refValue);
				}
				String dbName = null;
				if ("component".equalsIgnoreCase(relObj.getType())) {
					ExtensionExample example = new ExtensionExample();
					example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-adb3").andPluginEqualTo(relObj.getRef());
					List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
					dbName = "origion".equalsIgnoreCase(relObj.getLookupFrom()) ? exts.get(0).getDbName() : dstDbName;
				} else {
	    			JSONObject dsMetaJson = dsInfoService.getDbMetaByDsName(tenantId, relObj.getRef());
					dbName = "origion".equalsIgnoreCase(relObj.getLookupFrom()) ? dsMetaJson.getString("dbName") : dstDbName;
        			srcDsType = dsMetaJson.getString("dsType");
				}
				String selectSql = getSubQueryFromObject(tenantId,relObj) + " and " + StringUtils.join(joinOnCondsLeft, " and ");
				String blinkSql = null;
				if ("inner join".equalsIgnoreCase(relObj.getRelationType())) {
					blinkSql = " , lateral table(queryDim('" + ("tddl".equalsIgnoreCase(srcDsType) ? dstDbName : dbName) + "','" + selectSql.replace("'", "''") + "'," + StringUtils.join(joinOnCondsRight, ",") + ")) as " + relObj.getCode() + "(x)\n";
				} else {
					blinkSql = " left join lateral table(queryDim('" + ("tddl".equalsIgnoreCase(srcDsType) ? dstDbName : dbName)  + "','" + selectSql.replace("'", "''") + "'," + StringUtils.join(joinOnCondsRight, ",") + ")) as " + relObj.getCode() + "(x) on true\n";
				}
				tableJoinList.add(blinkSql);
    		}
    	}
    	for (ViewModel.Field field : funcFields) {
    		String funcExpress = field.getRef();
			String funcCode = lookupProcessor.parseFuncExpress(funcExpress).get(0);
			String[] cols = lookupProcessor.parseFuncExpress(funcExpress).get(1).split(",");
			List<String> exps = new ArrayList<>();
			for (String col : cols) {
				if (col.startsWith("'") && col.endsWith("'")) {
					exps.add(col);
				} else {
					exps.add(colMap.get(col));
				}
			}
			ExtensionExample example = new ExtensionExample();
			example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andCodeEqualTo(funcCode);
			List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(example);
			if (CollectionUtils.isEmpty(exts)) {
				log.error("funcCode:{} not found", funcCode);
				continue;
			}
			String func = exts.get(0).getPlugin().equalsIgnoreCase("groovy")?"groovyFunc":"dfaasFunc";
			String funcExp = null;
			if (field.getType().equalsIgnoreCase("varchar")) {
				funcExp = func + "('" + tenantId + "', '" + funcCode + "'," + StringUtils.join(exps, ",") + ") AS " + field.getCode();
			} else {
				funcExp = "CAST(" + func + "('" + tenantId + "', '" + funcCode + "'," + StringUtils.join(exps, ",") + ") AS " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()) + ") AS " + field.getCode();
			}
			relColDefs.add(getDbFieldEscape(dbMetaJson.getString("dbType")) + field.getCode() + getDbFieldEscape(dbMetaJson.getString("dbType")) + " " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
			relCols.add(funcExp);
    	}

    	String selectSql = "select " + StringUtils.join(relCols, ",") + "\n"
    			+ "from v_main_obj as " + dataModel.getObject().getCode() + "\n"
    			+ (CollectionUtils.isNotEmpty(compJoinList) ? StringUtils.join(compJoinList, " ") : "")
    			+ (CollectionUtils.isNotEmpty(tableJoinList) ? StringUtils.join(tableJoinList, " ") : "")
    			;
	    	
        return selectSql;
	}
    
    public String getStreamDdlSql(String tenantId, ViewModel dataModel) {
    	String pkField = dataModel.getObject().getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0).getCode();
    	String pkFieldType = dataModel.getObject().getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0).getType();
    	List<String> relColDefs = new ArrayList<>();
    	relColDefs.add(pkField + " " + ("datetime".equalsIgnoreCase(pkFieldType)?"timestamp":pkFieldType));
    	List<ViewModel.Field> funcFields = new ArrayList<>();
    	for (ViewModel.Field field : dataModel.getObject().getFields()) {
    		if (field.getObject() != null) {
    			relColDefs.add(field.getCode() + " " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
    		} else if (field.isFunc()) {
    			funcFields.add(field);
    			continue;
    		} else if ("null".equalsIgnoreCase(field.getRef())) {
    			continue;
    		} else if (field.isPk()) {
    			continue;
    		} else {
				relColDefs.add(field.getCode() + " " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
			}
    	}
    	if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
    		for (RelatedDataObject relObj : dataModel.getRelatedObjects()) {
    			Map<String, Relation> rels = relObj.getRelations().stream().filter(e->!e.getRelatedField().startsWith("exp#")).collect(Collectors.toMap(Relation::getField, Function.identity()));
    			List<ViewModel.Field> fields = relObj.getFields().stream().filter(e->!rels.containsKey(e.getCode())).collect(Collectors.toList());
    			for (ViewModel.Field field : fields) {
        			relColDefs.add(field.getCode() + " " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
    			}
    		}
    	}
    	for (ViewModel.Field field : funcFields) {
			relColDefs.add(field.getCode() + " " + ("datetime".equalsIgnoreCase(field.getType())?"timestamp":field.getType()));
    	}	
        return StringUtils.join(relColDefs, ',');
	}
}