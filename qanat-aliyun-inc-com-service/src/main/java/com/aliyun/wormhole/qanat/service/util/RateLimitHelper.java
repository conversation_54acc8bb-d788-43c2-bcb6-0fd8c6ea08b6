package com.aliyun.wormhole.qanat.service.util;

import com.google.common.util.concurrent.RateLimiter;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;


@Component
public class RateLimitHelper {


//    private RateLimiter rateLimiter = RateLimiter.create(200.0);
//
//    @Switch(description = "useRateLimiter", name = "useRateLimiter")
//    private boolean useRateLimiter = false;
//
//    @AteyeInvoker(paraDesc = "permitsPerSecond", description = "permitsPerSecond")
//    public boolean resetRateLimitCount(double permitsPerSecond){
//        rateLimiter.setRate(permitsPerSecond);
//        return true;
//    }
//
//    @AteyeInvoker(paraDesc = "getPermitsPerSecond", description = "getPermitsPerSecond")
//    public double getPermitsPerSecond() {
//        return rateLimiter.getRate();
//    }
//
//
//    /**
//     * Acquires a single permit from this {@code RateLimiter}, blocking until the
//     * request can be granted. Tells the amount of time slept, if any.
//     *
//     * <p>This method is equivalent to {@code acquire(1)}.
//     *
//     * @return time spent sleeping to enforce rate, in seconds; 0.0 if not rate-limited
//     * @since 16.0 (present in 13.0 with {@code void} return type})
//     */
//    public double acquire(){
//        return rateLimiter.acquire();
//    }
//
//    /**
//     * Acquires the given number of permits from this {@code RateLimiter}, blocking until the
//     * request can be granted. Tells the amount of time slept, if any.
//     *
//     * @param permits the number of permits to acquire
//     * @return time spent sleeping to enforce rate, in seconds; 0.0 if not rate-limited
//     * @throws IllegalArgumentException if the requested number of permits is negative or zero
//     * @since 16.0 (present in 13.0 with {@code void} return type})
//     */
//    public double acquire(int permits){
//        return rateLimiter.acquire(permits);
//    }
//
//    /**
//     * Acquires a permit from this {@code RateLimiter} if it can be obtained
//     * without exceeding the specified {@code timeout}, or returns {@code false}
//     * immediately (without waiting) if the permit would not have been granted
//     * before the timeout expired.
//     *
//     * <p>This method is equivalent to {@code tryAcquire(1, timeout, unit)}.
//     *
//     * @param timeout the maximum time to wait for the permit. Negative values are treated as zero.
//     * @param unit the time unit of the timeout argument
//     * @return {@code true} if the permit was acquired, {@code false} otherwise
//     * @throws IllegalArgumentException if the requested number of permits is negative or zero
//     */
//    public boolean tryAcquire(long timeout, TimeUnit unit) {
//        return rateLimiter.tryAcquire(timeout, unit);
//    }
//
//    /**
//     * Acquires permits from this {@link RateLimiter} if it can be acquired immediately without delay.
//     *
//     * <p>
//     * This method is equivalent to {@code tryAcquire(permits, 0, anyUnit)}.
//     *
//     * @param permits the number of permits to acquire
//     * @return {@code true} if the permits were acquired, {@code false} otherwise
//     * @throws IllegalArgumentException if the requested number of permits is negative or zero
//     * @since 14.0
//     */
//    public boolean tryAcquire(int permits) {
//        return rateLimiter.tryAcquire(permits);
//    }
//
//    /**
//     * Acquires a permit from this {@link RateLimiter} if it can be acquired immediately without
//     * delay.
//     *
//     * <p>
//     * This method is equivalent to {@code tryAcquire(1)}.
//     *
//     * @return {@code true} if the permit was acquired, {@code false} otherwise
//     * @since 14.0
//     */
//    public boolean tryAcquire() {
//        return rateLimiter.tryAcquire();
//    }
//
//    /**
//     * Acquires the given number of permits from this {@code RateLimiter} if it can be obtained
//     * without exceeding the specified {@code timeout}, or returns {@code false}
//     * immediately (without waiting) if the permits would not have been granted
//     * before the timeout expired.
//     *
//     * @param permits the number of permits to acquire
//     * @param timeout the maximum time to wait for the permits. Negative values are treated as zero.
//     * @param unit the time unit of the timeout argument
//     * @return {@code true} if the permits were acquired, {@code false} otherwise
//     * @throws IllegalArgumentException if the requested number of permits is negative or zero
//     */
//    public boolean tryAcquire(int permits, long timeout, TimeUnit unit) {
//        return rateLimiter.tryAcquire(permits, timeout, unit);
//    }
//
//    public boolean isUseRateLimiter() {
//        return useRateLimiter;
//    }
//
//    public void setUseRateLimiter(boolean useRateLimiter) {
//        this.useRateLimiter = useRateLimiter;
//    }

}
