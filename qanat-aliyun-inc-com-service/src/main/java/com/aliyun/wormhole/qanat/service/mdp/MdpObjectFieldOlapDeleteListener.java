package com.aliyun.wormhole.qanat.service.mdp;

import java.util.List;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.service.adapter.RtdwViewModelTaskServiceAdapter;
import com.aliyun.wormhole.qanat.service.metaq.MetaQProducer;
import com.taobao.metaq.client.MetaPushConsumer;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MdpObjectFieldOlapDeleteListener {
    
    @Resource
    private RtdwViewModelTaskServiceAdapter rtdwViewModelTaskService;
    
    @Resource
    private MetaQProducer metaQProducer;

    @Value("${qanat.unit.id}")
    private String tenantId;
    
    @PostConstruct
    private void init() {
    	MetaPushConsumer consumer = new MetaPushConsumer("CID-qanat-pool");
    	try {
	        consumer.subscribe("TOPIC_POOL_QUOTA_FIELD_DELETED", "");
			consumer.setConsumeThreadMin(1);
			consumer.setConsumeThreadMax(10);
			consumer.setPullInterval(1000);
			
			consumer.setMessageListener(new MessageListenerConcurrently() {
	            @Override
	            public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
	                for (MessageExt msg : msgs) {
	                    try {
	                        String msgBody = new String(msg.getBody(), "utf-8");
	                        log.info("msg={}", msgBody);
	                        JSONObject json = JSON.parseObject(msgBody);
	                        String eventId = json.getString("eventId");
	                        String objectUniqueCode = json.getJSONObject("extParam").getString("objectUniqueCode");
	                        String fieldUniqueCode = json.getString("uniqueCode");
	                        String operateType = json.getJSONObject("extParam").getString("operate");
	                        Integer isRef = json.getJSONObject("extParam").getJSONObject("simpleTagVO").getInteger("isQuote");
	                        if ("DELETE".equalsIgnoreCase(operateType)) {
		                        DataResult<Boolean> result = rtdwViewModelTaskService.reflectObjectFieldChange(tenantId, objectUniqueCode, fieldUniqueCode, isRef, operateType, null);
		                        log.info("reflect object field delete result:{}", result != null ? result.getData() : null);
		                        String exportMsg = eventId + "`" + objectUniqueCode + "`" + fieldUniqueCode + "`" + isRef + "`" + operateType;
		                        metaQProducer.sendMsg("TOPIC_QANAT_OBJ_FIELD_DELETE_LOG", "", fieldUniqueCode, exportMsg);
	                        }
	                    } catch (Exception e) {
	                        log.error("reflect object field delete failed, message={}", e.getMessage(), e);
	    	                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
	                    }
	                }
	                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
	            }
	        });

	        consumer.start();
    	} catch(Exception e) {
    		log.error("create consumer for object field delete failed", e);
    	}
    }
}