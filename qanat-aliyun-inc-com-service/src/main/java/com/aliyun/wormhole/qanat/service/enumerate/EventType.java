package com.aliyun.wormhole.qanat.service.enumerate;

/**
 * <AUTHOR>
 */

public enum EventType {
    INSERT(1, "INSERT"),
    UPDATE(2, "UPDATE"),
    DELETE(3, "DELETE");

    private int code;
    private String name;

    EventType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static EventType codeOf(int code) {
        switch(code) {
            case 1:
                return INSERT;
            case 2:
                return UPDATE;
            case 3:
                return DELETE;
            default:
                return null;
        }
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


}
