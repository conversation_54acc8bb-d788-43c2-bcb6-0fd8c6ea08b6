package com.aliyun.wormhole.qanat.service.adapter;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.tag.api.request.ListTagByObjectRequest;
import com.aliyun.tag.api.response.ListResponse;
import com.aliyun.tag.api.service.IQueryTagMetaService;
import com.aliyun.tag.api.vo.SimpleTagVO;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelHandler;

import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * ADB实时数仓同步服务
 * <AUTHOR>
 * 2019年7月23日
 */
@Slf4j
@Component
public class RtdwViewModelTaskServiceAdapter {
    
    @Resource
    private ViewModelHandler viewModelHandler;

    @Value("${env.unit}")
    private String envUnit;

    @Value("${qanat.vpc.endpoint}")
    private String vpcEndpoint;
    
    @HSFConsumer(clientTimeout=30000)
    private IQueryTagMetaService mdpService;
    
    public DataResult<Long> createViewModelFromObject(String tenantId, String appName, String objectType, String objectUniqueCode, String operateEmpid, String objectMsg) {
    	log.info("createViewModelFromObject({},{},{},{},{},{})", tenantId, appName, objectType, objectUniqueCode, operateEmpid, objectMsg);
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	if ("center".equalsIgnoreCase(envUnit)) {
        		result.setData(viewModelHandler.createViewModelFromObject(tenantId, appName, objectType, objectUniqueCode, operateEmpid, objectMsg));
        	} else {
        		JSONObject json = new JSONObject();
        		json.put("tenantId", tenantId);
        		json.put("appName", appName);
        		json.put("objectType", objectType);
        		json.put("objectUniqueCode", objectUniqueCode);
        		json.put("operateEmpid", operateEmpid);
        		JSONObject objectJson = JSON.parseObject(objectMsg);

        		//在当前单元通过HSF服务获取对象字段
        		try {
					ListTagByObjectRequest req = new ListTagByObjectRequest();
					req.setObjectType(objectType);
					req.setObjectUniqueCode(objectUniqueCode);
					ListResponse<SimpleTagVO> mdpResult = mdpService.listTagByObject(req);
					log.info("mdpResult={}", JSON.toJSONString(mdpResult));
					if (mdpResult != null && mdpResult.getData() != null) {
						objectJson.put("fields", mdpResult.getData());
					}
        		} catch (Exception e) {
        			log.error("get object[{}] fields failed", objectUniqueCode, e);
        		}
        		
        		json.put("objectMsg", objectJson.toJSONString());
        		String httpResult = this.doHttpPost(vpcEndpoint + "/api/createViewModelFromObject", json.toJSONString());
        		JSONObject httpResultJson = JSON.parseObject(httpResult);
        		result.setData(httpResultJson.getLong("data"));
        	}
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("createViewModelFromObject failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("createViewModelFromObject failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    public DataResult<Boolean> reflectObjectFieldChange(String tenantId, String dsUniqueName, String fieldName, Integer isRef, String operateType, String tagJson) {
        log.info("reflectObjectFieldChange({},{},{},{},{},{}) start", tenantId, dsUniqueName, fieldName, isRef, operateType, tagJson);
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	if ("center".equalsIgnoreCase(envUnit)) {
        		result.setData(viewModelHandler.reflectObjectFieldChange(tenantId, dsUniqueName, fieldName, isRef, operateType, tagJson));
        	} else {
        		JSONObject json = new JSONObject();
        		json.put("tenantId", tenantId);
        		json.put("dsUniqueName", dsUniqueName);
        		json.put("fieldName", fieldName);
        		json.put("isRef", isRef);
        		json.put("operateType", operateType);
        		json.put("tagJson", tagJson);
        		String httpResult = this.doHttpPost(vpcEndpoint + "/api/reflectObjectFieldChange", json.toJSONString());
        		JSONObject httpResultJson = JSON.parseObject(httpResult);
        		result.setData(httpResultJson.getBoolean("data"));
        	}
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("reflectObjectFieldChange failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("reflectObjectFieldChange failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    private String doHttpPost(String url, String data) {
        String resp = null;
        MediaType mediaType = MediaType.parse("application/json");
        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(mediaType, data))
                .build();
        OkHttpClient okHttpClient = new OkHttpClient();
        Response response = null;
        try {
            log.info("req:post {} -d'{}'", url, data);
            response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                resp = StringEscapeUtils.unescapeHtml(response.body().string()); 
                log.info("resp:{}", resp); 
            }
        } catch (Exception e) {
            log.error("http request failed", e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return resp;
    }
    
    public static void main(String [] args) {
    	System.out.println(StringEscapeUtils.unescapeHtml("{\"code\":\"200\",\"data\":146,\"success\":true}"));
    }
}