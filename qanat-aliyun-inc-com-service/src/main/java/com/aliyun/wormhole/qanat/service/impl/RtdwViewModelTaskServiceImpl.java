package com.aliyun.wormhole.qanat.service.impl;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.service.RtdwViewModelTaskService;
import com.aliyun.wormhole.qanat.api.service.ViewModelRequest;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelHandler;
import com.aliyun.wormhole.qanat.service.viewmodel.v2.ViewModelHandlerV2;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

/**
 * ADB实时数仓同步服务
 * <AUTHOR>
 * 2019年7月23日
 */
@Slf4j
@Component
@HSFProvider(serviceInterface = RtdwViewModelTaskService.class)
public class RtdwViewModelTaskServiceImpl implements RtdwViewModelTaskService {
    
    @Resource
    private ViewModelHandler viewModelHandler;
    
    @Resource
    private ViewModelHandlerV2 viewModelHandlerV2;
    
    @Resource
    private DatatubeInstanceMapper datatubeInstanceMapper;
    
    @Override
    public DataResult<Long> createViewModelFromObject(String tenantId, String appName, String objectType, String objectUniqueCode, String operateEmpid, String objectMsg) {
    	log.info("createViewModelFromObject({},{},{},{},{},{})", tenantId, appName, objectType, objectUniqueCode, operateEmpid, objectMsg);
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
	        result.setData(viewModelHandler.createViewModelFromObject(tenantId, appName, objectType, objectUniqueCode, operateEmpid, objectMsg));
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("createViewModelFromObject failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("createViewModelFromObject failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<Long> createViewModelFromYaml(ViewModelRequest request) {
    	log.info("createViewModelFromYaml({})", JSON.toJSONString(request));
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
	    	result.setData(viewModelHandler.createViewModelFromYaml(request));
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("createViewModelFromYaml failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("createViewModelFromYaml failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<Long> modifyViewModel(ViewModelRequest request) {
    	log.info("modifyViewModel({})", JSON.toJSONString(request));
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
	    	result.setData(viewModelHandler.modifyViewModel(request));
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("modifyViewModel failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("modifyViewModel failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<Long> createBatchStreamTasks(String tenantId, Long viewModelId, String operateEmpid) {
        log.info("start createBatchStreamTasks({},{},{})", tenantId, viewModelId, operateEmpid);
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	DatatubeInstanceExample diExample = new DatatubeInstanceExample();
            diExample.createCriteria().andTenantIdEqualTo(tenantId).andProviderEqualTo("viewmodel").andIsDeletedEqualTo(0L).andProviderIdEqualTo(viewModelId);
            List<DatatubeInstance> dis = datatubeInstanceMapper.selectByExample(diExample);
            if (CollectionUtils.isEmpty(dis)) {
            	throw new QanatBizException("viewModelId:" + viewModelId + " is not related to any datatube instance");
            }
            DatatubeInstance datatubeInst =  dis.get(0);
            
            if ("v2".equalsIgnoreCase(datatubeInst.getVersion())) {
            	result.setData(viewModelHandlerV2.createBatchStreamTasks(tenantId, viewModelId, operateEmpid));
            } else {
            	result.setData(viewModelHandler.createBatchStreamTasks(tenantId, viewModelId, operateEmpid));
            }
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("createBatchStreamTasks failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("createBatchStreamTasks failed, error={}", e.getMessage(), e);
        }
        return result;
    }
	
    @Override
    public DataResult<Long> createBatchCheckTask(String tenantId, Long viewModelId, String operateEmpid) {
        log.info("start createBatchCheckTask({},{},{})", tenantId, viewModelId, operateEmpid);
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
            result.setData(viewModelHandler.createBatchCheckTask(tenantId, viewModelId, operateEmpid));
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("createBatchCheckTask failed, error={}", e.getMessage(), e);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("createBatchCheckTask failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<Boolean> updateSlaData(String tenantId, String operateEmpid, List<Long> vmIds, String bizDate) {
        log.info("start createSlaView({},{},{},{})", tenantId, operateEmpid, JSON.toJSONString(vmIds), bizDate);
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
            result.setData(viewModelHandler.updateSlaData(tenantId, operateEmpid, vmIds, bizDate));
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("createDwdSyncTask4Adb3 failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("createDwdSyncTask4Adb3 failed, error={}", e.getMessage(), e);
        }
        return result;
    }
    
    @Override
    public DataResult<Boolean> createTableAndFullSyncByYaml(String tenantId, Long viewModelId, String dstDbName, String userYaml) {
        log.info("createTableAndFullSyncByYaml({},{},{},{}) start", tenantId, viewModelId, dstDbName, userYaml);
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
            result.setData(viewModelHandler.createTableAndFullSyncByYaml(tenantId, viewModelId, dstDbName, userYaml));
            return result;
        } catch (Exception e) {
            log.error("createTableAndFullSyncByYaml failed", e);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
        }
        return result;
    }
    
    @Override
    public DataResult<Boolean> createTableAndFullSync(String tenantId, Long viewModelId) {
    	return createTableAndFullSync(tenantId, viewModelId, null);
    }
    
    @Override
    public DataResult<Boolean> createTableAndFullSync(String tenantId, Long viewModelId, String batchJobs) {
        log.info("createAndFullSyncForDwdTable({},{},{}) start", tenantId, viewModelId, batchJobs);
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	DatatubeInstanceExample diExample = new DatatubeInstanceExample();
            diExample.createCriteria().andTenantIdEqualTo(tenantId).andProviderEqualTo("viewmodel").andIsDeletedEqualTo(0L).andProviderIdEqualTo(viewModelId);
            List<DatatubeInstance> dis = datatubeInstanceMapper.selectByExample(diExample);
            if (CollectionUtils.isEmpty(dis)) {
            	throw new QanatBizException("viewModelId:" + viewModelId + " is not related to any datatube instance");
            }
            DatatubeInstance datatubeInst =  dis.get(0);
            
            if ("v2".equalsIgnoreCase(datatubeInst.getVersion())) {
            	result.setData(viewModelHandlerV2.createTableAndFullSync(tenantId, viewModelId, batchJobs));
            } else {
            	result.setData(viewModelHandler.createTableAndFullSync(tenantId, viewModelId, batchJobs));
            }
            return result;
        } catch (Exception e) {
            log.error("createTableAndFullSync failed", e);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
        }
        return result;
    }
    
	@AteyeInvoker(description = "启动任务依赖(创建全量任务及增量任务)", paraDesc = "租户id,默认为1&定义viewModel的ID&当前操作人工号")
    @Override
    public DataResult<Long> restartModelTask(String tenantId, Long viewModelId, String operateEmpid) {
        log.info("start restartModelTask({},{},{})", tenantId, viewModelId, operateEmpid);
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	result.setData(viewModelHandler.restartModelTask(tenantId, viewModelId, operateEmpid));
        } catch (Exception e) {
            log.error("restartModelTask failed", e);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
        }
        return result;
    }

	@Override
	public DataResult<Boolean> reflectObjectFieldChange(String tenantId, String dsUniqueName, String fieldName, Integer isRef, String operateType, String tagJson) {
        log.info("reflectObjectFieldChange({},{},{},{},{},{}) start", tenantId, dsUniqueName, fieldName, isRef, operateType, tagJson);
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
            result.setData(viewModelHandler.reflectObjectFieldChange(tenantId, dsUniqueName, fieldName, isRef, operateType, tagJson));
            return result;
        } catch (Exception e) {
            log.error("reflectObjectFieldChange failed", e);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
        }
        return result;
	}
}