package com.aliyun.wormhole.qanat.process;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.KafkaManagementService;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfo;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelInfoMapper;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * ViewModel新version生效上线任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class ClearUnuseResourceProcessor extends JavaProcessor {
    
    @Resource
    private ViewModelInfoMapper viewModelInfoMapper;
    
    @Resource
    private KafkaManagementService kafkaService;
    
    @Resource
    private BlinkService blinkService;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
            log.info("start to refresh vm version, param=[]", context.getJobParameters());
//            String tenantId = paramsJson.getString("tenantId");
//            if (StringUtils.isBlank(tenantId)) {
//            	log.info("tenantId is empty");
//                return new ProcessResult(false);
//            }
            JSONArray vmIdJsonArray = paramsJson.getJSONArray("viewModelIds");
        	List<Long> vmIds = new ArrayList<>();
            if (vmIdJsonArray != null && vmIdJsonArray.size() > 0) {
            	for (int i = 0; i < vmIdJsonArray.size(); i++) {
            		vmIds.add(vmIdJsonArray.getLong(i));
            	}
            }
        	ViewModelInfoExample example = new ViewModelInfoExample();
        	ViewModelInfoExample.Criteria criteria = example.createCriteria();
//        	criteria.andTenantIdEqualTo(tenantId);
        	criteria.andIsDeletedEqualTo(0L);
        	if (CollectionUtils.isNotEmpty(vmIds)) {
        		criteria.andIdIn(vmIds);
        	}
			List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(example);
			if (CollectionUtils.isNotEmpty(viewModelInfos)) {
				List<String> versionIds = new ArrayList<>();
				List<String> tenantAppNames = new ArrayList<>();
				for (ViewModelInfo vm : viewModelInfos) {
					versionIds.add(vm.getVersionId() + "");
					String key = vm.getTenantId() + "," + vm.getAppName();
					if (!tenantAppNames.contains(key)) {
						tenantAppNames.add(key);
					}
				}
				log.info("versionIds:{}", JSON.toJSONString(versionIds));
				for (String key : tenantAppNames) {
					String tenantId = key.split(",")[0];
					String appName = key.split(",")[1];
					List<String> consumerList = kafkaService.getConsumerGroupList(tenantId, appName);
					if (CollectionUtils.isNotEmpty(consumerList)) {
						for (String consumerId : consumerList) {
							String[] tokens = consumerId.split("-");
							String version = tokens[tokens.length-1];
							if (StringUtils.isNumeric(version) && !versionIds.contains(version)) {
								log.info("kafkaconsumer:{} will be cleared", consumerId);
								kafkaService.deleteConsumerGroup(tenantId, appName, consumerId);
							}
						}
					}
	
					List<String> jobList = blinkService.getJobList(tenantId, appName);
					if (CollectionUtils.isNotEmpty(jobList)) {
						for (String jobName : jobList) {
							String[] tokens = jobName.split("\\_");
							String lastToken = tokens[tokens.length-1];
							String version = lastToken.replace("v", "");
							if (lastToken.startsWith("v") && StringUtils.isNumeric(version) && !versionIds.contains(version)) {
								log.info("blinkJob:{} will be cleared", jobName);
								blinkService.dropExistedJob(tenantId, appName, jobName);
							}
						}
					}
				}
			}
        } catch (QanatBizException e) {
            log.error("ClearUnuseResourceProcessor任务调度异常:{}", e.getMessage());
            return new ProcessResult(false);
        } catch (Exception e) {
            log.error("ClearUnuseResourceProcessor任务调度异常", e);
            return new ProcessResult(false);
        }
        return new ProcessResult(true);
    }
    
    @Override
    public void kill(JobContext context) {
        
    }
    
    public static void main(String [] args) {
    	String jobName = "fullsync_1_553_cid_v721";
		String[] tokens = jobName.split("\\_");
		String version = tokens[tokens.length-1].replace("v", "");
    	System.out.print(tokens[tokens.length-1]);
    }
}