package com.aliyun.wormhole.qanat.service.viewmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.security.util.StringUtils;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.DsFieldInfoMapper;
import com.aliyun.wormhole.qanat.service.util.YamlUtil;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.DataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Field;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.RelatedDataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Relation;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ViewModelOptimizer {
    
    @Resource
    private DsFieldInfoMapper dsFieldInfoMapper;
    
	public ViewModel getOptimizedViewModel(String tenantId, String yaml) {
    	ViewModel viewModel = YamlUtil.getViewModel(yaml);
    	ViewModel newViewModel = new ViewModel();
    	newViewModel.setCode(viewModel.getCode());
    	newViewModel.setName(viewModel.getName());
    	newViewModel.setType(viewModel.getType());
    	newViewModel.setDynamic(viewModel.isDynamic());
    	newViewModel.setAddOns(viewModel.getAddOns());
    	newViewModel.setSettings(viewModel.getSettings());
    	boolean mainObjectHasRefFields = false;
    	int i = 0;
    	if ("metadata".equalsIgnoreCase(viewModel.getObject().getType())) {
    		List<DsFieldInfo> dsFields = getDsFieldsByDsUniqueName(tenantId, viewModel.getObject(), viewModel);
    		Map<String, DsFieldInfo> dsFieldMap = dsFields.stream().collect(Collectors.toMap(DsFieldInfo::getFieldName, Function.identity()));
    		List<DsFieldInfo> refDsFields =  dsFields.stream().filter(item -> "ref".equalsIgnoreCase(item.getSysType()) && item.getExtInfo() != null).collect(Collectors.toList());
			if (viewModel.getSettings() != null && viewModel.getSettings().isObjectNoFulljoin() == true) {
				refDsFields = new ArrayList<>();
			}
			if (CollectionUtils.isNotEmpty(refDsFields)) {
				mainObjectHasRefFields = true;
				List<String> refDsFieldNames = refDsFields.stream().map(item -> item.getFieldName()).collect(Collectors.toList());
				DataObject newObject = new DataObject();
				newObject.setCode(viewModel.getObject().getCode());
				newObject.setRef(viewModel.getObject().getRef());
				newObject.setType(viewModel.getObject().getType());
				List<Field> newObjFields = new ArrayList<>();
				newObject.setFields(newObjFields);
				String newObjectPkField = null;
				if (CollectionUtils.isNotEmpty(viewModel.getObject().getFields()) && !viewModel.isDynamic()) {
					for (Field field : viewModel.getObject().getFields()) {
						if (!refDsFieldNames.contains(field.getCode())) {
							Field newField = new Field();
							BeanUtils.copyProperties(field, newField);
							if (StringUtils.isBlank(field.getType())) {
								newField.setType(dsFieldMap.get(field.getCode()).getFieldType());
							}
							if (StringUtils.isBlank(field.getRef())) {
								newField.setRef(dsFieldMap.get(field.getCode()).getFieldUniqueName());
							}
							if (dsFieldMap.get(field.getCode()) != null && "multivalue".equalsIgnoreCase(dsFieldMap.get(field.getCode()).getDataType())) {
								newField.setMultivalue(true);
								String extInfo = dsFieldMap.get(field.getCode()).getExtInfo();
								if (StringUtils.isNotBlank(extInfo) && JSON.parseObject(extInfo) != null) {
									newField.setMvToken(JSON.parseObject(extInfo).getString("separator"));
								} else {
									newField.setMvToken(",");
								}
							}
							if ("array".equalsIgnoreCase(field.getType())) {
								newField.setType("varchar");
								newField.setMultivalue(true);
								newField.setMvToken(",");
							}
							newObjFields.add(newField);
						}
						if (field.isPk()) {
							newObjectPkField = field.getCode();
						}
					}
				} else {
					for (DsFieldInfo dsField : dsFields) {
						if (!refDsFieldNames.contains(dsField.getFieldName())) {
							Field newField = new Field();
							newField.setCode(dsField.getFieldName());
							newField.setType(dsField.getFieldType());
							newField.setRef(dsField.getFieldUniqueName());
							newField.setName(dsField.getFieldDesc());
							if ("multivalue".equalsIgnoreCase(dsField.getDataType())) {
								newField.setMultivalue(true);
								String extInfo = dsField.getExtInfo();
								if (StringUtils.isNotBlank(extInfo) && JSON.parseObject(extInfo) != null) {
									newField.setMvToken(JSON.parseObject(extInfo).getString("separator"));
								} else {
									newField.setMvToken(",");
								}
							}
							newObjFields.add(newField);
							if (dsField.getIsPk() == 1) {
								newObjectPkField = dsField.getFieldName();
								newField.setPk(true);
							}
						}
					}
				}
				newViewModel.setObject(newObject);
				
				//新增RelatedObjects
				List<RelatedDataObject> newRelObjects = new ArrayList<>();
				newViewModel.setRelatedObjects(newRelObjects);
				Map<String, List<DsFieldInfo>> refDsFieldsMap = new HashMap<>();
				for (DsFieldInfo refDsField : refDsFields) {
					JSONObject extInfoJson = JSON.parseObject(refDsField.getExtInfo());
					String key = extInfoJson.getString("dsName");
					if (org.apache.commons.lang3.StringUtils.isBlank(key)) {
						continue;
					}
					if (refDsFieldsMap.get(key) != null) {
						refDsFieldsMap.get(key).add(refDsField);
					} else {
						List<DsFieldInfo> refs = new ArrayList<>();
						refs.add(refDsField);
						refDsFieldsMap.put(key, refs);
					}
				}
				for (String dsName : refDsFieldsMap.keySet()) {
					RelatedDataObject newRelObject = new RelatedDataObject();
					newRelObject.setCode("t" + i++);
					newRelObject.setType("table");
					newRelObject.setRef(dsName);
					List<Field> newRelObjFields = new ArrayList<>();
					newRelObject.setFields(newRelObjFields);
					List<Relation> newRelations = new ArrayList<>();
					DsFieldInfoExample example = new DsFieldInfoExample();
					example.createCriteria().andDsNameEqualTo(dsName).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L);
					List<DsFieldInfo> dsRefFields = dsFieldInfoMapper.selectByExample(example);
					Map<String, DsFieldInfo> fieldNameMap = dsRefFields.stream().collect(Collectors.toMap(DsFieldInfo::getFieldName, Function.identity()));
					List<String> pkFields = dsRefFields.stream().filter(item -> item.getIsPk() == 1).map(item -> item.getFieldName()).collect(Collectors.toList());
					
					Relation newRel = new Relation();
					newRel.setField(pkFields.get(0));
					newRel.setRelatedField(newObject.getCode() + "." + newObjectPkField);
					newRelations.add(newRel);
					
					newRelObject.setRelations(newRelations);
					newRelObject.setRelationType("FULL JOIN");
					for (DsFieldInfo dsFieldInfo : refDsFieldsMap.get(dsName)) {
						JSONObject extInfoJson = JSON.parseObject(dsFieldInfo.getExtInfo());
						
						Field newRelObjField = new Field();
						DsFieldInfo dsRefField = fieldNameMap.get(extInfoJson.getString("fieldName"));
						newRelObjField.setCode(dsFieldInfo.getFieldName());
						newRelObjField.setName(dsFieldInfo.getFieldDesc());
						newRelObjField.setType(dsFieldInfo.getFieldType());
						if ("multivalue".equalsIgnoreCase(dsFieldInfo.getDataType())) {
							newRelObjField.setMultivalue(true);
							String extInfo = dsFieldInfo.getExtInfo();
							if (StringUtils.isNotBlank(extInfo) && JSON.parseObject(extInfo) != null) {
								newRelObjField.setMvToken(JSON.parseObject(extInfo).getString("separator"));
							} else {
								newRelObjField.setMvToken(",");
							}
						}
						log.info("dsName={} fieldName={} fieldNameMap={}", dsName, extInfoJson.getString("fieldName"), JSON.toJSONString(fieldNameMap));
						newRelObjField.setRef(dsRefField == null ? extInfoJson.getString("fieldName") : (StringUtils.isNotBlank(dsRefField.getFieldUniqueName()) ? dsRefField.getFieldUniqueName() : dsRefField.getFieldName()));
						newRelObjFields.add(newRelObjField);
					}
					//引用表对象字段集里增加主键
					Field newRelObjField = new Field();
					DsFieldInfo dsRefField = fieldNameMap.get(pkFields.get(0));
					newRelObjField.setCode(dsRefField.getFieldName());
					newRelObjField.setName(dsRefField.getFieldDesc());
					newRelObjField.setType(dsRefField.getFieldType());
					newRelObjField.setPk(true);
					newRelObjField.setRef(StringUtils.isNotBlank(dsRefField.getFieldUniqueName()) ? dsRefField.getFieldUniqueName() : dsRefField.getFieldName());
					newRelObjFields.add(newRelObjField);
					
					newRelObjects.add(newRelObject);
				}
			} else {
				DataObject newObject = new DataObject();
				newObject.setCode(viewModel.getObject().getCode());
				newObject.setRef(viewModel.getObject().getRef());
				newObject.setType(viewModel.getObject().getType());
				List<Field> newObjFields = new ArrayList<>();
				newObject.setFields(newObjFields);
				if (CollectionUtils.isNotEmpty(viewModel.getObject().getFields()) && !viewModel.isDynamic()) {
					for (Field field : viewModel.getObject().getFields()) {
						Field newField = new Field();
						BeanUtils.copyProperties(field, newField);
						if (StringUtils.isBlank(field.getType())) {
							newField.setType(dsFieldMap.get(field.getCode()).getFieldType());
						}
						if (StringUtils.isBlank(field.getRef()) && dsFieldMap.get(field.getCode()) != null) {
							newField.setRef(dsFieldMap.get(field.getCode()).getFieldUniqueName());
						}
						if (dsFieldMap.get(field.getCode()) != null && "multivalue".equalsIgnoreCase(dsFieldMap.get(field.getCode()).getDataType())) {
							newField.setMultivalue(true);
							String extInfo = dsFieldMap.get(field.getCode()).getExtInfo();
							if (StringUtils.isNotBlank(extInfo) && JSON.parseObject(extInfo) != null) {
								newField.setMvToken(JSON.parseObject(extInfo).getString("separator"));
							} else {
								newField.setMvToken(",");
							}
						}
						if ("array".equalsIgnoreCase(field.getType())) {
							newField.setType("varchar");
							newField.setMultivalue(true);
							newField.setMvToken(",");
						}
						newObjFields.add(newField);
					}
				} else {
					for (DsFieldInfo dsField : dsFields) {
						Field newField = new Field();
						newField.setCode(dsField.getFieldName());
						newField.setType(dsField.getFieldType());
						newField.setRef(dsField.getFieldUniqueName());
						newField.setName(dsField.getFieldDesc());
						if ("multivalue".equalsIgnoreCase(dsField.getDataType())) {
							newField.setMultivalue(true);
							String extInfo = dsField.getExtInfo();
							if (StringUtils.isNotBlank(extInfo) && JSON.parseObject(extInfo) != null) {
								newField.setMvToken(JSON.parseObject(extInfo).getString("separator"));
							} else {
								newField.setMvToken(",");
							}
						}
						newObjFields.add(newField);
						
						if (dsField.getIsPk() == 1) {
							newField.setPk(true);
						}
					}
					if (CollectionUtils.isNotEmpty(viewModel.getObject().getFields())) {
						for (Field field : viewModel.getObject().getFields()) {
							if (field.getObject() != null) {
								Field newField = new Field();
								BeanUtils.copyProperties(field, newField);
								newObjFields.add(newField);
							}
						}
					}
				}
				newViewModel.setObject(newObject);
			}
    	} else {
    		List<DsFieldInfo> dsFields = getDsFieldsByDsName(tenantId, viewModel.getObject());
    		Map<String, DsFieldInfo> dsFieldMap = dsFields.stream().collect(Collectors.toMap(DsFieldInfo::getFieldName, Function.identity()));
    		DataObject newObject = new DataObject();
			newObject.setCode(viewModel.getObject().getCode());
			newObject.setRef(viewModel.getObject().getRef());
			newObject.setType(viewModel.getObject().getType());
			newObject.setFilter(viewModel.getObject().getFilter());
			List<Field> newObjFields = new ArrayList<>();
    		newViewModel.setObject(newObject);
			newObject.setFields(newObjFields);
    		for (Field field : viewModel.getObject().getFields()) {
				Field newField = new Field();
				BeanUtils.copyProperties(field, newField);
				if (StringUtils.isBlank(field.getType())) {
					newField.setType(dsFieldMap.get(field.getCode()).getFieldType());
				}
				if (dsFieldMap.get(field.getCode()) != null && "multivalue".equalsIgnoreCase(dsFieldMap.get(field.getCode()).getDataType())) {
					newField.setMultivalue(true);
					String extInfo = dsFieldMap.get(field.getCode()).getExtInfo();
					if (StringUtils.isNotBlank(extInfo) && JSON.parseObject(extInfo) != null) {
						newField.setMvToken(JSON.parseObject(extInfo).getString("separator"));
					} else {
						newField.setMvToken(",");
					}
				}
				if ("array".equalsIgnoreCase(field.getType())) {
					newField.setType("varchar");
					newField.setMultivalue(true);
					newField.setMvToken(",");
				}
				newObjFields.add(newField);
			}
    	}
    	if (CollectionUtils.isNotEmpty(viewModel.getRelatedObjects())) {
    		if (mainObjectHasRefFields) {
				throw new QanatBizException("related objects is not suported yet while main mdp object has ref fields");
    		}
    		List<RelatedDataObject> relObjects = new ArrayList<>();
    		newViewModel.setRelatedObjects(relObjects);
    		for (RelatedDataObject relObject :  viewModel.getRelatedObjects()) {
    			if ("metadata".equalsIgnoreCase(relObject.getType())) {
    	    		List<DsFieldInfo> dsFields = getDsFieldsByDsUniqueName(tenantId, relObject, viewModel);
    	    		Map<String, DsFieldInfo> dsFieldMap = dsFields.stream().collect(Collectors.toMap(DsFieldInfo::getFieldName, Function.identity()));
    	    		List<DsFieldInfo> refDsFields =  dsFields.stream().filter(item -> "ref".equalsIgnoreCase(item.getSysType()) && item.getExtInfo() != null).collect(Collectors.toList());
    				if (CollectionUtils.isNotEmpty(refDsFields)) {
        				List<String> refDsFieldNames = refDsFields.stream().map(item -> item.getFieldName()).collect(Collectors.toList());
    					Map<String, List<DsFieldInfo>> refDsFieldsMap = new HashMap<>();
    					for (DsFieldInfo refDsField : refDsFields) {
    						JSONObject extInfoJson = JSON.parseObject(refDsField.getExtInfo());
    						String key = extInfoJson.getString("dsName");
    						if (refDsFieldsMap.get(key) != null) {
    							refDsFieldsMap.get(key).add(refDsField);
    						} else {
    							List<DsFieldInfo> refs = new ArrayList<>();
    							refs.add(refDsField);
    							refDsFieldsMap.put(key, refs);
    						}
    					}
    					for (String dsName : refDsFieldsMap.keySet()) {
    						RelatedDataObject newRelObject = new RelatedDataObject();
    						newRelObject.setCode("t" + i++);
    						newRelObject.setType("table");
    						newRelObject.setRef(dsName);
    						List<Field> newObjFields = new ArrayList<>();
    						newRelObject.setFields(newObjFields);
    						List<Relation> newRelations = new ArrayList<>();
    						DsFieldInfoExample example = new DsFieldInfoExample();
    						example.createCriteria().andDsNameEqualTo(dsName).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L);
    						List<DsFieldInfo> dsRefFields = dsFieldInfoMapper.selectByExample(example);
    						Map<String, DsFieldInfo> fieldNameMap = dsRefFields.stream().collect(Collectors.toMap(DsFieldInfo::getFieldName, Function.identity()));
    						List<String> pkFields = dsRefFields.stream().filter(item -> item.getIsPk() == 1).map(item -> item.getFieldName()).collect(Collectors.toList());
    						if (CollectionUtils.isNotEmpty(relObject.getRelations())) {
	    						for(Relation rel : relObject.getRelations()) {
	    							Relation newRel = new Relation();
	    							newRel.setField(pkFields.get(0));
	    							newRel.setRelatedField(rel.getRelatedField());
	    							newRelations.add(newRel);
	    							
	    							Field newObjField = new Field();
		    						DsFieldInfo dsField = fieldNameMap.get(pkFields.get(0));
		    						newObjField.setCode(dsField.getFieldName());
		    						newObjField.setName(dsField.getFieldDesc());
		    						newObjField.setType(dsField.getFieldType());
		    						newObjField.setRef(StringUtils.isNotBlank(dsField.getFieldUniqueName()) ? dsField.getFieldUniqueName() : dsField.getFieldName());
		    						newObjField.setPk(true);
		    						newObjFields.add(newObjField);
	    						}
    						} else {
    							//默认按数据源主键进行关联
    							Relation newRel = new Relation();
    							newRel.setField(pkFields.get(0));
    							newRel.setRelatedField(newViewModel.getObject().getCode() + "." + newViewModel.getObject().getFields().stream().filter(item -> item.isPk()).map(item -> item.getCode()).collect(Collectors.toList()).get(0));
    							newRelations.add(newRel);
    							
    							Field newObjField = new Field();
	    						DsFieldInfo dsField = fieldNameMap.get(pkFields.get(0));
	    						newObjField.setCode(dsField.getFieldName());
	    						newObjField.setName(dsField.getFieldDesc());
	    						newObjField.setType(dsField.getFieldType());
	    						newObjField.setRef(StringUtils.isNotBlank(dsField.getFieldUniqueName()) ? dsField.getFieldUniqueName() : dsField.getFieldName());
	    						newObjField.setPk(true);
	    						newObjFields.add(newObjField);
    						}
    						newRelObject.setRelations(newRelations);
    						newRelObject.setRelationType("LEFT JOIN");
    						for (DsFieldInfo dsFieldInfo : refDsFieldsMap.get(dsName)) {
	    						JSONObject extInfoJson = JSON.parseObject(dsFieldInfo.getExtInfo());
	    						
	    						Field newObjField = new Field();
	    						DsFieldInfo dsField = fieldNameMap.get(extInfoJson.getString("fieldName"));
	    						newObjField.setCode(dsFieldInfo.getFieldName());
	    						newObjField.setName(dsFieldInfo.getFieldDesc());
	    						newObjField.setType(dsFieldInfo.getFieldType());
	    						newObjField.setRef(StringUtils.isNotBlank(dsField.getFieldUniqueName()) ? dsField.getFieldUniqueName() : dsField.getFieldName());
	    						newObjFields.add(newObjField);
	    						if ("multivalue".equalsIgnoreCase(dsFieldInfo.getDataType())) {
	    							newObjField.setMultivalue(true);
	    							String extInfo = dsFieldInfo.getExtInfo();
	    							if (StringUtils.isNotBlank(extInfo) && JSON.parseObject(extInfo) != null) {
	    								newObjField.setMvToken(JSON.parseObject(extInfo).getString("separator"));
	    							} else {
	    								newObjField.setMvToken(",");
	    							}
	    						}
    						}
    						relObjects.add(newRelObject);
    					}
    					//metadata object 去掉ref fields
						RelatedDataObject newRelObject = new RelatedDataObject();
						newRelObject.setCode(relObject.getCode());
						newRelObject.setRef(relObject.getRef());
						newRelObject.setType(relObject.getType());
						newRelObject.setRelationType("LEFT JOIN");
						List<Relation> newRelations = new ArrayList<>();
						List<Field> newObjFields = new ArrayList<>();
						String objPkField = dsFields.stream().filter(item -> item.getIsPk() == 1).map(item -> item.getFieldName()).collect(Collectors.toList()).get(0);
						if (CollectionUtils.isNotEmpty(relObject.getRelations())) {
    						for(Relation rel : relObject.getRelations()) {
    							Relation newRel = new Relation();
    							newRel.setField(objPkField);
    							newRel.setRelatedField(rel.getRelatedField());
    							newRelations.add(newRel);
    						}
						} else {
							//默认按数据源主键进行关联
							Relation newRel = new Relation();
							newRel.setField(objPkField);
							newRel.setRelatedField(newViewModel.getObject().getCode() + "." + newViewModel.getObject().getFields().stream().filter(item -> item.isPk()).map(item -> item.getCode()).collect(Collectors.toList()).get(0));
							newRelations.add(newRel);
						}
						newRelObject.setRelations(newRelations);
						newRelObject.setFields(newObjFields);
						if (CollectionUtils.isNotEmpty(relObject.getFields())) {
							for (Field field : relObject.getFields()) {
								if (!refDsFieldNames.contains(field.getCode())) {
									Field newField = new Field();
									BeanUtils.copyProperties(field, newField);
									if (StringUtils.isBlank(field.getType())) {
										newField.setType(dsFieldMap.get(field.getCode()).getFieldType());
									}
		    						if (dsFieldMap.get(field.getCode()) != null && "multivalue".equalsIgnoreCase(dsFieldMap.get(field.getCode()).getDataType())) {
		    							newField.setMultivalue(true);
		    							String extInfo = dsFieldMap.get(field.getCode()).getExtInfo();
		    							if (StringUtils.isNotBlank(extInfo) && JSON.parseObject(extInfo) != null) {
		    								newField.setMvToken(JSON.parseObject(extInfo).getString("separator"));
		    							} else {
		    								newField.setMvToken(",");
		    							}
		    						}
									newObjFields.add(newField);
								}
							}
						} else {
							for (DsFieldInfo dsField : dsFields) {
								if (!refDsFieldNames.contains(dsField.getFieldName())) {
									Field newField = new Field();
									newField.setCode(dsField.getFieldName());
									newField.setName(dsField.getFieldDesc());
									newField.setType(dsField.getFieldType());
									newField.setRef(StringUtils.isNotBlank(dsField.getFieldUniqueName()) ? dsField.getFieldUniqueName() : dsField.getFieldName());
									newField.setPk((dsField.getIsPk() != null && dsField.getIsPk() == 1) ? true : false);
		    						if ("multivalue".equalsIgnoreCase(dsField.getDataType())) {
		    							newField.setMultivalue(true);
		    							String extInfo = dsField.getExtInfo();
		    							if (StringUtils.isNotBlank(extInfo) && JSON.parseObject(extInfo) != null) {
		    								newField.setMvToken(JSON.parseObject(extInfo).getString("separator"));
		    							} else {
		    								newField.setMvToken(",");
		    							}
		    						}
									newObjFields.add(newField);
								}
							}
						}
						relObjects.add(newRelObject);
    				} else {
    					RelatedDataObject newRelObject = new RelatedDataObject();
    					newRelObject.setCode(relObject.getCode());
    					newRelObject.setRef(relObject.getRef());
    					newRelObject.setType(relObject.getType());
						newRelObject.setRelationType("LEFT JOIN");
    					List<Field> newRelObjFields = new ArrayList<>();
						List<Relation> newRelations = new ArrayList<>();
						String objPkField = dsFields.stream().filter(item -> item.getIsPk() == 1).map(item -> item.getFieldName()).collect(Collectors.toList()).get(0);
						if (CollectionUtils.isNotEmpty(relObject.getRelations())) {
    						for(Relation rel : relObject.getRelations()) {
    							Relation newRel = new Relation();
    							newRel.setField(objPkField);
    							newRel.setRelatedField(rel.getRelatedField());
    							newRelations.add(newRel);
    						}
						} else {
							//默认按数据源主键进行关联
							Relation newRel = new Relation();
							newRel.setField(objPkField);
							newRel.setRelatedField(newViewModel.getObject().getCode() + "." + newViewModel.getObject().getFields().stream().filter(item -> item.isPk()).map(item -> item.getCode()).collect(Collectors.toList()).get(0));
							newRelations.add(newRel);
						}
						newRelObject.setRelations(newRelations);
    					newRelObject.setFields(newRelObjFields);
    					if (CollectionUtils.isNotEmpty(viewModel.getObject().getFields())) {
    						for (Field field : viewModel.getObject().getFields()) {
    							Field newField = new Field();
    							BeanUtils.copyProperties(field, newField);
    							if (StringUtils.isBlank(field.getType())) {
    								newField.setType(dsFieldMap.get(field.getCode()).getFieldType());
    							}
    							if (StringUtils.isBlank(field.getRef())) {
    								newField.setRef(dsFieldMap.get(field.getCode()).getFieldUniqueName());
    							}
	    						if (dsFieldMap.get(field.getCode()) != null && "multivalue".equalsIgnoreCase(dsFieldMap.get(field.getCode()).getDataType())) {
	    							newField.setMultivalue(true);
	    							String extInfo = dsFieldMap.get(field.getCode()).getExtInfo();
	    							if (StringUtils.isNotBlank(extInfo) && JSON.parseObject(extInfo) != null) {
	    								newField.setMvToken(JSON.parseObject(extInfo).getString("separator"));
	    							} else {
	    								newField.setMvToken(",");
	    							}
	    						}
    							newRelObjFields.add(newField);
    						}
    					} else {
    						for (DsFieldInfo dsField : dsFields) {
    							Field newField = new Field();
    							newField.setCode(dsField.getFieldName());
    							newField.setType(dsField.getFieldType());
    							newField.setRef(dsField.getFieldUniqueName());
    							newField.setName(dsField.getFieldDesc());
    							newRelObjFields.add(newField);
	    						if ("multivalue".equalsIgnoreCase(dsField.getDataType())) {
	    							newField.setMultivalue(true);
	    							String extInfo = dsField.getExtInfo();
	    							if (StringUtils.isNotBlank(extInfo) && JSON.parseObject(extInfo) != null) {
	    								newField.setMvToken(JSON.parseObject(extInfo).getString("separator"));
	    							} else {
	    								newField.setMvToken(",");
	    							}
	    						}
    							
    							if (dsField.getIsPk() == 1) {
    								newField.setPk(true);
    							}
    						}
    					}
    					relObjects.add(newRelObject);
    				}
    	    	} else {
    	    		relObjects.add(relObject);
    	    	}
    		}
    	}
    	log.info("new yaml={}", YamlUtil.getYaml(newViewModel));
		return newViewModel;
	}

	private List<DsFieldInfo> getDsFieldsByDsUniqueName(String tenantId, DataObject dataObject, ViewModel viewModel) {
		List<DsFieldInfo> dsFields = new ArrayList<>();
		if (CollectionUtils.isEmpty(dataObject.getFields())
				|| (dataObject.getCode().equals(viewModel.getObject().getCode()) && viewModel.isDynamic())) {
			DsFieldInfoExample example = new DsFieldInfoExample();
			example.createCriteria().andDsUniqueNameEqualTo(dataObject.getRef()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L);
			dsFields = dsFieldInfoMapper.selectByExample(example);
		} else {
			List<String> fieldNames = dataObject.getFields().stream().map(item -> item.getCode()).collect(Collectors.toList());
			DsFieldInfoExample example = new DsFieldInfoExample();
			example.createCriteria().andDsUniqueNameEqualTo(dataObject.getRef()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andFieldNameIn(fieldNames);
			dsFields = dsFieldInfoMapper.selectByExample(example);
		}
		return dsFields;
	}

	private List<DsFieldInfo> getDsFieldsByDsName(String tenantId, DataObject dataObject) {
		List<DsFieldInfo> dsFields = new ArrayList<>();
		if (CollectionUtils.isEmpty(dataObject.getFields())) {
			DsFieldInfoExample example = new DsFieldInfoExample();
			example.createCriteria().andDsNameEqualTo(dataObject.getRef()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L);
			dsFields = dsFieldInfoMapper.selectByExample(example);
		} else {
			List<String> fieldNames = dataObject.getFields().stream().map(item -> item.getCode()).collect(Collectors.toList());
			DsFieldInfoExample example = new DsFieldInfoExample();
			example.createCriteria().andDsNameEqualTo(dataObject.getRef()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andFieldNameIn(fieldNames);
			dsFields = dsFieldInfoMapper.selectByExample(example);
		}
		return dsFields;
	}

	public int getParallel(String tenantId, String datatubeLevel, Integer predictQph) {
		int parallel = 1;
		if (StringUtils.isBlank(datatubeLevel) || predictQph == null) {
			return parallel;
		}
		if ("minor".equalsIgnoreCase(datatubeLevel)) {
			if (predictQph >= 0 && predictQph < 100) {
				parallel = 1;
			} else if (predictQph >= 100 && predictQph < 1000) {
				parallel = 3;
			} else if (predictQph >= 1000 && predictQph < 10000) {
				parallel = 3;
			} else if (predictQph >= 10000 && predictQph < 100000) {
				parallel = 6;
			} else if (predictQph >= 100000) {
				parallel = 6;
			}
		} else if ("normal".equalsIgnoreCase(datatubeLevel)) {
			if (predictQph >= 0 && predictQph < 100) {
				parallel = 3;
			} else if (predictQph >= 100 && predictQph < 1000) {
				parallel = 3;
			} else if (predictQph >= 1000 && predictQph < 10000) {
				parallel = 6;
			} else if (predictQph >= 10000 && predictQph < 100000) {
				parallel = 12;
			} else if (predictQph >= 100000) {
				parallel = 24;
			}
		} else if ("major".equalsIgnoreCase(datatubeLevel)) {
			if (predictQph >= 0 && predictQph < 100) {
				parallel = 3;
			} else if (predictQph >= 100 && predictQph < 1000) {
				parallel = 6;
			} else if (predictQph >= 1000 && predictQph < 10000) {
				parallel = 12;
			} else if (predictQph >= 10000 && predictQph < 100000) {
				parallel = 24;
			} else if (predictQph >= 100000) {
				parallel = 48;
			}
		} else if ("super".equalsIgnoreCase(datatubeLevel)) {
			if (predictQph >= 0 && predictQph < 100) {
				parallel = 6;
			} else if (predictQph >= 100 && predictQph < 1000) {
				parallel = 12;
			} else if (predictQph >= 1000 && predictQph < 10000) {
				parallel = 24;
			} else if (predictQph >= 10000 && predictQph < 100000) {
				parallel = 60;
			} else if (predictQph >= 100000) {
				parallel = 120;
			}
		}
		return parallel;
	}
}