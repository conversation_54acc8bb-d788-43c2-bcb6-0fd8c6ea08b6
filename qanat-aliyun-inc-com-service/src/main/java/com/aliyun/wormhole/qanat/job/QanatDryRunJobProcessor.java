package com.aliyun.wormhole.qanat.job;

import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.aliyun.wormhole.qanat.api.dag.Node;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 空跑任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatDryRunJobProcessor extends AbstractQanatNodeJobProcessor<Node> {

	@Override
	void doProcess(Map<String, Object> instParamsMap, Node node) {
	    log.info("[{}]instParamsMap={}", node.getId(), JSON.toJSONString(instParamsMap));
	}
}