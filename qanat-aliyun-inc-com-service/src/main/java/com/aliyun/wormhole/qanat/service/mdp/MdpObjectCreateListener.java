package com.aliyun.wormhole.qanat.service.mdp;

import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.service.adapter.RtdwViewModelTaskServiceAdapter;
import com.aliyun.wormhole.qanat.service.metaq.MetaQProducer;
import com.taobao.metaq.client.MetaPushConsumer;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MdpObjectCreateListener {
    
    @Resource
    private RtdwViewModelTaskServiceAdapter rtdwViewModelTaskService;
    
    @Resource
    private DatasourceService dsInfoService;
    
    @Resource
    private MetaQProducer metaQProducer;

    @Value("${qanat.object.appname}")
    private String objAppName;
    
    @Value("${qanat.unit.id}")
    private String tenantId;
    
    @PostConstruct
    private void init() {
    	MetaPushConsumer consumer = new MetaPushConsumer("CID-qanat-mdp-object_change");
    	try {
	        consumer.subscribe("TOPIC_META_DATA_CHANGE", "OBJECT");
			consumer.setConsumeThreadMin(1);
			consumer.setConsumeThreadMax(10);
			consumer.setPullInterval(1000);
			
	        consumer.setMessageListener(new MessageListenerConcurrently() {
	            @Override
	            public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
	                for (MessageExt msg : msgs) {
	                    try {
	                        String msgBody = new String(msg.getBody(), "utf-8");
	                        log.info("msg={}", msgBody);
	                        JSONObject json = JSON.parseObject(msgBody);
	                        if (!"OBJECT".equalsIgnoreCase(json.getString("entityType"))) {
	                        	continue;
	                        }
	                        String objectType = json.getJSONObject("extParam").getString("objectType");
	                        String objectUniqueCode = json.getString("uniqueCode");
	                        String operateType = json.getJSONObject("extParam").getString("operate");
	                        String operateEmpid = json.getJSONObject("extParam").getJSONObject("objectVO").getString("creator");
	                        String storeEngine = json.getJSONObject("extParam").getJSONObject("objectVO").getString("storeEngine");
	                        if ("CREATE".equalsIgnoreCase(operateType)) {
	                        	if ("slot".equalsIgnoreCase(storeEngine)) {
	                        		Map<String, Object> slotMetaMap = dsInfoService.getObjectSlotMeta(objectType, objectUniqueCode);
	                        		json.put("slotMeta", slotMetaMap);
	                        		json.put("storeType", "slot");
	                        	}
		                        DataResult<Long> result = rtdwViewModelTaskService.createViewModelFromObject(tenantId, objAppName, objectType, objectUniqueCode, operateEmpid, json.toJSONString());
		                        log.info("create object result:{}", result != null ? result.getData() : null);
		                        String exportMsg = msgBody + "`dwd_" + objectUniqueCode.toLowerCase();
		                        metaQProducer.sendMsg("TOPIC_QANAT_VM_CREATE", "", objectUniqueCode, exportMsg);
	                        }
	                    } catch (Exception e) {
	                        log.error("create object failed, message={}", e.getMessage(), e);
	    	                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
	                    }
	                }
	                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
	            }
	        });

	        consumer.start();
    	} catch(Exception e) {
    		log.error("create consumer for object create failed", e);
    	}
    }
}