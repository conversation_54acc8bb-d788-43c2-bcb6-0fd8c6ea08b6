package com.aliyun.wormhole.qanat.service.base;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.Dag;
import com.aliyun.wormhole.qanat.api.dag.DagEngineType;
import com.aliyun.wormhole.qanat.api.dag.DagInstStatus;
import com.aliyun.wormhole.qanat.api.dag.DagPolicy;
import com.aliyun.wormhole.qanat.api.dag.Node;
import com.aliyun.wormhole.qanat.api.dag.NodeAction;
import com.aliyun.wormhole.qanat.api.dag.NodeExecType;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.TaskInfoDTO;
import com.aliyun.wormhole.qanat.api.dto.TaskInfoPageRequest;
import com.aliyun.wormhole.qanat.api.dto.TaskInfoRequest;
import com.aliyun.wormhole.qanat.api.dto.TaskInstanceDTO;
import com.aliyun.wormhole.qanat.api.dto.TaskInstanceRequest;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.TaskService;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfo;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfoWithBLOBs;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstanceExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskScript;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskScriptMapper;
import com.aliyun.wormhole.qanat.service.dag.DagService;
import com.aliyun.wormhole.qanat.service.groovy.GroovyService;
import com.aliyun.wormhole.qanat.service.schedulerx.SchedulerXJobService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 数据同步服务
 * <AUTHOR>
 * 2019年7月23日
 */
@Slf4j
@Component
@HSFProvider(serviceInterface = TaskService.class)
public class TaskServiceImpl implements TaskService {
    
    @Resource
    private DagService dagService;
    
    @Resource
    private TaskInfoMapper taskInfoMapper;
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;
    
    @Resource
    private SchedulerXJobService schedulerXJobService;
    
    @Resource
    private GroovyService groovyService;
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private TaskScriptMapper taskScriptMapper;

    @Override
    public DataResult<PageInfo<TaskInfoDTO>> list4Page(TaskInfoPageRequest taskReq) {
        log.info("start list4Page({})", taskReq);
        DataResult<PageInfo<TaskInfoDTO>> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	if (StringUtils.isBlank(taskReq.getTenantId()) || StringUtils.isBlank(taskReq.getAppName())) {
        		throw new QanatBizException("tenantId or appName is empty");
        	}
            TaskInfoExample example = new TaskInfoExample();
            example.setOrderByClause("gmt_modified desc");
            TaskInfoExample.Criteria criteria = example.createCriteria();
            criteria.andIsDeletedEqualTo(0L);
            criteria.andTenantIdEqualTo(taskReq.getTenantId());
            criteria.andAppNameEqualTo(taskReq.getAppName());
            if (StringUtils.isNotBlank(taskReq.getName())) {
            	criteria.andNameLike("%" + taskReq.getName() + "%");
            }
            if (StringUtils.isNotBlank(taskReq.getTaskDesc())) {
            	criteria.andTaskDescLike("%" + taskReq.getTaskDesc() + "%");
            }
            PageHelper.startPage(taskReq.getPageNum(), taskReq.getPageSize());
            List<TaskInfo> taskList = taskInfoMapper.selectByExample(example);
            PageInfo<TaskInfo> pageInfoDO = new PageInfo<TaskInfo>(taskList);
            List<TaskInfoDTO> dtoList = new ArrayList<>();
            if (pageInfoDO != null && CollectionUtils.isNotEmpty(pageInfoDO.getList())) {
            	for (TaskInfo taskInfoDO : pageInfoDO.getList()) {
            		TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
            		BeanUtils.copyProperties(taskInfoDO, taskInfoDTO);
            		dtoList.add(taskInfoDTO);
            	}
            }
        	PageInfo<TaskInfoDTO> pageInfoDTO = new PageInfo<>();
        	pageInfoDTO.setList(dtoList);
        	pageInfoDTO.setPageNum(pageInfoDO.getPageNum());
        	pageInfoDTO.setPages(pageInfoDO.getPages());
        	pageInfoDTO.setPageSize(pageInfoDO.getPageSize());
        	pageInfoDTO.setTotal(pageInfoDO.getTotal());
        	result.setData(pageInfoDTO);
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("list4Page failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("list4Page failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public DataResult<PageInfo<TaskInstanceDTO>> listTaskInstance4Page(TaskInstanceRequest req) {
        log.info("start listTaskInstance4Page({})", req);
        DataResult<PageInfo<TaskInstanceDTO>> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	if (StringUtils.isBlank(req.getTenantId()) || StringUtils.isBlank(req.getAppName())) {
        		throw new QanatBizException("tenantId or appName is empty");
        	}
            TaskInstanceExample example = new TaskInstanceExample();
            example.setOrderByClause("gmt_modified desc");
            TaskInstanceExample.Criteria criteria = example.createCriteria();
            criteria.andTenantIdEqualTo(req.getTenantId());
            criteria.andAppNameEqualTo(req.getAppName());
            
            if (StringUtils.isNotBlank(req.getTaskName())) {
            	criteria.andTaskNameLike("%" + req.getTaskName() + "%");
            }
            if (null != req.getTaskId()) {
            	criteria.andTaskIdEqualTo(req.getTaskId());
            }
            if (null != req.getId()) {
            	criteria.andIdEqualTo(req.getId());
            }
            if (null != req.getParentInstanceId()) {
            	criteria.andParentInstanceIdEqualTo(req.getParentInstanceId());
            }
            if (null != req.getStatus()) {
            	criteria.andStatusEqualTo(req.getStatus());
            }
            if (StringUtils.isNotBlank(req.getOperator())) {
            	criteria.andOperatorEqualTo(req.getOperator());
            }
            if (StringUtils.isNotBlank(req.getExternalId())) {
            	criteria.andExternalIdEqualTo(req.getExternalId());
            }
            if (StringUtils.isNotBlank(req.getExternalInstId())) {
            	criteria.andExternalInstIdEqualTo(req.getExternalInstId());
            }
            if (StringUtils.isNotBlank(req.getHostAddr())) {
            	criteria.andHostAddrEqualTo(req.getHostAddr());
            }
            if (StringUtils.isNotBlank(req.getNodeAction())) {
            	criteria.andNodeActionEqualTo(req.getNodeAction());
            }
            PageHelper.startPage(req.getPageNum(), req.getPageSize());
            List<TaskInstance> taskList = taskInstanceMapper.selectByExampleWithBLOBs(example);
            PageInfo<TaskInstance> pageInfoDO = new PageInfo<TaskInstance>(taskList);
            List<TaskInstanceDTO> dtoList = new ArrayList<>();
            if (pageInfoDO != null && CollectionUtils.isNotEmpty(pageInfoDO.getList())) {
            	for (TaskInstance taskInstDO : pageInfoDO.getList()) {
            		TaskInstanceDTO taskInstDTO = new TaskInstanceDTO();
            		BeanUtils.copyProperties(taskInstDO, taskInstDTO);
            		dtoList.add(taskInstDTO);
            	}
            }
        	PageInfo<TaskInstanceDTO> pageInfoDTO = new PageInfo<>();
        	pageInfoDTO.setList(dtoList);
        	pageInfoDTO.setPageNum(pageInfoDO.getPageNum());
        	pageInfoDTO.setPages(pageInfoDO.getPages());
        	pageInfoDTO.setPageSize(pageInfoDO.getPageSize());
        	pageInfoDTO.setTotal(pageInfoDO.getTotal());
        	result.setData(pageInfoDTO);
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("listTaskInstance4Page failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("listTaskInstance4Page failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public DataResult<Long> createTaskFromDAG(String tenantId, String appName, String operateEmpid, String taskDesc, String dagScript) {
        log.info("start createTaskFromDAG({},{},{},{},{})", tenantId, appName, operateEmpid, taskDesc, dagScript);
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
	    	TaskInfoRequest taskInfo = new TaskInfoRequest();
	    	taskInfo.setDagScript(dagScript);
	        taskInfo.setOperateEmpid(operateEmpid);
	        taskInfo.setTaskDesc(taskDesc);
	        taskInfo.setPolicy(DagPolicy.ETL.toString());
	        taskInfo.setTenantId(tenantId);
	        taskInfo.setAppName(appName);
	        DataResult<Long> createResult = this.createTask(taskInfo);
	        if (createResult == null || !createResult.getSuccess() || createResult.getData() == null) {
	        	throw new QanatBizException(createResult.getMessage());
	        }
	        TaskInfoWithBLOBs task = taskInfoMapper.selectByPrimaryKey(createResult.getData());
	    	if (StringUtils.isBlank(task.getTimeExpression())) {
	    		result = this.runTask(tenantId, operateEmpid, createResult.getData());
	    	}
    	} catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("createTaskFromDAG failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("createTaskFromDAG failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public DataResult<Long> createTask(TaskInfoRequest taskReq) {
        log.info("start createTask({})", taskReq);
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	if (StringUtils.isBlank(taskReq.getTenantId()) || StringUtils.isBlank(taskReq.getAppName())) {
        		throw new QanatBizException("tenantId or appName is empty");
        	}
            TaskInfoWithBLOBs task = createDagTask(taskReq);
            result.setData(task.getId());
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("createComplexTaskBaseline failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("createComplexTaskBaseline failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public Long createDAGTask(TaskInfoRequest taskReq) {
        TaskInfoWithBLOBs task = createDagTask(taskReq);
        return task.getId();
    }

    @Override
    public Long isTaskExists(String tenantId, String taskName) {
    	TaskInfoExample example = new TaskInfoExample();
        example.createCriteria().andNameEqualTo(taskName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
        List<TaskInfo> taskList = taskInfoMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(taskList)) {
        	return taskList.get(0).getId();
        } else {
        	return null;
        }
    }

    @Override
    public Long createDAGTask(TaskInfoRequest taskReq, Dag dag) {
    	TaskInfoExample example = new TaskInfoExample();
        example.createCriteria().andNameEqualTo(StringUtils.isNotBlank(taskReq.getName()) ? taskReq.getName() : dag.getId()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(taskReq.getTenantId());
        List<TaskInfo> taskList = taskInfoMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(taskList)) {
        	throw new QanatBizException("任务名称[" + taskList.get(0).getName() + "]与其他任务[" + taskList.get(0).getId()  + "]重复");
        }
        
        String dagJson = JSON.toJSONString(dag);
        Date now = new Date();
        
        TaskInfoWithBLOBs task = new TaskInfoWithBLOBs();
        task.setCreateEmpid(taskReq.getOperateEmpid());
        task.setDag(dagJson);
        task.setEngineType(DagEngineType.SCHEDULERX2.toString());
        task.setGmtCreate(now);
        task.setGmtModified(now);
        task.setIsDeleted(0L);
        task.setModifyEmpid(taskReq.getOperateEmpid());
        task.setName(StringUtils.isNotBlank(taskReq.getName()) ? taskReq.getName() : dag.getId());
        task.setPolicy(StringUtils.isBlank(taskReq.getPolicy()) ? DagPolicy.ETL.toString() : taskReq.getPolicy());
        task.setTaskDesc(taskReq.getTaskDesc());
        task.setDagScript(taskReq.getDagScript());
        task.setTenantId(taskReq.getTenantId());
        task.setAppName(taskReq.getAppName());
        task.setTimeExpression(dag.getTimeExpression());
        taskInfoMapper.insert(task);
        //创建任务执行DAG
        createDagJob(task.getId(), taskReq.getOperateEmpid(), dag);
        return task.getId();
    }

    private TaskInfoWithBLOBs createDagTask(TaskInfoRequest taskReq) {
        
        String dagJson = groovyService.evalDag(taskReq.getDagScript());
        Date now = new Date();
        Dag dag = dagService.getDagByJson(dagJson);
        
        TaskInfoExample example = new TaskInfoExample();
        example.createCriteria().andNameEqualTo(StringUtils.isNotBlank(taskReq.getName()) ? taskReq.getName() : dag.getId()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(taskReq.getTenantId());
        List<TaskInfo> taskList = taskInfoMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(taskList)) {
    	    throw new QanatBizException("任务名称[" + taskList.get(0).getName() + "]与其他任务[" + taskList.get(0).getId()  + "]重复");
        }
        
        TaskInfoWithBLOBs task = new TaskInfoWithBLOBs();
        task.setCreateEmpid(taskReq.getOperateEmpid());
        task.setDag(dagJson);
        task.setEngineType(DagEngineType.SCHEDULERX2.toString());
        task.setGmtCreate(now);
        task.setGmtModified(now);
        task.setIsDeleted(0L);
        task.setModifyEmpid(taskReq.getOperateEmpid());
        task.setName(StringUtils.isNotBlank(taskReq.getName()) ? taskReq.getName() : dag.getId());
        task.setPolicy(StringUtils.isBlank(taskReq.getPolicy()) ? DagPolicy.ETL.toString() : taskReq.getPolicy());
        task.setTaskDesc(taskReq.getTaskDesc());
        task.setDagScript(taskReq.getDagScript());
        task.setTenantId(taskReq.getTenantId());
        task.setAppName(taskReq.getAppName());
        task.setTimeExpression(dag.getTimeExpression());
        taskInfoMapper.insert(task);
        //创建任务执行DAG
        createDagJob(task.getId(), taskReq.getOperateEmpid(), dag);
        return task;
    }

    @Override
    public DataResult<Long> runTask(String tenantId, String empid, Long taskId) {
        return runTask(tenantId, empid, taskId, null);
    }

    @Override
    public DataResult<Long> runTask(String tenantId, String empid, Long taskId, Map<String, Object> params) {
        log.info("start runTask({},{},{},{})", tenantId, empid, taskId, JSON.toJSONString(params));
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
            TaskInstanceExample example = new TaskInstanceExample();
            example.createCriteria().andTaskIdEqualTo(taskId)
            .andStatusEqualTo(DagInstStatus.EXECUTING.getCode().byteValue());
            List<TaskInstance> subTaskInstList = taskInstanceMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(subTaskInstList)) {
                throw new QanatBizException("存在执行中的任务");
            }
            TaskInfoWithBLOBs task = taskInfoMapper.selectByPrimaryKey(taskId);
            Map<String, Object> instParamsMap = new HashMap<>();
            instParamsMap.put("requestId", tenantId);
            instParamsMap.put("operator", empid);
            if (params != null && CollectionUtils.isNotEmpty(params.keySet())) {
                instParamsMap.putAll(params);
            }
            DataResult<Long> jobResut = schedulerXJobService.runJob(task.getTenantId(), task.getAppName(), Long.valueOf(task.getExternalId()), instParamsMap);
            result.setData(jobResut.getData());
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("runTask failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("runTask failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public DataResult<Boolean> stopTask(String tenantId, String empid, Long taskId) {
        log.info("start stopTask({},{},{})", tenantId, empid, taskId);
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);

        TaskInstanceExample example = new TaskInstanceExample();
        example.createCriteria().andTaskIdEqualTo(taskId)
        .andNodeActionEqualTo(NodeAction.BATCH.toString())
        .andStatusEqualTo(DagInstStatus.EXECUTING.getCode().byteValue());
        List<TaskInstance> subTaskInstList = taskInstanceMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(subTaskInstList)) {
        	List<TaskInstance> dataxSubTaskInstList = subTaskInstList.stream().filter(e -> "JOB:com.aliyun.wormhole.qanat.job.QanatDataXJobProcessor".equalsIgnoreCase(e.getTaskCommand()))
        							.collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dataxSubTaskInstList)) {
            	throw new QanatBizException("存在执行中的DataX作业不能停止任务，请联系管理员。");
            }
        }
        example = new TaskInstanceExample();
        example.createCriteria().andTaskIdEqualTo(taskId)
        .andNodeActionEqualTo(NodeAction.WORKFLOW.toString())
        .andStatusEqualTo(DagInstStatus.EXECUTING.getCode().byteValue());
        subTaskInstList = taskInstanceMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(subTaskInstList)) {
            throw new QanatBizException("存在执行中的BPMS流程不能停止任务，请联系管理员。");
        }
        
        new Thread(() -> {
            try {
                TaskInstanceExample taskInstExample = new TaskInstanceExample();
                taskInstExample.createCriteria().andTaskIdEqualTo(taskId)
                .andParentInstanceIdIsNotNull()
                .andStatusEqualTo(DagInstStatus.EXECUTING.getCode().byteValue());
                List<TaskInstance> instList = taskInstanceMapper.selectByExampleWithBLOBs(taskInstExample);
                if (CollectionUtils.isNotEmpty(instList)) {
                    for (TaskInstance taskInst : instList) {
                        JSONObject execJson = JSON.parseObject(taskInst.getExecParam());
                        if (execJson != null) {
                            JSONObject nodeJson = execJson.getJSONObject("node");
                            if (nodeJson != null) {
                                String nodeClass = nodeJson.getString("nodeClass");
                                if ("com.aliyun.wormhole.qanat.api.dag.BlinkStreamNode".equals(nodeClass)) {
                                    String jobName = nodeJson.getString("jobName");
                                    try{blinkService.stopJob(taskInst.getTenantId(), taskInst.getAppName(), jobName);}catch(Exception e) {}
                                    log.info("[{}]taskInst[{}:{}] blink job[{}] is stopped", tenantId, taskInst.getId(), taskInst.getTaskName(), jobName);
                                } else if ("com.aliyun.wormhole.qanat.api.dag.BlinkBatchNode".equals(nodeClass)) {
                                    String jobName = nodeJson.getString("jobName");
                                    try{blinkService.stopJob(taskInst.getTenantId(), taskInst.getAppName(), jobName);}catch(Exception e) {}
                                    log.info("[{}]taskInst[{}:{}] blink job[{}] is stopped", tenantId, taskInst.getId(), taskInst.getTaskName(), jobName);
                                    schedulerXJobService.killJob(taskInst.getTenantId(), taskInst.getAppName(), Long.valueOf(taskInst.getExternalId()), Long.valueOf(taskInst.getExternalInstId()));
                                } else {
                                    schedulerXJobService.killJob(taskInst.getTenantId(), taskInst.getAppName(), Long.valueOf(taskInst.getExternalId()), Long.valueOf(taskInst.getExternalInstId()));
                                }
                                TaskInstance updTaskInst = new TaskInstance();
                                updTaskInst.setId(taskInst.getId());
                                updTaskInst.setStatus(DagInstStatus.FAILED.getCode().byteValue());
                                updTaskInst.setModifyEmpid(empid);
                                updTaskInst.setGmtModified(new Date());
                                updTaskInst.setEndTime(new Date());
                                taskInstanceMapper.updateByPrimaryKeySelective(updTaskInst);
                            }
                        }
                    }
                }
                taskInstExample = new TaskInstanceExample();
                taskInstExample.createCriteria().andTaskIdEqualTo(taskId)
								                .andParentInstanceIdIsNull()
								                .andStatusEqualTo(DagInstStatus.EXECUTING.getCode().byteValue());
                instList = taskInstanceMapper.selectByExampleWithBLOBs(taskInstExample);
                if (CollectionUtils.isNotEmpty(instList)) {
                	TaskInstance updTaskInst = new TaskInstance();
	                updTaskInst.setId(instList.get(0).getId());
	                updTaskInst.setStatus(DagInstStatus.FAILED.getCode().byteValue());
	                updTaskInst.setModifyEmpid(empid);
	                updTaskInst.setGmtModified(new Date());
	                updTaskInst.setEndTime(new Date());
	                taskInstanceMapper.updateByPrimaryKeySelective(updTaskInst);
                }
                result.setData(true);
            } catch (QanatBizException e) {
                result.setData(false);
                result.setSuccess(false);
                result.setCode(e.getCode());
                result.setMessage(e.getMessage());
                log.error("stopTask failed, error={}", e.getMessage());
            } catch (Exception e) {
                result.setData(false);
                result.setSuccess(false);
                result.setCode("500");
                result.setMessage(e.getMessage());
                log.error("stopTask failed, error={}", e.getMessage(), e);
            }
        }).start();
        return result;
    }

    @Override
    public DataResult<Boolean> stopAndDeleteTask(String tenantId, String empid, Long taskId) {
        log.info("start stopTask({},{},{})", tenantId, empid, taskId);
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
//            TaskInstanceExample example = new TaskInstanceExample();
//            example.createCriteria().andTaskIdEqualTo(taskId)
//            .andNodeActionEqualTo(NodeAction.BATCH.toString())
//            .andStatusEqualTo(DagInstStatus.EXECUTING.getCode().byteValue());
//            List<TaskInstance> subTaskInstList = taskInstanceMapper.selectByExample(example);
//            if (CollectionUtils.isNotEmpty(subTaskInstList)) {
//            	List<TaskInstance> dataxSubTaskInstList = subTaskInstList.stream().filter(e -> "JOB:com.aliyun.wormhole.qanat.job.QanatDataXJobProcessor".equalsIgnoreCase(e.getTaskCommand()))
//            							.collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(dataxSubTaskInstList)) {
//                	throw new QanatBizException("存在执行中的DataX作业不能停止任务，请联系管理员。");
//                }
//            }
        	TaskInstanceExample example = new TaskInstanceExample();
            example.createCriteria().andTaskIdEqualTo(taskId)
            .andNodeActionEqualTo(NodeAction.WORKFLOW.toString())
            .andStatusEqualTo(DagInstStatus.EXECUTING.getCode().byteValue());
            List<TaskInstance> subTaskInstList = taskInstanceMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(subTaskInstList)) {
                throw new QanatBizException("存在执行中的BPMS流程不能停止任务，请联系管理员。");
            }
            TaskInstanceExample taskInstExample = new TaskInstanceExample();
            taskInstExample.createCriteria().andTaskIdEqualTo(taskId)
            .andParentInstanceIdIsNotNull()
            .andStatusEqualTo(DagInstStatus.EXECUTING.getCode().byteValue());
            List<TaskInstance> instList = taskInstanceMapper.selectByExampleWithBLOBs(taskInstExample);
            List<Long> parentTaskInstIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(instList)) {
                for (TaskInstance taskInst : instList) {
                	if (!parentTaskInstIds.contains(taskInst.getParentInstanceId())) {
                		parentTaskInstIds.add(taskInst.getParentInstanceId());
                	}
                    JSONObject execJson = JSON.parseObject(taskInst.getExecParam());
                    if (execJson != null) {
                        JSONObject nodeJson = execJson.getJSONObject("node");
                        if (nodeJson != null) {
                            String nodeClass = nodeJson.getString("nodeClass");
                            if ("com.aliyun.wormhole.qanat.api.dag.BlinkStreamNode".equals(nodeClass)) {
                                String jobName = nodeJson.getString("jobName");
        						String[] tokens = jobName.split("\\_");
        						String lastToken = tokens[tokens.length-1];
        						String version = lastToken.replace("v", "");
        						if (lastToken.startsWith("v") && StringUtils.isNumeric(version)) {
        							blinkService.dropExistedJob(taskInst.getTenantId(), taskInst.getAppName(), jobName);
        						}
                                log.info("[{}]taskInst[{}:{}] blink job[{}] is dropped", tenantId, taskInst.getId(), taskInst.getTaskName(), jobName);
                            } else if ("com.aliyun.wormhole.qanat.api.dag.BlinkBatchNode".equals(nodeClass)) {
                                String jobName = nodeJson.getString("jobName");
        						String[] tokens = jobName.split("\\_");
        						String lastToken = tokens[tokens.length-1];
        						String version = lastToken.replace("v", "");
        						if (lastToken.startsWith("v") && StringUtils.isNumeric(version)) {
        							blinkService.dropExistedJob(taskInst.getTenantId(), taskInst.getAppName(), jobName);
        						}
                                log.info("[{}]taskInst[{}:{}] blink job[{}] is dropped", tenantId, taskInst.getId(), taskInst.getTaskName(), jobName);
                                schedulerXJobService.killJob(taskInst.getTenantId(), taskInst.getAppName(), Long.valueOf(taskInst.getExternalId()), Long.valueOf(taskInst.getExternalInstId()));
                            } else {
                                schedulerXJobService.killJob(taskInst.getTenantId(), taskInst.getAppName(), Long.valueOf(taskInst.getExternalId()), Long.valueOf(taskInst.getExternalInstId()));
                            }
                            TaskInstance updTaskInst = new TaskInstance();
                            updTaskInst.setId(taskInst.getId());
                            updTaskInst.setStatus(DagInstStatus.FAILED.getCode().byteValue());
                            updTaskInst.setModifyEmpid(empid);
                            updTaskInst.setGmtModified(new Date());
                            updTaskInst.setEndTime(new Date());
                            taskInstanceMapper.updateByPrimaryKeySelective(updTaskInst);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(parentTaskInstIds)) {
    	            taskInstExample = new TaskInstanceExample();
    	            taskInstExample.createCriteria().andTaskIdEqualTo(taskId)
    								                .andIdIn(parentTaskInstIds);
    	            instList = taskInstanceMapper.selectByExampleWithBLOBs(taskInstExample);
    	            if (CollectionUtils.isNotEmpty(instList)) {
    	            	for (int i = 0; i < instList.size(); i++) {
    		            	TaskInstance updTaskInst = new TaskInstance();
    		                updTaskInst.setId(instList.get(i).getId());
    		                updTaskInst.setStatus(DagInstStatus.FAILED.getCode().byteValue());
    		                updTaskInst.setModifyEmpid(empid);
    		                updTaskInst.setGmtModified(new Date());
    		                updTaskInst.setEndTime(new Date());
    		                taskInstanceMapper.updateByPrimaryKeySelective(updTaskInst);
    		                
    		                taskInstExample = new TaskInstanceExample();
    		                taskInstExample.createCriteria().andTaskIdEqualTo(taskId)
    		                .andParentInstanceIdEqualTo(instList.get(i).getId())
    		                .andTaskCommandEqualTo("JOB:com.aliyun.wormhole.qanat.job.QanatBlinkJobProcessor")
    		                .andNodeActionEqualTo("BATCH");
    		                instList = taskInstanceMapper.selectByExampleWithBLOBs(taskInstExample);
    		                if (CollectionUtils.isNotEmpty(instList)) {
    		                    for (TaskInstance taskInst : instList) {
    		                        JSONObject execJson = JSON.parseObject(taskInst.getExecParam());
    		                        if (execJson != null) {
    		                            JSONObject nodeJson = execJson.getJSONObject("node");
    		                            if (nodeJson != null) {
    		                                String nodeClass = nodeJson.getString("nodeClass");
    		                                if ("com.aliyun.wormhole.qanat.api.dag.BlinkBatchNode".equals(nodeClass)) {
    		                                    String jobName = nodeJson.getString("jobName");
    		            						String[] tokens = jobName.split("\\_");
    		            						String lastToken = tokens[tokens.length-1];
    		            						String version = lastToken.replace("v", "");
    		            						if (lastToken.startsWith("v") && StringUtils.isNumeric(version)) {
    		            							blinkService.dropExistedJob(taskInst.getTenantId(), taskInst.getAppName(), jobName);
    		            						}
    		                                    schedulerXJobService.killJob(taskInst.getTenantId(), taskInst.getAppName(), Long.valueOf(taskInst.getExternalId()), Long.valueOf(taskInst.getExternalInstId()));
    		                                    log.info("[{}]taskInst[{}:{}] blink job[{}] is dropped", tenantId, taskInst.getId(), taskInst.getTaskName(), jobName);
    		                                }
    		                            }
    		                        }
    		                    }
    		                }
    	            	}
    	            }
                }
            } else {//删除定时任务中的blink batch Job，通常是已完成状态
            	taskInstExample = new TaskInstanceExample();
                taskInstExample.createCriteria().andTaskIdEqualTo(taskId)
                .andParentInstanceIdIsNotNull()
                .andStatusEqualTo(DagInstStatus.SUCCESS.getCode().byteValue())
                .andTaskCommandEqualTo("JOB:com.aliyun.wormhole.qanat.job.QanatBlinkJobProcessor")
                .andNodeActionEqualTo("BATCH");
                instList = taskInstanceMapper.selectByExampleWithBLOBs(taskInstExample);
                if (CollectionUtils.isNotEmpty(instList)) {
                    for (TaskInstance taskInst : instList) {
                    	if (!parentTaskInstIds.contains(taskInst.getParentInstanceId())) {
                    		parentTaskInstIds.add(taskInst.getParentInstanceId());
                    	}
                        JSONObject execJson = JSON.parseObject(taskInst.getExecParam());
                        if (execJson != null) {
                            JSONObject nodeJson = execJson.getJSONObject("node");
                            if (nodeJson != null) {
                                String nodeClass = nodeJson.getString("nodeClass");
                                if ("com.aliyun.wormhole.qanat.api.dag.BlinkBatchNode".equals(nodeClass)) {
                                    String jobName = nodeJson.getString("jobName");
            						String[] tokens = jobName.split("\\_");
            						String lastToken = tokens[tokens.length-1];
            						String version = lastToken.replace("v", "");
            						if (lastToken.startsWith("v") && StringUtils.isNumeric(version)) {
            							blinkService.dropExistedJob(taskInst.getTenantId(), taskInst.getAppName(), jobName);
            						}
                                    log.info("[{}]taskInst[{}:{}] blink job[{}] is dropped", tenantId, taskInst.getId(), taskInst.getTaskName(), jobName);
                                    schedulerXJobService.killJob(taskInst.getTenantId(), taskInst.getAppName(), Long.valueOf(taskInst.getExternalId()), Long.valueOf(taskInst.getExternalInstId()));
                                }
                            }
                        }
                    }
                }
            }
            log.info("task:{} is stoped", taskId);
            deleteTaskById(tenantId, empid, taskId);
            log.info("task:{} is deleted from schedulerx2", taskId);
            result.setData(true);
        } catch (QanatBizException e) {
            result.setData(false);
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("stopTask failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setData(false);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("stopTask failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public boolean checkIfDagFailed(Long taskInstId) {
        TaskInstanceExample example = new TaskInstanceExample();
        example.createCriteria().andParentInstanceIdEqualTo(taskInstId);
        List<TaskInstance> subTaskInstList = taskInstanceMapper.selectByExample(example);
        for (TaskInstance subTaskInst : subTaskInstList) {
            if (subTaskInst.getStatus().equals(DagInstStatus.FAILED.getCode().byteValue())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean checkIfDagFinished(Long taskInstId) {
        TaskInstanceExample example = new TaskInstanceExample();
        example.createCriteria().andParentInstanceIdEqualTo(taskInstId);
        List<TaskInstance> subTaskInstList = taskInstanceMapper.selectByExample(example);
        for (TaskInstance subTaskInst : subTaskInstList) {
            if (!subTaskInst.getStatus().equals(DagInstStatus.SUCCESS.getCode().byteValue())
                && !subTaskInst.getNodeAction().equals(NodeAction.STREAM.toString())) {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean checkIfPrevFailed(Long taskInstId, List<String> prevList) {
        if (CollectionUtils.isNotEmpty(prevList)) {
            TaskInstanceExample example = new TaskInstanceExample();
            example.createCriteria().andParentInstanceIdEqualTo(taskInstId)
            .andTaskNameIn(prevList)
            .andStatusEqualTo(DagInstStatus.FAILED.getCode().byteValue());
            List<TaskInstance> subTaskInstList = taskInstanceMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(subTaskInstList)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean checkIfPrevReady(Long taskInstId, List<String> prevList) {
        if (CollectionUtils.isNotEmpty(prevList)) {
            TaskInstanceExample example = new TaskInstanceExample();
            example.createCriteria().andParentInstanceIdEqualTo(taskInstId)
            .andTaskNameIn(prevList)
            .andStatusEqualTo(DagInstStatus.SUCCESS.getCode().byteValue());
            List<TaskInstance> successSubTaskInstList = taskInstanceMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(successSubTaskInstList) && successSubTaskInstList.size() == prevList.size()) {
                return true;
            } else {
                example = new TaskInstanceExample();
                example.createCriteria().andParentInstanceIdEqualTo(taskInstId)
                .andTaskNameIn(prevList)
                .andNodeActionEqualTo(NodeAction.STREAM.toString())
                .andStatusEqualTo(DagInstStatus.EXECUTING.getCode().byteValue());
                List<TaskInstance> executingStreamTaskInstList = taskInstanceMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(executingStreamTaskInstList) && (executingStreamTaskInstList.size() + successSubTaskInstList.size()) == prevList.size()) {
                    return true;
                } else {
                    return false;
                }
            }
        }
        return true;
    }

    private void createDagJob(Long taskId, String empid, Dag dag) {
        TaskInfoWithBLOBs task = taskInfoMapper.selectByPrimaryKey(taskId);
        if (task.getEngineType().equalsIgnoreCase(DagEngineType.SCHEDULERX2.toString())) {
            //创建SchedulerX2任务
            DataResult<Long> result = schedulerXJobService.createDagJob(task.getTenantId(), task.getAppName(), task, dag);
            if (result.getSuccess()) {
                Long jobId = result.getData();
                List<Map<String, Object>> subTaskList = new ArrayList<>();
                for (Node node : dag.getNodeList()) {
                    Map<String, Object> subTaskMap = new HashMap<>();
                    subTaskList.add(subTaskMap);
                    if (node.getExecType().equals(NodeExecType.JOB)) {
                        DataResult<Long> nodeResult = schedulerXJobService.createNodeJob(task.getTenantId(), task.getAppName(), task, node);
                        subTaskMap.put("jobId", nodeResult.getData());
                        subTaskMap.put("nodeId", node.getId());
                    }
                }
                Date now = new Date();
                TaskInfoWithBLOBs taskUpd = new TaskInfoWithBLOBs();
                taskUpd.setId(taskId);
                taskUpd.setGmtModified(now);
                taskUpd.setModifyEmpid(empid);
                taskUpd.setExternalId(jobId + "");
                taskUpd.setSubTasks(JSON.toJSONString(subTaskList));
    	        taskUpd.setDag(JSON.toJSONString(dag));
                taskInfoMapper.updateByPrimaryKeySelective(taskUpd);
            }
        }
    }

    @Override
    public DataResult<Boolean> updateTaskDag(Long taskId, String operateEmpid, String dagScript, Boolean isForceStop) {
        log.info("start updateDag({},{},{})", taskId, operateEmpid, dagScript);
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
            String dagJson = groovyService.evalDag(dagScript);
            Dag dag = dagService.getDagByJson(dagJson);
            if (this.updateTaskDag(taskId, operateEmpid, dag, isForceStop)) {
            	result.setData(true);
            	
                TaskInfoWithBLOBs taskUpd = new TaskInfoWithBLOBs();
                taskUpd.setId(taskId);
                taskUpd.setGmtModified(new Date());
                taskUpd.setModifyEmpid(operateEmpid);
    	        taskUpd.setDagScript(dagScript);
                taskInfoMapper.updateByPrimaryKeySelective(taskUpd);
            } else {
            	result.setData(false);
            }
        } catch (Exception e) {
            result.setData(false);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("updateDag failed, error={}", e.getMessage(), e);
        }
        result.setData(true);
        return result;
    }

    @Override
    public Boolean updateTaskDag(Long taskId, String operateEmpid, Dag dag, Boolean isForceStop) {
        TaskInfoExample taskInfoExample = new TaskInfoExample();
        taskInfoExample.createCriteria().andIdEqualTo(taskId).andIsDeletedEqualTo(0L);
        List<TaskInfoWithBLOBs> taskInfoList = taskInfoMapper.selectByExampleWithBLOBs(taskInfoExample);
        if (CollectionUtils.isEmpty(taskInfoList)) {
            throw new QanatBizException("TaskId:" + taskId + " 不存在");
        }
        TaskInfoWithBLOBs taskInfo = taskInfoList.get(0);
        boolean isSuccess = false;
        if (isForceStop) {
            if(stopTaskAndUninstallFromSchedulex(taskInfo.getTenantId(), operateEmpid, taskId)) {
            	isSuccess = true;
            }
        } else {
            if (uninstallTaskFromSchedulerx(taskId)) {
            	isSuccess = true;
            }
        }
        //更新DAG
        if (isSuccess) {
	        //创建任务执行DAG
	        createDagJob(taskId, operateEmpid, dag);
        }
        return true;
    }

    @Override
    public String evalDagScript(String accessId, String accessKey, String dagScript) {
        try {
            return groovyService.evalDag(dagScript);
        } catch (QanatBizException e) {
            log.error("restartAllPgSinkTasks failed, error={}", e.getMessage());
        } catch (Exception e) {
            log.error("restartAllPgSinkTasks failed, error={}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public DataResult<Boolean> stopAndDeleteScheduleTask(String tenantId, String empid, Long taskId) {
        log.info("start stopAndDeleteScheduleTask({},{},{})", tenantId, empid, taskId);
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        DataResult<Boolean> stopResult = stopTask(tenantId, empid, taskId);
        if (stopResult != null && stopResult.getData()) {
        	while (true) {
        		try {
    	            this.deleteTaskById(tenantId, empid, taskId);
    	            result.setData(true);
    	            break;
    	        } catch (QanatBizException e) {
    	        	if ("501".equals(e.getCode())) {
    	                log.info("{} 继续等待", e.getMessage());
    	                try {
							Thread.sleep(10000); // 等待10s
						} catch (InterruptedException e1) {
						}
    	        		continue;
    	        	} else {
    		            result.setData(false);
    		            result.setSuccess(false);
    		            result.setCode(e.getCode());
    		            result.setMessage(e.getMessage());
    		            log.error("deleteTask failed, error={}", e.getMessage());
    		            break;
    	        	}
    	        } catch (Exception e) {
    	            result.setData(false);
    	            result.setSuccess(false);
    	            result.setCode("500");
    	            result.setMessage(e.getMessage());
    	            log.error("deleteTask failed, error={}", e.getMessage(), e);
    	            break;
    	        }
        	}
        }
        return result;
    }
    
    private Boolean stopTaskAndUninstallFromSchedulex(String tenantId, String empid, Long taskId) {
        log.info("start stopTaskAndUninstallFromSchedulex({},{},{})", tenantId, empid, taskId);
        DataResult<Boolean> stopResult = stopTask(tenantId, empid, taskId);
        if (stopResult != null && stopResult.getData()) {
        	while (true) {
        		try {
    	            this.uninstallTaskFromSchedulerx(taskId);
    	            log.info("start stopTaskAndUninstallFromSchedulex({},{},{}) finished", tenantId, empid, taskId);
    	            return true;
    	        } catch (QanatBizException e) {
    	        	if ("501".equals(e.getCode())) {
    	                log.info("{} 继续等待", e.getMessage());
    	                try {
							Thread.sleep(10000); // 等待10s
						} catch (InterruptedException e1) {
						}
    	        		continue;
    	        	} else {
    		            log.error("stopTaskAndUninstallFromSchedulex({}) failed, error={}", taskId, e.getMessage());
    		            break;
    	        	}
    	        } catch (Exception e) {
    	            log.error("stopTaskAndUninstallFromSchedulex({}) failed, error={}", taskId, e.getMessage(), e);
    	            break;
    	        }
        	}
        }
        return false;
    }

    @Override
    public DataResult<Boolean> deleteScheduleTask(String tenantId, String empid, Long taskId) {
        log.info("start deleteTask({},{},{})", tenantId, empid, taskId);
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
            this.deleteTaskById(tenantId, empid, taskId);
            result.setData(true);
        } catch (QanatBizException e) {
            result.setData(false);
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("deleteTask failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setData(false);
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("deleteTask failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public DataResult<Long> createTaskScript(String tenantId, String script, String operateEmpid) {
        log.info("start createTaskScript({},{},{})", tenantId, script, operateEmpid);
        DataResult<Long> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	Date now = new Date();
        	TaskScript record = new TaskScript();
        	record.setTenantId(tenantId);
        	record.setScript(script);
        	record.setCreateEmpid(operateEmpid);
        	record.setModifyEmpid(operateEmpid);
        	record.setGmtCreate(now);
        	record.setGmtModified(now);
            taskScriptMapper.insertSelective(record);
            result.setData(record.getId());
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("createTaskScript failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("createTaskScript failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public DataResult<Boolean> updateTaskScript(String tenantId, String script, String operateEmpid, Long scriptId) {
        log.info("start updateTaskScript({},{},{},{})", tenantId, script, operateEmpid, scriptId);
        DataResult<Boolean> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        result.setData(false);
        try {
        	Date now = new Date();
        	TaskScript record = new TaskScript();
        	record.setId(scriptId);
        	record.setScript(script);
        	record.setModifyEmpid(operateEmpid);
        	record.setGmtModified(now);
            taskScriptMapper.updateByPrimaryKeyWithBLOBs(record);
            result.setData(true);
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("updateTaskScript failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("updateTaskScript failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    private void deleteTaskById(String tenantId, String empid, Long taskId) {
    	if (uninstallTaskFromSchedulerx(taskId)) {
	        TaskInfoWithBLOBs updTaskInfo = new TaskInfoWithBLOBs();
	        updTaskInfo.setId(taskId);
	        updTaskInfo.setIsDeleted(taskId);
	        updTaskInfo.setModifyEmpid(empid);
	        updTaskInfo.setGmtModified(new Date());
	        taskInfoMapper.updateByPrimaryKeySelective(updTaskInfo);
    	}
    }

    private boolean uninstallTaskFromSchedulerx(Long taskId) {
        TaskInstanceExample example = new TaskInstanceExample();
        example.createCriteria().andTaskIdEqualTo(taskId)
        .andStatusEqualTo(DagInstStatus.EXECUTING.getCode().byteValue());
        List<TaskInstance> subTaskInstList = taskInstanceMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(subTaskInstList)) {
            throw new QanatBizException("501", "存在执行中的任务，请先停止任务。");
        }
        TaskInfoExample taskInfoExample = new TaskInfoExample();
        taskInfoExample.createCriteria().andIdEqualTo(taskId).andIsDeletedEqualTo(0L);
        List<TaskInfoWithBLOBs> taskInfoList = taskInfoMapper.selectByExampleWithBLOBs(taskInfoExample);
        if (CollectionUtils.isNotEmpty(taskInfoList)) {
            TaskInfoWithBLOBs taskInfo = taskInfoList.get(0);
            JSONArray subTaskJsonArray = JSON.parseArray(taskInfo.getSubTasks());
            if (CollectionUtils.isNotEmpty(subTaskJsonArray)) {
	            for (int i = 0; i < subTaskJsonArray.size(); i++) {
	                JSONObject subTaskJson = subTaskJsonArray.getJSONObject(i);
	                Long jobId = subTaskJson.getLong("jobId");
	                schedulerXJobService.deleteJob(taskInfo.getTenantId(), taskInfo.getAppName(), jobId);
	            }
            }
            if (taskInfo.getExternalId() != null) {
            	Long mainJobId = Long.valueOf(taskInfo.getExternalId());
            	schedulerXJobService.deleteJob(taskInfo.getTenantId(), taskInfo.getAppName(), mainJobId);
            }
        }
        return true;
    }
}