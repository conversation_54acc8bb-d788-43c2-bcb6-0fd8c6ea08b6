package com.aliyun.wormhole.qanat.service.viewmodel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.BlinkBatchNode;
import com.aliyun.wormhole.qanat.api.dag.Dag;
import com.aliyun.wormhole.qanat.api.dag.DagPolicy;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.DatasourceRequest;
import com.aliyun.wormhole.qanat.api.dto.TaskInfoRequest;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.KafkaManagementService;
import com.aliyun.wormhole.qanat.api.service.TaskService;
import com.aliyun.wormhole.qanat.api.service.ViewModelRequest;
import com.aliyun.wormhole.qanat.dal.domain.AppInfo;
import com.aliyun.wormhole.qanat.dal.domain.AppInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelation;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTaskExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsRelation;
import com.aliyun.wormhole.qanat.dal.domain.DsRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.Extension;
import com.aliyun.wormhole.qanat.dal.domain.ExtensionExample;
import com.aliyun.wormhole.qanat.dal.domain.ResourceExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfo;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfoWithBLOBs;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfo;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelDsRelation;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelDsRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfo;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelTaskRelation;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelTaskRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelVersionExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelVersionWithBLOBs;
import com.aliyun.wormhole.qanat.dal.mapper.AppInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.AppResourceRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceDsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsFieldInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ExtensionMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ResourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TenantInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelDsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelTaskRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelVersionMapper;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.service.odps.OdpsClient;
import com.aliyun.wormhole.qanat.service.ods.OdsHandler;
import com.aliyun.wormhole.qanat.service.util.YamlUtil;
import com.aliyun.wormhole.qanat.service.viewmodel.ComponentObjectProcessor;
import com.aliyun.wormhole.qanat.service.viewmodel.FullLinkProcessor;
import com.aliyun.wormhole.qanat.service.viewmodel.FunctionObjectProcessor;
import com.aliyun.wormhole.qanat.service.viewmodel.LookupProcessor;
import com.aliyun.wormhole.qanat.service.viewmodel.MdpObjectProcessor;
import com.aliyun.wormhole.qanat.service.viewmodel.TableAggrProcessor;
import com.aliyun.wormhole.qanat.service.viewmodel.TableObjectProcessor;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelOptimizer;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelSqlBuilder;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.AddOn;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.DataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Field;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.RelatedDataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.Settings;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * ADB实时数仓同步服务
 * <AUTHOR>
 * 2019年7月23日
 */
@Slf4j
@Component
public class ViewModelHandler {
    
    @Resource
    protected TaskService taskService;
    
    @Resource
    protected DatasourceMapper datasourceMapper;

    @Resource
    protected QanatDatasourceHandler dsHandler;
    
    @Resource
    protected DsFieldInfoMapper dsFieldInfoMapper;
    
    @Resource
    protected KafkaManagementService kafkaManagementService;
    
    @Resource
    protected ViewModelOptimizer viewModelOptimizer;
    
    @Resource
    protected ViewModelInfoMapper viewModelInfoMapper;
    
    @Resource
    protected ViewModelVersionMapper viewModelVersionMapper;
    
    @Resource
    protected ViewModelTaskRelationMapper viewModelTaskRelationMapper;
    
    @Resource
    protected ViewModelDsRelationMapper viewModelDsRelationMapper;
    
    @Resource
    protected DatasourceService dsInfoService;
    
    @Resource
    protected AppInfoMapper appInfoMapper;
    
    @Resource
    protected DsRelationMapper dsRelationMapper;
    
    @Resource
    protected DbInfoMapper dbInfoMapper;
    
    @Resource
    protected TenantInfoMapper tenantInfoMapper;
    
    @Resource
    protected ExtensionMapper extensionMapper;
    
    @Resource
    private ComponentObjectProcessor componentObjectProcessor;
    
    @Resource
    protected ViewModelSqlBuilder viewModelSqlBuilder;
    
    @Resource
    private FunctionObjectProcessor functionObjectProcessor;
    
    @Resource
    private LookupProcessor lookupProcessor;
    
    @Resource
    private TableObjectProcessor tableObjectProcessor;
    
    @Resource
    private TableAggrProcessor tableAggrProcessor;
    
    @Resource
    protected MdpObjectProcessor mdpObjectProcessor;
    
    @Resource
    protected FullLinkProcessor fullLinkProcessor;
	
	@Resource
	private AppResourceRelationMapper appResourceRelationMapper;
	
	@Resource
	private ResourceMapper resourceMapper;
	
	@Resource
	protected BlinkService blinkService;
	
	@Resource
	protected OdsHandler odsHandler;
	
	@Resource
	protected DatatubeInstanceMapper datatubeInstanceMapper;
	
	@Resource
	protected DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;
	
	@Resource
	protected DatatubeInstanceDsRelationMapper datatubeInstanceDsRelationMapper;
	
	@Resource
	protected TaskInfoMapper taskInfoMapper;
	
	@Resource
	private TaskInstanceMapper taskInstanceMapper;
    
    @Transactional(propagation = Propagation.REQUIRED)
    public Long createViewModelFromObject(String tenantId, String appName, String objectType, String objectUniqueCode, String operateEmpid, String objectMsg) {
    	log.info("createViewModelFromObject({},{},{},{},{},{})", tenantId, appName, objectType, objectUniqueCode, operateEmpid, objectMsg);
        
    	if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(appName)
    			|| StringUtils.isBlank(objectType)
    			|| StringUtils.isBlank(objectUniqueCode)
    			|| StringUtils.isBlank(operateEmpid)
    			|| StringUtils.isBlank(objectMsg)) {
    		throw new QanatBizException("params valid failed, tenantId/appName/objectType/objectCode/operateEmpid/objectMsg are all neccesarry");
    	}
    	TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(tenantId);
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + tenantId + " is not configured");
    	}
    	//创建对象对应dsInfo
    	DatasourceExample example = new DatasourceExample();
    	example.createCriteria().andDsUniqueNameEqualTo(objectUniqueCode).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L);
    	List<Datasource> dsList = datasourceMapper.selectByExample(example);
    	if (CollectionUtils.isEmpty(dsList)) {
    		
        	DatasourceRequest dsInfoReq = new DatasourceRequest();
        	dsInfoReq.setTenantId(tenantId);
        	dsInfoReq.setAppName(appName);
        	dsInfoReq.setDbName(tenantList.get(0).getDefaultDw());
        	dsInfoReq.setDsName("obj_" + objectUniqueCode);
        	dsInfoReq.setDsType("obj");
        	dsInfoReq.setDsUniqueName(objectUniqueCode);
        	dsInfoReq.setTableName(objectUniqueCode);
        	dsInfoReq.setObjectType(objectType);
        	dsInfoReq.setDsDesc("create dsInfo for objectCode " + objectUniqueCode);
        	dsInfoReq.setOperateEmpid(operateEmpid);
        	dsInfoReq.setMeta(objectMsg);
        	DataResult<Long> createDsInfoResult = dsInfoService.createDsInfoFromObject(dsInfoReq);
        	log.info("createDsInfoResult={}", JSON.toJSONString(createDsInfoResult));
        	if (createDsInfoResult == null || !createDsInfoResult.getSuccess()) {
        		throw new QanatBizException("createDsInfoResult failed, error=" + (createDsInfoResult != null ? createDsInfoResult.getMessage() : "null"));
        	}
    	}
    	ViewModelInfoExample vmExample = new ViewModelInfoExample();
    	vmExample.createCriteria().andModelNameEqualTo("vm_dwd_" + objectUniqueCode.toLowerCase()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L);
    	List<ViewModelInfo> vmList = viewModelInfoMapper.selectByExample(vmExample);
    	
    	if (CollectionUtils.isEmpty(vmList)) {
        	//创建对象对应ViewModel
        	ViewModel vm = new ViewModel();
        	vm.setCode("dwd_" + objectUniqueCode.toLowerCase());
        	DataObject object = new DataObject();
        	vm.setObject(object);
        	vm.setDynamic(true);
        	object.setCode(objectType);
        	object.setType("metadata");
        	object.setRef(objectUniqueCode);
        	ViewModelRequest request = new ViewModelRequest();
        	request.setTenantId(tenantId);
        	request.setAppName(appName);
        	request.setObjectType(objectType);
        	request.setYaml(YamlUtil.getYaml(vm));
        	request.setOperateEmpid(operateEmpid);
        	request.setModelDesc("create viewModel for objectCode " + objectUniqueCode);
        	Long vmId = this.createViewModelFromYaml(request);
        	log.info("createVmResult={}", vmId);
        	
        	ViewModelInfo viewModelInfo = viewModelInfoMapper.selectByPrimaryKey(vmId);
        	
        	Date now = new Date();
        	DatatubeInstance diRecord = new DatatubeInstance();
        	diRecord.setAppName(appName);
        	diRecord.setCode(viewModelInfo.getModelName());
        	diRecord.setCreateEmpid(operateEmpid);
        	diRecord.setGmtCreate(now);
        	diRecord.setGmtModified(now);
        	diRecord.setIsDeleted(0L);
        	diRecord.setIsTest(0L);
        	diRecord.setLevel("minor");
        	diRecord.setModifyEmpid(operateEmpid);
        	diRecord.setName(viewModelInfo.getModelDesc());
        	diRecord.setObjectType(objectType);
        	diRecord.setProvider("viewmodel");
        	diRecord.setProviderId(vmId);
        	diRecord.setTenantId(tenantId);
        	diRecord.setType("object");
        	datatubeInstanceMapper.insert(diRecord);

        	if (vmId != null) {
            	if (this.createFinalTable(tenantId, vmId, objectType, tenantList.get(0).getDefaultDw())) {
//        			Integer directWrite = null;
//                    JSONObject metaJson = JSON.parseObject(objectMsg);
//        			if (metaJson != null &&  metaJson.getJSONObject("extParam") != null && metaJson.getJSONObject("extParam").getJSONObject("objectVO") != null) {
//        				directWrite = metaJson.getJSONObject("extParam").getJSONObject("objectVO").getInteger("directWrite");
//        			}
//        			if (directWrite != null && directWrite.intValue() == 1) {//防止被上线任务扫到，保存一个无意义的task
//        				viewModelInfo = viewModelInfoMapper.selectByPrimaryKey(vmId);
//        				ViewModelTaskRelation record = new ViewModelTaskRelation();
//        		        record.setCreateEmpid(operateEmpid);
//        		        record.setGmtCreate(new Date());
//        		        record.setGmtModified(new Date());
//        		        record.setIsDeleted(0L);
//        		        record.setModelVersionId(viewModelInfo.getVersionId());
//        		        record.setModifyEmpid(operateEmpid);
//        		        record.setTaskId(-1L);
//        		        record.setTenantId(tenantId);
//        		        record.setViewModelName(viewModelInfo.getModelName());
//        		        record.setRelationType("directWrite");
//        		        viewModelTaskRelationMapper.insert(record);
//        			} else {//非同步写入 则 生成异步写入任务
	            		new Thread(()->{
	    	        		Long taskId = this.createBatchStreamTasks(tenantId, vmId, request.getOperateEmpid());
	    	            	log.info("createTaskResult={}", taskId);
	    	            	try {
								Thread.sleep(3*1000);
							} catch (InterruptedException e) {
							}
	    	            	taskService.runTask(tenantId, operateEmpid, taskId);
	            		}).start();
//        			}
            	}
            	return vmId;
        	} else {
        		throw new QanatBizException("createVmResult failed, error=" + (vmId != null ? vmId : "null"));
        	}
    	}
        return null;
    }
    
    @Transactional(propagation = Propagation.REQUIRED)
    public Long createViewModelFromYaml(ViewModelRequest request) {
    	log.info("createViewModelFromYaml({})", JSON.toJSONString(request));
        
    	if (StringUtils.isBlank(request.getTenantId()) || StringUtils.isBlank(request.getYaml())
    			|| StringUtils.isBlank(request.getAppName())
    			|| StringUtils.isBlank(request.getOperateEmpid())
    			|| StringUtils.isBlank(request.getObjectType())) {
    		throw new QanatBizException("params valid failed");
    	}
    	TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(request.getTenantId());
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + request.getTenantId() + " is not configured");
    	}
    	TenantInfo tenantInfo = tenantList.get(0);
    	
    	ViewModel originModel = YamlUtil.getViewModel(request.getYaml());
    	ViewModelInfoExample example = new ViewModelInfoExample();
    	example.createCriteria().andTenantIdEqualTo(request.getTenantId()).andIsDeletedEqualTo(0L).andModelNameEqualTo("vm_" + originModel.getCode());
    	List<ViewModelInfo> vmInfos = viewModelInfoMapper.selectByExample(example);
    	if (CollectionUtils.isNotEmpty(vmInfos)) {
    		throw new QanatBizException("Duplicate viewModel Code:" + originModel.getCode());
    	}
    	Date now = new Date();
    	ViewModelInfo insRecord = new ViewModelInfo();
    	String modelName = "vm_" + originModel.getCode();
    	insRecord.setCreateEmpid(request.getOperateEmpid());
    	insRecord.setIsDeleted(0L);
    	insRecord.setGmtCreate(new Date());
    	insRecord.setGmtModified(new Date());
    	insRecord.setModifyEmpid(request.getOperateEmpid());
    	insRecord.setModelName(modelName);
    	insRecord.setModelDesc(request.getModelDesc());
    	insRecord.setObjectType(request.getObjectType());
    	insRecord.setTenantId(request.getTenantId());
    	insRecord.setAppName(request.getAppName());
    	insRecord.setDbName(tenantInfo.getDefaultDw());
    	viewModelInfoMapper.insert(insRecord);
    	
    	ViewModelVersionWithBLOBs version = new ViewModelVersionWithBLOBs();
    	version.setGmtCreate(now);
    	version.setGmtModified(now);
    	version.setTenantId(request.getTenantId());
    	version.setViewModelId(insRecord.getId());
    	version.setViewModelName(modelName);
    	version.setUserYaml(request.getYaml());
    	ViewModel viewModel = viewModelOptimizer.getOptimizedViewModel(request.getTenantId(), request.getYaml());
    	version.setSysYaml(YamlUtil.getYaml(viewModel));
    	viewModelVersionMapper.insert(version);
    	
    	ViewModelInfo updRecord = new ViewModelInfo();
    	updRecord.setId(insRecord.getId());
    	updRecord.setVersionId(version.getId());
    	if (StringUtils.isBlank(request.getModelDesc())) {
    		updRecord.setModelDesc(viewModel.getName());
    	}
    	viewModelInfoMapper.updateByPrimaryKeySelective(updRecord);

    	List<String> dbNames = this.getDstDbNames(tenantInfo);
    	refreshDsRelations(request.getTenantId(), request.getObjectType(), modelName, dbNames, tenantInfo.getDefaultDw(), request.getOperateEmpid(), now, viewModel, request.getAppName());
    	
        return insRecord.getId();
    }

	public void refreshDsRelations(String tenantId, String objectType, String modelName, List<String> dbNames, String mainDbName, String empid, Date now, ViewModel viewModel, String appName) {
		ViewModelDsRelationExample example = new ViewModelDsRelationExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andViewModelNameEqualTo(modelName);
		int delCnt = viewModelDsRelationMapper.deleteByExample(example);
		log.info("clear viewModelDsRelations, delCnt={}", delCnt);
		
		if (CollectionUtils.isNotEmpty(viewModel.getRelatedObjects())) {
			for (RelatedDataObject relObj : viewModel.getRelatedObjects()) {
				ViewModelDsRelation dsRel = new ViewModelDsRelation();
				dsRel.setCreateEmpid(empid);
				if ("metadata".equalsIgnoreCase(relObj.getType())) {
					JSONObject dsJson = dsInfoService.getTableMetaByDsUniqueName(tenantId, relObj.getRef());
					dsRel.setDsName(dsJson.getString("dsName"));
				} else {
					dsRel.setDsName(relObj.getRef());
				}
				dsRel.setGmtCreate(now);
				dsRel.setGmtModified(now);
				dsRel.setIsDeleted(0L);
				dsRel.setModifyEmpid(empid);
				dsRel.setRelationType("from_ds");
				dsRel.setTenantId(tenantId);
				dsRel.setViewModelName(modelName);
				viewModelDsRelationMapper.insert(dsRel);
			}
		}
		ViewModelDsRelation dsRel = new ViewModelDsRelation();
		dsRel.setCreateEmpid(empid);
		if ("metadata".equalsIgnoreCase(viewModel.getObject().getType())) {
			JSONObject dsJson = dsInfoService.getTableMetaByDsUniqueName(tenantId, viewModel.getObject().getRef());
			dsRel.setDsName(dsJson.getString("dsName"));
		} else {
			dsRel.setDsName(viewModel.getObject().getRef());
		}
		dsRel.setGmtCreate(now);
		dsRel.setGmtModified(now);
		dsRel.setIsDeleted(0L);
		dsRel.setModifyEmpid(empid);
		dsRel.setRelationType("from_ds");
		dsRel.setTenantId(tenantId);
		dsRel.setViewModelName(modelName);
		try {
			viewModelDsRelationMapper.insert(dsRel);
		} catch(Exception e) {}
		
		Datasource mainDsInfo = null;
		List<String> dstDsNames = new ArrayList<>();
		for (String dbName : dbNames) {
			JSONObject dbMetaJson = dsInfoService.getDbMetaByName(dbName);
			Datasource dsInfo = new Datasource();
			dsInfo.setCreateEmpid(empid);
			dsInfo.setDbName(dbName);
			dsInfo.setDsDesc("generate from view model");
			dsInfo.setDsName(dsInfoService.getDsName(tenantId, appName, dbName, viewModel.getCode()));
			dsInfo.setDsType(dbMetaJson.getString("dbType"));
			dsInfo.setGmtCreate(now);
			dsInfo.setGmtModified(now);
			dsInfo.setIsDeleted(0L);
			dsInfo.setTenantId(tenantId);
			dsInfo.setTableName(viewModel.getCode());
			dsInfo.setModifyEmpid(empid);
			dsInfo.setObjectType(objectType);
			dsInfo.setPkFields(viewModel.getObject().getFields().stream().filter(item -> item.isPk()).map(item -> item.getCode()).collect(Collectors.toList()).get(0));
			try {
				datasourceMapper.insert(dsInfo);
			} catch(Exception e) {}
			dstDsNames.add(dsInfo.getDsName());
			
			if (dbName.equalsIgnoreCase(mainDbName)) {
				mainDsInfo = dsInfo;
			}
			
			dsRel = new ViewModelDsRelation();
			dsRel.setCreateEmpid(empid);
			dsRel.setDsName(dsInfo.getDsName());
			dsRel.setGmtCreate(now);
			dsRel.setGmtModified(now);
			dsRel.setIsDeleted(0L);
			dsRel.setModifyEmpid(empid);
			dsRel.setRelationType("to_ds");
			dsRel.setTenantId(tenantId);
			dsRel.setViewModelName(modelName);
			try {
				viewModelDsRelationMapper.insert(dsRel);
			} catch(Exception e) {}
		}
		
		//DWD post-ods dsRelation 暂时先对xobject开放
		if ("metadata".equalsIgnoreCase(viewModel.getObject().getType())) {
			String dwdIncrTopic =  "stream-" + this.getAppIdByName(tenantId, appName) + "-" + mainDsInfo.getId();

	    	JSONObject appKafkaJson = kafkaManagementService.getKafkaConfByAppName(tenantId, appName);
			Datasource drcDs = new Datasource();
			drcDs.setDsName("kafka_" + dwdIncrTopic);
			drcDs.setDsType("kafka");
			drcDs.setTableName(dwdIncrTopic);
			drcDs.setIsDeleted(0L);
			drcDs.setDbName(appKafkaJson.getString("dbName"));
			drcDs.setGmtModified(now);
			drcDs.setModifyEmpid(empid);
			drcDs.setGmtCreate(now);
			drcDs.setCreateEmpid(empid);
			drcDs.setTenantId(tenantId);
			try {
				datasourceMapper.insert(drcDs);
			} catch(Exception e) {
				log.error("insert kafka dsInfo[{}] failed, error={}", drcDs.getDsName(), e.getMessage());
			}

			for (String dsName : dstDsNames) {
				DsRelationExample dsRelExp = new DsRelationExample();
				dsRelExp.createCriteria().andTenantIdEqualTo(tenantId)
										.andIsDeletedEqualTo(0L)
										.andRelationTypeEqualTo("post_ods")
										.andDstDsNameEqualTo(dsName);
				delCnt = dsRelationMapper.deleteByExample(dsRelExp);
				log.info("delete [{}] exists [{}] dsRelations for dstDsName[{}]", delCnt, "post_ods", dsName);
				
				DsRelation newDsRel = new DsRelation();
				newDsRel.setCreateEmpid(empid);
				newDsRel.setDstDsName(dsName);
				newDsRel.setGmtCreate(now);
				newDsRel.setGmtModified(now);
				newDsRel.setIsDeleted(0L);
				newDsRel.setModifyEmpid(empid);
				newDsRel.setRelationType("post_ods");
				newDsRel.setSrcDsName("kafka_" + dwdIncrTopic);
				newDsRel.setTenantId(tenantId);
				int insCnt = dsRelationMapper.insert(newDsRel);
				log.info("insert [{}] dsRelations[{},{},{}]", insCnt, "kafka_" + dwdIncrTopic, dsName, "post_ods");
			}
		}
	}

    @Transactional(propagation = Propagation.REQUIRED)
    public Long modifyViewModel(ViewModelRequest request) {
    	log.info("modifyViewModel({})", JSON.toJSONString(request));
    	if (StringUtils.isBlank(request.getTenantId()) || StringUtils.isBlank(request.getYaml()) || null == request.getModelId()) {
    		throw new QanatBizException("params valid failed");
    	}
    	
    	TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(request.getTenantId());
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + request.getTenantId() + " is not configured");
    	}
    	TenantInfo tenantInfo = tenantList.get(0);
    	
		String mainDbName = this.getMainDbName(tenantInfo);
    	
    	Date now = new Date();
		ViewModelInfo viewModelInfo = viewModelInfoMapper.selectByPrimaryKey(request.getModelId());
    	ViewModel originModel = YamlUtil.getViewModel(request.getYaml());
    	if (!("vm_" + originModel.getCode()).equalsIgnoreCase(viewModelInfo.getModelName())) {
    		throw new QanatBizException("viewModelId:" + request.getModelId() + " do not match model code:" + originModel.getCode());
    	}
    	
    	DatatubeInstanceExample diExample = new DatatubeInstanceExample();
        diExample.createCriteria().andTenantIdEqualTo(request.getTenantId()).andProviderEqualTo("viewmodel").andIsDeletedEqualTo(0L).andProviderIdEqualTo(request.getModelId());
        List<DatatubeInstance> dis = datatubeInstanceMapper.selectByExample(diExample);
        if (CollectionUtils.isEmpty(dis)) {
        	throw new QanatBizException("viewModelId:" + request.getModelId() + " is not related to any datatube instance");
        }
        DatatubeInstance datatubeInst =  dis.get(0);
    	
    	boolean isNewVersion = request.getNewVersion() == null ? true : request.getNewVersion();
    	Long versionId = viewModelInfo.getVersionId();
    	ViewModel viewModel = viewModelOptimizer.getOptimizedViewModel(request.getTenantId(), request.getYaml());
    	if (isNewVersion) {
	    	ViewModelVersionWithBLOBs version = new ViewModelVersionWithBLOBs();
	    	version.setGmtCreate(now);
	    	version.setGmtModified(now);
	    	version.setRemark(request.getRemark());
	    	version.setTenantId(request.getTenantId());
	    	version.setViewModelId(request.getModelId());
	    	version.setViewModelName(viewModelInfo.getModelName());
	    	version.setUserYaml(request.getYaml());
	    	version.setSysYaml(YamlUtil.getYaml(viewModel));
	    	viewModelVersionMapper.insert(version);
	    	
	    	ViewModelInfo updRecord = new ViewModelInfo();
	    	updRecord.setId(request.getModelId());
	    	updRecord.setVersionId(version.getId());
	    	updRecord.setGmtModified(now);
	    	updRecord.setModifyEmpid(request.getOperateEmpid());
	    	viewModelInfoMapper.updateByPrimaryKeySelective(updRecord);
	    	
	    	versionId = version.getId();
    	} else {
	    	ViewModelVersionWithBLOBs version = new ViewModelVersionWithBLOBs();
	    	version.setGmtModified(now);
	    	version.setRemark(request.getRemark());
	    	version.setUserYaml(request.getYaml());
	    	version.setSysYaml(YamlUtil.getYaml(viewModel));
	    	version.setId(versionId);
	    	viewModelVersionMapper.updateByPrimaryKeySelective(version);
	    	
	    	if (request.getRebuildNow() != null && request.getRebuildNow()) {
		    	//重新生成同步任务
				log.info("create syncTasks fro vm[{}] with version[{}]", viewModelInfo.getModelName(), viewModelInfo.getVersionId());
		    	Long createTaskResult = createBatchStreamTasks(viewModelInfo.getTenantId(), request.getModelId(), request.getOperateEmpid());
		    	log.info("rebuild sync jobs for viewModel:{} with new version:{} finished, result:{}", request.getModelId(), viewModelInfo.getVersionId(), createTaskResult);
		    	
		    	//wait for blink job ready
		    	try {
		    		Thread.sleep(1000 * 60);
		    	} catch(Exception e) {}
		    	
				log.info("restart vm[{}] dataSync tasks", viewModelInfo.getModelName());
	    		Long restartResult = restartModelTask(viewModelInfo.getTenantId(), request.getModelId(), request.getOperateEmpid());
	    		log.info("vm[{}] restartResult={}", viewModelInfo.getModelName(), restartResult);
	    	}
    	}
    	List<String> dbNames = this.getDstDbNames(tenantInfo, datatubeInst);
    	refreshDsRelations(viewModelInfo.getTenantId(), viewModelInfo.getObjectType(), viewModelInfo.getModelName(), dbNames, mainDbName, request.getOperateEmpid(), now, viewModel, viewModelInfo.getAppName());
    	return versionId;
    }
    
    @Transactional(propagation = Propagation.REQUIRED)
    public Long createBatchStreamTasks(String tenantId, Long viewModelId, String operateEmpid) {
        log.info("start createBatchStreamTasks({},{},{})", tenantId, viewModelId, operateEmpid);

    	TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(tenantId);
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + tenantId + " is not configured");
    	}
    	TenantInfo tenantInfo = tenantList.get(0);
        
        String datatubeLevel = null;
        DatatubeInstanceExample diExample = new DatatubeInstanceExample();
        diExample.createCriteria().andTenantIdEqualTo(tenantId).andProviderEqualTo("viewmodel").andIsDeletedEqualTo(0L).andProviderIdEqualTo(viewModelId);
        List<DatatubeInstance> dis = datatubeInstanceMapper.selectByExample(diExample);
        if (CollectionUtils.isEmpty(dis)) {
        	throw new QanatBizException("viewModelId:" + viewModelId + " is not related to any datatube instance");
        }
        DatatubeInstance datatubeInst =  dis.get(0);
        
    	datatubeLevel = datatubeInst.getLevel();
    	
    	List<String> dbNames = getDstDbNames(tenantInfo, datatubeInst);
    	
		String mainDbName = this.getMainDbName(tenantInfo);
    	
    	String etlDbName = this.getEtlDbName(tenantInfo);
    
    	List<String> extDbNames = getExtDbNames(tenantInfo, datatubeInst);
    	
    	DatatubeInstanceTaskExample ditExample = new DatatubeInstanceTaskExample();
    	ditExample.createCriteria().andTenantIdEqualTo(tenantId).andDatatubeInstIdEqualTo(datatubeInst.getId()).andIsDeletedEqualTo(0L);
    	DatatubeInstanceTask updDatatubeInstanceTask = new DatatubeInstanceTask();
    	updDatatubeInstanceTask.setIsDeleted(1L);
    	updDatatubeInstanceTask.setModifyEmpid(operateEmpid);
    	updDatatubeInstanceTask.setGmtModified(new Date());
    	datatubeInstanceTaskMapper.updateByExampleSelective(updDatatubeInstanceTask, ditExample);
        
    	ViewModelInfoExample example = new ViewModelInfoExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andIdEqualTo(viewModelId).andIsDeletedEqualTo(0L);
		List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(viewModelInfos)) {
			throw new QanatBizException("tenant check failed");
		}
		ViewModelInfo viewModelInfo = viewModelInfos.get(0);
    	
    	DbInfoExample dbInfoExample = new DbInfoExample();
    	dbInfoExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDbNameEqualTo(mainDbName);
    	List<DbInfo> dbInfos = dbInfoMapper.selectByExample(dbInfoExample);
    	if (CollectionUtils.isEmpty(dbInfos)) {
    		throw new QanatBizException("DbInfo not found:" + mainDbName);
    	}
		
        ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());

        ViewModel originModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
    	ViewModel sysModel = null;
    	if (originModel.isDynamic()) {
    		sysModel = viewModelOptimizer.getOptimizedViewModel(tenantId, modelVersion.getUserYaml());
    	} else {
    		sysModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
    	}
    	ViewModel dataModel = new ViewModel();
    	BeanUtils.copyProperties(sysModel, dataModel);
        String tableName = dataModel.getCode();
        List<String> batchJobs = new ArrayList<>();
        List<String> streamJobs = new ArrayList<>();
        Map<String, Field> correctTopics = new HashMap<>();
        
    	JSONObject appKafkaJson = kafkaManagementService.getKafkaConfByAppName(tenantId, viewModelInfo.getAppName());

        DataObject mainObject = dataModel.getObject();
        Field pkField = mainObject.getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0);
        String pkFieldName = pkField.getCode();
        boolean isMetricTableUpsert = false;
        Long appId = getAppIdByName(tenantId, viewModelInfo.getAppName());
		Long dstDsId = dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName);
    	String logTopicName = "stream-" + appId + "-" + dstDsId + "-" + mainObject.getCode();
		if ("metadata".equalsIgnoreCase(mainObject.getType())) {
        	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsUniqueName(tenantId, mainObject.getRef());
	    	JSONObject drcTopicInfo = srcDsMetaJson.getJSONObject("incrConf");
	    	String drcTopicName = drcTopicInfo.getString("topicName");
	    	String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_v" + viewModelInfo.getVersionId();
        	if (mdpObjectProcessor.processIncrSyncJob(tenantId, viewModelInfo.getAppName(), jobName, srcDsMetaJson, dbNames, mainDbName, tableName, mainObject, operateEmpid, CollectionUtils.isEmpty(originModel.getObject().getFields()) || dataModel.isDynamic(), viewModelInfo.getVersionId(), appKafkaJson, drcTopicName, logTopicName, dataModel, datatubeLevel, datatubeInst.getId())) {
        		streamJobs.add(jobName);
        	}
        	String drcTopicNameBatch = srcDsMetaJson.getJSONObject("incrConf").getString("topicNameBatch");
	    	String logTopicNameBatch = "stream-" + appId + "-" + dstDsId + "-" + mainObject.getCode() + "-batch";
	    	String jobNameBatch = "incrsync_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_batch_v" + viewModelInfo.getVersionId();
        	if (StringUtils.isNotBlank(drcTopicNameBatch) && mdpObjectProcessor.processIncrSyncJob(tenantId, viewModelInfo.getAppName(), jobNameBatch, srcDsMetaJson, dbNames, mainDbName, tableName, mainObject, operateEmpid, CollectionUtils.isEmpty(originModel.getObject().getFields()) || dataModel.isDynamic(), viewModelInfo.getVersionId(), appKafkaJson, drcTopicNameBatch, logTopicNameBatch, dataModel, datatubeLevel, datatubeInst.getId())) {
        		streamJobs.add(jobNameBatch);
        	}

        	String checkJobName = "incrcheck_" + appId + "_" + dstDsId+ "_" + mainObject.getCode() + "_v" + viewModelInfo.getVersionId();
        	if ("super".equalsIgnoreCase(datatubeLevel) && mdpObjectProcessor.processIncrCheckJob(tenantId, viewModelInfo.getAppName(), checkJobName, srcDsMetaJson, mainDbName, tableName, mainObject, operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, drcTopicName, dataModel, datatubeInst.getId())) {
            	streamJobs.add(checkJobName);
            	correctTopics.put("crt-" + appId + "-" + dstDsId + "-" + mainObject.getCode(), pkField);
			}
        	isMetricTableUpsert = true;
        } else if ("table".equalsIgnoreCase(mainObject.getType())) {
        	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, mainObject.getRef());
        	if ("metric".equals(srcDsMetaJson.getString("sysType"))) {
        		isMetricTableUpsert = true;
        		tableObjectProcessor.processBatchSyncJob(tenantId, viewModelInfo.getAppName(), dbNames, etlDbName, tableName, mainObject, operateEmpid, viewModelInfo.getVersionId(), viewModelInfo.getModelName(), appKafkaJson, isMetricTableUpsert, dataModel.getSettings(), datatubeInst.getId());
        	} else {
            	JSONObject drcTopicInfo = srcDsMetaJson.getJSONObject("incrConf");
        		String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_v" + viewModelInfo.getVersionId();
        		if (tableObjectProcessor.processMainObjectIncrSyncJob(tenantId, viewModelInfo.getAppName(), jobName, srcDsMetaJson, dbNames, etlDbName, tableName, mainObject, operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, drcTopicInfo, datatubeLevel, datatubeInst.getId(), dataModel)) {
            		streamJobs.add(jobName);
            	}
            	jobName = "incrcheck_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_v" + viewModelInfo.getVersionId();
            	if ("super".equalsIgnoreCase(datatubeLevel) && tableObjectProcessor.processIncrCheckJob(tenantId, viewModelInfo.getAppName(), jobName, srcDsMetaJson, mainDbName, tableName, mainObject, operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, drcTopicInfo, dataModel, datatubeInst.getId())) {
            		streamJobs.add(jobName);
                	correctTopics.put("crt-" + appId + "-" + dstDsId + "-" + mainObject.getCode(), pkField);
            	}
        	}
        }

		Map<ViewModel.Field, Map<String, List<ViewModel.Field>>> fieldObjFieldMap = new HashMap<>();
		Map<ViewModel.Field, List<ViewModel.Field>> fieldFuncFieldsMap = new HashMap<>();
		Map<ViewModel.Field, String> fieldFuncCodeMap = new HashMap<>();
    	for (ViewModel.Field field : mainObject.getFields()) {
    		if (field.getObject() != null) {
    			if ("metadata".equalsIgnoreCase(field.getObject().getType())) {
    				String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + field.getObject().getCode() + "_v" + viewModelInfo.getVersionId();
                	if (mdpObjectProcessor.processAggrIncrSyncJob(tenantId, viewModelInfo.getAppName(), jobName, mainDbName, tableName, field.getCode(), field.getObject(), operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, datatubeInst.getId())) {
                    	streamJobs.add(jobName);
        			}
                } else if ("table".equalsIgnoreCase(field.getObject().getType())) {
                	String jobName = tableAggrProcessor.processIncrSyncJob(tenantId, viewModelInfo.getAppName(), dbNames, etlDbName, tableName, field.getCode(), field.getObject(), operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, datatubeInst.getId());
                	streamJobs.add(jobName);
                	jobName = "incrcheck_" + appId + "_" + dstDsId + "_" + field.getObject().getCode() + "_v" + viewModelInfo.getVersionId();
                	if("super".equalsIgnoreCase(datatubeLevel) && tableAggrProcessor.processIncrCheckJob(tenantId, viewModelInfo.getAppName(), jobName, mainDbName, tableName, field.getCode(), field.getObject(), operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, dataModel.getSettings(), datatubeInst.getId())) {
                    	streamJobs.add(jobName);
                    	Field fkField = getFkFieldByObject(dataModel, field.getObject());
                    	correctTopics.put("crt-" + appId + "-" + dstDsId + "-" + field.getObject().getCode(), fkField);
                	}
                } else if ("component".equalsIgnoreCase(field.getObject().getType())) {
                	JSONObject streamTopicInfo = null;
                	ExtensionExample extExample = new ExtensionExample();
                	extExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-stream").andPluginEqualTo(field.getObject().getRef());
        			List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(extExample);
        			if (CollectionUtils.isNotEmpty(exts)) {
        				streamTopicInfo = JSON.parseObject(exts.get(0).getScript());
        				
        				DatasourceExample dsExample = new DatasourceExample();
        				dsExample.createCriteria().andTableNameEqualTo(streamTopicInfo.getString("topicName")).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
        				List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(dsExample);
        				if (CollectionUtils.isEmpty(dsList)) {
        					throw new QanatBizException(streamTopicInfo.getString("topicName") + " is not found in dsInfo");
        				}
        				streamTopicInfo.put("type", dsList.get(0).getDsType());
        				streamTopicInfo.put("dbName", dsList.get(0).getDbName());
        			}
                	List<JSONObject> idSourceTopics = new ArrayList<>();
                	if (streamTopicInfo == null) {
                		idSourceTopics = componentObjectProcessor.getTopicsByComponentObject(tenantId, field.getObject(), "id_in", dbInfos.get(0).getDbType());
                	}
                	List<JSONObject> drcSourceTopics = new ArrayList<>();
                	if (streamTopicInfo == null && CollectionUtils.isEmpty(idSourceTopics)) {
                		drcSourceTopics = componentObjectProcessor.getTopicsByComponentObject(tenantId, field.getObject(), "drc_in", dbInfos.get(0).getDbType());
                	}
                	String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + field.getCode() + "_v" + viewModelInfo.getVersionId();
                	if (componentObjectProcessor.processIncrSyncJob(tenantId, viewModelInfo.getAppName(), jobName, dbNames, etlDbName, tableName, field.getCode(), field.getObject(), operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, streamTopicInfo, idSourceTopics, drcSourceTopics, pkFieldName, dataModel, datatubeInst.getId())) {
                    	streamJobs.add(jobName);
                    }
                	jobName = "incrcheck_" + appId + "_" + dstDsId + "_" + field.getObject().getCode() + "_v" + viewModelInfo.getVersionId();
                	if ("super".equalsIgnoreCase(datatubeLevel) && componentObjectProcessor.processIncrCheckJob(tenantId, viewModelInfo.getAppName(), jobName, mainDbName, tableName, field.getCode(), field.getObject(), operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, streamTopicInfo, idSourceTopics, drcSourceTopics, dataModel.getSettings(), dataModel, datatubeInst.getId(), etlDbName)) {
                		streamJobs.add(jobName);
                    	Field fkField = getFkFieldByObject(dataModel, field.getObject());
                    	correctTopics.put("crt-" + appId + "-" + dstDsId + "-" + field.getObject().getCode(), fkField);
                	}
                }
    		}
    		if (field.isFunc()) {
    			String funcExpress = field.getRef();
    			String funcCode = lookupProcessor.parseFuncExpress(funcExpress).get(0);
    			String[] cols = lookupProcessor.parseFuncExpress(funcExpress).get(1).split(",");
    			Map<String, List<ViewModel.Field>> objFieldMap = new HashMap<>();
    			List<ViewModel.Field> funcFields  = new ArrayList<>();
    			for (String col : cols) {
    				if (col.startsWith("'") && col.endsWith("'")) {
    					ViewModel.Field funcField = new ViewModel.Field();
    					funcField.setRef(col);
    					funcFields.add(funcField);
    					continue;
    				}
    				String[] tokens = col.split("\\.");
    				if (tokens.length == 2) {
    					DataObject obj = null;
    					if (tokens[0].equalsIgnoreCase(mainObject.getCode())) {
    						obj = mainObject;
    					} else {
    						obj = dataModel.getRelatedObjects().stream().filter(e->e.getCode().equalsIgnoreCase(tokens[0])).collect(Collectors.toList()).get(0);
    					}
    					ViewModel.Field funcField = obj.getFields().stream().filter(e->e.getCode().equalsIgnoreCase(tokens[1])).collect(Collectors.toList()).get(0);
    					funcFields.add(funcField);
    					if (objFieldMap.get(obj.getCode()) == null) {
    						List<ViewModel.Field> list = new ArrayList<>();
    						list.add(funcField);
    						objFieldMap.put(obj.getCode(), list);
    					} else {
    						objFieldMap.get(obj.getCode()).add(funcField);
    					}
    				} else {
    					ViewModel.Field funcField = mainObject.getFields().stream().filter(e->e.getCode().equalsIgnoreCase(tokens[0])).collect(Collectors.toList()).get(0);
    					funcFields.add(funcField);
    					if (objFieldMap.get(mainObject.getCode()) == null) {
    						List<ViewModel.Field> list = new ArrayList<>();
    						list.add(funcField);
    						objFieldMap.put(mainObject.getCode(), list);
    					} else {
    						objFieldMap.get(mainObject.getCode()).add(funcField);
    					}
    				}
    			}
    			
    			String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + field.getCode() + "_v" + viewModelInfo.getVersionId();
            	if (functionObjectProcessor.processIncrSyncJob(tenantId, appId, viewModelInfo.getAppName(), jobName, dataModel, dbNames, mainDbName, tableName, dstDsId, field, objFieldMap,  operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, pkFieldName, funcFields, funcCode, datatubeInst.getId())) {
                	streamJobs.add(jobName);
                }
            	fieldObjFieldMap.put(field, objFieldMap);
            	fieldFuncFieldsMap.put(field, funcFields);
            	fieldFuncCodeMap.put(field, funcCode);
            	
    		}
    	}
    	if (CollectionUtils.isNotEmpty(fieldObjFieldMap.keySet())) {
        	String jobName = "fullsync_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_func_v" + viewModelInfo.getVersionId();
        	String tmpTableName = getTmpTableName(tableName, viewModelInfo.getVersionId());
        	if (functionObjectProcessor.processBatchSyncJob(tenantId, appId, viewModelInfo.getAppName(), jobName, dataModel, etlDbName, tmpTableName, tableName, dstDsId, fieldObjFieldMap.keySet(), fieldObjFieldMap,  operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, pkFieldName, fieldFuncFieldsMap, fieldFuncCodeMap, datatubeInst.getId())) {
            	batchJobs.add(jobName);
            }
    	}
        
        if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
        	for (ViewModel.RelatedDataObject relatedObject : dataModel.getRelatedObjects()) {
            	Field fkField = getFkFieldByObject(dataModel, relatedObject);
    			if ("metadata".equalsIgnoreCase(relatedObject.getType())) {
    				ViewModel.RelatedDataObject originRelatedObject = null;
    				for (ViewModel.RelatedDataObject obj : originModel.getRelatedObjects()) {
    					if (obj.getRef().equalsIgnoreCase(relatedObject.getRef())) {
    						originRelatedObject = obj;
    						break;
    					}
    				}
                	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsUniqueName(tenantId, mainObject.getRef());
        	    	JSONObject drcTopicInfo = srcDsMetaJson.getJSONObject("incrConf");
        	    	String drcTopicName = drcTopicInfo.getString("topicName");
        	    	String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + relatedObject.getCode() + "_v" + viewModelInfo.getVersionId();
        	    	logTopicName = "stream-" + appId + "-" + dstDsId + "-" + relatedObject.getCode();
    				if (mdpObjectProcessor.processIncrSyncJob(tenantId, viewModelInfo.getAppName(), jobName, srcDsMetaJson, dbNames, mainDbName, tableName, relatedObject, operateEmpid, CollectionUtils.isEmpty(originRelatedObject.getFields()) || dataModel.isDynamic(), viewModelInfo.getVersionId(), appKafkaJson, drcTopicName, logTopicName, dataModel, datatubeLevel, datatubeInst.getId())) {
                		streamJobs.add(jobName);
    				}
                	String checkJobName = "incrcheck_" + appId + "_" + dstDsId+ "_" + relatedObject.getCode() + "_v" + viewModelInfo.getVersionId();
                	if ("super".equalsIgnoreCase(datatubeLevel) && mdpObjectProcessor.processIncrCheckJob(tenantId, viewModelInfo.getAppName(), checkJobName, srcDsMetaJson, mainDbName, tableName, relatedObject, operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, drcTopicName, dataModel, datatubeInst.getId())) {
                    	streamJobs.add(checkJobName);
                    	correctTopics.put("crt-" +  appId + "-" + dstDsId + "-" + relatedObject.getCode(), fkField);
    				}
                } else if ("table".equalsIgnoreCase(relatedObject.getType())) {
                	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, relatedObject.getRef());
                	if ("metric".equals(srcDsMetaJson.getString("sysType"))) {
                		tableObjectProcessor.processBatchSyncJob(tenantId, viewModelInfo.getAppName(), dbNames, etlDbName, tableName, relatedObject, operateEmpid, viewModelInfo.getVersionId(), viewModelInfo.getModelName(), appKafkaJson, isMetricTableUpsert, dataModel.getSettings(), datatubeInst.getId());
                	} else {
                    	String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + relatedObject.getCode() + "_v" + viewModelInfo.getVersionId();
                		if (tableObjectProcessor.processRelatedObjectIncrSyncJob(tenantId, viewModelInfo.getAppName(), jobName, dbNames, etlDbName, tableName, relatedObject, operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, datatubeLevel, datatubeInst.getId(), pkFieldName, dataModel)) {
                    		streamJobs.add(jobName);
                    	}
                    	jobName = "incrcheck_" + appId + "_" + dstDsId + "_" + relatedObject.getCode() + "_v" + viewModelInfo.getVersionId();
                    	JSONObject drcTopicInfo = srcDsMetaJson.getJSONObject("incrConf");
                    	if ("super".equalsIgnoreCase(datatubeLevel) && tableObjectProcessor.processIncrCheckJob(tenantId, viewModelInfo.getAppName(), jobName, srcDsMetaJson, mainDbName, tableName, relatedObject, operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, drcTopicInfo, dataModel, datatubeInst.getId())) {
                    		streamJobs.add(jobName);
                        	correctTopics.put("crt-" + appId + "-" + dstDsId + "-" + relatedObject.getCode(), fkField);
                    	}
                	}
                } else if ("component".equalsIgnoreCase(relatedObject.getType())) {
					List<JSONObject> idSourceTopics = componentObjectProcessor.getTopicsByComponentObject(tenantId, relatedObject, "id_in", dbInfos.get(0).getDbType());
					if (CollectionUtils.isNotEmpty(idSourceTopics)) {
						String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + relatedObject.getCode() + "_v" + viewModelInfo.getVersionId();
						if (componentObjectProcessor.processIncrSyncJobForIdTopic(tenantId, viewModelInfo.getAppName(), jobName, dbNames, etlDbName, tableName, relatedObject, operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, idSourceTopics, dataModel, datatubeInst.getId())) {
							streamJobs.add(jobName);
						}
					}
					List<JSONObject> drcSourceTopics = componentObjectProcessor.getTopicsByComponentObject(tenantId, relatedObject, "drc_in", dbInfos.get(0).getDbType());
                	if (CollectionUtils.isNotEmpty(drcSourceTopics)) {
	                	String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + relatedObject.getCode() + "_v" + viewModelInfo.getVersionId();
	                	if (componentObjectProcessor.processIncrSyncJob(tenantId, viewModelInfo.getAppName(), jobName, dbNames, etlDbName, tableName, relatedObject, operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, drcSourceTopics, dataModel, datatubeInst.getId())) {
	                    	streamJobs.add(jobName);
	                    }
	                	jobName = "incrcheck_" + appId + "_" + dstDsId + "_" + relatedObject.getCode() + "_v" + viewModelInfo.getVersionId();
	                	if ("super".equalsIgnoreCase(datatubeLevel) && componentObjectProcessor.processIncrCheckJob(tenantId, viewModelInfo.getAppName(), jobName, mainDbName, tableName, relatedObject, operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, drcSourceTopics, dataModel.getSettings(), dataModel, datatubeInst.getId(), etlDbName)) {
	                		streamJobs.add(jobName);
	                    	Field fkField1 = getFkFieldByObject(dataModel, relatedObject);
	                    	correctTopics.put("crt-" + appId + "-" + dstDsId + "-" + relatedObject.getCode(), fkField1);
	                	}
                	}
                }
        	}
        }
        boolean lookup = false;
        List<String> objCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
            for (RelatedDataObject relObj : dataModel.getRelatedObjects()) {
            	if (relObj.getRelations().stream().filter(e->
            													!e.getRelatedField().startsWith("exp#")
            													&&(!e.getRelatedField().split("\\.")[0].equalsIgnoreCase(dataModel.getObject().getCode())
            															||!e.getRelatedField().split("\\.")[1].equalsIgnoreCase(pkFieldName))).count() > 0) {
            		lookup = true;
            		break;
            	} else if (!"none".equalsIgnoreCase(relObj.getLookupFrom())) {
            		objCodeList.add(relObj.getCode());
            	}
            }
        }
        if (!lookup) {
            for (ViewModel.Field field : dataModel.getObject().getFields()) {
            	if (field.getObject() != null) {
            		if (CollectionUtils.isNotEmpty(field.getObject().getRelations())) {
                    	if (field.getObject().getRelations().stream().filter(e->
																				!e.getRelatedField().startsWith("exp#")
																				&&(!e.getRelatedField().split("\\.")[0].equalsIgnoreCase(dataModel.getObject().getCode())
																						||!e.getRelatedField().split("\\.")[1].equalsIgnoreCase(pkFieldName))).count() > 0) {
							lookup = true;
							break;
						}
            		}
            	} else if (field.isFunc() || field.getRef().startsWith("exp#")) {
            		lookup = true;
					break;
            	} else if (field.getObject() != null && !"none".equalsIgnoreCase(field.getObject().getLookupFrom())) {
            		objCodeList.add(field.getObject().getCode());
            	}
            }
        }
        if (dataModel.getSettings().isLookupOptimize() && CollectionUtils.isNotEmpty(objCodeList)) {
        	String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_lookup_v" + viewModelInfo.getVersionId();
        	if (lookupProcessor.processIncrSyncJobOptimize(tenantId, appId, viewModelInfo.getAppName(), jobName, dataModel, dbNames, etlDbName, tableName, dstDsId, operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, pkFieldName, objCodeList, datatubeInst.getId())) {
            	streamJobs.add(jobName);
            }
        } else if (lookup) {
        	String jobName = "incrsync_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_lookup_v" + viewModelInfo.getVersionId();
        	if (dataModel.getSettings().isLookupOptimize()) {
            	if (lookupProcessor.processIncrSyncJobOptimize(tenantId, appId, viewModelInfo.getAppName(), jobName, dataModel, dbNames, etlDbName, tableName, dstDsId, operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, pkFieldName, null, datatubeInst.getId())) {
                	streamJobs.add(jobName);
                }
        	} else {
            	if (lookupProcessor.processIncrSyncJob(tenantId, appId, viewModelInfo.getAppName(), jobName, dataModel, mainDbName, tableName, dstDsId, operateEmpid, viewModelInfo.getVersionId(), appKafkaJson, pkFieldName, datatubeInst.getId())) {
                	streamJobs.add(jobName);
                }
        	}
        }
        //目标表级别的订正任务
        if (CollectionUtils.isNotEmpty(correctTopics.keySet()) && !"offhand".equalsIgnoreCase(dataModel.getSettings().getCorrectPolicy())) {
        	String correctJobName = mdpObjectProcessor.processCorrectJob(tenantId, viewModelInfo.getAppName(), mainDbName, tableName, operateEmpid, viewModelInfo.getVersionId(), dataModel, correctTopics, appKafkaJson, datatubeInst.getId());
        	streamJobs.add(correctJobName);
        }
        
        //附加任务
        if (CollectionUtils.isNotEmpty(dataModel.getAddOns())) {
        	for (AddOn addOn : dataModel.getAddOns()) {
        		if ("blink_stream".equalsIgnoreCase(addOn.getType())) {
        			streamJobs.add(addOn.getRef());
        		} else if ("blink_batch".equalsIgnoreCase(addOn.getType())) {
        			batchJobs.add(addOn.getRef());
        		}
        	}
        }
        
        //新增数据同步DAG任务
        Long taskId = processViewModelDAGTask(tenantId, viewModelInfo.getAppName(), operateEmpid, modelVersion.getViewModelId(), etlDbName, tableName, streamJobs, viewModelInfo.getVersionId(), batchJobs, dataModel.getSettings(), extDbNames, dataModel, datatubeInst);
        if (taskId == null) {
            throw new QanatBizException("任务创建失败");
        }

        try {
	        ViewModelTaskRelation record = new ViewModelTaskRelation();
	        record.setCreateEmpid(operateEmpid);
	        record.setGmtCreate(new Date());
	        record.setGmtModified(new Date());
	        record.setIsDeleted(0L);
	        record.setModelVersionId(viewModelInfo.getVersionId());
	        record.setModifyEmpid(operateEmpid);
	        record.setTaskId(taskId);
	        record.setTenantId(tenantId);
	        record.setViewModelName(viewModelInfo.getModelName());
	        record.setRelationType("main");
	        viewModelTaskRelationMapper.insert(record);
        } catch(Exception e) {}
        
        if (!"minor".equalsIgnoreCase(datatubeLevel)) {
            try {
	        	this.createBatchCheckTask(tenantId, viewModelId, operateEmpid);
	            this.createFullLinkTasks(datatubeInst.getId());
            } catch (Exception e) {
            	log.error("vm:{} create batchcheck or fulllink tasks failed:{}", viewModelId, e.getMessage(), e);
            }
        }
        
        return taskId;
    }
    
    public Map<String, String> getViewModelGids(DatatubeInstance datatubeInst) {
    	TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(datatubeInst.getTenantId());
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + datatubeInst.getTenantId() + " is not configured");
    	}
    	TenantInfo tenantInfo = tenantList.get(0);
    	
    	String etlDbName = this.getEtlDbName(tenantInfo);
        
    	ViewModelInfoExample example = new ViewModelInfoExample();
		example.createCriteria().andTenantIdEqualTo(datatubeInst.getTenantId()).andIdEqualTo(datatubeInst.getProviderId()).andIsDeletedEqualTo(0L);
		List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(viewModelInfos)) {
			throw new QanatBizException("tenant check failed");
		}
		ViewModelInfo viewModelInfo = viewModelInfos.get(0);
		
        ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());

        ViewModel originModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
    	ViewModel sysModel = null;
    	if (originModel.isDynamic()) {
    		sysModel = viewModelOptimizer.getOptimizedViewModel(datatubeInst.getTenantId(), modelVersion.getUserYaml());
    	} else {
    		sysModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
    	}
    	ViewModel dataModel = new ViewModel();
    	BeanUtils.copyProperties(sysModel, dataModel);
        String tableName = dataModel.getCode();

        DataObject mainObject = dataModel.getObject();
        Field pkField = mainObject.getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0);
        String pkFieldName = pkField.getCode();
        Long appId = getAppIdByName(datatubeInst.getTenantId(), viewModelInfo.getAppName());
		Long dstDsId = dsInfoService.getDsIdByTableName(datatubeInst.getTenantId(), tableName, etlDbName);

    	Map<String, String> gids = new HashMap<>(); 
    	
    	gids.put(mainObject.getCode(), "GID-" + appId + "-" + dstDsId + "-incr_sync-" + mainObject.getCode() + "-" + viewModelInfo.getVersionId());
    	
    	for (ViewModel.Field field : mainObject.getFields()) {
    		if (field.getObject() != null) {
    			gids.put(field.getObject().getCode(), "GID-" + appId + "-" + dstDsId + "-incr_sync-" + field.getObject().getCode() + "-" + viewModelInfo.getVersionId());
    		}
    	}
        
        if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
        	for (ViewModel.RelatedDataObject relatedObject : dataModel.getRelatedObjects()) {
            	gids.put(relatedObject.getCode(), "GID-" + appId + "-" + dstDsId + "-incr_sync-" + relatedObject.getCode() + "-" + viewModelInfo.getVersionId());
        	}
        }
        boolean lookup = false;
        List<String> objCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
            for (RelatedDataObject relObj : dataModel.getRelatedObjects()) {
            	if (relObj.getRelations().stream().filter(e->
            													!e.getRelatedField().startsWith("exp#")
            													&&(!e.getRelatedField().split("\\.")[0].equalsIgnoreCase(dataModel.getObject().getCode())
            															||!e.getRelatedField().split("\\.")[1].equalsIgnoreCase(pkFieldName))).count() > 0) {
            		lookup = true;
            		break;
            	} else if (!"none".equalsIgnoreCase(relObj.getLookupFrom())) {
            		objCodeList.add(relObj.getCode());
            	}
            }
        }
        if (!lookup) {
            for (ViewModel.Field field : dataModel.getObject().getFields()) {
            	if (field.getObject() != null) {
            		if (CollectionUtils.isNotEmpty(field.getObject().getRelations())) {
                    	if (field.getObject().getRelations().stream().filter(e->
																				!e.getRelatedField().startsWith("exp#")
																				&&(!e.getRelatedField().split("\\.")[0].equalsIgnoreCase(dataModel.getObject().getCode())
																						||!e.getRelatedField().split("\\.")[1].equalsIgnoreCase(pkFieldName))).count() > 0) {
							lookup = true;
							break;
						}
            		}
            	} else if (field.isFunc() || field.getRef().startsWith("exp#")) {
            		lookup = true;
					break;
            	} else if (field.getObject() != null && !"none".equalsIgnoreCase(field.getObject().getLookupFrom())) {
            		objCodeList.add(field.getObject().getCode());
            	}
            }
        }
        if (dataModel.getSettings().isLookupOptimize() && CollectionUtils.isNotEmpty(objCodeList)) {
        	gids.put(mainObject.getCode() + "__lookup", "GID-" + appId + "-" + dstDsId + "-incr_sync-" + mainObject.getCode() + "_lookup-" + viewModelInfo.getVersionId());
        } else if (lookup) {
        	gids.put(mainObject.getCode() + "__lookup", "GID-" + appId + "-" + dstDsId + "-incr_sync-" + mainObject.getCode() + "_lookup-" + viewModelInfo.getVersionId());
        }
        return gids;
    }
    
    public void createFullLinkTasks(Long datatubeInstId) {
        log.info("start createFullLinkTasks({})", datatubeInstId);

        DatatubeInstance datatubeInst = datatubeInstanceMapper.selectByPrimaryKey(datatubeInstId);
        
        String tenantId = datatubeInst.getTenantId();
        String operateEmpid = "schedulerx";
        
    	TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(tenantId);
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + tenantId + " is not configured");
    	}
    	TenantInfo tenantInfo = tenantList.get(0);
        
		String mainDbName = this.getMainDbName(tenantInfo);
    	
    	ViewModelInfoExample example = new ViewModelInfoExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andIdEqualTo(datatubeInst.getProviderId()).andIsDeletedEqualTo(0L);
		List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(viewModelInfos)) {
			throw new QanatBizException("tenant check failed");
		}
		ViewModelInfo viewModelInfo = viewModelInfos.get(0);
    	
    	DbInfoExample dbInfoExample = new DbInfoExample();
    	dbInfoExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDbNameEqualTo(mainDbName);
    	List<DbInfo> dbInfos = dbInfoMapper.selectByExample(dbInfoExample);
    	if (CollectionUtils.isEmpty(dbInfos)) {
    		throw new QanatBizException("DbInfo not found:" + mainDbName);
    	}
		
        ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());

        ViewModel originModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
    	ViewModel sysModel = null;
    	if (originModel.isDynamic()) {
    		sysModel = viewModelOptimizer.getOptimizedViewModel(tenantId, modelVersion.getUserYaml());
    	} else {
    		sysModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
    	}
    	ViewModel dataModel = new ViewModel();
    	BeanUtils.copyProperties(sysModel, dataModel);
        String tableName = dataModel.getCode();
        
    	JSONObject appKafkaJson = kafkaManagementService.getKafkaConfByAppName(tenantId, viewModelInfo.getAppName());

        DataObject mainObject = dataModel.getObject();
        String etlDbName = this.getEtlDbName(tenantId);
        Long appId = getAppIdByName(tenantId, viewModelInfo.getAppName());
		Long dstDsId = dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName);
    	String logTopicName = "stream-" + appId + "-" + dstDsId + "-" + mainObject.getCode();
		if ("metadata".equalsIgnoreCase(mainObject.getType())) {
        	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsUniqueName(tenantId, mainObject.getRef());
	    	JSONObject drcTopicInfo = srcDsMetaJson.getJSONObject("incrConf");
        	String jobName = "fulllink_" + appId + "_" + dstDsId + "_" + mainObject.getCode();
        	fullLinkProcessor.processObjectFullLinkJob4Drc(tenantId, viewModelInfo.getAppName(), jobName, mainDbName, tableName, operateEmpid, viewModelInfo.getVersionId(), mainObject, appKafkaJson, logTopicName, drcTopicInfo, dataModel, datatubeInst.getId(), originModel);
        } else if ("table".equalsIgnoreCase(mainObject.getType())) {
        	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, mainObject.getRef());
        	if (!"metric".equals(srcDsMetaJson.getString("sysType"))) {
            	JSONObject drcTopicInfo = srcDsMetaJson.getJSONObject("incrConf");
            	String jobName = "fulllink_" + appId + "_" + dstDsId + "_" + mainObject.getCode();
            	fullLinkProcessor.processFullLinkJob4Drc(tenantId, viewModelInfo.getAppName(), jobName, mainDbName, tableName, operateEmpid, viewModelInfo.getVersionId(), mainObject, appKafkaJson, logTopicName, dataModel, datatubeInst.getId(), drcTopicInfo);
        	}
        }
        
        if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
        	for (ViewModel.RelatedDataObject relatedObject : dataModel.getRelatedObjects()) {
            	logTopicName = "stream-" + appId + "-" + dstDsId + "-" + relatedObject.getCode();
    			if ("metadata".equalsIgnoreCase(relatedObject.getType())) {
                	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsUniqueName(tenantId, mainObject.getRef());
        	    	JSONObject drcTopicInfo = srcDsMetaJson.getJSONObject("incrConf");
        	    	
                	String jobName = "fulllink_" + appId + "_" + dstDsId + "_" + relatedObject.getCode();
                	fullLinkProcessor.processObjectFullLinkJob4Drc(tenantId, viewModelInfo.getAppName(), jobName, mainDbName, tableName, operateEmpid, viewModelInfo.getVersionId(), relatedObject, appKafkaJson, logTopicName, drcTopicInfo, dataModel, datatubeInst.getId(), originModel);
                } else if ("table".equalsIgnoreCase(relatedObject.getType())) {
                	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, relatedObject.getRef());
                	if (!"metric".equals(srcDsMetaJson.getString("sysType"))) {
                    	JSONObject drcTopicInfo = srcDsMetaJson.getJSONObject("incrConf");
                    	String jobName = "fulllink_" + appId + "_" + dstDsId + "_" + relatedObject.getCode();
                    	fullLinkProcessor.processFullLinkJob4Drc(tenantId, viewModelInfo.getAppName(), jobName, mainDbName, tableName, operateEmpid, viewModelInfo.getVersionId(), relatedObject, appKafkaJson, dataModel, datatubeInst.getId(), drcTopicInfo);
                	}
                } else if ("component".equalsIgnoreCase(relatedObject.getType())) {
                	List<JSONObject> drcSourceTopics = componentObjectProcessor.getTopicsByComponentObject(tenantId, relatedObject, "drc_in", dbInfos.get(0).getDbType());
                	if (CollectionUtils.isNotEmpty(drcSourceTopics)) {
	                	String jobName = "fulllink_" + appId + "_" + dstDsId + "_" + relatedObject.getCode();
	                	if (CollectionUtils.isNotEmpty(drcSourceTopics)) {
	                		fullLinkProcessor.processFullLinkJob4Drc(tenantId, viewModelInfo.getAppName(), jobName, mainDbName, tableName, operateEmpid, viewModelInfo.getVersionId(), relatedObject, appKafkaJson, logTopicName, dataModel, datatubeInst.getId(), drcSourceTopics.get(0));
	                	}
                	}
                }
        	}
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Long createBatchCheckTask(String tenantId, Long viewModelId, String operateEmpid) {
        log.info("start createBatchCheckTask({},{},{})", tenantId, viewModelId, operateEmpid);

		TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(tenantId);
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + tenantId + " is not configured");
    	}
    	TenantInfo tenantInfo = tenantList.get(0);
    	
        DatatubeInstanceExample diExample = new DatatubeInstanceExample();
        diExample.createCriteria().andTenantIdEqualTo(tenantId).andProviderEqualTo("viewmodel").andIsDeletedEqualTo(0L).andProviderIdEqualTo(viewModelId);
        List<DatatubeInstance> dis = datatubeInstanceMapper.selectByExample(diExample);
        if (CollectionUtils.isEmpty(dis)) {
        	throw new QanatBizException("viewModelId:" + viewModelId + " is not related to any datatube instance");
        }
        DatatubeInstance datatubeInst =  dis.get(0);

		String mainDbName = this.getMainDbName(tenantInfo);
        
    	ViewModelInfoExample example = new ViewModelInfoExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andIdEqualTo(viewModelId).andIsDeletedEqualTo(0L);
		List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(viewModelInfos)) {
			throw new QanatBizException("tenant check failed");
		}
		ViewModelInfo viewModelInfo = viewModelInfos.get(0);
        ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());

        ViewModel originModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
    	ViewModel sysModel = null;
    	if (originModel.isDynamic()) {
    		sysModel = viewModelOptimizer.getOptimizedViewModel(tenantId, modelVersion.getUserYaml());
    	} else {
    		sysModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
    	}
    	ViewModel dataModel = new ViewModel();
    	BeanUtils.copyProperties(sysModel, dataModel);
        String tableName = dataModel.getCode();
        List<String> batchCheckJobs = new ArrayList<>();
        
    	JSONObject kafkaJson = kafkaManagementService.getKafkaConfByAppName(tenantId, viewModelInfo.getAppName());

        DataObject mainObject = dataModel.getObject();
        Long appId = getAppIdByName(tenantId, viewModelInfo.getAppName());
        String etlDbName = this.getEtlDbName(tenantId);
		Long dstDsId = dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName);
//		if ("fullcolumns".equalsIgnoreCase(dataModel.getSettings().getBatchCheckPolicy())) {
        	String checkJobName = "fcbatcheck_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_v" + viewModelInfo.getVersionId();
			if ("table".equalsIgnoreCase(dataModel.getObject().getType()) && tableObjectProcessor.processFullColumnBatchCheckJob(tenantId, viewModelInfo.getAppName(), checkJobName, mainDbName, tableName, operateEmpid, viewModelInfo.getVersionId(), kafkaJson, dataModel, datatubeInst.getId())) {
				batchCheckJobs.add(checkJobName);
			}
//		} else {
//			if ("metadata".equalsIgnoreCase(mainObject.getType())) {
//            } else if ("table".equalsIgnoreCase(mainObject.getType())) {
//            	JSONObject dsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, mainObject.getRef());
//            	if ("metric".equals(dsMetaJson.getString("sysType"))) {
//            	} else {
//                	JSONObject srcDsMetaJson = dsInfoService.getDbMetaByDsName(tenantId, mainObject.getRef());
//                	String jobName = "batcheck_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_v" + viewModelInfo.getVersionId();
//                	if (tableObjectProcessor.processBatchCheckJob(tenantId, viewModelInfo.getAppName(), jobName, srcDsMetaJson, dbName, tableName, mainObject, operateEmpid, viewModelInfo.getVersionId(), kafkaJson, dataModel, datatubeInst.getId())) {
//                		batchCheckJobs.add(jobName);
//                	}
//            	}
//            }
//        	for (ViewModel.Field field : mainObject.getFields()) {
//        		if (field.getObject() != null) {
//        			if ("metadata".equalsIgnoreCase(field.getObject().getType())) {
//                    } else if ("table".equalsIgnoreCase(field.getObject().getType())) {
//                    } else if ("component".equalsIgnoreCase(field.getObject().getType())) {
//                    }
//        		}
//        		if (field.isFunc()) {
//        		}
//        	}
//            
//            if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
//            	for (ViewModel.RelatedDataObject relatedObject : dataModel.getRelatedObjects()) {
//        			if ("metadata".equalsIgnoreCase(relatedObject.getType())) {
//                    } else if ("table".equalsIgnoreCase(relatedObject.getType())) {
//                    	JSONObject srcDsMetaJson = dsInfoService.getDbMetaByDsName(tenantId, relatedObject.getRef());
//                    	if ("metric".equals(srcDsMetaJson.getString("sysType"))) {
//                    	} else {
//                        	String jobName = "batcheck_" + appId + "_" + dstDsId + "_" + relatedObject.getCode() + "_v" + viewModelInfo.getVersionId();
//                        	if (tableObjectProcessor.processBatchCheckJob(tenantId, viewModelInfo.getAppName(), jobName, srcDsMetaJson, dbName, tableName, relatedObject, operateEmpid, viewModelInfo.getVersionId(), kafkaJson, dataModel, datatubeInst.getId())) {
//                        		batchCheckJobs.add(jobName);
//                        	}
//                    	}
//                    }
//            	}
//            }
//		}
        
        //新增数据同步DAG任务
        if (CollectionUtils.isNotEmpty(batchCheckJobs)) {
        	String dagTaskName = "DAG_batchCheck_" + tableName;
        	
        	TaskInfoExample taskExp = new TaskInfoExample();
            taskExp.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andNameEqualTo(dagTaskName);
            List<TaskInfo> taskInfos = taskInfoMapper.selectByExample(taskExp);
    	    if (CollectionUtils.isEmpty(taskInfos)) {
	            Dag dag = new Dag(dagTaskName);
//				dag.setTimeExpression("0 0 2 * * ?");
				
				for (String jobName : batchCheckJobs) {
					BlinkBatchNode node = new BlinkBatchNode("BlinkBatch_" + jobName, dag);
					node.setJobName(jobName);
				}
				
				TaskInfoRequest taskInfo = new TaskInfoRequest();
		        taskInfo.setName(dagTaskName);
		        taskInfo.setOperateEmpid(operateEmpid);
		        taskInfo.setPolicy(DagPolicy.ODS.toString());
		        taskInfo.setTenantId(tenantId);
		        taskInfo.setAppName(viewModelInfo.getAppName());
		        Long taskId = taskService.createDAGTask(taskInfo, dag);
		        log.info("batcheck job[{}:{}] created, ", taskId, dagTaskName);
	            if (taskId == null) {
	                throw new QanatBizException("batcheck任务创建失败");
	            }
	            return taskId;
    	    } else {
		        log.info("batcheck job[{}:{}] already exists", taskInfos.get(0).getId(), dagTaskName);
    	    	return taskInfos.get(0).getId();
    	    }
        }
        return null;
    }
    
    public Boolean updateSlaData(String tenantId, String operateEmpid, List<Long> vmIds, String bizDate) {
        log.info("start createSlaView({},{},{},{})", tenantId, operateEmpid, JSON.toJSONString(vmIds), bizDate);
        
		TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(tenantId);
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + tenantId + " is not configured");
    	}
    	TenantInfo tenantInfo = tenantList.get(0);
        
        List<String> vmDelaySqls = new ArrayList<>();
        List<String> vmConsistencySqls = new ArrayList<>();
        String consistencySql = "insert overwrite table aliyun_tag.datatube_consistency_detail PARTITION(ds='#bizdate#') ";
        String delaySql = "insert overwrite table aliyun_tag.datatube_delay_detail PARTITION(ds='#bizdate#') ";
    	ViewModelInfoExample example = new ViewModelInfoExample();
    	ViewModelInfoExample.Criteria criteria = example.createCriteria();
    	criteria.andTenantIdEqualTo(tenantId);
    	criteria.andIsDeletedEqualTo(0L);
    	if (CollectionUtils.isNotEmpty(vmIds)) {
    		criteria.andIdIn(vmIds);
    	}
		List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(example);
		if (CollectionUtils.isNotEmpty(viewModelInfos)) {
			for (ViewModelInfo viewModelInfo : viewModelInfos) {
				DatatubeInstanceExample diExample = new DatatubeInstanceExample();
		        diExample.createCriteria().andTenantIdEqualTo(tenantId).andProviderEqualTo("viewmodel").andIsDeletedEqualTo(0L).andProviderIdEqualTo(viewModelInfo.getId());
		        List<DatatubeInstance> dis = datatubeInstanceMapper.selectByExample(diExample);
		        if (CollectionUtils.isEmpty(dis)) {
		        	throw new QanatBizException("viewModelId:" + viewModelInfo.getId() + " is not related to any datatube instance");
		        }
		        DatatubeInstance datatubeInst =  dis.get(0);	
				String mainDbName = this.getMainDbName(tenantInfo);
				
	            ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());
	            
	            DbInfoExample dbInfoExample = new DbInfoExample();
	        	dbInfoExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDbNameEqualTo(mainDbName);
	        	List<DbInfo> dbInfos = dbInfoMapper.selectByExample(dbInfoExample);
	        	if (CollectionUtils.isEmpty(dbInfos)) {
	        		throw new QanatBizException("DbInfo not found:" + mainDbName);
	        	}
	
	            ViewModel originModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
		    	ViewModel sysModel = null;
		    	if (originModel.isDynamic()) {
		    		sysModel = viewModelOptimizer.getOptimizedViewModel(tenantId, modelVersion.getUserYaml());
		    	} else {
		    		sysModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
		    	}
		    	if (!sysModel.getSettings().isSla()) {
		    		continue;
		    	}
		    	ViewModel dataModel = new ViewModel();
		    	BeanUtils.copyProperties(sysModel, dataModel);
	            String tableName = dataModel.getCode();
	
	            DataObject mainObject = dataModel.getObject();
	            if (CollectionUtils.isEmpty(mainObject.getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()))) {
	            	continue;
	            }
	            Field pkField = mainObject.getFields().stream().filter(e->e.isPk()).collect(Collectors.toList()).get(0);
	            String pkFieldName = pkField.getCode();
	            Long appId = getAppIdByName(tenantId, viewModelInfo.getAppName());

	            String etlDbName = this.getEtlDbName(tenantId);
				Long dstDsId = dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName);
				
				Map<String, String[]> dsDelaySlaMap = new HashMap<>();
				List<String> checkTopics = new ArrayList<>();
				List<String> correctTopics = new ArrayList<>();
				String chkPrefix = "chk-" + appId + "-" + dstDsId + "-";
				String crtPrefix = "crt-" + appId + "-" + dstDsId + "-";
				String bchkPrefix = "bchk-" + appId + "-" + dstDsId + "-";
				String bcrtPrefix = "bcrt-" + appId + "-" + dstDsId + "-";
    	    	String logTopicName = "stream-" + appId + "-" + dstDsId;
				if ("metadata".equalsIgnoreCase(mainObject.getType())) {
	            	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsUniqueName(tenantId, mainObject.getRef());
	    	    	String drcTopicName = srcDsMetaJson.getJSONObject("incrConf").getString("topicName");
	    	    	dsDelaySlaMap.put(mainObject.getCode(), new String[]{drcTopicName, logTopicName});
	            } else if ("table".equalsIgnoreCase(mainObject.getType())) {
	            	JSONObject dsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, mainObject.getRef());
	            	if (!"metric".equals(dsMetaJson.getString("sysType"))) {
	                	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, mainObject.getRef());
	                	String drcTopicName = srcDsMetaJson.getJSONObject("incrConf").getString("topicName");
	        	    	dsDelaySlaMap.put(mainObject.getCode(), new String[]{drcTopicName, logTopicName});
	            	}
	            }
		    	checkTopics.add(chkPrefix + mainObject.getCode());
		    	checkTopics.add(bchkPrefix + mainObject.getCode());
	        	correctTopics.add(crtPrefix + mainObject.getCode());
	        	correctTopics.add(bcrtPrefix + mainObject.getCode());
	
	        	for (ViewModel.Field field : mainObject.getFields()) {
	        		if (field.getObject() != null) {
	        			if ("metadata".equalsIgnoreCase(field.getObject().getType())) {
	        				
	                    } else if ("table".equalsIgnoreCase(field.getObject().getType())) {
	                    	JSONObject srcDsMetaJson = dsInfoService.getOdsTableMetaByDsName(tenantId, field.getObject().getRef());
	            	    	String drcTopicName = srcDsMetaJson.getJSONObject("incrConf").getString("topicName");
	            	    	dsDelaySlaMap.put(field.getObject().getCode(), new String[]{drcTopicName, logTopicName});
	                    } else if ("component".equalsIgnoreCase(field.getObject().getType())) {
	                    	ExtensionExample extExample = new ExtensionExample();
	                    	extExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTypeEqualTo("component-stream").andPluginEqualTo(field.getObject().getRef());
	            			List<Extension> exts = extensionMapper.selectByExampleWithBLOBs(extExample);
	            			if (CollectionUtils.isNotEmpty(exts)) {
	            				JSONObject json = JSON.parseObject(exts.get(0).getScript());
	            				String streamTopicName = json.getString("topicName");
		            	    	dsDelaySlaMap.put(field.getObject().getCode(), new String[]{streamTopicName, logTopicName});
	            			} else {
		                    	List<JSONObject> idSourceTopicInfos = componentObjectProcessor.getTopicsByComponentObject(tenantId, field.getObject(), "id_in", dbInfos.get(0).getDbType());
		                    	List<JSONObject> drcSourceTopicInfos = componentObjectProcessor.getTopicsByComponentObject(tenantId, field.getObject(), "drc_in", dbInfos.get(0).getDbType());
		        				List<String> idSourceTopics = idSourceTopicInfos.stream().map(e -> e.getString("topicName")).collect(Collectors.toList());
		        				List<String> drcSourceTopics = drcSourceTopicInfos.stream().map(e -> e.getString("topicName")).collect(Collectors.toList());
		            	    	dsDelaySlaMap.put(field.getObject().getCode(), new String[]{CollectionUtils.isNotEmpty(idSourceTopics) ? StringUtils.join(idSourceTopics, "|") : StringUtils.join(drcSourceTopics, "|"), logTopicName});
	            			}
	                    }
	        	    	checkTopics.add(chkPrefix + field.getObject().getCode());
	                	correctTopics.add(crtPrefix + field.getObject().getCode());
	        		}
	        	}
	            
	            if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
	            	for (ViewModel.RelatedDataObject relatedObject : dataModel.getRelatedObjects()) {
	        			if ("metadata".equalsIgnoreCase(relatedObject.getType())) {
	                    	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsUniqueName(tenantId, mainObject.getRef());
	            	    	String drcTopicName = srcDsMetaJson.getJSONObject("incrConf").getString("topicName");
	            	    	dsDelaySlaMap.put(relatedObject.getCode(), new String[]{drcTopicName, logTopicName});
	                    } else if ("table".equalsIgnoreCase(relatedObject.getType())) {
	                    	JSONObject srcDsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, relatedObject.getRef());
	                    	if (!"metric".equals(srcDsMetaJson.getString("sysType"))) {
	                        	String drcTopicName = srcDsMetaJson.getJSONObject("incrConf").getString("topicName");
	                	    	dsDelaySlaMap.put(relatedObject.getCode(), new String[]{drcTopicName, logTopicName});
	                    	}
	                    } else if ("component".equalsIgnoreCase(relatedObject.getType())) {
	                    	List<JSONObject> drcSourceTopicInfos = componentObjectProcessor.getTopicsByComponentObject(tenantId, relatedObject, "drc_in", dbInfos.get(0).getDbType());
	        				List<String> drcSourceTopics = drcSourceTopicInfos.stream().map(e -> e.getString("topicName")).collect(Collectors.toList());
                	    	dsDelaySlaMap.put(relatedObject.getCode(), new String[]{StringUtils.join(drcSourceTopics, ","), logTopicName});
	                    }
	        	    	checkTopics.add(chkPrefix + relatedObject.getCode());
	                	correctTopics.add(crtPrefix + relatedObject.getCode());
	            	}
	            }
	            boolean lookup = false;
	            if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
	                for (RelatedDataObject relObj : dataModel.getRelatedObjects()) {
	                	if (relObj.getRelations().stream().filter(e->
	                													!e.getRelatedField().startsWith("exp#")
	                													&&(!e.getRelatedField().split("\\.")[0].equalsIgnoreCase(dataModel.getObject().getCode())
	                															||!e.getRelatedField().split("\\.")[1].equalsIgnoreCase(pkFieldName))).count() > 0) {
	                		lookup = true;
	                		break;
	                	}
	                }
	            }
	            if (!lookup) {
		            for (ViewModel.Field field : dataModel.getObject().getFields()) {
		            	if (field.getObject() != null) {
		            		if (CollectionUtils.isNotEmpty(field.getObject().getRelations())) {
		                    	if (field.getObject().getRelations().stream().filter(e->
																						!e.getRelatedField().startsWith("exp#")
																						&&(!e.getRelatedField().split("\\.")[0].equalsIgnoreCase(dataModel.getObject().getCode())
																								||!e.getRelatedField().split("\\.")[1].equalsIgnoreCase(pkFieldName))).count() > 0) {
									lookup = true;
									break;
								}
		            		}
		            	} else if (field.isFunc() || (field.getRef() != null && field.getRef().startsWith("exp#"))) {
		            		lookup = true;
							break;
		            	}
		            }
	            }
	            if (lookup) {
	    	    	if ("metadata".equalsIgnoreCase(dataModel.getObject().getType())) {
	    				JSONObject odsDsMeta = dsInfoService.getTableMetaByDsUniqueName(tenantId, dataModel.getObject().getRef());
	    				String drcTopicName = odsDsMeta.getJSONObject("incrConf").getString("topicName");
	        	    	dsDelaySlaMap.put(dataModel.getObject().getCode() + "-lookup", new String[]{drcTopicName, logTopicName});
	    	    	} else if ("table".equalsIgnoreCase(dataModel.getObject().getType())) {
	    				JSONObject odsDsMeta = dsInfoService.getOdsTableMetaByDsName(tenantId, dataModel.getObject().getRef());
	    				String drcTopicName = odsDsMeta.getJSONObject("incrConf").getString("topicName");
	        	    	dsDelaySlaMap.put(dataModel.getObject().getCode() + "-lookup", new String[]{drcTopicName, logTopicName});
	    	    	}
	            }
	            List<String> tables = new ArrayList<>();
	            for (String table : dsDelaySlaMap.keySet()) {
	            	tables.add(" select " + datatubeInst.getId() + " as datatube_id, '" + table + "' as `table_name`, a.trace_id, a.gmt_create,(b.ts-a.ts)  as delay_ms\r\n" + 
	            			"        from (select * from aliyun_tag.ods_datatube_fulllink_log_df where ds='#bizdate#' and db='" + dsDelaySlaMap.get(table)[0] + "')  as a \r\n" + 
	            			"             inner join \r\n" + 
	            			"             (select * from aliyun_tag.ods_datatube_fulllink_log_df where ds='#bizdate#' and db='" + dsDelaySlaMap.get(table)[1] + "') as b \r\n" + 
	            			"             on a.trace_id= b.trace_id ");
	            }
	            vmDelaySqls.add(StringUtils.join(tables, " union all "));
	            vmConsistencySqls.add(" select " + datatubeInst.getId() + " as datatube_id, chk.db as obj, chk.biz_day, chk.cnt as check_cnt, COALESCE(crt.cnt, 0) as correct_cnt from (\r\n" + 
	                		"    select replace(replace(db,'" + bchkPrefix + "',''),'" + chkPrefix + "','') as db,to_char(gmt_create, 'yyyyMMdd') as biz_day, count(*) as cnt\r\n" + 
	                		"    from aliyun_tag.ods_datatube_fulllink_log_df \r\n" + 
	                		"    where ds='#bizdate#' and db in ('" + StringUtils.join(checkTopics, "','") + "')\r\n" + 
	                		"    group by db, to_char(gmt_create, 'yyyyMMdd')\r\n" + 
	                		") as chk left join (\r\n" + 
	                		"    select replace(replace(db,'" + bcrtPrefix + "',''),'" + crtPrefix + "','') as db,to_char(gmt_create, 'yyyyMMdd') as biz_day, count(*) as cnt\r\n" + 
	                		"    from aliyun_tag.ods_datatube_fulllink_log_df \r\n" + 
	                		"    where ds='#bizdate#' and db in ('" + StringUtils.join(correctTopics, "','") + "')\r\n" + 
	                		"    group by db, to_char(gmt_create, 'yyyyMMdd')\r\n" + 
	                		") as crt\r\n" + 
	                		"on chk.biz_day=crt.biz_day and chk.db=crt.db ");
			}
			delaySql +=  StringUtils.join(vmDelaySqls, " union all ");
			log.info("delaySql={}", delaySql);
			consistencySql +=  StringUtils.join(vmConsistencySqls, " union all ");
			log.info("consistencySql={}", consistencySql);
			
			execOdpsSql(tenantId, delaySql, bizDate);
			execOdpsSql(tenantId, consistencySql, bizDate);
    	}
        return true;
    }

	private void execOdpsSql(String tenantId, String sql, String bizDate) {
        if (bizDate == null) {
        	Date today = new Date();
        	SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        	bizDate = sdf.format(com.taobao.unifiedsession.core.commons.utils.DateUtils.addDay(today, -1));
        }
		
		AppResourceRelationExample example = new AppResourceRelationExample();
		example.createCriteria().andAppNameEqualTo("qanat_sla").andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andRelationTypeEqualTo("odps");
		List<AppResourceRelation> rels = appResourceRelationMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(rels)) {
			throw new QanatBizException("no app resouces");
		}
		AppResourceRelation ref = rels.get(0);
		ResourceExample example1 = new ResourceExample();
		example1.createCriteria().andResourceNameEqualTo(ref.getResourceName()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L);
		List<com.aliyun.wormhole.qanat.dal.domain.Resource> resources = resourceMapper.selectByExampleWithBLOBs(example1);
		if (CollectionUtils.isEmpty(resources)) {
			throw new QanatBizException(ref.getResourceName() + " has no app resouces");
		}
		com.aliyun.wormhole.qanat.dal.domain.Resource resource = resources.get(0);
		JSONObject metaJson = JSON.parseObject(resource.getMeta());
		OdpsClient client = new OdpsClient(metaJson.getString("endpoint"), metaJson.getString("accessId"), metaJson.getString("accessKey"),
				metaJson.getString("project"), metaJson.getString("mcUrl"), metaJson.getString("mcToken"));
		
		String execSql = sql.replaceAll("#bizdate#", bizDate) + ";";
		log.info("odps sql={}", execSql);
		long startTs = System.currentTimeMillis();
		String logview = client.queryOdpsSql(execSql);
		log.info("odps sql exec finished using {} ms", System.currentTimeMillis()-startTs);
		log.info("Qanat Odps logview:{}", logview);
	}

	protected Field getFkFieldByObject(ViewModel dataModel, RelatedDataObject object) {
		String relationField = object.getRelations().stream().filter(e->!e.getRelatedField().startsWith("exp#")).collect(Collectors.toList()).get(0).getRelatedField();
		String relObjCode = relationField.split("\\.")[0];
		String relFieldName = relationField.split("\\.")[1];
		Field fkField = null;
		if (relObjCode.equalsIgnoreCase(dataModel.getObject().getCode())) {
			fkField = dataModel.getObject().getFields().stream().filter(e->e.getCode().equalsIgnoreCase(relFieldName)).collect(Collectors.toList()).get(0);
		} else {
			for (RelatedDataObject relObject : dataModel.getRelatedObjects()) {
				if (relObject.getCode().equalsIgnoreCase(relObjCode)) {
					fkField = relObject.getFields().stream().filter(e->e.getCode().equalsIgnoreCase(relFieldName)).collect(Collectors.toList()).get(0);
					break;
				}
			}
		}
		return fkField;
	}
    
    @Transactional(propagation = Propagation.REQUIRED)
    public Boolean createTableAndFullSync(String tenantId, Long viewModelId, String batchJobs) {
    	log.info("createTableAndFullSync({},{},{},{}) start", tenantId, viewModelId, batchJobs);
        Statement statement = null;
        Connection connection = null;
        try {
        	ViewModelInfoExample example = new ViewModelInfoExample();
    		example.createCriteria().andTenantIdEqualTo(tenantId).andIdEqualTo(viewModelId).andIsDeletedEqualTo(0L);
    		List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(example);
    		if (CollectionUtils.isEmpty(viewModelInfos)) {
    			throw new QanatBizException("tenant check failed");
    		}
    		ViewModelInfo viewModelInfo = viewModelInfos.get(0);
            ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());
            ViewModel originModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
	    	ViewModel dataModel = getSysViewModel(tenantId, modelVersion, originModel);
	    	
	    	String etlDbName = getEtlDbName(tenantId);
	    	
    		String workTableName = getTmpTableName(dataModel.getCode(), modelVersion.getId());
    		
            log.info("vm[{}] start to build[{}.{}]", viewModelId, etlDbName, workTableName);
            createTable(tenantId, dataModel, viewModelInfo.getObjectType(), etlDbName, workTableName, true);
            log.info("vm[{}] work table[{}.{}] created", viewModelId, etlDbName, workTableName);

            log.info("vm[{}] fullsync to [{}.{}] started", viewModelId, etlDbName, workTableName);
            Boolean isSuccess = syncfullDataForAdbTable(tenantId, dataModel, etlDbName, workTableName);
            if (!isSuccess) {
                log.error("vm[{}] fullsync to [{}.{}] failed", viewModelId, etlDbName, workTableName);
                throw new QanatBizException("vm:" + viewModelId + " full data sync to " + etlDbName + "." + workTableName + " failed");
            }
            log.info("vm[{}] fullsync to [{}.{}] finished", viewModelId, etlDbName, workTableName);
	    	
            if (StringUtils.isNotBlank(batchJobs)) {
            	String[] jobs = batchJobs.split(",");
            	if (jobs.length > 0) {
                    log.info("vm[{}] start to run blinkbatch jobs", viewModelId, batchJobs);
            		for (int i=0; i<jobs.length; i++) {
            			String jobName = jobs[i];
                        log.info("batch job[{}] start", jobName);
                        Map<String, String> params = new HashMap<>();
                        params.put("tableName", workTableName);
            			Long blinkInstId = blinkService.startBatchJob(tenantId, viewModelInfo.getAppName(), jobName, params);
                        if (blinkInstId != null) {
                            while (true) {
                                String instState = blinkService.getInstanceActualState(tenantId, viewModelInfo.getAppName(), jobName, blinkInstId);
                                if ("SUCCESS".equalsIgnoreCase(instState)) {
                                    break;
                                }
                                Thread.sleep(60000);//60s
                            }
                        }
                        log.info("batch job[{}] finished", jobName);
            		}
            	}
            }
            log.info("start to offline last version tasks");
            offlineLastVersionTasks(tenantId, "schedulerx2", viewModelInfo);
            log.info("finished to offline last version tasks");

            JSONObject dbMetaJson = dsInfoService.getDbMetaByName(etlDbName);
            RdsConnectionParam param = new RdsConnectionParam();
            param.setUrl(dbMetaJson.getString("jdbcUrl"))
	            .setUserName(dbMetaJson.getString("username"))
	            .setPassword(dbMetaJson.getString("password"));
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement();
            String finalTableName = dataModel.getCode();
            String backupTableName = "bak_" + finalTableName + "_" + viewModelInfo.getVersionId() + "_" + DateUtils.formatDate(new Date(), "yyMMddHHmm");
            
            statement = connection.createStatement();
            try {
                statement.execute("alter table " + finalTableName + " rename to " + backupTableName);
            } catch (Exception e) {
                log.error("alter final table name to backup failed, error={}", e.getMessage());
            }
            statement.execute("alter table " + workTableName + " rename to " + finalTableName);
            
            DatasourceRequest dsInfoReq = new DatasourceRequest();
            dsInfoReq.setTenantId(tenantId);
            dsInfoReq.setDsName(dsInfoService.getDsName(tenantId, viewModelInfo.getAppName(), etlDbName, finalTableName));
            dsInfoReq.setOperateEmpid("schedulerx2");
            dsInfoService.modifyDatasource(dsInfoReq);
            log.info("update dsInfo[{}] finished", dsInfoReq.getDsName());
            
            //重新生成从库临时表
        	List<String> extDbNames = getExtDbNames(tenantId);
        	if (CollectionUtils.isNotEmpty(extDbNames)) {
        		for (String extDbName : extDbNames) {
		        	String tmpTableName = "tmp_" + dataModel.getCode();
		            log.info("vm[{}] start to build[{}.{}] created", viewModelId, extDbName, tmpTableName);
		    		createTable(tenantId, dataModel, viewModelInfo.getObjectType(), extDbName, tmpTableName, true);
		            log.info("vm[{}] work table[{}.{}] created", viewModelId, extDbName, tmpTableName);
        		}
            	
            	//重新生成从库复制批量任务
        		try {
	            	DatatubeInstanceExample diExample = new DatatubeInstanceExample();
	            	diExample.createCriteria().andTenantIdEqualTo(tenantId).andProviderIdEqualTo(viewModelId).andProviderEqualTo("viewmodel");
	            	List<DatatubeInstance> datatubeInsts = datatubeInstanceMapper.selectByExample(diExample);
	                if (CollectionUtils.isNotEmpty(datatubeInsts)) {
		                this.rebuildMultiDbSyncJob(datatubeInsts.get(0).getId());
			            log.info("vm[{}] rebuildMultiDbSyncJob finished", viewModelId);
	                }
        		} catch (Exception e) {
        			log.error("重新生成从库复制批量任务失败:{}, vmId={}", e.getMessage(), viewModelId, e);
        		}
        	}
            
            return true;
        } catch (Exception e) {
            log.error("createTableAndFullSync({},{},{}) failed:{}",  tenantId, viewModelId, batchJobs, e.getMessage(), e);
            throw new QanatBizException("createTableAndFullSync faild:" + e.getMessage());
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                } finally {
                	statement = null;
                }
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                } finally {
                	connection = null;
                }
            }
        }
    }
    
    @Transactional(propagation = Propagation.REQUIRED)
    public Boolean createTableAndFullSyncByYaml(String tenantId, Long viewModelId, String dstDbName, String userYaml) {
    	log.info("createTableAndFullSyncByYaml({},{},{},{}) start", tenantId, viewModelId, dstDbName, userYaml);
        Statement statement = null;
        Connection connection = null;
        try {
        	ViewModelInfoExample example = new ViewModelInfoExample();
    		example.createCriteria().andTenantIdEqualTo(tenantId).andIdEqualTo(viewModelId).andIsDeletedEqualTo(0L);
    		List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(example);
    		if (CollectionUtils.isEmpty(viewModelInfos)) {
    			throw new QanatBizException("tenant check failed");
    		}
    		ViewModelInfo viewModelInfo = viewModelInfos.get(0);
            ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());
            
	    	ViewModel sysModel = viewModelOptimizer.getOptimizedViewModel(tenantId, userYaml);
	    	
    		String workTableName = getTmpTableName(sysModel.getCode(), modelVersion.getId());
    		
            createTable(tenantId, sysModel, viewModelInfo.getObjectType(), dstDbName, workTableName, true);
            log.info("vm[{}] work table[{}.{}] created", viewModelId, dstDbName, workTableName);

            Boolean isSuccess = syncfullDataForAdbTable(tenantId, sysModel, dstDbName, workTableName);
            if (!isSuccess) {
                log.error("vm[{}] fullsync to [{}.{}] failed", viewModelId, dstDbName, workTableName);
                throw new QanatBizException("vm:" + viewModelId + " full data sync to " + dstDbName + "." + workTableName + " failed");
            }
            log.info("vm[{}] fullsync to [{}.{}] finished", viewModelId, dstDbName, workTableName);
	    	
            JSONObject dbMetaJson = dsInfoService.getDbMetaByName(dstDbName);
            RdsConnectionParam param = new RdsConnectionParam();
            param.setUrl(dbMetaJson.getString("jdbcUrl"))
	            .setUserName(dbMetaJson.getString("username"))
	            .setPassword(dbMetaJson.getString("password"));
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement();
            String finalTableName = sysModel.getCode();
            String backupTableName = "bak_" + finalTableName + "_" + viewModelInfo.getVersionId() + "_" + DateUtils.formatDate(new Date(), "yyMMddHHmm");
            
            statement = connection.createStatement();
            try {
                statement.execute("alter table " + finalTableName + " rename to " + backupTableName);
            } catch (Exception e) {
                log.error("alter final table name to backup failed, error={}", e.getMessage());
            }
            statement.execute("alter table " + workTableName + " rename to " + finalTableName);
            
            DatasourceRequest dsInfoReq = new DatasourceRequest();
            dsInfoReq.setTenantId(tenantId);
            dsInfoReq.setDsName(dsInfoService.getDsName(tenantId, viewModelInfo.getAppName(), dstDbName, finalTableName));
            dsInfoReq.setOperateEmpid("schedulerx2");
            dsInfoService.modifyDatasource(dsInfoReq);
            log.info("update dsInfo[{}] finished", dsInfoReq.getDsName());
            
            return true;
        } catch (Exception e) {
            log.error("createTableAndFullSyncByYaml({},{},{},{}) failed:{}",  tenantId, viewModelId, dstDbName, userYaml, e.getMessage(), e);
            throw new QanatBizException("createTableAndFullSyncByYaml faild:" + e.getMessage());
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                } finally {
                	statement = null;
                }
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                } finally {
                	connection = null;
                }
            }
        }
    }

	protected ViewModel getSysViewModel(String tenantId, ViewModelVersionWithBLOBs modelVersion, ViewModel originModel) {
		ViewModel dataModel = null;
		if (originModel.isDynamic()) {
			dataModel = viewModelOptimizer.getOptimizedViewModel(tenantId, modelVersion.getUserYaml());
		} else {
			dataModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
		}
		return dataModel;
	}

	protected String getMainDbName(TenantInfo tenantInfo) {
		return tenantInfo.getDefaultDw();
	}

	public String getEtlDbName(String tenantId) {
		TenantInfoExample tiExample = new TenantInfoExample();
		tiExample.createCriteria().andTenantIdEqualTo(tenantId);
		List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
		if (CollectionUtils.isEmpty(tenantList)) {
			throw new QanatBizException("tenantId:" + tenantId + " is not configured");
		}
		TenantInfo tenantInfo = tenantList.get(0);
		return getEtlDbName(tenantInfo);
	}

	protected String getEtlDbName(TenantInfo tenantInfo) {
		if (StringUtils.isNotBlank(tenantInfo.getEtlDw())) {
			return tenantInfo.getEtlDw();
		} else {
			return tenantInfo.getDefaultDw();
		}
	}

	public List<String> getExtDbNames(String tenantId) {
		TenantInfoExample tiExample = new TenantInfoExample();
		tiExample.createCriteria().andTenantIdEqualTo(tenantId);
		List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
		if (CollectionUtils.isEmpty(tenantList)) {
			throw new QanatBizException("tenantId:" + tenantId + " is not configured");
		}
		TenantInfo tenantInfo = tenantList.get(0);
		List<String> extDbNames = new ArrayList<>();
		List<String> dstDbNames = getDstDbNames(tenantInfo);
		String etlDbName = this.getEtlDbName(tenantInfo);
		for (String dstDbName : dstDbNames) {
			if (!dstDbName.equalsIgnoreCase(etlDbName)) {
				extDbNames.add(dstDbName);
			}
		}
		return extDbNames;
	}

	public List<String> getExtDbNames(TenantInfo tenantInfo, DatatubeInstance datatubeInst) {
		List<String> extDbNames = new ArrayList<>();
		List<String> dstDbNames = getDstDbNames(tenantInfo, datatubeInst);
		String etlDbName = this.getEtlDbName(tenantInfo);
		for (String dstDbName : dstDbNames) {
			if (!dstDbName.equalsIgnoreCase(etlDbName)) {
				extDbNames.add(dstDbName);
			}
		}
		return extDbNames;
	}

	public List<String> getDstDbNames(String tenantId) {
		TenantInfoExample tiExample = new TenantInfoExample();
		tiExample.createCriteria().andTenantIdEqualTo(tenantId);
		List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
		if (CollectionUtils.isEmpty(tenantList)) {
			throw new QanatBizException("tenantId:" + tenantId + " is not configured");
		}
		TenantInfo tenantInfo = tenantList.get(0);
		return getDstDbNames(tenantInfo);
	}

	public List<String> getDstDbNames(TenantInfo tenantInfo) {
		List<String> dbNames = new ArrayList<>();
		
		dbNames.add(tenantInfo.getDefaultDw());
		if (StringUtils.isNotBlank(tenantInfo.getBackupDw())) {
    		for (String bakDbName : Arrays.asList(tenantInfo.getBackupDw().split(","))) {
    			if (!dbNames.contains(bakDbName)) {
    	    		dbNames.add(bakDbName);
    			}
    		}
		}
		
		return dbNames;
	}

	public List<String> getDstDbNames(TenantInfo tenantInfo, DatatubeInstance datatubeInst) {
		List<String> dbNames = new ArrayList<>();
		
		dbNames.add(tenantInfo.getDefaultDw());
		if (StringUtils.isNotBlank(tenantInfo.getBackupDw())) {
    		for (String bakDbName : Arrays.asList(tenantInfo.getBackupDw().split(","))) {
    			if (!dbNames.contains(bakDbName)) {
    	    		dbNames.add(bakDbName);
    			}
    		}
		}
		
		if (StringUtils.isNotBlank(datatubeInst.getDbName())) {
    		for (String extDbName : Arrays.asList(datatubeInst.getDbName().split(","))) {
    			if (!dbNames.contains(extDbName)) {
    	    		dbNames.add(extDbName);
    			}
    		}
    	}
		
		return dbNames;
	}
    
    public Long restartModelTask(String tenantId, Long viewModelId, String operateEmpid) {
        log.info("start restartModelTask({},{},{})", tenantId, viewModelId, operateEmpid);
        
    	ViewModelInfoExample example = new ViewModelInfoExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andIdEqualTo(viewModelId).andIsDeletedEqualTo(0L);
		List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(viewModelInfos)) {
			throw new QanatBizException("vmId:" + viewModelId + " has no vmInfo found");
		}
		ViewModelInfo viewModelInfo = viewModelInfos.get(0);
		//offlineLastVersionTasks(tenantId, operateEmpid, viewModelInfo);
    	
    	//start new version task
		ViewModelTaskRelationExample modelTaskRelExample = new ViewModelTaskRelationExample();
    	modelTaskRelExample.setOrderByClause("model_version_id desc");
    	modelTaskRelExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andViewModelNameEqualTo(viewModelInfo.getModelName()).andModelVersionIdEqualTo(viewModelInfo.getVersionId());
    	List<ViewModelTaskRelation> modelTaskRels = viewModelTaskRelationMapper.selectByExample(modelTaskRelExample);
    	if (CollectionUtils.isEmpty(modelTaskRels)) {
    		throw new QanatBizException("no task related to model[" + viewModelId + "] found");
    	}
    	Long taskId = null;
    	//取主任务
    	for (ViewModelTaskRelation modelTaskRel : modelTaskRels) {
        	if ("main".equalsIgnoreCase(modelTaskRel.getRelationType())) {
        		taskId = modelTaskRel.getTaskId();
        		break;
        	}
    	}
    	if (taskId != null) {
        	DataResult<Long> runTaskResult = taskService.runTask(tenantId, operateEmpid, taskId);
        	if (runTaskResult == null || !runTaskResult.getSuccess()) {
        		throw new QanatBizException("task[" + modelTaskRels.get(0).getTaskId() + "] is started with exception[" + (runTaskResult != null ? runTaskResult.getMessage() : "null") + "]");
        	}
        	log.info("new version[{}] task[{}] is started with taskInstId[{}]", viewModelInfo.getVersionId(), taskId, runTaskResult.getData());
        	return runTaskResult.getData();
    	} else {
    		log.info("no main task related to model[{}] with version[{}]", viewModelInfo.getModelName(), viewModelInfo.getVersionId());
    	}
        return null;
    }

	protected void offlineLastVersionTasks(String tenantId, String operateEmpid, ViewModelInfo viewModelInfo) {
		//查询model关联的上一个taskId，如果存在则stop and delete
		ViewModelTaskRelationExample modelTaskRelExample = new ViewModelTaskRelationExample();
		modelTaskRelExample.setOrderByClause("model_version_id desc");
		modelTaskRelExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andViewModelNameEqualTo(viewModelInfo.getModelName()).andModelVersionIdNotEqualTo(viewModelInfo.getVersionId());
		List<ViewModelTaskRelation> modelTaskRels = viewModelTaskRelationMapper.selectByExample(modelTaskRelExample);
		if (CollectionUtils.isNotEmpty(modelTaskRels)) {
			for (ViewModelTaskRelation modelTaskRel : modelTaskRels) {
				log.info("start to proecess vm[{}] version[{}] task[{}]", modelTaskRel.getViewModelName(), modelTaskRel.getModelVersionId(), modelTaskRel.getTaskId());
		    	if (taskService.stopAndDeleteTask(tenantId, operateEmpid, modelTaskRel.getTaskId()).getData()) {
					log.info("vm[{}] version[{}] task[{}] is stopped and deleted", modelTaskRel.getViewModelName(), modelTaskRel.getModelVersionId(), modelTaskRel.getTaskId());
					//任务删除完随机从VM关联任务中去掉
					ViewModelTaskRelation record = new ViewModelTaskRelation();
					record.setId(modelTaskRel.getId());
					record.setIsDeleted(modelTaskRel.getId());
					record.setGmtModified(new Date());
					record.setModifyEmpid(operateEmpid);
					viewModelTaskRelationMapper.updateByPrimaryKey(record);
		    	} else {
		    		log.error("vm[{}] version[{}] task[{}] is stopped and deleted with error", modelTaskRel.getViewModelName(), modelTaskRel.getModelVersionId(), modelTaskRel.getTaskId());
		    	}
			}
		}
	}
    
    public Boolean reflectObjectFieldChange(String tenantId, String dsUniqueName, String fieldName, Integer isRef, String operateType, String tagJson) {
        log.info("reflectObjectFieldChange({},{},{},{},{},{}) start", tenantId, dsUniqueName, fieldName, isRef, operateType, tagJson);
        
        Statement statement = null;
        Connection connection = null;
        try {
        	List<ViewModelDsRelation> modelDsRels = null;
        	Datasource objDsInfo = null;
        	String objDsName = null;

        	//根据对象code获取对应的表定义
        	DatasourceExample objDsExample = new DatasourceExample();
        	objDsExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsUniqueNameEqualTo(dsUniqueName).andDsTypeEqualTo("obj");
        	List<Datasource> objDsList = datasourceMapper.selectByExample(objDsExample);
        	if (CollectionUtils.isEmpty(objDsList)) {
        		throw new QanatBizException("no dsInfo found, dusUniqueName:" + dsUniqueName);
        	}
        	objDsInfo = objDsList.get(0);
        	objDsName = objDsInfo.getDsName();
        	
        	//根据对象dsName获取以对象(metadata)作为源表的viewModel范围
        	ViewModelDsRelationExample dsRelExample = new ViewModelDsRelationExample();
        	dsRelExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andRelationTypeEqualTo("from_ds").andDsNameEqualTo(objDsName);
        	modelDsRels = viewModelDsRelationMapper.selectByExample(dsRelExample);
        	if (CollectionUtils.isEmpty(modelDsRels)) {
        		throw new QanatBizException("no related viewModel found by from_ds:" + objDsName);
        	}
        	
        	List<String> viewModels = modelDsRels.stream().map(item -> item.getViewModelName()).collect(Collectors.toList());
        	
        	//获取model的定义，用于判断是否引用对象全字段，只有这种情况下会触发模型产生的目标表的表结构变更
        	ViewModelInfoExample modelInfoExample = new ViewModelInfoExample();
        	modelInfoExample.createCriteria().andTenantIdEqualTo(tenantId).andModelNameIn(viewModels).andIsDeletedEqualTo(0L);
    		List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(modelInfoExample);
    		if (CollectionUtils.isEmpty(viewModelInfos)) {
    			throw new QanatBizException("no models found");
    		}
    		List<Long> versionIds = viewModelInfos.stream().map(item -> item.getVersionId()).collect(Collectors.toList());
        	ViewModelVersionExample modelVersionExample = new ViewModelVersionExample();
        	modelVersionExample.createCriteria().andTenantIdEqualTo(tenantId).andIdIn(versionIds);
            List<ViewModelVersionWithBLOBs> modelVersions = viewModelVersionMapper.selectByExampleWithBLOBs(modelVersionExample);
            if (CollectionUtils.isEmpty(modelVersions)) {
    			throw new QanatBizException("no model versions found");
    		}
            Map<String, Boolean> modelObjWithDyniamicFieldsMap = new HashMap<>();
            for (ViewModelVersionWithBLOBs modelVersion : modelVersions) {
            	ViewModel userModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
            	if (dsUniqueName.equalsIgnoreCase(userModel.getObject().getRef())) {
            		if (CollectionUtils.isEmpty(userModel.getObject().getFields()) || userModel.isDynamic()) {
            			modelObjWithDyniamicFieldsMap.put(modelVersion.getViewModelName(), true);
            			continue;
            		}
            		if (CollectionUtils.isNotEmpty(userModel.getRelatedObjects())) {
            			for (RelatedDataObject relObj : userModel.getRelatedObjects()) {
            				if (dsUniqueName.equalsIgnoreCase(relObj.getRef())) {
                        		if (CollectionUtils.isEmpty(relObj.getFields()) || userModel.isDynamic()) {
                        			modelObjWithDyniamicFieldsMap.put(modelVersion.getViewModelName(), true);
                        			continue;
                        		}
            				}
            			}
            		}
            	}
            }
        	//根据viewModel获取目标表的表定义
            dsRelExample = new ViewModelDsRelationExample();
        	dsRelExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andRelationTypeEqualTo("to_ds").andViewModelNameIn(viewModels);
        	List<ViewModelDsRelation> modelToDsRels = viewModelDsRelationMapper.selectByExample(dsRelExample);
        	if (CollectionUtils.isEmpty(modelToDsRels)) {
        		throw new QanatBizException("no to_ds in viewModel found");
        	}
        	List<String> toDsNames = modelToDsRels.stream().map(item -> item.getDsName()).collect(Collectors.toList());
        	Map<String, ViewModelDsRelation> dsModelMap = modelToDsRels.stream().collect(Collectors.toMap(ViewModelDsRelation::getDsName, Function.identity()));
        	DatasourceExample adbDsExample = new DatasourceExample();
        	adbDsExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsNameIn(toDsNames);
        	List<Datasource> adbDsList = datasourceMapper.selectByExample(adbDsExample);

			//遍历相关目标表进行表结构变更
        	for (int i = 0; i < adbDsList.size(); i++) {
				Datasource toDsInfo = adbDsList.get(i);

				//获取目标DB连接
        		log.info("start to ddl:{}", toDsInfo.getDbName());
	        	JSONObject dbMetaJson = dsInfoService.getDbMetaByName(toDsInfo.getDbName());
	        	String dstDbType = dbMetaJson.getString("dbType");
	            RdsConnectionParam param = new RdsConnectionParam();
	            param.setUrl(dbMetaJson.getString("jdbcUrl"))
		            .setUserName(dbMetaJson.getString("username"))
		            .setPassword(dbMetaJson.getString("password"));
	            connection = dsHandler.connectToTable(param);
	
	        	//对与此对象相关的源表及目标表调整表结构，且晋档model中的对象为默认引用全字段时进行表结构调整，注：因为是单方面对象增减字段，不产生新的model version
	        	if ("CREATE".equalsIgnoreCase(operateType)) {
	        		addDsFieldInfoAndTableColumn(tenantId, dsUniqueName, fieldName, statement, connection,
							objDsInfo, objDsName, modelObjWithDyniamicFieldsMap, dsModelMap, toDsInfo, tagJson, dstDbType);
	        	} else if ("UPDATE".equalsIgnoreCase(operateType)) {
	        		//obj表字段更新描述信息
	        		dsInfoService.updObjectField(tenantId, objDsInfo.getObjectType(), dsUniqueName, fieldName, tagJson);
	        		//容错处理
	    			try {
	    				addDsFieldInfoAndTableColumn(tenantId, dsUniqueName, fieldName, statement, connection,
	    						objDsInfo, objDsName, modelObjWithDyniamicFieldsMap, dsModelMap, toDsInfo, tagJson, dstDbType);
	    			} catch(Exception e) {
	    				log.info("try to add field again failed,{}", e.getMessage());
	    			}
	        		
	        		DsFieldInfoExample dsFieldExample= new DsFieldInfoExample();
	            	dsFieldExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsUniqueNameEqualTo(dsUniqueName).andFieldUniqueNameEqualTo(fieldName);
	            	List<DsFieldInfo> dsFieldInfos = dsFieldInfoMapper.selectByExample(dsFieldExample);
	            	if (CollectionUtils.isEmpty(dsFieldInfos)) {
	            		throw new QanatBizException("upd field is not found");
	            	}
	            	DsFieldInfo updField = dsFieldInfos.get(0);
	            	
	            	//adb目标表更改字段描述
					String tableName = toDsInfo.getTableName();
					statement = connection.createStatement();
					try {
						String sql = null;
						if ("hologres".equalsIgnoreCase(dstDbType) || "postgresql".equalsIgnoreCase(dstDbType)) {
							sql = "alter table " + tableName + " MODIFY COLUMN " + updField.getFieldName() + " " + updField.getFieldType();
						} else {
							sql = "alter table " + tableName + " MODIFY COLUMN " + updField.getFieldName() + " " + updField.getFieldType() + " COMMENT '" + updField.getFieldDesc() + "'";
						}
						log.info("alter sql={}", sql);
						statement.execute(sql);
					} catch (Exception e) {
						log.error("alter table failed:{}", e.getMessage());
					}
	        	} else if ("DELETE".equalsIgnoreCase(operateType)) {
	        		DsFieldInfoExample dsFieldExample= new DsFieldInfoExample();
	            	dsFieldExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsUniqueNameEqualTo(dsUniqueName).andFieldUniqueNameEqualTo(fieldName);
	            	List<DsFieldInfo> dsFieldInfos = dsFieldInfoMapper.selectByExample(dsFieldExample);
	            	if (CollectionUtils.isEmpty(dsFieldInfos)) {
	            		throw new QanatBizException("upd field is not found:" + dsUniqueName + ":" + fieldName);
	            	}
	            	DsFieldInfo updField = dsFieldInfos.get(0);
	        		//obj表模型清字段(逻辑删)
	        		dsInfoService.deleteObjectField(tenantId, dsUniqueName, fieldName);
	
	            	//adb目标表清理字段
					if (dsModelMap.get(toDsInfo.getDsName()) == null
							|| dsModelMap.get(toDsInfo.getDsName()).getViewModelName() == null
							|| modelObjWithDyniamicFieldsMap.get(dsModelMap.get(toDsInfo.getDsName()).getViewModelName()) == null) {
						log.error("ds[{}] has no viewmodel matched", toDsInfo.getDsName());
						continue;
					}
					if (modelObjWithDyniamicFieldsMap.get(dsModelMap.get(toDsInfo.getDsName()).getViewModelName())) {
						String tableName = toDsInfo.getTableName();
						statement = connection.createStatement();
						try {
							String sql = "alter table " + tableName + " drop column " + updField.getFieldName();
							log.info("alter sql={}", sql);
							statement.execute(sql);
						} catch (Exception e) {
							log.error("alter table failed:{}", e.getMessage());
						}
					}
	        	}
        		log.info("finish to ddl:{}", adbDsList.get(i).getDbName());
        	}
        	
        	//如果增删的是引用类型的字段，需要重新生成model version
    		DsFieldInfoExample dsFieldExample= new DsFieldInfoExample();
        	dsFieldExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsUniqueNameEqualTo(dsUniqueName).andFieldUniqueNameEqualTo(fieldName);
        	List<DsFieldInfo> dsFieldInfos = dsFieldInfoMapper.selectByExample(dsFieldExample);
        	if (CollectionUtils.isEmpty(dsFieldInfos)) {
        		throw new QanatBizException("upd field is not found");
        	}
        	DsFieldInfo updField = dsFieldInfos.get(0);
    		for (ViewModelVersionWithBLOBs modelVersion : modelVersions) {
    			boolean isNeedToRestart = true;
            	ViewModel userModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
    			if (modelObjWithDyniamicFieldsMap.get(modelVersion.getViewModelName())) {
    				log.info("object[{}] field[{}] changed will touch off a new model version", dsUniqueName, fieldName);
    				
    				Date now = new Date();
    		    	
    		    	ViewModelVersionWithBLOBs version = new ViewModelVersionWithBLOBs();
    		    	version.setGmtCreate(now);
    		    	version.setGmtModified(now);
    		    	version.setRemark("new version by " + operateType + " " + (isRef != null && isRef == 1?"ref":"normal") + " fields[" + fieldName + "] on object:" + dsUniqueName);
    		    	version.setTenantId(tenantId);
    		    	version.setViewModelId(modelVersion.getViewModelId());
    		    	version.setViewModelName(modelVersion.getViewModelName());
					//来源主表是自定义对象
    		    	if (CollectionUtils.isEmpty(userModel.getObject().getFields())) {
    		    		if (isRef != null && isRef == 1) {
    		    			
    		    			//判断新增引用字段是否引入新的数据源，无新的数据源引入也不需要新增版本
    		            	ViewModel sysModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
    		            	if (sysModel != null && CollectionUtils.isNotEmpty(sysModel.getRelatedObjects())) {
        		            	List<String> relObjDsNames = sysModel.getRelatedObjects().stream().map(e -> e.getRef()).collect(Collectors.toList());
        						JSONObject extInfoJson = JSON.parseObject(updField.getExtInfo());
        						String refDsName = extInfoJson.getString("dsName");
        		            	if (relObjDsNames.contains(refDsName)) {
        		            		log.info("object:{} field:{} reference the dsName:{} exists in model:{} with version:{}", dsUniqueName, fieldName, refDsName, modelVersion.getViewModelName(), modelVersion.getId());
        		            		continue;
        		            	}
    		            	}
    		    			
	        		    	version.setUserYaml(modelVersion.getUserYaml());
	        		    	ViewModel viewModel = viewModelOptimizer.getOptimizedViewModel(tenantId, modelVersion.getUserYaml());
	        		    	version.setSysYaml(YamlUtil.getYaml(viewModel));
    		    		} else {
    		    			continue;
    		    		}
    		    	} else {
    		    		if ("CREATE".equalsIgnoreCase(operateType) || "UPDATE".equalsIgnoreCase(operateType)) {
    		    			if (userModel.getObject().getFields().stream().filter(e->e.getCode().equalsIgnoreCase(updField.getFieldName())).count() == 0) {
	        		    		ViewModel.Field field = new ViewModel.Field();
	        		    		field.setCode(updField.getFieldName());
	        		    		field.setRef(updField.getFieldUniqueName());
	        		    		field.setType(updField.getFieldType());
	        		    		userModel.getObject().getFields().add(field);
	        		    		
		        		    	ViewModel viewModel = viewModelOptimizer.getOptimizedViewModel(tenantId, YamlUtil.getYaml(userModel));
		        		    	//引用字段则产生新版本
		        		    	if (isRef != null && isRef == 1) {
		        		    		isNeedToRestart = true;
		        		    		
			        		    	version.setUserYaml(YamlUtil.getYaml(userModel));
			        		    	version.setSysYaml(YamlUtil.getYaml(viewModel));
		        		    	} else {
		        		    		isNeedToRestart = false;

		        		    		//vm多对象建模的情况下，并且北冥对象作为vm主对象，字段集非空且指定动态 的情况下，增加非引用字段，只更新最新的一个建模版本，无需新建版本
		            		    	ViewModelVersionWithBLOBs record = new ViewModelVersionWithBLOBs();
		            		    	record.setId(modelVersion.getId());
		            		    	record.setUserYaml(YamlUtil.getYaml(userModel));
		            		    	record.setSysYaml(YamlUtil.getYaml(viewModel));
		            		    	record.setGmtModified(now);
		            		    	record.setRemark("[" + now + "] " + operateType + " normal" + " fields[" + fieldName + "] on object:" + dsUniqueName + " \n " + "[" + modelVersion.getGmtCreate() + "] " + modelVersion.getRemark());
		        		    		
		        		    		viewModelVersionMapper.updateByPrimaryKeySelective(record);
		        		    		continue;
		        		    	}
    		    			} else {
    		    				continue;
    		    			}
    		    		} else {
    		    			if (userModel.getObject().getFields().stream().filter(e->e.getCode().equalsIgnoreCase(updField.getFieldName())).count() > 0) {
	        		    		userModel.getObject().getFields().removeIf(e->e.getCode().equalsIgnoreCase(updField.getFieldName()));
	        		    		
		        		    	ViewModel viewModel = viewModelOptimizer.getOptimizedViewModel(tenantId, YamlUtil.getYaml(userModel));
		        		    	
		        		    	if (isRef != null && isRef == 1) {
		        		    		isNeedToRestart = true;
		        		    		
			        		    	version.setUserYaml(YamlUtil.getYaml(userModel));
			        		    	version.setSysYaml(YamlUtil.getYaml(viewModel));
		        		    	} else {
		        		    		isNeedToRestart = false;

		        		    		//vm多对象建模的情况下，并且北冥对象作为vm主对象，字段集非空且指定动态 的情况下，增加非引用字段，只更新最新的一个建模版本，无需新建版本
		            		    	ViewModelVersionWithBLOBs record = new ViewModelVersionWithBLOBs();
		            		    	record.setId(modelVersion.getId());
		            		    	record.setUserYaml(YamlUtil.getYaml(userModel));
		            		    	record.setSysYaml(YamlUtil.getYaml(viewModel));
		            		    	record.setGmtModified(now);
		            		    	record.setRemark("[" + now + "] " + operateType + " normal" + " fields[" + fieldName + "] on object:" + dsUniqueName + " \n " + "[" + modelVersion.getGmtCreate() + "] " + modelVersion.getRemark());
		        		    		
		        		    		viewModelVersionMapper.updateByPrimaryKeySelective(record);
		        		    		continue;
		        		    	}
    		    			} else {
    		    				continue;
    		    			}
    		    		}
    		    	}
    		    	viewModelVersionMapper.insert(version);
    		    	
    		    	ViewModelInfo updRecord = new ViewModelInfo();
    		    	updRecord.setId(modelVersion.getViewModelId());
    		    	updRecord.setVersionId(version.getId());
    		    	viewModelInfoMapper.updateByPrimaryKeySelective(updRecord);
    		    	
    		    	if (!isNeedToRestart) {
    		    		log.info("no need to restart, vm[{},{}] version[{}]", modelVersion.getViewModelId(), modelVersion.getViewModelName(), version.getId());
    		    		ViewModelTaskRelationExample example = new ViewModelTaskRelationExample();
        		    	example.createCriteria().andTenantIdEqualTo(tenantId)
        		    							.andViewModelNameEqualTo(modelVersion.getViewModelName())
        		    							.andModelVersionIdEqualTo(modelVersion.getId())
        		    							.andIsDeletedEqualTo(0L)
        		    							.andRelationTypeEqualTo("main");
        		    	List<ViewModelTaskRelation> vmTaskRels = viewModelTaskRelationMapper.selectByExample(example);
        		    	if (CollectionUtils.isNotEmpty(vmTaskRels)) {
	        		    	ViewModelTaskRelation record = new ViewModelTaskRelation();
	        	            record.setCreateEmpid("mdp_listener");
	        	            record.setGmtCreate(new Date());
	        	            record.setGmtModified(new Date());
	        	            record.setIsDeleted(0L);
	        	            record.setModelVersionId(version.getId());
	        	            record.setModifyEmpid("mdp_listener");
	        	            record.setTaskId(vmTaskRels.get(0).getTaskId());
	        	            record.setTenantId(tenantId);
	        	            record.setViewModelName(modelVersion.getViewModelName());
	        	            record.setRelationType("main");
	        	            viewModelTaskRelationMapper.insert(record);
        		    	}
    		    	}
    			}
    		}
            return true;
        } catch (Exception e) {
            log.error("reflectObjectFieldChange failed", e);
            throw new QanatBizException("reflectObjectFieldChange failed: " + e.getMessage());
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                } finally {
                	statement = null;
                }
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                } finally {
                	connection = null;
                }
            }
        }
    }

	private void addDsFieldInfoAndTableColumn(String tenantId, String dsUniqueName, String fieldName,
			Statement statement, Connection connection, Datasource objDsInfo, String objDsName,
			Map<String, Boolean> modelObjWithAllFieldsMap, Map<String, ViewModelDsRelation> dsModelMap,
			Datasource toDsInfo, String tagJson, String dstDbType) throws SQLException {
		//obj表模型加字段
		dsInfoService.addObjectField(tenantId, objDsInfo.getDbName(), objDsName, objDsInfo.getObjectType(), dsUniqueName, fieldName, tagJson);
		
		DsFieldInfoExample dsFieldExample= new DsFieldInfoExample();
		dsFieldExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsUniqueNameEqualTo(dsUniqueName).andFieldUniqueNameEqualTo(fieldName);
		List<DsFieldInfo> dsFieldInfos = dsFieldInfoMapper.selectByExample(dsFieldExample);
		if (CollectionUtils.isEmpty(dsFieldInfos)) {
			throw new QanatBizException("add field is not found");
		}
		DsFieldInfo newField = dsFieldInfos.get(0);
		
		//adb目标表加字段
		if (dsModelMap.get(toDsInfo.getDsName()) == null
				|| dsModelMap.get(toDsInfo.getDsName()).getViewModelName() == null
				|| modelObjWithAllFieldsMap.get(dsModelMap.get(toDsInfo.getDsName()).getViewModelName()) == null) {
			log.error("ds[{}] has no match model found", toDsInfo.getDsName());
			return;
		}
		if (modelObjWithAllFieldsMap.get(dsModelMap.get(toDsInfo.getDsName()).getViewModelName())) {
			String tableName = toDsInfo.getTableName();
			log.info("tableName={}", tableName);
			statement = connection.createStatement();
			String objFieldType = JSON.parseObject(tagJson).getString("dataType");
			String seperator = (StringUtils.isNotBlank(newField.getExtInfo()) && JSON.parseObject(newField.getExtInfo()) != null) ? JSON.parseObject(newField.getExtInfo()).getString("separator") : ",";

			String testSql = "select " + newField.getFieldName() + " from " + tableName + " limit 1";
			log.info("alter testSql={}", testSql);
			try {
				statement.execute(testSql);
				log.info("field:{} is already in table:{}", newField.getFieldName(), tableName);
				return;
			} catch(Exception e) {

			}
			String sql = null;
			if ("hologres".equalsIgnoreCase(dstDbType) || "postgresql".equalsIgnoreCase(dstDbType)) {
				sql = "alter table " + tableName + " add column " + newField.getFieldName() + " " + ("ENUMS".equalsIgnoreCase(objFieldType) || "multivalue".equalsIgnoreCase(newField.getDataType()) ? "text" : newField.getFieldType());
			} else {
				sql = "alter table " + tableName + " add column " + newField.getFieldName() + " " + ("ENUMS".equalsIgnoreCase(objFieldType) || "multivalue".equalsIgnoreCase(newField.getDataType()) ? "multivalue delimiter_tokenizer '" + seperator + "' value_type '" + newField.getFieldType() + "'" : newField.getFieldType()) + " COMMENT '" + newField.getFieldDesc() + "'";
			}
			log.info("alter sql={}", sql);
			statement.execute(sql);

			try {
				String trySql = null;
				if ("hologres".equalsIgnoreCase(dstDbType) || "postgresql".equalsIgnoreCase(dstDbType)) {
					trySql = "alter table tmp_" + tableName + " add column " + newField.getFieldName() + " " + ("ENUMS".equalsIgnoreCase(objFieldType) || "multivalue".equalsIgnoreCase(newField.getDataType()) ? "text" : newField.getFieldType());
				} else {
					trySql = "alter table tmp_" + tableName + " add column " + newField.getFieldName() + " " + ("ENUMS".equalsIgnoreCase(objFieldType) || "multivalue".equalsIgnoreCase(newField.getDataType()) ? "multivalue delimiter_tokenizer '" + seperator + "' value_type '" + newField.getFieldType() + "'" : newField.getFieldType()) + " COMMENT '" + newField.getFieldDesc() + "'";
				}
				statement.execute(trySql);
			} catch(Exception e) {

			}
		}
	}
    
    public void createTable(String tenantId, ViewModel viewModel, String objectType, String dstDbName, String tableName) {
    	this.createTable(tenantId, viewModel, objectType, dstDbName, tableName, false);
    }
    
    public void createTable(String tenantId, ViewModel viewModel, String objectType, String dstDbName, String tableName, boolean isTmp) {
        log.info("start createTable({},{},{},{},{})", tenantId, JSON.toJSONString(viewModel), objectType, dstDbName, tableName);
        
        RdsConnectionParam param = new RdsConnectionParam();
        JSONObject dbMetaJson = dsInfoService.getDbMetaByName(dstDbName);
        param.setUrl(dbMetaJson.getString("jdbcUrl"))
	        .setUserName(dbMetaJson.getString("username"))
	        .setPassword(dbMetaJson.getString("password"));
        Statement statement = null;
        Connection connection = null;
        try {
            connection = dsHandler.connectToTable(param);
            connection.setAutoCommit(false);
    		
    		String sql = viewModelSqlBuilder.getCreateTableByModel(viewModel, tableName, objectType, dbMetaJson);
            log.info("ddl=[{}]", sql);
            
            statement = connection.createStatement();
            
            if (isTmp) {
            	try {
            		statement.execute("drop table " + tableName);
            	} catch(Exception e) {
            		if (connection != null) {
                    	try {
        					connection.rollback();
        				} catch (SQLException e1) {
        				}
                    }
            	}
            }
            
            log.info("create table ddl:{}", sql);
            try {
            	String[] subSqls = sql.split(";");
            	for (String subSql : subSqls) {
            		log.info("start to exex subSql:{}", subSql);
            		statement.execute(subSql);
            	}
            } catch(Exception e) {
            	log.error("create table failed, e={}", e.getMessage());
            }
            connection.commit();
        } catch (Exception e) {
            log.error("create adb table failed:{}", e.getMessage(), e);
            if (connection != null) {
            	try {
					connection.rollback();
				} catch (SQLException e1) {
				}
            }
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                } finally {
                	statement = null;
                }
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                } finally {
                	connection = null;
                }
            }
        }
    }

	protected String getTmpTableName(String tableName, long versionId) {
		return "tmp_" + tableName + "_" + versionId;
	}
    
    private boolean createFinalTable(String tenantId, Long viewModelId, String objectType, String dstDbName) {
        log.info("start createFinalTable({},{},{},{})", tenantId, viewModelId, objectType, dstDbName);
        
        ViewModelVersionWithBLOBs modelVersion = getLatestModelVersion(tenantId, viewModelId);
        
        RdsConnectionParam param = new RdsConnectionParam();
        JSONObject dbMetaJson = dsInfoService.getDbMetaByName(dstDbName);
        param.setUrl(dbMetaJson.getString("jdbcUrl"))
	        .setUserName(dbMetaJson.getString("username"))
	        .setPassword(dbMetaJson.getString("password"));
        Statement statement = null;
        Connection connection = null;
        try {
            connection = dsHandler.connectToTable(param);
            connection.setAutoCommit(false);
            ViewModel originModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
	    	ViewModel viewModel = null;
	    	if (originModel.isDynamic()) {
	    		viewModel = viewModelOptimizer.getOptimizedViewModel(tenantId, modelVersion.getUserYaml());
	    	} else {
	    		viewModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
	    	}
    		String tableName = viewModel.getCode();
    		
    		String sql = viewModelSqlBuilder.getCreateTableByModel(viewModel, tableName, objectType, dbMetaJson);
            log.info("ddl=[{}]", sql);
            
            statement = connection.createStatement();
            
            log.info("create table ddl:{}", sql);
            try {
            	String[] subSqls = sql.split(";");
            	for (String subSql : subSqls) {
            		log.info("start to exex subSql:{}", subSql);
            		statement.execute(subSql);
            	}
            	connection.commit();
                return true;
            } catch(Exception e) {
            	log.error("create table failed, e={}", e.getMessage());
                if (connection != null) {
                	try {
    					connection.rollback();
    				} catch (SQLException e1) {
    				}
                }
            }
        } catch (Exception e) {
            log.error("create adb table failed", e);
            if (connection != null) {
            	try {
					connection.rollback();
				} catch (SQLException e1) {
				}
            }
            throw new QanatBizException("create adb table failed:" + e.getMessage());
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                } finally {
                	statement = null;
                }
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                } finally {
                	connection = null;
                }
            }
        }
        return false;
    }

	private ViewModelVersionWithBLOBs getLatestModelVersion(String tenantId, Long viewModelId) {
		ViewModelInfoExample example = new ViewModelInfoExample();
		example.createCriteria().andTenantIdEqualTo(tenantId).andIdEqualTo(viewModelId).andIsDeletedEqualTo(0L);
		List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(viewModelInfos)) {
			throw new QanatBizException("tenant check failed");
		}
        ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfos.get(0).getVersionId());
		return modelVersion;
	}
	
    private Boolean syncfullDataForAdbTable(String tenantId, ViewModel dataModel, String dbName, String tableName) {
        log.info("start syncfullDataForAdbTable({},{},{},{})",tenantId, JSON.toJSONString(dataModel), dbName, tableName);
        Statement statement = null;
        Connection connection = null;
        try {
	    	DbInfoExample dbInfoExample = new DbInfoExample();
	    	dbInfoExample.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
	    	List<DbInfo> dbInfos = dbInfoMapper.selectByExample(dbInfoExample);
	    	if (CollectionUtils.isEmpty(dbInfos)) {
	    		throw new QanatBizException("DbInfo not found:" + dbName);
	    	}
            String sql = viewModelSqlBuilder.getLoadDataSql4Adb(tenantId, dataModel, tableName, dbInfos.get(0).getDbType());
            
            RdsConnectionParam param = new RdsConnectionParam();
            JSONObject dbMetaJson = dsInfoService.getDbMetaByName(dbName);
            param.setUrl(dbMetaJson.getString("jdbcUrl"))
    	        .setUserName(dbMetaJson.getString("username"))
    	        .setPassword(dbMetaJson.getString("password"));
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement();
            
    		log.info("before exec sql=[{}]", sql);
            
            statement = connection.createStatement();
            long startTs = System.currentTimeMillis();
            statement.execute(sql);
    		log.info("after exec sql=[{}], cost={}ms", sql, System.currentTimeMillis() - startTs);
            return true;
        } catch (Exception e) {
            log.error("syncfullDataForAdbTable failed", e);
            return false;
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                } finally {
                    statement = null;
                }
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                } finally {
                	connection = null;
                }
            }
        }
    }

    private Long processViewModelDAGTask(String tenantId, String appName, String empid, Long viewModelId, String dbName, String tableName, List<String> jobList, Long versionId, List<String> batchJobs, Settings setting, List<String> extDbNames, ViewModel dataModel, DatatubeInstance datatubeInst) {
    	boolean isDirectWriteObj = "metadata".equalsIgnoreCase(dataModel.getObject().getType()) && dsInfoService.isObjectDirectWriteByMdp(tenantId, dataModel.getObject().getRef());
    	
    	String blinkSqlTemplate = "DataTubeIncrSyncNode incrSync = new DataTubeIncrSyncNode(\"ViewModel_incrSync_%s\", dag);\r\n" + 
        "incrSync.setStreamJobs(\"%s\");\r\n";
    	
    	ViewModelInfoExample vmExample = new ViewModelInfoExample();
		vmExample.createCriteria().andTenantIdEqualTo(tenantId).andIdEqualTo(viewModelId).andIsDeletedEqualTo(0L);
		List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(vmExample);
		if (CollectionUtils.isEmpty(viewModelInfos)) {
			throw new QanatBizException("tenant check failed");
		}
		ViewModelInfo viewModelInfo = viewModelInfos.get(0);
        
        StringBuffer blinkPart = new StringBuffer();
        blinkPart.append(String.format(blinkSqlTemplate, tableName + "_v" + versionId, StringUtils.join(jobList, ",")));
        
    	DatasourceExample example = new DatasourceExample();
    	example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName).andTableNameEqualTo(tableName);
    	Datasource dstDsInfo = datasourceMapper.selectByExample(example).get(0);
    	
		DsFieldInfoExample dfiExample = new DsFieldInfoExample();
		dfiExample.createCriteria().andTenantIdEqualTo(tenantId).andDsNameEqualTo(dstDsInfo.getDsName()).andIsDeletedEqualTo(0L);
    	List<DsFieldInfo> dsFields = dsFieldInfoMapper.selectByExample(dfiExample);
    	Map<String, String> colNameTypeMap = new HashMap<>();
    	String pkColumn = "id";
    	for (DsFieldInfo dsField : dsFields) {
    		colNameTypeMap.put(dsField.getFieldName(), dsField.getFieldType());
    		if (dsField.getIsPk().intValue() == 1) {
    			pkColumn = dsField.getFieldName();
    		}
    	}

    	JSONObject srcDbMetaJson = dsInfoService.getDbMetaByName(dbName);
    	
    	//兼容逻辑，按新数据源命名规则找补数据源配置
    	DatasourceExample dsInfoExample = new DatasourceExample();
    	dsInfoExample.createCriteria().andIsDeletedEqualTo(0L)
								    	.andTenantIdEqualTo(tenantId)
								    	.andDsNameEqualTo(dsInfoService.getDsName(tenantId, appName, dbName, tableName));
    	List<Datasource> dsInfos = datasourceMapper.selectByExample(dsInfoExample);
    	if (CollectionUtils.isEmpty(dsInfos)) {
	    	DatasourceRequest dsInfoReq = new DatasourceRequest();
	    	dsInfoReq.setTenantId(tenantId);
	    	dsInfoReq.setDbName(dbName);
	    	dsInfoReq.setDsName(dsInfoService.getDsName(tenantId, appName, dbName, tableName));
	    	dsInfoReq.setDsType(srcDbMetaJson.getString("dbType"));
	    	dsInfoReq.setTableName(tableName);
	    	dsInfoReq.setOperateEmpid(empid);
	    	dsInfoReq.setPkField(pkColumn);
	    	dsInfoService.createDatasource(dsInfoReq);
	    	log.info("{} has been created", dsInfoReq.getDsName());
    	} else {
    		DatasourceRequest dsInfoReq = new DatasourceRequest();
            dsInfoReq.setTenantId(tenantId);
            dsInfoReq.setDsName(dsInfoService.getDsName(tenantId, appName, dbName, tableName));
            dsInfoReq.setOperateEmpid(empid);
    		dsInfoService.modifyDatasource(dsInfoReq);
    	}

        List<String> extDbSyncScript = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(extDbNames)) {
        	
        	Long predictSize = null;
        	if ("metadata".equalsIgnoreCase(dataModel.getObject().getType())) {
            	example = new DatasourceExample();
            	example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andTableNameEqualTo(dataModel.getObject().getRef()).andDsTypeEqualTo("obj");
            	Datasource srcDsInfo = datasourceMapper.selectByExample(example).get(0);
            	predictSize = srcDsInfo.getSize() == null ? srcDsInfo.getPredictSize() : srcDsInfo.getSize();
        	} else if ("table".equalsIgnoreCase(dataModel.getObject().getType())) {
            	example = new DatasourceExample();
            	example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsNameEqualTo(dataModel.getObject().getRef());
            	Datasource srcDsInfo = datasourceMapper.selectByExample(example).get(0);
            	predictSize = srcDsInfo.getSize() == null ? srcDsInfo.getPredictSize() : srcDsInfo.getSize();
        	}
        	
        	int batchSize = 102400;
        	int parallelism = 10;
        	if (predictSize == null || predictSize > 500 * 10000) {
        		batchSize = 1000000;
        		parallelism = 20;
        	}
    		for (int i = 0; i < extDbNames.size(); i++) {
            	String dstDbName = extDbNames.get(i);
            	
            	JSONObject dstDbMetaJson = dsInfoService.getDbMetaByName(dstDbName);
            	
            	dsInfoExample = new DatasourceExample();
            	dsInfoExample.createCriteria().andIsDeletedEqualTo(0L)
        								    	.andTenantIdEqualTo(tenantId)
        								    	.andTableNameEqualTo(tableName)
        								    	.andDbNameEqualTo(dstDbName);
            	dsInfos = datasourceMapper.selectByExample(dsInfoExample);
            	if (CollectionUtils.isEmpty(dsInfos)) {
        	    	DatasourceRequest dsInfoReq = new DatasourceRequest();
        	    	dsInfoReq.setTenantId(tenantId);
        	    	dsInfoReq.setDbName(dstDbName);
        	    	dsInfoReq.setDsName(dsInfoService.getDsName(tenantId, appName, dstDbName, tableName));
        	    	dsInfoReq.setDsType(dstDbMetaJson.getString("dbType"));
        	    	dsInfoReq.setTableName(tableName);
        	    	dsInfoReq.setOperateEmpid(empid);
        	    	dsInfoReq.setPkField(pkColumn);
        	    	DataResult<Long> createDsInfoResult = dsInfoService.createDatasource(dsInfoReq);
        	    	log.info("createDsInfoResult={}", JSON.toJSONString(createDsInfoResult));
            	}
            	ViewModelDsRelation dsRel = new ViewModelDsRelation();
    			dsRel.setCreateEmpid(empid);
    			dsRel.setDsName(dsInfoService.getDsName(tenantId, appName, dstDbName, tableName));
    			dsRel.setGmtCreate(new Date());
    			dsRel.setGmtModified(new Date());
    			dsRel.setIsDeleted(0L);
    			dsRel.setModifyEmpid(empid);
    			dsRel.setRelationType("to_ds");
    			dsRel.setTenantId(tenantId);
    			dsRel.setViewModelName(viewModelInfo.getModelName());
    			try {
    				viewModelDsRelationMapper.insert(dsRel);
    			} catch(Exception e) {}
    		}
        	
//        	if (extDbNames.size() > 1) {
        	    String tmpTableName = "tmp_" + tableName;
        	    String bakTableName = "bak_" + tableName;
        		String jobName = odsHandler.generateMultiDbSinkBatchTask(tenantId, appName, dsInfoService.getDsName(tenantId, appName, dbName, tableName), extDbNames, tmpTableName, batchSize, parallelism, dataModel.getSettings().getMultiDbSyncFilter(), dataModel.getObject().getFields().stream().filter(e -> e.isPk()).collect(Collectors.toList()).get(0).getCode());
            	String script = "MultiDbSinkNode multiDbSinkNode = new MultiDbSinkNode(\"MultiDbSink_" + tableName + "_" + versionId + "\", dag);\r\n" + 
    					"multiDbSinkNode.setDatatubeInstId(" + datatubeInst.getId() + ");\r\n" +
    					"multiDbSinkNode.setSrcDbName(\"" + dbName + "\");\r\n" +
    					"multiDbSinkNode.setDstDbNames(\"" + StringUtils.join(extDbNames, ",") + "\");\r\n" +
    					"multiDbSinkNode.setTableName(\"" + tableName + "\");\r\n" +
    					"multiDbSinkNode.setTmpTableName(\"" + tmpTableName + "\");\r\n" +
    					"multiDbSinkNode.setBakTableName(\"" + bakTableName + "\");\r\n" +
    					"multiDbSinkNode.setJobName(\"" + jobName + "\");\r\n" +
            			"\r\n" +
            			"fullSync.setNext(multiDbSinkNode);\r\n" +
                		"multiDbSinkNode.setNext(incrSync);\r\n" +
            			"\r\n";
    			extDbSyncScript.add(script);
//        	} else {
//        		String tmpTableName = "tmp_" + tableName;
//            	String dstDbName = extDbNames.get(0);
//        		createTable(tenantId, dataModel, viewModelInfo.getObjectType(), dstDbName, tableName);
//        		createTable(tenantId, dataModel, viewModelInfo.getObjectType(), dstDbName, tmpTableName, true);
//        		
//        		String jobName = odsHandler.generateBatchTask(tenantId, appName, odsHandler.getDsName(tenantId, appName, dbName, tableName), dstDbName, tmpTableName, batchSize, parallelism, dataModel.getSettings().getMultiDbSyncFilter());
//            	String script = "BlinkBatchNode extDbSyncNode = new BlinkBatchNode(\"ExtDbSync_" + dstDbName + "_" + tableName + "_" + versionId + "\", dag);\r\n" + 
//    					"extDbSyncNode.setJobName(\"" + jobName + "\");\r\n" +
//            			"\r\n" +
//                		"fullSync.setNext(extDbSyncNode);\r\n" +
//            			"\r\n" +
//                		"Adb3MultiSqlNode renameNode = new Adb3MultiSqlNode(\"AdbSwitch_" + dstDbName + "_" + tableName + "_" + versionId + "\", dag);\r\n" + 
//                		"renameNode.setDbName(\"" + dstDbName + "\");\r\n" + 
//                		"renameNode.setSql(\"alter table " + tableName + " rename to bak_" + tableName + ";alter table tmp_" + tableName + " rename to " + tableName + ";alter table bak_" + tableName + " rename to tmp_" + tableName + "\");" +
//                		"\r\n" + 
//                		"extDbSyncNode.setNext(renameNode);\r\n" +
//        				"renameNode.setNext(incrSync);\r\n" +
//                		"\r\n";
//    			extDbSyncScript.add(script);
//        	}
        }
        
        String dagScript = "";
        if (isDirectWriteObj) {
    		String objDsName = dsInfoService.getDsNameByObjectCode(tenantId, dataModel.getObject().getRef());
    		dsInfoService.modifyDsInfoMdpSlot(tenantId, objDsName, getEtlDbName(tenantId));
    	} 
		dagScript =	String.format(VIEW_MODEL_DAG_SCRIPT
        , tableName + "_v" + versionId
        , ("daily_rebuild".equalsIgnoreCase(setting.getGuaranteePolicy()) && StringUtils.isNotBlank(setting.getTimeExpression()) && !isDirectWriteObj) ? ("dag.setTimeExpression(\"" + setting.getTimeExpression() + "\");") : ""
        , tableName + "_v" + versionId
        , viewModelId
        , StringUtils.join(batchJobs, ",")
        , blinkPart.toString()
        , (CollectionUtils.isNotEmpty(extDbSyncScript) ? StringUtils.join(extDbSyncScript, "\r\n") : "fullSync.setNext(incrSync);\r\n"));
        
        String taskName = "DAG_" + tableName + "_v" + versionId;
        Long taskId = taskService.isTaskExists(tenantId, taskName);
        if (taskId != null) {
        	taskService.updateTaskDag(taskId, empid, dagScript, false);
            log.info("[{}]update qanat viewModel task[{}] finished", viewModelId, taskId);
        } else {
	        TaskInfoRequest taskInfo = new TaskInfoRequest();
	        taskInfo.setDagScript(dagScript);
	        taskInfo.setName(taskName);
	        taskInfo.setOperateEmpid(empid);
	        taskInfo.setTaskDesc(taskName);
	        taskInfo.setPolicy(DagPolicy.ETL.toString());
	        taskInfo.setTenantId(tenantId);
	        taskInfo.setAppName(appName);
	        taskId = taskService.createDAGTask(taskInfo);
	        log.info("[{}]create qanat viewModel task[{}] finished", viewModelId,  taskId);
        }
        return taskId;
    }
    
    public void rebuildMultiDbSyncJob(Long datatubeInstId) {
    	DatatubeInstance datatube = datatubeInstanceMapper.selectByPrimaryKey(datatubeInstId);
        if (datatube == null) {
        	log.error("datatube:{} get instance failed", datatubeInstId);
        	throw new QanatBizException("datatube:" + datatubeInstId + " get instance failed");
        }
        
    	TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(datatube.getTenantId());
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + datatube.getTenantId() + " is not configured");
    	}
    	TenantInfo tenantInfo = tenantList.get(0);
    	
		ViewModelInfo viewModelInfo = viewModelInfoMapper.selectByPrimaryKey(datatube.getProviderId());
        if (viewModelInfo == null) {
        	log.error("datatube:{} get viewmodel failed", datatubeInstId);
        	throw new QanatBizException("datatube:" + datatubeInstId + " get viewmodel failed");
        }
		ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());
        if (modelVersion == null) {
        	log.error("datatube:{} get viewmodelVersion failed", datatubeInstId);
        	throw new QanatBizException("datatube:" + datatubeInstId + " get viewmodelVersion failed");
        }

        ViewModel originModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
        if (originModel == null) {
        	log.error("datatube:{} get yaml failed", datatubeInstId);
        	throw new QanatBizException("datatube:" + datatubeInstId + " get yaml failed");
        }
    	ViewModel sysModel = null;
    	if (originModel.isDynamic()) {
    		sysModel = viewModelOptimizer.getOptimizedViewModel(datatube.getTenantId(), modelVersion.getUserYaml());
    	} else {
    		sysModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
    	}
    	ViewModel dataModel = new ViewModel();
    	BeanUtils.copyProperties(sysModel, dataModel);
    	
        String tableName = dataModel.getCode();
    	
    	String etlDbName = getEtlDbName(tenantInfo);
    	List<String> extDbNames = getExtDbNames(tenantInfo, datatube);
        if (CollectionUtils.isNotEmpty(extDbNames)) {
        	Long predictSize = null;
        	if ("metadata".equalsIgnoreCase(dataModel.getObject().getType())) {
        		DatasourceExample example = new DatasourceExample();
            	example.createCriteria().andTenantIdEqualTo(datatube.getTenantId()).andIsDeletedEqualTo(0L).andTableNameEqualTo(dataModel.getObject().getRef()).andDsTypeEqualTo("obj");
            	Datasource srcDsInfo = datasourceMapper.selectByExample(example).get(0);
            	predictSize = srcDsInfo.getSize() == null ? srcDsInfo.getPredictSize() : srcDsInfo.getSize();
        	} else if ("table".equalsIgnoreCase(dataModel.getObject().getType())) {
        		DatasourceExample example = new DatasourceExample();
            	example.createCriteria().andTenantIdEqualTo(datatube.getTenantId()).andIsDeletedEqualTo(0L).andDsNameEqualTo(dataModel.getObject().getRef());
            	Datasource srcDsInfo = datasourceMapper.selectByExample(example).get(0);
            	predictSize = srcDsInfo.getSize() == null ? srcDsInfo.getPredictSize() : srcDsInfo.getSize();
        	}
        	
        	int batchSize = 102400;
        	int parallelism = 10;
        	if (predictSize == null || predictSize > 500 * 10000) {
        		batchSize = 1000000;
        		parallelism = 20;
        	}
        	
        	boolean isNewMultiDbSyncMode = false;
        	ViewModelTaskRelationExample modelTaskRelExample = new ViewModelTaskRelationExample();
        	modelTaskRelExample.createCriteria().andTenantIdEqualTo(datatube.getTenantId()).andIsDeletedEqualTo(0L).andModelVersionIdEqualTo(viewModelInfo.getVersionId()).andRelationTypeEqualTo("main");
        	List<ViewModelTaskRelation> modelTaskRels = viewModelTaskRelationMapper.selectByExample(modelTaskRelExample);
        	if (CollectionUtils.isNotEmpty(modelTaskRels)) {
            	Long taskId = modelTaskRels.get(0).getTaskId();
            	TaskInfoWithBLOBs taskInfo = taskInfoMapper.selectByPrimaryKey(taskId);
            	if (StringUtils.isNotBlank(taskInfo.getDag())) {
    	        	JSONObject dagJson = JSON.parseObject(taskInfo.getDag());
    	        	if (dagJson.getJSONArray("nodeList") != null && dagJson.getJSONArray("nodeList").size() > 0) {
    		        	for (int i = 0; i < dagJson.getJSONArray("nodeList").size(); i++) {
    		        		JSONObject nodeJson = dagJson.getJSONArray("nodeList").getJSONObject(i);
    		        		if ("com.aliyun.wormhole.qanat.job.QanatMultiDbSinkJobProcessor".equalsIgnoreCase(nodeJson.getString("action"))) {
    		        			isNewMultiDbSyncMode = true;
    		        			break;
    		        		}
    		        	}
    	        	}
            	}
        	}
        	log.info("datatube:{} isNewMultiDbSyncMode:{}", datatubeInstId, isNewMultiDbSyncMode);

    	    String tmpTableName = "tmp_" + tableName;
        	if (isNewMultiDbSyncMode) {
        		odsHandler.generateMultiDbSinkBatchTask(datatube.getTenantId(), datatube.getAppName(), dsInfoService.getDsName(datatube.getTenantId(), datatube.getAppName(), etlDbName, tableName), extDbNames, tmpTableName, batchSize, parallelism, dataModel.getSettings().getMultiDbSyncFilter(), dataModel.getObject().getFields().stream().filter(e -> e.isPk()).collect(Collectors.toList()).get(0).getCode());
        	} else {
        		odsHandler.generateBatchTask(datatube.getTenantId(), datatube.getAppName(), dsInfoService.getDsName(datatube.getTenantId(), datatube.getAppName(), etlDbName, tableName), extDbNames.get(0), tmpTableName, batchSize, parallelism, dataModel.getSettings().getMultiDbSyncFilter());
        	}
        }
    }
    
    protected Long getAppIdByName(String tenantId, String appName) {
    	AppInfoExample example = new AppInfoExample();
    	example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
    	List<AppInfo> apps = appInfoMapper.selectByExample(example);
    	return apps.get(0).getId();
    }
    
    public static String VIEW_MODEL_DAG_SCRIPT = "Dag dag = new Dag(\"DAG_%s\");\r\n" + 
            "%s\r\n" + 
            "DataTubeFullSyncNode fullSync = new DataTubeFullSyncNode(\"ViewModel_fullSync_%s\", dag);\r\n" + 
            "fullSync.setModelId(%s);\r\n" + 
            "fullSync.setBatchJobs(\"%s\");\r\n" + 
            "fullSync.setDataBaseline(true);\r\n" + 
            "%s\r\n" +
            "%s\r\n" +
            "return dag;";
    
    public static String VIEW_MODEL_DAG_SCRIPT_FOR_DIRECTWRITE = "Dag dag = new Dag(\"DAG_%s\");\r\n" + 
            "%s\r\n" + 
            "%s\r\n" +
            "%s\r\n" +
            "return dag;";
    
    public static void main(String [] args) {
    }
}