package com.aliyun.wormhole.qanat.job;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.dag.DataTubeIncrSyncNode;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * ViewModel增量同步执行任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatViewModelIncrSyncJobProcessor extends AbstractQanatNodeJobProcessor<DataTubeIncrSyncNode> {
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Override
    void doProcess(Map<String, Object> instParamsMap, DataTubeIncrSyncNode node) {
        Long taskInstId = Long.valueOf(String.valueOf(instParamsMap.get("taskInstId")));
        String tenantId = String.valueOf(instParamsMap.get("tenantId"));
        String appName = String.valueOf(instParamsMap.get("appName"));
        
        final Date startTime = getStartTime(instParamsMap, taskInstId);
        String[] jobs = node.getStreamJobs().split(",");
        
        for (String jobName : jobs) {
        	new Thread(() -> {
		        blinkService.stopJob(tenantId, appName, jobName);
		        log.info("blink job[{}] has been stopped", jobName);
		        blinkService.startJob(tenantId, appName, jobName, startTime);
		        log.info("blink job[{}] has been started", jobName);
        	}).start();
        }
    }

	private Date getStartTime(Map<String, Object> instParamsMap, Long taskInstId) {
		Date startTime = new Date();
        String incrSyncStartTime = "";
        if (instParamsMap.get("taskInstId") != null) {
            TaskInstance taskInst = taskInstanceMapper.selectByPrimaryKey(taskInstId);
            JSONObject execParam = JSON.parseObject(taskInst.getExecParam());
            incrSyncStartTime = execParam.getString("incr_sync_start_time");
        }
        if (StringUtils.isNotBlank((String)instParamsMap.get("incr_sync_start_time"))) {//JOB参数如果设置优先级更高
            incrSyncStartTime = (String)instParamsMap.get("startConsumeTime");
        }
        if (StringUtils.isNotBlank(incrSyncStartTime)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try {
				startTime = sdf.parse(incrSyncStartTime);
			} catch (ParseException e) {
				e.printStackTrace();
			}
        }
		return startTime;
	}
}