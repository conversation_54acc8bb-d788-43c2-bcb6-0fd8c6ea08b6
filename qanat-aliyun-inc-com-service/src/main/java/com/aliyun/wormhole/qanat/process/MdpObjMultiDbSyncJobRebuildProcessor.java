package com.aliyun.wormhole.qanat.process;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelHandler;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * MDP对象多库同步任务重建
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class MdpObjMultiDbSyncJobRebuildProcessor extends JavaProcessor {

	@Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource 
    private DatatubeInstanceMapper datatubeInstanceMapper;
    
    @Resource
    private ViewModelHandler viewModelHandler;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            log.info("MdpObjMultiDbSyncJobRebuildProcessor, param={}", context.getJobParameters());
            JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
            String tenantId = paramsJson.getString("tenantId");
            String datatubeInstIds = paramsJson.getString("datatubeInstIds");
            DatatubeInstanceExample example = new DatatubeInstanceExample();
            DatatubeInstanceExample.Criteria criteria = example.createCriteria();
            criteria.andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andIsTestEqualTo(0L).andProviderEqualTo("viewmodel");
            if (StringUtils.isNotBlank(datatubeInstIds)) {
            	List<Long> datatubeInstIdList = new ArrayList<>();
            	for (String datatubeInstId : datatubeInstIds.split(",")) {
            		datatubeInstIdList.add(Long.valueOf(datatubeInstId));
            	}
            	criteria.andIdIn(datatubeInstIdList);
            } else {
            	criteria.andTypeEqualTo("object");
            }
            List<DatatubeInstance> datatubeInstList = datatubeInstanceMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(datatubeInstList)) {
            	return new ProcessResult(false, "no datatube instances found");
            }
            
        	for (DatatubeInstance inst : datatubeInstList) {
        		log.info("start to rebuild inst:{}", inst.getCode());
        		try {
        			viewModelHandler.rebuildMultiDbSyncJob(inst.getId());
            		log.info("finish to rebuild inst:{}", inst.getCode());
        		} catch(Exception e) {
            		log.error("failed to rebuild inst:{}", inst.getCode());
        		}
        	}
        } catch (QanatBizException e) {
            log.error("MdpObjMultiDbSyncJobRebuildProcessor任务调度异常:{}", e.getMessage());
            return new ProcessResult(false, e.getMessage());
        } catch (Exception e) {
            log.error("MdpObjMultiDbSyncJobRebuildProcessor任务调度异常", e);
            return new ProcessResult(false, e.getMessage());
        }
        return new ProcessResult(true);
    }
    
    @Override
    public void kill(JobContext context) {
        
    }
}