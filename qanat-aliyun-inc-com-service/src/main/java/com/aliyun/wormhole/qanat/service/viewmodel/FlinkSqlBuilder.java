package com.aliyun.wormhole.qanat.service.viewmodel;

import com.alibaba.fastjson.JSON;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.service.template.FlinkSyncTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Flink SQL生成器
 * 基于TDD思路实现，组合复用ViewModelSqlBuilder的核心逻辑
 */
@Slf4j
@Component
public class FlinkSqlBuilder {
    
    @Resource
    private ViewModelSqlBuilder viewModelSqlBuilder; // 组合复用现有逻辑
    
    @Resource
    private DatasourceMapper datasourceMapper;
    
    @Resource
    private FlinkSyncTemplate flinkSyncTemplate;
    
    /**
     * 生成完整的Flink作业SQL
     */
    public String generateFlinkJobSql(String tenantId, ViewModel dataModel, String jobName) {
        log.info("FlinkSqlBuilder.generateFlinkJobSql start, tenantId={}, modelCode={}, jobName={}", 
                tenantId, dataModel.getCode(), jobName);
        
        try {
            StringBuilder sqlBuilder = new StringBuilder();
            
            // 1. 添加SQL头部注释
            sqlBuilder.append(generateSqlHeader(dataModel, jobName));
            
            // 2. 创建UDF函数定义
            sqlBuilder.append(generateUdfDefinitions());
            
            // 3. 创建源表定义（Hologres binlog）
            sqlBuilder.append(generateSourceTableDefinitions(tenantId, dataModel));
            
            // 4. 创建目标表定义（Hologres sink）
            sqlBuilder.append(generateSinkTableDefinition(tenantId, dataModel));
            
            // 5. 创建维表定义（如果有关联对象）
            sqlBuilder.append(generateDimTableDefinitions(tenantId, dataModel));
            
            // 6. 生成主要的INSERT语句
            sqlBuilder.append(generateMainInsertStatement(tenantId, dataModel));
            
            String finalSql = sqlBuilder.toString();
            log.info("FlinkSqlBuilder.generateFlinkJobSql success, sql length: {}", finalSql.length());
            
            return finalSql;
            
        } catch (Exception e) {
            log.error("FlinkSqlBuilder.generateFlinkJobSql failed", e);
            throw new QanatBizException("生成Flink作业SQL失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成Flink CREATE TABLE语句（源表）
     */
    public String generateFlinkSourceTableSql(String tenantId, ViewModel.DataObject dataObject) {
        try {
            // 获取数据源信息
            Datasource datasource = getDatasource(tenantId, dataObject.getRef());
            
            // 根据对象类型生成不同的源表定义
            if ("metadata".equalsIgnoreCase(dataObject.getType())) {
                return generateHologresBinlogSourceSql(datasource, dataObject);
            } else if ("table".equalsIgnoreCase(dataObject.getType())) {
                return generateHologresTableSourceSql(datasource, dataObject);
            } else {
                // 其他数据源类型的处理
                return generateGenericSourceSql(datasource, dataObject);
            }
            
        } catch (Exception e) {
            log.error("generateFlinkSourceTableSql failed", e);
            throw new QanatBizException("生成Flink源表SQL失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成Flink INSERT语句
     */
    public String generateFlinkInsertSql(String tenantId, ViewModel dataModel) {
        try {
            // 复用现有ViewModelSqlBuilder的核心逻辑
            String selectSql = viewModelSqlBuilder.getSelectSql(tenantId, dataModel, "hologres");
            
            // 适配Flink语法
            String adaptedSelectSql = adaptToFlinkSyntax(selectSql);
            
            // 生成完整的INSERT语句
            StringBuilder insertSql = new StringBuilder();
            insertSql.append("INSERT INTO ").append(dataModel.getCode()).append("\n");
            insertSql.append(adaptedSelectSql);
            
            return insertSql.toString();
            
        } catch (Exception e) {
            log.error("generateFlinkInsertSql failed", e);
            throw new QanatBizException("生成Flink INSERT SQL失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成Flink流作业SQL
     */
    public String generateFlinkStreamJobSql(String tenantId, Object dataObject, String jobName, 
                                           String topicName, boolean isBatch) {
        try {
            StringBuilder sqlBuilder = new StringBuilder();
            
            // 1. 添加SQL头部注释
            sqlBuilder.append(generateSqlHeader(dataObject, jobName));
            
            // 2. 添加UDF函数定义
            sqlBuilder.append(generateUdfDefinitions());
            
            // 3. 根据对象类型生成不同的源表定义
            if (dataObject instanceof ViewModel.DataObject) {
                ViewModel.DataObject obj = (ViewModel.DataObject) dataObject;
                sqlBuilder.append(generateSourceTableForDataObject(tenantId, obj, topicName, isBatch));
            } else if (dataObject instanceof ViewModel.RelatedDataObject) {
                ViewModel.RelatedDataObject relObj = (ViewModel.RelatedDataObject) dataObject;
                sqlBuilder.append(generateSourceTableForRelatedObject(tenantId, relObj, topicName, isBatch));
            }
            
            // 4. 生成目标表定义
            sqlBuilder.append(generateSinkTableForObject(tenantId, dataObject));
            
            // 5. 生成INSERT语句
            sqlBuilder.append(generateInsertStatementForObject(tenantId, dataObject));
            
            return sqlBuilder.toString();
            
        } catch (Exception e) {
            log.error("generateFlinkStreamJobSql failed", e);
            throw new QanatBizException("生成Flink流作业SQL失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成Flink批量作业SQL
     */
    public String generateFlinkBatchJobSql(String tenantId, Object dataObject, String jobName, String topicName) {
        try {
            StringBuilder sqlBuilder = new StringBuilder();
            
            // 1. 添加SQL头部注释
            sqlBuilder.append(generateSqlHeader(dataObject, jobName));
            
            // 2. 添加批量作业配置
            sqlBuilder.append(generateBatchJobConfig());
            
            // 3. 生成批量源表定义
            sqlBuilder.append(generateBatchSourceTable(tenantId, dataObject, topicName));
            
            // 4. 生成目标表定义
            sqlBuilder.append(generateSinkTableForObject(tenantId, dataObject));
            
            // 5. 生成批量INSERT语句
            sqlBuilder.append(generateBatchInsertStatement(tenantId, dataObject));
            
            return sqlBuilder.toString();
            
        } catch (Exception e) {
            log.error("generateFlinkBatchJobSql failed", e);
            throw new QanatBizException("生成Flink批量作业SQL失败: " + e.getMessage());
        }
    }
    
    // ==================== 私有方法实现 ====================
    
    /**
     * 生成Hologres binlog源表定义
     */
    private String generateHologresBinlogSourceSql(Datasource datasource, ViewModel.DataObject dataObject) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", "source_" + dataObject.getCode());
        params.put("physicalTableName", datasource.getTableName());
        params.put("jdbcUrl", getHologresJdbcUrl(datasource));
        params.put("username", getHologresUsername(datasource));
        params.put("password", getHologresPassword(datasource));
        params.put("endpoint", getHologresEndpoint(datasource));
        
        // 生成字段定义
        List<String> fieldDefinitions = generateFieldDefinitions(dataObject.getFields());
        params.put("fieldDefinitions", String.join(",\n    ", fieldDefinitions));
        
        // 生成主键定义
        List<String> primaryKeys = dataObject.getFields().stream()
                .filter(ViewModel.Field::isPk)
                .map(ViewModel.Field::getCode)
                .collect(Collectors.toList());
        params.put("primaryKeys", String.join(", ", primaryKeys));
        
        return flinkSyncTemplate.formatTemplate(FlinkSyncTemplate.HOLO_BINLOG_SOURCE, params);
    }
    
    /**
     * 生成Hologres表源表定义
     */
    private String generateHologresTableSourceSql(Datasource datasource, ViewModel.DataObject dataObject) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", "source_" + dataObject.getCode());
        params.put("physicalTableName", datasource.getTableName());
        params.put("jdbcUrl", getHologresJdbcUrl(datasource));
        params.put("username", getHologresUsername(datasource));
        params.put("password", getHologresPassword(datasource));
        params.put("endpoint", getHologresEndpoint(datasource));
        
        // 生成字段定义
        List<String> fieldDefinitions = generateFieldDefinitions(dataObject.getFields());
        params.put("fieldDefinitions", String.join(",\n    ", fieldDefinitions));
        
        // 生成主键定义
        List<String> primaryKeys = dataObject.getFields().stream()
                .filter(ViewModel.Field::isPk)
                .map(ViewModel.Field::getCode)
                .collect(Collectors.toList());
        params.put("primaryKeys", String.join(", ", primaryKeys));
        
        return flinkSyncTemplate.formatTemplate(FlinkSyncTemplate.HOLO_SINK_TABLE, params);
    }
    
    /**
     * 适配Flink SQL语法
     */
    private String adaptToFlinkSyntax(String originalSql) {
        String adaptedSql = originalSql;
        
        // 1. 处理时间函数
        adaptedSql = adaptedSql.replaceAll("DATE_FORMAT\\(([^,]+),'%Y-%m-%d %H:%i:%s'\\)", 
                                          "DATE_FORMAT($1, 'yyyy-MM-dd HH:mm:ss')");
        
        // 2. 处理字段转义（Hologres使用双引号）
        // ViewModelSqlBuilder已经处理了，因为它已经支持hologres类型
        
        // 3. 处理Flink特有的语法
        adaptedSql = adaptedSql.replaceAll("DISTINCT\\s+", ""); // Flink流计算中去掉DISTINCT
        
        // 4. 添加处理时间字段
        if (!adaptedSql.contains("PROCTIME()")) {
            adaptedSql = adaptedSql.replaceFirst("SELECT", "SELECT PROCTIME() as proc_time,");
        }
        
        return adaptedSql;
    }
    
    /**
     * 映射到Flink数据类型
     */
    private String mapToFlinkType(String originalType) {
        switch (originalType.toLowerCase()) {
            case "varchar":
            case "string":
                return "STRING";
            case "int":
            case "integer":
                return "INT";
            case "bigint":
            case "long":
                return "BIGINT";
            case "decimal":
                return "DECIMAL(18,2)";
            case "datetime":
            case "timestamp":
                return "TIMESTAMP(3)";
            case "date":
                return "DATE";
            case "boolean":
                return "BOOLEAN";
            case "double":
                return "DOUBLE";
            case "float":
                return "FLOAT";
            default:
                return "STRING"; // 默认类型
        }
    }

    /**
     * 生成字段定义
     */
    private List<String> generateFieldDefinitions(List<ViewModel.Field> fields) {
        return fields.stream()
                .map(field -> {
                    String flinkType = mapToFlinkType(field.getType());
                    return String.format("\"%s\" %s", field.getCode(), flinkType);
                })
                .collect(Collectors.toList());
    }

    /**
     * 生成SQL头部注释
     */
    private String generateSqlHeader(Object dataModel, String jobName) {
        String modelCode = "";
        String modelName = "";

        if (dataModel instanceof ViewModel) {
            ViewModel vm = (ViewModel) dataModel;
            modelCode = vm.getCode();
            modelName = vm.getName();
        } else if (dataModel instanceof ViewModel.DataObject) {
            ViewModel.DataObject obj = (ViewModel.DataObject) dataModel;
            modelCode = obj.getCode();
            modelName = obj.getCode();
        }

        return String.format(
            "--SQL\n" +
            "--********************************************************************--\n" +
            "--Author: Datatube Flink Generator\n" +
            "--CreateTime: %s\n" +
            "--JobName: %s\n" +
            "--ModelCode: %s\n" +
            "--ModelName: %s\n" +
            "--Architecture: Flink + Hologres\n" +
            "--********************************************************************--\n\n",
            new Date().toString(), jobName, modelCode, modelName
        );
    }

    /**
     * 生成UDF函数定义
     */
    private String generateUdfDefinitions() {
        StringBuilder udfSql = new StringBuilder();

        // 添加常用的UDF函数
        udfSql.append("-- UDF函数定义\n");
        udfSql.append("CREATE TEMPORARY FUNCTION IF NOT EXISTS qanatConcat AS 'com.aliyun.wormhole.qanat.flink.udf.QanatConcatUdf';\n");
        udfSql.append("CREATE TEMPORARY FUNCTION IF NOT EXISTS qanatNvl AS 'com.aliyun.wormhole.qanat.flink.udf.QanatNvlUdf';\n");
        udfSql.append("CREATE TEMPORARY FUNCTION IF NOT EXISTS qanatDateFormat AS 'com.aliyun.wormhole.qanat.flink.udf.QanatDateFormatUdf';\n\n");

        return udfSql.toString();
    }

    /**
     * 生成批量作业配置
     */
    private String generateBatchJobConfig() {
        StringBuilder configSql = new StringBuilder();

        configSql.append("-- Flink批量作业配置\n");
        configSql.append("SET 'execution.runtime-mode' = 'BATCH';\n");
        configSql.append("SET 'execution.checkpointing.interval' = '300s';\n");
        configSql.append("SET 'table.exec.resource.default-parallelism' = '4';\n\n");

        return configSql.toString();
    }

    // ==================== 数据源信息获取方法 ====================

    private Datasource getDatasource(String tenantId, String dsName) {
        DatasourceExample example = new DatasourceExample();
        example.createCriteria()
               .andTenantIdEqualTo(tenantId)
               .andDsNameEqualTo(dsName)
               .andIsDeletedEqualTo(0L);

        List<Datasource> datasources = datasourceMapper.selectByExample(example);
        if (datasources.isEmpty()) {
            throw new QanatBizException("数据源不存在: " + dsName);
        }

        return datasources.get(0);
    }

    private String getHologresJdbcUrl(Datasource datasource) {
        if (StringUtils.isNotBlank(datasource.getMeta())) {
            Map<String, Object> meta = JSON.parseObject(datasource.getMeta(), Map.class);
            return (String) meta.get("jdbcUrl");
        }
        throw new QanatBizException("数据源JDBC URL配置缺失: " + datasource.getDsName());
    }

    private String getHologresUsername(Datasource datasource) {
        if (StringUtils.isNotBlank(datasource.getMeta())) {
            Map<String, Object> meta = JSON.parseObject(datasource.getMeta(), Map.class);
            return (String) meta.get("username");
        }
        throw new QanatBizException("数据源用户名配置缺失: " + datasource.getDsName());
    }

    private String getHologresPassword(Datasource datasource) {
        if (StringUtils.isNotBlank(datasource.getMeta())) {
            Map<String, Object> meta = JSON.parseObject(datasource.getMeta(), Map.class);
            return (String) meta.get("password");
        }
        throw new QanatBizException("数据源密码配置缺失: " + datasource.getDsName());
    }

    private String getHologresEndpoint(Datasource datasource) {
        if (StringUtils.isNotBlank(datasource.getMeta())) {
            Map<String, Object> meta = JSON.parseObject(datasource.getMeta(), Map.class);
            return (String) meta.get("endpoint");
        }
        throw new QanatBizException("数据源endpoint配置缺失: " + datasource.getDsName());
    }

    // ==================== 占位符方法（待实现） ====================

    private String generateGenericSourceSql(Datasource datasource, ViewModel.DataObject dataObject) {
        // TODO: 实现通用数据源SQL生成
        return "-- Generic source table\n";
    }

    private String generateSourceTableDefinitions(String tenantId, ViewModel dataModel) {
        // TODO: 实现源表定义生成
        return generateFlinkSourceTableSql(tenantId, dataModel.getObject());
    }

    private String generateSinkTableDefinition(String tenantId, ViewModel dataModel) {
        // TODO: 实现目标表定义生成
        return "-- Sink table definition\n";
    }

    private String generateDimTableDefinitions(String tenantId, ViewModel dataModel) {
        // TODO: 实现维表定义生成
        return "-- Dimension table definitions\n";
    }

    private String generateMainInsertStatement(String tenantId, ViewModel dataModel) {
        // TODO: 实现主INSERT语句生成
        return generateFlinkInsertSql(tenantId, dataModel);
    }

    private String generateSourceTableForDataObject(String tenantId, ViewModel.DataObject obj, String topicName, boolean isBatch) {
        // TODO: 实现DataObject源表生成
        return generateFlinkSourceTableSql(tenantId, obj);
    }

    private String generateSourceTableForRelatedObject(String tenantId, ViewModel.RelatedDataObject relObj, String topicName, boolean isBatch) {
        // TODO: 实现RelatedDataObject源表生成
        return "-- Related object source table\n";
    }

    private String generateSinkTableForObject(String tenantId, Object dataObject) {
        // TODO: 实现对象目标表生成
        return "-- Sink table for object\n";
    }

    private String generateInsertStatementForObject(String tenantId, Object dataObject) {
        // TODO: 实现对象INSERT语句生成
        return "-- INSERT statement for object\n";
    }

    private String generateBatchSourceTable(String tenantId, Object dataObject, String topicName) {
        // TODO: 实现批量源表生成
        return "-- Batch source table\n";
    }

    private String generateBatchInsertStatement(String tenantId, Object dataObject) {
        // TODO: 实现批量INSERT语句生成
        return "-- Batch INSERT statement\n";
    }
}
