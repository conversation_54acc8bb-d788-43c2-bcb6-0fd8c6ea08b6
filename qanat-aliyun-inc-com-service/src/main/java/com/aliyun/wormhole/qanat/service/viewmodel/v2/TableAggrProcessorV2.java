package com.aliyun.wormhole.qanat.service.viewmodel.v2;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.dto.ResourcePackage;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.FlowCtlService;
import com.aliyun.wormhole.qanat.dal.domain.AppInfo;
import com.aliyun.wormhole.qanat.dal.domain.AppInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.mapper.AppInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper;
import com.aliyun.wormhole.qanat.service.viewmodel.FullLinkProcessor;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel.RelatedDataObject;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelSqlBuilder;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class TableAggrProcessorV2 {
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private AppInfoMapper appInfoMapper;
    
    @Resource
    private DatasourceService dsInfoService;
    
    @Resource
    private ViewModelSqlBuilder viewModelSqlBuilder;
    
    @Resource
    private FullLinkProcessor fullLinkProcessor;
	
	@Resource
	private DatatubeInstanceTaskMapper datatubeInstanceTaskMapper;
	
	@Resource
	private FlowCtlService flowCtlService;
    
    @Value("${datatube.codegen.version}")
    private String codegenVersion;

    public String processIncrSyncJob(String tenantId, String appName, String etlDbName, String tableName, String arrayFieldName, RelatedDataObject object, String operateEmpid, Long versionId, Long datatubeInstId) {
    	String jobName = "incrsync_" + getAppIdByName(tenantId, appName) + "_" + dsInfoService.getDsIdByTableName(tenantId, tableName, etlDbName) + "_" + object.getCode() + "_v" + versionId;
    	JSONObject srcDsMetaJson = dsInfoService.getOdsTableMetaByDsName(tenantId, object.getRef());
    	if (srcDsMetaJson.getJSONObject("odsConf") == null) {
    		log.info("{} no ods sync conf", srcDsMetaJson.getString("dsName"));
    		return null;
    	}
		flowCtlService.setFlowControlIfNotExists(datatubeInstId, jobName, 1.0);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> columnDefines = new ArrayList<>();
        List<String> drcParseColumns = new ArrayList<>();
        List<String> dimParseColumns = new ArrayList<>();
        List<String> fkColumns = new ArrayList<>();
        List<String> fkColumns1 = new ArrayList<>();
        List<String> fkColumns2 = new ArrayList<>();
        Map<String, String> srcTgtMap = new HashMap<>();
        for (ViewModel.Relation rel : object.getRelations()) {
        	if (!rel.getRelatedField().startsWith("exp#")) {
        		fkColumns.add(rel.getRelatedField().split("\\.")[1]);
        		fkColumns1.add("`" + rel.getField() + "`");
        		fkColumns2.add(rel.getField());
        		srcTgtMap.put(rel.getField(), rel.getRelatedField().split("\\.")[1]);
        	}
        	break;
        }
        for (ViewModel.Field field : object.getFields()) {
        	if (srcTgtMap.containsKey(field.getCode())) {
        		columnDefines.add("`" + srcTgtMap.get(field.getCode()) + "` " + ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType()));
        	}
        }
		columnDefines.add("`" + arrayFieldName + "` varchar");
        for (ViewModel.Field field : object.getFields()) {
        	if (srcTgtMap.containsKey(field.getCode())) {
        		if ("varchar".equalsIgnoreCase(field.getType())) {
            		drcParseColumns.add("(case when JSON_VALUE (b.x, '$." + field.getRef() + "') is null then JSON_VALUE (b.x, '$." + field.getRef() + "_old') else JSON_VALUE (b.x, '$." + field.getRef() + "') end) as " + field.getRef());
            	} else {
            		drcParseColumns.add("CAST((case when JSON_VALUE (b.x, '$." + field.getRef() + "') is null then JSON_VALUE (b.x, '$." + field.getRef() + "_old') else JSON_VALUE (b.x, '$." + field.getRef() + "') end) AS " + field.getType() + ") as " + field.getRef());
            	}
        		continue;
        	}
        }
    	dimParseColumns.add("JSON_VALUE (b.x, '$." + arrayFieldName + "') AS " + arrayFieldName);
        String sql = "--SQL\n" + 
    			"--********************************************************************--\n" + 
    			"--Author: " + operateEmpid + "\n" + 
    			"--CreateTime: " + sdf.format(new Date()) + "\n" + 
    			"--Comment: " + ("sync for " + tableName + " from " + object.getCode()) + "\n" + 
    			"--Version: " + codegenVersion + "\n" + 
    			"--********************************************************************--\n" + 
    			"create table holobinlog_source (\n" + 
    			"    hg_binlog_lsn BIGINT,\n" +
    			"    hg_binlog_event_type BIGINT,\n" +
    			"    hg_binlog_timestamp_us BIGINT,\n" +
    			"   " + StringUtils.join(columnDefines, ",\n") + "\n" + 
    			") with (\n" + 
    			"  type = 'hologres',\n" + 
    			"  dbname = '" + srcDsMetaJson.getJSONObject("odsConf").getString("database") + "',\n" + 
    			"  tablename = '" + srcDsMetaJson.getJSONObject("odsConf").getString("tableName") + "',\n" + 
    			"  username = '" + srcDsMetaJson.getJSONObject("odsConf").getString("username") + "',\n" +
    			"  password = '" + srcDsMetaJson.getJSONObject("odsConf").getString("password") + "',\n" +
    			"  endpoint = '" + srcDsMetaJson.getJSONObject("odsConf").getString("endpoint") + "',\n" +
    			"  binlog = 'true',\n" +
    			"  binlogMaxRetryTimes = '10',\n" +
    			"  binlogRetryIntervalMs = '500',\n" +
    			"  binlogBatchReadSize = '256'\n" +
    			");\n" +
    			"\n" +
    			"CREATE FUNCTION trace AS 'com.aliyun.wormhole.qanat.blink.udf.QanatTraceUdf';\n" +
    			"CREATE FUNCTION flowCtl AS 'com.aliyun.wormhole.qanat.blink.udf.QanatFlowControlUdf';\n" +
    			"CREATE FUNCTION hololog AS 'com.aliyun.wormhole.qanat.blink.udf.QanatHoloLogTUdf';\n" +
    			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" + 
    			"\n" + 
    			"create view v_source as \n" + 
    			"select \n" + 
    			"    hg_binlog_lsn,\n" + 
    			"    hg_binlog_event_type,\n" + 
    			"    hg_binlog_timestamp_us,\n" + 
    			"    " + StringUtils.join(fkColumns1, ",\n") + ",\n" + 
    			"    trace(UUID(),hg_binlog_lsn,hg_binlog_event_type,hg_binlog_timestamp_us," + StringUtils.join(fkColumns1, ",") + ",'" + jobName + "','holobinlog') as __trace_id__\n" + 
    			"from holobinlog_source;\n" + 
    			"\n" + 
    			"create view v_sink as \n" + 
    			"select \n" + 
    			"    hg_binlog_lsn,\n" + 
    			"    hg_binlog_event_type,\n" + 
    			"    hg_binlog_timestamp_us,\n" + 
    			"    " + StringUtils.join(fkColumns1, ",\n") + ",\n" + 
    			"    flowCtl(__trace_id__,'" + jobName + "',cast(" + StringUtils.join(fkColumns1, ",") + " as varchar)) as __trace_id__,\n" +
    			"    hololog(__trace_id__,hg_binlog_lsn,hg_binlog_event_type,hg_binlog_timestamp_us,'rowdata'," + StringUtils.join(fkColumns1, ",") + ") as __haslog__\n" +
    			"from v_source;\n" + 
    			"\n" + 
    			"create view v_group as \n" + 
    			"select\n" + 
    			"  " + StringUtils.join(fkColumns2, ",") + ",\n" + 
    			"  " + StringUtils.join(dimParseColumns, ",") + ",\n" + 
    			"  a.__trace_id__\n" + 
    			"from\n" + 
    			"      v_mq as a\n" + 
    			"      LEFT JOIN LATERAL TABLE (queryDim('" + ("ods".equalsIgnoreCase(object.getLookupFrom()) ? srcDsMetaJson.getString("srcDbName") : etlDbName) + "', '" + viewModelSqlBuilder.getSqlFromAggrField(tenantId, arrayFieldName, object, fkColumns2, ("ods".equalsIgnoreCase(object.getLookupFrom()) ? null : dsInfoService.getDbMetaByName(etlDbName).getString("dbType"))).replace("'", "''") + "', " + StringUtils.join(fkColumns2, ",") + ")) as b (x) ON TRUE\n" +  
    			"where " + StringUtils.join(fkColumns2, ",") + " is not null\n" + 
    			";\n" + 
    			"\n";
		
		sql += "create table update_sink(\n" +
			"    " + StringUtils.join(columnDefines, ",") + ",\n" + 
			"    __trace_id__ varchar,\n" + 
			"    primary key(" + StringUtils.join(fkColumns, ",") + ")\n" + 
			") with (\n" + 
			"    type = 'QANAT_ADB30',\n" + 
			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
			"    dbName='" + etlDbName + "',\n" + 
			"    tableName='" + tableName + "',\n" + 
			"    replaceMode = 'update_by_query',\n" + 
			"    writeMode = 'single',\n" +
			"    streamEvent = 'disable'\n" +
			");\n" + 
			"\n" + 
			"insert into update_sink\n" + 
			"select * from v_group;" +
			"\n";
        
        blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName +"/" + tableName + "/", 
        		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_ADB3, ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_UDF), false);
        
        DatatubeInstanceTask ditRecord = new DatatubeInstanceTask();
        ditRecord.setCreateEmpid(operateEmpid);
        ditRecord.setDatatubeInstId(datatubeInstId);
        ditRecord.setGmtCreate(new Date());
        ditRecord.setGmtModified(new Date());
        ditRecord.setIsDeleted(0L);
        ditRecord.setModifyEmpid(operateEmpid);
        ditRecord.setTaskName(jobName);
        ditRecord.setTaskScript(sql);
        ditRecord.setTaskType("blink_stream");
        ditRecord.setTenantId(tenantId);
        ditRecord.setVersion(versionId.intValue());
        datatubeInstanceTaskMapper.insert(ditRecord);
        
        return jobName;
    }
	
	private Long getAppIdByName(String tenantId, String appName) {
		AppInfoExample example = new AppInfoExample();
		example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
		List<AppInfo> apps = appInfoMapper.selectByExample(example);
		return apps.get(0).getId();
	}
}