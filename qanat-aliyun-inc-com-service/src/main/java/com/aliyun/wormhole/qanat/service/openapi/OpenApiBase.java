package com.aliyun.wormhole.qanat.service.openapi;

import com.aliyun.wormhole.qanat.openapi.model.ApiBaseRequest;
import com.taobao.common.keycenter.security.Cryptograph;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class OpenApiBase {

    @Autowired
    private Cryptograph crypt;
    
    private static final String QANAT_KEY_CENTER_KEY = "qanat-admin";

    protected boolean checkAccessKey(ApiBaseRequest request) {
        if (StringUtils.isBlank(request.getAccessId()) || StringUtils.isBlank(request.getAccessKey())) {
            return false;
        }
        try {
            return true;
//            String decryptAccessId = crypt.decrypt(request.getAccessKey(), QANAT_KEY_CENTER_KEY);
//            if (request.getAccessId().equals(decryptAccessId)) {
//                return true;
//            }
        } catch(Exception e) {
            log.error("checkAccess<PERSON><PERSON> failed", e);
        }
        return false;
    }
}
