package com.aliyun.wormhole.qanat.service.flink;

import java.util.*;

import com.alibaba.fastjson.TypeReference;
import com.aliyuncs.ververica.model.v20200501.*;
import com.ververica.common.model.deployment.Artifact;
import com.ververica.common.model.deployment.Deployment;
import com.ververica.common.model.deploymenttarget.DeploymentTarget;
import com.ververica.common.params.CreateDeploymentParams;
import com.ververica.common.resp.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.ververica.common.model.deployment.DeploymentState;
import com.ververica.common.params.StartParamBase;
import com.ververica.common.params.UpdateDeploymentDesiredStateParams;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
public class FlinkClient {

	private FlinkConf conf;
	private DefaultAcsClient client;

	public FlinkClient(FlinkConf conf) {
		log.debug("flinkConfig={}", JSON.toJSONString(conf));
		DefaultProfile.addEndpoint(conf.getRegionId(), StringUtils.isNotBlank(conf.getProduct()) ? conf.getProduct() : "ververica", StringUtils.isNotBlank(conf.getEndpoint()) ? conf.getEndpoint() : "ververica-share.aliyuncs.com");
		IClientProfile profile = DefaultProfile.getProfile(conf.getRegionId(), conf.getAccessId(), conf.getAccessKey());
		client = new DefaultAcsClient(profile);
		client.setAutoRetry(false);
		this.conf = conf;
	}

	/**
	 * 获取部署目标列表
	 */
	private List<DeploymentTarget> listDeploymentTargets() {
		log.info("begin to listDeploymentTargets({})");
		try {
			ListDeploymentTargetsRequest listDeploymentTargetsRequest=new ListDeploymentTargetsRequest();
			listDeploymentTargetsRequest.setWorkspace(this.conf.getWorkspace());
			listDeploymentTargetsRequest.setNamespace(this.conf.getNamespace());

			JSONObject respJsonObject = JSON.parseObject(client.doAction(listDeploymentTargetsRequest).getHttpContentString());
			log.info("respJsonObject={}", JSON.toJSONString(respJsonObject));
			return JSON.parseObject(respJsonObject.getJSONObject("data").getJSONArray("items").toJSONString(), new TypeReference<List<DeploymentTarget>>(){});
		} catch (Exception e) {
			log.error("listDeploymentTargets() failed:{}", e.getMessage(), e);
		}
		return null;
	}

	/**
	 * 获取部署目标列表
	 *
	 * @param deploymentTargetName
	 */
	private DeploymentTarget getDeploymentTargetByName(String deploymentTargetName) {
		List<DeploymentTarget> targets = this.listDeploymentTargets();
		if (CollectionUtils.isNotEmpty(targets)) {
			return targets.stream().filter(e -> e.getMetadata().getName().equalsIgnoreCase(deploymentTargetName)).findAny().get();
		}
		return null;
	}

	/**
	 * 创建任务
	 *
	 * @param jobName
	 */
	public boolean createJob(String jobName, String sql, boolean isBatch) {
		log.info("begin to createJob({},{},{})", jobName, sql, isBatch);
		try {
			GetGlobalDeploymentDefaultsRequest getGlobalDeploymentDefaultsRequest = new GetGlobalDeploymentDefaultsRequest();
			getGlobalDeploymentDefaultsRequest.setWorkspace(this.conf.getWorkspace());
			getGlobalDeploymentDefaultsRequest.setNamespace(this.conf.getNamespace());
			JSONObject respJsonObject = JSON.parseObject(client.doAction(getGlobalDeploymentDefaultsRequest).getHttpContentString());
			GetGlobalDeploymentDefaultsResp getGlobalDeploymentDefaultsResp = JSON.parseObject(respJsonObject.getJSONObject("data").toJSONString(), GetGlobalDeploymentDefaultsResp.class);
			Deployment.DeploymentSpec deploymentSpec = getGlobalDeploymentDefaultsResp.getSpec();
			log.info("globalDeploymentSpec={}", JSON.toJSONString(deploymentSpec));

			// 创建sql作业
			Artifact.SqlScriptArtifact artifact = new Artifact.SqlScriptArtifact();
			// 设置sqlScript语句
			artifact.setSqlScript(sql);

			CreateDeploymentParams deployment = new CreateDeploymentParams();
			Deployment.DeploymentMetadata deploymentMetadata = new Deployment.DeploymentMetadata();
			deploymentMetadata.setNamespace(this.conf.getNamespace());
			deploymentMetadata.setName(jobName);
			deployment.setMetadata(deploymentMetadata);

			// 设置作业类型是批作业还是流作业，当为true时，该作业为批作业，当为false时，该作业为流作业，默认是false
			deploymentSpec.getTemplate().getSpec().setBatchMode(isBatch);
			// 部署目标的ID，当sessionCluster的名称为null时，设置deploymentTargetId值
			deploymentSpec.setDeploymentTargetId(this.getDeploymentTargetByName(conf.getDeploymentTarget()).getMetadata().getId());

			deploymentSpec.getTemplate().getSpec().setArtifact(artifact);
			deployment.setSpec(deploymentSpec);
			CreateDeploymentRequest createDeploymentRequest = new CreateDeploymentRequest();
			createDeploymentRequest.setWorkspace(this.conf.getWorkspace());
			createDeploymentRequest.setNamespace(this.conf.getNamespace());
			// http的内容参数类型，POST,PUT,PATCH请求都需要设置，否则调不通
			createDeploymentRequest.setHttpContentType(FormatType.JSON);
			// 更新的作业参数，转换为json格式
			createDeploymentRequest.setParamsJson(JSON.toJSONString(deployment));
			respJsonObject = JSON.parseObject(client.doAction(createDeploymentRequest).getHttpContentString());
			log.info("respJsonObject={}", JSON.toJSONString(respJsonObject));
			return respJsonObject.getBooleanValue("success");
		} catch (Exception e) {
			log.error("createJob({}) failed:{}", jobName, e.getMessage(), e);
		}
		return false;
	}

	/**
	 * 更新任务SQL
	 * @param jobName
	 * @param sql
	 * @return
	 */
	public boolean updateJobSql(String jobName, String sql) {
		log.info("begin to updateJobSql({},{})", jobName, sql);
		try {
			Deployment deployment = this.getJob(jobName);

			Artifact.SqlScriptArtifact sqlScriptArtifact = new Artifact.SqlScriptArtifact();
			sqlScriptArtifact.setSqlScript(sql);
			deployment.getSpec().getTemplate().getSpec().setArtifact(sqlScriptArtifact);

			UpdateDeploymentRequest updateDeploymentRequest = new UpdateDeploymentRequest();
			updateDeploymentRequest.setWorkspace(this.conf.getWorkspace());
			updateDeploymentRequest.setNamespace(this.conf.getNamespace());
			// http的内容参数类型，POST,PUT,PATCH请求都需要设置，否则调不通
			updateDeploymentRequest.setHttpContentType(FormatType.JSON);
			// 该接口不支持sessionClusterName参数和deploymentTargetId参数之间的切换，所以无法更新sessionClusterName参数和deploymentTargetId参数，包含DataStream和PYTHON作业
			updateDeploymentRequest.setDeploymentId(getDeploymentIdByName(jobName));
			// 更新的作业参数，转换为json格式
			updateDeploymentRequest.setParamsJson(JSON.toJSONString(deployment));
			JSONObject respJsonObject = JSON.parseObject(client.doAction(updateDeploymentRequest).getHttpContentString());
			log.info("respJsonObject={}", JSON.toJSONString(respJsonObject));
			return respJsonObject.getBooleanValue("success");
		} catch (Exception e) {
			log.error("updateJobSql({}) failed:{}", jobName, e.getMessage(), e);
		}
		return false;
	}

	/**
	 * 更新任务引擎版本
	 * @param jobName
	 * @param version
	 * @return
	 */
	public boolean updateJobVersion(String jobName, String version) {
		log.info("begin to updateJobVersion({},{})", jobName, version);
		try {
			Deployment deployment = this.getJob(jobName);

			Artifact.SqlScriptArtifact sqlScriptArtifact = new Artifact.SqlScriptArtifact();
			sqlScriptArtifact.setVersionName(version);
			deployment.getSpec().getTemplate().getSpec().setArtifact(sqlScriptArtifact);

			UpdateDeploymentRequest updateDeploymentRequest = new UpdateDeploymentRequest();
			updateDeploymentRequest.setWorkspace(this.conf.getWorkspace());
			updateDeploymentRequest.setNamespace(this.conf.getNamespace());
			// http的内容参数类型，POST,PUT,PATCH请求都需要设置，否则调不通
			updateDeploymentRequest.setHttpContentType(FormatType.JSON);
			// 该接口不支持sessionClusterName参数和deploymentTargetId参数之间的切换，所以无法更新sessionClusterName参数和deploymentTargetId参数，包含DataStream和PYTHON作业
			updateDeploymentRequest.setDeploymentId(getDeploymentIdByName(jobName));
			// 更新的作业参数，转换为json格式
			updateDeploymentRequest.setParamsJson(JSON.toJSONString(deployment));
			JSONObject respJsonObject = JSON.parseObject(client.doAction(updateDeploymentRequest).getHttpContentString());
			log.info("respJsonObject={}", JSON.toJSONString(respJsonObject));
			return respJsonObject.getBooleanValue("success");
		} catch (Exception e) {
			log.error("updateJobVersion({}) failed:{}", jobName, e.getMessage(), e);
		}
		return false;
	}



	/**
	 * 更新任务引擎版本
	 * @param jobName
	 * @param version
	 * @return
	 */
	public boolean updateJobParams(String jobName, Map<String,String> params) {
		log.info("begin to updateJobParams({},{})", jobName, params);
		try {
			Deployment deployment = this.getJob(jobName);

			UpdateDeploymentRequest updateDeploymentRequest = new UpdateDeploymentRequest();
			updateDeploymentRequest.setWorkspace(this.conf.getWorkspace());
			updateDeploymentRequest.setNamespace(this.conf.getNamespace());
			// http的内容参数类型，POST,PUT,PATCH请求都需要设置，否则调不通
			updateDeploymentRequest.setHttpContentType(FormatType.JSON);
			// 该接口不支持sessionClusterName参数和deploymentTargetId参数之间的切换，所以无法更新sessionClusterName参数和deploymentTargetId参数，包含DataStream和PYTHON作业
			updateDeploymentRequest.setDeploymentId(getDeploymentIdByName(jobName));
			// 更新的作业参数，转换为json格式
			JSONObject json = JSON.parseObject(JSON.toJSONString(deployment));
			json.getJSONObject("spec").put("localVariables", params);
			System.out.println("json:" + JSON.toJSONString(json));
			updateDeploymentRequest.setParamsJson(JSON.toJSONString(json));
			JSONObject respJsonObject = JSON.parseObject(client.doAction(updateDeploymentRequest).getHttpContentString());
			log.info("respJsonObject={}", JSON.toJSONString(respJsonObject));
			return respJsonObject.getBooleanValue("success");
		} catch (Exception e) {
			log.error("updateJobVersion({}) failed:{}", jobName, e.getMessage(), e);
		}
		return false;
	}

	/**
	 * 更新任务引擎
	 * @param jobName
	 * @param deploymentTarget
	 * @return
	 */
	public boolean updateJobEngine(String jobName, String deploymentTarget) {
		log.info("begin to updateJobEngine({},{})", jobName, deploymentTarget);
		try {
			Deployment deployment = this.getJob(jobName);
			deployment.getSpec().setDeploymentTargetId(this.getDeploymentTargetByName(deploymentTarget).getMetadata().getId());

			UpdateDeploymentRequest updateDeploymentRequest = new UpdateDeploymentRequest();
			updateDeploymentRequest.setWorkspace(this.conf.getWorkspace());
			updateDeploymentRequest.setNamespace(this.conf.getNamespace());
			// http的内容参数类型，POST,PUT,PATCH请求都需要设置，否则调不通
			updateDeploymentRequest.setHttpContentType(FormatType.JSON);
			// 该接口不支持sessionClusterName参数和deploymentTargetId参数之间的切换，所以无法更新sessionClusterName参数和deploymentTargetId参数，包含DataStream和PYTHON作业
			updateDeploymentRequest.setDeploymentId(getDeploymentIdByName(jobName));
			// 更新的作业参数，转换为json格式
			updateDeploymentRequest.setParamsJson(JSON.toJSONString(deployment));
			JSONObject respJsonObject = JSON.parseObject(client.doAction(updateDeploymentRequest).getHttpContentString());
			log.info("respJsonObject={}", JSON.toJSONString(respJsonObject));
			return respJsonObject.getBooleanValue("success");
		} catch (Exception e) {
			log.error("updateJobEngine({}) failed:{}", jobName, e.getMessage(), e);
		}
		return false;
	}

	/**
	 * 获取任务信息
	 *
	 * @param jobName
	 */
	public GetDeploymentResp getJob(String jobName) {
		log.info("begin to getJob({})", jobName);
		try {
			GetDeploymentRequest getDeploymentRequest = new GetDeploymentRequest();
			getDeploymentRequest.setWorkspace(this.conf.getWorkspace());
			getDeploymentRequest.setNamespace(this.conf.getNamespace());
			getDeploymentRequest.setDeploymentId(getDeploymentIdByName(jobName));

			JSONObject respJsonObject = JSON.parseObject(client.doAction(getDeploymentRequest).getHttpContentString());
			log.info("respJsonObject={}", JSON.toJSONString(respJsonObject));
			return JSON.parseObject(respJsonObject.getJSONObject("data").toJSONString(), GetDeploymentResp.class);
		} catch (Exception e) {
			log.error("getJob({}) failed:{}", jobName, e.getMessage(), e);
		}
		return null;
	}

	/**
	 * 根据任务名称获取DeploymentId
	 * @param jobName
	 * @return
	 */
	private String getDeploymentIdByName(String jobName) {
		log.info("begin to getDeploymentIdByName({})", jobName);
		try {
			String workspace = this.conf.getWorkspace();
			String namespace = this.conf.getNamespace();

			// 查看指定的作业
			ListDeploymentsRequest listDeploymentsRequest = new ListDeploymentsRequest();
			listDeploymentsRequest.setWorkspace(workspace);
			listDeploymentsRequest.setNamespace(namespace);
			listDeploymentsRequest.setName(jobName);

			JSONObject respJsonObject = JSON.parseObject(client.doAction(listDeploymentsRequest).getHttpContentString());
			if (respJsonObject != null && respJsonObject.getJSONObject("data") != null &&respJsonObject.getJSONObject("data").getJSONArray("items") != null && respJsonObject.getJSONObject("data").getJSONArray("items").size() > 0) {
				for (int i = 0; i < respJsonObject.getJSONObject("data").getJSONArray("items").size(); i++) {
					JSONObject itemJsonObject = respJsonObject.getJSONObject("data").getJSONArray("items").getJSONObject(i);
					if (jobName.equalsIgnoreCase(itemJsonObject.getJSONObject("metadata").getString("name"))) {
						log.info("jobName:{} deploymentId:{}", jobName, itemJsonObject.getJSONObject("metadata").getString("id"));
						return itemJsonObject.getJSONObject("metadata").getString("id");
					}
				}
			} else {
				listDeploymentsRequest = new ListDeploymentsRequest();
				listDeploymentsRequest.setWorkspace(workspace);
				listDeploymentsRequest.setNamespace(namespace);
				listDeploymentsRequest.setName(jobName);
				listDeploymentsRequest.setBatchMode(true);

				respJsonObject = JSON.parseObject(client.doAction(listDeploymentsRequest).getHttpContentString());
				if (respJsonObject != null && respJsonObject.getJSONObject("data") != null &&respJsonObject.getJSONObject("data").getJSONArray("items") != null && respJsonObject.getJSONObject("data").getJSONArray("items").size() > 0) {
					for (int i = 0; i < respJsonObject.getJSONObject("data").getJSONArray("items").size(); i++) {
						JSONObject itemJsonObject = respJsonObject.getJSONObject("data").getJSONArray("items").getJSONObject(i);
						if (jobName.equalsIgnoreCase(itemJsonObject.getJSONObject("metadata").getString("name"))) {
							log.info("jobName:{} deploymentId:{}", jobName, itemJsonObject.getJSONObject("metadata").getString("id"));
							return itemJsonObject.getJSONObject("metadata").getString("id");
						}
					}
				}
			}
		} catch (Exception e) {
			log.error("getDeploymentIdByName({}) failed, error={}", jobName, e.getMessage(), e);
		}
		return null;
	}

	/**
	 * 启动任务
	 *
	 * @param jobName
	 * @param startTime
	 */
	public boolean startJob(String jobName, Date startTime) {
		return startJob(jobName, startTime, null);
	}

	/**
	 * 启动任务
	 *
	 * @param jobName
	 * @param startTime
	 */
	public boolean startJob(String jobName, Date startTime, Map<String, String> params) {
		log.info("begin to startJob({},{},{})", jobName, startTime, JSON.toJSONString(params));
		try {
			// 启动作业，实例状态机参考文档
			UpdateDeploymentDesiredStateParams updateDeploymentDesiredStateParams = new UpdateDeploymentDesiredStateParams();

			// 设置作业运行的期望状态 支持的状态有:RUNNING,SUSPENDED,CANCELLED;
			updateDeploymentDesiredStateParams.setState(DeploymentState.RUNNING);

			if (startTime != null) {
				// 按照指定时间启动
				StartParamBase.WithOffset startParams = new StartParamBase.WithOffset();
				startParams.setTimestamp(startTime.getTime());
				updateDeploymentDesiredStateParams.setStartParamBase(startParams);
			} else {
				//无状态启动
				StartParamBase.WithNoneState noneStateStartParams = new StartParamBase.WithNoneState();
				updateDeploymentDesiredStateParams.setStartParamBase(noneStateStartParams);
			}

			UpdateDeploymentDesiredStateRequest updateDeploymentDesiredStateRequest = new UpdateDeploymentDesiredStateRequest();
			updateDeploymentDesiredStateRequest.setNamespace(this.conf.getNamespace());
			updateDeploymentDesiredStateRequest.setWorkspace(this.conf.getWorkspace());
			updateDeploymentDesiredStateRequest.setDeploymentId(getDeploymentIdByName(jobName));

			// http的内容参数类型，POST,PUT,PATCH请求都需要设置，否则调不通
			updateDeploymentDesiredStateRequest.setHttpContentType(FormatType.JSON);

			// 参数配置，当updateDeploymentDesiredStateParams为空时，会按照配置的期望状态进行启动，当期望状态是SUSPENDED,CANCELLED时，此参数值不需要设置
			JSONObject paramJson = JSON.parseObject(JSON.toJSONString(updateDeploymentDesiredStateParams));
			if (params != null && CollectionUtils.isNotEmpty(params.keySet())) {
				paramJson.putAll(params);
			}
			updateDeploymentDesiredStateRequest.setParamsJson(JSON.toJSONString(paramJson));
			log.info("updateDeploymentDesiredStateRequest={}", JSON.toJSONString(paramJson));

			JSONObject respJsonObject = JSON.parseObject(client.doAction(updateDeploymentDesiredStateRequest).getHttpContentString());
			log.info("respJsonObject={}", JSON.toJSONString(respJsonObject));
			return respJsonObject.getBooleanValue("success");
		} catch (Exception e) {
			log.error("startJob({}) failed:{}", jobName, e.getMessage(), e);
		}
		return false;
	}

	/**
	 * 停止任务
	 *
	 * @param jobName
	 */
	public boolean stopJob(String jobName) {
		log.info("begin to stopJob({})", jobName);
		try {
			// 启动作业，实例状态机参考文档
			UpdateDeploymentDesiredStateParams updateDeploymentDesiredStateParams = new UpdateDeploymentDesiredStateParams();
			// 设置作业运行的期望状态 支持的状态有:RUNNING,SUSPENDED,CANCELLED;
			updateDeploymentDesiredStateParams.setState(DeploymentState.CANCELLED);

			UpdateDeploymentDesiredStateRequest updateDeploymentDesiredStateRequest = new UpdateDeploymentDesiredStateRequest();
			updateDeploymentDesiredStateRequest.setNamespace(this.conf.getNamespace());
			updateDeploymentDesiredStateRequest.setWorkspace(this.conf.getWorkspace());
			updateDeploymentDesiredStateRequest.setDeploymentId(getDeploymentIdByName(jobName));
			// http的内容参数类型，POST,PUT,PATCH请求都需要设置，否则调不通
			updateDeploymentDesiredStateRequest.setHttpContentType(FormatType.JSON);
			// 参数配置，当updateDeploymentDesiredStateParams为空时，会按照配置的期望状态进行启动，当期望状态是SUSPENDED,CANCELLED时，此参数值不需要设置
			updateDeploymentDesiredStateRequest.setParamsJson(JSON.toJSONString(updateDeploymentDesiredStateParams));

			JSONObject respJsonObject = JSON.parseObject(client.doAction(updateDeploymentDesiredStateRequest).getHttpContentString());
			log.info("respJsonObject={}", JSON.toJSONString(respJsonObject));
			return respJsonObject.getBooleanValue("success");
		} catch (Exception e) {
			log.error("stopJob({}) failed:{}", jobName, e.getMessage(), e);
		}
		return false;
	}

	public static void main(String[] args) {
		FlinkConf conf = new FlinkConf();
		conf.setRegionId("center");
		conf.setNamespace("datatube");
		conf.setWorkspace("default");
		conf.setProduct("ververica");
		conf.setEndpoint("ververica-share.aliyuncs.com");
		conf.setDeploymentTarget("asi-zjk-flink-c02_na63blinkcptssd1_aly-devata-customer");

		FlinkClient client = new FlinkClient(conf);
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		cal.add(Calendar.MINUTE, -10);
		Map<String, String> customParams = new HashMap<>();
		customParams.put("indexName", "indexName");
//		System.out.println(client.startJob("fullsync-new_ppl-partner_bd_acl", null, customParams));
//		System.out.println(JSON.toJSONString(client.getJob("fullsync-new_ppl-partner_bd_acl")));
		client.updateJobParams("test_params", customParams);
		System.out.println(JSON.toJSONString(client.getJob("test_params")));
//		System.out.println(JSON.toJSONString(client.updateJob("test_period_sink_2", null, "vvr-6.0.7-flink-1.15")));
//		System.out.println(JSON.toJSONString(client.listDeploymentTargets()));
		String sql = "CREATE Temporary TABLE datagen_source (\n" +
				"  name VARCHAR,               \n" +
				"    ts TIMESTAMP(3),\n" +
				"    WATERMARK FOR ts AS ts - INTERVAL '2' SECOND\n" +
				") WITH (\n" +
				"  'connector' = 'datagen',\n" +
				"  'rows-per-second' = '1'\n" +
				");\n" +
				"\n" +
				"CREATE TEMPORARY View v_source AS\n" +
				"SELECT\n" +
				"TUMBLE_START(ts, INTERVAL '1' MINUTE) as window_start,\n" +
				"TUMBLE_END(ts, INTERVAL '1' MINUTE) as window_end,\n" +
				"COUNT(name)\n" +
				"FROM datagen_source\n" +
				"GROUP BY TUMBLE(ts, INTERVAL '1' MINUTE);\n" +
				"\n" +
				"CREATE TEMPORARY View v_lookup AS \n" +
				"select cast(b.cid as varchar) as cid,'${testKey1}',current_timestamp\n" +
				"from v_source as a\n" +
				"LEFT\n" +
				"  JOIN v_period_sink_test2 FOR SYSTEM_TIME AS OF PROCTIME () AS b\n" +
				"  on b.tag='flink'\n" +
				";\n" +
				"\n" +
				"create TEMPORARY table mysql_sink (\n" +
				"    cid varchar,\n" +
				"    empids varchar,\n" +
				"    gmt_modified TIMESTAMP,\n" +
				"    primary key(cid) not enforced\n" +
				") WITH (\n" +
				"  'connector' = 'mysql',\n" +
				"  'hostname' = 'rm-8vb31fh184t3rhctz.mysql.zhangbei.rds.aliyuncs.com',\n" +
				"  'port' = '3306',\n" +
				"  'username' = 'alyqanat',\n" +
				"  'password' = 'Qanat123',\n" +
				"  'database-name' = 'alyqanat',\n" +
				"  'table-name' = 'flink_period_sink_test2'\n" +
				");\n" +
				"\n" +
				"insert into mysql_sink select * from v_lookup;";
//		System.out.println(client.createJob("test_create_flink_job_2", sql, false));
//		System.out.println(client.stopJob("test_period_sink_2"));
//		System.out.println(client.getDeploymentIdByName("test_period_sink_2"));
	}
}
