package com.aliyun.wormhole.qanat.service.template;

public interface Adb3SyncTemplate {

	public static String BLINK_SYNC_SQL = "--SQL\r\n" + 
            "--********************************************************************--\r\n" + 
            "--Author: %s\r\n" + 
            "--CreateTime: %s\r\n" + 
            "--Comment: %s\r\n" + 
            "--********************************************************************--\r\n" + 
			"create table mq_source (\n" + 
			"    msg varchar,\n" + 
			"    __traceId__ varchar header\n" + 
			") with (\n" + 
			"  type = 'custom',\n" + 
			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"  topic = '%s',\n" + 
			"  `group.id` = '%s',\n" + 
			"  `dbName` = '%s',\n" +
			"  startupMode = 'TIMESTAMP',\n" +
			"  fieldDelimiter = '`'\n" +
			");\n" + 
            "\r\n" + 
            "create table adb3_sink (\r\n" + 
            "    msg varchar,\r\n" + 
            "    trace_id varchar\r\n" +
            ") with (\r\n" + 
            "    type='custom',\r\n" + 
            "    class = 'com.aliyun.wormhole.qanat.blink.sink.QanatAdb3Sink',\r\n" + 
            "    dbName='%s',\r\n" + 
            "    tableName='%s',\r\n" + 
            "    %s\n" + 
			"    streamType = 'kafka',\n" + 
			"    eventTopic = '%s',\n" + 
			"    eventServer = '%s'\n" + 
            ");\r\n" + 
            "\r\n" + 
            "insert into adb3_sink\r\n" + 
            "select * from mq_source;";
	
	public static String BLINK_ODS_DEFAULT_PLANJSON = "{\n" + 
			"  \"global\": [\n" + 
			"    {\n" + 
			"      \"jobManagerMinCpuCores\": 0.1,\n" + 
			"      \"jobManagerMinMemoryCores\": 1024,\n" + 
			"      \"jobManagerCpuCores\": 0.25,\n" + 
			"      \"jobManagerMemoryInMB\": 1024\n" + 
			"    }\n" + 
			"  ],\n" + 
			"  \"nodes\": [\n" + 
			"    {\n" + 
			"      \"id\": 3,\n" + 
			"      \"uid\": \"3\",\n" + 
			"      \"name\": \"Kafka010TableSource(msg, __traceId__)-Stream\",\n" + 
			"      \"pact\": \"Source\",\n" + 
			"      \"chainingStrategy\": \"HEAD\",\n" + 
			"      \"parallelism\": 12,\n" + 
			"      \"maxParallelism\": 12,\n" + 
			"      \"vcore\": 0.25,\n" + 
			"      \"heap_memory\": 246,\n" + 
			"      \"native_memory\": 10\n" + 
			"    },\n" + 
			"    {\n" + 
			"      \"id\": 4,\n" + 
			"      \"uid\": \"4\",\n" + 
			"      \"name\": \"SinkConversion to Tuple2\",\n" + 
			"      \"pact\": \"Operator\",\n" + 
			"      \"parallelism\": 12,\n" + 
			"      \"maxParallelism\": 32768,\n" + 
			"      \"vcore\": 0.25,\n" + 
			"      \"heap_memory\": 246,\n" + 
			"      \"native_memory\": 10\n" + 
			"    },\n" + 
			"    {\n" + 
			"      \"id\": 5,\n" + 
			"      \"uid\": \"5\",\n" + 
			"      \"name\": \"TupleOutputFormatAdapterSink:com.alibaba.blink.streaming.connector.custom.sink.CustomOutputFormat@4fdca00a\",\n" + 
			"      \"pact\": \"Sink\",\n" + 
			"      \"parallelism\": 12,\n" + 
			"      \"maxParallelism\": 32768,\n" + 
			"      \"vcore\": 0.25,\n" + 
			"      \"heap_memory\": 236,\n" + 
			"      \"native_memory\": 20\n" + 
			"    }\n" + 
			"  ],\n" + 
			"  \"links\": [\n" + 
			"    {\n" + 
			"      \"source\": 3,\n" + 
			"      \"target\": 4\n" + 
			"    },\n" + 
			"    {\n" + 
			"      \"source\": 4,\n" + 
			"      \"target\": 5\n" + 
			"    }\n" + 
			"  ]\n" + 
			"}";
	
	public static String DAG_SCRIPT = "Dag dag = new Dag(\"%s\");\r\n" + 
            "%s\r\n" + 
            "DataXNode fullSync = new DataXNode(\"DataX_%s\", dag);\r\n" + 
            "fullSync.setSrcDsName(\"%s\");\r\n" + 
            "fullSync.setDstDsName(\"%s\");\r\n" + 
            "fullSync.setDataBaseline(true);\r\n" + 
            "fullSync.setParallism(%s);\r\n" + 
            "fullSync.setBatchSize(%s);\r\n" + 
            "%s\r\n" +
            "BlinkStreamNode incrSync = new BlinkStreamNode(\"BlinkStream_%s\", dag);\r\n" + 
            "incrSync.setJobName(\"%s\");\r\n" + 
            "fullSync.setNext(incrSync);\r\n" + 
            "%s" + 
            "return dag;";
	
	public static String FULLSYNC_ONLY_DAG_SCRIPT = "Dag dag = new Dag(\"DAG_%s\");\r\n" + 
            "DataXNode fullSync = new DataXNode(\"DataX_%s\", dag);\r\n" + 
            "fullSync.setSrcDsName(\"%s\");\r\n" + 
            "fullSync.setDstDsName(\"%s\");\r\n" + 
            "fullSync.setParallism(%s);\r\n" + 
            "fullSync.setBatchSize(%s);\r\n" + 
            "return dag;";
    
    public static String CHECK_ALL_DAG_SCRIPT_BIGDATA = "Dag dag = new Dag(\"DAG_checkAll_%s\");\r\n" + 
            "DataXNode fullSync = new DataXNode(\"DataX_checkAll_%s\", dag);\r\n" + 
            "fullSync.setSrcDsName(\"%s\");\r\n" + 
            "fullSync.setDstDsName(\"%s\");\r\n" + 
            "fullSync.setDataBaseline(true);\r\n" + 
            "fullSync.setParallism(10);\r\n" + 
            "fullSync.setBatchSize(20480);\r\n" + 
            "BlinkBatchNode idScan = new BlinkBatchNode(\"BlinkBatch_%s\", dag);\r\n" + 
            "idScan.setJobName(\"%s\");\r\n" + 
            "fullSync.setNext(idScan);\r\n" + 
            "return dag;";
    
    public static String CHECK_ALL_DAG_SCRIPT = "Dag dag = new Dag(\"DAG_checkAll_%s\");\r\n" + 
            "BlinkBatchNode idScan = new BlinkBatchNode(\"BlinkBatch_%s\", dag);\r\n" + 
            "idScan.setJobName(\"%s\");\r\n" + 
            "return dag;";
    
    public static String FULLLINK_DAG_SCRIPT = "Dag dag = new Dag(\"DAG_FullLink_%s\");\r\n" + 
        "BlinkStreamNode blink = new BlinkStreamNode(\"BlinkStream_%s\", dag);\r\n" + 
        "blink.setJobName(\"%s\");\r\n" + 
        "return dag;";
    
    public static String DATACHECK_DAG_SCRIPT= "Dag dag = new Dag(\"DAG_DataCheck_%s\");\r\n" + 
        "BlinkStreamNode drcCheck = new BlinkStreamNode(\"BlinkStream_%s\", dag);\r\n" + 
        "drcCheck.setJobName(\"%s\");\r\n" + 
        "BlinkStreamNode compute = new BlinkStreamNode(\"BlinkStream_%s\", dag);\r\n" + 
        "compute.setJobName(\"%s\");\r\n" + 
        "BlinkStreamNode correct = new BlinkStreamNode(\"BlinkStream_%s\", dag);\r\n" + 
        "correct.setJobName(\"%s\");\r\n" + 
        "return dag;";
	
	public static String BLINK_INCR_CHECK_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--********************************************************************--\n" + 
            "CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
            "CREATE FUNCTION delayMs AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDelayMsUdf';\n" +
			"CREATE FUNCTION checkData AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDataCheckUdf';\n" + 
			"CREATE FUNCTION qanatConcat AS 'com.aliyun.wormhole.qanat.blink.udf.QanatConcatUdf';\n" + 
			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" +
            "\n" + 
            "create table drc_source (\n" + 
            "    msg varchar,\n" + 
            "    __traceId__ varchar header  \n" + 
            ") with (\n" + 
            "  type = 'custom',\n" + 
            "  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
            "  topic = '%s',\n" + 
            "  `group.id` = '%s',\n" + 
            "  `dbName` = '%s',\n" +
            "  startupMode = 'TIMESTAMP',\n" +
            "  fieldDelimiter = '`'\n" +
            ");\n" + 
			"\n" + 
            "create view v_check_id AS\n" + 
            "select \n" + 
            "    (case when JSON_VALUE(t.a, '$.%s') is null then JSON_VALUE(t.a, '$.%s_old') else JSON_VALUE(t.a, '$.%s') end) as id,\n" + 
            "    'drc' as src,\n" + 
            "    delayMs(%s, msg) as msg,\n" + 
            "    (case when JSON_VALUE(t.a, '$.%s') is null then JSON_VALUE(t.a, '$.%s_old') else JSON_VALUE(t.a, '$.%s') end) as `key`,\n" + 
            "    JSON_VALUE(t.a, '$.eventType') as eventType,\n" +
            "    __traceId__\n" +
            "from drc_source, LATERAL TABLE (parseDrcFields (msg, '%s')) as t (a)\n" + 
            ";" +
            "\n" +
			"%s";
	
	public static String BLINK_CHECK_COMPUTE_SQL = 
			"\n" + 
			"CREATE TABLE check_result_mq_sink (\n" + 
			"    msg varchar,\n" + 
			"    `key` varchar,\n" + 
			"    __traceId__ varchar,\n" + 
			"    primary key(`key`)\n" + 
			") WITH (\n" + 
			"    type='QANAT_KAFKA010',\n" + 
			"    class='com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"    topic='%s',\n" + 
			"    dbName='%s',\n" + 
			"    fieldDelimiter='`'\n" +  
			");\n" + 
			"\n" + 
			"create view v_check as\n" + 
			"select \n" + 
			"    a.id,\n" + 
			"    a.src,\n" + 
			"    checkData('%s',\n" + 
			"    qanatConcat('|', %s),\n" + 
			"    qanatConcat('|', %s)) as msg,\n" + 
			"    a.__traceId__\n" +
			"from v_check_id as a\n" + 
			"LEFT JOIN LATERAL TABLE (queryDim('%s', 'select %s from %s where %s=?', a.id)) as b(x) ON TRUE\n" + 
			"LEFT JOIN LATERAL TABLE (queryDim('%s', 'select %s from %s where %s=?', a.id)) as c(x) ON TRUE\n" + 
			"%s\n" + 
			";\n" + 
			"\n" + 
			"create view v_check_result as \n" + 
			"select (case when msg='' then concat_ws('|', '%s', id, src, 'OK') else concat_ws('|', '%s', id, src, msg) end) as msg, cast(id AS VARCHAR) as `key`, __traceId__\n" + 
			"from v_check;\n" + 
			"\n" + 
			"insert into check_result_mq_sink\n" + 
			"select * from v_check_result;\n" + 
			"\n" +
			"CREATE TABLE correct_mq_sink (\n" + 
			"    ids varchar,\n" + 
			"    `key` varchar,\n" + 
			"    __traceId__ varchar,\n" + 
			"    primary key(`key`)\n" + 
			") WITH (\n" + 
			"    type='QANAT_KAFKA010',\n" + 
			"    class='com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"    topic='%s',\n" + 
			"    dbName='%s',\n" + 
			"    fieldDelimiter='`'\n" +  
			");\n" + 
			"\n" + 
			"insert into correct_mq_sink\n" + 
			"select\n" + 
			"  split_index (msg, '|', 1) as ids,\n" + 
			"  split_index (msg, '|', 1) as `key`,\n" + 
			"  __traceId__\n" + 
			"from v_check_result\n" + 
			"where split_index (msg, '|', 3)<>'OK' and split_index (msg, '|', 1) is not null and split_index (msg, '|', 1) <> '';"
			;
	
	public static String BLINK_CHECK_CORRECT_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--********************************************************************--\n" + 
			"create table mq_source (\n" + 
			"    ids varchar,\n" + 
			"    __traceId__ varchar header  \n" + 
			") with (\n" + 
			"  type = 'custom',\n" + 
			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"  topic = '%s',\n" + 
			"  `group.id` = '%s',\n" + 
			"  `dbName` = '%s',\n" +
			"  startupMode = 'TIMESTAMP',\n" +
			"  fieldDelimiter = '`'\n" +
			");\n" + 
			"\n" + 
			"CREATE TABLE tddl_dim (\n" + 
			"  %s,\n" + 
			"  PRIMARY KEY (%s),\n" + 
			"  PERIOD FOR SYSTEM_TIME\n" + 
			") WITH (\n" + 
			"  %s\n" + 
			");\n" + 
			"\n" + 
			"CREATE TABLE adb_sink (\n" + 
			" %s,\n" + 
			" __trace_id__ varchar,\n" +
			"  PRIMARY KEY (%s)\n" + 
			") WITH (\n" + 
			"    type = 'QANAT_ADB30',\n" + 
			"    tablefactoryclass = 'com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory',\n" + 
            "    dbName='%s',\r\n" + 
			"    tableName='%s',\n" + 
			"    writeMode='single',\n" + 
			"    replaceMode='replace',\n" + 
			"    streamType = 'kafka',\n" + 
			"    eventTopic = '%s',\n" + 
			"    eventServer = '%s'\n" + 
			");\n" + 
			"\n" + 
			"create view v_id as\n" + 
			"select id as %s, __traceId__ from mq_source, lateral table (STRING_SPLIT (ids, ',')) as T (id)\n" + 
			";\n" + 
			"\n" + 
			"insert into adb_sink\n" + 
			"select b.%s,a.__traceId__\n" + 
			"from v_id as a\n" + 
			"inner join tddl_dim FOR SYSTEM_TIME AS OF PROCTIME () as b on a.%s=b.%s;";
	
	public static String BLINK_FULL_LINK_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--********************************************************************--\n" + 
			"CREATE FUNCTION parseDrcFields AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatDrcParseFieldsUDTF';\n" + 
			"create table drc_source (\n" + 
			"    msg varchar,\n" + 
			"    __ts__ bigint header,\n" + 
			"    __traceId__ varchar header\n" + 
			") with (\n" + 
			"  type = 'custom',\n" + 
			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"  topicPattern = '%s',\n" + 
			"  `group.id` = '%s',\n" + 
			"  `dbName` = '%s',\n" +
			"  startupMode = 'TIMESTAMP',\n" +
			"  fieldDelimiter = '`'\n" +
			");\n" + 
			"\n" + 
			"create table log_source (\n" + 
			"    msg varchar,\n" + 
			"    __ts__ bigint header,\n" + 
			"    __traceId__ varchar header  \n" + 
			") with (\n" + 
			"  type = 'custom',\n" + 
			"  tableFactoryClass = 'com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"  topicPattern = '%s',\n" + 
			"  `group.id` = '%s',\n" + 
			"  `dbName` = '%s',\n" +
			"  startupMode = 'TIMESTAMP',\n" +
			"  fieldDelimiter = '`'\n" +
			");\n" + 
			"\n" + 
			"CREATE TABLE common_data_full_link_log_adb_sink (\n" + 
			"  trace_id varchar,\n" + 
			"  pk bigint,\n" + 
			"  key varchar,\n" + 
			"  ts bigint,\n" + 
			"  msg varchar,\n" + 
			"  db varchar,\n" + 
			"  ext1 varchar,\n" + 
			"  gmt_create timestamp\n" + 
			") WITH (\n" + 
			"  %s" + 
			");\n" + 
			"\n" + 
			"insert\n" + 
			"  into common_data_full_link_log_adb_sink\n" + 
			"select\n" + 
			"  __traceId__ as trace_id,\n" + 
			"  %s as pk,\n" + 
			"  (case when JSON_VALUE (t.a, '$.%s') is null then JSON_VALUE (t.a, '$.%s_old') else JSON_VALUE (t.a, '$.%s') END) as key,\n" + 
			"  __ts__ as ts,\n" + 
			"  msg,\n" + 
			"  '%s' as db,\n" + 
			"  '%s' as ext1,\n" + 
			"  CURRENT_TIMESTAMP as gmt_create\n" + 
			"from\n" + 
			"  drc_source, LATERAL TABLE (parseDrcFields (msg, 'id')) as t (a);\n" + 
			"\n" + 
			"insert\n" + 
			"  into common_data_full_link_log_adb_sink\n" + 
			"select\n" + 
			"  __traceId__ as trace_id,\n" + 
			"  %s as pk,\n" + 
			"  (case when JSON_VALUE (t.a, '$.%s') is null then JSON_VALUE (t.a, '$.%s_old') else JSON_VALUE (t.a, '$.%s') END) as key,\n" + 
			"  __ts__ as ts,\n" + 
			"  msg,\n" + 
			"  '%s' as db,\n" + 
			"  '%s' as ext1,\n" + 
			"  CURRENT_TIMESTAMP as gmt_create\n" + 
			"from\n" + 
			"  log_source, LATERAL TABLE (parseDrcFields (msg, 'id')) as t (a);";
	
	public static String BLINK_CHECK_ALL_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--********************************************************************--\n" + 
			"CREATE FUNCTION checkData AS 'com.aliyun.wormhole.qanat.blink.udf.QanatDataCheckUdf';\n" + 
			"CREATE FUNCTION qanatConcat AS 'com.aliyun.wormhole.qanat.blink.udf.QanatConcatUdf';\n" + 
			"CREATE FUNCTION queryDim AS 'com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF';\n" +
			"CREATE TABLE check_all_source (\n" + 
			"  %s bigint\n" + 
			") WITH (\n" + 
			"    %s\n" + 
			");\n" + 
			"\n" + 
			"create view v_check_id as\n" + 
			"select \n" + 
			"    %s as id,\n" + 
			"    '%s' as src,\n" + 
			"    cast(%s as varchar) as `key`,\n" +
			"    REGEXP_REPLACE(UUID(),'-','') as __traceId__\n" +
			"from check_all_source;" + 
			"\n" + 
			"%s";
    
    public static String DRC_DAG_SCRIPT = "Dag dag = new Dag(\"DAG_%s\");\r\n" + 
            "BlinkStreamNode incrSync = new BlinkStreamNode(\"BlinkStream_%s\", dag);\r\n" + 
            "incrSync.setJobName(\"%s\");\r\n" + 
            "return dag;";
	
	public static String BLINK_SYNC_DRC_SQL = "--SQL\n" + 
			"--********************************************************************--\n" + 
			"--Author: %s\n" + 
			"--CreateTime: %s\n" + 
			"--Comment: %s\n" + 
			"--********************************************************************--\n" + 
			"CREATE TABLE drc_source (\n" + 
			"    msg varchar,\n" + 
			"    __traceId__ varchar header\n" + 
			") WITH (\n" + 
			"    type = 'custom',\n" + 
			"    tableFactoryClass = 'com.aliyun.wormhole.qanat.blink.drc.DrcTableFactory',\n" + 
			"    sourceCollectorClass = 'com.aliyun.wormhole.qanat.blink.drc.parser.DrcSourceParser',\n" + 
			"    groupName = '%s',\n" + 
			"    identification = '%s',\n" + 
			"    %s\n" + 
			"    taskId = '%s',\n" + 
			"    dataModel = 'datatube',\n" + 
			"    consumeId = '%s',\n" + 
			"    %s\n" + 
			");\n" + 
			"\n" + 
			"create view v_drc as\n" + 
			"select msg, COALESCE(JSON_VALUE(msg, '$.fieldValues.%s.newValue'),JSON_VALUE(msg, '$.fieldValues.%s.oldValue')) as id, __traceId__\n" + 
			"from drc_source;" +
			"\n" + 
			"create table mq_sink (\n" + 
			"    msg varchar,\n" + 
			"    id varchar,\n" + 
			"    __traceId__ varchar,\n" + 
			"    primary key(id)\n" + 
			") with (\n" + 
			"    type='QANAT_KAFKA010',\n" + 
			"    class='com.aliyun.wormhole.qanat.kafka010.Kafka010TableFactory',\n" + 
			"    topic='%s',\n" + 
			"    dbName='%s'\n" + 
			");\n" + 
			"\n" + 
			"insert into mq_sink\n" + 
			"select * from v_drc;";
}
