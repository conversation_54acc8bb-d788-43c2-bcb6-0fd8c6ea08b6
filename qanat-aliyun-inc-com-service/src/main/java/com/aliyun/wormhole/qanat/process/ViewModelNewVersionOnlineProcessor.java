package com.aliyun.wormhole.qanat.process;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfo;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelTaskRelation;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelTaskRelationExample;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelTaskRelationMapper;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelHandler;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * ViewModel新version生效上线任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class ViewModelNewVersionOnlineProcessor extends JavaProcessor {
    
    @Resource
    private ViewModelInfoMapper viewModelInfoMapper;
    
    @Resource
    private ViewModelTaskRelationMapper viewModelTaskRelationMapper;
    
    @Resource
    private ViewModelHandler viewModelHandler;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
            log.info("start to refresh vm version, param=[]", context.getJobParameters());
            String tenantId = paramsJson.getString("tenantId");
            if (StringUtils.isBlank(tenantId)) {
            	log.info("tenantId is empty");
                return new ProcessResult(false);
            }
            JSONArray vmIdJsonArray = paramsJson.getJSONArray("viewModelIds");
        	List<Long> vmIds = new ArrayList<>();
            if (vmIdJsonArray != null && vmIdJsonArray.size() > 0) {
            	for (int i = 0; i < vmIdJsonArray.size(); i++) {
            		vmIds.add(vmIdJsonArray.getLong(i));
            	}
            }
        	ViewModelInfoExample example = new ViewModelInfoExample();
        	ViewModelInfoExample.Criteria criteria = example.createCriteria();
        	criteria.andTenantIdEqualTo(tenantId);
        	criteria.andIsDeletedEqualTo(0L);
        	if (CollectionUtils.isNotEmpty(vmIds)) {
        		criteria.andIdIn(vmIds);
        	}
			List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(example);
			if (CollectionUtils.isNotEmpty(viewModelInfos)) {
				for (ViewModelInfo vm : viewModelInfos) {
					log.info("begin to procees vm[{}]", vm.getModelName());
					
					ViewModelTaskRelationExample modelTaskRelExample = new ViewModelTaskRelationExample();
		        	modelTaskRelExample.createCriteria().andTenantIdEqualTo(tenantId)
		        										.andIsDeletedEqualTo(0L)
		        										.andViewModelNameEqualTo(vm.getModelName())
		        										.andModelVersionIdEqualTo(vm.getVersionId());
		        	List<ViewModelTaskRelation> modelTaskRels = viewModelTaskRelationMapper.selectByExample(modelTaskRelExample);
		        	if (CollectionUtils.isEmpty(modelTaskRels)) {
	    		    	//重新生成同步任务
	    		    	//TODO 可以单独生成ref相关的metric表的同步任务，减小修改范围
						log.info("create syncTasks fro vm[{}] with version[{}]", vm.getModelName(), vm.getVersionId());
	    		    	Long createTaskResult = viewModelHandler.createBatchStreamTasks(tenantId, vm.getId(), "schedulerx2");
	    		    	log.info("rebuild sync jobs for viewModel:{} with new version:{} finished, result:{}", vm.getId(), vm.getVersionId(), createTaskResult);
	    		    	
	    		    	//wait for blink job ready
	    		    	try {
	    		    		Thread.sleep(1000 * 30);
	    		    	} catch(Exception e) {}
	    		    	
						log.info("restart vm[{}] dataSync tasks", vm.getModelName());
		        		Long restartResult = viewModelHandler.restartModelTask(tenantId, vm.getId(), "schedulerx2");
		        		log.info("vm[{}] restartResult={}", vm.getModelName(), restartResult);
		        	} else {
		        		log.info("vm[{}] with new version[{}] already has syncTask[{}], pass", vm.getModelName(), vm.getVersionId(), modelTaskRels.get(0).getTaskId());
		        	}
					log.info("finish to procees vm[{}]", vm.getModelName());
				}
			}
        } catch (QanatBizException e) {
            log.error("ViewModelNewVersionOnlineProcessor任务调度异常:{}", e.getMessage());
            return new ProcessResult(false, e.getMessage());
        } catch (Exception e) {
            log.error("ViewModelNewVersionOnlineProcessor任务调度异常", e);
            return new ProcessResult(false, e.getMessage());
        }
        return new ProcessResult(true);
    }
    
    @Override
    public void kill(JobContext context) {
        
    }
}