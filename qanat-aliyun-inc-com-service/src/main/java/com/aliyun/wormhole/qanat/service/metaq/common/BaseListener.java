package com.aliyun.wormhole.qanat.service.metaq.common;

import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.taobao.ateye.annotation.Switch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class BaseListener implements MessageListenerConcurrently {

	private static final Logger logger = LoggerFactory.getLogger(BaseListener.class);

	@Switch(description = "是否开启消费metaQ")
	public Boolean isConsumeTask = Boolean.TRUE;

	@Switch(description = "消费重试次数")
	protected int retryTimes = 5;

	@Override
	public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgList, ConsumeConcurrentlyContext context) {
		if(isConsumeTask){
			MessageExt msg = msgList.get(0);
			try {
				this.processMessage(msg);
			} catch (TaskRetryException e) {
				if(msg.getReconsumeTimes() >= retryTimes){
					return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
				}
				context.setDelayLevelWhenNextConsume(e.getLaterLevel());
				return ConsumeConcurrentlyStatus.RECONSUME_LATER;
			} catch (Exception t) {
				logger.error("消费异常:" + msg, t);
			}
		}
		return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
	}

	protected abstract void processMessage(MessageExt msg) throws TaskRetryException;

}
