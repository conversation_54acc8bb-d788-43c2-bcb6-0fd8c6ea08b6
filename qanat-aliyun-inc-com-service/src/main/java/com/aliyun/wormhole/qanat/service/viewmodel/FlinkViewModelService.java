package com.aliyun.wormhole.qanat.service.viewmodel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.*;
import com.aliyun.wormhole.qanat.dal.mapper.*;
import com.aliyun.wormhole.qanat.service.base.DatasourceService;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.flink.FlinkService;
import com.aliyun.wormhole.qanat.service.task.TaskService;
import com.aliyun.wormhole.qanat.service.template.FlinkSyncTemplate;
import com.aliyun.wormhole.qanat.service.util.YamlUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * Flink视图模型服务
 * 基于TDD思路实现，专门处理Flink架构的业务逻辑
 */
@Slf4j
@Service
public class FlinkViewModelService {
    
    // 复用现有基础服务（不修改）
    @Resource
    private FlinkService flinkService;
    
    @Resource
    private DatasourceService datasourceService;
    
    @Resource
    private TaskService taskService;
    
    @Resource
    private QanatDatasourceHandler dsHandler;
    
    // 复用现有Mapper（不修改）
    @Resource
    private ViewModelInfoMapper viewModelInfoMapper;
    
    @Resource
    private ViewModelVersionMapper viewModelVersionMapper;
    
    @Resource
    private TaskInfoMapper taskInfoMapper;
    
    @Resource
    private DatasourceMapper datasourceMapper;
    
    @Resource
    private TenantInfoMapper tenantInfoMapper;
    
    // 新增的Flink专用组件
    @Resource
    private FlinkSqlBuilder flinkSqlBuilder;
    
    @Resource
    private FlinkDagBuilder flinkDagBuilder;
    
    @Resource
    private FlinkSyncTemplate flinkSyncTemplate;
    
    /**
     * 创建Flink视图模型
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createFlinkViewModel(String tenantId, ViewModel viewModel, String yamlContent, String appName) {
        log.info("FlinkViewModelService.createFlinkViewModel start, tenantId={}, modelCode={}", tenantId, viewModel.getCode());
        
        try {
            // 1. 验证租户信息
            TenantInfo tenantInfo = getTenantInfo(tenantId);
            
            // 2. 检查视图模型是否已存在
            if (isViewModelExists(tenantId, viewModel.getCode())) {
                throw new QanatBizException("视图模型已存在: " + viewModel.getCode());
            }
            
            // 3. 验证数据源（确保引用的数据源支持Hologres）
            validateDataSources(tenantId, viewModel);
            
            // 4. 生成系统YAML（优化后的配置）
            String sysYaml = generateSystemYaml(tenantId, viewModel, yamlContent);
            
            // 5. 创建视图模型记录
            Long viewModelId = createViewModelRecord(tenantId, viewModel, yamlContent, sysYaml, appName);
            
            // 6. 创建Hologres目标表
            createHologresTargetTable(tenantId, viewModelId, viewModel);
            
            log.info("FlinkViewModelService.createFlinkViewModel success, viewModelId={}", viewModelId);
            return true;
            
        } catch (Exception e) {
            log.error("FlinkViewModelService.createFlinkViewModel failed", e);
            throw new QanatBizException("创建Flink视图模型失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建Flink批流ETL任务
     * 参考ViewModelHandler的核心思路，根据对象类型和主/关联关系生成不同的任务
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> createFlinkBatchStreamTasks(String tenantId, Long viewModelId) {
        log.info("FlinkViewModelService.createFlinkBatchStreamTasks start, tenantId={}, viewModelId={}", tenantId, viewModelId);
        
        try {
            // 1. 获取视图模型信息
            ViewModelInfo viewModelInfo = getViewModelInfo(tenantId, viewModelId);
            ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());
            ViewModel viewModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
            
            // 2. 获取基础信息
            String appName = viewModelInfo.getAppName();
            String tableName = viewModel.getCode();
            String etlDbName = getHologresDbName(getTenantInfo(tenantId));
            Long appId = getAppIdByName(tenantId, appName);
            Long dstDsId = getDstDsId(tenantId, tableName, etlDbName);
            
            List<String> streamJobs = new ArrayList<>();
            List<String> batchJobs = new ArrayList<>();
            
            // 3. 处理主对象 - 根据对象类型采用不同策略
            ViewModel.DataObject mainObject = viewModel.getObject();
            processMainObjectFlinkJobs(tenantId, viewModelInfo, mainObject, appId, dstDsId, 
                                     etlDbName, tableName, streamJobs, batchJobs);
            
            // 4. 处理关联对象 - 字段级关联对象
            processFieldRelatedObjectFlinkJobs(tenantId, viewModelInfo, viewModel, appId, dstDsId,
                                              etlDbName, tableName, streamJobs, batchJobs);
            
            // 5. 处理关联对象 - 模型级关联对象
            processModelRelatedObjectFlinkJobs(tenantId, viewModelInfo, viewModel, appId, dstDsId,
                                              etlDbName, tableName, streamJobs, batchJobs);
            
            // 6. 生成DAG调度脚本
            String dagScript = flinkDagBuilder.generateFlinkDag(tenantId, viewModel, streamJobs, batchJobs);
            
            // 7. 创建任务记录
            String taskId = createTaskRecord(tenantId, viewModelInfo, dagScript, streamJobs, batchJobs);
            
            // 8. 返回所有作业名称
            List<String> allJobs = new ArrayList<>();
            allJobs.addAll(streamJobs);
            allJobs.addAll(batchJobs);
            
            log.info("FlinkViewModelService.createFlinkBatchStreamTasks success, taskId={}, streamJobs={}, batchJobs={}", 
                    taskId, streamJobs, batchJobs);
            return allJobs;
            
        } catch (Exception e) {
            log.error("FlinkViewModelService.createFlinkBatchStreamTasks failed", e);
            throw new QanatBizException("创建Flink批流任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建Hologres表和全量同步
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createHologresTableAndFullSync(String tenantId, Long viewModelId, String batchJobs) {
        log.info("FlinkViewModelService.createHologresTableAndFullSync start, tenantId={}, viewModelId={}", tenantId, viewModelId);
        
        try {
            // 1. 获取视图模型信息
            ViewModelInfo viewModelInfo = getViewModelInfo(tenantId, viewModelId);
            ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());
            ViewModel viewModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
            
            // 2. 获取租户默认Hologres数据库
            TenantInfo tenantInfo = getTenantInfo(tenantId);
            String hologresDbName = getHologresDbName(tenantInfo);
            
            // 3. 创建Hologres目标表
            createHologresTable(tenantId, viewModel, hologresDbName);
            
            // 4. 执行全量数据同步
            executeFullDataSync(tenantId, viewModel, hologresDbName);
            
            // 5. 验证数据一致性
            validateDataConsistency(tenantId, viewModel, hologresDbName);
            
            log.info("FlinkViewModelService.createHologresTableAndFullSync success");
            return true;
            
        } catch (Exception e) {
            log.error("FlinkViewModelService.createHologresTableAndFullSync failed", e);
            throw new QanatBizException("创建Hologres表和全量同步失败: " + e.getMessage());
        }
    }
    
    // ==================== 对象类型处理方法 ====================
    
    /**
     * 处理主对象的Flink作业创建
     * 参考ViewModelHandler中的主对象处理逻辑
     */
    private void processMainObjectFlinkJobs(String tenantId, ViewModelInfo viewModelInfo, 
                                           ViewModel.DataObject mainObject, Long appId, Long dstDsId,
                                           String etlDbName, String tableName, 
                                           List<String> streamJobs, List<String> batchJobs) {
        
        String objectType = mainObject.getType();
        String objectRef = mainObject.getRef();
        Long versionId = viewModelInfo.getVersionId();
        
        log.info("处理主对象: type={}, ref={}", objectType, objectRef);
        
        if ("metadata".equalsIgnoreCase(objectType)) {
            // metadata类型：对象表，通过DRC获取增量数据
            processMetadataMainObject(tenantId, viewModelInfo, mainObject, appId, dstDsId, 
                                    etlDbName, tableName, streamJobs, batchJobs);
            
        } else if ("table".equalsIgnoreCase(objectType)) {
            // table类型：数据表，直接从表获取数据
            processTableMainObject(tenantId, viewModelInfo, mainObject, appId, dstDsId,
                                  etlDbName, tableName, streamJobs, batchJobs);
            
        } else if ("component".equalsIgnoreCase(objectType)) {
            // component类型：组件对象，通过扩展机制处理
            processComponentMainObject(tenantId, viewModelInfo, mainObject, appId, dstDsId,
                                     etlDbName, tableName, streamJobs, batchJobs);
        } else {
            log.warn("未知的主对象类型: {}", objectType);
        }
    }
    
    /**
     * 处理metadata类型的主对象
     */
    private void processMetadataMainObject(String tenantId, ViewModelInfo viewModelInfo,
                                         ViewModel.DataObject mainObject, Long appId, Long dstDsId,
                                         String etlDbName, String tableName,
                                         List<String> streamJobs, List<String> batchJobs) {
        try {
            // 获取对象元数据
            JSONObject srcDsMetaJson = datasourceService.getTableMetaByDsUniqueName(tenantId, mainObject.getRef());
            JSONObject drcTopicInfo = srcDsMetaJson.getJSONObject("incrConf");
            
            if (drcTopicInfo != null) {
                String drcTopicName = drcTopicInfo.getString("topicName");
                String drcTopicNameBatch = drcTopicInfo.getString("topicNameBatch");
                
                // 创建实时流作业
                if (drcTopicName != null && !drcTopicName.isEmpty()) {
                    String streamJobName = "incrsync_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_v" + viewModelInfo.getVersionId();
                    createFlinkStreamJob(tenantId, viewModelInfo, streamJobName, mainObject, drcTopicName, false);
                    streamJobs.add(streamJobName);
                }
                
                // 创建批量作业
                if (drcTopicNameBatch != null && !drcTopicNameBatch.isEmpty()) {
                    String batchJobName = "incrsync_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_batch_v" + viewModelInfo.getVersionId();
                    createFlinkBatchJob(tenantId, viewModelInfo, batchJobName, mainObject, drcTopicNameBatch, true);
                    batchJobs.add(batchJobName);
                }
            }
            
        } catch (Exception e) {
            log.error("处理metadata主对象失败: {}", e.getMessage(), e);
            throw new QanatBizException("处理metadata主对象失败: " + e.getMessage());
        }
    }

    /**
     * 处理table类型的主对象
     */
    private void processTableMainObject(String tenantId, ViewModelInfo viewModelInfo,
                                      ViewModel.DataObject mainObject, Long appId, Long dstDsId,
                                      String etlDbName, String tableName,
                                      List<String> streamJobs, List<String> batchJobs) {
        try {
            // 获取表元数据
            JSONObject srcDsMetaJson = datasourceService.getTableMetaByDsName(tenantId, mainObject.getRef());
            String sysType = srcDsMetaJson.getString("sysType");

            if ("metric".equals(sysType)) {
                // metric表特殊处理：使用upsert模式
                String batchJobName = "fullsync_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_v" + viewModelInfo.getVersionId();
                createFlinkMetricTableJob(tenantId, viewModelInfo, batchJobName, mainObject, true);
                batchJobs.add(batchJobName);

            } else {
                // 普通表处理：创建增量同步作业
                JSONObject drcTopicInfo = srcDsMetaJson.getJSONObject("incrConf");
                if (drcTopicInfo != null) {
                    String streamJobName = "incrsync_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_v" + viewModelInfo.getVersionId();
                    String drcTopicName = drcTopicInfo.getString("topicName");
                    createFlinkStreamJob(tenantId, viewModelInfo, streamJobName, mainObject, drcTopicName, false);
                    streamJobs.add(streamJobName);
                }
            }

        } catch (Exception e) {
            log.error("处理table主对象失败: {}", e.getMessage(), e);
            throw new QanatBizException("处理table主对象失败: " + e.getMessage());
        }
    }

    /**
     * 处理component类型的主对象
     */
    private void processComponentMainObject(String tenantId, ViewModelInfo viewModelInfo,
                                          ViewModel.DataObject mainObject, Long appId, Long dstDsId,
                                          String etlDbName, String tableName,
                                          List<String> streamJobs, List<String> batchJobs) {
        try {
            // 查询组件扩展配置
            // 这里简化处理，实际应该查询Extension表
            String streamJobName = "incrsync_" + appId + "_" + dstDsId + "_" + mainObject.getCode() + "_v" + viewModelInfo.getVersionId();
            JSONObject streamTopicInfo = new JSONObject();
            streamTopicInfo.put("topicName", "component_topic_" + mainObject.getRef());

            createFlinkComponentJob(tenantId, viewModelInfo, streamJobName, mainObject, streamTopicInfo);
            streamJobs.add(streamJobName);

        } catch (Exception e) {
            log.error("处理component主对象失败: {}", e.getMessage(), e);
            throw new QanatBizException("处理component主对象失败: " + e.getMessage());
        }
    }

    /**
     * 处理字段级关联对象的Flink作业创建
     */
    private void processFieldRelatedObjectFlinkJobs(String tenantId, ViewModelInfo viewModelInfo,
                                                   ViewModel viewModel, Long appId, Long dstDsId,
                                                   String etlDbName, String tableName,
                                                   List<String> streamJobs, List<String> batchJobs) {

        // 处理主对象字段中的关联对象
        List<ViewModel.Field> mainObjectFields = viewModel.getObject().getFields();
        if (CollectionUtils.isNotEmpty(mainObjectFields)) {
            for (ViewModel.Field field : mainObjectFields) {
                if (field.getObject() != null) {
                    processFieldRelatedObject(tenantId, viewModelInfo, field, appId, dstDsId,
                                            etlDbName, tableName, streamJobs, batchJobs);
                }
            }
        }
    }

    /**
     * 处理单个字段关联对象
     */
    private void processFieldRelatedObject(String tenantId, ViewModelInfo viewModelInfo,
                                         ViewModel.Field field, Long appId, Long dstDsId,
                                         String etlDbName, String tableName,
                                         List<String> streamJobs, List<String> batchJobs) {

        ViewModel.RelatedDataObject relatedObject = field.getObject();
        String objectType = relatedObject.getType();
        String fieldCode = field.getCode();

        log.info("处理字段关联对象: field={}, type={}, ref={}", fieldCode, objectType, relatedObject.getRef());

        if ("table".equalsIgnoreCase(objectType)) {
            // table类型字段关联对象：创建聚合作业
            String jobName = "aggr_" + appId + "_" + dstDsId + "_" + fieldCode + "_v" + viewModelInfo.getVersionId();
            createFlinkTableAggrJob(tenantId, viewModelInfo, jobName, relatedObject, fieldCode, etlDbName, tableName);
            streamJobs.add(jobName);

        } else if ("component".equalsIgnoreCase(objectType)) {
            // component类型字段关联对象：处理组件流
            processFieldComponentObject(tenantId, viewModelInfo, field, appId, dstDsId, streamJobs);

        } else if ("metadata".equalsIgnoreCase(objectType)) {
            // metadata类型字段关联对象：处理对象引用
            processFieldMetadataObject(tenantId, viewModelInfo, field, appId, dstDsId, streamJobs);
        }
    }

    /**
     * 处理模型级关联对象的Flink作业创建
     */
    private void processModelRelatedObjectFlinkJobs(String tenantId, ViewModelInfo viewModelInfo,
                                                   ViewModel viewModel, Long appId, Long dstDsId,
                                                   String etlDbName, String tableName,
                                                   List<String> streamJobs, List<String> batchJobs) {

        List<ViewModel.RelatedDataObject> relatedObjects = viewModel.getRelatedObjects();
        if (CollectionUtils.isNotEmpty(relatedObjects)) {
            for (ViewModel.RelatedDataObject relatedObject : relatedObjects) {
                processModelRelatedObject(tenantId, viewModelInfo, relatedObject, appId, dstDsId,
                                        etlDbName, tableName, streamJobs, batchJobs);
            }
        }
    }

    /**
     * 处理单个模型关联对象
     */
    private void processModelRelatedObject(String tenantId, ViewModelInfo viewModelInfo,
                                         ViewModel.RelatedDataObject relatedObject, Long appId, Long dstDsId,
                                         String etlDbName, String tableName,
                                         List<String> streamJobs, List<String> batchJobs) {

        String objectType = relatedObject.getType();
        String objectCode = relatedObject.getCode();

        log.info("处理模型关联对象: code={}, type={}, ref={}", objectCode, objectType, relatedObject.getRef());

        if ("table".equalsIgnoreCase(objectType)) {
            // table类型模型关联对象
            processTableRelatedObject(tenantId, viewModelInfo, relatedObject, appId, dstDsId,
                                     etlDbName, tableName, streamJobs, batchJobs);

        } else if ("component".equalsIgnoreCase(objectType)) {
            // component类型模型关联对象
            processComponentRelatedObject(tenantId, viewModelInfo, relatedObject, appId, dstDsId,
                                        etlDbName, tableName, streamJobs, batchJobs);

        } else if ("metadata".equalsIgnoreCase(objectType)) {
            // metadata类型模型关联对象
            processMetadataRelatedObject(tenantId, viewModelInfo, relatedObject, appId, dstDsId,
                                       etlDbName, tableName, streamJobs, batchJobs);
        }
    }

    // ==================== Flink作业创建方法 ====================

    /**
     * 创建Flink流作业
     */
    private void createFlinkStreamJob(String tenantId, ViewModelInfo viewModelInfo, String jobName,
                                     Object dataObject, String topicName, boolean isBatch) {
        try {
            // 生成Flink SQL
            String flinkSql = flinkSqlBuilder.generateFlinkStreamJobSql(tenantId, dataObject, jobName, topicName, isBatch);

            // 创建Flink作业
            flinkService.createJob(tenantId, viewModelInfo.getAppName(), jobName, flinkSql, isBatch);

            log.info("创建Flink流作业成功: jobName={}, isBatch={}", jobName, isBatch);

        } catch (Exception e) {
            log.error("创建Flink流作业失败: jobName={}", jobName, e);
            throw new QanatBizException("创建Flink流作业失败: " + e.getMessage());
        }
    }

    /**
     * 创建Flink批量作业
     */
    private void createFlinkBatchJob(String tenantId, ViewModelInfo viewModelInfo, String jobName,
                                    Object dataObject, String topicName, boolean isBatch) {
        try {
            // 生成Flink SQL
            String flinkSql = flinkSqlBuilder.generateFlinkBatchJobSql(tenantId, dataObject, jobName, topicName);

            // 创建Flink作业
            flinkService.createJob(tenantId, viewModelInfo.getAppName(), jobName, flinkSql, true);

            log.info("创建Flink批量作业成功: jobName={}", jobName);

        } catch (Exception e) {
            log.error("创建Flink批量作业失败: jobName={}", jobName, e);
            throw new QanatBizException("创建Flink批量作业失败: " + e.getMessage());
        }
    }
}
