package com.aliyun.wormhole.qanat.service.metaq.check;

import com.alibaba.rocketmq.common.consumer.ConsumeFromWhere;
import com.alibaba.rocketmq.common.protocol.heartbeat.MessageModel;
import com.aliyun.wormhole.qanat.service.metaq.common.AbstractConsumer;
import com.taobao.ateye.annotation.AteyeInvoker;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class QanatCheckAggConsumer extends AbstractConsumer implements InitializingBean {

    private final static String QANAT_CHECK_AGG_TOPIC = "qanat_check_agg_sink";

    private final static String QANAT_CHECK_AGG_TAG = "*";

    private final static String INSTACE_NAME = "qanat_check_agg_task";

    private final static String group = "CID_qanat_check_agg_sink";

    private MessageModel messageModel = MessageModel.CLUSTERING;

    @Resource(name = "qanatCheckAggListener")
    private QanatCheckAggListener messageListener;

    @Override
    public Map<String, String> getTopicConfig() {
        Map<String, String> map = new HashMap<String, String>(4);
        map.put(QANAT_CHECK_AGG_TOPIC, QANAT_CHECK_AGG_TAG);
        return map;
    }

    @Override
    public String getGroup() {
        return group;
    }

    public void setMessageListener(QanatCheckAggListener messageListener) {
        this.messageListener = messageListener;
    }

    @Override
    public QanatCheckAggListener getMessageListener() {
        return messageListener;
    }

    @Override
    protected String getInstanceName() {
        return INSTACE_NAME;
    }

    @Override
    protected MessageModel getMessageModel() {
        return messageModel;
    }

    @Override
    public String getUniqueName() {
        return INSTACE_NAME;
    }

    @Override
    protected ConsumeFromWhere getConsumeFromWhere() {
        return ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET;
    }

    @AteyeInvoker(description = "设置metaq线程数", paraDesc = "线程数")
    public void setMetaqConsumerThreadNum(int max ,int min){
        this.consumer.setConsumeThreadMax(max);
        this.consumer.setConsumeThreadMin(min);
    }

    @AteyeInvoker(description = "获取metaq消费最大线程数")
    public int getMetaqConsumeThreadMax(){
        return this.consumer.getConsumeThreadMax();
    }

    @AteyeInvoker(description = "获取metaq消费最小线程数")
    public int getMetaqConsumeThreadMin(){
        return this.consumer.getConsumeThreadMin();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        start(5);
    }
}
