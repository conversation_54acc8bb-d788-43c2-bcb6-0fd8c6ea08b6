package com.aliyun.wormhole.qanat.service.impl;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastsql.DbType;
import com.alibaba.fastsql.sql.SQLUtils;
import com.alibaba.fastsql.sql.ast.SQLStatement;
import com.alibaba.fastsql.sql.ast.statement.SQLColumnDefinition;
import com.alibaba.fastsql.sql.dialect.mysql.ast.statement.MySqlCreateTableStatement;
import com.alibaba.fastsql.sql.visitor.SchemaStatVisitor;
import com.alibaba.fastsql.stat.TableStat.Column;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.DagPolicy;
import com.aliyun.wormhole.qanat.api.dto.BlinkJobRequest;
import com.aliyun.wormhole.qanat.api.dto.ResourcePackage;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.DatasourceRequest;
import com.aliyun.wormhole.qanat.api.dto.ConsumerInfo;
import com.aliyun.wormhole.qanat.api.dto.OdsSyncTaskResponse;
import com.aliyun.wormhole.qanat.api.dto.PgCheckAndCorrectTaskRequest;
import com.aliyun.wormhole.qanat.api.dto.PgCheckAndCorrectTaskRequest.DsInfo;
import com.aliyun.wormhole.qanat.api.dto.TaskInfoRequest;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.Pg94TaskService;
import com.aliyun.wormhole.qanat.api.service.TaskService;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.service.template.Pg94SyncTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * PG数仓同步服务
 * <AUTHOR>
 * 2019年7月23日
 */
@Slf4j
@Component
@HSFProvider(serviceInterface = Pg94TaskService.class)
public class Pg94TaskServiceImpl implements Pg94TaskService {
    
    @Resource
    private DatasourceService datasourceService;
    
    @Resource
    private DatasourceMapper datasourceMapper;
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private TaskService taskService;

    private DbInfo getDbInfoByName(String dbName) {
        DbInfoExample dbExample = new DbInfoExample();
        dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("db not found");
        }
        DbInfo dbInfo = dbs.get(0);
        return dbInfo;
    }

    @Override
    public DataResult<OdsSyncTaskResponse> createCheckAndCorrectTask4Pg(PgCheckAndCorrectTaskRequest taskReq) {
        log.info("start createOdsSyncTask4Adb3({})", taskReq);
        DataResult<OdsSyncTaskResponse> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
            OdsSyncTaskResponse resp = new OdsSyncTaskResponse();
            //处理src数据源，如果没有则创建一个数据源
            DsInfo srcDs = taskReq.getSrcDs();
            String srcDsName = StringUtils.isNotBlank(srcDs.getDsName()) ? 
                    srcDs.getDsName() : (srcDs.getDsType() + "_" + taskReq.getTableName());
            JSONObject srcDsMetaJson  = getDsMeta("", srcDs, srcDsName, taskReq.getOperateEmpid());
            
            String dstDsName = taskReq.getPgTableName();
            
            //获取任务执行需要的consumerId
            Map<String, ConsumerInfo> consumerMap = getConsumerMapForOdsSync4Pg(taskReq, srcDsMetaJson);
                resp.setConsumerMap(consumerMap);
            
            //处理blink check任务
            new Thread(() -> {
                processBlinkCheckComputeJob4Pg(taskReq, srcDsMetaJson, dstDsName, consumerMap.get("check").getConsumerId());
                processBlinkCheckAllJob4Pg(taskReq, srcDsMetaJson);
                processBlinkCheckCorrectJob4Pg(taskReq, srcDsMetaJson, consumerMap.get("correct").getConsumerId());
            }).start();
                
            //新增全量对账DAG任务
            Long taskId = processPgCheckAllDAGTask(taskReq);
            if (taskId == null) {
                throw new QanatBizException("任务创建失败");
            }
            resp.setCheckAllTaskId(taskId);
                
            result.setData(resp);
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("createOdsSyncTask4Adb3 failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("createOdsSyncTask4Adb3 failed, error={}", e.getMessage(), e);
        }
        return result;
    }

    private Map<String, ConsumerInfo> getConsumerMapForOdsSync4Pg(PgCheckAndCorrectTaskRequest taskReq, JSONObject srcDsMetaJson) {
        String tableName = taskReq.getTableName();
        
        Map<String, ConsumerInfo> consumerMap = new HashMap<>();

        String correctConsumerId = "CID_qanat-pg_correct__" + tableName;
        ConsumerInfo correctConsumer = new ConsumerInfo("qanat_pg94_id_correct_topic", correctConsumerId);
        consumerMap.put("correct", correctConsumer);

        String checkConsumerId = "CID_qanat-pg_check__" + tableName;
        ConsumerInfo checkConsumer = new ConsumerInfo("qanat_pg94_check_id_midlayer_topic", checkConsumerId);
        consumerMap.put("check", checkConsumer);
        
        return consumerMap;
    }

    private Long processPgCheckAllDAGTask(PgCheckAndCorrectTaskRequest taskReq) {
        //先判断该表是否有odps数据源，没有则生成一个
        String chkAllJobName = "pg_check_all_" + taskReq.getTableName();
        String chkComputeJobName = "pg_check_compute_" + taskReq.getTableName();
        String chkCorrectJobName = "pg_check_correct_" + taskReq.getTableName();
        String dagScript = String.format(
            Pg94SyncTemplate.CHECK_ALL_DAG_SCRIPT
            , taskReq.getTableName()
            , chkAllJobName
            , chkAllJobName
            , chkComputeJobName
            , chkComputeJobName
            , chkCorrectJobName
            , chkCorrectJobName);
        TaskInfoRequest taskInfo = new TaskInfoRequest();
        taskInfo.setTenantId(taskReq.getTenantId());
        taskInfo.setAppName(taskReq.getAppName());
        taskInfo.setDagScript(dagScript);
        taskInfo.setName("DAG_checkAll" + taskReq.getTableName());
        taskInfo.setOperateEmpid(taskReq.getOperateEmpid());
        taskInfo.setTaskDesc("DAG_checkAll" + taskReq.getTableName());
        taskInfo.setPolicy(DagPolicy.ODS.toString());
        Long taskId = taskService.createDAGTask(taskInfo);
        log.info("create qanat task[{}] finished", taskId);
        return taskId;
    }

    private void dropExistedJob(String tenantId, String appName, String jobName) {
        if (blinkService.isJobExists(tenantId, appName, jobName)) {
            blinkService.stopJob(tenantId, appName, jobName);
            log.info("job:{} is stopped", jobName);
            blinkService.offlineJob(tenantId, appName, jobName);
            log.info("job:{} is offlined", jobName);
            blinkService.deleteJob(tenantId, appName, jobName);
            log.info("job:{} is deleted", jobName);
        }
    }

    private void processBlinkCheckComputeJob4Pg(PgCheckAndCorrectTaskRequest taskReq, JSONObject srcDsMetaJson, String dstDsName, String checkConsumerId) {
        String jobName = "pg_check_compute_" + taskReq.getTableName();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        String pkField = srcDsMetaJson.getString("pk");
        String ddlSql = srcDsMetaJson.getString("create_ddl");
        ddlSql = ddlSql.replaceAll("`", "");
        DbType dbType = DbType.mysql;
        List<SQLStatement> stmtList = SQLUtils.parseStatements(ddlSql, dbType);
        SQLStatement stmt = stmtList.get(0);

        MySqlCreateTableStatement createStmt = (MySqlCreateTableStatement)stmt;
        List<String> colNameTypeList = new ArrayList<>();
        List<String> colNameList = new ArrayList<>();
        List<String> colNameWithAliasBList = new ArrayList<>();
        List<String> colNameWithAliasCList = new ArrayList<>();
        for (SQLColumnDefinition col : createStmt.getColumnDefinitions()) {
                colNameTypeList.add("`" + col.getColumnName() + "` " + (col.getDataType().getName().equalsIgnoreCase("datetime")?"timestamp":col.getDataType().getName()));
                colNameList.add(col.getColumnName());
                colNameWithAliasBList.add("b.`" + col.getColumnName() + "`");
                colNameWithAliasCList.add("c.`" + col.getColumnName() + "`");
        }
        
        String sql = String.format(Pg94SyncTemplate.BLINK_CHECK_COMPUTE_SQL
            , taskReq.getOperateEmpid()
            , sdf.format(new Date())
            , taskReq.getTaskDesc() + " for Check"
            , taskReq.getTableName()
            , checkConsumerId
            , StringUtils.join(colNameTypeList, ",")
            , pkField
            , srcDsMetaJson.getString("dsType").equalsIgnoreCase("tddl") ? tddlWithClause(srcDsMetaJson) : rdsWithClause(srcDsMetaJson)
            , StringUtils.join(colNameTypeList, ",")
            , pkField
            , dstDsName
            , taskReq.getTableName()
            , StringUtils.join(colNameList, ",")
            , StringUtils.join(colNameWithAliasBList, ",")
            , StringUtils.join(colNameWithAliasCList, ",")
            , pkField
            , pkField
            , taskReq.getTableName()
            , taskReq.getTableName()
            );
        log.info("blink sla sql=[{}]", sql);
        
        //删除即存同名任务
        dropExistedJob(taskReq.getTenantId(), taskReq.getAppName(), jobName);
        
        BlinkJobRequest req = new BlinkJobRequest();
        req.setTenantId(taskReq.getTenantId());
        req.setAppName(taskReq.getAppName());
        req.setJobName(jobName);
        req.setSql(sql);
        req.setFolderName("/qanat/pg94_check/" + taskReq.getTableName() + "/");
        req.setPackages(blinkService.getBlinkExtensionsByPackage(taskReq.getTenantId(), ResourcePackage.BLINK_UDTF, ResourcePackage.BLINK_RDS_PG));
        if (taskReq.getBlinkConf() != null && StringUtils.isNotBlank(taskReq.getBlinkConf().getClusterId())) {
            req.setClusterId(taskReq.getBlinkConf().getClusterId());
        }
        if (taskReq.getBlinkConf() != null && StringUtils.isNotBlank(taskReq.getBlinkConf().getQueueName())) {
            req.setQueueName(taskReq.getBlinkConf().getQueueName());
        }
        req.setEngineVersion("blink-3.4.2");
        boolean flag = blinkService.createJob(req);
        if (!flag) {
            throw new QanatBizException("blink job:" + jobName + " create failed");
        }
        log.info("create blink job[{}] finished", jobName);
        
        //update planjson
        String planJson = blinkService.getPlanJson(taskReq.getTenantId(), taskReq.getAppName(), jobName, null, false);
        log.info("job:{} planjson={}", jobName, planJson);
        
        blinkService.updateJob(taskReq.getTenantId(), taskReq.getAppName(), jobName, null, planJson, null, null);
        log.info("update blink job planjson finished", jobName);
        
        flag = blinkService.commitJob(taskReq.getTenantId(), taskReq.getAppName(), jobName);
//        if (!flag) {
//            throw new QanatBizException("blink job:" + jobName + " commit failed");
//        }
        log.info("commit blink job[{}] finished", jobName);
    }

    private void processBlinkCheckAllJob4Pg(PgCheckAndCorrectTaskRequest taskReq, JSONObject srcDsMetaJson) {
        String jobName = "pg_check_all_" + taskReq.getTableName();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        String pkField = srcDsMetaJson.getString("dsType").equalsIgnoreCase("tddl") ? "pk" : srcDsMetaJson.getString("pk");
        
        String sql = String.format(Pg94SyncTemplate.BLINK_CHECK_ALL_SQL
            , taskReq.getOperateEmpid()
            , sdf.format(new Date())
            , taskReq.getTaskDesc() + " for Check All"
            , pkField
            , rdsWithClause4Scan(srcDsMetaJson, taskReq.getWhereClause())
            , taskReq.getTableName()
            , pkField
            );
        log.info("blink check all sql=[{}]", sql);
        
        //删除即存同名任务
        dropExistedJob(taskReq.getTenantId(), taskReq.getAppName(), jobName);
        
        BlinkJobRequest req = new BlinkJobRequest();
        req.setTenantId(taskReq.getTenantId());
        req.setAppName(taskReq.getAppName());
        req.setJobName(jobName);
        req.setSql(sql);
        req.setFolderName("/qanat/pg94_check/" + taskReq.getTableName() + "/");
        req.setPackages(blinkService.getBlinkExtensionsByPackage(taskReq.getTenantId(), ResourcePackage.BLINK_MYSQL_SCAN));
        req.setIsBatch(true);
        if (taskReq.getBlinkConf() != null && StringUtils.isNotBlank(taskReq.getBlinkConf().getClusterId())) {
            req.setClusterId(taskReq.getBlinkConf().getClusterId());
        }
        if (taskReq.getBlinkConf() != null && StringUtils.isNotBlank(taskReq.getBlinkConf().getQueueName())) {
            req.setQueueName(taskReq.getBlinkConf().getQueueName());
        }
        req.setEngineVersion("blink-3.6.3");
        boolean flag = blinkService.createJob(req);
        if (!flag) {
            throw new QanatBizException("blink job:" + jobName + " create failed");
        }
        log.info("create blink job[{}] finished", jobName);
        
        //update planjson
        String planJson = blinkService.getPlanJson(taskReq.getTenantId(), taskReq.getAppName(), jobName, null, false);
        log.info("job:{} planjson={}", jobName, planJson);
        
        blinkService.updateJob(taskReq.getTenantId(), taskReq.getAppName(), jobName, null, planJson, null, null);
        log.info("update blink job planjson finished", jobName);
        
        flag = blinkService.commitJob(taskReq.getTenantId(), taskReq.getAppName(), jobName);
//        if (!flag) {
//            throw new QanatBizException("blink job:" + jobName + " commit failed");
//        }
        log.info("commit blink job[{}] finished", jobName);
    }

    private void processBlinkCheckCorrectJob4Pg(PgCheckAndCorrectTaskRequest taskReq, JSONObject srcDsMetaJson, String consumerId) {
        String jobName = "pg_check_correct_" + taskReq.getTableName();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        String pkField = srcDsMetaJson.getString("pk");
        String ddlSql = srcDsMetaJson.getString("create_ddl");
        ddlSql = ddlSql.replaceAll("`", "");
        DbType dbType = DbType.mysql;
        List<SQLStatement> stmtList = SQLUtils.parseStatements(ddlSql, dbType);
        SQLStatement stmt = stmtList.get(0);

        MySqlCreateTableStatement createStmt = (MySqlCreateTableStatement)stmt;
        List<String> colNameTypeList = new ArrayList<>();
        List<String> colNameList = new ArrayList<>();
        for (SQLColumnDefinition col : createStmt.getColumnDefinitions()) {
            colNameTypeList.add("`" + col.getColumnName() + "` " + (col.getDataType().getName().equalsIgnoreCase("datetime")?"timestamp":col.getDataType().getName()));
            colNameList.add(col.getColumnName());
        }
        
        String sql = String.format(Pg94SyncTemplate.BLINK_CHECK_CORRECT_SQL
            , taskReq.getOperateEmpid()
            , sdf.format(new Date())
            , taskReq.getTaskDesc() + " for Correct"
            , taskReq.getTableName()
            , consumerId
            , StringUtils.join(colNameTypeList, ",")
            , pkField
            , srcDsMetaJson.getString("dsType").equalsIgnoreCase("tddl") ? tddlWithClause(srcDsMetaJson) : rdsWithClause(srcDsMetaJson)
            , StringUtils.join(colNameTypeList, ",")
            , pkField
            , taskReq.getPgTableName()
            , StringUtils.join(colNameList, ",")
            );
        log.info("blink check correct sql=[{}]", sql);
        
        //删除即存同名任务
        dropExistedJob(taskReq.getTenantId(), taskReq.getAppName(), jobName);
        
        BlinkJobRequest req = new BlinkJobRequest();
        req.setTenantId(taskReq.getTenantId());
        req.setAppName(taskReq.getAppName());
        req.setJobName(jobName);
        req.setSql(sql);
        req.setFolderName("/qanat/pg94_check/" + taskReq.getTableName() + "/");
        req.setPackages(blinkService.getBlinkExtensionsByPackage(taskReq.getTenantId(), ResourcePackage.BLINK_RDS_PG));
        if (taskReq.getBlinkConf() != null && StringUtils.isNotBlank(taskReq.getBlinkConf().getClusterId())) {
            req.setClusterId(taskReq.getBlinkConf().getClusterId());
        }
        if (taskReq.getBlinkConf() != null && StringUtils.isNotBlank(taskReq.getBlinkConf().getQueueName())) {
            req.setQueueName(taskReq.getBlinkConf().getQueueName());
        }
        req.setEngineVersion("blink-3.4.2");
        boolean flag = blinkService.createJob(req);
        if (!flag) {
            throw new QanatBizException("blink job:" + jobName + " create failed");
        }
        log.info("create blink job[{}] finished", jobName);
        
        //update planjson
        String planJson = blinkService.getPlanJson(taskReq.getTenantId(), taskReq.getAppName(), jobName, null, false);
        log.info("job:{} planjson={}", jobName, planJson);
        
        blinkService.updateJob(taskReq.getTenantId(), taskReq.getAppName(), jobName, null, planJson, null, null);
        log.info("update blink job planjson finished", jobName);
        
        flag = blinkService.commitJob(taskReq.getTenantId(), taskReq.getAppName(), jobName);
//        if (!flag) {
//            throw new QanatBizException("blink job:" + jobName + " commit failed");
//        }
        log.info("commit blink job[{}] finished", jobName);
    }

    private String tddlWithClause(JSONObject srcDsMetaJson) {
        String clause = "type='Tddl',\n" +
                        "appName='" + srcDsMetaJson.getString("appName") + "',\n" +
                        "tableName='" + srcDsMetaJson.getString("table") + "',\n" +
                        "isSharding='" + srcDsMetaJson.getString("isSharding") + "',\n" +
                        "accessKey='" + srcDsMetaJson.getString("accessKey") + "',\n" +
                        "secretKey='" + srcDsMetaJson.getString("secretKey") + "'" 
                        ;
        return clause;
    }

    private String rdsWithClause(JSONObject srcDsMetaJson) {
        String clause = "type='rds',\n" +
                "url='" + srcDsMetaJson.getString("jdbcUrl") + "',\n" +
                "tableName='" + srcDsMetaJson.getString("table") + "',\n" +
                "userName='" + srcDsMetaJson.getString("username") + "',\n" +
                "password='" + srcDsMetaJson.getString("password") + "'" 
                ;
        return clause;
    }

    private String rdsWithClause4Scan(JSONObject srcDsMetaJson, String whereClause) {
        String clause = "url='" + srcDsMetaJson.getString("jdbcUrl") + "',\n" +
                "tableName='" + srcDsMetaJson.getString("table") + "',\n" +
                "userName='" + srcDsMetaJson.getString("username") + "',\n" +
                "password='" + srcDsMetaJson.getString("password") + "',\n" +
                (StringUtils.isBlank(whereClause) ? "" : ("whereClause='" + whereClause.replaceAll("'", "''") + "',\n")) +
                "batchSize='10000'" 
                ;
        return clause;
    }

    private JSONObject getDsMeta(String requestId, DsInfo dsInfo, String dsName, String operateEmpid) {
        JSONObject srcDsMetaJson = null;
        if (StringUtils.isNotBlank(dsInfo.getDsName())) {
            DatasourceExample example = new DatasourceExample();
            example.createCriteria().andDsNameEqualTo(dsInfo.getDsName()).andIsDeletedEqualTo(0L);
            List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isEmpty(dsList)) {
                throw new QanatBizException("Datasource:" + dsInfo.getDsName() + " doesn't exists!");
            }
            Datasource ds = dsList.get(0);
            srcDsMetaJson = JSON.parseObject(ds.getMeta());
            srcDsMetaJson.put("dsType", ds.getDsType());
            if (StringUtils.isNotBlank(ds.getDbName())) {
                DbInfo dbInfo = getDbInfoByName(ds.getDbName());
                JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
                srcDsMetaJson.put("jdbcUrl", dbMetaJson.getString("jdbcUrl"));
                srcDsMetaJson.put("username", dbMetaJson.getString("username"));
                srcDsMetaJson.put("password", dbMetaJson.getString("password"));
            }
        } else {
            srcDsMetaJson = JSON.parseObject(dsInfo.getMeta());
            DatasourceExample example = new DatasourceExample();
            example.createCriteria().andDsNameEqualTo(dsName).andIsDeletedEqualTo(0L);
            List<Datasource> dsList = datasourceMapper.selectByExample(example);
            Long dsId = null;
            if (CollectionUtils.isEmpty(dsList)) {
                DatasourceRequest dsReq = new DatasourceRequest();
                dsReq.setDsName(dsName);
                dsReq.setDsDesc(dsName);
                dsReq.setDsType(dsInfo.getDsType());
                dsReq.setMeta(dsInfo.getMeta());
                dsReq.setOperateEmpid(operateEmpid);
                dsReq.setRemark(dsName);
                dsReq.setRequestId(requestId);
                DataResult<Long> result = datasourceService.createDatasource(dsReq);
                log.info("[{}]create Ds[{}] finished", requestId, dsName);
                dsId = result.getData();
            } else {
                dsId = dsList.get(0).getId();
            }
            
            Datasource ds = datasourceMapper.selectByPrimaryKey(dsId);
            srcDsMetaJson = JSON.parseObject(ds.getMeta());
            srcDsMetaJson.put("dsType", ds.getDsType());
            if (StringUtils.isNotBlank(ds.getDbName())) {
                DbInfo dbInfo = getDbInfoByName(ds.getDbName());
                JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
                srcDsMetaJson.put("jdbcUrl", dbMetaJson.getString("jdbcUrl"));
                srcDsMetaJson.put("username", dbMetaJson.getString("username"));
                srcDsMetaJson.put("password", dbMetaJson.getString("password"));
            }
        }
        String pk = "id";
        String ddlSql = srcDsMetaJson.getString("create_ddl");
        if (StringUtils.isNotBlank(ddlSql)) {
            ddlSql = ddlSql.replaceAll("`", "");
            DbType dbType = DbType.mysql;
            List<SQLStatement> stmtList = SQLUtils.parseStatements(ddlSql, dbType);
            SQLStatement stmt = stmtList.get(0);
            SchemaStatVisitor statVisitor = SQLUtils.createSchemaStatVisitor(dbType);
            stmt.accept(statVisitor);
            List<String> pkList = statVisitor.getColumns().stream().filter(column -> column.isPrimaryKey()).map(Column::getName).collect(Collectors.toList());
            pk = pkList.get(0);
        }
        srcDsMetaJson.put("pk", pk);
        return srcDsMetaJson;
    }
}