package com.aliyun.wormhole.qanat.service.mdp;

import java.util.List;

public class AnalyticModel {

    private Model model;

    public Model getModel() {
        return model;
    }

    public void setModel(Model model) {
        this.model = model;
    }

    public static class Model {

        private String code;
        private String name;
        private String domain;
        private String metaRef;
        private List<Field> fields;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDomain() {
            return domain;
        }

        public void setDomain(String domain) {
            this.domain = domain;
        }

        public String getMetaRef() {
            return metaRef;
        }

        public void setMetaRef(String metaRef) {
            this.metaRef = metaRef;
        }
        
        public List<Field> getFields() {
            return fields;
        }

        public void setFields(List<Field> fields) {
            this.fields = fields;
        }
    }

    public static class Field {
        private String name;
        private String code;
        private String type;
        private String metaRef;
        private String enums;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getMetaRef() {
            return metaRef;
        }

        public void setMetaRef(String metaRef) {
            this.metaRef = metaRef;
        }
        
        public String getEnums() {
            return enums;
        }

        public void setEnums(String enums) {
            this.enums = enums;
        }
    }
}
