package com.aliyun.wormhole.qanat.process;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.CreateOdsRequest;
import com.aliyun.wormhole.qanat.api.service.TaskService;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfo;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceDsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TenantInfoMapper;
import com.aliyun.wormhole.qanat.service.datatube.DatatubeHandler;
import com.aliyun.wormhole.qanat.service.schedulerx.SchedulerXJobService;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 管道实例ODSDAG重构任务
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class DatatubeInstanceOdsRebuildProcessor extends JavaProcessor {
    
    @Resource
    private DatatubeHandler datatubeHandler;
    
    @Resource 
    private TenantInfoMapper tenantInfoMapper;
    
    @Resource 
    private DatatubeInstanceMapper datatubeInstanceMapper;
    
    @Resource 
    private DatatubeInstanceDsRelationMapper datatubeInstanceDsRelationMapper;
    
    @Resource 
    private DatasourceMapper dsInfoMapper;
    
    @Resource 
    private TaskInfoMapper taskInfoMapper;
    
    @Resource
    private SchedulerXJobService schedulerXJobService;
    
    @Resource
    private TaskService taskService;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
            log.info("DatatubeInstanceOdsRebuildProcessor, param=[]", context.getJobParameters());
            String tenantId = paramsJson.getString("tenantId");
            if (StringUtils.isBlank(tenantId)) {
            	log.info("tenantId is empty");
                return new ProcessResult(false, "tenantId is empty");
            }
            
    		TenantInfoExample tiExample = new TenantInfoExample();
        	tiExample.createCriteria().andTenantIdEqualTo(tenantId);
        	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
        	if (CollectionUtils.isEmpty(tenantList)) {
            	return new ProcessResult(false, "no datatube instances found");
        	}
        	
            JSONArray datatubeInstIdArray = paramsJson.getJSONArray("datatubeInstIds");
            List<Long> datatubeInstIds = new ArrayList<>();
            if (datatubeInstIdArray != null && datatubeInstIdArray.size() > 0) {
            	for (int i = 0 ; i < datatubeInstIdArray.size(); i++) {
                	datatubeInstIds.add(datatubeInstIdArray.getLong(i));
            	}
            }
            
            DatatubeInstanceExample example = new DatatubeInstanceExample();
            DatatubeInstanceExample.Criteria criteria = example.createCriteria();
            criteria.andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andProviderEqualTo("ods").andIsTestEqualTo(0L);
            if (CollectionUtils.isNotEmpty(datatubeInstIds)) {
            	criteria.andIdIn(datatubeInstIds);
            }
            List<DatatubeInstance> datatubeInstList = datatubeInstanceMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(datatubeInstList)) {
            	return new ProcessResult(false, "no datatube instances found");
            }
            
        	for (DatatubeInstance inst : datatubeInstList) {
            	log.info("datatube[{}-{}-{}] update started", inst.getId(), inst.getName(), inst.getProviderId());
            	try {
            		CreateOdsRequest req = new CreateOdsRequest();
            		req.setOperateEmpid("schedulerx");
            		datatubeHandler.modifyOdsDatatube(inst.getId(), req);
                	log.info("datatube[{}-{}-{}] update DAG finished", inst.getId(), inst.getName(), inst.getProviderId());
            	} catch(Exception e) {
            		log.error("datatube[{}-{}-{}] update DAG failed, error={}", inst.getId(), inst.getName(), inst.getProviderId(), e.getMessage(), e);
            	}
        	}
        } catch (QanatBizException e) {
            log.error("DatatubeInstanceOdsRebuildProcessor任务调度异常:{}", e.getMessage());
            return new ProcessResult(false, e.getMessage());
        } catch (Exception e) {
            log.error("DatatubeInstanceOdsRebuildProcessor任务调度异常", e);
            return new ProcessResult(false, e.getMessage());
        }
        return new ProcessResult(true);
    }
    
    @Override
    public void kill(JobContext context) {
        
    }
}