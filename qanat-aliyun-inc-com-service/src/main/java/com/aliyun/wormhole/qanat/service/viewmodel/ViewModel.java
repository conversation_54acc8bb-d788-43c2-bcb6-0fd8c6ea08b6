package com.aliyun.wormhole.qanat.service.viewmodel;

import java.util.List;

import lombok.Data;

@Data
public class ViewModel {
    private String code;
    private String name;
    private String type;
    private DataObject object;
    private List<RelatedDataObject> relatedObjects;
    private List<AddOn> addOns;
	private boolean dynamic;
	private Settings settings = new Settings();

    @Data
    public static class DataObject {
    	 private String code;
    	 private String type;
    	 private String ref;
    	 private List<Field> fields;
    	 private String filter;
    }

    @Data
    public static class RelatedDataObject extends DataObject {
    	 private String relationType;
    	 private List<Relation> relations;
         private String aggrFunc;
         private String lookupFrom = "ods";
//         private boolean lookup = false;
    }

    @Data
    public static class Field {
        private String code;
        private String name;
        private String type;
        private String ref;
        private String refFields;
        private boolean pk = false;
        private boolean enums = false;
        private RelatedDataObject object;
        private boolean multivalue = false;
        private String mvToken;
        private boolean func = false;
        private String expr;
        
        public boolean isCompute() {
        	return (ref != null && ref.startsWith("exp#")) || (ref != null && ref.contains("("));
        }
    }

    @Data
    public static class Relation {
        private String field;
        private String relatedField;
        private String op;
    }

    @Data
    public static class AddOn {
    	 private String code;
    	 private String type;
    	 private String ref;
    }
    
    @Data
    public static class Settings {
    	 private boolean check = false;
    	 private boolean fullLink = false;
    	 private boolean sla = false;
    	 private int incrCheckDelayMs = 10000;
    	 private int rdsScanBatchSize = 100000;
    	 private int fullSyncBatchSize = 10240;
    	 private int fullSyncParallelism = 10;
    	 private String guaranteePolicy = "daily_rebuild";
    	 private String timeExpression;
    	 private boolean sqlOptimize = false;
    	 private String correctPolicy = "offhand";
    	 private boolean lookupOptimize = true;
    	 private String batchCheckPolicy = "fullcolumns";
    	 private String batchCheckDateField = "gmt_modified";
    	 private int batchCheckDays = 1;
    	 private String distributeKey;
    	 private String multiDbSyncFilter;
        private boolean objectNoFulljoin = false;
    }
}