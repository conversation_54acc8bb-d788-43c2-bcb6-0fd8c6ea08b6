package com.aliyun.wormhole.qanat.service.kafka;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.service.KafkaManagementService;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelation;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ResourceExample;
import com.aliyun.wormhole.qanat.dal.mapper.AppResourceRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ResourceMapper;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.alikafka.model.v20190916.CreateConsumerGroupRequest;
import com.aliyuncs.alikafka.model.v20190916.CreateConsumerGroupResponse;
import com.aliyuncs.alikafka.model.v20190916.CreateTopicRequest;
import com.aliyuncs.alikafka.model.v20190916.CreateTopicResponse;
import com.aliyuncs.alikafka.model.v20190916.DeleteConsumerGroupRequest;
import com.aliyuncs.alikafka.model.v20190916.DeleteConsumerGroupResponse;
import com.aliyuncs.alikafka.model.v20190916.GetConsumerListRequest;
import com.aliyuncs.alikafka.model.v20190916.GetConsumerListResponse;
import com.aliyuncs.alikafka.model.v20190916.GetTopicListRequest;
import com.aliyuncs.alikafka.model.v20190916.GetTopicListResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@HSFProvider(serviceInterface = KafkaManagementService.class)
public class KafkaManagementServiceImpl implements KafkaManagementService {
	
	@Resource
	private AppResourceRelationMapper appResourceRelationMapper;
	
	@Resource
	private ResourceMapper resourceMapper;
	
	@Resource
	private DbInfoMapper dbInfoMapper;
	
	@Resource
	private DatasourceMapper dsInfoMapper;
	
	private ExecutorService createTopicPool = new ThreadPoolExecutor(1, 1, 0, TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(1000));

	@Override
	public Boolean createTopic(String tenantId, String appName, String topic) {
		return this.createTopic(tenantId, appName, topic, 3);
	}

	@Override
	public Boolean createTopic(String tenantId, String appName, String topic, Integer partitions) {
		if (checkTopicExists(tenantId, appName, topic)) {
			return true;
		}
		JSONObject metaJson = getKafkaConfByAppName(tenantId, appName);
		IAcsClient iAcsClient = buildAcsClient(metaJson);
		CreateTopicRequest req = new CreateTopicRequest();
		req.setInstanceId(metaJson.getString("instanceId"));
		req.setPartitionNum(partitions + "");
		req.setTopic(topic);
		req.setRemark("from api");
		try {
			Future<Boolean> task = createTopicPool.submit(
				new Callable<Boolean>() {
					@Override
					public Boolean call() throws Exception {
						CreateTopicResponse resp = iAcsClient.getAcsResponse(req);
						log.info("resp:{}", JSON.toJSONString(resp));
						if (resp != null && resp.getSuccess() && 200 == resp.getCode()) {
							return true;
						} else {
							return false;
						}
					}
				}
			);
			if (task.get()) {
				try {
		        	JSONObject appKafkaConf = getKafkaConfByAppName(tenantId, appName);
			        Datasource drcDs = new Datasource();
			        drcDs.setDsName("kafka_" + topic);
			        drcDs.setDsType("kafka");
			        drcDs.setTableName(topic);
			        drcDs.setIsDeleted(0L);
			        drcDs.setDbName(appKafkaConf.getString("dbName"));
			        drcDs.setGmtModified(new Date());
			        drcDs.setModifyEmpid("viewmodel");
			        drcDs.setGmtCreate(new Date());
			        drcDs.setCreateEmpid("viewmodel");
			        drcDs.setTenantId(tenantId);
			        dsInfoMapper.insert(drcDs);
				} catch(Exception e) {}
			}
			return true;
		} catch (Exception e) {
            //处理自己的异常逻辑，请注意异常中的requestId字段，排查问题时，需要提供该值给服务端
            if (e instanceof ServerException) {
                ServerException serverException = (ServerException) e;
                log.error("createTopic failed, kafka requestId:{} errorCode:{} message:{}", serverException.getRequestId(), serverException.getErrCode(), serverException.getMessage());
            } else if (e instanceof ClientException) {
            	ClientException clientException = (ClientException) e;
                log.error("createTopic failed, kafka requestId:{} errorCode:{} message:{}", clientException.getRequestId(), clientException.getErrCode(), clientException.getMessage());
            } else {
            	log.error("createTopic failed:{}", e.getMessage());
            }
		}
		return false;
	}

	@Override
	public Boolean createConsumerGroup(String tenantId, String appName, String consumerId) {
		if (checkConsumerIdExists(tenantId, appName, consumerId)) {
			return true;
		}
		JSONObject metaJson = getKafkaConfByAppName(tenantId, appName);
		IAcsClient iAcsClient = buildAcsClient(metaJson);
		CreateConsumerGroupRequest req = new CreateConsumerGroupRequest();
		req.setInstanceId(metaJson.getString("instanceId"));
		req.setConsumerId(consumerId);
		req.setRemark("from api");
		try {
			CreateConsumerGroupResponse resp = iAcsClient.getAcsResponse(req);
			log.info("resp:{}", JSON.toJSONString(resp));
			if (resp != null && resp.getSuccess() && 200 == resp.getCode()) {
				return true;
			}
		} catch (Exception e) {
            //处理自己的异常逻辑，请注意异常中的requestId字段，排查问题时，需要提供该值给服务端
            if (e instanceof ServerException) {
                ServerException serverException = (ServerException) e;
                log.error("createTopic failed, kafka requestId:{} errorCode:{} message:{}", serverException.getRequestId(), serverException.getErrCode(), serverException.getMessage());
            } else if (e instanceof ClientException) {
            	ClientException clientException = (ClientException) e;
                log.error("createTopic failed, kafka requestId:{} errorCode:{} message:{}", clientException.getRequestId(), clientException.getErrCode(), clientException.getMessage());
            } else {
            	log.error("createConsumer failed:{}", e.getMessage());
            }
		}
		return false;
	}

	@Override
	public Boolean createConsumerGroupFromDbInfo(String tenantId, String dbName, String consumerId) {
		if (checkConsumerIdExistsForDbInfo(tenantId, dbName, consumerId)) {
			return true;
		}
		JSONObject metaJson = getKafkaConfByDbName(tenantId, dbName);
		IAcsClient iAcsClient = buildAcsClient(metaJson);
		CreateConsumerGroupRequest req = new CreateConsumerGroupRequest();
		req.setInstanceId(metaJson.getString("instanceId"));
		req.setConsumerId(consumerId);
		req.setRemark("from api");
		try {
			CreateConsumerGroupResponse resp = iAcsClient.getAcsResponse(req);
			log.info("resp:{}", JSON.toJSONString(resp));
			if (resp != null && resp.getSuccess() && "200".equals(resp.getCode())) {
				return true;
			}
		} catch (Exception e) {
            //处理自己的异常逻辑，请注意异常中的requestId字段，排查问题时，需要提供该值给服务端
            if (e instanceof ServerException) {
                ServerException serverException = (ServerException) e;
                log.error("createTopic failed, kafka requestId:{} errorCode:{} message:{}", serverException.getRequestId(), serverException.getErrCode(), serverException.getMessage());
            } else if (e instanceof ClientException) {
            	ClientException clientException = (ClientException) e;
                log.error("createTopic failed, kafka requestId:{} errorCode:{} message:{}", clientException.getRequestId(), clientException.getErrCode(), clientException.getMessage());
            } else {
            	log.error("createConsumer failed:{}", e.getMessage(), e);
            }
		}
		return false;
	}

	@Override
	public Boolean deleteConsumerGroup(String tenantId, String appName, String consumerId) {
		JSONObject metaJson = getKafkaConfByAppName(tenantId, appName);
		IAcsClient iAcsClient = buildAcsClient(metaJson);
		DeleteConsumerGroupRequest req = new DeleteConsumerGroupRequest();
		req.setInstanceId(metaJson.getString("instanceId"));
		req.setConsumerId(consumerId);
		try {
			DeleteConsumerGroupResponse resp = iAcsClient.getAcsResponse(req);
			log.info("resp:{}", JSON.toJSONString(resp));
			if (resp != null && resp.getSuccess() && "200".equals(resp.getCode())) {
				return true;
			}
		} catch (Exception e) {
            //处理自己的异常逻辑，请注意异常中的requestId字段，排查问题时，需要提供该值给服务端
            if (e instanceof ServerException) {
                ServerException serverException = (ServerException) e;
                log.error("deleteConsumerGroup failed, kafka requestId:{} errorCode:{} message:{}", serverException.getRequestId()
                		,serverException.getErrCode(),serverException.getMessage());//本次请求的requestId
            } else if (e instanceof ClientException) {
            	ClientException clientException = (ClientException) e;
                log.error("deleteConsumerGroup failed, kafka requestId:{} errorCode:{} message:{}", clientException.getRequestId()
                		,clientException.getErrCode(),clientException.getMessage());//本次请求的requestId
            } else {
            	log.error("deleteConsumerGroup failed:{}", e.getMessage(), e);
            }
		}
		return false;
	}
	
	private Boolean checkTopicExists(String tenantId, String appName, String topic) {
		return getTopicList(tenantId, appName).contains(topic);
	}
	
	private List<String> getTopicList(String tenantId, String appName) {
		JSONObject metaJson = getKafkaConfByAppName(tenantId, appName);
		IAcsClient iAcsClient = buildAcsClient(metaJson);
		GetTopicListRequest req = new GetTopicListRequest();
		req.setInstanceId(metaJson.getString("instanceId"));
		req.setCurrentPage("0");
		req.setPageSize("500");
		try {
			GetTopicListResponse resp = iAcsClient.getAcsResponse(req);
			if (resp != null && resp.getSuccess() && CollectionUtils.isNotEmpty(resp.getTopicList())) {
				return resp.getTopicList().stream().map(e -> e.getTopic()).collect(Collectors.toList());
			}
		} catch (Exception e) {
            //处理自己的异常逻辑，请注意异常中的requestId字段，排查问题时，需要提供该值给服务端
            if (e instanceof ServerException) {
                ServerException serverException = (ServerException) e;
                log.error("createTopic failed, kafka requestId:{} errorCode:{} message:{}", serverException.getRequestId(), serverException.getErrCode(), serverException.getMessage());
            } else if (e instanceof ClientException) {
            	ClientException clientException = (ClientException) e;
                log.error("createTopic failed, kafka requestId:{} errorCode:{} message:{}", clientException.getRequestId(), clientException.getErrCode(), clientException.getMessage());
            } else {
            	log.error("getTopicList failed:{}", e.getMessage(), e);
            }
		}
		return new ArrayList<String>();
	}
	
	private Boolean checkConsumerIdExists(String tenantId, String appName, String consumerId) {
		return getConsumerIdList(tenantId, appName).contains(consumerId);
	}
	
	private Boolean checkConsumerIdExistsForDbInfo(String tenantId, String dbName, String consumerId) {
		return getConsumerIdListForDbInfo(tenantId, dbName).contains(consumerId);
	}
	
	@Override
	public List<String> getConsumerGroupList(String tenantId, String appName) {
		return this.getConsumerIdList(tenantId, appName);
	}
	
	private List<String> getConsumerIdList(String tenantId, String appName) {
		JSONObject metaJson = getKafkaConfByAppName(tenantId, appName);
		IAcsClient iAcsClient = buildAcsClient(metaJson);
		GetConsumerListRequest req = new GetConsumerListRequest();
		req.setInstanceId(metaJson.getString("instanceId"));
		try {
			GetConsumerListResponse resp = iAcsClient.getAcsResponse(req);
			if (resp != null && resp.getSuccess() && CollectionUtils.isNotEmpty(resp.getConsumerList())) {
				return resp.getConsumerList().stream().map(e -> e.getConsumerId()).collect(Collectors.toList());
			}
		} catch (Exception e) {
            //处理自己的异常逻辑，请注意异常中的requestId字段，排查问题时，需要提供该值给服务端
            if (e instanceof ServerException) {
                ServerException serverException = (ServerException) e;
                log.error("createTopic failed, kafka requestId:{} errorCode:{} message:{}", serverException.getRequestId(), serverException.getErrCode(), serverException.getMessage());
            } else if (e instanceof ClientException) {
            	ClientException clientException = (ClientException) e;
                log.error("createTopic failed, kafka requestId:{} errorCode:{} message:{}", clientException.getRequestId(), clientException.getErrCode(), clientException.getMessage());
            } else {
            	log.error("getConsumerIdList failed:{}", e.getMessage(), e);
            }
		}
		return new ArrayList<String>();
	}
	
	private List<String> getConsumerIdListForDbInfo(String tenantId, String dbName) {
		JSONObject metaJson = getKafkaConfByDbName(tenantId, dbName);
		IAcsClient iAcsClient = buildAcsClient(metaJson);
		GetConsumerListRequest req = new GetConsumerListRequest();
		req.setInstanceId(metaJson.getString("instanceId"));
		try {
			GetConsumerListResponse resp = iAcsClient.getAcsResponse(req);
			if (resp != null && resp.getSuccess() && CollectionUtils.isNotEmpty(resp.getConsumerList())) {
				return resp.getConsumerList().stream().map(e -> e.getConsumerId()).collect(Collectors.toList());
			}
		} catch (Exception e) {
            //处理自己的异常逻辑，请注意异常中的requestId字段，排查问题时，需要提供该值给服务端
            if (e instanceof ServerException) {
                ServerException serverException = (ServerException) e;
                log.error("createTopic failed, kafka requestId:{} errorCode:{} message:{}", serverException.getRequestId(), serverException.getErrCode(), serverException.getMessage());
            } else if (e instanceof ClientException) {
            	ClientException clientException = (ClientException) e;
                log.error("createTopic failed, kafka requestId:{} errorCode:{} message:{}", clientException.getRequestId(), clientException.getErrCode(), clientException.getMessage());
            } else {
            	log.error("getConsumerIdList failed:{}", e.getMessage(), e);
            }
		}
		return new ArrayList<String>();
	}

	private IAcsClient buildAcsClient(JSONObject metaJson) {
		try {
			DefaultProfile.addEndpoint(metaJson.getString("endPointName"), metaJson.getString("regionId"), metaJson.getString("productName"), metaJson.getString("domain"));
		} catch (ClientException e) {
			// log error
		}
		// 构造 Client
		IClientProfile profile = DefaultProfile.getProfile(metaJson.getString("regionId"), metaJson.getString("accessKey"), metaJson.getString("secretKey"));
		return new DefaultAcsClient(profile);
	}

	@Override
	public JSONObject getKafkaConfByAppName(String tenantId, String appName) {
		AppResourceRelationExample example = new AppResourceRelationExample();
    	example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andRelationTypeEqualTo("kafka");
    	List<AppResourceRelation> rels = appResourceRelationMapper.selectByExample(example);
    	if (CollectionUtils.isEmpty(rels)) {
    		throw new QanatBizException("no app resouces");
    	}
    	AppResourceRelation ref = rels.get(0);
    	ResourceExample example1 = new ResourceExample();
    	example1.createCriteria().andResourceNameEqualTo(ref.getResourceName()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andResourceNameEqualTo(ref.getResourceName());
    	List<com.aliyun.wormhole.qanat.dal.domain.Resource> resources = resourceMapper.selectByExampleWithBLOBs(example1);
    	if (CollectionUtils.isEmpty(resources)) {
    		throw new QanatBizException("no app resouces");
    	}
    	com.aliyun.wormhole.qanat.dal.domain.Resource resource = resources.get(0);
    	JSONObject metaJson = JSON.parseObject(resource.getMeta());
    	metaJson.put("dbName", resource.getDbName());
		return metaJson;
	}

	@Override
	public JSONObject getKafkaConfByDbName(String tenantId, String dbName) {
		DbInfoExample example = new DbInfoExample();
    	example.createCriteria().andDbNameEqualTo(dbName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDbTypeEqualTo("kafka");
    	List<DbInfo> dbInfos = dbInfoMapper.selectByExampleWithBLOBs(example);
    	if (CollectionUtils.isEmpty(dbInfos)) {
    		throw new QanatBizException("no dbInfo:" + dbName);
    	}
    	JSONObject metaJson = JSON.parseObject(dbInfos.get(0).getMeta());
		return metaJson;
	}
}