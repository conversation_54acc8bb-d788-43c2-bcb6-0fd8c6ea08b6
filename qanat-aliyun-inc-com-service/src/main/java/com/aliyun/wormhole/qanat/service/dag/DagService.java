package com.aliyun.wormhole.qanat.service.dag;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.aliyun.wormhole.qanat.api.dag.Dag;
import com.aliyun.wormhole.qanat.api.dag.Node;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DagService {

    public Dag getDagByJson(String dagJsonStr) {
        JSONObject dagJson = JSON.parseObject(dagJsonStr);
        Dag dag = new Dag(dagJson.getString("id"));
        List<Node> nodeList = new ArrayList<>();
        dag.setNodeList(nodeList);
        JSONArray nodeJsonArray = dagJson.getJSONArray("nodeList");
        for (int i = 0; i < nodeJsonArray.size(); i++) {
            JSONObject nodeJson = nodeJsonArray.getJSONObject(i);
            Node node = getNodeByJSONObject(nodeJson);
            nodeList.add(node);
        }
        dag.setTimeExpression(dagJson.getString("timeExpression"));
        return dag;
    }

    public Node getNodeByJSONObject(JSONObject nodeJson) {
        Class nodeClass = nodeJson.getObject("nodeClass", Class.class);
        return (Node)nodeJson.toJavaObject(nodeClass);
    }
}