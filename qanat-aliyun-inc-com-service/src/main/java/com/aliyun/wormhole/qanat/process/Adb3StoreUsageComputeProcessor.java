package com.aliyun.wormhole.qanat.process;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfo;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TenantInfoMapper;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class Adb3StoreUsageComputeProcessor extends JavaProcessor {

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private TenantInfoMapper tenantInfoMapper;
    
    @Resource
    private DatasourceMapper dsInfoMapper;
    
    @Resource
    private DatatubeInstanceMapper datatubeInstanceMapper;
	
	@Override
    public ProcessResult process(JobContext context) {
        JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
        log.info("start Adb3DataSizeComputeProcessor, params={}", context.getJobParameters());
        
        String tenantId = paramsJson.getString("tenantId");
        Date now = new Date();
        
    	TenantInfoExample tiExample = new TenantInfoExample();
    	tiExample.createCriteria().andTenantIdEqualTo(tenantId);
    	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
    	if (CollectionUtils.isEmpty(tenantList)) {
    		throw new QanatBizException("tenantId:" + tenantId + " is not configured");
    	}
	    
	    DatasourceExample example = new DatasourceExample();
	    example.createCriteria().andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andDsTypeEqualTo("adb3").andDbNameEqualTo(tenantList.get(0).getDefaultDw());
	    List<Datasource> dsInfos = dsInfoMapper.selectByExample(example);
	    
	    if (CollectionUtils.isEmpty(dsInfos)) {
	    	return new ProcessResult(true, "no records found");
	    }
	    List<String> tableNames = new ArrayList<>();
	    for (Datasource dsInfo : dsInfos) {
	    	tableNames.add("'" + dsInfo.getTableName() + "'");
	    }
        
		RdsConnectionParam param = new RdsConnectionParam();
	    JSONObject dbMetaJson = getAdbDbMeta(tenantList.get(0).getDefaultDw());
	    param.setUrl(dbMetaJson.getString("jdbcUrl"))
	        .setUserName(dbMetaJson.getString("username"))
	        .setPassword(dbMetaJson.getString("password"));
	    Connection connection = null;
	    try {
	        connection = dsHandler.connectToTable(param);
	        
	        Map<String, BigDecimal> data1 = querySql1(connection, StringUtils.join(tableNames, ",").toUpperCase());
	        Map<String, Long> data2 = querySql2(connection, StringUtils.join(tableNames, ",").toLowerCase());
            
	        if (data1 != null && CollectionUtils.isNotEmpty(data1.keySet())) {
	        	for (String tableName : data1.keySet()) {
	        		DatasourceExample dsExample = new DatasourceExample();
	        		dsExample.createCriteria().andTenantIdEqualTo(tenantId)
	        									.andIsDeletedEqualTo(0L)
	        									.andDsTypeEqualTo("adb3")
	        									.andDbNameEqualTo(tenantList.get(0).getDefaultDw())
	        									.andTableNameEqualTo(tableName.toLowerCase());
	        		
	        		Datasource updRecord = new Datasource();
	        		updRecord.setDataSize(data1.get(tableName));
	        		updRecord.setModifyEmpid("schedulerx");
	        		updRecord.setGmtModified(now);
	        		dsInfoMapper.updateByExampleSelective(updRecord, dsExample);
	        	}
	        }
	        if (data2 != null && CollectionUtils.isNotEmpty(data2.keySet())) {
	        	for (String tableName : data2.keySet()) {
	        		DatasourceExample dsExample = new DatasourceExample();
	        		dsExample.createCriteria().andTenantIdEqualTo(tenantId)
	        									.andIsDeletedEqualTo(0L)
	        									.andDsTypeEqualTo("adb3")
	        									.andDbNameEqualTo(tenantList.get(0).getDefaultDw())
	        									.andTableNameEqualTo(tableName.toLowerCase());
	        		
	        		Datasource updRecord = new Datasource();
	        		updRecord.setSize(data2.get(tableName));
	        		updRecord.setModifyEmpid("schedulerx");
	        		updRecord.setGmtModified(now);
	        		dsInfoMapper.updateByExampleSelective(updRecord, dsExample);
	        	}
	        }
        	
	        param = new RdsConnectionParam();
		    dbMetaJson = getAdbDbMeta("qanat");
		    param.setUrl(dbMetaJson.getString("jdbcUrl"))
		        .setUserName(dbMetaJson.getString("username"))
		        .setPassword(dbMetaJson.getString("password"));
	        connection = dsHandler.connectToTable(param);
	        
	        Map<Long, BigDecimal> data3 = querySql3(connection, tenantList.get(0).getDefaultDw());
            
	        if (data3 != null && CollectionUtils.isNotEmpty(data3.keySet())) {
	        	for (Long datatubeInstId : data3.keySet()) {
	        		DatatubeInstance updRecord = new DatatubeInstance();
	        		updRecord.setId(datatubeInstId);
	        		updRecord.setStoreCost(data3.get(datatubeInstId));
	        		updRecord.setModifyEmpid("schedulerx");
	        		updRecord.setGmtModified(now);
	        		datatubeInstanceMapper.updateByPrimaryKeySelective(updRecord);
	        	}
	        }
        	log.info("Adb3DataSizeComputeProcessor finished");
	    } catch (Exception e) {
	        log.error("Adb3DataSizeComputeProcessor failed, error={}", e.getMessage(), e);
	        return new ProcessResult(false, e.getMessage());
	    } finally {
	        if (param != null) {
            	dsHandler.closeDataSource(param);
	        }
	    }
        return new ProcessResult(true);
	}

    private JSONObject getAdbDbMeta(String dbName) {
        DbInfoExample example = new DbInfoExample();
        example.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("no db found");
        }
        String dbMeta = dbs.get(0).getMeta();
        JSONObject dbMetaJson = JSON.parseObject(dbMeta);
        return dbMetaJson;
    }

    private Map<String, BigDecimal> querySql1(Connection connection, String tableNames) {
        Map<String, BigDecimal> data = new HashMap<>();
    	String sql = "select table_name,round(hot_total_size/1024/1024, 2) as data_size_m from information_schema.table_usage where table_name in (" + tableNames + ")";
        log.info("before exec sql={}", sql);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                data.put(resultSet.getString("table_name"), resultSet.getBigDecimal("data_size_m"));
            }
            log.info("after exec sql data={}", JSON.toJSONString(data));
        } catch(Exception e) {
            log.error("querySql failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                }
                resultSet = null;
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
        }
        return data;
    }

    private Map<String, Long> querySql2(Connection connection, String tableNames) {
        Map<String, Long> data = new HashMap<>();
    	String sql = "select table_name, table_rows from information_schema.tables where table_name in (" + tableNames + ")";
        log.info("before exec sql={}", sql);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                data.put(resultSet.getString("table_name"), resultSet.getLong("table_rows"));
            }
            log.info("after exec sql data={}", JSON.toJSONString(data));
        } catch(Exception e) {
            log.error("querySql failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                }
                resultSet = null;
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
        }
        return data;
    }

    private Map<Long, BigDecimal> querySql3(Connection connection, String dbName) {
        Map<Long, BigDecimal> data = new HashMap<>();
    	String sql = "select a.datatube_inst_id, sum(b.data_size) as data_size \n" + 
    			"from datatube_instance_ds_relation as a inner join datasource as b on a.ds_name=b.ds_name \n" + 
    			"where a.is_deleted=0 and a.ds_type='adb3' and b.db_name='" + dbName + "' and b.is_deleted=0 and b.ds_type='adb3'\n" + 
    			"group by a.datatube_inst_id";
        log.info("before exec sql={}", sql);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                data.put(resultSet.getLong("datatube_inst_id"), resultSet.getBigDecimal("data_size"));
            }
            log.info("after exec sql data={}", JSON.toJSONString(data));
        } catch(Exception e) {
            log.error("querySql failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                }
                resultSet = null;
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
        }
        return data;
    }
    
    @Override
    public void kill(JobContext context) {
        
    }
}