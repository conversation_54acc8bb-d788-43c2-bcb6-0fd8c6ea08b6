package com.aliyun.wormhole.qanat.service.mdp;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.taobao.ateye.annotation.AteyeInvoker;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.aliyun.odps.Instance;
import com.aliyun.odps.Odps;
import com.aliyun.odps.account.Account;
import com.aliyun.odps.account.AliyunAccount;
import com.aliyun.odps.task.SQLTask;
import com.aliyun.tag.api.vo.SimpleTagVO;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.ExceptionCodeConstants;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.MdpDataModelService;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.service.mdp.AnalyticModel.Field;
import com.aliyun.wormhole.qanat.service.util.YamlUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@HSFProvider(serviceInterface = MdpDataModelService.class)
public class MdpDataModelServiceImpl implements MdpDataModelService, InitializingBean{
	
	private static final String BIZ_ID_PREFIX="124299^aliyun_tag^id^";
	private static final String BIZ_ID_KEY="biz_id";
    
	private Odps odps;

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private DatasourceService dsInfoService;
    
    @Override
    public String getYamlFromDomain(String domainCode, String objectUniqueCode) {
        log.info("getYamlFromDomain({},{}) start", domainCode, objectUniqueCode);
        StringBuffer sql = new StringBuffer();
        RdsConnectionParam param = new RdsConnectionParam();
        JSONObject dbMetaJson = getAdbDbMeta("devata_rtdw");
        param.setUrl(dbMetaJson.getString("jdbcUrl"))
        .setUserName(dbMetaJson.getString("username"))
        .setPassword(dbMetaJson.getString("password"));
        Statement statement = null;
        Connection connection = null;
        try {
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement();

            List<Map<String, String>> dataList
                = getViewStructFromDomainAndObjectCode(domainCode, objectUniqueCode, connection);
            
            sql.append("model:\n");
            sql.append("  code: " + objectUniqueCode + "\n");
            sql.append("  name: " + objectUniqueCode + "\n");
            sql.append("  domain: " + domainCode + "\n");
            sql.append("  metaRef: " + objectUniqueCode + "\n");
            sql.append("  fields:\n");
            for (int i = 0; i < dataList.size(); i++) {
                Map<String, String> field = dataList.get(i);
                sql.append("     - name: " + field.get("name") + "\n");
                sql.append("       code: " + field.get("code") + "\n");
                //sql.append("       type: " + transformMdpTypeToAdbType(field.get("data_type")) + "\n");
                if ("enum".equalsIgnoreCase(field.get("data_type")) || "enums".equalsIgnoreCase(field.get("data_type"))) {
                    sql.append("       enums: true\n");
                }
                if (field.get("code").equalsIgnoreCase(field.get("unique_code"))) {
                    sql.append("       metaRef: " + field.get("unique_code") + "\n");
                }
            }
            return sql.toString();
        } catch (Exception e) {
            log.error("createAdbViewFromDomain failed", e);
            return null;
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {}
                statement = null;
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {}
                connection = null;
            }
        }
    }

    private JSONObject getAdbDbMeta(String dbName) {
        DbInfoExample example = new DbInfoExample();
        example.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("no db found");
        }
        String dbMeta = dbs.get(0).getMeta();
        JSONObject dbMetaJson = JSON.parseObject(dbMeta);
        return dbMetaJson;
    }

    private List<Map<String, String>> querySql(Connection connection, String sql) {
        log.info("before exec sql={}", sql);
        List<Map<String, String>> dataList = new ArrayList<>();
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                Map<String, String> data = new HashMap<>();
                data.put("code", resultSet.getString("code"));
                data.put("unique_code", resultSet.getString("unique_code"));
                data.put("name", resultSet.getString("name"));
                data.put("data_type", resultSet.getString("data_type"));
                dataList.add(data);
            }
            log.info("after exec sql cnt={}", dataList.size());
        } catch(Exception e) {
            log.error("querySql failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                }
                resultSet = null;
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
        }
        return dataList;
    }

	@Override
	public String createOdpsViewFromYaml(String yaml) {
		return createOdpsViewFromYaml(yaml, null);
	}

	@Override
	public String createOdpsViewFromYaml(String yaml, String pkField) {
		log.info("start createOdpsViewFromYaml({},{})", yaml, pkField);
		String viewName = "";
		try {
	        AnalyticModel dataModel = YamlUtil.getDataModel(yaml);
	        viewName = getOdpsViewName("aliyun_tag", dataModel);
		    String sql = getCreateViewSqlFromYaml(dataModel, viewName, pkField);
			log.info("getCreateViewSqlFromYaml={}", sql);
			odps.setDefaultProject("aliyun_tag");
			Map<String, String> hints = new HashMap<>();
			hints.put(BIZ_ID_KEY, BIZ_ID_PREFIX + System.currentTimeMillis());
			Instance inst = SQLTask.run(odps, odps.getDefaultProject(), sql, hints, null);
			inst.waitForSuccess();
			String logview = odps.logview().generateLogView(inst, 7 * 24);
            log.info("logview={}", logview);
            return viewName;
		} catch (Exception e) {
			log.error("createOdpsViewFromYaml failed", e);
			return null;
		}
	}

	private String getCreateViewSqlFromYaml(AnalyticModel dataModel, String viewName, String pkField) {
	    String metaRef = dataModel.getModel().getMetaRef();
	    JSONObject odpsDsMeta = dsInfoService.getOdpsTableMetaByObjectCode("1", metaRef);
	    String tableName = "s_tag_meta_tag_object_biz_relation_aliyun_tag_m_app_new";
	    if (odpsDsMeta != null && odpsDsMeta.getString("table") != null) {
	    	tableName = odpsDsMeta.getString("table");
	    }
	    List<Field> fields = dataModel.getModel().getFields();
	    if (StringUtils.isBlank(pkField)) {
		    if ("DEVATA__CUSTOMER".equalsIgnoreCase(dataModel.getModel().getMetaRef())) {
		    	pkField = "cid";
		    } else if ("DEVATA__USER".equalsIgnoreCase(dataModel.getModel().getMetaRef())) {
		    	pkField = "uid";
		    } else {
		    	pkField = "id";
		    }
	    }
	    StringBuffer sql = new StringBuffer();
	    sql.append("CREATE OR REPLACE VIEW " + viewName + " (" + pkField + " COMMENT '" + pkField + "',");
		if (odpsDsMeta.containsKey("storeType") && "slot".equalsIgnoreCase(odpsDsMeta.getString("storeType")) && odpsDsMeta.getJSONObject("slotMeta") != null) {
			tableName = odpsDsMeta.getJSONObject("slotMeta").getString("tableName");
			Map<String, String> fieldMap = dsInfoService.getObjectSlotFieldMap(odpsDsMeta.getString("objectType"), metaRef, null);
			for (int i = 0; i < fields.size(); i++) {
				Field field = fields.get(i);
				sql.append(field.getCode() + " COMMENT '" + field.getName() + "'");
				if (i < (fields.size() - 1)) {
					sql.append(",");
				}
			}
		    sql.append(") COMMENT '" + dataModel.getModel().getName() + "' AS SELECT object_id as " + pkField + ",");
		    for (int i = 0; i < fields.size(); i++) {
				Field field = fields.get(i);
				sql.append(fieldMap.get(field.getMetaRef()) + " AS " + field.getCode());
				if (i < (fields.size() - 1)) {
					sql.append(",");
				}
			}
		    sql.append(" FROM ");
	        sql.append(tableName);
	        sql.append(" WHERE pt=MAX_PT('" + tableName + "') AND is_deleted = 0 AND tenant_id = 'aliyun' AND object_code = '" + metaRef + "';");
		} else {
			for (int i = 0; i < fields.size(); i++) {
				Field field = fields.get(i);
				sql.append(field.getCode() + " COMMENT '" + field.getName() + "'");
				if ("true".equalsIgnoreCase(field.getEnums())) {
					sql.append("," + field.getCode() + "_desc COMMENT '" + field.getName() + "'");
				}
				if (i < (fields.size() - 1)) {
					sql.append(",");
				}
			}
		    sql.append(") COMMENT '" + dataModel.getModel().getName() + "' AS SELECT object_biz_id as " + pkField + ",");
		    for (int i = 0; i < fields.size(); i++) {
				Field field = fields.get(i);
				if (StringUtils.isNotBlank(field.getMetaRef())) {
				    sql.append("MAX(CASE WHEN tag_unique_code = '" + field.getMetaRef() + "' THEN tag_value ELSE NULL END) AS " + field.getCode() + "");
				} else {
				    sql.append("MAX(CASE WHEN tag_code = '" + field.getCode() + "' THEN tag_value ELSE NULL END) AS " + field.getCode() + "");
				}
				if ("true".equalsIgnoreCase(field.getEnums())) {
		            if (StringUtils.isNotBlank(field.getMetaRef())) {
		                sql.append(",MAX(CASE WHEN tag_unique_code = '" + field.getMetaRef() + "' THEN tag_value_zh ELSE NULL END) AS " + field.getCode() + "_desc");
		            } else {
		                sql.append(",MAX(CASE WHEN tag_code = '" + field.getCode() + "' THEN tag_value_zh ELSE NULL END) AS " + field.getCode() + "_desc");
		            }
				}
				if (i < (fields.size() - 1)) {
					sql.append(",");
				}
			}
		    sql.append(" FROM (");
	        sql.append("SELECT A.object_biz_id,A.tag_code,A.tag_value,A.tag_value_zh,A.tag_unique_code FROM (SELECT object_biz_id,tag_code,tag_value,tag_value_zh,tag_unique_code,object_unique_code FROM aliyun_tag." + tableName + " WHERE ds=MAX_PT('aliyun_tag." + tableName + "')");
	        sql.append(" AND is_deleted = 0 AND object_unique_code = '" + metaRef + "') AS A");
	        sql.append(" JOIN (SELECT object_unique_code, tag_unique_code FROM aliyun_tag.ods_aliyun_tag_domain_meta_permission WHERE ds=MAX_PT('aliyun_tag.ods_aliyun_tag_domain_meta_permission') AND is_deleted = 0 AND object_unique_code = '" + metaRef + "') AS B ON A.tag_unique_code=B.tag_unique_code AND A.object_unique_code=B.object_unique_code");
	        sql.append(") GROUP BY object_biz_id;");
		}
        return sql.toString();
	}
	
	private String getOdpsViewName(String project, AnalyticModel dataModel) {
        return  project + ".v_am_" + dataModel.getModel().getCode();
	}

    @Override
    public String createOdpsViewFromDomain(String domainCode, String objectUniqueCode, String pkField) {
        log.info("start createOdpsViewFromDomain({},{},{})", domainCode);
        try {
            String sql = getCreateViewSqlFromDomain("aliyun_tag", domainCode, objectUniqueCode, pkField, null);
            log.info("createOdpsViewFromDomain={}", sql);
            odps.setDefaultProject("aliyun_tag");
            Map<String, String> hints = new HashMap<>();
            hints.put(BIZ_ID_KEY, BIZ_ID_PREFIX + System.currentTimeMillis());
            Instance inst = SQLTask.run(odps, odps.getDefaultProject(), sql, hints, null);
            inst.waitForSuccess();
            String logview = odps.logview().generateLogView(inst, 7 * 24);
            log.info("logview={}", logview);
            return getOdpsViewNameByDomain("aliyun_tag", domainCode, objectUniqueCode);
        } catch (Exception e) {
            log.error("createOdpsViewFromDomain failed", e);
            return null;
        }
    }

    @Override
    public String createOdpsViewFromDomain(String domainCode, String objectUniqueCode, String pkField, String viewDesc) {
        log.info("start createOdpsViewFromDomain({},{},{})", domainCode);
        try {
            String sql = getCreateViewSqlFromDomain("aliyun_tag", domainCode, objectUniqueCode, pkField, viewDesc);
            log.info("createOdpsViewFromDomain={}", sql);
            odps.setDefaultProject("aliyun_tag");
            Map<String, String> hints = new HashMap<>();
            hints.put(BIZ_ID_KEY, BIZ_ID_PREFIX + System.currentTimeMillis());
            Instance inst = SQLTask.run(odps, odps.getDefaultProject(), sql, hints, null);
            inst.waitForSuccess();
            String logview = odps.logview().generateLogView(inst, 7 * 24);
            log.info("logview={}", logview);
            return getOdpsViewNameByDomain("aliyun_tag", domainCode, objectUniqueCode);
        } catch (Exception e) {
            log.error("createOdpsViewFromDomain failed", e);
            return null;
        }
    }

    private String getCreateViewSqlFromDomain(String project, String domainCode, String objectUniqueCode, String pkField, String viewDesc) {
        StringBuffer sql = new StringBuffer();
        RdsConnectionParam param = new RdsConnectionParam();
        JSONObject dbMetaJson = getAdbDbMeta("devata_rtdw");
        param.setUrl(dbMetaJson.getString("jdbcUrl"))
        .setUserName(dbMetaJson.getString("username"))
        .setPassword(dbMetaJson.getString("password"));
        Statement statement = null;
        Connection connection = null;
        try {
    	    JSONObject odpsDsMeta = dsInfoService.getOdpsTableMetaByObjectCode("1", objectUniqueCode);
    	    String tableName = "s_tag_meta_tag_object_biz_relation_aliyun_tag_m_app_new";
    	    if (odpsDsMeta != null && odpsDsMeta.getString("table") != null) {
    	    	tableName = odpsDsMeta.getString("table");
    	    }

            String viewName = getOdpsViewNameByDomain(project, domainCode, objectUniqueCode);
    	    if (StringUtils.isBlank(pkField)) {
	    	    if ("DEVATA__CUSTOMER".equalsIgnoreCase(objectUniqueCode)) {
	    	    	pkField = "cid";
	    	    } else if ("DEVATA__USER".equalsIgnoreCase(objectUniqueCode)) {
	    	    	pkField = "uid";
	    	    } else {
	    	    	pkField = "id";
	    	    }
    	    }
            sql.append("CREATE OR REPLACE VIEW " + viewName + " (" + pkField + " COMMENT '" + pkField + "',");
    		if (odpsDsMeta.containsKey("storeType") && "slot".equalsIgnoreCase(odpsDsMeta.getString("storeType")) && odpsDsMeta.getJSONObject("slotMeta") != null) {
    			tableName = odpsDsMeta.getJSONObject("slotMeta").getString("table");
    			List<String> fieldList = dsInfoService.getObjectSlotFieldList(odpsDsMeta.getString("objectType"), objectUniqueCode, domainCode);
    			for (int i = 0; i < fieldList.size(); i++) {
    				SimpleTagVO field = JSON.parseObject(fieldList.get(i), SimpleTagVO.class);
	                sql.append(field.getCode() + " COMMENT '" + field.getName() + "'");
	                if (i < (fieldList.size() - 1)) {
	                    sql.append(",");
	                }
	            }
	            sql.append(") COMMENT '" + (StringUtils.isNotBlank(viewDesc) ? viewDesc : viewName) + "' AS SELECT object_id as " + pkField + ",");
	            for (int i = 0; i < fieldList.size(); i++) {
    				SimpleTagVO field = JSON.parseObject(fieldList.get(i), SimpleTagVO.class);
    				sql.append(field.getSlotFieldCode() + " AS " + field.getCode());
    				if (i < (fieldList.size() - 1)) {
    					sql.append(",");
    				}
    			}
    		    sql.append(" FROM ");
    	        sql.append(tableName);
    	        sql.append(" WHERE pt=MAX_PT('" + tableName + "') AND is_deleted = 0 AND tenant_id = 'aliyun' AND object_code = '" + objectUniqueCode + "';");
    	        return sql.toString();
    		} else {
                connection = dsHandler.connectToTable(param);
                statement = connection.createStatement();

                List<Map<String, String>> dataList
                    = getViewStructFromDomainAndObjectCode(domainCode, objectUniqueCode, connection);
	            for (int i = 0; i < dataList.size(); i++) {
	                Map<String, String> field = dataList.get(i);
	                sql.append(field.get("code") + " COMMENT '" + field.get("name") + "'");
	                if ("enum".equalsIgnoreCase(field.get("data_type")) || "enums".equalsIgnoreCase(field.get("data_type"))) {
	                    sql.append("," + field.get("code") + "_desc COMMENT '" + field.get("name") + "__描述'");
	                }
	                if (i < (dataList.size() - 1)) {
	                    sql.append(",");
	                }
	            }
	            sql.append(") COMMENT '" + (StringUtils.isNotBlank(viewDesc) ? viewDesc : viewName) + "' AS SELECT object_biz_id as " + pkField + ",");
	            for (int i = 0; i < dataList.size(); i++) {
	                Map<String, String> field = dataList.get(i);
	                sql.append("MAX(CASE WHEN tag_unique_code = '" + field.get("unique_code") + "' THEN tag_value ELSE NULL END) AS " + field.get("code") + "");
	                if ("enum".equalsIgnoreCase(field.get("data_type")) || "enums".equalsIgnoreCase(field.get("data_type"))) {
	                    sql.append(",MAX(CASE WHEN tag_unique_code = '" + field.get("unique_code") + "' THEN tag_value_zh ELSE NULL END) AS " + field.get("code") + "_desc");
	                }
	                if (i < (dataList.size() - 1)) {
	                    sql.append(",");
	                }
	            }
	            sql.append(" FROM (");
	            sql.append("SELECT A.object_biz_id,A.tag_code,A.tag_value,A.tag_value_zh,A.tag_unique_code FROM (SELECT object_biz_id,tag_code,tag_value,tag_value_zh,tag_unique_code,object_unique_code FROM aliyun_tag." + tableName);
	            sql.append(" WHERE ds=MAX_PT('aliyun_tag." + tableName + "')");
	            sql.append(" AND is_deleted = 0");
	            sql.append(" AND object_unique_code = '" + objectUniqueCode + "') AS A JOIN (SELECT object_unique_code, tag_unique_code FROM aliyun_tag.ods_aliyun_tag_domain_meta_permission WHERE ds=MAX_PT('aliyun_tag.ods_aliyun_tag_domain_meta_permission') AND is_deleted = 0 AND domain_code='" + domainCode + "' AND object_unique_code = '" + objectUniqueCode + "') AS B ON A.tag_unique_code=B.tag_unique_code AND A.object_unique_code=B.object_unique_code ");
	            sql.append(") GROUP BY object_biz_id;");
    		}
            return sql.toString();
        } catch (Exception e) {
            log.error("syncfullDataForAdbTableFromYaml failed", e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {}
                statement = null;
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {}
                connection = null;
            }
        }
        return sql.toString();
    }

    private List<Map<String, String>> getViewStructFromDomainAndObjectCode(String domainCode, String objectUniqueCode,
        Connection connection) {
        String selectSql = String.format("select b.code, b.unique_code, b.name, b.data_type from tag_domain_meta_permission as a, tag_meta_tag as b where b.unique_code=a.tag_unique_code and a.is_deleted=0 and b.is_deleted=0 and a.domain_code='%s' and a.object_unique_code=b.object_unique_code and a.object_unique_code='%s'"
            , domainCode, objectUniqueCode);
        List<Map<String, String>> dataList = querySql(connection, selectSql);
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        List<String> codeList = new ArrayList<>();
        for (Map<String, String> data : dataList) {
            codeList.add(data.get("code"));
        }
        Map<String, Long> codeCntMap = codeList.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        for (Map<String, String> data : dataList) {
            if (codeCntMap.get(data.get("code")) > 1) {
                data.put("code", data.get("unique_code"));
            }
        }
        return dataList;
    }

    private String getOdpsViewNameByDomain(String project, String domainCode, String objectUniqueCode) {
        String viewName = project + ".v_am_" + objectUniqueCode + "_" + domainCode;
        return viewName;
    }

	@Override
	public void afterPropertiesSet() throws Exception {
		try {
	    	DbInfoExample example = new DbInfoExample();
	    	example.createCriteria().andDbNameEqualTo("mdp_odps").andIsDeletedEqualTo(0L);
	    	List<DbInfo> dbInfos = dbInfoMapper.selectByExampleWithBLOBs(example);
	    	if (CollectionUtils.isNotEmpty(dbInfos)) {
		    	DbInfo dbInfo = dbInfos.get(0);
		    	JSONObject metaJson = JSON.parseObject(dbInfo.getMeta());
		    
				Account account = new AliyunAccount(metaJson.getString("accessId"), metaJson.getString("accessKey"));
				odps = new Odps(account);
				odps.setEndpoint(metaJson.getString("odpsServer"));
	    	}
		} catch (Exception e) {
			log.error("连接到ODPS错误, error={}", e.getMessage(), e);
		}
	}

	@AteyeInvoker(paraDesc = "source&destTable")
	public String alterTable(String sourceTable, String destTable) throws Exception {
        Statement statement = null;
        Connection connection = null;
        RdsConnectionParam param = new RdsConnectionParam();
        JSONObject dbMetaJson = getAdbDbMeta("devata_rtdw");
        param.setUrl(dbMetaJson.getString("jdbcUrl"))
                .setUserName(dbMetaJson.getString("username"))
                .setPassword(dbMetaJson.getString("password"));
        connection = dsHandler.connectToTable(param);
        statement = connection.createStatement();
        statement.execute("alter table " + sourceTable + " rename to " + destTable);
        return "success";
    }
}