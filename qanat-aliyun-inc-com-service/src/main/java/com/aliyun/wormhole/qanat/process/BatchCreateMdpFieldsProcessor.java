package com.aliyun.wormhole.qanat.process;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.tag.api.request.dictionary.CreateEnumBatchRequest;
import com.aliyun.tag.api.request.dictionary.CreateEnumBatchRequest.CreateEnumVO;
import com.aliyun.tag.api.request.tag.CreateTagRequest;
import com.aliyun.tag.api.response.BaseResponse;
import com.aliyun.tag.api.response.DataResponse;
import com.aliyun.tag.api.service.IOperateEnumService;
import com.aliyun.tag.api.service.IOperateTagMetaService;
import com.aliyun.tag.api.vo.SimpleTagVO;
import com.aliyun.wormhole.qanat.api.QanatBizException;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 批量创建mdp字段
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class BatchCreateMdpFieldsProcessor extends JavaProcessor {
    
	@HSFConsumer(clientTimeout=30000)
    private IOperateTagMetaService operateTagMetaService;
    
	@HSFConsumer(clientTimeout=30000)
    private IOperateEnumService operateEnumService;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
            log.info("start to add fields, param=[]", context.getJobParameters());
            JSONArray fieldJsonArray = paramsJson.getJSONArray("fields");
            String bizOperator = paramsJson.getString("operator");
            String domainAK = paramsJson.getString("domainAK");
            String domainCode = paramsJson.getString("domainCode");
            String objectType = paramsJson.getString("objectType");
            String objectUniqueCode = paramsJson.getString("objectUniqueCode");
            if (fieldJsonArray != null && fieldJsonArray.size() > 0) {
            	for (int i = 0; i < fieldJsonArray.size(); i++) {
            		JSONObject fieldJson = fieldJsonArray.getJSONObject(i);
                    CreateTagRequest req = new CreateTagRequest();
                    req.setAliasName(fieldJson.getString("fieldName"));
                    req.setBizOperator(bizOperator);
                    req.setCode(fieldJson.getString("fieldName"));
                    req.setDataType(fieldJson.getString("fieldType"));
                    req.setDomainAK(domainAK);
                    req.setDomainCode(domainCode);
                    req.setExtInfo(fieldJson.getString("extInfo"));
                    req.setIntro(fieldJson.getString("fieldDesc"));
                    req.setName(fieldJson.getString("fieldDesc"));
                    req.setObjectType(objectType);
                    req.setObjectUniqueCode(objectUniqueCode);
                    req.setRequestId(UUID.randomUUID().toString());
                    req.setTagMarkerSelector("ADB");
                    req.setTagType("normal");
                    req.setIsMultiple(fieldJson.getLong("isMultiple"));
                    req.setIsQuote(fieldJson.getLong("isQuote"));
                    try {
	                    DataResponse<SimpleTagVO> createFieldResponse = operateTagMetaService.create(req);
	                    log.info("field[{}] createFieldResponse={}", fieldJson.getString("fieldName"), JSON.toJSONString(createFieldResponse));
	                    if (createFieldResponse != null && createFieldResponse.isSuccess()) {
	                    	log.info("field[{}] is created successfully", fieldJson.getString("fieldName"));
	                    	
	                    	if ("ENUM".equalsIgnoreCase(fieldJson.getString("fieldType"))
	                    			|| "ENUMS".equalsIgnoreCase(fieldJson.getString("fieldType"))) {
		                    	CreateEnumBatchRequest createEnumReq = new CreateEnumBatchRequest();
		                    	createEnumReq.setBizOperator(bizOperator);
		                    	List<CreateEnumVO> createEnumVOList = new ArrayList<>();
		                    	createEnumReq.setCreateEnumVOList(createEnumVOList);
		                        JSONArray enumsJsonArray = fieldJson.getJSONArray("enums");
		                    	for (int j = 0; j < enumsJsonArray.size(); j++) {
		                    		JSONObject enumsJson = enumsJsonArray.getJSONObject(j);
		                    		CreateEnumVO createEnum = new CreateEnumVO();
		                    		createEnum.setSort(j);
		                    		createEnum.setValueCode(enumsJson.getString("valueCode"));
		                    		createEnum.setValueEn(enumsJson.getString("valueEn"));
		                    		createEnum.setValueZh(enumsJson.getString("valueZh"));
		                    		createEnumVOList.add(createEnum);
		                    	}
		                    	createEnumReq.setDomainAK(domainAK);
		                    	createEnumReq.setDomainCode(domainCode);
		                    	createEnumReq.setRequestId(UUID.randomUUID().toString());
		                    	createEnumReq.setTagCode(fieldJson.getString("fieldName"));
		                    	createEnumReq.setTagDomainCode(domainCode);
		                    	DataResponse<Map<String, BaseResponse>> createEnumResponse = operateEnumService.createBatch(createEnumReq);
			                    log.info("field[{}] createEnumResponse={}", fieldJson.getString("fieldName"), JSON.toJSONString(createEnumResponse));
			                    if (createEnumResponse != null && createEnumResponse.isSuccess()) {
			                    	log.info("field[{}] enums is created successfully", fieldJson.getString("fieldName"));
			                    } else {
			                    	log.error("field[{}] enums is created failed, error={}", fieldJson.getString("fieldName"), createEnumResponse != null ? createEnumResponse.getMessage() : "");
			                    }
	                    	}
	                    } else {
	                    	log.error("field[{}] is created failed, error={}", fieldJson.getString("fieldName"), createFieldResponse != null ? createFieldResponse.getMessage() : "");
	                    }
                    } catch (Exception e) {
                    	log.error("field[{}] is created failed,exception={}", fieldJson.getString("fieldName"), e.getMessage());
                    }
            	}
            }
        } catch (QanatBizException e) {
            log.error("BatchCreateMdpFieldsProcessor任务执行异常:{}", e.getMessage());
            return new ProcessResult(false);
        } catch (Exception e) {
            log.error("BatchCreateMdpFieldsProcessor任务执行异常", e);
            return new ProcessResult(false);
        }
        return new ProcessResult(true);
    }
    
    @Override
    public void kill(JobContext context) {
        
    }
}