package com.aliyun.wormhole.qanat.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.StartFlinksNode;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import com.aliyun.wormhole.qanat.service.flink.FlinkService;
import com.taobao.ateye.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Flink任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatStartFlinksJobProcessor extends AbstractQanatNodeJobProcessor<StartFlinksNode> {
    
    @Resource
    private FlinkService flinkService;

    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Override
    void doProcess(Map<String, Object> instParamsMap, StartFlinksNode flink) {
        try {Long taskInstId = Long.valueOf(String.valueOf(instParamsMap.get("taskInstId")));
            JSONObject execParamJson = null;
            if (taskInstId != null) {
                TaskInstance taskInst = taskInstanceMapper.selectByPrimaryKey(taskInstId);
                execParamJson = JSON.parseObject(taskInst.getExecParam());
            }
            Date startTime = new Date();
            String incrSyncStartTime = "";
            String tenantId = String.valueOf(instParamsMap.get("tenantId"));
            String appName = String.valueOf(instParamsMap.get("appName"));

            if (flink.getStartTimePolicy() != null) {
                startTime = getStartTimeFromPolicy(flink.getStartTimePolicy());
            } else {
                if (execParamJson != null) {
                    incrSyncStartTime = execParamJson.getString("incr_sync_start_time");
                }
                if (StringUtils.isNotBlank((String)instParamsMap.get("incr_sync_start_time"))) {//JOB参数如果设置优先级更高
                    incrSyncStartTime = (String)instParamsMap.get("startConsumeTime");
                }
                if (StringUtils.isNotBlank(incrSyncStartTime)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    startTime = sdf.parse(incrSyncStartTime);
                }
            }
            Map<String, String> flinkParams = null;
            if (execParamJson != null) {
                JSONObject dataJson = execParamJson.getJSONObject("data");
                if (dataJson != null && CollectionUtils.isNotEmpty(dataJson.keySet())) {
                    flinkParams = new HashMap<>();
                    for (String key : dataJson.keySet()) {
                        flinkParams.put(key, dataJson.getString(key));
                    }
                }
            }
            String[] jobNameArray = flink.getJobNames().split(",");
            for (String jobName : jobNameArray) {
                if (flink.isRestart()) {
                    flinkService.stopJob(tenantId, appName, jobName);
                }
                flinkService.startJob(tenantId, appName, jobName, startTime, flinkParams);
            }
            log.info("Flink任务:{} 已启动，offset:{}", flink.getJobNames(), startTime);
        } catch (QanatBizException e) {
            log.error("Flink任务调度异常:{}", e.getMessage());
            throw new QanatBizException(e.getMessage());
        } catch (Exception e) {
            log.error("Flink任务调度异常", e);
            throw new QanatBizException(e.getMessage());
        }
    }

    private Date getStartTimeFromPolicy(String startTimePolicy) {
        if ("everyday_00_00_00".equalsIgnoreCase(startTimePolicy)) {
            SimpleDateFormat fromSdf = new SimpleDateFormat("yyyy-MM-dd");
            String date = fromSdf.format(new Date());
            date = date + " 00:00:00";
            SimpleDateFormat toSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try {
                return toSdf.parse(date);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        } else if ("without_state".equalsIgnoreCase(startTimePolicy)) {
            return null;
        }
        return null;
    }
}