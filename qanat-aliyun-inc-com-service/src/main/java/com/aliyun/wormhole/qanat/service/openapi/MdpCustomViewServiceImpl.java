package com.aliyun.wormhole.qanat.service.openapi;

import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.service.MdpDataModelService;
import com.aliyun.wormhole.qanat.api.service.TaskService;
import com.aliyun.wormhole.qanat.openapi.MdpCustomViewService;
import com.aliyun.wormhole.qanat.openapi.model.ApiResult;
import com.aliyun.wormhole.qanat.openapi.model.CustomViewRequest;
import com.aliyun.wormhole.qanat.openapi.model.DomainViewRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@HSFProvider(serviceInterface = MdpCustomViewService.class)
public class MdpCustomViewServiceImpl extends OpenApiBase implements MdpCustomViewService {
    
    @Resource
    private MdpDataModelService mdpDataModelService;
    
    @Resource
    private TaskService taskService;
    
    @Override
    public ApiResult<String> buildCustomOdpsView(CustomViewRequest request) {
        log.info("buildCustomOdpsView({})", JSON.toJSONString(request));
        ApiResult<String> apiResult = new ApiResult<>();
        apiResult.setCode("200");
        apiResult.setSuccess(true);
        try {
            if (!checkAccessKey(request)) {
                throw new QanatBizException("AK failed");
            }
            String viewName = mdpDataModelService.createOdpsViewFromYaml(request.getYaml(), request.getPkField());
            if (StringUtils.isBlank(viewName)) {
                throw new QanatBizException("generate view failed");
            }
            apiResult.setData(viewName);
        } catch(Exception e) {
            log.error("buildCustomOdpsView failed", e);
            apiResult.setSuccess(false);
            apiResult.setCode("500");
            apiResult.setMessage(e.getMessage());
        }
        return apiResult;
    }

    @Override
    public ApiResult<String> buildDomainOdpsView(DomainViewRequest request) {
        log.info("buildDomainOdpsView({})", JSON.toJSONString(request));
        ApiResult<String> apiResult = new ApiResult<>();
        apiResult.setCode("200");
        apiResult.setSuccess(true);
        try {
            if (!checkAccessKey(request)) {
                throw new QanatBizException("AK failed");
            }
            String viewName = mdpDataModelService.createOdpsViewFromDomain(request.getDomainCode(), request.getObjectUniqueCode(), request.getPkField(), request.getViewDesc());
            if (StringUtils.isBlank(viewName)) {
                throw new QanatBizException("generate view failed");
            }
            apiResult.setData(viewName);
        } catch(Exception e) {
            log.error("buildDomainOdpsView failed", e);
            apiResult.setSuccess(false);
            apiResult.setCode("500");
            apiResult.setMessage(e.getMessage());
        }
        return apiResult;
    }

    @Override
    public ApiResult<String> getYamlFromDomain(DomainViewRequest request) {
        log.info("getYamlFromDomain({})", JSON.toJSONString(request));
        ApiResult<String> apiResult = new ApiResult<>();
        apiResult.setCode("200");
        apiResult.setSuccess(true);
        try {
            if (!checkAccessKey(request)) {
                throw new QanatBizException("AK failed");
            }
            String yaml = mdpDataModelService.getYamlFromDomain(request.getDomainCode(), request.getObjectUniqueCode());
            if (StringUtils.isBlank(yaml)) {
                throw new QanatBizException("get yaml failed");
            }
            apiResult.setData(yaml);
        } catch(Exception e) {
            log.error("getYamlFromDomain failed", e);
            apiResult.setSuccess(false);
            apiResult.setCode("500");
            apiResult.setMessage(e.getMessage());
        }
        return apiResult;
    }
}
