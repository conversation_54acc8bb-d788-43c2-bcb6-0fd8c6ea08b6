package com.aliyun.wormhole.qanat.service.drc;

import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.DagPolicy;
import com.aliyun.wormhole.qanat.api.dto.ResourcePackage;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.TaskInfoRequest;
import com.aliyun.wormhole.qanat.api.service.BlinkService;
import com.aliyun.wormhole.qanat.api.service.DatasourceService;
import com.aliyun.wormhole.qanat.api.service.DrcService;
import com.aliyun.wormhole.qanat.api.service.KafkaManagementService;
import com.aliyun.wormhole.qanat.api.service.KvstoreService;
import com.aliyun.wormhole.qanat.api.service.TaskService;
import com.aliyun.wormhole.qanat.dal.domain.AppInfo;
import com.aliyun.wormhole.qanat.dal.domain.AppInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelation;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsRelation;
import com.aliyun.wormhole.qanat.dal.domain.ResourceExample;
import com.aliyun.wormhole.qanat.dal.mapper.AppInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.AppResourceRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ResourceMapper;
import com.aliyun.wormhole.qanat.service.template.Adb3SyncTemplate;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelOptimizer;

import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Slf4j
@Component
@HSFProvider(serviceInterface = DrcService.class)
public class DrcServiceImpl implements DrcService {
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource
    private BlinkService blinkService;
    
    @Resource
    private TaskService taskService;
    
    @Resource
    private KafkaManagementService kafkaManagementService;
    
    @Resource
    private DatasourceMapper datasourceMapper;
    
    @Resource
    private DsRelationMapper dsRelationMapper;
	
	@Resource
	private AppResourceRelationMapper appResourceRelationMapper;
	
	@Resource
	private ResourceMapper resourceMapper;
    
    @Resource
    private AppInfoMapper appInfoMapper;
    
    @Resource
    private ViewModelOptimizer viewModelOptimizer;
    
    @Resource
    private DatasourceService dsInfoService;
    
    @Resource
    private KvstoreService kvstoreService;

    @Override
	public DataResult<Map<String, Long>> createDrcTaskForDs(String tenantId, String appName, String dsName, String empid) {
    	return createDrcTaskForDs(tenantId, appName, dsName, empid, null);
    }

    @Override
	public DataResult<Map<String, Long>> createDrcTaskForDs(String tenantId, String appName, String dsName, String empid, String datatubeLevel) {
	    log.info("createDrcTaskForDs({},{},{},{})", tenantId, appName, dsName, empid);
		DataResult<Map<String, Long>> result = new DataResult<>();
		result.setCode("200");
		result.setSuccess(true);
		try {
			Map<String, Long> resultData = new HashMap<>();
			Long drcTaskId = createDrcTaskForDs(tenantId, appName, dsName);
			resultData.put("drcTaskId", drcTaskId);
			Long taskId = createTaskDAG(tenantId, appName, dsName, empid, drcTaskId, datatubeLevel);
			resultData.put("taskId", taskId);
			result.setData(resultData);
			
			new Thread(() -> {
				try {
					while (true) {
						String drcResult = this.getDrcTaskStatus(tenantId, appName, drcTaskId);
						JSONObject drcResultJson = JSON.parseObject(drcResult);
						if (drcResultJson != null 
								&& drcResultJson.getBoolean("success") != null
								&& drcResultJson.getBoolean("success")
								&& drcResultJson.getJSONObject("data") != null
								&& drcResultJson.getJSONObject("data").getInteger("status") != null
								&& drcResultJson.getJSONObject("data").getInteger("status") == 200
								&& CollectionUtils.isNotEmpty(drcResultJson.getJSONObject("data").getJSONArray("cfg"))) {
							taskService.runTask(tenantId, empid, taskId);
							break;
						}
					}
				} catch (Exception e) {
					
				}
			}).start();
		    log.info("[{}]create qanat drc task[{}] finished", "", taskId);
		} catch(Exception e) {
			log.error("createDrcTaskForDs failed", e);
			result.setCode("500");
			result.setMessage(e.getMessage());
			result.setSuccess(false);
		}
		return result;
	}

    @Override
	public DataResult<Long> createDrcTaskForDs(String tenantId, String appName, String dsName, Long drcTaskId, String empid) {
	    log.info("createDrcTaskForDs({},{},{},{})", tenantId, appName, dsName, empid);
		DataResult<Long> result = new DataResult<>();
		result.setCode("200");
		result.setSuccess(true);
		try {
			Long taskId = createTaskDAG(tenantId, appName, dsName, empid, drcTaskId, null);
			result.setData(taskId);
		    log.info("[{}]create qanat drc task[{}] finished", "", taskId);
		} catch(Exception e) {
			log.error("createDrcTaskForDs failed", e);
			result.setCode("500");
			result.setMessage(e.getMessage());
			result.setSuccess(false);
		}
		return result;
	}

    @Override
	public DataResult<String> getDrcTaskInfo(String tenantId, String appName, Long drcTaskId) {
    	log.info("getDrcTaskInfo({},{},{})", tenantId, appName, drcTaskId);
		DataResult<String> result = new DataResult<>();
		result.setCode("200");
		result.setSuccess(true);
		try {
			result.setData(getDrcTaskStatus(tenantId, appName, drcTaskId));
		} catch (Exception e) {
			log.error("getDrcTaskInfo failed", e);
			result.setCode("500");
			result.setMessage(e.getMessage());
			result.setSuccess(false);
		}
		return result;
    }

    @Override
	public JSONObject getDrcConfByAppName(String tenantId, String appName) {
		AppResourceRelationExample example = new AppResourceRelationExample();
		example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andRelationTypeEqualTo("drc");
		List<AppResourceRelation> rels = appResourceRelationMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(rels)) {
			throw new QanatBizException("no app resouces");
		}
		AppResourceRelation ref = rels.get(0);
		ResourceExample example1 = new ResourceExample();
		example1.createCriteria().andResourceNameEqualTo(ref.getResourceName()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L);
		List<com.aliyun.wormhole.qanat.dal.domain.Resource> resources = resourceMapper.selectByExampleWithBLOBs(example1);
		if (CollectionUtils.isEmpty(resources)) {
			throw new QanatBizException("no app resouces");
		}
		com.aliyun.wormhole.qanat.dal.domain.Resource resource = resources.get(0);
		JSONObject drcConfJson = JSON.parseObject(resource.getMeta());
		drcConfJson.put("dbName", ref.getResourceName());
		return drcConfJson;
	}

	private Long createTaskDAG(String tenantId, String appName, String dsName, String empid, Long drcTaskId, String datatubeLevel) {
		String topicName = "drc-" + getAppIdByName(tenantId, appName) + "-" + getDsIdByName(tenantId, dsName);
		JSONObject dsMetaJson = dsInfoService.getTableMetaByDsName(tenantId, dsName);
        int parallel = viewModelOptimizer.getParallel(tenantId, datatubeLevel, dsMetaJson.getInteger("qph"));
		boolean res = kafkaManagementService.createTopic(tenantId, appName, topicName, parallel);
		log.info("topic[{}] is created by result[{}]", topicName, res);
		if (!res) {
			log.error("topic:{} create is failed", topicName);
		}
		JSONObject drcConfJson = getDrcConfByAppName(tenantId, appName);
		return createTaskDAG(tenantId, appName, dsName, empid, drcTaskId, topicName, drcConfJson);
	}

    private Long getAppIdByName(String tenantId, String appName) {
    	AppInfoExample example = new AppInfoExample();
    	example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
    	List<AppInfo> apps = appInfoMapper.selectByExample(example);
    	return apps.get(0).getId();
    }

    private Long getDsIdByName(String tenantId, String dsName) {
    	DatasourceExample example = new DatasourceExample();
    	example.createCriteria().andDsNameEqualTo(dsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
    	List<Datasource> dsInfos = datasourceMapper.selectByExample(example);
    	return dsInfos.get(0).getId();
    }

	private Long createTaskDAG(String tenantId, String appName, String dsName, String empid, Long drcTaskId, String topicName, JSONObject drcConfJson) {
        JSONObject dsMetaJson = this.getDsMeta(tenantId, dsName);
		String jobName = "drc_" + getAppIdByName(tenantId, appName) + "_" + dsName;
		
		String dagScript = String.format(
		        Adb3SyncTemplate.DRC_DAG_SCRIPT
		        , jobName
		        , jobName
		        , jobName);
	    TaskInfoRequest taskInfo = new TaskInfoRequest();
	    taskInfo.setDagScript(dagScript);
	    taskInfo.setName("DAG_" + jobName);
	    taskInfo.setOperateEmpid(empid);
	    taskInfo.setTaskDesc("DAG_" + jobName);
	    taskInfo.setPolicy(DagPolicy.ETL.toString());
	    taskInfo.setTenantId(tenantId);
	    taskInfo.setAppName(appName);
	    Long taskId = taskService.createDAGTask(taskInfo);
	    
        Datasource record = new Datasource();
        record.setId(dsMetaJson.getLong("dsId"));
        Map<String, String> incrConfMap = new HashMap<>();
        incrConfMap.put("topicName", topicName);
        incrConfMap.put("type", "kafka");
        dsMetaJson.put("incrConf", incrConfMap);
        record.setMeta(dsMetaJson.toJSONString());
        record.setGmtModified(new Date());
        record.setModifyEmpid(empid);
        datasourceMapper.updateByPrimaryKeySelective(record);
        
        try {
        	JSONObject appKafkaConf = kafkaManagementService.getKafkaConfByAppName(tenantId, appName);
	        Datasource drcDs = new Datasource();
	        drcDs.setDsName("kafka_" + topicName);
	        drcDs.setDsType("kafka");
	        drcDs.setTableName(topicName);
	        drcDs.setIsDeleted(0L);
	        drcDs.setDbName(appKafkaConf.getString("dbName"));
	        drcDs.setGmtModified(new Date());
	        drcDs.setModifyEmpid(empid);
	        drcDs.setGmtCreate(new Date());
	        drcDs.setCreateEmpid(empid);
	        drcDs.setTenantId(tenantId);
	        datasourceMapper.insert(drcDs);
        } catch(Exception e) {}

        try {
	        DsRelation rel= new DsRelation();
	        rel.setSrcDsName("kafka_" + topicName);
	        rel.setCreateEmpid(empid);
	        rel.setGmtCreate(new Date());
	        rel.setGmtModified(new Date());
	        rel.setIsDeleted(0L);
	        rel.setModifyEmpid(empid);
	        rel.setRelationType("incr");
	        rel.setDstDsName(dsName);
	        rel.setTenantId(tenantId);
	        dsRelationMapper.insert(rel);
        } catch(Exception e) {}
        
        try {
	        Datasource drcDs = new Datasource();
	        drcDs.setDsName(jobName);
	        drcDs.setDsType("drc");
	        drcDs.setTableName(drcTaskId + "");
	        drcDs.setIsDeleted(0L);
	        drcDs.setDbName(drcConfJson.getString("dbName"));
	        drcDs.setGmtModified(new Date());
	        drcDs.setModifyEmpid(empid);
	        drcDs.setGmtCreate(new Date());
	        drcDs.setCreateEmpid(empid);
	        drcDs.setTenantId(tenantId);
	        datasourceMapper.insert(drcDs);
        } catch(Exception e) {}

        try {
	        DsRelation rel= new DsRelation();
	        rel.setSrcDsName(jobName);
	        rel.setCreateEmpid(empid);
	        rel.setGmtCreate(new Date());
	        rel.setGmtModified(new Date());
	        rel.setIsDeleted(0L);
	        rel.setModifyEmpid(empid);
	        rel.setRelationType("drc");
	        rel.setDstDsName("kafka_" + topicName);
	        rel.setTenantId(tenantId);
	        dsRelationMapper.insert(rel);
        } catch(Exception e) {}
        
    	JSONObject kvMetaJson = kvstoreService.getResourceConf(tenantId, appName);
    	String cachePart = "";
    	if (kvMetaJson != null && kvMetaJson.containsKey("host") && kvMetaJson.containsKey("port")) {
    		cachePart = "    consumeMode = 'cache',\n" + 
    				"    cacheHost = '" + kvMetaJson.getString("host") + "',\n" + 
    				"    cachePort = '" + kvMetaJson.getInteger("port") + "'";
    	} else {
    		cachePart = "    consumeMode = 'userInput'";
    	}
		
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String sql = String.format(Adb3SyncTemplate.BLINK_SYNC_DRC_SQL
		        , empid
		        , sdf.format(new Date())
		        , "drc consume for " + dsName
		        , drcConfJson.getString("username")
		        , drcConfJson.getString("password")
		        , "tddl".equalsIgnoreCase(dsMetaJson.getString("dsType")) ? "`filter` = '*;" + dsMetaJson.getString("tableName") + "_[0-9]*;*'," : "`filter` = '*;" + dsMetaJson.getString("tableName") + ";*',"
		        , drcTaskId
		        , jobName
		        , cachePart
		        , dsMetaJson.getString("pkFields")
		        , dsMetaJson.getString("pkFields")
		        , topicName
		        , kafkaManagementService.getKafkaConfByAppName(tenantId, appName).getString("dbName")
		        );
	    log.info("blink sql=[{}]", sql);
	    blinkService.buildBlinkJob(tenantId, appName, jobName, sql, "/" + appName + "/" + dsMetaJson.getString("tableName") + "/", 
	    		blinkService.getBlinkExtensionsByPackage(tenantId, ResourcePackage.BLINK_DRC, ResourcePackage.BLINK_KAFKA010), 
	    		false);
		return taskId;
	}

    private Long createDrcTaskForDs(String tenantId, String appName, String dsName) {
		JSONObject drcConfJson = getDrcConfByAppName(tenantId, appName);
		String url = "http://" + drcConfJson.getString("rmip") + "/api/auth";
        String data = String.format("taskParam={\"user\":\"%s\",\"password\":\"%s\"}", drcConfJson.getString("username"), drcConfJson.getString("password"));
        String resp = doHttpPost(url, data);
        if (StringUtils.isNotBlank(resp)) {
            JSONObject respJson = JSON.parseObject(resp);
            if (respJson != null && respJson.getBoolean("success") && respJson.getJSONObject("data") != null) {
                String token = respJson.getJSONObject("data").getString("token");
                JSONObject dsMetaJson = this.getDsMeta(tenantId, dsName);
                if (dsMetaJson.containsKey("appName")) {
                	data = String.format("taskParam={\"type\":\"subscribe\",\"source\":{\"type\":\"tddl\",\"appName\":\"%s\",\"filter\":\"%s.%s\",\"region\":\"sh\",\"domain\":\"ali\"},\"token\":\"%s\"}", dsMetaJson.getString("appName"), dsMetaJson.getString("dbName"), dsMetaJson.getString("tableName"), token);
                } else {
	                String hostPort = dsMetaJson.getString("masterJdbcUrl").split("/")[2];
	                String jdbcUrl = dsMetaJson.getString("masterJdbcUrl") != null ? dsMetaJson.getString("masterJdbcUrl") : dsMetaJson.getString("jdbcUrl");
	                String db = jdbcUrl.split("/")[3].split("\\?")[0];
	                String user = dsMetaJson.getString("username");
	                String pass = null;
	                try {
	                	pass = URLEncoder.encode(dsMetaJson.getString("password"), "utf-8");
	                } catch(Exception e) {
	                	pass = dsMetaJson.getString("password");
	                }
	                String tableName = dsMetaJson.getString("tableName");
	                data = String.format("taskParam={\"type\":\"subscribe\",\"source\":{\"type\":\"raw_mysql\",\"address\":\"%s\",\"user\":\"%s\",\"password\":\"%s\",\"filter\":\"%s.%s\",\"mode\":\"no_pre_check\",\"region\":\"%s\",\"domain\":\"ali\"},\"token\":\"%s\"}", hostPort, user, pass, db, tableName, "8.0".equalsIgnoreCase(dsMetaJson.getString("version")) ? "sh":"rds_sh", token);
                }
                url = "http://" + drcConfJson.getString("rmip") + "/api/submit";
                resp = doHttpPost(url, data);
                respJson = JSON.parseObject(resp);
                if (respJson != null && respJson.getBooleanValue("success")&& respJson.getJSONObject("data") != null) {
                	return respJson.getJSONObject("data").getLong("taskId");
                }
            }
        }
        return null;
	}

	private String getDrcTaskStatus(String tenantId, String appName, Long taskId) {
		JSONObject drcConfJson = getDrcConfByAppName(tenantId, appName);
		String url = "http://" + drcConfJson.getString("rmip") + "/api/auth";
        String data = String.format("taskParam={\"user\":\"%s\",\"password\":\"%s\"}", drcConfJson.getString("username"), drcConfJson.getString("password"));
        String resp = doHttpPost(url, data);
        if (StringUtils.isNotBlank(resp)) {
            JSONObject respJson = JSON.parseObject(resp);
            if (respJson != null && respJson.getBoolean("success") && respJson.getJSONObject("data") != null) {
                String token = respJson.getJSONObject("data").getString("token");
                
                data = String.format("taskParam={\"taskId\":\"%s\",\"token\":\"%s\"}", taskId, token);
                url = "http://" + drcConfJson.getString("rmip") + "/api/query";
                return doHttpPost(url, data);
            }
        }
        return null;
	}
	
	private String doHttpPost(String url, String data) {
        String resp = null;
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(mediaType, data))
                .build();
        OkHttpClient okHttpClient = new OkHttpClient();
        Response response = null;
        try {
            log.info("req:post {} -d'{}'", url, data);
            response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                resp = response.body().string(); 
                log.info("resp:{}", resp); 
            }
        } catch (IOException e) {
            log.error("http request failed", e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return resp;
    }

    private JSONObject getDsMeta(String tenantId, String dsName) {
        DatasourceExample example = new DatasourceExample();
        example.createCriteria().andIsDeletedEqualTo(0L).andDsNameEqualTo(dsName).andTenantIdEqualTo(tenantId);
        List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(dsList)) {
            throw new QanatBizException("no ds found");
        }
        if (StringUtils.isBlank(dsList.get(0).getDbName())) {
            throw new QanatBizException("no dbName conf");
        }
        JSONObject dsMetaJson = JSON.parseObject(dsList.get(0).getMeta());
        DbInfoExample example1 = new DbInfoExample();
        example1.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dsList.get(0).getDbName()).andTenantIdEqualTo(tenantId);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(example1);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("no db found");
        }
        String dbMeta = dbs.get(0).getMeta();
        JSONObject metaJson = JSON.parseObject(dbMeta);
        metaJson.put("tableName", StringUtils.isNotBlank(dsList.get(0).getTableName()) ? dsList.get(0).getTableName() : dsMetaJson.getString("table"));
        metaJson.put("dsId", dsList.get(0).getId());
        metaJson.put("create_ddl", dsMetaJson.getString("create_ddl"));
        metaJson.put("pkFields", dsList.get(0).getPkFields());
        metaJson.put("dsType", dsList.get(0).getDsType());
        return metaJson;
    }
    
    @Override
    public Boolean restartDrcService(String tenantId, String appName, String jobName, Date startTime) {
    	try {
	    	String cacheKey = "DRC_" + jobName + "_0";
	    	String val = kvstoreService.get(tenantId, appName, cacheKey);
	    	log.info("current key={} vlaue={}", cacheKey, val);
	    	kvstoreService.del(tenantId, appName, "DRC_" + jobName + "_0");
	    	log.info("finish to del key:{}", cacheKey);
	    	blinkService.restartJob(tenantId, appName, jobName, startTime, false);
	    	log.info("finish to restart job:{}", jobName);
	    	return true;
    	} catch(Exception e) {
    		log.error("restartDrcService failed:{}", e.getMessage(), e);
    		return false;
    	}
    }
}
