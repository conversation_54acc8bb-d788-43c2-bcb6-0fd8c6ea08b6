package com.aliyun.wormhole.qanat.job;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.WaitNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Wait任务
 */
@Slf4j
@Component
public class QanatWaitJobProcessor extends AbstractQanatNodeJobProcessor<WaitNode> {

    @Override
    void doProcess(Map<String, Object> instParamsMap, WaitNode node) {
        try {
            Thread.sleep(node.getMillis());
            log.info("Wait任务结束，等待:{}ms", node.getMillis());
        } catch (QanatBizException e) {
            log.error("Wait任务调度异常:{}", e.getMessage());
            throw new QanatBizException(e.getMessage());
        } catch (Exception e) {
            log.error("Wait任务调度异常", e);
            throw new QanatBizException(e.getMessage());
        }
    }
}