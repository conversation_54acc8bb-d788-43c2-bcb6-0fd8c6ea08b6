package com.aliyun.wormhole.qanat.service.metaq.check;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.metrics.StringUtils;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.schedulerx.shade.com.google.gson.Gson;
import com.aliyun.wormhole.qanat.service.ateye.SystemSwitch;
import com.aliyun.wormhole.qanat.service.enumerate.EventType;
import com.aliyun.wormhole.qanat.service.metaq.common.BaseListener;
import com.aliyun.wormhole.qanat.service.metaq.common.TaskRetryException;
import com.aliyun.wormhole.qanat.service.metaq.producer.CheckProducer;
import com.google.common.base.Splitter;
import com.taobao.ateye.monitor.TripMonitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class QanatCheckAggListener extends BaseListener {

    private static Splitter splitterOnDouhao = Splitter.on(",");

    private String INC_TYPE = "INC";

    private String FULL_TYPE = "FULL";

    private static Logger log = LoggerFactory.getLogger(QanatCheckAggListener.class);

    @Resource(name = "checkProducer")
    private CheckProducer checkProducer;

    public CheckProducer getCheckProducer() {
        return checkProducer;
    }
    /**
     * cid_sales(增量) full_cid_salse(全量)
     * @param msg
     * @throws TaskRetryException
     */
    @Override
    protected void processMessage(MessageExt msg) throws TaskRetryException {
        if (SystemSwitch.openLog){
            log.info("QanatCheckAggListener msg body : {}", new String(msg.getBody()));
        }

        Map<String, String> map = splitterOnDouhao.withKeyValueSeparator("#").split(new String(msg.getBody()));
        if (SystemSwitch.openLog){
            log.info("QanatCheckAggListener msg:{}", JSONObject.toJSONString(map));
        }
        String eventType = map.get("op");
        String tags = msg.getTags();
        String moniType = "common";
        if (StringUtils.isNotBlank(tags)){
            moniType=  tags.startsWith("full") ? FULL_TYPE : INC_TYPE;
        }
        TripMonitor.common(moniType, tags, EventType.codeOf(Integer.valueOf(eventType)).getName()).setValue1(1);
        getCheckProducer().sendDelayCheckMsg(JSONObject.toJSONString(map).getBytes(), tags, EventType.codeOf(Integer.parseInt(eventType)));
    }


}
