package com.aliyun.wormhole.qanat.service.util;

public class HoloUtils {
    
    public static String getHoloTypeFromMysql(String type) {
        if (type.equalsIgnoreCase("varchar")
        		||type.equalsIgnoreCase("char")
        		||type.equalsIgnoreCase("text")
        		||type.equalsIgnoreCase("mediumtext")
        		||type.equalsIgnoreCase("longtext")) {
            return "text";
        } else if (type.equalsIgnoreCase("datetime")) {
            return "timestamptz";
        } else if (type.equalsIgnoreCase("tinyint")
        		|| type.equalsIgnoreCase("smallint")) {
            return "int";
		} else if (type.equalsIgnoreCase("double")) {
		    return "float8";
		}
        return type;
	}
    
    public static String getHoloTypeFromOdps(String type) {
        if (type.equalsIgnoreCase("string")) {
            return "text";
        } else if (type.equalsIgnoreCase("datetime")
        		|| type.equalsIgnoreCase("timestamp")) {
            return "timestamptz";
        } else if (type.equalsIgnoreCase("tinyint")) {
            return "int";
		} else if (type.equalsIgnoreCase("double")) {
		    return "float8";
		}
        return type;
	}
}
