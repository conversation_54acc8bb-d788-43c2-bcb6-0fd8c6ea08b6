package com.aliyun.wormhole.qanat.job;

import java.net.InetAddress;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.Adb3MultiSqlNode;
import com.aliyun.wormhole.qanat.api.dag.DagInstStatus;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import com.aliyun.wormhole.qanat.service.dag.DagService;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import com.taobao.ateye.monitor.TripMonitor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * ADB3 SQL执行任务入口
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class QanatAdb3MultiSqlJobProcessor extends JavaProcessor {
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;
    
    @Resource
    private DatasourceMapper datasourceMapper;

    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private DagService dagService;
    
    @Resource
    private DbInfoMapper dbInfoMapper;

    // 新增OXS地域感知配置
    @Value("${region.type:}")
    private String regionType;

    @Value("${environment.type:}")
    private String environmentType;

    @Value("${qanat.db.oxs.enabled:false}")
    private boolean oxsEnabled;

    @Override
    public ProcessResult process(JobContext context) {
        String taskName = context.getTaskName();
        log.info("Qanat AdbSql Job[{}] start.", taskName);
        Connection connection = null;
        Statement statement = null;
        try {
            Map<String, Object> instParamsMap = null;
            if (StringUtils.isNotBlank(context.getInstanceParameters())) {
                instParamsMap = (Map<String, Object>)JSON.parseObject(context.getInstanceParameters(), Map.class);
            }
            if (instParamsMap == null) {
                instParamsMap = (Map<String, Object>)JSON.parseObject(context.getJobParameters(), Map.class);
            }
            JSONObject nodeJson = (JSONObject)instParamsMap.get("node");
            Adb3MultiSqlNode node = (Adb3MultiSqlNode)dagService.getNodeByJSONObject(nodeJson);
            String operator = (String)instParamsMap.get("operator");
            String requestId = (String)instParamsMap.get("requestId");
            String subTaskInstId = String.valueOf(instParamsMap.get("subTaskInstId"));
            Long taskInstId = Long.valueOf(String.valueOf(instParamsMap.get("taskInstId")));
            String tenantId = (String)instParamsMap.get("tenantId");
            
            String dbName = node.getDbName();
            if (StringUtils.isBlank(dbName) && StringUtils.isNotBlank(node.getDsName())) {
	            DatasourceExample example = new DatasourceExample();
	            example.createCriteria().andDsNameEqualTo(node.getDsName()).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
	            Datasource dstDs = datasourceMapper.selectByExampleWithBLOBs(example).get(0);
	            dbName = dstDs.getDbName();
            }

            if (StringUtils.isBlank(dbName)) {
            	throw new QanatBizException("dbName not exitss");
            }

            DbInfoExample dbExample = new DbInfoExample();
            dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName).andTenantIdEqualTo(tenantId);
            List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
            if (CollectionUtils.isEmpty(dbs)) {
                throw new QanatBizException("db not found");
            }
            DbInfo dbInfo = dbs.get(0);
            JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
            
            RdsConnectionParam param = new RdsConnectionParam();
            param.setUrl(getDbConnectionUrl(dbMetaJson));
            param.setPassword(dbMetaJson.getString("password"));
            param.setUserName(dbMetaJson.getString("username"));
            connection = dsHandler.connectToTable(param);
            statement = connection.createStatement();
            log.info("[{}]dsName={},sql={}", requestId, node.getDsName(), node.getSql());
            Date startTime = new Date();
            String [] sqls = node.getSql().split(";");
            for (String sql : sqls) {
                long startTs = System.currentTimeMillis();
                try {
	                statement.execute(sql);
	                try {
                        TripMonitor.rt("DDL", node.getDsName(), node.getSql()).record(startTs);
                    } catch (Exception e){
	                    log.error(e.getMessage(),e);
                    }
	                log.info("[{}]sql[{}] exec finished using {} ms", requestId, sql, System.currentTimeMillis()-startTs);
                } catch(Exception e) {
	                log.error("[{}]sql[{}] exec failed", requestId, sql);
                }
            }
            
            //全局任务参数更新到主任务实例的参数中
            if (node.isDataBaseline()) {
                TaskInstance taskInst = taskInstanceMapper.selectByPrimaryKey(taskInstId);
                JSONObject execParam = JSON.parseObject(taskInst.getExecParam());
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                execParam.put("incr_sync_start_time", sdf.format(startTime));
                TaskInstance taskInstUpd = new TaskInstance();
                taskInstUpd.setId(taskInstId);
                taskInstUpd.setExecParam(JSON.toJSONString(execParam));
                taskInstUpd.setGmtModified(new Date());
                taskInstUpd.setModifyEmpid(operator);
                taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
            }
            
            TaskInstance taskInstUpd = new TaskInstance();
            taskInstUpd.setId(Long.valueOf(subTaskInstId));
            taskInstUpd.setGmtModified(new Date());
            taskInstUpd.setEndTime(new Date());
            taskInstUpd.setModifyEmpid(operator);
            taskInstUpd.setExternalInstId(context.getJobInstanceId() + "");//SchedulerX任务实例id
            taskInstUpd.setStatus(DagInstStatus.SUCCESS.getCode().byteValue());
            taskInstUpd.setHostAddr(InetAddress.getLocalHost().getHostAddress());
            taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
            log.info("[{}]Consumer Started. jobId:{}, jobInstId:{}", requestId, context.getJobId(), context.getJobInstanceId());
        } catch (QanatBizException e) {
            log.error("AdbSql任务调度异常:{}", e.getMessage());
            return new ProcessResult(false);
        } catch (Exception e) {
            log.error("AdbSql任务调度异常", e);
            return new ProcessResult(false);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                }
                connection = null;
            }
        }
        return new ProcessResult(true);
    }
    
    @Override
    public void kill(JobContext context) {
        
    }

    /**
     * 根据地域获取数据库连接URL
     * @param dbMetaJson 数据库元数据JSON
     * @return 适合当前地域的数据库连接URL
     */
    private String getDbConnectionUrl(JSONObject dbMetaJson) {
        if (oxsEnabled && isOxsRegion() && dbMetaJson.containsKey("oxsJdbcUrl")) {
            String oxsJdbcUrl = dbMetaJson.getString("oxsJdbcUrl");
            if (StringUtils.isNotBlank(oxsJdbcUrl)) {
                log.info("ADB3 Multi SQL Job using OXS JDBC URL for region: {} env: {}", regionType, environmentType);
                return oxsJdbcUrl;
            }
        }
        return dbMetaJson.getString("jdbcUrl");
    }

    /**
     * 判断当前是否为OXS区域
     * @return true如果是OXS区域
     */
    private boolean isOxsRegion() {
        return "singapore".equalsIgnoreCase(regionType) && 
               StringUtils.containsIgnoreCase(environmentType, "oxs");
    }
}