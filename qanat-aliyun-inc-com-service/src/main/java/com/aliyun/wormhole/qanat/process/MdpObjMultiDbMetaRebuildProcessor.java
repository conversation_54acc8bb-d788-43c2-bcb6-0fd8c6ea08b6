package com.aliyun.wormhole.qanat.process;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfo;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfo;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelVersionWithBLOBs;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceDsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsFieldInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.TenantInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelVersionMapper;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.QanatTddlDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.service.datasource.TddlConnectionParam;
import com.aliyun.wormhole.qanat.service.datatube.DatatubeHandler;
import com.aliyun.wormhole.qanat.service.util.YamlUtil;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelHandler;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelOptimizer;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * 管道实例通用数据稽核任务
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class MdpObjMultiDbMetaRebuildProcessor extends JavaProcessor {

	@Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource 
    private DatatubeInstanceMapper datatubeInstanceMapper;
    
    @Resource 
    private DatatubeInstanceDsRelationMapper datatubeInstanceDsRelationMapper;
    
    @Resource 
    private DatasourceMapper dsInfoMapper;
    
    @Resource 
    private DsFieldInfoMapper dsFieldInfoMapper;
    
    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private ViewModelHandler viewModelHandler;
    
    @Resource
    private ViewModelInfoMapper viewModelInfoMapper;
    
    @Resource
    private ViewModelVersionMapper viewModelVersionMapper;
    
    @Resource
    private ViewModelOptimizer viewModelOptimizer;
    
    @Resource
    private DatatubeHandler datatubeHandler;
    
    @Resource
    private TenantInfoMapper tenantInfoMapper;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            log.info("DatatubeInstanceDataCheckProcessor, param=[]", context.getJobParameters());
            JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
            String tenantId = paramsJson.getString("tenantId");
            String datatubeInstIds = paramsJson.getString("datatubeInstIds");
            DatatubeInstanceExample example = new DatatubeInstanceExample();
            DatatubeInstanceExample.Criteria criteria = example.createCriteria();
            criteria.andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andIsTestEqualTo(0L).andProviderEqualTo("viewmodel").andTypeEqualTo("object");
            if (StringUtils.isNotBlank(datatubeInstIds)) {
            	List<Long> datatubeInstIdList = new ArrayList<>();
            	for (String datatubeInstId : datatubeInstIds.split(",")) {
            		datatubeInstIdList.add(Long.valueOf(datatubeInstId));
            	}
            	criteria.andIdIn(datatubeInstIdList);
            }
        	TenantInfoExample tiExample = new TenantInfoExample();
        	tiExample.createCriteria().andTenantIdEqualTo(tenantId);
        	List<TenantInfo> tenantList = tenantInfoMapper.selectByExample(tiExample);
        	if (CollectionUtils.isEmpty(tenantList)) {
        		throw new QanatBizException("tenantId:" + tenantId + " is not configured");
        	}
        	TenantInfo tenantInfo = tenantList.get(0);
            List<DatatubeInstance> datatubeInstList = datatubeInstanceMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(datatubeInstList)) {
            	return new ProcessResult(false, "no datatube instances found");
            }
            
        	for (DatatubeInstance inst : datatubeInstList) {
        		log.info("start to refresh inst:{}", inst.getCode());
        		ViewModelInfo viewModelInfo = viewModelInfoMapper.selectByPrimaryKey(inst.getProviderId());
            	List<String> dbNames = viewModelHandler.getDstDbNames(tenantInfo, inst);
            	ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());

                ViewModel originModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
            	ViewModel sysModel = null;
            	if (originModel.isDynamic()) {
            		sysModel = viewModelOptimizer.getOptimizedViewModel(tenantId, modelVersion.getUserYaml());
            	} else {
            		sysModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
            	}
            	ViewModel dataModel = new ViewModel();
            	BeanUtils.copyProperties(sysModel, dataModel);
        		viewModelHandler.refreshDsRelations(tenantId, inst.getObjectType(), viewModelInfo.getModelName(), dbNames, tenantInfo.getEtlDw(), "schedulerx2", new Date(), dataModel, inst.getAppName());
        		log.info("finish to refresh inst:{}", inst.getCode());
        	}
        } catch (QanatBizException e) {
            log.error("MdpObjMultiDbMetaRebuildProcessor任务调度异常:{}", e.getMessage());
            return new ProcessResult(false, e.getMessage());
        } catch (Exception e) {
            log.error("MdpObjMultiDbMetaRebuildProcessor任务调度异常", e);
            return new ProcessResult(false, e.getMessage());
        }
        return new ProcessResult(true);
    }
    
    private Map<String, Long> getDsMetrics(String tenantId, Datasource dsInfo) {
    	String pkField = dsInfo.getPkFields();
    	String gmtModifiedField = "gmt_modified";
    	String sql = "select count(1) as total_cnt, max(" + pkField + ") as max_id, max(" + gmtModifiedField + ") as latest_update from " + dsInfo.getTableName();
    	
    	DsFieldInfoExample dfiExample = new DsFieldInfoExample();
    	dfiExample.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsNameEqualTo(dsInfo.getDsName()).andFieldNameEqualTo(gmtModifiedField);
    	int cnt = dsFieldInfoMapper.countByExample(dfiExample);
    	if (cnt == 0) {
	    	sql = "select count(1) as total_cnt, max(" + pkField + ") as max_id, now() as latest_update from " + dsInfo.getTableName();
    	}

	    Connection connection = null;
	    try {
		    JSONObject dbMetaJson = getAdbDbMeta(dsInfo.getDbName());
		    String hint = "";
	        if (dbMetaJson.containsKey("appName")) {
	        	TddlConnectionParam param = new TddlConnectionParam();
	        	param.setAppName(dbMetaJson.getString("appName"))
	        		.setAccessKey("accessKey")
	        		.setSecretKey("secretKey");
	        	connection = QanatTddlDatasourceHandler.connectToTable(param);
	        	hint = "/*+TDDL({\"extra\":{\"ALLOW_FULL_TABLE_SCAN\":\"TRUE\"}})*//*+TDDL_GROUP({groupIndex:1})*//*+TDDL({'extra':{'SOCKET_TIMEOUT':'10000'}})*/";
	        } else {
		    	RdsConnectionParam param = new RdsConnectionParam();
			    param.setUrl(dbMetaJson.getString("jdbcUrl"))
			        .setUserName(dbMetaJson.getString("username"))
			        .setPassword(dbMetaJson.getString("password"));
		        connection = dsHandler.connectToTable(param);
	        }

	        return querySql(connection, dsInfo.getDbName(), hint + sql);
        	
	    } catch (Exception e) {
	        log.error("get table data monitor failed, error={}", e.getMessage(), e);
	        throw new QanatBizException(e.getMessage());
	    } finally {
            if (connection != null) {
                try {
                	connection.close();
                } catch (SQLException e) {
                }
                connection = null;
            }
	    }
    }

    private JSONObject getAdbDbMeta(String dbName) {
        DbInfoExample example = new DbInfoExample();
        example.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("no db found");
        }
        String dbMeta = dbs.get(0).getMeta();
        JSONObject dbMetaJson = JSON.parseObject(dbMeta);
        return dbMetaJson;
    }

    private Map<String, Long> querySql(Connection connection, String dbName, String sql) {
        Map<String, Long> data = new HashMap<>();
        log.info("before exec dbName={} sql={}", dbName, sql);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            Long startTs = System.currentTimeMillis();
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                data.put("total_cnt", resultSet.getLong("total_cnt"));
                data.put("max_id", resultSet.getLong("max_id"));
                data.put("latest_update", resultSet.getTimestamp("latest_update").getTime());
            }
            log.info("after exec sql data={} cost={}", JSON.toJSONString(data), System.currentTimeMillis() - startTs);
        } catch(Exception e) {
            log.error("querySql failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                }
                resultSet = null;
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
        }
        return data;
    }
    
    @Override
    public void kill(JobContext context) {
        
    }
}