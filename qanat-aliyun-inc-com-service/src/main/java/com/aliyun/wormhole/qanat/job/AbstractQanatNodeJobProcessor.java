package com.aliyun.wormhole.qanat.job;

import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dag.DagInstStatus;
import com.aliyun.wormhole.qanat.api.dag.Node;
import com.aliyun.wormhole.qanat.api.service.MdpDataModelService;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper;
import com.aliyun.wormhole.qanat.service.dag.DagService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Value;

@Slf4j
public abstract class AbstractQanatNodeJobProcessor<T extends Node> extends JavaProcessor {
    
    @Resource
    private TaskInstanceMapper taskInstanceMapper;
    
    @Resource
    private DagService dagService;
    
    @Resource
    private MdpDataModelService mdpDataModelService;

    // 新增OXS地域感知配置
    @Value("${region.type:}")
    private String regionType;

    @Value("${environment.type:}")
    private String environmentType;

    @Value("${qanat.db.oxs.enabled:false}")
    private boolean oxsEnabled;

    /**
     * 根据地域获取数据库连接URL
     * @param dbMetaJson 数据库元数据JSON
     * @return 适合当前地域的数据库连接URL
     */
    protected String getDbConnectionUrl(JSONObject dbMetaJson) {
        if (oxsEnabled && isOxsRegion() && dbMetaJson.containsKey("oxsJdbcUrl")) {
            String oxsJdbcUrl = dbMetaJson.getString("oxsJdbcUrl");
            if (StringUtils.isNotBlank(oxsJdbcUrl)) {
                log.info("Job using OXS JDBC URL for region: {} env: {}", regionType, environmentType);
                return oxsJdbcUrl;
            }
        }
        return dbMetaJson.getString("jdbcUrl");
    }

    /**
     * 判断当前是否为OXS区域
     * @return true如果是OXS区域
     */
    protected boolean isOxsRegion() {
        return "singapore".equalsIgnoreCase(regionType) && 
               StringUtils.containsIgnoreCase(environmentType, "oxs");
    }

    @Override
    public ProcessResult process(JobContext context) {
    	log.info("context.getInstanceParameters={}", context.getInstanceParameters());
        try {
            Map<String, Object> instParamsMap = null;
            if (StringUtils.isNotBlank(context.getInstanceParameters())) {
                instParamsMap = (Map<String, Object>)JSON.parseObject(context.getInstanceParameters(), Map.class);
            }
            if (instParamsMap == null) {
                instParamsMap = (Map<String, Object>)JSON.parseObject(context.getJobParameters(), Map.class);
            }
            DateTime dt = context.getDataTime();
            if (dt != null) {
            	instParamsMap.put("bizDate", dt.toString("yyyyMMdd"));
            }
            JSONObject nodeJson = (JSONObject)instParamsMap.get("node");
            T node = (T)dagService.getNodeByJSONObject(nodeJson);
            String operator = (String)instParamsMap.get("operator");
            String subTaskInstId = String.valueOf(instParamsMap.get("subTaskInstId"));
            Long taskInstId = Long.valueOf(String.valueOf(instParamsMap.get("taskInstId")));

            log.info("start subTaskInstId:{}", subTaskInstId);
            Long startTs = System.currentTimeMillis();
            
            TaskInstance taskInstUpd = new TaskInstance();
            taskInstUpd.setId(Long.valueOf(subTaskInstId));
            taskInstUpd.setGmtModified(new Date());
            taskInstUpd.setStatus(DagInstStatus.EXECUTING.getCode().byteValue());
            taskInstUpd.setHostAddr(InetAddress.getLocalHost().getHostAddress());
            taskInstUpd.setExternalInstId(context.getJobInstanceId() + "");//SchedulerX任务实例id
            taskInstUpd.setModifyEmpid(operator);
            try {
            	taskInstUpd.setHostAddr(InetAddress.getLocalHost().getHostAddress());
            } catch(Exception e) {}
            taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
            
            doProcess(instParamsMap, node);
            
            //全局任务参数更新到主任务实例的参数中
            if (node.isDataBaseline()) {
                TaskInstance taskInst = taskInstanceMapper.selectByPrimaryKey(taskInstId);
                JSONObject execParam = JSON.parseObject(taskInst.getExecParam());
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                execParam.put("incr_sync_start_time", sdf.format(new Date(startTs)));
                taskInstUpd = new TaskInstance();
                taskInstUpd.setId(taskInstId);
                taskInstUpd.setExecParam(JSON.toJSONString(execParam));
                taskInstUpd.setGmtModified(new Date());
                taskInstUpd.setModifyEmpid(operator);
                taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
            }
            
            taskInstUpd = new TaskInstance();
            taskInstUpd.setId(Long.valueOf(subTaskInstId));
            taskInstUpd.setGmtModified(new Date());
            taskInstUpd.setEndTime(new Date());
            taskInstUpd.setModifyEmpid(operator);
            taskInstUpd.setStatus(DagInstStatus.SUCCESS.getCode().byteValue());
            taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
            log.info("Jon exec finished. jobId:{}, jobInstId:{}", context.getJobId(), context.getJobInstanceId());
        } catch (QanatBizException e) {
            log.error("任务调度异常:{}", e.getMessage());
            return new ProcessResult(false, e.getMessage());
        } catch (Exception e) {
            log.error("任务调度异常", e);
            return new ProcessResult(false, e.getMessage());
        }
        return new ProcessResult(true);
    }
    
    abstract void doProcess(Map<String, Object> instParamsMap, T node);
    
    protected void doKill(Map<String, Object> instParamsMap, T node) {
    	
    };
    
    @Override
    public void kill(JobContext context) {
    	String operator = null;
    	String subTaskInstId = null;
    	try {
            Map<String, Object> instParamsMap = null;
            if (StringUtils.isNotBlank(context.getInstanceParameters())) {
                instParamsMap = (Map<String, Object>)JSON.parseObject(context.getInstanceParameters(), Map.class);
            }
            if (instParamsMap == null) {
                instParamsMap = (Map<String, Object>)JSON.parseObject(context.getJobParameters(), Map.class);
            }
            JSONObject nodeJson = (JSONObject)instParamsMap.get("node");
            T node = (T)dagService.getNodeByJSONObject(nodeJson);
            operator = (String)instParamsMap.get("operator");
            subTaskInstId = String.valueOf(instParamsMap.get("subTaskInstId"));

            log.info("start subTaskInstId:{}", subTaskInstId);
            
            doKill(instParamsMap, node);
    	} catch (Exception e) {
            log.error("Kill Blink任务异常:{}", e.getMessage(), e);
    	} finally {
        	TaskInstance taskInstUpd = new TaskInstance();
            taskInstUpd.setId(Long.valueOf(subTaskInstId));
            taskInstUpd.setGmtModified(new Date());
            taskInstUpd.setEndTime(new Date());
            taskInstUpd.setModifyEmpid(operator);
            taskInstUpd.setStatus(DagInstStatus.FAILED.getCode().byteValue());
            taskInstanceMapper.updateByPrimaryKeySelective(taskInstUpd);
    	}
    }
}