package com.aliyun.wormhole.qanat.process;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.wormhole.qanat.api.service.RedisService;

import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * DRC消费活性验证
 * <AUTHOR>
 *
 */
@Slf4j
@Component
public class DrcConsumeAliveCheckProcessor extends JavaProcessor {
    
    @Resource
    private RedisService redisService;
	
	@Override
    public ProcessResult process(JobContext context) {
        JSONObject paramsJson= JSON.parseObject(context.getJobParameters());

        String tenantId = paramsJson.getString("tenantId");
        String appName = paramsJson.getString("appName");
    	Integer thresholdSec = paramsJson.getInteger("thresholdSec") != null ? paramsJson.getInteger("thresholdSec") : 60;
    	JSONArray consumers = paramsJson.getJSONArray("consumers");
        
    	 String groupName = "RM_aly_qanat";
        String identification = "alyqanat";
        String endpoint = "http://140.205.137.192:8080";
    	
	    try {
	    	 for (int j = 0; j < consumers.size(); j++) {
	    		 JSONObject consumer = consumers.getJSONObject(j);
	    		 String consumerId = consumer.getString("consumerId");
	    	     String taskId = consumer.getString("taskId");
	    		 log.info("start to check consumerId:{} taskId:{}", consumerId, taskId);
	    	        
    	        List<JSONObject> topics = getDrcTopics(groupName, identification, taskId, endpoint);
    	        if (topics != null) {
    	            for (int i = 0; i < topics.size(); i++) {
    	                JSONObject cfg = topics.get(i);
    	                Long drcTs = cfg.getLong("checkpoint-end");

    	 	        	String key = "DRC_" + consumerId + "_" + i;
    	 	        	String val = redisService.get(tenantId, appName, key);
    	 	        	if (StringUtils.isBlank(val)) {
    	 	        		log.error("drc_consume_alive_check miss_checkpoint key:{}", key);
    	 	        		break;
    	 	        	}
    	        		long cacheTs = Long.valueOf(val);
	 	        		log.info("drc_consume_alive_check taskId:{} key:{} topic:{} ts:{} cacheTs:{} threshold:{} checkResult:{}", taskId, key, cfg.getString("topic"), drcTs, cacheTs, thresholdSec, Math.abs(drcTs - cacheTs) > thresholdSec);
    	        		
	 	        		if (Math.abs(drcTs - cacheTs) > thresholdSec) {
    	 	        		log.error("drc_consume_alive_check taskId:{} key:{} topic:{} seem not be syncronized", taskId, key, cfg.getString("topic"));
    	 	        		break;
    	    			}
    	            }
    	        }
	    		log.info("finish to check consumerId:{} taskId:{}", consumerId, taskId);
	         }
	    } catch (Exception e) {
	        log.error("get table data monitor failed, error={}", e.getMessage(), e);
	        return new ProcessResult(false);
	    }
        return new ProcessResult(true);
	}
    
    @Override
    public void kill(JobContext context) {
        
    }
    
    private List<JSONObject> getDrcTopics(String username, String password, String taskId, String endpoint) {
        List<JSONObject> topics = new ArrayList<>();
        String url = endpoint + "/api/auth";
        String token = null;
        String data = String.format("taskParam={\"user\":\"%s\",\"password\":\"%s\"}", username, password);
        String resp = doHttpPost(url, data);
        if (StringUtils.isNotBlank(resp)) {
            JSONObject respJson = JSON.parseObject(resp);
            if (respJson != null && respJson.getBoolean("success") && respJson.getJSONObject("data") != null) {
                token = respJson.getJSONObject("data").getString("token");
                
                data = String.format("taskParam={\"taskId\":\"%s\",\"token\":\"%s\"}", taskId, token);
                url = endpoint + "/api/query";
                resp = doHttpPost(url, data);
                if (StringUtils.isNotBlank(resp)) {
                    respJson = JSON.parseObject(resp);
                    if (respJson != null && respJson.getBoolean("success") && respJson.getJSONObject("data") != null) {
                        JSONArray cfgArray =  respJson.getJSONObject("data").getJSONArray("cfg");
                        if (cfgArray != null) {
                            for (int i = 0; i < cfgArray.size(); i++) {
                                JSONObject cfg = cfgArray.getJSONObject(i);
                                topics.add(cfg);
                            }
                        }
                    }
                }
            }
        }
        return topics;
    }
	
	private String doHttpPost(String url, String data) {
        String resp = null;
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(mediaType, data))
                .build();
        OkHttpClient okHttpClient = new OkHttpClient();
        Response response = null;
        try {
            log.info("req:post {} -d'{}'", url, data);
            response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                resp = response.body().string(); 
                log.info("resp:{}", resp); 
            }
        } catch (IOException e) {
            log.error("http request failed", e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return resp;
    }
}