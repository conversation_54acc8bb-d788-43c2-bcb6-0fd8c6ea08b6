package com.aliyun.wormhole.qanat.service.es;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.service.ElasticSearchService;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.apache.http.nio.entity.NStringEntity;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.action.admin.indices.alias.IndicesAliasesRequest;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchAllQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@HSFProvider(serviceInterface = ElasticSearchService.class)
public class ElasticSearchServiceImpl implements ElasticSearchService {
    
    @Resource
    private DatasourceMapper datasourceMapper;
    
    @Resource
    private DbInfoMapper dbInfoMapper;
    
    private Map<String, RestHighLevelClient> rhlClientMap = new ConcurrentHashMap<>();

    private RestHighLevelClient getClient(String tenantId, String dbName) {
    	String key = tenantId + "__" + dbName;
    	if (rhlClientMap.get(key) != null) {
    		return rhlClientMap.get(key);
    	} else {
	        DbInfoExample dbExample = new DbInfoExample();
	        dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName).andTenantIdEqualTo(tenantId).andDbTypeEqualTo("elasticsearch");
	        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
	        if (CollectionUtils.isEmpty(dbs)) {
	            throw new QanatBizException(dbName + " is not found");
	        }
	        DbInfo dbInfo = dbs.get(0);
	        JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
	        
	        HttpHost httpHost = new HttpHost(dbMetaJson.getString("host"), dbMetaJson.getInteger("port"));
	        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
	        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(dbMetaJson.getString("username"), dbMetaJson.getString("password")));
	        RestClientBuilder builder = RestClient.builder(httpHost);
	        builder.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
	            @Override
	            public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
	                return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
	            }
	        }).build();
	        RestHighLevelClient rhlClient = new RestHighLevelClient(builder);
	        rhlClientMap.put(key, rhlClient);
	        return rhlClient;
    	}
    }

    @Override
    public Map<String, Object> queryByPk(String tenantId, String dbName, String index, String pk) {
        SearchHit hit = searchHitByPk(tenantId, dbName, index, pk);
        if (hit == null) {
            return null;
        }
        return hit.getSourceAsMap();
    }

    @Override
    public Long countIndex(String tenantId, String dbName, String index) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.from(0);
        sourceBuilder.size(10);
        MatchAllQueryBuilder matchAllQueryBuilder = QueryBuilders.matchAllQuery();
        sourceBuilder.query(matchAllQueryBuilder);
        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.types("_doc");
        searchRequest.source(sourceBuilder);
        try {
            SearchResponse response = getClient(tenantId, dbName).search(searchRequest);
            SearchHits hits = response.getHits();
            if (hits != null) {
                return hits.getTotalHits();
            }
        } catch (Exception e) {
            log.error("countIndex failed", e);
        }
        return null;
    }

    @Override
    public String upsert(String tenantId, String dbName, String index, String id, Map<String, Object> dataMap) {
        try {
            UpdateRequest request = new UpdateRequest(index, "_doc", id).doc(dataMap, XContentType.JSON).upsert(dataMap);
            UpdateResponse response = getClient(tenantId, dbName).update(request);
            return response.getId();
        } catch (Exception e) {
            log.error("updateByQuery failed", e);
        }
        return null;
    }

    @Override
    public Integer updateByQuery(String tenantId, String dbName, String index, String body) {
        try {
            HttpEntity entity = new NStringEntity(body, ContentType.APPLICATION_JSON);
            Response resp = getClient(tenantId, dbName).getLowLevelClient().performRequest("POST", "/" + index + "/_doc/_update_by_query", Collections.emptyMap(), entity);
            String content = EntityUtils.toString(resp.getEntity());
            log.info("resp:{}", content);
            JSONObject rtnJson = JSON.parseObject(content);
            return rtnJson.getInteger("updated");
        } catch (Exception e) {
            log.error("updateByQuery failed", e);
        }
        return 0;
    }

    @Override
    public Boolean createIndexByClone(String tenantId, String dbName, String newIndex, String oldIndex) {
        log.info("createIndexByClone({},{},{},{})", tenantId, dbName, newIndex, oldIndex);
        try {
            Response resp = getClient(tenantId, dbName).getLowLevelClient().performRequest("get", "/" + oldIndex);
            String content = EntityUtils.toString(resp.getEntity());
            JSONObject rtnJson = JSON.parseObject(content);
            String mappings = rtnJson.getJSONObject(oldIndex).getJSONObject("mappings").toJSONString();
            JSONObject setttingsJson = rtnJson.getJSONObject(oldIndex).getJSONObject("settings");
            setttingsJson.getJSONObject("index").remove("uuid");
            setttingsJson.getJSONObject("index").remove("version");
            setttingsJson.getJSONObject("index").remove("creation_date");
            setttingsJson.getJSONObject("index").remove("provided_name");
            setttingsJson.getJSONObject("index").remove("indexing");
            setttingsJson.getJSONObject("index").remove("search");
            String settings = setttingsJson.toJSONString();
            CreateIndexRequest request = new CreateIndexRequest(newIndex);
            request.settings(settings, XContentType.JSON);
            request.mapping("_doc", mappings, XContentType.JSON);
            getClient(tenantId, dbName).indices().create(request);
            log.info("createIndexByClone({},{},{},{}) finished", tenantId, dbName, newIndex, oldIndex);
            return true;
        } catch (Exception e) {
            log.error("克隆索引mapping失败", e);
        }
        return false;
    }

    @Override
    public Boolean createIndex(String tenantId, String dbName, String index, String settings, String mappings) {
        try {
            CreateIndexRequest request = new CreateIndexRequest(index);
            request.settings(settings, XContentType.JSON);
            request.mapping("_doc", mappings, XContentType.JSON);
            getClient(tenantId, dbName).indices().create(request);
        } catch (Exception e) {
            log.error("指定别名失败", e);
        }
        return false;
    }

    @Override
    public Boolean setIndexAlias(String tenantId, String dbName, String alias, String newIndex, String oldIndex) {
        log.info("setIndexAlias({},{},{},{},{})", tenantId, dbName, alias, newIndex, oldIndex);
        IndicesAliasesRequest request=new IndicesAliasesRequest();
        IndicesAliasesRequest.AliasActions aliasAction = null;
        //旧的删除
        if (StringUtils.isNotBlank(oldIndex)) {
            aliasAction =
                    new IndicesAliasesRequest.AliasActions(IndicesAliasesRequest.AliasActions.Type.REMOVE)
                            .index(oldIndex)
                            .alias(alias);
            request.addAliasAction(aliasAction);
        }
        //新的绑定
        aliasAction =
                new IndicesAliasesRequest.AliasActions(IndicesAliasesRequest.AliasActions.Type.ADD)
                        .index(newIndex)
                        .alias(alias);
        request.addAliasAction(aliasAction);
        try {
            AcknowledgedResponse indicesAliasesResponse =
            		getClient(tenantId, dbName).indices().updateAliases(request);
            log.info("setIndexAlias({},{},{},{},{}) finished", tenantId, dbName, alias, newIndex, oldIndex);
            return indicesAliasesResponse.isAcknowledged();
        } catch (Exception e) {
            log.error("指定别名失败", e);
        }
        return false;
    }

    @Override
    public String getIndexByAlias(String tenantId, String dbName, String alias) {
        log.info("getIndexByAlias({},{},{})", tenantId, dbName, alias);
        try {
            Response response = getClient(tenantId, dbName).getLowLevelClient().performRequest("GET", "/_alias/" + alias, Collections.<String, String>emptyMap());
            if (response != null) {
            	String respBody = EntityUtils.toString(response.getEntity());
                log.info("response={}", respBody);
            	JSONObject respJson = JSON.parseObject(respBody);
            	if (respJson != null && CollectionUtils.isNotEmpty(respJson.keySet())) {
            		for (String key : respJson.keySet()) {
            			return key;
            		}
            	}
            }
            return EntityUtils.toString(response.getEntity());
        } catch (Exception e) {
            log.error("根据别名获取索引失败");
        }
        return null;
    }

    @Override
    public Boolean removeIndexAlias(String tenantId, String dbName, String index, String alias) {
        IndicesAliasesRequest request=new IndicesAliasesRequest();
        //旧的删除
        IndicesAliasesRequest.AliasActions aliasAction =
                    new IndicesAliasesRequest.AliasActions(IndicesAliasesRequest.AliasActions.Type.REMOVE)
                            .index(index)
                            .alias(alias);
            request.addAliasAction(aliasAction);
        try {
            AcknowledgedResponse indicesAliasesResponse =
            		getClient(tenantId, dbName).indices().updateAliases(request);
            return indicesAliasesResponse.isAcknowledged();
        } catch (Exception e) {
            log.info("删除别名失败");
        }
        return false;
    }

    private SearchHit searchHitByPk(String tenantId, String dbName, String index, String pk) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.from(0);
        sourceBuilder.size(10);
        TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery("id", pk);
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        boolBuilder.must(termQueryBuilder);
        sourceBuilder.query(boolBuilder);
        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.types("_doc");
        searchRequest.source(sourceBuilder);
        try {
            SearchResponse response = getClient(tenantId, dbName).search(searchRequest);
            SearchHits hits = response.getHits();
            if (hits != null && hits.getHits().length > 0) {
                return hits.getHits()[0];
            }
        } catch (Exception e) {
            log.error("queryByPk failed", e);
        }
        return null;
    }
}
