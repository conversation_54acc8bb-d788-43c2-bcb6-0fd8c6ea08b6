package com.aliyun.wormhole.qanat.service.impl;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;

import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.api.dto.DataResult;
import com.aliyun.wormhole.qanat.api.dto.CreateOdsRequest;
import com.aliyun.wormhole.qanat.api.service.RtdwTaskService;
import com.aliyun.wormhole.qanat.service.ods.OdsHandler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

import javax.annotation.Resource;

/**
 * ADB实时数仓同步服务
 * <AUTHOR>
 * 2019年7月23日
 */
@Slf4j
@Component
@HSFProvider(serviceInterface = RtdwTaskService.class)
public class RtdwOdsTaskServiceImpl implements RtdwTaskService {
    
    @Resource
    private OdsHandler odsHandler;
    
    @Override
    public DataResult<Map<String, Long>> createDsInfoAndOdsTask(CreateOdsRequest createOdsReq) {
        log.info("start createDsInfoAndOdsTask({})", JSON.toJSONString(createOdsReq));
        DataResult<Map<String, Long>> result = new DataResult<>();
        result.setCode("200");
        result.setSuccess(true);
        try {
        	result.setData(odsHandler.createDsInfoAndOdsTask(createOdsReq));
        } catch (QanatBizException e) {
            result.setSuccess(false);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
            log.error("createDsInfoAndOdsTask failed, error={}", e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(e.getMessage());
            log.error("createDsInfoAndOdsTask failed, error={}", e.getMessage(), e);
        }
        return result;
    }
}