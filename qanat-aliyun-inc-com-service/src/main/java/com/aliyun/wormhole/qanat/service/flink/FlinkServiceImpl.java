package com.aliyun.wormhole.qanat.service.flink;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelation;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.ResourceExample;
import com.aliyun.wormhole.qanat.dal.mapper.AppResourceRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ExtensionMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ResourceMapper;

import com.ververica.common.resp.GetDeploymentResp;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * Blink操作服务类
 * <AUTHOR>
 * 2019年2月1日
 */
@Data
@Slf4j
@Component
@HSFProvider(serviceInterface = FlinkService.class)
public class FlinkServiceImpl implements FlinkService {
	
	@Resource
	private AppResourceRelationMapper appResourceRelationMapper;
	
	@Resource
	private ResourceMapper resourceMapper;
	
	@Resource
	private ExtensionMapper extensionMapper;
    
    private FlinkConf getFlinkConfByAppName(String tenantId, String appName) {
    	AppResourceRelationExample example = new AppResourceRelationExample();
    	example.createCriteria().andAppNameEqualTo(appName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andRelationTypeEqualTo("flink");
    	List<AppResourceRelation> rels = appResourceRelationMapper.selectByExample(example);
    	if (CollectionUtils.isEmpty(rels)) {
    		throw new QanatBizException("no flink app resouces");
    	}
    	AppResourceRelation ref = rels.get(0);
    	ResourceExample example1 = new ResourceExample();
    	example1.createCriteria().andResourceNameEqualTo(ref.getResourceName()).andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L);
    	List<com.aliyun.wormhole.qanat.dal.domain.Resource> resources = resourceMapper.selectByExampleWithBLOBs(example1);
    	if (CollectionUtils.isEmpty(resources)) {
    		throw new QanatBizException("no app resouces:" + ref.getResourceName() + " found");
    	}
    	com.aliyun.wormhole.qanat.dal.domain.Resource resource = resources.get(0);
    	JSONObject metaJson = new JSONObject();
    	if (resource.getParentResourceId() != null) {
    		com.aliyun.wormhole.qanat.dal.domain.Resource parentResource = resourceMapper.selectByPrimaryKey(resource.getParentResourceId());
        	JSONObject parentMetaJson = JSON.parseObject(parentResource.getMeta());
        	metaJson.putAll(parentMetaJson);
    	}
    	metaJson.putAll(JSON.parseObject(resource.getMeta()));
    	FlinkConf conf = new FlinkConf();
    	conf.setAccessId(metaJson.getString("accessId"));
    	conf.setAccessKey(metaJson.getString("accessKey"));
        conf.setNamespace(metaJson.getString("namespace"));
    	conf.setRegionId(metaJson.getString("regionId"));
        conf.setWorkspace(metaJson.getString("workspace"));
    	conf.setProduct(metaJson.getString("product"));
    	conf.setEndpoint(metaJson.getString("endpoint"));
        conf.setDeploymentTarget(metaJson.getString("deploymentTarget"));
		return conf;
	}

	/**
     * 启动任务
     * @param jobName
     * @param startTime
     */
    @Override
    public boolean startJob(String tenantId, String appName, String jobName, Date startTime) {
    	return this.startJob(tenantId, appName, jobName, startTime, null);
    }

	/**
     * 启动任务
     * @param jobName
     * @param startTime
     */
    @Override
    public boolean startJob(String tenantId, String appName, String jobName, Date startTime, Map<String, String> params) {
        log.debug("begin to startJob({},{},{},{})", tenantId, appName, jobName, startTime, JSON.toJSONString(params));
        try {
            FlinkClientV2 flinkClient = new FlinkClientV2(getFlinkConfByAppName(tenantId, appName));
            return flinkClient.startJob(jobName, startTime, params);
        } catch (Exception e) {
            log.error("startJob({}) failed:{}", jobName, e.getMessage(), e);
        } 
        return false;
    }

    /**
     * 启动任务
     * @param jobName
     */
    @Override
    public String getJobStatus(String tenantId, String appName, String jobName) {
        log.debug("begin to getJobStatus({},{},{})", tenantId, appName, jobName);
        try {
            FlinkClient flinkClient = new FlinkClient(getFlinkConfByAppName(tenantId, appName));
            GetDeploymentResp resp =  flinkClient.getJob(jobName);
            if (resp != null && resp.getStatus() != null) {
                return resp.getStatus().getState().toString();
            }
        } catch (Exception e) {
            log.error("getJobStatus({}) failed:{}", jobName, e.getMessage(), e);
        }
        return null;
    }

	/**
     * 启动批任务
     * @param jobName
     */
    @Override
    public boolean startBatchJob(String tenantId, String appName, String jobName, Map<String, String> params) {
        log.debug("begin to startBatchJob({},{},{},{})", tenantId, appName, jobName, JSON.toJSONString(params));
        try {
            FlinkClientV2 flinkClient = new FlinkClientV2(getFlinkConfByAppName(tenantId, appName));
            return flinkClient.startBatchJob(jobName, params);
        } catch (Exception e) {
            log.error("startBatchJob({}) failed:{}", jobName, e.getMessage(), e);
        } 
        return false;
    }
    
    /**
     * 停止任务
     * @param jobName
     */
    @Override
    public void stopJob(String tenantId, String appName, String jobName) throws Exception {
        log.debug("begin to stopJob({},{},{})", tenantId, appName, jobName);
        FlinkClientV2 flinkClient = new FlinkClientV2(getFlinkConfByAppName(tenantId, appName));
        if (!flinkClient.stopJob(jobName)) {
            throw new RuntimeException(jobName + " stop failed");
        }
    }

    @Override
    public void restartJob(String tenantId, String appName, String jobName, Date startTime, Boolean isBatch) throws Exception {
        this.stopJob(tenantId, appName, jobName);
        this.startJob(tenantId, appName, jobName, startTime);
    }

    @Override
    public void restartJob(String tenantId, String appName, String jobName, Map<String, String> params) {
        log.debug("begin to restartJob({},{},{},{})", tenantId, appName, jobName, JSON.toJSONString(params));
        try {
            FlinkClientV2 flinkClient = new FlinkClientV2(getFlinkConfByAppName(tenantId, appName));
            flinkClient.restartJob(jobName, params);
        } catch (Exception e) {
            log.error("restartJob({}) failed:{}", jobName, e.getMessage(), e);
        }
    }

    @Override
    public void createJob(String tenantId, String appName, String jobName, String sql, Boolean isBatch) {
        log.debug("begin to createJob({},{},{},{},{})", tenantId, appName, jobName, sql, isBatch);
        try {
            FlinkClient flinkClient = new FlinkClient(getFlinkConfByAppName(tenantId, appName));
            flinkClient.createJob(jobName, sql, isBatch);
        } catch (Exception e) {
            log.error("createJob({}) failed:{}", jobName, e.getMessage(), e);
        }
    }
}
