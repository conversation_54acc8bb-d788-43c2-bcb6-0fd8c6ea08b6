package com.aliyun.wormhole.qanat.process;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.wormhole.qanat.api.QanatBizException;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceDsRelation;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceDsRelationExample;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample;
import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfo;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelVersionWithBLOBs;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceDsRelationMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DbInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.DsFieldInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelInfoMapper;
import com.aliyun.wormhole.qanat.dal.mapper.ViewModelVersionMapper;
import com.aliyun.wormhole.qanat.service.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.QanatTddlDatasourceHandler;
import com.aliyun.wormhole.qanat.service.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.service.datasource.TddlConnectionParam;
import com.aliyun.wormhole.qanat.service.datatube.DatatubeHandler;
import com.aliyun.wormhole.qanat.service.util.YamlUtil;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelHandler;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModelOptimizer;
import com.taobao.unifiedsession.core.commons.utils.DateUtils;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * 管道实例通用数据稽核任务
 * <AUTHOR>
 * 2019年7月24日
 */
@Slf4j
@Component
public class DatatubeInstanceDataCheckProcessor extends JavaProcessor {

	@Resource
    private DbInfoMapper dbInfoMapper;
    
    @Resource 
    private DatatubeInstanceMapper datatubeInstanceMapper;
    
    @Resource 
    private DatatubeInstanceDsRelationMapper datatubeInstanceDsRelationMapper;
    
    @Resource 
    private DatasourceMapper dsInfoMapper;
    
    @Resource 
    private DsFieldInfoMapper dsFieldInfoMapper;
    
    @Resource
    private QanatDatasourceHandler dsHandler;
    
    @Resource
    private ViewModelHandler viewModelHandler;
    
    @Resource
    private ViewModelInfoMapper viewModelInfoMapper;
    
    @Resource
    private ViewModelVersionMapper viewModelVersionMapper;
    
    @Resource
    private ViewModelOptimizer viewModelOptimizer;
    
    @Resource
    private DatatubeHandler datatubeHandler;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            log.info("DatatubeInstanceDataCheckProcessor, param=[]", context.getJobParameters());
            JSONObject paramsJson= JSON.parseObject(context.getJobParameters());
            String tenantId = paramsJson.getString("tenantId");
            String checkCode = paramsJson.getString("checkCode");
            String provider = paramsJson.getString("provider");
            String type = paramsJson.getString("type");
            String ref = paramsJson.getString("ref");
            String datatubeInstIds = paramsJson.getString("datatubeInstIds");
            DatatubeInstanceExample example = new DatatubeInstanceExample();
            DatatubeInstanceExample.Criteria criteria = example.createCriteria();
            criteria.andTenantIdEqualTo(tenantId).andIsDeletedEqualTo(0L).andIsTestEqualTo(0L);
            if (StringUtils.isNotBlank(provider)) {
            	criteria.andProviderEqualTo(provider);
            }
            if (StringUtils.isNotBlank(type)) {
            	criteria.andTypeEqualTo(type);
            }
            if (StringUtils.isNotBlank(datatubeInstIds)) {
            	List<Long> datatubeInstIdList = new ArrayList<>();
            	for (String datatubeInstId : datatubeInstIds.split(",")) {
            		datatubeInstIdList.add(Long.valueOf(datatubeInstId));
            	}
            	criteria.andIdIn(datatubeInstIdList);
            }
            List<DatatubeInstance> datatubeInstList = datatubeInstanceMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(datatubeInstList)) {
            	return new ProcessResult(false, "no datatube instances found");
            }
            
        	for (DatatubeInstance inst : datatubeInstList) {
            	log.info("datatube[{}-{}-{}] check started", inst.getId(), inst.getName(), inst.getProviderId());
            	if ("viewmodel".equalsIgnoreCase(inst.getProvider())) {
                	datatubeHandler.makeupViewModelDatatubeTasks(inst.getId(), null, "schedulerx2");
            	} else if ("ods".equalsIgnoreCase(inst.getProvider())) {
                	datatubeHandler.makeupOdsDatatubeTasks(inst.getProviderId(), inst.getLevel(), "schedulerx2");
            	}
            	
            	Map<String, Object> checkResult = new HashMap<>();
            	Map<String, Long> metric = new HashMap<>();
            	checkResult.put("metric", metric);
            	checkResult.put("batchNo", "TableCheck-" + context.getJobInstanceId());
            	Map<String, String> dbInfo = new HashMap<>();
            	checkResult.put("dbInfo", dbInfo);
            	Map<String, Long> fromDsMetric = new HashMap<>();
            	try {
            		if ("originDb".equalsIgnoreCase(ref) && "ods".equalsIgnoreCase(provider)) {
	            		DatatubeInstanceDsRelationExample didrExample = new DatatubeInstanceDsRelationExample();
	            		didrExample.createCriteria().andIsDeletedEqualTo(0L).andDatatubeInstIdEqualTo(inst.getId()).andRelationTypeEqualTo("from_ds");
	            		List<DatatubeInstanceDsRelation> fromDsList = datatubeInstanceDsRelationMapper.selectByExample(didrExample);
	                	if (CollectionUtils.isEmpty(fromDsList)) {
	                    	log.error("datatube[{}-{}-{}] has no from_ds", inst.getId(), inst.getName(), inst.getProviderId());
	                		continue;
	                	}
	                	DatatubeInstanceDsRelation fromDs = fromDsList.get(0);
	                	DatasourceExample dsInfoExample = new DatasourceExample();
	                	dsInfoExample.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsNameEqualTo(fromDs.getDsName());
	                	List<Datasource> dsInfos = dsInfoMapper.selectByExample(dsInfoExample);
	                	if (CollectionUtils.isEmpty(dsInfos)) {
	                		throw new QanatBizException("dsInfo:" + fromDs.getDsName() + " is not found");
	                	}
	                	Datasource fromDsInfo = dsInfos.get(0);
	                	dbInfo.put("db0", fromDsInfo.getDbName());
	                	checkResult.put("tableName", fromDsInfo.getTableName());
	                	checkResult.put("checkTime", DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
	                	fromDsMetric = this.getDsMetrics(inst.getTenantId(), fromDsInfo);
	                	if (fromDsMetric == null || CollectionUtils.isEmpty(fromDsMetric.keySet())) {
	                    	log.error("datatube[{}-{}-{}] can not get from_ds metric", inst.getId(), inst.getName(), inst.getProviderId());
	                		continue;
	                	}
	                	metric.put("db0_total_cnt", fromDsMetric.get("total_cnt"));
	                	metric.put("db0_max_id", fromDsMetric.get("max_id"));
	                	metric.put("db0_latest_update", fromDsMetric.get("latest_update"));
            		} else if ("originMainObjDb".equalsIgnoreCase(ref) && "viewmodel".equalsIgnoreCase(provider)) {
            			ViewModelInfoExample vmExample = new ViewModelInfoExample();
            			vmExample.createCriteria().andTenantIdEqualTo(tenantId).andIdEqualTo(inst.getProviderId()).andIsDeletedEqualTo(0L);
            			List<ViewModelInfo> viewModelInfos = viewModelInfoMapper.selectByExample(vmExample);
            			if (CollectionUtils.isEmpty(viewModelInfos)) {
            				throw new QanatBizException("tenant check failed");
            			}
            			ViewModelInfo viewModelInfo = viewModelInfos.get(0);
            			
            	        ViewModelVersionWithBLOBs modelVersion = viewModelVersionMapper.selectByPrimaryKey(viewModelInfo.getVersionId());

            	        ViewModel originModel = YamlUtil.getViewModel(modelVersion.getUserYaml());
            	    	ViewModel sysModel = null;
            	    	if (originModel.isDynamic()) {
            	    		sysModel = viewModelOptimizer.getOptimizedViewModel(tenantId, modelVersion.getUserYaml());
            	    	} else {
            	    		sysModel = YamlUtil.getViewModel(modelVersion.getSysYaml());
            	    	}
            	    	ViewModel dataModel = new ViewModel();
            	    	BeanUtils.copyProperties(sysModel, dataModel);
            			
	            		DatatubeInstanceDsRelationExample didrExample = new DatatubeInstanceDsRelationExample();
	            		didrExample.createCriteria().andIsDeletedEqualTo(0L).andDatatubeInstIdEqualTo(inst.getId()).andRelationTypeEqualTo("from_ds").andDsNameEqualTo(dataModel.getObject().getRef());
	            		List<DatatubeInstanceDsRelation> fromDsList = datatubeInstanceDsRelationMapper.selectByExample(didrExample);
	                	if (CollectionUtils.isEmpty(fromDsList)) {
	                    	log.error("datatube[{}-{}-{}] has no from_ds", inst.getId(), inst.getName(), inst.getProviderId());
	                		continue;
	                	}
	                	DatatubeInstanceDsRelation fromDs = fromDsList.get(0);
	                	DatasourceExample dsInfoExample = new DatasourceExample();
	                	dsInfoExample.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsNameEqualTo(fromDs.getDsName());
	                	List<Datasource> dsInfos = dsInfoMapper.selectByExample(dsInfoExample);
	                	if (CollectionUtils.isEmpty(dsInfos)) {
	                		throw new QanatBizException("dsInfo:" + fromDs.getDsName() + " is not found");
	                	}
	                	Datasource fromDsInfo = dsInfos.get(0);
	                	dbInfo.put("db0", fromDsInfo.getDbName());
	                	checkResult.put("tableName", fromDsInfo.getTableName());
	                	checkResult.put("checkTime", DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
	                	fromDsMetric = this.getDsMetrics(inst.getTenantId(), fromDsInfo);
	                	if (fromDsMetric == null || CollectionUtils.isEmpty(fromDsMetric.keySet())) {
	                    	log.error("datatube[{}-{}-{}] can not get from_ds metric", inst.getId(), inst.getName(), inst.getProviderId());
	                		continue;
	                	}
	                	metric.put("db0_total_cnt", fromDsMetric.get("total_cnt"));
	                	metric.put("db0_max_id", fromDsMetric.get("max_id"));
	                	metric.put("db0_latest_update", fromDsMetric.get("latest_update"));
            		} else if ("etlDb".equalsIgnoreCase(ref)) {
            			String etlDbName = viewModelHandler.getEtlDbName(tenantId);
            			
            			DatatubeInstanceDsRelationExample didrExample = new DatatubeInstanceDsRelationExample();
	            		didrExample.createCriteria().andIsDeletedEqualTo(0L).andDatatubeInstIdEqualTo(inst.getId()).andRelationTypeEqualTo("to_ds");
	            		List<DatatubeInstanceDsRelation> toDsList = datatubeInstanceDsRelationMapper.selectByExample(didrExample);
	                	if (CollectionUtils.isEmpty(toDsList)) {
	                    	log.error("datatube[{}-{}-{}] has no to_ds", inst.getId(), inst.getName(), inst.getProviderId());
	                		continue;
	                	}
	                	for (int i = 0; i < toDsList.size(); i++) {
		                	DatatubeInstanceDsRelation toDs = toDsList.get(i);
		                	DatasourceExample dsInfoExample = new DatasourceExample();
		                	dsInfoExample.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsNameEqualTo(toDs.getDsName());
		                	List<Datasource> dsInfos = dsInfoMapper.selectByExample(dsInfoExample);
		                	if (CollectionUtils.isEmpty(dsInfos)) {
		                		throw new QanatBizException("dsInfo:" + toDs.getDsName() + " is not found");
		                	}
		                	Datasource toDsInfo = dsInfos.get(0);
		                	dbInfo.put("db0", toDsInfo.getDbName());
		                	if (etlDbName.equalsIgnoreCase(toDsInfo.getDbName())) {
			                	checkResult.put("tableName", toDsInfo.getTableName());
			                	checkResult.put("checkTime", DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
			                	fromDsMetric = this.getDsMetrics(inst.getTenantId(), toDsInfo);
			                	if (fromDsMetric == null || CollectionUtils.isEmpty(fromDsMetric.keySet())) {
			                    	log.error("datatube[{}-{}-{}] can not get from_ds metric", inst.getId(), inst.getName(), inst.getProviderId());
			                		continue;
			                	}
			                	metric.put("db0_total_cnt", fromDsMetric.get("total_cnt"));
			                	metric.put("db0_max_id", fromDsMetric.get("max_id"));
			                	metric.put("db0_latest_update", fromDsMetric.get("latest_update"));
			                	break;
		                	}
	                	}
            		} else {
            			log.error("datatube[{}-{}-{}] check failed due to config error", inst.getId(), inst.getName(), inst.getProviderId());
            			continue;
            		}

            		DatatubeInstanceDsRelationExample didrExample = new DatatubeInstanceDsRelationExample();
            		didrExample.createCriteria().andIsDeletedEqualTo(0L).andDatatubeInstIdEqualTo(inst.getId()).andRelationTypeEqualTo("to_ds");
            		List<DatatubeInstanceDsRelation> toDsList = datatubeInstanceDsRelationMapper.selectByExample(didrExample);
                	if (CollectionUtils.isEmpty(toDsList)) {
                    	log.error("datatube[{}-{}-{}] has no to_ds", inst.getId(), inst.getName(), inst.getProviderId());
                		continue;
                	}
                	for (int i = 0; i < toDsList.size(); i++) {
                		DatatubeInstanceDsRelation toDs = toDsList.get(i);
                    	log.error("datatube[{}-{}-{}] start to get metric from {}", inst.getId(), inst.getName(), inst.getProviderId(), toDs.getDsName());
                		DatasourceExample dsInfoExample = new DatasourceExample();
                    	dsInfoExample.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsNameEqualTo(toDs.getDsName());
                    	List<Datasource> dsInfos = dsInfoMapper.selectByExample(dsInfoExample);
                    	if (CollectionUtils.isEmpty(dsInfos)) {
                    		throw new QanatBizException("dsInfo:" + toDs.getDsName() + " is not found");
                    	}
                    	Datasource toDsInfo = dsInfos.get(0);
	                	dbInfo.put("db" + (i + 1), toDsInfo.getDbName());
	                	Map<String, Long> toDsMetric = this.getDsMetrics(inst.getTenantId(), toDsInfo);
	                	if (toDsMetric == null || CollectionUtils.isEmpty(toDsMetric.keySet())) {
	                    	log.error("datatube[{}-{}-{}] can not get to_ds metric", inst.getId(), inst.getName(), inst.getProviderId());
	                		continue;
	                	}
	                	metric.put("db" + (i + 1) + "_total_cnt", toDsMetric.get("total_cnt"));
	                	metric.put("db" + (i + 1) + "_max_id", toDsMetric.get("max_id"));
	                	metric.put("db" + (i + 1) + "_latest_update", toDsMetric.get("latest_update"));
	                	metric.put("db" + (i + 1) + "_total_cnt_gap", fromDsMetric.get("total_cnt") - toDsMetric.get("total_cnt"));
                		metric.put("db" + (i + 1) + "_max_id_gap", fromDsMetric.get("max_id") - toDsMetric.get("max_id"));
                		metric.put("db" + (i + 1) + "_latest_update_gap", fromDsMetric.get("latest_update") - toDsMetric.get("latest_update"));
            		}
                	log.info("qanat_common_datacheck {} result={}", checkCode, JSON.toJSONString(checkResult));
                	log.info("datatube[{}-{}-{}] check finished", inst.getId(), inst.getName(), inst.getProviderId());
            	} catch(Exception e) {
            		log.error("datatube[{}-{}-{}] check failed, error={}", inst.getId(), inst.getName(), inst.getProviderId(), e.getMessage(), e);
            	}
        	}
        } catch (QanatBizException e) {
            log.error("DatatubeInstanceDataCheckProcessor任务调度异常:{}", e.getMessage());
            return new ProcessResult(false, e.getMessage());
        } catch (Exception e) {
            log.error("DatatubeInstanceDataCheckProcessor任务调度异常", e);
            return new ProcessResult(false, e.getMessage());
        }
        return new ProcessResult(true);
    }
    
    private Map<String, Long> getDsMetrics(String tenantId, Datasource dsInfo) {
    	String pkField = dsInfo.getPkFields();
    	String gmtModifiedField = "gmt_modified";
    	String sql = "select count(1) as total_cnt, max(" + pkField + ") as max_id, max(" + gmtModifiedField + ") as latest_update from " + dsInfo.getTableName();
    	
    	DsFieldInfoExample dfiExample = new DsFieldInfoExample();
    	dfiExample.createCriteria().andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId).andDsNameEqualTo(dsInfo.getDsName()).andFieldNameEqualTo(gmtModifiedField);
    	int cnt = dsFieldInfoMapper.countByExample(dfiExample);
    	if (cnt == 0) {
	    	sql = "select count(1) as total_cnt, max(" + pkField + ") as max_id, now() as latest_update from " + dsInfo.getTableName();
    	}

	    Connection connection = null;
	    try {
		    JSONObject dbMetaJson = getAdbDbMeta(dsInfo.getDbName());
		    String hint = "";
	        if (dbMetaJson.containsKey("appName")) {
	        	TddlConnectionParam param = new TddlConnectionParam();
	        	param.setAppName(dbMetaJson.getString("appName"))
	        		.setAccessKey("accessKey")
	        		.setSecretKey("secretKey");
	        	connection = QanatTddlDatasourceHandler.connectToTable(param);
	        	hint = "/*+TDDL({\"extra\":{\"ALLOW_FULL_TABLE_SCAN\":\"TRUE\"}})*//*+TDDL_GROUP({groupIndex:1})*//*+TDDL({'extra':{'SOCKET_TIMEOUT':'10000'}})*/";
	        } else {
		    	RdsConnectionParam param = new RdsConnectionParam();
			    param.setUrl(dbMetaJson.getString("jdbcUrl"))
			        .setUserName(dbMetaJson.getString("username"))
			        .setPassword(dbMetaJson.getString("password"));
		        connection = dsHandler.connectToTable(param);
	        }

	        return querySql(connection, dsInfo.getDbName(), hint + sql);
        	
	    } catch (Exception e) {
	        log.error("get table data monitor failed, error={}", e.getMessage(), e);
	        throw new QanatBizException(e.getMessage());
	    } finally {
            if (connection != null) {
                try {
                	connection.close();
                } catch (SQLException e) {
                }
                connection = null;
            }
	    }
    }

    private JSONObject getAdbDbMeta(String dbName) {
        DbInfoExample example = new DbInfoExample();
        example.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName);
        List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new QanatBizException("no db found");
        }
        String dbMeta = dbs.get(0).getMeta();
        JSONObject dbMetaJson = JSON.parseObject(dbMeta);
        return dbMetaJson;
    }

    private Map<String, Long> querySql(Connection connection, String dbName, String sql) {
        Map<String, Long> data = new HashMap<>();
        log.info("before exec dbName={} sql={}", dbName, sql);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            Long startTs = System.currentTimeMillis();
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                data.put("total_cnt", resultSet.getLong("total_cnt"));
                data.put("max_id", resultSet.getLong("max_id"));
                data.put("latest_update", resultSet.getTimestamp("latest_update").getTime());
            }
            log.info("after exec sql data={} cost={}", JSON.toJSONString(data), System.currentTimeMillis() - startTs);
        } catch(Exception e) {
            log.error("querySql failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                }
                resultSet = null;
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
        }
        return data;
    }
    
    @Override
    public void kill(JobContext context) {
        
    }
}