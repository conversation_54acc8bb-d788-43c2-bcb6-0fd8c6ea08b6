package com.aliyun.wormhole.qanat.service.util;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.SafeConstructor;

import com.alibaba.fastjson.JSON;
import com.aliyun.wormhole.qanat.service.mdp.AnalyticModel;
import com.aliyun.wormhole.qanat.service.viewmodel.ViewModel;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class YamlUtil {

    public static AnalyticModel getDataModel(String dataModelYaml) {
        try {
            Yaml yaml = new Yaml(new SafeConstructor());
            Object obj = yaml.load(dataModelYaml);
            return JSON.parseObject(JSON.toJSONString(obj), AnalyticModel.class);
        } catch(Exception e) {
            log.error("getViewModel failed, {}", e.getMessage(), e);
        }
        return null;
    }

    public static ViewModel getViewModel(String dataModelYaml) {
        try {
            Yaml yaml = new Yaml(new SafeConstructor());
            dataModelYaml = dataModelYaml.replace("!!com.aliyun.wormhole.qanat.service.viewmodel.ViewModel", "");
            Object obj = yaml.load(dataModelYaml);
            return JSON.parseObject(JSON.toJSONString(obj), ViewModel.class);
        } catch(Exception e) {
            log.error("getViewModel failed, {}", e.getMessage(), e);
        }
        return null;
    }

    public static String getYaml(Object data) {
        try {
            Yaml yaml = new Yaml();
            return yaml.dump(data);
        } catch(Exception e) {
            log.error("getViewModel failed, {}", e.getMessage(), e);
        }
        return null;
    }

	public static List<String> getModelColNameTypes(ViewModel dataModel) {
		List<ViewModel.Field> columnList = getColumnsByModel(dataModel);
		List<String> colDefList = new ArrayList<>();
		for (ViewModel.Field field : columnList) {
			String code = field.getCode();
			if (StringUtils.isBlank(code)) {
				code = field.getRef();
			}
			String type = StringUtils.isBlank(field.getType()) ? "varchar" : ("datetime".equalsIgnoreCase(field.getType()) ? "timestamp" : field.getType());
			if (field.getObject() != null) {
				type = "varchar";
			}
		    colDefList.add("`" + code + "` " + type);
		    if (field.isEnums()) {
		        colDefList.add("`" + code+ "_desc` varchar");
		    }
		}
		return colDefList;
	}

	public static List<ViewModel.Field> getColumnsByModel(ViewModel dataModel) {
		List<ViewModel.Field> columnList = new ArrayList<>();
		columnList.addAll(dataModel.getObject().getFields());
		
		if (CollectionUtils.isNotEmpty(dataModel.getRelatedObjects())) {
			for (ViewModel.RelatedDataObject object : dataModel.getRelatedObjects()) {
				List<String> joinOnFields = object.getRelations().stream().map(e -> e.getField()).collect(Collectors.toList());
				
				for (ViewModel.Field field : object.getFields()) {
					if (joinOnFields.contains(field.getCode())) {
						continue;
					}
					columnList.add(field);
				}
			}
		}
		return columnList;
	}
}