package com.aliyun.wormhole.qanat.service.viewmodel;

import com.alibaba.fastjson.JSON;
import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper;
import com.aliyun.wormhole.qanat.service.template.FlinkSyncTemplate;
import com.aliyun.wormhole.qanat.service.util.YamlUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * FlinkSqlBuilder测试类
 * 基于TDD思路，先编写测试用例，再实现功能
 */
@ExtendWith(MockitoExtension.class)
class FlinkSqlBuilderTest {
    
    @Mock
    private ViewModelSqlBuilder viewModelSqlBuilder;
    
    @Mock
    private DatasourceMapper datasourceMapper;
    
    @Mock
    private FlinkSyncTemplate flinkSyncTemplate;
    
    @InjectMocks
    private FlinkSqlBuilder flinkSqlBuilder;
    
    private ViewModel testViewModel;
    private Datasource testDatasource;
    
    @BeforeEach
    void setUp() {
        // 创建测试数据
        testViewModel = createTestViewModel();
        testDatasource = createTestDatasource();
    }
    
    @Test
    void testGenerateFlinkJobSql_ShouldGenerateCompleteSQL() {
        // Given
        String tenantId = "test_tenant";
        String jobName = "test_job";
        when(datasourceMapper.selectByExample(any())).thenReturn(Arrays.asList(testDatasource));
        when(viewModelSqlBuilder.getSelectSql(tenantId, testViewModel, "hologres"))
            .thenReturn("SELECT \"id\", \"name\" FROM test_table");
        
        // When
        String result = flinkSqlBuilder.generateFlinkJobSql(tenantId, testViewModel, jobName);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).contains("CREATE TABLE");
        assertThat(result).contains("INSERT INTO");
        assertThat(result).contains("hologres");
        assertThat(result).contains("\"id\"");
        assertThat(result).contains("\"name\"");
        assertThat(result).contains(jobName);
    }
    
    @Test
    void testGenerateFlinkSourceTableSql_MetadataObject_ShouldGenerateHologresBinlogSource() {
        // Given
        String tenantId = "test_tenant";
        ViewModel.DataObject dataObject = createMetadataObject();
        when(datasourceMapper.selectByExample(any())).thenReturn(Arrays.asList(testDatasource));
        
        // When
        String result = flinkSqlBuilder.generateFlinkSourceTableSql(tenantId, dataObject);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).contains("CREATE TABLE");
        assertThat(result).contains("'connector' = 'hologres'");
        assertThat(result).contains("'binlog' = 'true'");
        assertThat(result).contains("'binlog.scan.startup.mode' = 'latest'");
    }
    
    @Test
    void testGenerateFlinkSourceTableSql_TableObject_ShouldGenerateHologresTableSource() {
        // Given
        String tenantId = "test_tenant";
        ViewModel.DataObject dataObject = createTableObject();
        when(datasourceMapper.selectByExample(any())).thenReturn(Arrays.asList(testDatasource));
        
        // When
        String result = flinkSqlBuilder.generateFlinkSourceTableSql(tenantId, dataObject);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).contains("CREATE TABLE");
        assertThat(result).contains("'connector' = 'hologres'");
        assertThat(result).doesNotContain("'binlog' = 'true'");
    }
    
    @Test
    void testGenerateFlinkInsertSql_ShouldAdaptToFlinkSyntax() {
        // Given
        String tenantId = "test_tenant";
        when(viewModelSqlBuilder.getSelectSql(tenantId, testViewModel, "hologres"))
            .thenReturn("SELECT \"id\", \"name\", DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') FROM test_table");
        
        // When
        String result = flinkSqlBuilder.generateFlinkInsertSql(tenantId, testViewModel);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).startsWith("INSERT INTO");
        assertThat(result).contains(testViewModel.getCode());
        assertThat(result).contains("SELECT");
        // 验证Flink语法适配
        assertThat(result).contains("DATE_FORMAT");
        assertThat(result).contains("yyyy-MM-dd HH:mm:ss");
    }
    
    @Test
    void testMapToFlinkType_ShouldMapCorrectly() {
        // 使用反射测试私有方法
        String stringType = invokePrivateMethod("mapToFlinkType", "varchar");
        String intType = invokePrivateMethod("mapToFlinkType", "int");
        String timestampType = invokePrivateMethod("mapToFlinkType", "datetime");
        String decimalType = invokePrivateMethod("mapToFlinkType", "decimal");
        
        assertThat(stringType).isEqualTo("STRING");
        assertThat(intType).isEqualTo("INT");
        assertThat(timestampType).isEqualTo("TIMESTAMP(3)");
        assertThat(decimalType).isEqualTo("DECIMAL(18,2)");
    }
    
    @Test
    void testGenerateFlinkStreamJobSql_ShouldHandleDifferentObjectTypes() {
        // Given
        String tenantId = "test_tenant";
        String jobName = "stream_job";
        String topicName = "test_topic";
        ViewModel.DataObject dataObject = createMetadataObject();
        
        // When
        String result = flinkSqlBuilder.generateFlinkStreamJobSql(tenantId, dataObject, jobName, topicName, false);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).contains("-- Author: Datatube Flink Generator");
        assertThat(result).contains("CREATE TEMPORARY FUNCTION");
        assertThat(result).contains("CREATE TABLE");
        assertThat(result).contains("INSERT INTO");
    }
    
    @Test
    void testGenerateFlinkBatchJobSql_ShouldGenerateBatchConfiguration() {
        // Given
        String tenantId = "test_tenant";
        String jobName = "batch_job";
        String topicName = "batch_topic";
        ViewModel.DataObject dataObject = createTableObject();
        
        // When
        String result = flinkSqlBuilder.generateFlinkBatchJobSql(tenantId, dataObject, jobName, topicName);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).contains("-- Flink批量作业配置");
        assertThat(result).contains("SET 'execution.runtime-mode' = 'BATCH'");
    }
    
    // ==================== 测试数据创建方法 ====================
    
    private ViewModel createTestViewModel() {
        ViewModel model = new ViewModel();
        model.setCode("test_model");
        model.setName("测试模型");
        
        ViewModel.DataObject object = new ViewModel.DataObject();
        object.setCode("test_table");
        object.setRef("test_datasource");
        object.setType("metadata");
        
        ViewModel.Field field1 = new ViewModel.Field();
        field1.setCode("id");
        field1.setType("bigint");
        field1.setPk(true);
        
        ViewModel.Field field2 = new ViewModel.Field();
        field2.setCode("name");
        field2.setType("varchar");
        
        ViewModel.Field field3 = new ViewModel.Field();
        field3.setCode("create_time");
        field3.setType("datetime");
        
        object.setFields(Arrays.asList(field1, field2, field3));
        model.setObject(object);
        
        return model;
    }
    
    private ViewModel.DataObject createMetadataObject() {
        ViewModel.DataObject object = new ViewModel.DataObject();
        object.setCode("metadata_obj");
        object.setRef("metadata_datasource");
        object.setType("metadata");
        
        ViewModel.Field field = new ViewModel.Field();
        field.setCode("id");
        field.setType("bigint");
        field.setPk(true);
        
        object.setFields(Arrays.asList(field));
        return object;
    }
    
    private ViewModel.DataObject createTableObject() {
        ViewModel.DataObject object = new ViewModel.DataObject();
        object.setCode("table_obj");
        object.setRef("table_datasource");
        object.setType("table");
        
        ViewModel.Field field = new ViewModel.Field();
        field.setCode("id");
        field.setType("bigint");
        field.setPk(true);
        
        object.setFields(Arrays.asList(field));
        return object;
    }
    
    private Datasource createTestDatasource() {
        Datasource ds = new Datasource();
        ds.setDsName("test_datasource");
        ds.setDsType("hologres");
        ds.setTableName("test_table");
        ds.setMeta("{\"jdbcUrl\":\"********************************\",\"username\":\"test\",\"password\":\"test\",\"endpoint\":\"test_endpoint\"}");
        return ds;
    }
    
    @SuppressWarnings("unchecked")
    private <T> T invokePrivateMethod(String methodName, Object... args) {
        try {
            java.lang.reflect.Method method = FlinkSqlBuilder.class.getDeclaredMethod(methodName, String.class);
            method.setAccessible(true);
            return (T) method.invoke(flinkSqlBuilder, args);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke private method: " + methodName, e);
        }
    }
}
