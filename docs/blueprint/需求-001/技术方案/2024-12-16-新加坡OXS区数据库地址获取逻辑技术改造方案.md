# 新加坡OXS区数据库地址获取逻辑技术改造方案

## 1. 改造概述

### 1.1 改造目标
在现有Qanat系统基础上，增加对新加坡OXS区数据库地址的支持，实现地域化的数据库配置管理，确保新加坡站点能够优先使用OXS数据库连接地址。

### 1.2 改造原则
- **向后兼容**: 保持现有功能不受影响
- **配置驱动**: 通过配置控制功能开关
- **性能优先**: 最小化性能影响
- **可扩展性**: 支持未来其他地域扩展

## 2. 系统架构分析

### 2.1 当前架构分析
根据blueprint文档和代码分析，当前数据库连接管理架构：

```
┌─────────────────────────────────────────┐
│           应用层                         │
│  DataTubeController等REST API            │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────┴───────────────────────┐
│           服务层                         │
│  DatasourceServiceImpl                  │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────┴───────────────────────┐
│           数据访问层                     │
│  DatasourceMapper, DbInfoMapper         │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────┴───────────────────────┐
│           数据存储层                     │
│  datasource表, dbinfo表                │
└─────────────────────────────────────────┘
```

### 2.2 改造后架构设计
```
┌─────────────────────────────────────────┐
│           应用层                         │
│  DataTubeController等REST API            │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────┴───────────────────────┐
│           服务层                         │
│  DatasourceServiceImpl + RegionAware    │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────┴───────────────────────┐
│         配置管理层                       │
│  OxsRegionConfigService                 │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────┴───────────────────────┐
│           数据访问层                     │
│  DatasourceMapper, DbInfoMapper         │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────┴───────────────────────┐
│           数据存储层                     │
│  datasource表, dbinfo表(扩展oxsJdbcUrl) │
└─────────────────────────────────────────┘
```

## 3. 详细技术方案

### 3.1 核心改造点

#### 3.1.1 DatasourceServiceImpl改造

**文件路径**: `qanat-aliyun-inc-com-service/src/main/java/com/aliyun/wormhole/qanat/service/base/DatasourceServiceImpl.java`

**改造内容**:
```java
// 在DatasourceServiceImpl类中增加以下方法和字段

@Value("${region.type:}")
private String regionType;

@Value("${environment.type:}")
private String environmentType;

@Value("${qanat.db.oxs.enabled:false}")
private boolean oxsEnabled;

/**
 * 根据地域获取适合的JDBC URL
 * @param dbMetaJson 数据库元数据JSON
 * @return 适合当前地域的JDBC URL
 */
private String getJdbcUrlByRegion(JSONObject dbMetaJson) {
    // 检查是否启用OXS功能
    if (!oxsEnabled) {
        return dbMetaJson.getString("jdbcUrl");
    }
    
    // 检查当前环境是否为新加坡OXS区
    if (isOxsRegion() && dbMetaJson.containsKey("oxsJdbcUrl")) {
        String oxsJdbcUrl = dbMetaJson.getString("oxsJdbcUrl");
        if (StringUtils.isNotBlank(oxsJdbcUrl)) {
            log.info("Using OXS JDBC URL for region: {}, env: {}", regionType, environmentType);
            return oxsJdbcUrl;
        }
    }
    
    // 默认使用标准jdbcUrl
    return dbMetaJson.getString("jdbcUrl");
}

/**
 * 判断当前是否为OXS区域
 * @return true如果是OXS区域
 */
private boolean isOxsRegion() {
    return "singapore".equalsIgnoreCase(regionType) && 
           StringUtils.containsIgnoreCase(environmentType, "oxs");
}

// 修改现有的getDbMetaByDsName方法
@Override
public JSONObject getDbMetaByDsName(String tenantId, String dsName) {
    JSONObject srcDsMetaJson = null;
    DatasourceExample example = new DatasourceExample();
    example.createCriteria().andDsNameEqualTo(dsName).andIsDeletedEqualTo(0L).andTenantIdEqualTo(tenantId);
    List<Datasource> dsList = datasourceMapper.selectByExampleWithBLOBs(example);
    if (CollectionUtils.isEmpty(dsList)) {
        throw new QanatBizException("Datasource:" + dsName + " doesn't exists!");
    }
    Datasource ds = dsList.get(0);
    srcDsMetaJson = JSON.parseObject(ds.getMeta());
    srcDsMetaJson.put("dsType", ds.getDsType());
    srcDsMetaJson.put("table", ds.getTableName());
    srcDsMetaJson.put("dbName", ds.getDbName());
    srcDsMetaJson.put("dsId", ds.getId());

    DbInfo dbInfo = getDbInfoByName(tenantId, ds.getDbName());
    JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
    
    // 改造点：使用地域感知的JDBC URL获取逻辑
    String jdbcUrl = getJdbcUrlByRegion(dbMetaJson);
    srcDsMetaJson.put("jdbcUrl", jdbcUrl);
    
    srcDsMetaJson.put("username", dbMetaJson.getString("username"));
    srcDsMetaJson.put("password", dbMetaJson.getString("password"));
   
    // ... 其他现有逻辑保持不变 ...
    return srcDsMetaJson;
}
```

#### 3.1.2 配置管理增强

**文件路径**: `qanat-aliyun-inc-com-start/src/main/resources/application.properties`

**新增配置项**:
```properties
# OXS区域配置
region.type=${REGION_TYPE:}
environment.type=${ENVIRONMENT_TYPE:}
qanat.db.oxs.enabled=${OXS_ENABLED:false}

# 日志配置 - 用于调试OXS功能
logging.level.com.aliyun.wormhole.qanat.service.base.DatasourceServiceImpl=INFO
```

#### 3.1.3 QanatDatasourceHandler增强

**文件路径**: `qanat-aliyun-inc-com-service/src/main/java/com/aliyun/wormhole/qanat/service/datasource/QanatDatasourceHandler.java`

**改造内容**:
```java
// 在QanatDatasourceHandler类中增加地域感知能力

@Value("${region.type:}")
private String regionType;

@Value("${environment.type:}")
private String environmentType;

/**
 * 根据地域获取数据源
 * @param dbName 数据库名称
 * @return 数据源对象
 */
public DataSource getDatasourceByDbName(String dbName){
    DbInfoExample dbExample = new DbInfoExample();
    dbExample.createCriteria().andIsDeletedEqualTo(0L).andDbNameEqualTo(dbName).andTenantIdEqualTo("1");
    List<DbInfo> dbs = dbInfoMapper.selectByExampleWithBLOBs(dbExample);
    if (CollectionUtils.isEmpty(dbs)) {
        throw new RuntimeException("db not found");
    }
    DbInfo dbInfo = dbs.get(0);
    JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
    
    RdsConnectionParam param = new RdsConnectionParam();
    
    // 增加地域感知的URL选择逻辑
    String jdbcUrl = selectJdbcUrlByRegion(dbMetaJson);
    param.setUrl(jdbcUrl);
    
    param.setPassword(dbMetaJson.getString("password"));
    param.setUserName(dbMetaJson.getString("username"));
    param.setRemoveAbandonedTimeout(60*10);
    param.setMaxWait(60000);
    return getDataSource(param);
}

/**
 * 根据地域选择JDBC URL
 */
private String selectJdbcUrlByRegion(JSONObject dbMetaJson) {
    if ("singapore".equalsIgnoreCase(regionType) && 
        StringUtils.containsIgnoreCase(environmentType, "oxs") && 
        dbMetaJson.containsKey("oxsJdbcUrl")) {
        
        String oxsJdbcUrl = dbMetaJson.getString("oxsJdbcUrl");
        if (StringUtils.isNotBlank(oxsJdbcUrl)) {
            log.info("Selected OXS JDBC URL for region: {} env: {}", regionType, environmentType);
            return oxsJdbcUrl;
        }
    }
    return dbMetaJson.getString("jdbcUrl");
}
```

#### 3.1.4 DAG调度模块影响评估

**影响的JobProcessor列表**:
由于数据库连接逻辑变更，以下DAG节点处理器可能受到影响：

| 处理器类 | 影响程度 | 改造需求 |
|----------|----------|----------|
| **QanatDataXJobProcessor** | 高 | 需要支持OXS数据源配置 |
| **QanatAdb3SqlJobProcessor** | 中 | ADB3连接可能需要OXS支持 |
| **QanatAdb3MultiSqlJobProcessor** | 中 | 批量SQL执行需要地域感知 |
| **QanatMultiDbSinkJobProcessor** | 中 | 多DB写入需要地域适配 |
| **QanatOdpsJobProcessor** | 低 | ODPS连接相对独立 |
| **QanatBlinkJobProcessor** | 低 | Blink任务主要通过服务调用 |

**具体改造建议**:
```java
// 在相关JobProcessor中增加地域感知
@Value("${region.type:}")
private String regionType;

@Value("${environment.type:}")
private String environmentType;

@Value("${qanat.db.oxs.enabled:false}")
private boolean oxsEnabled;

// 在构建数据库连接时使用地域感知逻辑
private String getDbConnectionUrl(JSONObject dbMetaJson) {
    if (oxsEnabled && isOxsRegion() && dbMetaJson.containsKey("oxsJdbcUrl")) {
        return dbMetaJson.getString("oxsJdbcUrl");
    }
    return dbMetaJson.getString("jdbcUrl");
}
```

### 3.2 数据模型扩展

#### 3.2.1 DbInfo元数据结构扩展

现有DbInfo.meta字段的JSON结构需要扩展支持oxsJdbcUrl：

**原有结构**:
```json
{
  "jdbcUrl": "****************************************",
  "username": "username",
  "password": "password",
  "version": "5.7"
}
```

**扩展后结构**:
```json
{
  "jdbcUrl": "****************************************",
  "oxsJdbcUrl": "***********************************",
  "username": "username",
  "password": "password",
  "version": "5.7"
}
```

#### 3.2.2 数据库表结构
无需修改数据库表结构，通过扩展现有dbinfo表的meta字段（TEXT/JSON类型）即可支持。

### 3.3 配置文件详细改造

#### 3.3.1 环境特定配置

**Daily环境配置**:
```properties
# qanat-aliyun-inc-com-start/src/main/resources/application.properties
region.type=singapore
environment.type=daily-oxs
qanat.db.oxs.enabled=true
```

**Production-SG环境配置**:
```properties
region.type=singapore
environment.type=production-oxs
qanat.db.oxs.enabled=true
```

**标准环境配置**:
```properties
region.type=china
environment.type=production
qanat.db.oxs.enabled=false
```

#### 3.3.2 Docker配置文件改造

**文件路径**: `APP-META/docker-config/environment/production-sg/app/application.properties`

```properties
# 新加坡OXS区特定配置
region.type=singapore
environment.type=production-oxs
qanat.db.oxs.enabled=true

# 其他现有配置保持不变
server.port=7001
management.port=7002
# ...
```

## 4. 测试策略

### 4.1 单元测试

#### 4.1.1 测试类设计
```java
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {TestConfig.class})
public class DatasourceServiceImplOxsTest {
    
    @Autowired
    private DatasourceServiceImpl datasourceService;
    
    @Mock
    private DbInfoMapper dbInfoMapper;
    
    @Test
    public void testGetJdbcUrlByRegion_OxsEnabled() {
        // 测试OXS环境下返回oxsJdbcUrl
        // Given
        JSONObject dbMetaJson = new JSONObject();
        dbMetaJson.put("jdbcUrl", "*****************************");
        dbMetaJson.put("oxsJdbcUrl", "************************");
        
        // When - 设置为OXS环境
        ReflectionTestUtils.setField(datasourceService, "regionType", "singapore");
        ReflectionTestUtils.setField(datasourceService, "environmentType", "production-oxs");
        ReflectionTestUtils.setField(datasourceService, "oxsEnabled", true);
        
        String result = datasourceService.getJdbcUrlByRegion(dbMetaJson);
        
        // Then
        assertEquals("************************", result);
    }
    
    @Test
    public void testGetJdbcUrlByRegion_StandardEnvironment() {
        // 测试标准环境下返回标准jdbcUrl
        // ... 测试逻辑
    }
}
```

#### 4.1.2 测试用例列表

**基础功能测试**:
| 测试场景 | 输入条件 | 预期结果 |
|----------|----------|----------|
| OXS环境+有oxsJdbcUrl | region=singapore, env=production-oxs, oxsEnabled=true | 返回oxsJdbcUrl |
| OXS环境+无oxsJdbcUrl | region=singapore, env=production-oxs, 无oxsJdbcUrl字段 | 返回标准jdbcUrl |
| 非OXS环境 | region=china, env=production | 返回标准jdbcUrl |
| 功能关闭 | oxsEnabled=false | 返回标准jdbcUrl |

**DAG调度集成测试**:
| 测试场景 | JobProcessor | 测试内容 | 预期结果 |
|----------|-------------|----------|----------|
| DataX同步任务OXS支持 | QanatDataXJobProcessor | OXS环境下DataX任务数据源连接 | 使用oxsJdbcUrl连接 |
| ADB3 SQL执行OXS支持 | QanatAdb3SqlJobProcessor | OXS环境下ADB3任务执行 | SQL执行成功 |
| 多DB写入OXS支持 | QanatMultiDbSinkJobProcessor | OXS环境下多数据库写入 | 写入成功 |
| DAG任务完整流程 | QanatDagJobProcessor | 包含数据库操作的完整DAG | DAG执行成功 |

### 4.2 集成测试

#### 4.2.1 测试环境搭建
```yaml
# docker-compose-test.yml
version: '3'
services:
  qanat-test:
    image: qanat:test
    environment:
      - REGION_TYPE=singapore
      - ENVIRONMENT_TYPE=testing-oxs
      - OXS_ENABLED=true
    depends_on:
      - mysql-standard
      - mysql-oxs
      
  mysql-standard:
    image: mysql:5.7
    environment:
      MYSQL_DATABASE: alyqanat
      MYSQL_USER: testuser
      MYSQL_PASSWORD: testpass
    ports:
      - "3306:3306"
      
  mysql-oxs:
    image: mysql:5.7
    environment:
      MYSQL_DATABASE: alyqanat
      MYSQL_USER: oxsuser
      MYSQL_PASSWORD: oxspass
    ports:
      - "3307:3306"
```

#### 4.2.2 端到端测试脚本
```bash
#!/bin/bash
# e2e-test.sh

echo "启动测试环境..."
docker-compose -f docker-compose-test.yml up -d

echo "等待服务启动..."
sleep 30

echo "测试OXS数据库连接..."
curl -X GET "http://localhost:7001/api/datasource/meta?dsName=test_ds" \
  -H "Content-Type: application/json" \
  | jq '.data.jdbcUrl' | grep "oxs"

if [ $? -eq 0 ]; then
    echo "✅ OXS数据库连接测试通过"
else
    echo "❌ OXS数据库连接测试失败"
    exit 1
fi

echo "清理测试环境..."
docker-compose -f docker-compose-test.yml down
```

## 5. 部署方案

### 5.1 部署流程

#### 5.1.1 阶段化部署
```mermaid
graph TD
    A[代码提交] --> B[单元测试]
    B --> C[构建镜像]
    C --> D[部署到Testing环境]
    D --> E[集成测试验证]
    E --> F[部署到Staging-SG环境]
    F --> G[用户验收测试]
    G --> H[部署到Production-SG环境]
    H --> I[生产验证]
```

#### 5.1.2 配置推送流程
1. **Diamond配置推送**:
   ```bash
   # 推送OXS区域配置
   diamond-cli set qanat.db.oxs.enabled true --env production-sg
   diamond-cli set region.type singapore --env production-sg
   diamond-cli set environment.type production-oxs --env production-sg
   ```

2. **数据库元数据更新**:
   ```sql
   -- 更新dbinfo表增加oxsJdbcUrl配置
   UPDATE dbinfo 
   SET meta = JSON_SET(meta, '$.oxsJdbcUrl', '***********************************')
   WHERE db_name = 'default_db' AND tenant_id = '1';
   ```

### 5.2 监控和告警

#### 5.2.1 监控指标
| 指标名称 | 指标类型 | 阈值 | 告警级别 |
|----------|----------|------|----------|
| oxs.database.connection.success | Counter | N/A | INFO |
| oxs.database.connection.failure | Counter | >5/min | ERROR |
| oxs.config.switch.count | Counter | N/A | INFO |
| database.connection.time | Histogram | >500ms | WARN |

#### 5.2.2 告警规则
```yaml
# alerting-rules.yml
groups:
- name: qanat-oxs
  rules:
  - alert: OxsDatabaseConnectionFailure
    expr: rate(oxs_database_connection_failure_total[5m]) > 0.1
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "OXS数据库连接失败率过高"
      description: "OXS数据库连接失败率超过10%，当前值：{{ $value }}"
```

### 5.3 回滚策略

#### 5.3.1 配置回滚
```bash
# 紧急配置回滚脚本
#!/bin/bash
echo "执行紧急配置回滚..."
diamond-cli set qanat.db.oxs.enabled false --env production-sg
echo "OXS功能已关闭，系统将使用标准数据库连接"
```

#### 5.3.2 代码回滚
- 保持向后兼容性设计，确保可以快速回滚到上一版本
- 使用feature flag控制新功能的开启/关闭

## 6. 运维指南

### 6.1 配置管理

#### 6.1.1 配置检查清单
- [ ] region.type配置正确
- [ ] environment.type包含oxs标识
- [ ] qanat.db.oxs.enabled设置为true
- [ ] dbinfo表中包含oxsJdbcUrl配置
- [ ] OXS数据库连接可达性验证

#### 6.1.2 故障排查手册

**问题1**: OXS数据库连接失败
```bash
# 检查配置
curl "http://localhost:7002/configprops" | jq '.region'

# 检查数据库连接
mysql -h oxs-host -u oxsuser -p

# 查看应用日志
tail -f /app/logs/qanat.log | grep -i oxs
```

**问题2**: 配置未生效
```bash
# 检查Diamond配置
diamond-cli get qanat.db.oxs.enabled --env production-sg

# 重启应用刷新配置
kubectl rollout restart deployment/qanat-app
```

### 6.2 性能监控

#### 6.2.1 关键指标监控
```bash
# 监控数据库连接池状态
curl "http://localhost:7002/metrics" | grep hikari

# 监控OXS功能使用情况
curl "http://localhost:7002/metrics" | grep oxs
```

#### 6.2.2 日常巡检脚本
```bash
#!/bin/bash
# daily-check.sh
echo "=== Qanat OXS功能日常巡检 ==="
echo "1. 检查OXS配置状态..."
curl -s "http://localhost:7002/configprops" | jq '.qanat.db.oxs'

echo "2. 检查数据库连接状态..."
curl -s "http://localhost:7002/health" | jq '.db'

echo "3. 检查近1小时内的错误日志..."
grep -i "error\|exception" /app/logs/qanat.log | tail -10

echo "巡检完成"
```

---

**文档版本**: v1.0  
**创建日期**: 2024-12-16  
**创建人**: Qanat技术团队  
**审核人**: 架构组、DBA组  
**最后更新**: 2024-12-16 