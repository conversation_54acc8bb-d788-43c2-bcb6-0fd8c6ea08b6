# 新加坡OXS区数据库地址获取逻辑需求分析

## 1. 需求背景

### 1.1 业务背景
- **应用场景**: 新加坡OXS区部署时需要支持读取oxs特定的数据库连接地址
- **业务需求**: 新加坡站点在读取DB信息时，能够从配置中读取`oxsJdbcUrl`字段，实现地域化的数据库访问
- **技术背景**: 当前系统通过`QanatDatasourceHandler`和`DatasourceServiceImpl`管理数据库连接，需要扩展支持OXS区域数据库配置

### 1.2 需求范围
- **涉及模块**: qanat-aliyun-inc-com-service (数据源服务层)
- **影响范围**: 数据库连接管理、配置管理、地域化部署
- **依赖系统**: 配置中心(Diamond)、数据库元数据管理

## 2. 需求分析

### 2.1 功能需求分析

#### 2.1.1 核心需求
| 需求ID | 需求描述 | 优先级 | 复杂度 |
|--------|----------|--------|---------|
| REQ-001 | 支持读取oxsJdbcUrl配置字段 | P0 | 低 |
| REQ-002 | 新加坡地域部署时优先使用OXS数据库地址 | P0 | 中 |
| REQ-003 | 保持向后兼容性，非OXS环境正常使用原有配置 | P0 | 低 |
| REQ-004 | 支持配置热更新和动态切换 | P1 | 中 |

#### 2.1.2 非功能需求
| 需求类型 | 需求描述 | 指标要求 |
|----------|----------|----------|
| 性能需求 | 数据库连接获取延迟 | < 100ms |
| 可用性需求 | 服务可用性 | 99.9% |
| 兼容性需求 | 向后兼容原有配置 | 100% |
| 可扩展性需求 | 支持其他地域扩展 | 插件化设计 |

### 2.2 业务流程分析

#### 2.2.1 当前业务流程
```mermaid
graph LR
    A[客户端请求] --> B[DatasourceServiceImpl.getDbMetaByDsName]
    B --> C[查询datasource表]
    C --> D[调用getDbInfoByName]
    D --> E[查询dbinfo表]
    E --> F[解析meta.jdbcUrl]
    F --> G[返回数据库连接信息]
```

#### 2.2.2 改造后业务流程
```mermaid
graph LR
    A[客户端请求] --> B[DatasourceServiceImpl.getDbMetaByDsName]
    B --> C[查询datasource表]
    C --> D[调用getDbInfoByName]
    D --> E[查询dbinfo表]
    E --> F{检查地域和OXS配置}
    F -->|新加坡+OXS| G[优先使用meta.oxsJdbcUrl]
    F -->|其他地域| H[使用meta.jdbcUrl]
    G --> I[返回OXS数据库连接信息]
    H --> I
```

## 3. 技术方案设计

### 3.1 架构设计

#### 3.1.1 改造点分析
根据现有代码分析，主要改造点在：
1. **DatasourceServiceImpl.getDbMetaByDsName()**: 数据库连接信息获取逻辑
2. **DbInfo元数据结构**: 扩展支持oxsJdbcUrl字段
3. **配置管理**: 增加地域判断和OXS配置支持

#### 3.1.2 技术方案
```java
// 改造方案：在DatasourceServiceImpl中增加OXS支持
public JSONObject getDbMetaByDsName(String tenantId, String dsName) {
    // ... 现有逻辑 ...
    
    DbInfo dbInfo = getDbInfoByName(tenantId, ds.getDbName());
    JSONObject dbMetaJson = JSON.parseObject(dbInfo.getMeta());
    
    // 新增：OXS地域数据库地址获取逻辑
    String jdbcUrl = getJdbcUrlByRegion(dbMetaJson);
    srcDsMetaJson.put("jdbcUrl", jdbcUrl);
    
    // ... 其他逻辑 ...
}

private String getJdbcUrlByRegion(JSONObject dbMetaJson) {
    // 检查当前环境是否为新加坡+OXS
    if (isOxsRegion() && dbMetaJson.containsKey("oxsJdbcUrl")) {
        String oxsJdbcUrl = dbMetaJson.getString("oxsJdbcUrl");
        if (StringUtils.isNotBlank(oxsJdbcUrl)) {
            return oxsJdbcUrl;
        }
    }
    // 默认使用标准jdbcUrl
    return dbMetaJson.getString("jdbcUrl");
}

private boolean isOxsRegion() {
    // 通过配置或环境变量判断当前是否为OXS区域
    String regionType = System.getProperty("region.type", "");
    String environmentType = System.getProperty("environment.type", "");
    return "singapore".equals(regionType) && environmentType.contains("oxs");
}
```

### 3.2 数据模型设计

#### 3.2.1 DbInfo元数据扩展
```json
{
  "jdbcUrl": "****************************************",
  "oxsJdbcUrl": "***********************************",
  "username": "username",
  "password": "password",
  "version": "5.7"
}
```

#### 3.2.2 配置项设计
| 配置项 | 配置值示例 | 说明 |
|--------|------------|------|
| `region.type` | `singapore` | 部署地域类型 |
| `environment.type` | `production-oxs` | 环境类型（包含oxs标识） |
| `qanat.db.oxs.enabled` | `true` | OXS数据库功能开关 |

### 3.3 实现细节

#### 3.3.1 代码改造清单
| 文件路径 | 改造内容 | 改造类型 |
|----------|----------|----------|
| `DatasourceServiceImpl.java` | 增加OXS地址获取逻辑 | 功能增强 |
| `application.properties` | 增加OXS相关配置 | 配置增加 |
| `QanatDatasourceHandler.java` | 增加地域感知能力 | 功能增强 |

#### 3.3.2 配置文件改造
```properties
# 新加坡OXS区配置
region.type=singapore
environment.type=production-oxs
qanat.db.oxs.enabled=true

# OXS数据库连接配置
qanat.db.oxs.default.url=*****************************************
qanat.db.oxs.default.username=oxs_user
qanat.db.oxs.default.password=oxs_password
```

## 4. 实施计划

### 4.1 开发阶段

#### 4.1.1 阶段划分
| 阶段 | 工作内容 | 工期 | 交付物 |
|------|----------|------|--------|
| 阶段1 | 需求分析和技术方案设计 | 1天 | 技术方案文档 |
| 阶段2 | 核心功能开发 | 2天 | 功能代码 |
| 阶段3 | 单元测试和集成测试 | 1天 | 测试报告 |
| 阶段4 | 配置文件和部署脚本调整 | 0.5天 | 部署配置 |

#### 4.1.2 开发任务分解
| 任务ID | 任务描述 | 负责人 | 工期 | 依赖 |
|--------|----------|--------|------|------|
| TASK-001 | 修改DatasourceServiceImpl.getDbMetaByDsName方法 | 开发 | 1天 | 无 |
| TASK-002 | 增加地域判断工具类 | 开发 | 0.5天 | TASK-001 |
| TASK-003 | 扩展配置管理支持OXS配置 | 开发 | 0.5天 | TASK-002 |
| TASK-004 | 编写单元测试用例 | 开发 | 0.5天 | TASK-003 |
| TASK-005 | 集成测试和验证 | 测试 | 0.5天 | TASK-004 |

### 4.2 测试计划

#### 4.2.1 测试用例设计
| 用例ID | 测试场景 | 预期结果 |
|--------|----------|----------|
| TC-001 | 新加坡OXS环境，配置了oxsJdbcUrl | 返回oxsJdbcUrl |
| TC-002 | 新加坡OXS环境，未配置oxsJdbcUrl | 返回标准jdbcUrl |
| TC-003 | 非OXS环境（标准环境） | 返回标准jdbcUrl |
| TC-004 | 配置热更新测试 | 动态切换生效 |

#### 4.2.2 测试环境
- **单元测试**: 本地开发环境
- **集成测试**: 新加坡staging-sg环境
- **验收测试**: 新加坡production-sg环境

### 4.3 部署计划

#### 4.3.1 部署策略
1. **灰度部署**: 先在staging-sg环境验证
2. **配置推送**: 通过Diamond配置中心推送OXS配置
3. **切换验证**: 验证数据库连接切换效果
4. **全量部署**: 确认无误后全量部署

#### 4.3.2 回滚预案
- **配置回滚**: 通过Diamond快速回滚配置
- **代码回滚**: 保持向后兼容，支持快速代码回滚
- **监控告警**: 设置数据库连接失败告警

## 5. 风险评估

### 5.1 技术风险
| 风险项 | 风险等级 | 影响 | 缓解措施 |
|--------|----------|------|----------|
| 配置错误导致连接失败 | 中 | 服务不可用 | 充分测试+向后兼容 |
| 性能影响 | 低 | 轻微延迟 | 优化判断逻辑 |
| 兼容性问题 | 低 | 影响现有功能 | 保持向后兼容 |

### 5.2 业务风险
| 风险项 | 风险等级 | 影响 | 缓解措施 |
|--------|----------|------|----------|
| 数据库切换影响业务 | 中 | 数据访问异常 | 灰度部署+快速回滚 |
| 配置管理复杂度增加 | 低 | 运维复杂度 | 文档化+自动化 |

## 6. 验收标准

### 6.1 功能验收
- ✅ 新加坡OXS环境能够正确读取oxsJdbcUrl配置
- ✅ 非OXS环境保持原有功能不变
- ✅ 支持配置动态更新
- ✅ 数据库连接池正常工作

### 6.2 性能验收
- ✅ 数据库连接获取延迟 < 100ms
- ✅ 服务响应时间无明显增加
- ✅ 内存使用无异常增长

### 6.3 稳定性验收
- ✅ 持续运行24小时无异常
- ✅ 配置切换过程无服务中断
- ✅ 异常情况下能够自动降级

---

**文档版本**: v1.0  
**创建日期**: 2024-12-16  
**创建人**: Qanat团队  
**审核人**: 架构组  
**最后更新**: 2024-12-16 