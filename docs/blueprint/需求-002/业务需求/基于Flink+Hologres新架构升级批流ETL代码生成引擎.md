# 需求背景
现行的基于Blink SQL为核心的批流ETL技术方案已经体现出技术架构劣势，运维复杂、性能低、技术栈复杂，无法满足日趋复杂、快速变化的业务变化需求。

## 现行的技术架构概况(详细情况参考知识库中相关章节内容)
- 技术栈
  - 批流ETL: Blink SQL（Stream + Batch）
  - 实时消息层: DRC + Kafka队列
  - 数仓: ADB3

- 数据分层
  - DRC增量消息分发，DRC实现将mysql binlog分装成通用增量消息，本身是单队列，通过分发到Kafka Topic，实现下游应用多队列并行消费
  - ODS，完成源库(myslq/tddl)表到ADB3目标ODS表的同步，三个一致：表名一致、字段一致、数据一致
    - 全量同步，基于Datax实现mysql源表到ADB3目标ODS表的全量数据同步
    - 增量同步，基于Kafka消费增量DRC消息，通过Blink SQL实现数据同步，订制TableFactory实现消息转换DML SQL为核心的批流ETL技术方案已经体现出技术架构劣势，运维复杂、性能低、技术栈复杂，无法满足日趋复杂、快速变化的业务变化需求。数据写入ADB3

  - DWD，完成多个（可以是来自不同源库）的ODS表到ADB3目标DWD表的同步，通过视图模型viewModel yaml协议定义映射关系
    - 全量同步，基于视图模型yaml定义翻译成adb3目标库中多个ODS表的insert select多表join的ETL SQL，在ADB库中完成数据全量构建
    - 增量计算，基于视图模型中定义的每个ods表的kafka增量消息，通过Blink SQL实现数据同步，订制TableFactory实现ADB3目标表的部分字段upsert语义的写入
      - 主表，upsert主表相关字段
      - 主表，识别新增事件，完成其他副表字段的lookup查询
      - 副表，upsert副表相关字段，副表可以是ods表，也可以是一段sql视图

- 当前架构问题
  - 运维复杂，需要维护多个技术栈，包括Datax、Blink SQL、DRC、Kafka、ADB3等
  - 性能低，Blink SQL的性能低，无法满足实时性要求
  - 技术栈复杂，需要维护多个技术栈，包括Datax、Blink SQL、DRC、Kafka、ADB3等
  - 无法满足日趋复杂、快速变化的业务变化需求

## 新架构升级需求
- 需求内容
  - 批流ETL计算框架从Blink SQL+ADB3升级到Flink SQL + Hologres，Flink SQL的性能和实时性都比Blink SQL要高，同时Flink SQL的生态也更加成熟，支持的算子也更加丰富，可以满足日趋复杂、快速变化的业务变化需求；Hologres与Flink、ODPS等大数据解决方案集成度更好，自身提供的binlog机制使之更适合做实时数仓中间层建设，同时弹性计算组机制方便做负载隔离，更适合综合性在线数仓的选型
  - Flink+Hologres新架构，管道实例的scope从Hologres里的ods或dwd为起始，到Hologres里的dwd目标表为终点，如果有其他存储同步的必要，如ADB3、ElasticSearch、ODPS等，不在管道实例自身范畴之内，可以参考Flink CDC或Hologres 外表同步等功能从Hologres订阅，方案由下游数据消费方评估
  - 需求内容细节
    - 数仓ods和dwd层数据全部构建在hologres
      - ODS层数据构建不再由datatube自身的ODS类型管道实例实现，基于dataworks数据集成工具完成源库源表到hologres的全增量实时同步
      - 因此实时消息层也不需要从源库(mysql)的DRC消息订阅，Hologres的性能和实时性都比DRC要高，同时Hologres的生态也更加成熟，支持的算子也更加丰富，可以满足日趋复杂、快速变化的业务变化需求。
      - DWD层数据构建，依旧基于视图模型定义，实现hologres数仓内ods到dwd表的insert select多表join的ETL SQL完成全量构建；增量数据直接依赖hologres ods表的binlog
    - 视图模型yaml定义，数据源信息应该全基于hologres表构建，但也要适配存量配置
      - yaml定义中的object->ref指向的数据源code，来自datasource表中的ds_name，新架构中应该对应的hologres库中的表，因为业务源库表到hologres的ods同步使用dataworks构建，这部分ods表元数据缺少了定义入口，需要根据hologres表的元数据异步或定时的方式补充回datasource、ds_feild_info表中
      - hologres里定义的视图，常用语lookup处理，也可以按表的方式维护到datasource里，区别与表，视图定义中的字段需要特殊处理后也需要补充到ds_field_info中
      - 存量yaml中的ref数据源code，一般使用的是源库表对应的datasource，如mysql库表对应的ds_name，新方案里需要适配，根据datasource中定义的源库表ds_name信息，自动识别出对应的hologres库中的表ds_name
      - component类型关联对象，还是依旧延用现有的sql组件模式，在flink SQL中依旧使用queryDim的UDTF进行查询，要使用读专用计算组
    - 依旧延续ViewModel定义， 核心是基于新架构进行调度DAG、Flink SQL作业的代码生成

- 技术栈
  - 批流ETL：Flink SQL(Stream)，关键特性：
    - CDC：实现自动的全增量写入，非常适用于Hologres目标宽表向其他异构存储如ADB、ElasticSearch等数据复制
  - 实时消息层：Hologres binlog
  - 数仓(ODS/DWD)：Hologres，关键特性
    - 读写分离：使用Hologres弹性计算组实现读写流量互相隔离，全增量写入流量需要与ETL中的look读流量使用不同的计算组
    - 覆盖式写入：宽表数据的构建，可以使用Hologres的CALL hg_insert_overwrite实现覆盖写入，做到全量构建写入数据不影响线上查询

- 要求
  - Flink+Hologres新架构使用新增代码链路支持，现有的基于Blink+ADB+drc+kafka的机制保留不变
  - Flink SQL生成规范，要严格遵循阿里云Flink官方文档
  - 尽量使用Flink、Hologres开箱即用能力（参考阿里云Flink、Hologres官方文档），不使用或最小化使用使用订制connector、UDF、UDTF
  - 架构设计要遵循现有能力基础之上，不能缺失现有已支持的能力，如果有可以值得舍弃的内容，先提交出来供产品review讨论，设计改造遵循最小必要的简约原则

