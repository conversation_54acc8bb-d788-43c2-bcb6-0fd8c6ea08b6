# Datatube技术方案说明书 - 需求-002

**文档标题**: 基于Flink+Hologres新架构升级批流ETL代码生成引擎技术方案说明书  
**文档编号**: datatube-blueprint-需求-002-技术方案-2025-01-25  
**版本号**: v1.0  
**创建日期**: 2025-01-25  
**创建人**: Datatube技术架构师  
**审核人**: 技术负责人  
**批准人**: 项目负责人  

## 1. 技术方案概述

### 1.1 方案背景
基于需求说明书分析，现有Blink SQL+ADB3架构存在性能瓶颈和运维复杂性问题。本技术方案旨在通过新增Flink+Hologres架构支持，在保持现有架构稳定运行的前提下，提供更高性能的批流ETL解决方案。

### 1.2 设计原则
- **最小必要原则**: 仅新增必要组件，最大化复用现有代码
- **向后兼容原则**: 保持现有API和配置格式100%兼容
- **渐进式升级**: 支持新旧架构并存，平滑迁移
- **代码复用优先**: 基于现有成熟组件扩展，降低开发风险

### 1.3 技术选型
- **流计算引擎**: 阿里云Flink（基于现有FlinkService扩展）
- **数据存储**: Hologres（基于现有连接器扩展）
- **代码生成**: 扩展ViewModelSqlBuilder支持Flink SQL
- **调度框架**: 复用现有DAG调度框架
- **模板系统**: 新增FlinkSyncTemplate，复用现有模板架构

## 2. 架构设计

### 2.1 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    Datatube平台                              │
├─────────────────────────────────────────────────────────────┤
│  ViewModelHandler (入口，保持不变)                           │
│  ├── createBatchStreamTasks() (扩展支持Flink)                │
│  ├── createTableAndFullSync() (扩展支持Hologres)             │
│  └── getSysViewModel() (保持兼容)                            │
├─────────────────────────────────────────────────────────────┤
│  代码生成层                                                  │
│  ├── ViewModelSqlBuilder (现有，已支持Hologres)              │
│  ├── FlinkSqlBuilder (新增，继承ViewModelSqlBuilder)         │
│  └── FlinkSyncTemplate (新增模板)                           │
├─────────────────────────────────────────────────────────────┤
│  DAG调度层                                                   │
│  ├── QanatDagJobProcessor (现有，保持不变)                   │
│  ├── QanatFlinkJobProcessor (现有，扩展功能)                 │
│  └── QanatHoloExternalTableJobProcessor (现有，完善功能)     │
├─────────────────────────────────────────────────────────────┤
│  服务层                                                      │
│  ├── FlinkService (现有，完善createJob实现)                  │
│  ├── DatasourceService (现有，扩展Hologres支持)              │
│  └── ViewModelOptimizer (现有，保持不变)                     │
├─────────────────────────────────────────────────────────────┤
│  连接器层                                                    │
│  ├── FlinkClient (现有，完善实现)                            │
│  ├── QanatDatasourceHandler (现有，扩展Hologres)             │
│  └── HoloUtils (现有工具类)                                 │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 数据流向设计
```
YAML配置 → ViewModelHandler → FlinkSqlBuilder → FlinkSyncTemplate → 
Flink作业 → Hologres binlog → Hologres表 → 视图模型
```

### 2.3 核心组件关系
- **ViewModelHandler**: 保持现有接口，内部扩展Flink支持
- **FlinkSqlBuilder**: 继承ViewModelSqlBuilder，复用70%核心逻辑
- **QanatFlinkJobProcessor**: 扩展现有实现，支持更多节点类型
- **FlinkService**: 完善现有createJob方法实现

## 3. 详细技术设计

### 3.1 代码生成引擎设计

#### 3.1.1 FlinkSqlBuilder实现
**文件路径**: `qanat-aliyun-inc-com-service/src/main/java/com/aliyun/wormhole/qanat/service/viewmodel/FlinkSqlBuilder.java`

**设计要点**:
- 继承现有ViewModelSqlBuilder，复用核心JOIN逻辑
- 重写getDbFieldEscape()方法，适配Hologres双引号转义
- 扩展getSelectSql()方法，支持Hologres特有语法
- 新增generateFlinkDDL()方法，生成Flink表定义

**核心方法**:
```java
public class FlinkSqlBuilder extends ViewModelSqlBuilder {
    
    @Override
    protected String getDbFieldEscape(String dbType) {
        return "hologres".equalsIgnoreCase(dbType) ? "\"" : super.getDbFieldEscape(dbType);
    }
    
    public String generateFlinkDDL(ViewModel dataModel, String connectorType) {
        // 生成Flink CREATE TABLE语句
    }
    
    public String generateFlinkInsertSql(String tenantId, ViewModel dataModel) {
        // 生成Flink INSERT INTO语句，复用父类getSelectSql逻辑
        return "INSERT INTO " + dataModel.getCode() + " " + 
               super.getSelectSql(tenantId, dataModel, "hologres");
    }
}
```

#### 3.1.2 FlinkSyncTemplate模板设计
**文件路径**: `qanat-aliyun-inc-com-service/src/main/java/com/aliyun/wormhole/qanat/service/template/FlinkSyncTemplate.java`

**模板内容**:
- FLINK_SYNC_SQL: Flink SQL作业模板
- FLINK_DAG_SCRIPT: Flink DAG调度脚本模板
- HOLO_BINLOG_SOURCE: Hologres binlog源表模板
- HOLO_SINK_TABLE: Hologres目标表模板

### 3.2 DAG调度扩展设计

#### 3.2.1 QanatFlinkJobProcessor扩展
**文件路径**: `qanat-aliyun-inc-com-service/src/main/java/com/aliyun/wormhole/qanat/job/QanatFlinkJobProcessor.java`

**扩展内容**:
- 支持FlinkNode、FlinkBatchNode、FlinkStreamNode多种节点类型
- 扩展doProcess方法，支持批流作业统一处理
- 增强参数传递和状态管理

**修改方案**:
```java
// 现有类已支持FlinkStreamNode，需要扩展支持更多节点类型
public class QanatFlinkJobProcessor extends AbstractQanatNodeJobProcessor<Node> {
    
    @Override
    void doProcess(Map<String, Object> instParamsMap, Node node) {
        if (node instanceof FlinkStreamNode) {
            processStreamNode(instParamsMap, (FlinkStreamNode) node);
        } else if (node instanceof FlinkBatchNode) {
            processBatchNode(instParamsMap, (FlinkBatchNode) node);
        } else if (node instanceof FlinkNode) {
            processGenericNode(instParamsMap, (FlinkNode) node);
        }
    }
}
```

#### 3.2.2 新增节点类型支持
**无需新增节点类型**，现有FlinkNode、FlinkBatchNode、FlinkStreamNode已满足需求。

### 3.3 服务层扩展设计

#### 3.3.1 FlinkService完善
**文件路径**: `qanat-aliyun-inc-com-service/src/main/java/com/aliyun/wormhole/qanat/service/flink/FlinkServiceImpl.java`

**完善内容**:
- 完善createJob方法实现（现有方法调用FlinkClient但FlinkClientV2实现为空）
- 扩展支持Hologres连接器配置
- 增强作业参数传递机制

**实现方案**:
```java
@Override
public void createJob(String tenantId, String appName, String jobName, String sql, Boolean isBatch) {
    log.debug("begin to createJob({},{},{},{},{})", tenantId, appName, jobName, sql, isBatch);
    try {
        FlinkClient flinkClient = new FlinkClient(getFlinkConfByAppName(tenantId, appName));
        // 调用现有FlinkClient.createJob方法（已实现）
        flinkClient.createJob(jobName, sql, isBatch);
    } catch (Exception e) {
        log.error("createJob({}) failed:{}", jobName, e.getMessage(), e);
        throw new QanatBizException("create flink job failed: " + e.getMessage());
    }
}
```

#### 3.3.2 DatasourceService扩展
**文件路径**: `qanat-aliyun-inc-com-service/src/main/java/com/aliyun/wormhole/qanat/service/base/DatasourceServiceImpl.java`

**扩展内容**:
- 现有代码已支持Hologres（有getHologresCreateTableDdl方法）
- 扩展testConnection方法支持Hologres连接测试
- 完善元数据同步机制

### 3.4 ViewModelHandler扩展设计

#### 3.4.1 核心方法扩展
**文件路径**: `qanat-aliyun-inc-com-service/src/main/java/com/aliyun/wormhole/qanat/service/viewmodel/ViewModelHandler.java`

**扩展方案**:
- createBatchStreamTasks方法：增加架构类型参数，支持选择Blink或Flink
- createTableAndFullSync方法：支持Hologres表创建和数据同步
- 保持现有方法签名不变，通过内部逻辑判断架构类型

**实现示例**:
```java
public List<String> createBatchStreamTasks(String tenantId, Long viewModelId, String archType) {
    // archType: "blink" | "flink"
    if ("flink".equalsIgnoreCase(archType)) {
        return createFlinkBatchStreamTasks(tenantId, viewModelId);
    } else {
        return createBlinkBatchStreamTasks(tenantId, viewModelId); // 现有逻辑
    }
}

private List<String> createFlinkBatchStreamTasks(String tenantId, Long viewModelId) {
    // 使用FlinkSqlBuilder和FlinkSyncTemplate生成Flink作业
    FlinkSqlBuilder sqlBuilder = new FlinkSqlBuilder();
    // ... 实现逻辑
}
```

## 4. 数据库设计

### 4.1 现有表结构扩展
**无需新增表**，现有表结构已支持：
- datasource表：已支持多种数据源类型
- ds_field_info表：已支持字段元数据管理
- view_model_info表：已支持视图模型定义
- task_info表：已支持任务管理

### 4.2 配置扩展
在现有配置基础上扩展：
- 数据源类型枚举：增加"hologres"类型（已支持）
- 作业类型枚举：增加"flink"类型
- 模板类型：增加Flink相关模板

## 5. 接口设计

### 5.1 现有接口保持不变
- ViewModelHandler的所有public方法保持向后兼容
- DatasourceService接口保持不变
- FlinkService接口保持不变（仅完善实现）

### 5.2 新增配置参数
在现有方法中增加可选参数：
- 架构类型选择：blink | flink
- Hologres连接配置：计算组、读写分离等
- Flink作业配置：并行度、资源配置等

## 6. 配置管理

### 6.1 YAML配置兼容性
**100%兼容现有YAML格式**，通过以下方式实现：
- 保持ViewModel实体类不变
- 扩展YamlUtil解析逻辑，支持架构类型标识
- 数据源引用自动映射到Hologres表

### 6.2 新增配置项
```yaml
# 在现有YAML基础上可选增加
settings:
  architecture: "flink"  # 可选：blink | flink，默认blink
  hologres:
    computeGroup: "default"  # Hologres计算组
    readWriteSeparation: true  # 读写分离
```

## 7. 部署方案

### 7.1 分阶段部署策略

#### 第一阶段：基础组件部署（周期：2周）
**部署内容**:
- FlinkSqlBuilder类
- FlinkSyncTemplate模板
- FlinkService完善
- 单元测试

**部署步骤**:
1. 代码开发和本地测试
2. 提交代码审查
3. 部署到开发环境
4. 执行单元测试和集成测试
5. 部署到测试环境

#### 第二阶段：DAG调度扩展（周期：2周）
**部署内容**:
- QanatFlinkJobProcessor扩展
- ViewModelHandler扩展
- 端到端测试

**部署步骤**:
1. 完成开发和测试
2. 在测试环境验证DAG调度功能
3. 执行兼容性测试
4. 部署到预发环境

#### 第三阶段：生产环境部署（周期：1周）
**部署内容**:
- 完整功能部署
- 监控和告警配置
- 用户培训

**部署步骤**:
1. 生产环境部署
2. 灰度验证
3. 全量发布
4. 监控观察

### 7.2 回滚方案
- **代码回滚**: 通过Git版本控制快速回滚
- **配置回滚**: 保持现有配置不变，新功能通过开关控制
- **数据回滚**: 无数据结构变更，无需数据回滚

## 8. 测试方案

### 8.1 单元测试
**测试范围**:
- FlinkSqlBuilder核心方法测试
- FlinkService方法测试
- ViewModelHandler扩展方法测试
- 模板生成逻辑测试

**测试用例设计**:
```java
@Test
public void testFlinkSqlBuilderSelectSql() {
    FlinkSqlBuilder builder = new FlinkSqlBuilder();
    ViewModel model = createTestViewModel();
    String sql = builder.getSelectSql("test_tenant", model, "hologres");
    // 验证SQL语法正确性和字段转义
    assertThat(sql).contains("\"field_name\"");
}
```

### 8.2 集成测试
**测试场景**:
- 端到端视图模型创建流程
- DAG调度执行流程
- Flink作业创建和启动
- 数据一致性验证

### 8.3 兼容性测试
**测试内容**:
- 现有YAML配置100%兼容性
- 现有API接口向后兼容性
- 新旧架构并存测试
- 数据迁移一致性测试

### 8.4 性能测试
**测试指标**:
- Flink作业启动时间
- 数据处理延迟对比
- 资源利用率对比
- 并发处理能力测试

## 9. 监控和运维

### 9.1 监控指标
- Flink作业运行状态
- Hologres连接池状态
- 数据处理延迟
- 错误率和成功率

### 9.2 告警配置
- Flink作业失败告警
- Hologres连接异常告警
- 数据延迟超阈值告警
- 资源使用率告警

### 9.3 日志管理
- 统一日志格式
- 关键操作审计日志
- 错误日志聚合分析
- 性能日志监控

## 10. 风险控制

### 10.1 技术风险控制
- **代码复用率目标**: 70%以上，降低开发风险
- **渐进式发布**: 分阶段部署，及时发现问题
- **完整测试覆盖**: 单元测试+集成测试+性能测试
- **监控告警**: 实时监控，快速响应

### 10.2 业务风险控制
- **向后兼容**: 保证现有功能不受影响
- **并存架构**: 新旧架构可同时运行
- **快速回滚**: 支持快速回滚到稳定版本
- **灰度发布**: 小范围验证后全量发布

## 11. 具体实施细节

### 11.1 核心类修改清单

#### 11.1.1 新增类列表
| 类名 | 文件路径 | 功能描述 | 代码行数估算 |
|------|----------|----------|-------------|
| FlinkSqlBuilder | qanat-aliyun-inc-com-service/.../FlinkSqlBuilder.java | Flink SQL生成器 | 200行 |
| FlinkSyncTemplate | qanat-aliyun-inc-com-service/.../FlinkSyncTemplate.java | Flink模板定义 | 300行 |

#### 11.1.2 修改类列表
| 类名 | 文件路径 | 修改内容 | 影响范围 |
|------|----------|----------|----------|
| FlinkServiceImpl | qanat-aliyun-inc-com-service/.../FlinkServiceImpl.java | 完善createJob方法 | 低风险 |
| QanatFlinkJobProcessor | qanat-aliyun-inc-com-service/.../QanatFlinkJobProcessor.java | 扩展节点类型支持 | 中风险 |
| ViewModelHandler | qanat-aliyun-inc-com-service/.../ViewModelHandler.java | 增加架构选择逻辑 | 中风险 |
| DatasourceServiceImpl | qanat-aliyun-inc-com-service/.../DatasourceServiceImpl.java | 扩展Hologres支持 | 低风险 |

### 11.2 详细开发任务分解

#### 11.2.1 第一阶段任务（2周）
**任务1: FlinkSqlBuilder开发**
- 负责人：后端开发工程师A
- 工期：5天
- 交付物：FlinkSqlBuilder.java + 单元测试
- 验收标准：通过所有单元测试，代码覆盖率>80%

**任务2: FlinkSyncTemplate开发**
- 负责人：后端开发工程师B
- 工期：3天
- 交付物：FlinkSyncTemplate.java
- 验收标准：模板语法正确，支持参数化

**任务3: FlinkService完善**
- 负责人：后端开发工程师C
- 工期：4天
- 交付物：完善的FlinkServiceImpl.createJob方法
- 验收标准：能够成功创建Flink作业

**任务4: 集成测试**
- 负责人：测试工程师
- 工期：2天
- 交付物：集成测试用例和报告
- 验收标准：所有测试用例通过

#### 11.2.2 第二阶段任务（2周）
**任务5: QanatFlinkJobProcessor扩展**
- 负责人：后端开发工程师A
- 工期：4天
- 交付物：扩展的处理器类
- 验收标准：支持多种Flink节点类型

**任务6: ViewModelHandler扩展**
- 负责人：后端开发工程师B
- 工期：5天
- 交付物：扩展的ViewModelHandler
- 验收标准：支持架构选择，保持向后兼容

**任务7: 端到端测试**
- 负责人：测试工程师
- 工期：3天
- 交付物：端到端测试报告
- 验收标准：完整流程测试通过

#### 11.2.3 第三阶段任务（1周）
**任务8: 生产环境部署**
- 负责人：运维工程师
- 工期：2天
- 交付物：生产环境部署
- 验收标准：服务正常运行

**任务9: 监控配置**
- 负责人：运维工程师
- 工期：2天
- 交付物：监控和告警配置
- 验收标准：监控指标正常采集

**任务10: 用户培训**
- 负责人：产品经理
- 工期：1天
- 交付物：培训材料和培训记录
- 验收标准：用户能够正常使用新功能

### 11.3 灰度发布方案

#### 11.3.1 灰度策略
**阶段1: 内部验证（1天）**
- 范围：开发团队内部测试
- 验证内容：基础功能正常性
- 成功标准：所有核心功能正常

**阶段2: 小范围试点（3天）**
- 范围：选择1-2个低风险业务场景
- 验证内容：真实业务场景验证
- 成功标准：数据一致性100%，性能指标达标

**阶段3: 扩大范围（1周）**
- 范围：扩展到10%的业务场景
- 验证内容：并发处理能力和稳定性
- 成功标准：系统稳定运行，无重大问题

**阶段4: 全量发布（1周）**
- 范围：所有业务场景
- 验证内容：全量业务验证
- 成功标准：系统整体稳定，用户反馈良好

#### 11.3.2 回滚触发条件
- 系统可用性低于99%
- 数据一致性问题
- 性能指标下降超过20%
- 出现P0级别故障

#### 11.3.3 回滚执行步骤
1. 立即停止新功能使用
2. 切换到原有架构
3. 验证系统恢复正常
4. 分析问题原因
5. 制定修复方案

### 11.4 质量保证措施

#### 11.4.1 代码质量
- **代码审查**: 所有代码必须经过至少2人审查
- **静态分析**: 使用SonarQube进行代码质量检查
- **单元测试**: 代码覆盖率要求>80%
- **集成测试**: 覆盖所有主要业务场景

#### 11.4.2 性能质量
- **基准测试**: 建立性能基准线
- **压力测试**: 验证系统承载能力
- **延迟测试**: 验证实时性指标
- **资源监控**: 监控CPU、内存、网络使用情况

#### 11.4.3 安全质量
- **权限验证**: 验证现有权限体系兼容性
- **数据加密**: 确保敏感数据传输加密
- **审计日志**: 记录所有关键操作
- **漏洞扫描**: 定期进行安全漏洞扫描

## 12. 总结

本技术方案基于现有成熟架构，通过最小化修改实现Flink+Hologres新架构支持。主要优势：

1. **高代码复用率**: 预期70%以上代码复用，降低开发风险
2. **完全向后兼容**: 现有功能和配置100%兼容
3. **渐进式升级**: 支持新旧架构并存，平滑迁移
4. **成熟技术栈**: 基于现有FlinkService和Hologres集成扩展
5. **详细实施计划**: 分阶段实施，风险可控
6. **完善质量保证**: 多层次测试和监控保障

通过本方案实施，将显著提升Datatube平台的性能和实时性，为业务发展提供更强有力的技术支撑。

---

**文档状态**: 待审核
**下一步行动**: 技术评审和开发排期
**联系人**: Datatube技术架构师
**最后更新**: 2025-01-25
