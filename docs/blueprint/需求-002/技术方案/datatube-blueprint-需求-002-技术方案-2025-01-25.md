# Datatube技术方案说明书 - 需求-002

**文档标题**: 基于Flink+Hologres新架构升级批流ETL代码生成引擎技术方案说明书  
**文档编号**: datatube-blueprint-需求-002-技术方案-2025-01-25  
**版本号**: v1.0  
**创建日期**: 2025-01-25  
**创建人**: Datatube技术架构师  
**审核人**: 技术负责人  
**批准人**: 项目负责人  

## 1. 技术方案概述

### 1.1 方案背景
基于需求说明书分析，现有Blink SQL+ADB3架构存在性能瓶颈和运维复杂性问题。本技术方案旨在通过新增Flink+Hologres架构支持，在保持现有架构稳定运行的前提下，提供更高性能的批流ETL解决方案。

### 1.2 设计原则
- **零影响原则**: 现有架构完全不修改，确保旧架构零风险
- **独立入口原则**: 新增独立的Flink架构入口，与现有架构完全隔离
- **组合复用原则**: 通过组合方式复用现有组件，而非修改现有代码
- **平滑迁移原则**: 支持新旧架构并存，业务可逐步迁移

### 1.3 技术选型
- **流计算引擎**: 阿里云Flink（复用现有FlinkService，新增独立业务层）
- **数据存储**: Hologres（复用现有连接器，新增独立处理逻辑）
- **代码生成**: 新增FlinkSqlBuilder，组合复用ViewModelSqlBuilder核心逻辑
- **调度框架**: 复用现有DAG调度框架，新增独立的DAG构建器
- **模板系统**: 新增独立的FlinkSyncTemplate模板系统

## 2. 架构设计

### 2.1 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    Datatube平台                              │
├─────────────────────────────────────────────────────────────┤
│  业务入口层 (完全隔离的双入口设计)                            │
│  ├── ViewModelHandler (现有Blink架构入口，完全不变)          │
│  └── FlinkViewModelHandler (新增Flink架构入口，独立实现)     │
├─────────────────────────────────────────────────────────────┤
│  业务服务层                                                  │
│  ├── ViewModelService (现有，保持不变)                       │
│  └── FlinkViewModelService (新增，独立的Flink业务逻辑)       │
├─────────────────────────────────────────────────────────────┤
│  代码生成层                                                  │
│  ├── ViewModelSqlBuilder (现有，保持不变)                    │
│  ├── FlinkSqlBuilder (新增，组合复用现有逻辑)                │
│  ├── Adb3SyncTemplate (现有，保持不变)                       │
│  └── FlinkSyncTemplate (新增，独立模板系统)                  │
├─────────────────────────────────────────────────────────────┤
│  DAG构建层                                                   │
│  ├── 现有DAG构建逻辑 (保持不变)                              │
│  └── FlinkDagBuilder (新增，独立的Flink DAG构建器)           │
├─────────────────────────────────────────────────────────────┤
│  共享基础服务层 (可复用，不修改)                             │
│  ├── FlinkService (现有，通过组合方式复用)                   │
│  ├── DatasourceService (现有，通过组合方式复用)              │
│  ├── QanatDagJobProcessor (现有DAG调度框架，复用)            │
│  └── QanatFlinkJobProcessor (现有处理器，复用)               │
├─────────────────────────────────────────────────────────────┤
│  基础设施层 (完全复用，不修改)                               │
│  ├── FlinkClient (现有)                                     │
│  ├── QanatDatasourceHandler (现有)                          │
│  └── 数据库访问层 (现有)                                    │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 双架构数据流向设计

#### 2.2.1 现有Blink架构流向（保持不变）
```
YAML配置 → ViewModelHandler → ViewModelSqlBuilder → Adb3SyncTemplate →
Blink作业 → DRC+Kafka → ADB3表 → 视图模型
```

#### 2.2.2 新增Flink架构流向（完全独立）
```
YAML配置 → FlinkViewModelHandler → FlinkSqlBuilder → FlinkSyncTemplate →
Flink作业 → Hologres binlog → Hologres表 → 视图模型
```

### 2.3 架构隔离设计
- **完全独立的入口**: FlinkViewModelHandler与ViewModelHandler完全隔离
- **独立的业务逻辑**: FlinkViewModelService独立处理Flink相关业务
- **组合复用基础服务**: 通过依赖注入复用FlinkService、DatasourceService等
- **独立的模板系统**: FlinkSyncTemplate与现有模板系统完全独立
- **共享基础设施**: 复用DAG调度框架、数据库访问层等基础设施

## 3. 详细技术设计

### 3.1 新增组件设计

#### 3.1.1 FlinkViewModelHandler（新增入口）
**文件路径**: `qanat-aliyun-inc-com-service/src/main/java/com/aliyun/wormhole/qanat/service/viewmodel/FlinkViewModelHandler.java`

**设计要点**:
- 完全独立的新入口，与ViewModelHandler无任何继承关系
- 提供与ViewModelHandler相同的方法签名，保持API一致性
- 内部通过组合方式复用现有服务和组件
- 专门处理Flink+Hologres架构的业务逻辑

**核心方法**:
```java
@Component
public class FlinkViewModelHandler {

    @Resource
    private FlinkViewModelService flinkViewModelService;

    @Resource
    private FlinkSqlBuilder flinkSqlBuilder;

    @Resource
    private FlinkDagBuilder flinkDagBuilder;

    // 与ViewModelHandler相同的方法签名，但内部实现完全基于Flink
    public List<String> createBatchStreamTasks(String tenantId, Long viewModelId) {
        return flinkViewModelService.createFlinkBatchStreamTasks(tenantId, viewModelId);
    }

    public Boolean createTableAndFullSync(String tenantId, Long viewModelId, String batchJobs) {
        return flinkViewModelService.createHologresTableAndFullSync(tenantId, viewModelId, batchJobs);
    }
}
```

#### 3.1.2 FlinkViewModelService（新增业务服务）
**文件路径**: `qanat-aliyun-inc-com-service/src/main/java/com/aliyun/wormhole/qanat/service/viewmodel/FlinkViewModelService.java`

**设计要点**:
- 独立的业务服务类，专门处理Flink架构的业务逻辑
- 通过依赖注入复用现有基础服务（FlinkService、DatasourceService等）
- 组合使用FlinkSqlBuilder和FlinkDagBuilder
- 与现有ViewModelService完全隔离

#### 3.1.3 FlinkSqlBuilder（新增SQL生成器）
**文件路径**: `qanat-aliyun-inc-com-service/src/main/java/com/aliyun/wormhole/qanat/service/viewmodel/FlinkSqlBuilder.java`

**设计要点**:
- 独立的SQL生成器，不继承ViewModelSqlBuilder
- 通过组合方式复用ViewModelSqlBuilder的核心逻辑
- 专门生成Flink SQL语法
- 支持Hologres特有的语法和函数

**核心方法**:
```java
@Component
public class FlinkSqlBuilder {

    @Resource
    private ViewModelSqlBuilder viewModelSqlBuilder; // 组合复用现有逻辑

    public String generateFlinkCreateTableSql(ViewModel dataModel) {
        // 生成Flink CREATE TABLE语句
    }

    public String generateFlinkInsertSql(String tenantId, ViewModel dataModel) {
        // 组合使用现有SQL生成逻辑，适配Flink语法
        String selectSql = viewModelSqlBuilder.getSelectSql(tenantId, dataModel, "hologres");
        return "INSERT INTO " + dataModel.getCode() + " " + selectSql;
    }

    public String generateHologresBinlogSourceSql(ViewModel dataModel) {
        // 生成Hologres binlog源表定义
    }
}
```

#### 3.1.4 FlinkDagBuilder（新增DAG构建器）
**文件路径**: `qanat-aliyun-inc-com-service/src/main/java/com/aliyun/wormhole/qanat/service/dag/FlinkDagBuilder.java`

**设计要点**:
- 独立的DAG构建器，专门构建Flink架构的DAG
- 复用现有的DAG节点类型（FlinkNode、FlinkStreamNode、HoloExtTblNode等）
- 生成适配Flink架构的DAG脚本
- 与现有DAG构建逻辑完全隔离

#### 3.1.5 FlinkSyncTemplate（新增模板系统）
**文件路径**: `qanat-aliyun-inc-com-service/src/main/java/com/aliyun/wormhole/qanat/service/template/FlinkSyncTemplate.java`

**设计要点**:
- 完全独立的模板系统，与Adb3SyncTemplate无任何关系
- 专门为Flink+Hologres架构设计的模板
- 支持Hologres binlog、Flink SQL等特有语法

**模板内容**:
- FLINK_SYNC_SQL: Flink SQL作业模板
- FLINK_DAG_SCRIPT: Flink DAG调度脚本模板
- HOLO_BINLOG_SOURCE: Hologres binlog源表模板
- HOLO_SINK_TABLE: Hologres目标表模板

### 3.2 基础服务复用设计

#### 3.2.1 FlinkService复用（无需修改）
**复用方式**: 通过依赖注入在FlinkViewModelService中使用
- 现有FlinkService接口和实现保持完全不变
- FlinkViewModelService通过@Resource注入FlinkService
- 调用现有的createJob、startJob、stopJob等方法
- 如果发现FlinkServiceImpl.createJob实现不完整，可以单独完善，不影响现有逻辑

#### 3.2.2 DatasourceService复用（无需修改）
**复用方式**: 通过依赖注入在FlinkViewModelService中使用
- 现有DatasourceService接口和实现保持完全不变
- 复用现有的Hologres连接和元数据管理功能
- 复用现有的数据源注册和管理功能

#### 3.2.3 DAG调度框架复用（无需修改）
**复用方式**: FlinkDagBuilder生成的DAG可直接被现有调度框架执行
- QanatDagJobProcessor保持完全不变
- QanatFlinkJobProcessor保持完全不变（已支持FlinkStreamNode等）
- QanatHoloExternalTableJobProcessor保持完全不变
- 新架构生成的DAG与现有DAG格式完全兼容

### 3.3 API接口设计

#### 3.3.1 新增独立的API端点
为了保证架构完全隔离，新增独立的API端点：

**现有Blink架构API（保持不变）**:
- `POST /api/viewmodel/create` - 使用ViewModelHandler
- `POST /api/viewmodel/sync` - 使用ViewModelHandler
- `POST /api/viewmodel/tasks` - 使用ViewModelHandler

**新增Flink架构API**:
- `POST /api/flink/viewmodel/create` - 使用FlinkViewModelHandler
- `POST /api/flink/viewmodel/sync` - 使用FlinkViewModelHandler
- `POST /api/flink/viewmodel/tasks` - 使用FlinkViewModelHandler

#### 3.3.2 Controller层设计
**新增FlinkViewModelController**:
```java
@RestController
@RequestMapping("/api/flink/viewmodel")
public class FlinkViewModelController {

    @Resource
    private FlinkViewModelHandler flinkViewModelHandler;

    @PostMapping("/create")
    public DataResult<Boolean> createViewModel(@RequestBody ViewModelRequest request) {
        // 调用FlinkViewModelHandler处理
        return flinkViewModelHandler.createViewModelFromYaml(request);
    }

    @PostMapping("/tasks")
    public DataResult<List<String>> createTasks(@RequestBody TaskRequest request) {
        // 调用FlinkViewModelHandler处理
        return flinkViewModelHandler.createBatchStreamTasks(request.getTenantId(), request.getViewModelId());
    }
}
```

#### 3.3.3 配置选择机制
除了独立API端点外，也可以通过配置参数选择架构：
```java
// 在现有API中增加可选参数
@PostMapping("/api/viewmodel/create")
public DataResult<Boolean> createViewModel(@RequestBody ViewModelRequest request) {
    if ("flink".equalsIgnoreCase(request.getArchType())) {
        return flinkViewModelHandler.createViewModelFromYaml(request);
    } else {
        return viewModelHandler.createViewModelFromYaml(request); // 现有逻辑
    }
}
```

### 3.4 架构隔离保证

#### 3.4.1 零修改保证
**现有组件完全不修改**:
- ViewModelHandler：一行代码都不修改
- ViewModelService：保持原样
- ViewModelSqlBuilder：保持原样
- Adb3SyncTemplate：保持原样
- 所有现有的处理器：保持原样
- 所有现有的API：保持原样

#### 3.4.2 独立部署保证
**新组件独立部署**:
- 所有新增组件可以独立编译、测试、部署
- 新组件出现问题不会影响现有功能
- 可以独立回滚新组件而不影响现有系统
- 支持分阶段部署和灰度发布

#### 3.4.3 数据隔离保证
**数据层面完全隔离**:
- 新架构使用独立的Hologres数据源
- 现有架构继续使用ADB3数据源
- 两套架构的数据完全独立，互不影响
- 支持数据迁移但不强制迁移

## 4. 数据库设计

### 4.1 完全复用现有表结构
**无需新增任何表**，现有表结构完全满足需求：
- datasource表：已支持多种数据源类型，包括Hologres
- ds_field_info表：已支持字段元数据管理
- view_model_info表：已支持视图模型定义
- task_info表：已支持任务管理
- 所有其他业务表：完全复用

### 4.2 配置数据隔离
**通过数据内容区分架构类型**：
- 在view_model_info表中通过字段值区分架构类型
- 在task_info表中通过任务名称前缀区分（如：flink_xxx）
- 在datasource表中通过数据源名称区分
- 无需修改任何表结构，仅通过数据内容实现隔离

## 5. 接口设计

### 5.1 现有接口完全不变
**零修改保证**：
- ViewModelHandler的所有方法：完全不修改
- 所有现有Controller：完全不修改
- 所有现有Service接口：完全不修改
- 所有现有API端点：完全不修改

### 5.2 新增独立接口
**完全独立的新接口**：
- FlinkViewModelHandler：新增的独立入口
- FlinkViewModelController：新增的独立Controller
- FlinkViewModelService：新增的独立Service
- 新增独立的API端点：/api/flink/xxx

### 5.3 接口选择机制
**两种调用方式**：
1. **独立端点方式**：调用/api/flink/xxx使用新架构
2. **参数选择方式**：在请求参数中指定archType="flink"

## 6. 配置管理

### 6.1 YAML配置兼容性
**100%兼容现有YAML格式**，通过以下方式实现：
- 保持ViewModel实体类不变
- 扩展YamlUtil解析逻辑，支持架构类型标识
- 数据源引用自动映射到Hologres表

### 6.2 新增配置项
```yaml
# 在现有YAML基础上可选增加
settings:
  architecture: "flink"  # 可选：blink | flink，默认blink
  hologres:
    computeGroup: "default"  # Hologres计算组
    readWriteSeparation: true  # 读写分离
```

## 7. 部署方案

### 7.1 分阶段部署策略

#### 第一阶段：基础组件部署（周期：2周）
**部署内容**:
- FlinkSqlBuilder类
- FlinkSyncTemplate模板
- FlinkService完善
- 单元测试

**部署步骤**:
1. 代码开发和本地测试
2. 提交代码审查
3. 部署到开发环境
4. 执行单元测试和集成测试
5. 部署到测试环境

#### 第二阶段：DAG调度扩展（周期：2周）
**部署内容**:
- QanatFlinkJobProcessor扩展
- ViewModelHandler扩展
- 端到端测试

**部署步骤**:
1. 完成开发和测试
2. 在测试环境验证DAG调度功能
3. 执行兼容性测试
4. 部署到预发环境

#### 第三阶段：生产环境部署（周期：1周）
**部署内容**:
- 完整功能部署
- 监控和告警配置
- 用户培训

**部署步骤**:
1. 生产环境部署
2. 灰度验证
3. 全量发布
4. 监控观察

### 7.2 回滚方案
- **代码回滚**: 通过Git版本控制快速回滚
- **配置回滚**: 保持现有配置不变，新功能通过开关控制
- **数据回滚**: 无数据结构变更，无需数据回滚

## 8. 测试方案

### 8.1 单元测试
**测试范围**:
- FlinkSqlBuilder核心方法测试
- FlinkService方法测试
- ViewModelHandler扩展方法测试
- 模板生成逻辑测试

**测试用例设计**:
```java
@Test
public void testFlinkSqlBuilderSelectSql() {
    FlinkSqlBuilder builder = new FlinkSqlBuilder();
    ViewModel model = createTestViewModel();
    String sql = builder.getSelectSql("test_tenant", model, "hologres");
    // 验证SQL语法正确性和字段转义
    assertThat(sql).contains("\"field_name\"");
}
```

### 8.2 集成测试
**测试场景**:
- 端到端视图模型创建流程
- DAG调度执行流程
- Flink作业创建和启动
- 数据一致性验证

### 8.3 兼容性测试
**测试内容**:
- 现有YAML配置100%兼容性
- 现有API接口向后兼容性
- 新旧架构并存测试
- 数据迁移一致性测试

### 8.4 性能测试
**测试指标**:
- Flink作业启动时间
- 数据处理延迟对比
- 资源利用率对比
- 并发处理能力测试

## 9. 监控和运维

### 9.1 监控指标
- Flink作业运行状态
- Hologres连接池状态
- 数据处理延迟
- 错误率和成功率

### 9.2 告警配置
- Flink作业失败告警
- Hologres连接异常告警
- 数据延迟超阈值告警
- 资源使用率告警

### 9.3 日志管理
- 统一日志格式
- 关键操作审计日志
- 错误日志聚合分析
- 性能日志监控

## 10. 风险控制

### 10.1 技术风险控制
- **代码复用率目标**: 70%以上，降低开发风险
- **渐进式发布**: 分阶段部署，及时发现问题
- **完整测试覆盖**: 单元测试+集成测试+性能测试
- **监控告警**: 实时监控，快速响应

### 10.2 业务风险控制
- **向后兼容**: 保证现有功能不受影响
- **并存架构**: 新旧架构可同时运行
- **快速回滚**: 支持快速回滚到稳定版本
- **灰度发布**: 小范围验证后全量发布

## 11. 具体实施细节

### 11.1 核心类修改清单

#### 11.1.1 新增类列表（完全独立）
| 类名 | 文件路径 | 功能描述 | 代码行数估算 |
|------|----------|----------|-------------|
| FlinkViewModelHandler | qanat-aliyun-inc-com-service/.../FlinkViewModelHandler.java | Flink架构入口 | 150行 |
| FlinkViewModelService | qanat-aliyun-inc-com-service/.../FlinkViewModelService.java | Flink业务服务 | 300行 |
| FlinkViewModelController | qanat-aliyun-inc-com-service/.../FlinkViewModelController.java | Flink控制器 | 100行 |
| FlinkSqlBuilder | qanat-aliyun-inc-com-service/.../FlinkSqlBuilder.java | Flink SQL生成器 | 200行 |
| FlinkDagBuilder | qanat-aliyun-inc-com-service/.../FlinkDagBuilder.java | Flink DAG构建器 | 150行 |
| FlinkSyncTemplate | qanat-aliyun-inc-com-service/.../FlinkSyncTemplate.java | Flink模板定义 | 300行 |

#### 11.1.2 修改类列表（零修改）
**重要说明：现有类完全不修改！**

| 类名 | 修改内容 | 影响范围 |
|------|----------|----------|
| 所有现有类 | 完全不修改 | 零风险 |

**可选的独立完善**（不影响现有功能）：
| 类名 | 可选完善内容 | 影响范围 |
|------|-------------|----------|
| FlinkServiceImpl | 如createJob方法实现不完整，可独立完善 | 零风险（独立方法） |

### 11.2 详细开发任务分解

#### 11.2.1 第一阶段任务（2周）
**任务1: FlinkSqlBuilder开发**
- 负责人：后端开发工程师A
- 工期：4天
- 交付物：FlinkSqlBuilder.java + 单元测试
- 验收标准：通过所有单元测试，代码覆盖率>80%

**任务2: FlinkSyncTemplate开发**
- 负责人：后端开发工程师B
- 工期：3天
- 交付物：FlinkSyncTemplate.java
- 验收标准：模板语法正确，支持参数化

**任务3: FlinkDagBuilder开发**
- 负责人：后端开发工程师C
- 工期：3天
- 交付物：FlinkDagBuilder.java + 单元测试
- 验收标准：能够生成正确的Flink DAG

**任务4: FlinkViewModelService开发**
- 负责人：后端开发工程师A
- 工期：4天
- 交付物：FlinkViewModelService.java + 单元测试
- 验收标准：核心业务逻辑正确，测试覆盖率>80%

#### 11.2.2 第二阶段任务（2周）
**任务5: FlinkViewModelHandler开发**
- 负责人：后端开发工程师B
- 工期：3天
- 交付物：FlinkViewModelHandler.java + 单元测试
- 验收标准：提供完整的入口功能，API兼容

**任务6: FlinkViewModelController开发**
- 负责人：后端开发工程师C
- 工期：2天
- 交付物：FlinkViewModelController.java + API测试
- 验收标准：API接口正常，参数验证正确

**任务7: 集成联调**
- 负责人：后端开发工程师A
- 工期：4天
- 交付物：完整的Flink架构集成
- 验收标准：所有组件协作正常

**任务8: 端到端测试**
- 负责人：测试工程师
- 工期：5天
- 交付物：端到端测试报告
- 验收标准：完整流程测试通过，与现有架构隔离验证

#### 11.2.3 第三阶段任务（1周）
**任务8: 生产环境部署**
- 负责人：运维工程师
- 工期：2天
- 交付物：生产环境部署
- 验收标准：服务正常运行

**任务9: 监控配置**
- 负责人：运维工程师
- 工期：2天
- 交付物：监控和告警配置
- 验收标准：监控指标正常采集

**任务10: 用户培训**
- 负责人：产品经理
- 工期：1天
- 交付物：培训材料和培训记录
- 验收标准：用户能够正常使用新功能

### 11.3 灰度发布方案

#### 11.3.1 灰度策略
**阶段1: 内部验证（1天）**
- 范围：开发团队内部测试
- 验证内容：基础功能正常性
- 成功标准：所有核心功能正常

**阶段2: 小范围试点（3天）**
- 范围：选择1-2个低风险业务场景
- 验证内容：真实业务场景验证
- 成功标准：数据一致性100%，性能指标达标

**阶段3: 扩大范围（1周）**
- 范围：扩展到10%的业务场景
- 验证内容：并发处理能力和稳定性
- 成功标准：系统稳定运行，无重大问题

**阶段4: 全量发布（1周）**
- 范围：所有业务场景
- 验证内容：全量业务验证
- 成功标准：系统整体稳定，用户反馈良好

#### 11.3.2 回滚触发条件
- 系统可用性低于99%
- 数据一致性问题
- 性能指标下降超过20%
- 出现P0级别故障

#### 11.3.3 回滚执行步骤
1. 立即停止新功能使用
2. 切换到原有架构
3. 验证系统恢复正常
4. 分析问题原因
5. 制定修复方案

### 11.4 质量保证措施

#### 11.4.1 代码质量
- **代码审查**: 所有代码必须经过至少2人审查
- **静态分析**: 使用SonarQube进行代码质量检查
- **单元测试**: 代码覆盖率要求>80%
- **集成测试**: 覆盖所有主要业务场景

#### 11.4.2 性能质量
- **基准测试**: 建立性能基准线
- **压力测试**: 验证系统承载能力
- **延迟测试**: 验证实时性指标
- **资源监控**: 监控CPU、内存、网络使用情况

#### 11.4.3 安全质量
- **权限验证**: 验证现有权限体系兼容性
- **数据加密**: 确保敏感数据传输加密
- **审计日志**: 记录所有关键操作
- **漏洞扫描**: 定期进行安全漏洞扫描

## 12. 总结

本技术方案采用**完全独立的新架构入口**设计，确保现有架构零风险。主要优势：

### 12.1 核心优势
1. **零风险保证**: 现有架构完全不修改，确保业务连续性
2. **完全架构隔离**: 新旧架构完全独立，互不影响
3. **高代码复用率**: 通过组合方式复用70%以上现有代码
4. **独立演进能力**: 新架构可独立迭代优化
5. **平滑迁移支持**: 支持业务逐步从旧架构迁移到新架构

### 12.2 技术特点
- **新增6个核心类**: 完全独立实现，无任何现有类修改
- **双入口设计**: ViewModelHandler（现有）+ FlinkViewModelHandler（新增）
- **组合复用模式**: 通过依赖注入复用基础服务，而非继承修改
- **独立API端点**: /api/flink/xxx 与现有API完全隔离
- **数据层隔离**: 通过数据内容区分，无需修改表结构

### 12.3 实施保障
- **分阶段部署**: 2+2+1周的渐进式实施计划
- **独立测试**: 新架构测试完全独立，不影响现有测试
- **灰度发布**: 4阶段灰度，风险完全可控
- **快速回滚**: 支持独立回滚，不影响现有系统

### 12.4 业务价值
通过本方案实施，将在**零风险**的前提下：
- 显著提升批流ETL性能和实时性
- 简化技术栈，降低运维复杂度
- 为业务发展提供更强有力的技术支撑
- 支持新旧架构并存，业务可按需选择

**这是一个真正意义上的零风险架构升级方案！**

---

**文档状态**: 待审核
**下一步行动**: 技术评审和开发排期
**联系人**: Datatube技术架构师
**最后更新**: 2025-01-25
