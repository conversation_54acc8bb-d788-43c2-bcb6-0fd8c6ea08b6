# Qanat Blueprint - 监控与运维

## 1. 监控体系概览

### 1.1 监控架构图
```
监控数据收集层
├── 应用监控：接口响应时间、错误率统计、并发连接数、JVM状态
├── 基础设施监控：CPU/内存使用、网络I/O、磁盘使用率、数据库连接池
├── 业务监控：任务执行状态、数据同步延迟、流任务RT、数据质量
└── 链路监控：HSF调用链、SQL执行、外部依赖、异常追踪
```

### 1.2 监控指标分类
| 监控类型 | 指标数量 | 覆盖范围 | 更新频率 | 告警级别 |
|---------|----------|----------|----------|----------|
| 系统指标 | 50+ | 基础设施 | 1分钟 | P1/P2 |
| 应用指标 | 100+ | 业务服务 | 1分钟 | P1/P2/P3 |
| 业务指标 | 80+ | 核心业务 | 5分钟 | P1/P2 |
| 链路指标 | 200+ | 调用链路 | 实时 | P2/P3 |

## 2. 应用性能监控

### 2.1 Alimonitor集成
```properties
spring.alimonitor.enabled=true
spring.alimonitor.app-name=qanat-aliyun-inc-com
spring.alimonitor.method-patterns[0]=com.aliyun.wormhole.qanat.service.*
spring.alimonitor.method-patterns[1]=com.aliyun.wormhole.qanat.dal.*
```

**监控指标**:
- 接口响应时间：RT分布、P99/P95响应时间
- 接口成功率：成功/失败请求比例
- 并发量：QPS、并发连接数
- 异常统计：异常类型、频率分析

### 2.2 自定义业务指标
```java
@Component
public class BusinessMetrics {
    @Autowired
    private MeterRegistry meterRegistry;
    
    private Counter datasourceCreateCounter;
    private Counter taskSuccessCounter;
    private Timer blinkJobTimer;
    
    @PostConstruct
    public void initMetrics() {
        this.datasourceCreateCounter = Counter.builder("qanat.datasource.create")
            .description("Count of datasource creation")
            .tag("app", "qanat")
            .register(meterRegistry);
            
        this.taskSuccessCounter = Counter.builder("qanat.task.success")
            .description("Count of successful tasks")
            .tag("app", "qanat")
            .register(meterRegistry);
    }
}
```

## 3. 链路追踪监控

### 3.1 EagleEye链路追踪
```properties
spring.eagleeye.enabled=true
spring.eagleeye.mdc-updater=slf4j
spring.eagleeye.sample-rate=0.1
```

**追踪范围**:
- HTTP请求：Controller层的所有HTTP请求
- HSF调用：所有HSF服务调用
- 数据库操作：MyBatis SQL执行
- 消息队列：MetaQ消息发送和接收
- 缓存操作：Redis读写操作

## 4. 告警体系

### 4.1 告警级别定义
| 告警级别 | 描述 | 响应时间 | 通知方式 | 处理策略 |
|---------|------|----------|----------|----------|
| P1 - 严重 | 系统不可用，影响核心业务 | 5分钟 | 电话+短信+钉钉 | 立即响应 |
| P2 - 重要 | 功能异常，影响部分业务 | 15分钟 | 短信+钉钉 | 优先处理 |
| P3 - 一般 | 性能下降，不影响功能 | 30分钟 | 钉钉+邮件 | 正常处理 |
| P4 - 提醒 | 趋势异常，预警信息 | 1小时 | 邮件 | 关注处理 |

### 4.2 告警规则配置
```yaml
alerting:
  rules:
    - name: "high_cpu_usage"
      level: "P2"
      condition: "cpu_usage > 80"
      duration: "5m"
      message: "CPU使用率超过80%"
      
    - name: "high_error_rate"
      level: "P1"
      condition: "error_rate > 5"
      duration: "2m"
      message: "错误率超过5%"
      
    - name: "task_failure"
      level: "P1"
      condition: "task_fail_count > 10"
      duration: "5m"
      message: "任务失败数量超过10个"
```

## 5. 运维工具

### 5.1 健康检查
```java
@RestController
public class HealthController {
    
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        // 检查各个组件健康状态
        health.put("database", checkDatabaseHealth());
        health.put("redis", checkRedisHealth());
        health.put("hsf", checkHsfHealth());
        health.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(health);
    }
}
```

### 5.2 运维接口
```java
@RestController
@RequestMapping("/ops")
public class OpsController {
    
    // 紧急停止任务
    @PostMapping("/tasks/{taskId}/stop")
    public ResponseEntity<String> stopTask(@PathVariable String taskId) {
        try {
            taskService.emergencyStopTask(taskId);
            return ResponseEntity.ok("Task stopped successfully");
        } catch (Exception e) {
            return ResponseEntity.status(500).body("Failed: " + e.getMessage());
        }
    }
    
    // 清理缓存
    @PostMapping("/cache/clear")
    public ResponseEntity<String> clearCache() {
        try {
            cacheService.clearAllCache();
            return ResponseEntity.ok("Cache cleared successfully");
        } catch (Exception e) {
            return ResponseEntity.status(500).body("Failed: " + e.getMessage());
        }
    }
}
```

---

**文档版本**: v1.0
**创建时间**: 2024-01-XX
**维护团队**: Qanat平台运维组 