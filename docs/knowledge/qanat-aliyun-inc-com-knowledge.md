# Qanat-Aliyun-Inc-Com 工程 knowledge

> **项目概述**: 基于阿里云内部的企业级数据处理和流计算平台，提供数据源管理、数据传输、流计算处理和开放API服务。

## 项目基本信息

- **项目名称**: `qanat-aliyun-inc-com`
- **项目描述**: `企业级数据处理平台，专注于数据源管理、数据传输管道、流计算处理和多种数据库连接器`
- **技术栈**:
  - 框架: `Spring Boot` `1.5.20.RELEASE`
  - 构建工具: `Maven`
  - 编程语言: `Java` `1.8`
  - 数据库: `MySQL`
  - 消息队列: `Kafka`
  - 缓存: `Redis (通过 HSF)`
  - 流计算引擎: `Flink/Blink`
  - 大数据平台: `ODPS`
  - 其他中间件: `HSF 服务框架`, `SchedulerX2 定时任务`, `MyBatis ORM`, `Velocity 模版引擎`
- **部署环境**:
  - 开发环境: `daily 环境`
  - 测试环境: `testing 环境`
  - 预发环境: `staging-sg 环境`
  - 生产环境: `production/production-sg 环境`

## 核心功能模块

- **数据源管理模块**: `qanat-datasource` - `数据源的统一管理和元数据维护` - `com.aliyun.wormhole.qanat.datasource`
- **服务层模块**: `qanat-aliyun-inc-com-service` - `核心业务逻辑和服务实现` - `com.aliyun.wormhole.qanat.service`
- **数据访问层模块**: `qanat-service-dao` - `数据库访问和ORM映射` - `com.aliyun.wormhole.qanat.dal`
- **API接口模块**: `qanat-service-api` - `服务接口定义和契约` - `com.aliyun.wormhole.qanat.api`
- **开放API模块**: `qanat-openapi` - `对外开放的REST API服务` - `com.aliyun.wormhole.qanat.openapi`
- **启动模块**: `qanat-aliyun-inc-com-start` - `应用启动和配置管理` - `com.aliyun.wormhole`

### Flink/Blink 连接器模块 (9个)
- **ADB3连接器**: `qanat-blink-connector-adb3` - `连接AnalyticDB 3.0数据库`
- **ElasticSearch连接器**: `qanat-blink-connector-elasticsearch` - `连接ElasticSearch搜索引擎`  
- **Kafka连接器**: `qanat-blink-connector-kafka010` - `连接Kafka 0.10版本消息队列`
- **MongoDB连接器**: `qanat-blink-connector-mongodb` - `连接MongoDB数据库`
- **PostgreSQL连接器**: `qanat-blink-connector-rds-pg` - `连接RDS PostgreSQL数据库`
- **TDDL连接器**: `qanat-blink-connector-tddl` - `连接阿里TDDL分布式数据库`
- **MySQL扫描连接器**: `qanat-blink-connector-mysqlscan` - `MySQL数据库全表扫描连接器`
- **HTTP连接器**: `qanat-blink-connector-http` - `HTTP数据源连接器`
- **DRC连接器**: `qanat-blink-connector-drc` - `数据实时同步连接器`

### UDF/UDTF 模块 (5个)
- **Blink UDF**: `qanat-blink-udf` - `Blink流计算用户自定义函数`
- **Blink UDTF**: `qanat-blink-udtf` - `Blink流计算用户自定义表函数`
- **HSF UDF**: `qanat-blink-hsf-udf` - `集成HSF服务的用户自定义函数`
- **Flink UDF**: `qanat-flink-udf` - `Flink流计算用户自定义函数`
- **ODPS UDF**: `qanat-odps-udf` - `ODPS大数据平台用户自定义函数`

### 数据处理模块 (5个)
- **CDP域模型**: `qanat-cdp-domain-odps` - `客户数据平台域模型处理`
- **自定义Sink**: `qanat-blink-custom-sink` - `自定义数据输出连接器`
- **CDP Sink**: `qanat-blink-cdp-sink` - `CDP数据平台输出连接器`
- **DRC事件**: `qanat-drc-event` - `数据实时同步事件处理`
- **流事件**: `qanat-stream-event` - `流数据事件处理`

## 技术架构亮点

### 多连接器架构
支持 **10+ 种数据库和中间件连接器**，包括关系型数据库(MySQL, PostgreSQL, TDDL)、NoSQL数据库(MongoDB, Redis)、搜索引擎(ElasticSearch)、消息队列(Kafka)、大数据平台(ODPS, ADB3)等。

### 流计算集成
深度集成 **Flink/Blink** 流计算引擎，支持实时流处理和批处理任务，提供丰富的UDF/UDTF函数库。

### 企业级特性
集成阿里内部完整的中间件体系，包括HSF服务框架、SchedulerX2定时任务、BUC用户认证、ACL权限控制等。

## 详细文档导航

### 📋 基础文档
- **[01. 项目概述](./qanat-aliyun-inc-com-knowledge-01-project-overview.md)** - 项目基本信息、技术栈、业务背景
- **[02. 核心功能模块](./qanat-aliyun-inc-com-knowledge-02-core-modules.md)** - 详细的模块功能说明和技术实现

### 🏗️ 架构设计
- **[03. 工程架构](./qanat-aliyun-inc-com-knowledge-03-architecture.md)** - 系统架构设计和模块依赖关系

### 🌐 接口设计  
- **[04. API设计](./qanat-aliyun-inc-com-knowledge-04-api-design.md)** - REST API和HSF服务接口设计

### 🔄 业务流程
- **[05. 核心链路调用流程](./qanat-aliyun-inc-com-knowledge-05-call-flow.md)** - 关键业务流程和调用链路

### 📊 数据模型
- **[06. 领域实体及关系](./qanat-aliyun-inc-com-knowledge-06-domain-entities.md)** - 数据库实体设计和关系模型

### 🔗 外部依赖
- **[07. 对外部接口的依赖](./qanat-aliyun-inc-com-knowledge-07-external-dependencies.md)** - 外部系统集成和依赖关系

### ⚙️ 配置管理
- **[08. 配置管理](./qanat-aliyun-inc-com-knowledge-08-configuration.md)** - 环境配置和参数管理

### 📈 运维监控  
- **[09. 监控与运维](./qanat-aliyun-inc-com-knowledge-09-monitoring.md)** - 监控体系和运维指南

## 快速开始

### 环境要求
- **JDK**: 1.8+
- **Maven**: 3.6+
- **MySQL**: 5.7+
- **Redis**: 3.2+

### 构建和部署
```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 打包部署
mvn clean package

# 启动应用 
java -jar qanat-aliyun-inc-com-start/target/qanat-aliyun-inc-com-start.jar
```

### 重要配置项
- **数据库配置**: `application.properties` 中的数据源配置
- **HSF配置**: HSF服务版本和超时配置  
- **环境配置**: 通过 `environment.type` 指定运行环境

---

**文档版本**: v1.0  
**最后更新**: 2024年12月  
**维护者**: Qanat团队 