# Qanat-Aliyun-Inc-Com Blueprint - 领域实体及关系

## 1. 核心领域实体

### 1.1 数据源管理实体

#### Datasource (数据源)
- **实体描述**: 系统中数据源的基本信息和元数据
- **主要属性**: 
  - `id`: 主键ID
  - `dsName`: 数据源名称
  - `dsDesc`: 数据源描述
  - `dsType`: 数据源类型 (mysql, pg, mongodb等)
  - `dsUniqueName`: 数据源唯一名称
  - `dbName`: 数据库名称
  - `tableName`: 表名称
  - `objectType`: 对象类型
  - `pkFields`: 主键字段
  - `owner`: 数据源负责人
  - `predictSize`: 预估数据量
  - `qph`: 每小时查询量
  - `meta`: 元数据JSON
- **关联关系**: 
  - 一对多关联 `DsFieldInfo` (数据源字段)
  - 多对多关联 `DatatubeInstance` (数据管道实例)

#### DsFieldInfo (数据源字段信息)
- **实体描述**: 数据源中字段的详细信息
- **主要属性**:
  - `fieldName`: 字段名称
  - `fieldType`: 字段类型
  - `fieldDesc`: 字段描述
  - `isPk`: 是否主键
  - `isFk`: 是否外键
  - `isNotNull`: 是否非空
  - `defaultValue`: 默认值
- **关联关系**: 多对一关联 `Datasource`

#### DsCatelog (数据源分类)
- **实体描述**: 数据源的分类管理
- **主要属性**:
  - `catelogName`: 分类名称
  - `catelogDesc`: 分类描述
- **关联关系**: 一对多关联 `Datasource`

### 1.2 数据传输管道实体

#### DatatubeInstance (数据传输实例)
- **实体描述**: 数据传输管道的实例配置
- **主要属性**:
  - `code`: 实例编码
  - `name`: 实例名称
  - `provider`: 服务提供方
  - `level`: 实例级别
  - `type`: 实例类型
  - `appName`: 应用名称
  - `computeCost`: 计算成本
  - `storeCost`: 存储成本
  - `consistentRate`: 一致性率
  - `delayMs`: 延迟毫秒数
- **关联关系**:
  - 一对多关联 `DatatubeInstanceModelField`
  - 一对多关联 `DatatubeInstanceModelObj`
  - 一对多关联 `DatatubeInstanceTask`

#### DatatubeInstanceModelField (实例模型字段)
- **实体描述**: 数据传输实例中的模型字段配置
- **主要属性**:
  - `fieldName`: 字段名称
  - `refFieldName`: 引用字段名称
  - `fieldType`: 字段类型
  - `fieldDesc`: 字段描述
  - `isMultivalue`: 是否多值
  - `isPk`: 是否主键
  - `isFk`: 是否外键
  - `isFunc`: 是否函数
  - `modelObjectCode`: 模型对象编码
- **关联关系**: 多对一关联 `DatatubeInstance`

#### DatatubeInstanceModelObj (实例模型对象)
- **实体描述**: 数据传输实例中的模型对象配置
- **主要属性**:
  - `modelObjectCode`: 模型对象编码
  - `modelObjectType`: 模型对象类型
  - `refDsName`: 引用数据源名称
  - `filter`: 过滤条件
  - `isMain`: 是否主对象
  - `isLookup`: 是否查找表
  - `relType`: 关系类型
  - `tpm`: 每分钟事务数
  - `sla`: 服务级别协议
- **关联关系**: 多对一关联 `DatatubeInstance`

### 1.3 任务管理实体

#### TaskInfo (任务信息)
- **实体描述**: 数据处理任务的基本信息
- **主要属性**:
  - `name`: 任务名称
  - `taskDesc`: 任务描述
  - `policy`: 执行策略
  - `engineType`: 引擎类型 (blink, flink等)
  - `externalId`: 外部系统ID
  - `appName`: 应用名称
  - `timeExpression`: 时间表达式
  - `meta`: 任务元数据
  - `dag`: DAG配置
  - `subTasks`: 子任务列表
  - `dagScript`: DAG脚本
- **关联关系**: 
  - 一对多关联 `TaskInstance` (任务实例)
  - 多对多关联 `Datasource` (通过DsTaskRelation)

#### TaskInstance (任务实例)
- **实体描述**: 任务的执行实例
- **主要属性**:
  - `taskId`: 任务ID
  - `status`: 执行状态
  - `startTime`: 开始时间
  - `endTime`: 结束时间
  - `instanceId`: 实例ID
  - `bizDate`: 业务日期
- **关联关系**: 多对一关联 `TaskInfo`

### 1.4 应用和资源管理实体

#### AppInfo (应用信息)
- **实体描述**: 系统中应用的基本信息
- **主要属性**:
  - `appName`: 应用名称
  - `appDesc`: 应用描述
  - `owner`: 应用负责人
- **关联关系**: 
  - 一对多关联 `TaskInfo`
  - 多对多关联 `Resource` (通过AppResourceRelation)

#### Resource (资源信息)
- **实体描述**: 系统资源的配置信息
- **主要属性**:
  - `resourceName`: 资源名称
  - `resourceType`: 资源类型
  - `resourceDesc`: 资源描述
  - `owner`: 资源负责人
  - `parentResourceId`: 父资源ID
  - `meta`: 资源元数据
- **关联关系**: 
  - 多对多关联 `AppInfo` (通过AppResourceRelation)
  - 自关联 (父子资源关系)

#### TenantInfo (租户信息)
- **实体描述**: 多租户系统的租户配置
- **主要属性**:
  - `tenantId`: 租户ID
  - `name`: 租户名称
  - `unit`: 单元
  - `defaultDw`: 默认数据仓库
  - `etlDw`: ETL数据仓库
  - `backupDw`: 备份数据仓库
  - `mdpMeta`: MDP元数据
- **关联关系**: 一对多关联所有业务实体

### 1.5 视图模型实体

#### ViewModelInfo (视图模型信息)
- **实体描述**: 视图模型的配置信息
- **主要属性**:
  - `modelName`: 模型名称
  - `modelDesc`: 模型描述
  - `objectType`: 对象类型
  - `appName`: 应用名称
  - `versionId`: 版本ID
  - `dbName`: 数据库名称
- **关联关系**: 
  - 一对多关联 `ViewModelVersion`
  - 多对多关联 `Datasource` (通过ViewModelDsRelation)

#### ViewModelVersion (视图模型版本)
- **实体描述**: 视图模型的版本管理
- **主要属性**:
  - `versionCode`: 版本编码
  - `versionDesc`: 版本描述
  - `isPublished`: 是否已发布
  - `publishTime`: 发布时间
- **关联关系**: 多对一关联 `ViewModelInfo`

## 2. 实体关系图

### 2.1 核心实体关系
```
TenantInfo (租户)
    ├── AppInfo (应用)
    │   ├── TaskInfo (任务)
    │   │   └── TaskInstance (任务实例)
    │   └── ViewModelInfo (视图模型)
    │       └── ViewModelVersion (模型版本)
    ├── Datasource (数据源)
    │   └── DsFieldInfo (字段信息)
    ├── DatatubeInstance (数据管道)
    │   ├── DatatubeInstanceModelField (模型字段)
    │   ├── DatatubeInstanceModelObj (模型对象)
    │   └── DatatubeInstanceTask (管道任务)
    └── Resource (资源)
```

### 2.2 关系类型说明
- **一对多关系**: 父实体可以包含多个子实体
- **多对多关系**: 通过中间关系表建立关联
- **租户隔离**: 所有业务实体都与租户关联，实现数据隔离

## 3. 关键业务关系

### 3.1 数据源与任务关系
- `DsTaskRelation`: 数据源与任务的关联关系
- `relationType`: 关系类型 (source, target, lookup)
- 实现数据源与处理任务的多对多关联

### 3.2 数据源字段关系
- `DsFieldRelation`: 数据源字段间的关系
- `srcFieldName/dstFieldName`: 源字段和目标字段
- `relationType`: 关系类型 (mapping, compute, aggregate)
- `computeFunc`: 计算函数

### 3.3 数据传输实例关系
- `DatatubeInstanceDsRelation`: 数据管道与数据源关系
- `relationType`: 关系类型 (input, output, dimension)
- 定义数据在管道中的流向

### 3.4 视图模型关系
- `ViewModelDsRelation`: 视图模型与数据源关系
- `ViewModelTaskRelation`: 视图模型与任务关系
- 实现视图模型的数据来源和处理任务关联

## 4. 数据模型设计

### 4.1 主要数据表结构

```sql
-- 数据源表
CREATE TABLE datasource (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    ds_name VARCHAR(255) NOT NULL,
    ds_type VARCHAR(50) NOT NULL,
    db_name VARCHAR(255),
    table_name VARCHAR(255),
    object_type VARCHAR(50),
    pk_fields VARCHAR(500),
    predict_size BIGINT,
    qph INTEGER,
    tenant_id VARCHAR(50),
    meta LONGTEXT,
    INDEX idx_tenant_ds (tenant_id, ds_name)
);

-- 任务信息表
CREATE TABLE task_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    engine_type VARCHAR(50),
    app_name VARCHAR(255),
    external_id VARCHAR(255),
    tenant_id VARCHAR(50),
    meta LONGTEXT,
    dag LONGTEXT,
    INDEX idx_tenant_app (tenant_id, app_name)
);

-- 数据传输实例表
CREATE TABLE datatube_instance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    provider VARCHAR(255),
    app_name VARCHAR(255),
    type VARCHAR(50),
    tenant_id VARCHAR(50),
    INDEX idx_tenant_app (tenant_id, app_name)
);
```

### 4.2 索引策略
- **租户索引**: 所有表都建立 tenant_id 索引，支持多租户查询
- **复合索引**: tenant_id + 业务字段的复合索引，提高查询效率
- **外键索引**: 关联表的外键字段建立索引

## 5. 数据一致性设计

### 5.1 事务管理
- **本地事务**: 单表操作使用本地事务
- **分布式事务**: 跨服务操作使用分布式事务管理
- **最终一致性**: 异步处理场景保证最终一致性

### 5.2 数据校验
- **字段校验**: 必填字段、长度、格式校验
- **业务规则校验**: 实体间关系完整性校验
- **唯一性校验**: 租户内唯一性约束

### 5.3 数据版本管理
- **乐观锁**: 使用 gmt_modified 实现乐观锁
- **版本控制**: 关键配置实体支持版本管理
- **变更日志**: 记录实体变更历史

---

**文档版本**: v1.0  
**最后更新**: 2024年12月  
**维护者**: Qanat团队 