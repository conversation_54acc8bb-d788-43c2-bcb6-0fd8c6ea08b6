# Qanat-Aliyun-Inc-Com Blueprint - 外部依赖

## 1. 外部依赖概览

### 1.1 依赖架构图
```
┌─────────────────────────────────────────────────────────────────┐
│                        Qanat平台                                │
├─────────────────────────────────────────────────────────────────┤
│                        外部依赖层                                │
├─────────────────┬─────────────────┬─────────────────┬─────────────┤
│   HSF服务依赖   │   中间件依赖     │   计算引擎依赖   │  存储依赖   │
├─────────────────┼─────────────────┼─────────────────┼─────────────┤
│ • MDP服务       │ • MetaQ消息队列 │ • Blink流计算   │ • MySQL     │
│ • Tag服务       │ • Redis缓存     │ • Flink流计算   │ • PostgreSQL│
│ • BPMS服务      │ • Diamond配置   │ • ODPS批处理    │ • ADB3      │
│ • 认证中心      │ • SchedulerX2   │ • DataX同步     │ • MongoDB   │
│ • 北明RTDW      │ • HSF注册中心   │ • Jingwei3     │ • ElasticSrc│
│ • 表更新时间    │ • EagleEye链路  │                 │ • TDDL      │
│ • DFAAS服务     │ • Alimonitor监控│                 │ • OTS表格   │
└─────────────────┴─────────────────┴─────────────────┴─────────────┘
```

### 1.2 依赖分类统计
| 依赖类型 | 数量 | 关键程度 | 备注 |
|---------|------|----------|------|
| HSF服务 | 10+ | 高 | 业务核心依赖 |
| 中间件服务 | 8+ | 高 | 基础设施依赖 |
| 计算引擎 | 5+ | 高 | 数据处理核心 |
| 存储系统 | 7+ | 高 | 数据持久化 |
| 监控系统 | 4+ | 中 | 运维保障 |
| 第三方服务 | 3+ | 中 | 扩展功能 |

## 2. HSF服务依赖

### 2.1 MDP (Meta Data Platform) 服务
```java
@HSFConsumer(clientTimeout=30000)
private IQueryTagMetaService mdpService;

@HSFConsumer(clientTimeout=30000)
private IQueryObjectMetaService queryObjectMetaService;

@HSFConsumer(clientTimeout=30000)
private IOperateObjectMetaService operateObjectMetaService;

@HSFConsumer(clientTimeout=30000)
private IOperateTagMetaService operateTagMetaService;

@HSFConsumer(clientTimeout=30000)
private IOperateEnumService operateEnumService;
```

**功能说明**:
- **元数据查询**: 获取数据源的元数据信息
- **对象管理**: 创建和管理数据对象类型
- **标签管理**: 数据标签的增删改查操作
- **枚举管理**: 数据字典和枚举值管理

**配置信息**:
```properties
# MDP服务配置
mdp.api.domainCode=pmo_ext
mdp.api.domainAk=your_domain_ak
mdp.hsf.version=1.0.0.DAILY
```

### 2.2 BPMS (Business Process Management System) 服务
```xml
<!-- BPMS流程服务 -->
<bean id="processInstanceService" class="com.taobao.hsf.app.spring.util.HSFSpringConsumerBean">
    <property name="interfaceName">
        <value>com.alibaba.alipmc.api.ProcessInstanceService</value>
    </property>
    <property name="version">
        <value>${qanat.bpms.service.version}</value>
    </property>
</bean>

<bean id="processRecordService" class="com.taobao.hsf.app.spring.util.HSFSpringConsumerBean">
    <property name="interfaceName">
        <value>com.alibaba.alipmc.api.ProcessRecordService</value>
    </property>
    <property name="version">
        <value>${qanat.bpms.service.version}</value>
    </property>
</bean>
```

**功能说明**:
- **流程实例管理**: 启动、暂停、终止业务流程
- **流程记录查询**: 获取流程执行历史和状态
- **人工审批**: 支持复杂的业务审批流程

**配置信息**:
```properties
# BPMS配置
qanat.bpms.appKey=qanat-system
qanat.bpms.authKey=your_auth_key
qanat.bpms.service.version=3.0.7
```

### 2.3 认证中心服务
```java
// 通过HSF UDF调用认证中心
@HSFConsumer(clientTimeout=30000)
private IAuthCenterService authCenterService;
```

**功能说明**:
- **用户认证**: 验证用户身份和权限
- **访问控制**: 基于角色的访问控制(RBAC)
- **单点登录**: 与BUC系统集成的SSO

### 2.4 北明RTDW服务
```java
@HSFConsumer(clientTimeout=30000) 
private RtdwViewModelTaskService rtdwViewModelTaskService;
```

**功能说明**:
- **实时数仓**: 实时数据仓库服务
- **视图模型**: 数据视图模型管理
- **对象变更**: 数据对象变更通知

### 2.5 表更新时间服务
```java
@HSFConsumer(clientTimeout=30000)
private TableDataUpdateTimeService tableDataUpdateTimeService;
```

**功能说明**:
- **更新时间监控**: 监控表数据的最后更新时间
- **数据新鲜度**: 提供数据新鲜度指标
- **延迟监控**: 数据同步延迟监控

### 2.6 DFAAS (Data Function as a Service) 服务
```java
// 通过HSF UDF调用DFAAS
HSFApiConsumerBean consumerBean = new HSFApiConsumerBean();
consumerBean.setInterfaceName("com.alibaba.dt.oneness.common.hsf.OnenessHSFService");
consumerBean.setGeneric("true");
consumerBean.setGroup("HSF");
consumerBean.setVersion("devata_dfaas_1.0");
```

**功能说明**:
- **函数计算**: 提供数据处理函数服务
- **算法调用**: 机器学习算法服务
- **数据增强**: 数据质量和增强服务

### 2.7 标签服务
```java
// 标签服务HSF调用
HSFApiConsumerBean consumerBean = new HSFApiConsumerBean();
consumerBean.setInterfaceName("com.aliyun.tag.api.service.ITagService");
consumerBean.setGeneric("true");
consumerBean.setGroup("HSF");
consumerBean.setVersion("1.0.0");
```

**功能说明**:
- **数据打标**: 为数据对象添加标签
- **标签查询**: 根据标签检索数据
- **标签管理**: 标签的增删改查

## 3. 中间件依赖

### 3.1 MetaQ消息队列
```properties
# MetaQ配置
metaq.producer.group=qanat-producer
metaq.consumer.group=CID-qanat-mdp
metaq.topic.metadata.change=TOPIC_META_DATA_CHANGE
metaq.tag.default=TAG
```

**使用场景**:
- **元数据变更通知**: 监听MDP元数据变更事件
- **BPMS流程通知**: 接收BPMS流程状态变更
- **系统间异步通信**: 各模块间的异步消息传递

**代码示例**:
```java
@Component
public class MdpObjectFieldChangeListener {
    @PostConstruct
    private void init() {
        MetaPushConsumer consumer = new MetaPushConsumer("CID-qanat-mdp");
        consumer.subscribe("TOPIC_META_DATA_CHANGE", "TAG");
        consumer.setMessageListener(new MessageListenerConcurrently() {
            @Override
            public ConsumeConcurrentlyStatus consumeMessage(
                List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
                // 处理元数据变更消息
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
        });
        consumer.start();
    }
}
```

### 3.2 Redis缓存
```properties
# Redis配置
spring.redis.host=your-redis-host
spring.redis.port=6379
spring.redis.password=your-password
spring.redis.timeout=3000
spring.redis.jedis.pool.max-active=20
spring.redis.jedis.pool.max-idle=10
```

**使用场景**:
- **数据源配置缓存**: 缓存数据库连接配置
- **任务状态缓存**: 缓存任务执行状态
- **会话管理**: 用户会话信息存储
- **热点数据缓存**: 频繁访问的数据缓存

### 3.3 Diamond配置中心
```properties
# Diamond配置
diamond.client.appName=qanat-aliyun-inc-com
diamond.client.env=daily
diamond.client.serverAddr=addr-daily.diamond.alibaba.net
```

**功能说明**:
- **动态配置**: 运行时配置更新
- **环境隔离**: 不同环境的配置管理
- **配置版本**: 配置变更历史和回滚

### 3.4 SchedulerX2分布式任务调度
```properties
# SchedulerX2配置
spring.schedulerx2.endpoint=your-endpoint
spring.schedulerx2.namespace=your-namespace
spring.schedulerx2.group-id=qanat-group
spring.schedulerx2.app-name=qanat-aliyun-inc-com
```

**功能说明**:
- **DAG任务调度**: 复杂的DAG工作流调度
- **定时任务**: 周期性任务执行
- **分布式执行**: 支持集群任务分发
- **任务监控**: 任务执行状态监控

### 3.5 HSF服务框架
```properties
# HSF配置
spring.hsf.group=HSF
spring.hsf.version=1.0.0.DAILY
spring.hsf.timeout=20000
```

**功能说明**:
- **服务注册发现**: 自动服务注册和发现
- **负载均衡**: 多种负载均衡策略
- **服务治理**: 限流、熔断、降级
- **调用链监控**: 服务调用链追踪

### 3.6 EagleEye链路追踪
```properties
# EagleEye配置
spring.eagleeye.enabled=true
spring.eagleeye.mdc-updater=slf4j
```

**功能说明**:
- **分布式链路追踪**: 跟踪请求在微服务间的调用路径
- **性能监控**: 接口响应时间和调用频率统计
- **异常追踪**: 异常和错误的链路定位

### 3.7 Alimonitor监控
```properties
# Alimonitor配置
spring.alimonitor.method-patterns[0]=com.aliyun.wormhole.qanat.service.*
spring.alimonitor.method-patterns[1]=com.aliyun.wormhole.qanat.dal.*
spring.alimonitor.excluded-suffixes=gif,css,js,ico,do
```

**功能说明**:
- **业务监控**: 业务指标的实时监控
- **系统监控**: CPU、内存、网络等系统指标
- **告警通知**: 异常情况的告警和通知

## 4. 计算引擎依赖

### 4.1 Blink流计算引擎
```xml
<dependency>
    <groupId>org.apache.flink</groupId>
    <artifactId>flink-core</artifactId>
    <version>blink-2.0-SNAPSHOT</version>
</dependency>
<dependency>
    <groupId>org.apache.flink</groupId>
    <artifactId>flink-table_2.11</artifactId>
    <version>blink-2.0-SNAPSHOT</version>
</dependency>
```

**功能说明**:
- **流计算处理**: 实时数据流处理
- **SQL支持**: 支持标准SQL的流计算
- **状态管理**: 分布式状态管理
- **容错机制**: 自动故障恢复

**支持的连接器**:
- Kafka Source/Sink
- MySQL Scan Source
- ADB3 Sink
- ElasticSearch Sink
- MongoDB Sink
- PostgreSQL Sink
- TDDL Sink

### 4.2 Flink流计算引擎
```xml
<dependency>
    <groupId>com.aliyun</groupId>
    <artifactId>aliyun-java-sdk-ververica</artifactId>
    <version>1.0.8</version>
</dependency>
<dependency>
    <groupId>com.aliyun</groupId>
    <artifactId>ververica-common</artifactId>
    <version>1.0.25</version>
</dependency>
```

**功能说明**:
- **阿里云Flink**: 托管的Flink服务
- **弹性计算**: 自动扩缩容能力
- **SQL作业**: 支持Flink SQL作业
- **监控集成**: 与阿里云监控系统集成

### 4.3 ODPS (MaxCompute) 批处理
```xml
<dependency>
    <groupId>com.aliyun.odps</groupId>
    <artifactId>odps-sdk-core-internal</artifactId>
    <version>0.38.3</version>
</dependency>
```

**功能说明**:
- **大数据批处理**: PB级数据批处理能力
- **SQL引擎**: 标准SQL支持
- **作业调度**: 批作业的调度和管理
- **数据仓库**: 企业级数据仓库服务

### 4.4 DataX数据同步
**功能说明**:
- **异构数据源同步**: 支持多种数据源间同步
- **批量数据传输**: 高效的批量数据传输
- **数据转换**: 支持数据格式转换
- **增量同步**: 支持增量数据同步

### 4.5 Jingwei3实时同步
```java
// Jingwei3自定义同步
<dependency>
    <groupId>com.taobao.drc</groupId>
    <artifactId>client</artifactId>
    <version>********</version>
</dependency>
```

**功能说明**:
- **实时数据同步**: 毫秒级数据同步
- **增量同步**: 基于binlog的增量同步
- **多源同步**: 支持多数据源实时同步
- **高可用**: 支持集群高可用部署

## 5. 存储系统依赖

### 5.1 关系型数据库

#### MySQL数据库
```properties
# MySQL配置示例
spring.datasource.mysql.url=*******************************
spring.datasource.mysql.username=username
spring.datasource.mysql.password=password
spring.datasource.mysql.driver-class-name=com.mysql.jdbc.Driver
```

**使用场景**:
- **业务数据存储**: 核心业务数据
- **配置信息**: 系统配置和元数据
- **日志记录**: 业务操作日志

#### PostgreSQL数据库
```properties
# PostgreSQL配置示例
spring.datasource.postgresql.url=************************************
spring.datasource.postgresql.username=username
spring.datasource.postgresql.password=password
spring.datasource.postgresql.driver-class-name=org.postgresql.Driver
```

**使用场景**:
- **分析型数据**: 复杂查询和分析
- **地理空间数据**: 支持GIS数据类型
- **JSON数据**: 原生JSON数据支持

#### TDDL分布式数据库
```properties
# TDDL配置示例
tddl.appname=your-app-name
tddl.group=your-group
```

**功能说明**:
- **分库分表**: 自动分库分表
- **读写分离**: 主从读写分离
- **分布式事务**: 分布式事务支持
- **弹性扩容**: 在线扩容能力

### 5.2 NoSQL数据库

#### ADB3 (AnalyticDB)
```properties
# ADB3配置示例
adb3.url=*******************************
adb3.username=username
adb3.password=password
```

**功能说明**:
- **实时分析**: 实时OLAP分析
- **列式存储**: 高压缩比列式存储
- **并行计算**: 大规模并行处理
- **标准SQL**: 兼容MySQL协议

#### MongoDB文档数据库
```properties
# MongoDB配置示例
spring.data.mongodb.uri=********************************:port/database
spring.data.mongodb.database=metadata
```

**使用场景**:
- **文档存储**: 非结构化数据存储
- **元数据管理**: 灵活的元数据存储
- **缓存数据**: 半结构化缓存数据

#### ElasticSearch搜索引擎
```properties
# ElasticSearch配置示例
elasticsearch.cluster.name=your-cluster
elasticsearch.cluster.nodes=host1:port1,host2:port2
```

**使用场景**:
- **全文搜索**: 文本搜索和分析
- **日志分析**: 日志聚合和分析
- **实时查询**: 近实时数据查询

#### OTS (Table Store) 表格存储
```yaml
# OTS连接器配置
connector-meta-ots.yaml:
  name: "ots-connector"
  type: "ots"
  version: "1.0"
```

**功能说明**:
- **NoSQL存储**: 海量结构化数据存储
- **自动扩展**: 按需自动扩展
- **多模型**: 支持宽表和时序数据
- **强一致性**: 分布式强一致性

## 6. 第三方服务依赖

### 6.1 阿里云服务

#### OSS对象存储
```xml
<dependency>
    <groupId>com.aliyun.oss</groupId>
    <artifactId>aliyun-sdk-oss</artifactId>
    <version>2.8.2</version>
</dependency>
```

**使用场景**:
- **文件存储**: 大文件和备份文件存储
- **数据归档**: 历史数据归档
- **静态资源**: 静态资源托管

#### DataWorks数据工厂
```xml
<dependency>
    <groupId>com.aliyun.dataworks</groupId>
    <artifactId>aliyun-java-sdk-dataworks</artifactId>
    <version>1.1.6</version>
</dependency>
```

**功能说明**:
- **数据集成**: 数据源集成和同步
- **数据开发**: 可视化数据开发
- **调度运维**: 数据作业调度和运维

### 6.2 安全认证服务

#### BUC (Business User Center)
```properties
# BUC配置
spring.buc.client-key=6940e739-9797-43ff-8da5-c993b82e45ee
spring.buc.app-code=e7919b83e2ac4e3bb9bfb7f608231759
spring.buc.filter.url-patterns=/*
spring.buc.sso-server-url=https://login-test.alibaba-inc.com
spring.buc.loginEnv=daily
```

**功能说明**:
- **用户管理**: 企业用户管理
- **单点登录**: SSO认证服务
- **权限管理**: 基于角色的权限控制

#### ACL访问控制
```properties
# ACL配置
spring.acl.access-key=BearAdvice2-&668Y8@8&AI@aQfmAn
```

**功能说明**:
- **访问控制**: 细粒度访问控制
- **权限验证**: API级别权限验证
- **安全审计**: 访问行为审计

### 6.3 运维监控服务

#### Ateye监控
```xml
<dependency>
    <groupId>com.taobao.ateye</groupId>
    <artifactId>ateye-client</artifactId>
    <version>2.2.11</version>
</dependency>
```

**功能说明**:
- **业务监控**: 业务指标监控
- **异常检测**: 自动异常检测
- **智能告警**: 智能告警和诊断

#### Sentinel流量控制
```xml
<dependency>
    <groupId>com.taobao.csp</groupId>
    <artifactId>sentinel</artifactId>
    <version>2.16.0</version>
</dependency>
```

**功能说明**:
- **流量控制**: 限流和流量整形
- **熔断降级**: 服务熔断和降级
- **系统保护**: 系统负载保护

## 7. 依赖版本管理

### 7.1 核心依赖版本
```xml
<properties>
    <spring-boot.version>1.5.20.RELEASE</spring-boot.version>
    <pandora-boot.version>2020-04-release</pandora-boot.version>
    <schedulerx2.starter.version>1.1.4</schedulerx2.starter.version>
    <fastjson.version>1.2.82</fastjson.version>
    <security.version>1.0.11</security.version>
</properties>
```

### 7.2 版本兼容性矩阵
| 组件 | 当前版本 | 兼容版本 | 升级计划 |
|------|----------|----------|----------|
| Spring Boot | 1.5.20.RELEASE | 1.5.x | 2.x计划中 |
| Pandora Boot | 2020-04-release | 2020-xx | 跟随阿里云 |
| Flink | blink-2.0-SNAPSHOT | 1.x/2.x | 关注社区版本 |
| SchedulerX2 | 1.1.4 | 1.1.x | 按需升级 |
| FastJSON | 1.2.82 | 1.2.x | 安全版本优先 |

## 8. 依赖风险评估

### 8.1 高风险依赖
| 依赖组件 | 风险等级 | 风险描述 | 缓解措施 |
|---------|----------|----------|----------|
| HSF服务 | 高 | 服务不可用影响业务 | 服务降级+重试机制 |
| Blink引擎 | 高 | 流计算任务失败 | 多集群+任务重启 |
| MetaQ | 中 | 消息丢失或延迟 | 消息持久化+监控 |
| Redis | 中 | 缓存失效影响性能 | 缓存兜底+集群部署 |

### 8.2 依赖监控策略
- **健康检查**: 定期检查外部依赖健康状态
- **熔断机制**: 依赖不可用时的快速失败
- **监控告警**: 依赖异常的实时告警
- **降级方案**: 关键依赖的降级备用方案

### 8.3 业务连续性保障
- **多活部署**: 关键依赖的多地域部署
- **数据备份**: 重要数据的定期备份
- **故障演练**: 定期进行故障演练
- **应急预案**: 完善的应急响应预案

---

**文档版本**: v1.0  
**创建时间**: 2024-01-XX  
**维护团队**: Qanat平台运维组 