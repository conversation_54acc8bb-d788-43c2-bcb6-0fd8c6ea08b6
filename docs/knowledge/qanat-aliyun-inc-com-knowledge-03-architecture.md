# Qanat-Aliyun-Inc-Com Blueprint - 工程架构

## 1. 整体架构设计

### 1.1 系统架构概览
```
┌─────────────────────────────────────────────────────────────────┐
│                        Qanat数据处理平台                          │
├─────────────────────────────────────────────────────────────────┤
│                          前端层                                  │
│  • Web Console  • API Gateway  • 管理控制台                     │
├─────────────────────────────────────────────────────────────────┤
│                         应用服务层                                │
│  • REST API服务  • HSF服务  • 定时任务服务                      │
├─────────────────────────────────────────────────────────────────┤
│                         业务逻辑层                                │
│  • 数据源管理  • 任务调度  • 视图模型  • 数据管道                 │
├─────────────────────────────────────────────────────────────────┤
│                         数据处理层                                │
│  • 流计算引擎  • 批处理引擎  • UDF/UDTF  • 连接器                │
├─────────────────────────────────────────────────────────────────┤
│                         数据访问层                                │
│  • MyBatis ORM  • 数据源连接池  • 缓存层                        │
├─────────────────────────────────────────────────────────────────┤
│                         基础设施层                                │
│  • 中间件集成  • 监控体系  • 配置管理  • 安全认证                 │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 核心架构原则
- **微服务架构**: 基于HSF的服务化架构
- **分层设计**: 清晰的分层结构，职责分离
- **插件化设计**: 支持多种连接器和UDF扩展
- **流批一体**: 统一的流计算和批处理架构
- **云原生**: 支持容器化部署和弹性扩缩容

## 2. 分层架构设计

### 2.1 Web层 (Presentation Layer)
```
qanat-aliyun-inc-com-start
├── Controller层
│   ├── DataTubeController.java       # 主要业务API
│   ├── MainController.java           # 系统基础功能
│   ├── BucController.java            # 用户认证
│   └── JsonpController.java          # JSONP支持
├── 配置层
│   ├── application.properties        # 主配置文件
│   ├── logback-spring.xml           # 日志配置
│   └── velocity/                    # 页面模板
└── 安全层
    ├── BUC认证集成
    ├── ACL权限控制
    └── 安全过滤器链
```

**技术特点**：
- **Spring Boot框架**: 提供自动配置和快速开发能力
- **RESTful API**: 标准的REST接口设计
- **统一异常处理**: 全局异常拦截和处理
- **安全认证**: BUC + ACL双重安全保障

### 2.2 服务层 (Service Layer)
```
qanat-aliyun-inc-com-service
├── HSF服务提供者
│   ├── DatasourceServiceImpl        # 数据源服务
│   ├── TaskServiceImpl              # 任务管理服务
│   ├── BlinkServiceImpl             # 流计算服务
│   └── DatatubeManagementServiceImpl # 数据管道服务
├── 业务处理器
│   ├── job/                         # 任务处理器
│   ├── process/                     # 业务流程处理器
│   └── service/                     # 核心业务服务
└── 工具类
    ├── adapter/                     # 适配器模式
    ├── util/                        # 工具类
    └── handler/                     # 处理器
```

**核心服务功能**：
- **数据源服务**: 支持10+种数据库的连接管理
- **任务调度服务**: 基于SchedulerX2的分布式任务调度
- **流计算服务**: Flink/Blink流计算任务管理
- **数据管道服务**: 端到端数据处理管道

### 2.3 数据访问层 (Data Access Layer)
```
qanat-service-dao
├── 实体类 (Domain)
│   ├── Datasource.java              # 数据源实体
│   ├── TaskInfo.java                # 任务信息实体
│   ├── DatatubeInstance.java        # 数据管道实例
│   └── ViewModelInfo.java           # 视图模型实体
├── 映射接口 (Mapper)
│   ├── DatasourceMapper.java        # 数据源映射
│   ├── TaskInfoMapper.java          # 任务映射
│   └── DatatubeInstanceMapper.java  # 管道映射
└── SQL映射文件
    ├── DatasourceMapper.xml
    ├── TaskInfoMapper.xml
    └── DatatubeInstanceMapper.xml
```

**数据访问特点**：
- **MyBatis ORM**: 灵活的SQL映射框架
- **连接池管理**: HikariCP高性能连接池
- **读写分离**: 支持主从数据库配置
- **事务管理**: Spring声明式事务

### 2.4 API接口层 (API Layer)
```
qanat-service-api
├── 服务接口
│   ├── DatasourceService.java       # 数据源服务接口
│   ├── TaskService.java             # 任务服务接口
│   └── BlinkService.java            # 流计算服务接口
├── DTO对象
│   ├── DataResult<T>.java           # 统一响应对象
│   ├── DatasourceRequest.java       # 数据源请求对象
│   └── TaskInfoRequest.java         # 任务请求对象
└── 常量定义
    ├── QanatBizException.java       # 业务异常
    └── 枚举类定义
```

## 3. 模块架构设计

### 3.1 核心模块架构
```
┌─────────────────────────────────────────────────────────────────┐
│                         核心平台模块                              │
├─────────────────────┬───────────────────┬─────────────────────────┤
│    基础服务模块     │    连接器模块     │      UDF/UDTF模块       │
├─────────────────────┼───────────────────┼─────────────────────────┤
│ • 启动模块         │ • Blink连接器     │ • Blink UDF            │
│ • 服务模块         │ • Flink连接器     │ • Flink UDF            │
│ • API模块          │ • ADB3连接器      │ • HSF UDF              │
│ • DAO模块          │ • ES连接器        │ • ODPS UDF             │
│ • OpenAPI模块      │ • Kafka连接器     │ • UDTF模块             │
│ • 数据源模块       │ • MongoDB连接器   │                         │
│                     │ • PostgreSQL连接器│                         │
│                     │ • MySQL连接器     │                         │
│                     │ • HTTP连接器      │                         │
│                     │ • TDDL连接器      │                         │
└─────────────────────┴───────────────────┴─────────────────────────┘
```

### 3.2 Blink/Flink连接器架构
```
连接器生态系统
├── Source连接器 (数据输入)
│   ├── qanat-blink-connector-kafka010      # Kafka 0.10连接器
│   ├── qanat-blink-connector-mysqlscan     # MySQL扫描连接器
│   ├── qanat-blink-connector-tddlscan      # TDDL扫描连接器
│   ├── qanat-blink-connector-drc           # DRC连接器
│   └── qanat-blink-connector-http          # HTTP连接器
├── Sink连接器 (数据输出)
│   ├── qanat-blink-connector-adb3          # ADB3连接器
│   ├── qanat-blink-connector-elasticsearch # ElasticSearch连接器
│   ├── qanat-blink-connector-mongodb       # MongoDB连接器
│   ├── qanat-blink-connector-rds-pg        # PostgreSQL连接器
│   ├── qanat-blink-connector-tddl          # TDDL连接器
│   └── qanat-blink-custom-sink             # 自定义Sink
└── CDP连接器
    ├── qanat-cdp-domain-odps               # ODPS域连接器
    └── qanat-blink-cdp-sink                # CDP Sink连接器
```

### 3.3 UDF/UDTF架构
```
函数扩展生态
├── 标量函数 (UDF)
│   ├── qanat-blink-udf                     # Blink标量函数
│   ├── qanat-flink-udf                     # Flink标量函数
│   ├── qanat-blink-hsf-udf                 # HSF调用函数
│   └── qanat-odps-udf                      # ODPS函数
├── 表函数 (UDTF)
│   └── qanat-blink-udtf                    # Blink表函数
└── 特殊模块
    ├── qanat-drc-event                     # DRC事件处理
    ├── qanat-stream-event                  # 流事件处理
    └── qanat-jingwei3-custom               # 精卫3定制
```

## 4. 微服务架构

### 4.1 HSF服务架构
```
HSF服务提供者                    HSF服务消费者
┌─────────────────────┐         ┌─────────────────────┐
│ @HSFProvider        │         │ @HSFConsumer        │
├─────────────────────┤         ├─────────────────────┤
│ • DatasourceService │◄────────┤ • MDP服务           │
│ • TaskService       │         │ • Tag服务           │
│ • BlinkService      │         │ • BPMS服务          │
│ • ElasticSearchBCP  │         │ • 认证中心服务      │
│ • MdpCustomView     │         │ • 北明RTDW服务      │
│ • QanatSqlExecute   │         │ • 表更新时间服务    │
└─────────────────────┘         └─────────────────────┘
```

### 4.2 服务间通信
- **同步调用**: HSF RPC调用，支持负载均衡和服务发现
- **异步消息**: MetaQ消息队列，实现事件驱动架构
- **配置中心**: Diamond配置管理，支持动态配置更新
- **服务注册**: ConfigServer服务注册与发现

### 4.3 服务治理
- **服务降级**: HSF超时和重试机制
- **流量控制**: 限流和熔断保护
- **监控告警**: 服务调用链监控
- **版本管理**: 服务版本控制和灰度发布

## 5. 数据处理架构

### 5.1 流批一体架构
```
┌─────────────────────────────────────────────────────────────────┐
│                         统一计算引擎                              │
├─────────────────────┬───────────────────┬─────────────────────────┤
│      流计算层       │      批处理层     │       混合处理层        │
├─────────────────────┼───────────────────┼─────────────────────────┤
│ • Blink流计算       │ • DataX批同步     │ • Lambda架构            │
│ • Flink流计算       │ • ODPS批处理      │ • Kappa架构             │
│ • 实时数据管道      │ • 离线数据仓库    │ • 流批融合              │
└─────────────────────┴───────────────────┴─────────────────────────┘
```

### 5.2 数据处理流程
```
数据源 ──► 数据采集 ──► 数据处理 ──► 数据存储 ──► 数据应用
   │          │          │          │          │
   │          │          │          │          └── • BI报表
   │          │          │          │             • 数据API
   │          │          │          │             • 实时大屏
   │          │          │          │
   │          │          │          └── • ADB数据库
   │          │          │             • ElasticSearch
   │          │          │             • MongoDB
   │          │          │
   │          │          └── • 数据清洗
   │          │             • 数据转换
   │          │             • 数据聚合
   │          │
   │          └── • Kafka消息队列
   │             • DRC实时同步
   │             • 全量/增量扫描
   │
   └── • MySQL数据库
      • PostgreSQL数据库
      • TDDL分布式数据库
      • HTTP接口数据
```

## 6. 部署架构

### 6.1 容器化部署
```
┌─────────────────────────────────────────────────────────────────┐
│                         阿里云容器服务                           │
├─────────────────────────────────────────────────────────────────┤
│ Pod 1: Web服务         │ Pod 2: 任务调度        │ Pod 3: 流计算    │
│ • Tomcat              │ • SchedulerX Worker    │ • Blink集群      │
│ • Spring Boot应用     │ • 任务执行器           │ • Flink集群      │
│ • 负载均衡            │ • 监控代理             │ • 资源管理       │
├─────────────────────────────────────────────────────────────────┤
│                         共享存储和服务                           │
│ • Redis缓存集群        • MySQL数据库集群       • 配置中心        │
│ • MetaQ消息队列        • ADB分析数据库         • 监控系统        │
└─────────────────────────────────────────────────────────────────┘
```

### 6.2 多环境部署
- **Daily环境**: 日常开发测试环境
- **Pre环境**: 预生产验证环境  
- **Production环境**: 生产环境
- **Production-SG环境**: 新加坡生产环境

### 6.3 弹性扩缩容
- **水平扩容**: 基于CPU/内存使用率自动扩容
- **垂直扩容**: 根据业务负载调整资源配置
- **定时扩容**: 支持定时任务高峰期扩容
- **手动扩容**: 紧急情况下的手动干预

## 7. 架构优势与特点

### 7.1 技术优势
- **高可用性**: 99.9%以上的服务可用性
- **高性能**: 支持万级QPS的并发处理
- **高扩展性**: 插件化的连接器和UDF扩展
- **易维护性**: 清晰的分层架构和模块化设计

### 7.2 业务特点
- **多数据源支持**: 统一接入10+种数据源
- **实时处理能力**: 毫秒级的流计算响应
- **批流一体**: 统一的开发和运维体验
- **企业级特性**: 完整的监控、告警、权限体系

### 7.3 发展方向
- **云原生**: 全面容器化和Serverless化
- **智能化**: AI驱动的自动调优和故障诊断
- **标准化**: 行业标准的数据处理规范
- **生态化**: 丰富的连接器和函数生态

---

**文档版本**: v1.0  
**创建时间**: 2024-01-XX  
**维护团队**: Qanat平台架构组 