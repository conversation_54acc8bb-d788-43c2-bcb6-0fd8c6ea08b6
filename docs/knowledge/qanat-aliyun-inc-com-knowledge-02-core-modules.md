# Qanat-Aliyun-Inc-Com Blueprint - 核心功能模块

## 1. 基础服务模块

### 1.1 启动模块 (qanat-aliyun-inc-com-start)
- **模块功能**: 应用启动和配置管理
- **技术实现**: Spring Boot启动器
- **核心组件**:
  - `DataTubeController`: 主要的REST API控制器
  - `MainController`: 系统基础功能控制器  
  - `BucController`: BUC用户认证控制器
- **主要职责**:
  - 应用启动入口
  - Web层路由配置
  - 统一异常处理
  - 健康检查接口
- **配置管理**:
  - `application.properties`: 主配置文件
  - 多环境配置支持
  - Diamond配置中心集成

### 1.2 核心服务模块 (qanat-aliyun-inc-com-service)
- **模块功能**: 核心业务逻辑和服务实现
- **技术实现**: Spring服务层 + HSF服务提供者
- **包结构**:
  - `service.base`: 基础数据服务
  - `service.blink`: Blink流计算服务
  - `service.datax`: DataX数据同步服务
  - `service.openapi`: 开放API服务实现
  - `job`: DAG调度任务处理器包
- **核心服务类**:
  - `DatasourceServiceImpl`: 数据源管理服务
  - `TaskServiceImpl`: 任务管理服务
  - `BlinkServiceImpl`: Blink流计算服务
  - `DatatubeManagementServiceImpl`: 数据管道管理服务
- **DAG调度入口**:
  - `QanatDagJobProcessor`: DAG工作流调度主入口，基于SchedulerX2实现

### 1.3 API接口模块 (qanat-service-api)
- **模块功能**: 服务接口定义和契约
- **技术实现**: Java接口定义
- **主要接口**:
  - `DatasourceService`: 数据源服务接口
  - `TaskService`: 任务服务接口
  - `BlinkService`: 流计算服务接口
  - `SyncDataService`: 数据同步服务接口
- **DTO对象**:
  - `DataResult<T>`: 统一响应结果
  - `DatasourceRequest`: 数据源请求对象
  - `TaskInfoRequest`: 任务信息请求对象

### 1.4 数据访问层模块 (qanat-service-dao)
- **模块功能**: 数据库访问和ORM映射
- **技术实现**: MyBatis ORM框架
- **主要组件**:
  - `domain`: 数据实体类
  - `mapper`: MyBatis映射接口
  - `mapper.xml`: SQL映射文件
- **核心实体**:
  - `Datasource`: 数据源实体
  - `TaskInfo`: 任务信息实体
  - `DatatubeInstance`: 数据管道实例实体

### 1.5 开放API模块 (qanat-openapi)
- **模块功能**: 对外开放的REST API服务
- **技术实现**: HSF服务接口定义
- **主要服务**:
  - `MdpCustomViewService`: MDP自定义视图服务
  - `QanatSqlExecuteService`: SQL执行服务
- **响应格式**: `ApiResult<T>` 统一响应结构

## 2. Flink/Blink 连接器模块组

### 2.1 数据库连接器

#### ADB3连接器 (qanat-blink-connector-adb3)
- **功能**: 连接AnalyticDB 3.0数据库
- **技术特点**: 支持实时写入和批量读取
- **适用场景**: 大数据分析和OLAP查询

#### PostgreSQL连接器 (qanat-blink-connector-rds-pg)
- **功能**: 连接RDS PostgreSQL数据库
- **技术特点**: 支持CDC变更数据捕获
- **适用场景**: 关系型数据实时同步

#### TDDL连接器 (qanat-blink-connector-tddl)
- **功能**: 连接阿里TDDL分布式数据库
- **技术特点**: 支持分库分表数据访问
- **适用场景**: 大规模分布式数据处理

#### MySQL扫描连接器 (qanat-blink-connector-mysqlscan)
- **功能**: MySQL数据库全表扫描连接器
- **技术特点**: 优化的批量数据读取
- **适用场景**: 历史数据迁移和全量同步

### 2.2 NoSQL连接器

#### MongoDB连接器 (qanat-blink-connector-mongodb)
- **功能**: 连接MongoDB数据库
- **技术特点**: 支持文档数据的流式处理
- **适用场景**: 非结构化数据处理

#### ElasticSearch连接器 (qanat-blink-connector-elasticsearch)
- **功能**: 连接ElasticSearch搜索引擎
- **技术特点**: 支持索引数据的实时写入
- **适用场景**: 搜索数据的实时更新

### 2.3 消息队列连接器

#### Kafka连接器 (qanat-blink-connector-kafka010)
- **功能**: 连接Kafka 0.10版本消息队列
- **技术特点**: 支持exactly-once语义
- **适用场景**: 流数据的可靠传输

### 2.4 其他连接器

#### HTTP连接器 (qanat-blink-connector-http)
- **功能**: HTTP数据源连接器
- **技术特点**: 支持RESTful API数据接入
- **适用场景**: 外部API数据集成

#### DRC连接器 (qanat-blink-connector-drc)
- **功能**: 数据实时同步连接器
- **技术特点**: 基于binlog的CDC同步
- **适用场景**: 数据库实时同步

### 2.5. UDF/UDTF 模块组

#### 2.5.1 Blink用户自定义函数

##### Blink UDF (qanat-blink-udf)
- **功能**: Blink流计算用户自定义函数
- **技术实现**: Java UDF开发框架
- **函数类型**: 标量函数、聚合函数
- **应用场景**: 数据清洗、格式转换、业务计算

##### Blink UDTF (qanat-blink-udtf)
- **功能**: Blink流计算用户自定义表函数
- **技术实现**: Java UDTF开发框架
- **函数类型**: 一进多出的表函数
- **应用场景**: 数据拆分、维表关联

#### 2.5.2 集成服务UDF

##### HSF UDF (qanat-blink-hsf-udf)
- **功能**: 集成HSF服务的用户自定义函数
- **技术实现**: HSF服务调用封装
- **应用场景**: 流计算中调用外部服务
- **性能优化**: 连接池和缓存机制

#### 2.5.3 平台UDF

##### Flink UDF (qanat-flink-udf)
- **功能**: Flink流计算用户自定义函数
- **技术实现**: Flink UDF接口实现
- **兼容性**: 支持Flink多版本

##### ODPS UDF (qanat-odps-udf)
- **功能**: ODPS大数据平台用户自定义函数
- **技术实现**: ODPS UDF开发规范
- **应用场景**: 离线批处理计算

### 2.6. 数据处理模块组

#### 2.6.1 域模型处理

##### CDP域模型 (qanat-cdp-domain-odps)
- **功能**: 客户数据平台域模型处理
- **技术实现**: ODPS数据处理框架
- **应用场景**: 客户数据建模和分析
- **数据流向**: 从多数据源到统一域模型

#### 2.6.2 数据输出连接器

##### 自定义Sink (qanat-blink-custom-sink)
- **功能**: 自定义数据输出连接器
- **技术实现**: Blink Sink接口实现
- **扩展性**: 支持多种输出格式
- **应用场景**: 特殊业务需求的数据输出

##### CDP Sink (qanat-blink-cdp-sink)
- **功能**: CDP数据平台输出连接器
- **技术实现**: 专用的CDP数据格式
- **应用场景**: 数据输出到CDP平台

#### 2.6.3 事件处理模块

##### DRC事件 (qanat-drc-event)
- **功能**: 数据实时同步事件处理
- **技术实现**: 事件驱动架构
- **事件类型**: 数据变更事件、同步状态事件
- **应用场景**: 实时数据同步监控

##### 流事件 (qanat-stream-event)
- **功能**: 流数据事件处理
- **技术实现**: 流式事件处理框架
- **事件类型**: 业务事件、系统事件
- **应用场景**: 实时业务监控和告警

## 3. DAG调度模块组

### 3.1 DAG调度核心架构

#### QanatDagJobProcessor - DAG调度主入口
- **功能**: 基于DAG的工作流调度引擎
- **技术实现**: 继承SchedulerX2的MapJobProcessor
- **核心职责**:
  - DAG任务解析和验证
  - 节点依赖关系分析
  - 子任务分发和状态管理
  - 任务实例生命周期管理
- **调度流程**:
  ```
  DAG解析 -> 头节点识别 -> 依赖检查 -> 节点分发 -> 状态监控 -> 完成确认
  ```

### 3.2 节点处理器分类

#### 3.2.1 流计算处理器组
| 处理器类 | 功能描述 | 适用场景 |
|----------|----------|----------|
| **QanatBlinkJobProcessor** | Blink流计算任务处理 | 实时流处理、批处理任务 |
| **QanatFlinkJobProcessor** | Flink流计算任务处理 | Flink实时流处理 |
| **QanatBlinkBatchJobProcessor** | Blink批处理任务 | 大数据批量计算 |
| **QanatBlinkBatchV3JobProcessor** | Blink批处理V3版本 | 优化版批处理引擎 |
| **QanatFlinkBatchJobProcessor** | Flink批处理任务 | Flink批量计算 |

#### 3.2.2 数据同步处理器组
| 处理器类 | 功能描述 | 适用场景 |
|----------|----------|----------|
| **QanatDataXJobProcessor** | DataX数据同步任务 | 异构数据源同步 |
| **QanatDataXV2JobProcessor** | DataX V2版本同步 | 增强版数据同步 |
| **QanatMultiDbSinkJobProcessor** | 多数据库写入任务 | 数据分发到多DB |

#### 3.2.3 大数据平台处理器组
| 处理器类 | 功能描述 | 适用场景 |
|----------|----------|----------|
| **QanatOdpsJobProcessor** | ODPS批处理任务 | MaxCompute离线计算 |
| **QanatOdpsSqlJobProcessor** | ODPS SQL执行 | ODPS查询分析 |
| **QanatHoloExternalTableJobProcessor** | Hologres外表处理 | 实时数仓外表管理 |

#### 3.2.4 数据库操作处理器组
| 处理器类 | 功能描述 | 适用场景 |
|----------|----------|----------|
| **QanatAdb3SqlJobProcessor** | ADB3 SQL执行 | 分析型数据库操作 |
| **QanatAdb3MultiSqlJobProcessor** | ADB3多SQL批量执行 | 批量SQL操作 |
| **QanatAdb3ExternalTableJobProcessor** | ADB3外表管理 | 外部数据源接入 |

#### 3.2.5 搜索引擎处理器组
| 处理器类 | 功能描述 | 适用场景 |
|----------|----------|----------|
| **QanatEsIndexCloneJobProcessor** | ES索引克隆 | 索引备份复制 |
| **QanatEsIndexSwitchJobProcessor** | ES索引切换 | 索引热切换 |

#### 3.2.6 流计算控制处理器组
| 处理器类 | 功能描述 | 适用场景 |
|----------|----------|----------|
| **QanatStartFlinksJobProcessor** | 启动Flink任务 | 流任务启动控制 |
| **QanatStopBlinkJobProcessor** | 停止Blink任务 | 流任务停止控制 |
| **QanatStopFlinksJobProcessor** | 停止Flink任务 | 流任务停止控制 |
| **QanatRestartBlinkJobProcessor** | 重启Blink任务 | 流任务重启控制 |

#### 3.2.7 业务流程处理器组
| 处理器类 | 功能描述 | 适用场景 |
|----------|----------|----------|
| **QanatBpmsJobProcessor** | BPMS工作流任务 | 人工审核流程 |
| **QanatViewModelJobProcessor** | 视图模型处理 | 数据视图构建 |
| **QanatViewModelFullSyncJobProcessor** | 视图模型全量同步 | 全量数据同步 |
| **QanatViewModelIncrSyncJobProcessor** | 视图模型增量同步 | 增量数据同步 |
| **QanatMetricPubJobProcessor** | 指标发布任务 | 数据指标发布 |

#### 3.2.8 工具类处理器组
| 处理器类 | 功能描述 | 适用场景 |
|----------|----------|----------|
| **QanatWaitJobProcessor** | 等待节点 | 任务间等待控制 |
| **QanatDryRunJobProcessor** | 干跑测试 | 任务预执行验证 |
| **QanatCreateOdsDsInfoJobProcessor** | ODS数据源创建 | 数据源自动创建 |
| **AbstractQanatNodeJobProcessor** | 抽象基类 | 节点处理器基础框架 |

### 3.3 DAG执行机制

#### 3.3.1 节点依赖处理
```java
// 依赖检查核心逻辑
while (nodeMap.size() > 0) {
    boolean hasFailed = false;
    for(Node nd : dag.getNodeList()) {
        Node node = nodeMap.get(nd.getId());
        if (node == null) continue;
        
        List<String> prevList = node.getPrevNodeList();
        if (taskService.checkIfPrevFailed(taskInstId, prevList)) {
            hasFailed = true; // 前置节点失败，停止执行
            break;
        }
        if (taskService.checkIfPrevReady(taskInstId, prevList)) {
            // 前置节点就绪，分发当前节点
            dispatchNode(node, instParamsMap);
        }
    }
    if (hasFailed) break;
}
```

#### 3.3.2 任务状态管理
- **任务实例状态**: EXECUTING、SUCCESS、FAILED
- **状态转换**: 任务启动 -> 执行中 -> 成功/失败
- **状态监控**: 定时检查任务执行状态，超时处理
- **失败处理**: 节点失败时终止整个DAG执行

#### 3.3.3 参数传递机制
```java
// 节点参数构建
Map<String, Object> mapData = new HashMap<>();
mapData.put("requestId", requestId);
mapData.put("operator", operator);
mapData.put("taskId", taskId);
mapData.put("taskInstId", taskInstId);
mapData.put("node", node);
mapData.put("tenantId", task.getTenantId());
mapData.put("appName", task.getAppName());
```

### 3.4 节点处理器架构模式

#### 3.4.1 抽象基类模式
```java
public abstract class AbstractQanatNodeJobProcessor<T extends Node> extends JavaProcessor {
    // 通用的节点处理逻辑
    @Override
    public ProcessResult process(JobContext context) {
        // 1. 参数解析和验证
        // 2. 任务状态更新为执行中
        // 3. 调用具体实现的doProcess方法
        // 4. 任务状态更新为成功/失败
    }
    
    // 子类需要实现的具体处理逻辑
    abstract void doProcess(Map<String, Object> instParamsMap, T node);
}
```

#### 3.4.2 节点类型映射
| 节点类型 | 对应处理器 | 执行模式 |
|----------|------------|----------|
| BlinkNode | QanatBlinkJobProcessor | 流/批处理 |
| DataXNode | QanatDataXJobProcessor | 批处理 |
| BpmsNode | QanatBpmsJobProcessor | 异步等待 |
| WaitNode | QanatWaitJobProcessor | 同步等待 |
| OdpsNode | QanatOdpsJobProcessor | 批处理 |

## 4. 模块间协作关系

### 4.1 数据流向
```
数据源 -> 连接器 -> 流计算引擎 -> UDF处理 -> 输出连接器 -> 目标系统
```

### 4.2 DAG调度流向
```
DAG定义 -> QanatDagJobProcessor -> 节点处理器 -> 任务执行 -> 状态回调 -> 下游节点触发
```

### 4.3 服务调用关系
```
Controller -> Service -> DAO -> Database
            -> HSF Service -> External System
            -> OpenAPI -> External Consumer
            -> SchedulerX2 -> JobProcessor -> External Service
```

### 4.4 配置管理
- **统一配置**: 通过Diamond配置中心管理
- **环境隔离**: 不同环境使用不同配置
- **动态更新**: 支持配置的动态更新

## 5. 管道实例管理

### 5.1 管道实例定义
完成源库（mysql、ODPS、北冥自定义对象）数据到目标库（ADB3）的全增量数据同步/ETL链路构建
- 管道类型
  - ods: ODS类型，构建ODS层数据，完成源库到数仓数仓ADB3的table to table的数据同步
  - viewmodel: 视图模型，构建DWD宽表数据，基于yaml视图模型定义，完成多个源库多张表到数仓ADB3的宽表数据构建
  - dag：DAG类型，与ods、viewmodel不同，没有固定的模型定义，是基于DAG任务调度编排的自定义数据ETL链路构建，通常用于解决标准ODS、viewmodel无法描述的复杂ETL任务的构建

- 管道服务入口

| 方法 | 处理类 | 描述 |
|----------|------------|----------|
| create | DatatubeServiceImpl | 创建管道实例 |
| modify | DatatubeServiceImpl | 变更管道实例 |


### 5.2 视图模型
- 介绍
基于yaml描述的对象关系，实现多张ods表join后映射到一张目标宽表的模型，以及基于次模型会产出全增量数据构建的任务，与任务调度DAG图，并基于视图模型完成目标宽表全增量数据构建的管理

- 视图模型定义，参看ViewModel类定义
  - code: 视图模型名称，通常也是目标宽表表名
  - name: 宽表描述
  - dynamic: 默认false， 仅在主object为北冥自定义对象时为true，代表视图模型接受动态字段增删改
  - object: 必填，宽表主object，定义了宽表的数据粒度和所属业务领域
    - code: 对象别名，视图模型定义内部唯一，会应用到blink任务名称中方便辨识
    - ref: 对象所指向的数据源名称ds_name，定义在datasource表
    - type: 对象类型，支持类型如下：
      - table: 表，代表是一张数据库中的表
      - obj: 北冥自定义对象
      - component: 组件，代表是一段自定义SQL，可能是一张或多张表join的复杂逻辑，类似数据库视图，组件的定义在extension表
    - fields: 字段映射定义
  - relatedObjects: 非必填，对象数组，除主对象外，所有其他关联的对象都在此定义
    - code: 对象别名，视图模型定义内部唯一，会应用到blink任务名称中方便辨识
    - ref: 对象所指向的数据源名称ds_name(定义在datasource表)
    - type: 对象类型，支持类型如下：
      - table: 表，代表是一张确定的表
      - component: 组件，代表是一段自定义SQL，可能是一张或多张表join的复杂逻辑，类似数据库视图
    - fields: 字段映射定义
    - lookupFrom: 用于实时ETL计算场景，描述该关联对象是否在主对象数据*创建*后主动被lookup补全，支持的选项如下
      - origion: 查询源库表完成补全
      - ods: 查询ADB3数仓中的ods表完成补全
      - none: 不参与lookup, 取决于与主对象在业务中的生命周期关系，如果关联对象数据的产生是在主对象数据产生之后，就没必要触发lookup
    - relationType: 此对象与其他对象(不限定主对象)的关联关系，支持选项：LEFT JOIN、 INNER JOIN、FULL JOIN
    - relations: 此对象与其他对象(不限定主对象)的关联字段
  - addOns: 非必填，对象数组，用于描述目标宽表在ADB中全量构建完成之后，以及启动实时ETL任务之前，调用定义的一个或多个blink batch任务，完成全量数据构建的补充处理，用于视图模型定义不了的业务订制逻辑处理
    - code: 唯一命名用于辨识用处
    - ref: blink任务名
    - type: 任务类型，如下:
      - blink_batch: blink批ETL作业
      - blink_stream: blink流ETL作业

- 核心类入口：ViewModelHandler
  - createViewModelFromObject：实现来自北冥自定义对象创建后的ADB数仓的对象宽表构建
  - createViewModelFromYaml：实现指定视图模型yaml定义，完成目标宽表对应管道实例的元数据构建
  - createTableAndFullSync：根据视图模型定义，完成目标宽表在ADB数仓的建表与全量数据构建，完全是根据视图模型定义的多表join关系，用的是源库表在ADB3数仓中的ods表,insert select 多表join 完成的目标宽表的数据全量构建
  - createBatchStreamTasks：根据视图模型定义，完成目标宽表全增量数据构建任务创建，并编排任务调度DAG，主要产出以下部分：
    - 批流ETL任务调度的编排DAG，用于编排全量构建与增量ETL任务的执行序列
    - 批流ETL作业, Blink SQL
      - 主对象实时写入任务: blink stream sql，订阅主对象增量消息，完成宽表中主对象部分字段的upsert与delete处理，有且仅能是基于主键的增删改，connector参考com.alibaba.blink.使用了自定义connectors.adb30.QanatAdb30TableFactory
        - 主对象是obj:北冥自定义对象时，增量消息来自对象Kafka消息队列，队列通常是每个对象会有quick和slow一对儿，quick队列负责对象数据实时变更，slow队列负责对象数据批量变更(如上游来自离线批量导入等)，自定义connector与正常表类型也不同，参考com.aliyun.wormhole.qanat.blink.sink.MdpObjectInstanceSink
      - 主对象Lookup关联对象实时补全任务: blink stream sql，订阅主对象*创建*增量消息，会调用模型定义中lookupFrom非none的关联对象查询，来补全关联对象中的字段内容
        - 使用了自定义connector，参考com.alibaba.blink.connectors.adb30.QanatAdb30TableFactory
        - Lookup使用了UDTF，参考com.aliyun.wormhole.qanat.blink.udtf.QanatQueryUDTF
      - 关联对象实时写入任务，connector参考com.alibaba.blink.使用了自定义connectors.adb30.QanatAdb30TableFactory
        - 如果关联对象来自mysql等在线数据，作业类型是blink stream sql，订阅关联对象的增量消息，完成完成宽表中关联对象部分字段的update，如果关联对象与主对象是外键关联，则实现的是对宽表的updateByFk
        - 如果关联对象来自ODPS表离线数据，datasource中定义ds_type=odps，会生成blink batch sql作业，实现odps表数据到目标adb宽表中部分字段的upsert操作
  - reflectObjectFieldChange， 相应北冥自定义对象，字段增、删、改事件，同步完成视图模型中的字段元数据同步刷新，并最后映射到目标宽表上的字段变更


---

**文档版本**: v1.0  
**最后更新**: 2024年12月  
**维护者**: Qanat团队 