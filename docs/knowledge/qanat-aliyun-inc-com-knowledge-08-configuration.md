# Qanat-Aliyun-Inc-Com Blueprint - 配置管理

## 1. 配置架构设计

### 1.1 配置分层结构
```
┌─────────────────────────────────────────┐
│            应用层配置                    │
│    • 业务参数配置                       │
│    • 功能开关配置                       │
└─────────────────────────────────────────┘
                     │
┌─────────────────────────────────────────┐
│            中间件配置                    │
│    • HSF配置                           │
│    • SchedulerX2配置                   │
│    • BUC配置                           │
└─────────────────────────────────────────┘
                     │
┌─────────────────────────────────────────┐
│            基础设施配置                  │
│    • 数据库配置                         │
│    • 服务端口配置                       │
│    • 日志配置                           │
└─────────────────────────────────────────┘
```

### 1.2 配置管理原则
- **环境隔离**: 不同环境使用独立配置
- **安全管控**: 敏感配置统一加密管理
- **动态更新**: 支持配置的热更新
- **版本管理**: 配置变更的版本控制
- **集中管理**: 通过Diamond配置中心统一管理

## 2. 环境配置

### 2.1 开发环境 (daily)

#### 基础服务配置
| 配置项 | 配置值 | 说明 |
|--------|--------|------|
| `server.port` | `7001` | HTTP服务端口 |
| `management.port` | `7002` | 管理端口 |
| `spring.hsf.version` | `1.0.0.DAILY` | HSF服务版本 |
| `spring.buc.loginEnv` | `daily` | BUC登录环境 |

#### 数据库配置
| 配置项 | 配置值 | 说明 |
|--------|--------|------|
| `spring.datasource.url` | `********************************************************************` | 测试数据库URL |
| `spring.datasource.username` | `alyqanat` | 数据库用户名 |
| `spring.datasource.password` | `alyqanat` | 数据库密码 |

#### SchedulerX2配置
| 配置项 | 配置值 | 说明 |
|--------|--------|------|
| `spring.schedulerx2.groupId` | `qanat-aliyun-inc-com.defaultGroup` | 调度组ID |
| `spring.schedulerx2.appKey` | `kNWLXQ1sezlgRjKiJOwpDA==` | 应用密钥 |
| `spring.schedulerx2.domainName` | `schedulerx2.taobao.net` | 调度域名 |

### 2.2 测试环境 (testing)

#### 环境特定配置
| 配置项 | 测试环境值 | 说明 |
|--------|------------|------|
| `environment.type` | `testing` | 环境类型标识 |
| `spring.hsf.version` | `1.0.0.TEST` | HSF测试版本 |
| `spring.buc.loginEnv` | `test` | BUC测试环境 |

#### 资源配置调整
- **数据库连接池**: 适中的连接数配置
- **缓存大小**: 测试级别的缓存配置
- **日志级别**: DEBUG级别便于问题排查

### 2.3 预发环境 (staging-sg)

#### 新加坡预发配置
| 配置项 | 预发环境值 | 说明 |
|--------|------------|------|
| `environment.type` | `staging-sg` | 新加坡预发环境 |
| `region.type` | `singapore` | 地域类型 |
| `qanat.vpc.endpoint` | `http://pre-qanat-sgp-vip.aliyun-inc.com` | VPC内网端点 |

#### 性能配置
- **连接池大小**: 接近生产环境的配置
- **超时时间**: 生产级别的超时配置
- **监控级别**: 全量监控配置

### 2.4 生产环境 (production/production-sg)

#### 生产环境配置
| 配置项 | 生产环境值 | 说明 |
|--------|------------|------|
| `environment.type` | `production` | 生产环境标识 |
| `spring.hsf.version` | `1.0.0.RELEASE` | HSF生产版本 |
| `spring.buc.loginEnv` | `online` | BUC线上环境 |

#### 高可用配置
- **数据库**: 主从配置，读写分离
- **连接池**: 最大连接数配置
- **缓存**: 高性能缓存配置
- **监控**: 全方位监控告警

## 3. 中间件配置

### 3.1 HSF服务框架配置

#### 基础HSF配置
```properties
# HSF基础配置
spring.hsf.group=HSF
spring.hsf.version=1.0.0.DAILY
spring.hsf.timeout=20000

# HSF服务提供者配置
spring.hsf.provider.threads=200
spring.hsf.provider.queues=1000
```

#### HSF服务消费者配置
```properties
# HSF消费者超时配置
spring.hsf.consumer.timeout=30000
spring.hsf.consumer.retries=2
```

### 3.2 SchedulerX2定时任务配置

#### 调度配置
```properties
# SchedulerX2基础配置
spring.schedulerx2.groupId=qanat-aliyun-inc-com.defaultGroup
spring.schedulerx2.appKey=kNWLXQ1sezlgRjKiJOwpDA==
spring.schedulerx2.domainName=schedulerx2.taobao.net

# 任务执行配置
spring.schedulerx2.maxConcurrency=10
spring.schedulerx2.instanceTimeout=3600
```

### 3.3 BUC用户认证配置

#### BUC基础配置
```properties
# BUC认证配置
spring.buc.client-key=6940e739-9797-43ff-8da5-c993b82e45ee
spring.buc.app-code=e7919b83e2ac4e3bb9bfb7f608231759
spring.buc.filter.url-patterns=/*
spring.buc.exclusions=/checkpreload.htm

# BUC环境配置
spring.buc.loginEnv=daily
spring.buc.sso-server-url=https://login-test.alibaba-inc.com
```

### 3.4 ACL权限控制配置

#### ACL配置
```properties
# ACL权限配置
spring.acl.access-key=qanat-6dixaYM18vgtTFNmOdFvQVBz
spring.acl.hsf-version=2.0.0.daily

# 权限应用配置
spring.acl.app-name=qanat-aliyun-inc-com
spring.acl.app-code=qanat-6dixaYM18vgtTFNmOdFvQVBz
```

## 4. 数据库配置

### 4.1 主数据库配置

#### MySQL配置
```properties
# 数据库连接配置
spring.datasource.url=************************************************************************************
spring.datasource.username=${db.username}
spring.datasource.password=${db.password}
spring.datasource.driver-class-name=com.mysql.jdbc.Driver

# 连接池配置
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
```

#### MyBatis配置
```properties
# MyBatis配置
spring.mybatis.config=classpath:/mybatis-config.xml
spring.mybatis.scan-base-package=com.aliyun.wormhole.qanat.dal.mapper
```

### 4.2 多数据源配置

#### 读写分离配置
```properties
# 主库配置
spring.datasource.master.url=${master.db.url}
spring.datasource.master.username=${master.db.username}
spring.datasource.master.password=${master.db.password}

# 从库配置
spring.datasource.slave.url=${slave.db.url}
spring.datasource.slave.username=${slave.db.username}
spring.datasource.slave.password=${slave.db.password}
```

## 5. 业务配置

### 5.1 MDP集成配置

#### MDP API配置
```properties
# MDP配置
mdp.api.domainCode=qanat_api
mdp.api.domainAk=53f7c5e8-9175-45d3-b70f-a050db39aed1
```

### 5.2 ODPS配置

#### ODPS回调配置
```properties
# ODPS回调配置
qanat.odps.callback=http://pre-qanat.aliyun-inc.com/api/reflectOdpsMetaEvent
```

### 5.3 系统参数配置

#### 应用配置
```properties
# 应用基础配置
project.name=qanat-aliyun-inc-com
environment.type=${environment.type}
region.type=${region.type}
aone.id=107771

# 业务配置
qanat.default.dw=devata_rtdw
qanat.unit.id=1
qanat.object.appname=beiming_xobject
```

## 6. 安全配置

### 6.1 Spring Security配置

#### CSRF配置
```properties
# CSRF配置
spring.security.csrf.supportedMethods=PUT
spring.security.csrf.url.style=regex
spring.security.csrf.url.included=/.*?
spring.security.csrf.url.excluded=^/csrf/nocheck
```

#### XSS防护配置
```properties
# XSS防护配置
spring.security.xss.enabled=true
spring.security.xss.ignored.files=security/xss/ignored.vm
spring.security.xss.ignored.context.names=ignoredName
```

#### HTTP安全配置
```properties
# HTTP安全域名配置
spring.security.http.safe.domains=\
  *.alibaba-inc.com,\
  .alibaba-inc.net,\
  localhost

spring.security.filters.redirect-validation.enabled=true
spring.security.filters.referer-validation.enabled=true
```

### 6.2 Keycenter配置

#### 密钥管理配置
```properties
# Keycenter配置
spring.keycenter.http-service-address=http://daily.keycenter.alibaba.net/keycenter
spring.keycenter.app-publish-num=8b1e44629f864538903bd7f9f0a67016
spring.keycenter.key-name=qanat-admin
```

## 7. 监控配置

### 7.1 Alimonitor配置

#### 监控配置
```properties
# Alimonitor监控配置
spring.alimonitor.method-patterns[0]=com.aliyun.wormhole.qanat.service.*
spring.alimonitor.method-patterns[1]=com.aliyun.wormhole.qanat.dal.*
spring.alimonitor.excluded-suffixes=gif,css,js,ico,do
```

### 7.2 Eagleeye配置

#### 链路追踪配置
```properties
# Eagleeye配置
spring.eagleeye.enabled=true
spring.eagleeye.mdc-updater=slf4j
```

## 8. 配置开关

### 8.1 功能开关

| 开关名称 | 开关描述 | 默认值 | 影响范围 |
|---------|---------|--------|---------|
| `management.security.enabled` | 管理端点安全 | `false` | 监控端点访问 |
| `spring.eagleeye.enabled` | 链路追踪开关 | `true` | 请求链路追踪 |
| `spring.security.xss.enabled` | XSS防护开关 | `true` | XSS攻击防护 |
| `spring.security.jsonp.enabled` | JSONP支持开关 | `false` | JSONP请求支持 |

### 8.2 性能开关

| 开关名称 | 开关描述 | 默认值 | 影响范围 |
|---------|---------|--------|---------|
| `spring.datasource.hikari.leak-detection-threshold` | 连接泄露检测 | `60000` | 数据库连接池 |
| `spring.hsf.timeout` | HSF调用超时 | `20000` | HSF服务调用 |

## 9. 配置管理最佳实践

### 9.1 配置安全
- **敏感信息加密**: 数据库密码、API密钥等敏感信息加密存储
- **权限控制**: 配置修改权限严格控制
- **审计日志**: 配置变更操作记录审计日志

### 9.2 配置验证
- **格式校验**: 配置项格式和取值范围校验
- **依赖检查**: 配置项之间的依赖关系检查
- **生效验证**: 配置生效后的功能验证

### 9.3 变更管理
- **版本控制**: 配置变更的版本管理
- **回滚机制**: 支持配置的快速回滚
- **灰度发布**: 重要配置支持灰度发布

---

**文档版本**: v1.0  
**最后更新**: 2024年12月  
**维护者**: Qanat团队 