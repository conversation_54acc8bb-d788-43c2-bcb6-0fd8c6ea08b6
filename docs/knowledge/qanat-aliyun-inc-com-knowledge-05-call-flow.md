# Qanat-Aliyun-Inc-Com Blueprint - 核心链路调用流程

## 1. 业务流程概览

### 1.1 系统核心流程架构
```
┌─────────────────────────────────────────────────────────────────┐
│                        Qanat核心业务流程                         │
├─────────────────────────────────────────────────────────────────┤
│ 数据源管理流程  │  任务调度流程  │  数据处理流程  │  视图模型流程 │
├─────────────────────────────────────────────────────────────────┤
│ • 数据源接入   │ • DAG任务创建  │ • 流计算处理   │ • 模型创建    │
│ • 连接测试     │ • 任务调度     │ • 批处理同步   │ • 视图生成    │
│ • 元数据采集   │ • 状态监控     │ • 数据管道     │ • 模型发布    │
│ • 字段映射     │ • 异常处理     │ • 结果输出     │ • 版本管理    │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 调用链路层次
```
用户请求 ──► Web层 ──► 服务层 ──► 数据层 ──► 外部系统
    │        │       │       │         │
    │        │       │       │         └── • HSF服务
    │        │       │       │            • 数据库
    │        │       │       │            • 消息队列
    │        │       │       │
    │        │       │       └── • MyBatis映射
    │        │       │          • 数据源连接
    │        │       │          • 缓存操作
    │        │       │
    │        │       └── • 业务逻辑处理
    │        │          • HSF服务调用
    │        │          • 异步任务处理
    │        │
    │        └── • REST Controller
    │           • 参数验证
    │           • 统一响应
    │
    └── • 前端页面
       • API调用
       • 用户交互
```

## 2. 数据源管理流程

### 2.1 数据源注册流程
```
用户创建数据源请求
         │
         ▼
┌─────────────────────┐
│  DataTubeController │
│  addDatasource()    │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐
│ DatasourceService   │
│ addDatasource()     │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐    ┌─────────────────────┐
│  连接性测试          │────┤  QanatDatasource    │
│  testConnection()   │    │  Handler            │
└─────────────────────┘    └─────────────────────┘
         │
         ▼
┌─────────────────────┐    ┌─────────────────────┐
│  元数据采集          │────┤  HSF调用MDP服务     │
│  collectMetadata()  │    │  @HSFConsumer       │
└─────────────────────┘    └─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  数据持久化          │
│  DatasourceMapper   │
│  insert()           │
└─────────────────────┘
         │
         ▼
   响应结果给用户
```

### 2.2 数据源连接测试流程
```java
public DataResult<Boolean> testConnection(String tenantId, String dsName) {
    // 1. 获取数据源配置
    Datasource datasource = getDatasourceByName(tenantId, dsName);
    
    // 2. 解析连接参数
    JSONObject dbMeta = JSON.parseObject(datasource.getMeta());
    
    // 3. 根据数据库类型创建连接
    switch(datasource.getDbType()) {
        case "MySQL":
            return testMySQLConnection(dbMeta);
        case "PostgreSQL":
            return testPostgreSQLConnection(dbMeta);
        case "TDDL":
            return testTDDLConnection(dbMeta);
        // ... 其他数据库类型
    }
    
    // 4. 返回测试结果
    return DataResult.success(true);
}
```

## 3. DAG任务调度流程

### 3.1 DAG任务创建流程
```
任务创建请求
         │
         ▼
┌─────────────────────┐
│  TaskController     │
│  createTask()       │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  TaskService        │
│  createTaskInfo()   │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  DAG解析与验证       │
│  DagService         │
│  parseDag()         │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐    ┌─────────────────────┐
│  SchedulerX任务注册  │────┤  SchedulerX客户端   │
│  registerJob()      │    │  创建分布式任务     │
└─────────────────────┘    └─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  任务信息持久化      │
│  TaskInfoMapper     │
│  insert()           │
└─────────────────────┘
```

### 3.2 DAG任务执行流程
```
SchedulerX触发
         │
         ▼
┌─────────────────────┐
│ QanatDagJobProcessor│
│ process()           │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  DAG解析             │
│  获取头节点          │
│  HeadNode Analysis  │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  并行执行头节点      │
│  MapJobProcessor    │
│  map()              │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  子任务状态监控      │
│  checkPrevReady()   │
│  checkPrevFailed()  │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  后续节点执行        │
│  NextNode Execute   │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  DAG执行完成         │
│  状态更新           │
└─────────────────────┘
```

### 3.3 子任务执行分发
```java
public ProcessResult processSubTask(Map<String, Object> instParamsMap) {
    JSONObject nodeJson = (JSONObject)instParamsMap.get("node");
    Node node = dagService.getNodeByJSONObject(nodeJson);
    
    // 根据节点类型分发到不同的处理器
    switch(node.getNodeAction()) {
        case BATCH:
            // 批处理任务
            if (node instanceof BlinkNode) {
                return executeBlinkJob(instParamsMap);
            } else if (node instanceof DataXNode) {
                return executeDataXJob(instParamsMap);
            }
            break;
            
        case STREAM:
            // 流计算任务
            return executeStreamJob(instParamsMap);
            
        case WORKFLOW:
            // 工作流任务
            return executeBpmsJob(instParamsMap);
            
        // 其他节点类型...
    }
}
```

## 4. 流计算处理流程

### 4.1 Blink任务执行流程
```
Blink节点触发
         │
         ▼
┌─────────────────────┐
│ QanatBlinkJob       │
│ Processor           │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐    ┌─────────────────────┐
│  获取Blink配置      │────┤  BlinkClient        │
│  getBlinkConf()     │    │  初始化客户端       │
└─────────────────────┘    └─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  构建启动参数        │
│  buildStartParams() │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐    ┌─────────────────────┐
│  提交Blink任务      │────┤  BlinkService       │
│  startJob()         │    │  startJob()         │
└─────────────────────┘    └─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  任务状态监控        │
│  循环检查状态        │
│  getInstanceState() │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  任务完成处理        │
│  更新任务状态        │
│  资源清理           │
└─────────────────────┘
```

### 4.2 Blink任务状态监控
```java
public void monitorBlinkJob(String tenantId, String appName, 
                           String jobName, Long instanceId) {
    while (true) {
        // 1. 获取任务实例状态
        String actualState = blinkService.getInstanceActualState(
            tenantId, appName, jobName, instanceId);
        
        // 2. 检查任务状态
        if ("FINISHED".equals(actualState)) {
            // 任务成功完成
            updateTaskStatus(instanceId, DagInstStatus.SUCCESS);
            break;
        } else if ("FAILED".equals(actualState) || "CANCELED".equals(actualState)) {
            // 任务失败或取消
            updateTaskStatus(instanceId, DagInstStatus.FAILED);
            break;
        }
        
        // 3. 检查执行超时
        if (isTimeout(instanceId)) {
            cancelJob(tenantId, appName, jobName, instanceId);
            break;
        }
        
        // 4. 等待下次检查
        Thread.sleep(30000); // 30秒检查一次
    }
}
```

## 5. 数据同步处理流程

### 5.1 DataX批量同步流程
```
DataX节点触发
         │
         ▼
┌─────────────────────┐
│ QanatDataXJob       │
│ Processor           │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐    ┌─────────────────────┐
│  生成DataX配置      │────┤  SyncDataService    │
│  generateConfig()   │    │  buildDataXJson()   │
└─────────────────────┘    └─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  Source配置构建     │
│  buildReaderConfig()│
└─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  Target配置构建     │
│  buildWriterConfig()│
└─────────────────────┘
         │
         ▼
┌─────────────────────┐    ┌─────────────────────┐
│  提交DataX任务      │────┤  DataX引擎          │
│  submitJob()        │    │  执行同步任务       │
└─────────────────────┘    └─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  同步状态监控        │
│  checkSyncStatus()  │
└─────────────────────┘
```

### 5.2 DRC实时同步流程
```
DRC变更事件
         │
         ▼
┌─────────────────────┐    ┌─────────────────────┐
│  DRC消息监听        │────┤  DrcEventListener   │
│  receiveMessage()   │    │  处理变更事件       │
└─────────────────────┘    └─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  事件解析           │
│  parseChangeEvent() │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  数据转换           │
│  transformData()    │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐    ┌─────────────────────┐
│  目标数据源写入      │────┤  TargetConnector    │
│  writeToTarget()    │    │  批量写入           │
└─────────────────────┘    └─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  状态更新           │
│  updateSyncStatus() │
└─────────────────────┘
```

## 6. 视图模型处理流程

### 6.1 视图模型创建流程
```
视图模型创建请求
         │
         ▼
┌─────────────────────┐
│  ViewModel          │
│  Controller         │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  视图模型服务        │
│  ViewModelService   │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐    ┌─────────────────────┐
│  模型定义解析        │────┤  ViewModelHandler   │
│  parseModelDef()    │    │  生成SQL和配置      │
└─────────────────────┘    └─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  数据处理作业生成    │
│  generateJobs()     │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐    ┌─────────────────────┐
│  Blink作业提交      │────┤  BlinkService       │
│  submitBlinkJob()   │    │  创建流计算作业     │
└─────────────────────┘    └─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  模型状态管理        │
│  updateModelStatus()│
└─────────────────────┘
```

### 6.2 视图模型SQL生成
```java
public String generateViewModelSQL(ViewModel viewModel) {
    StringBuilder sql = new StringBuilder();
    
    // 1. 构建SELECT子句
    sql.append("SELECT ");
    sql.append(buildSelectClause(viewModel.getFields()));
    
    // 2. 构建FROM子句
    sql.append(" FROM ");
    sql.append(buildFromClause(viewModel.getMainObject()));
    
    // 3. 构建JOIN子句
    if (viewModel.hasLookupObjects()) {
        sql.append(buildJoinClause(viewModel.getLookupObjects()));
    }
    
    // 4. 构建WHERE子句
    if (viewModel.hasFilters()) {
        sql.append(" WHERE ");
        sql.append(buildWhereClause(viewModel.getFilters()));
    }
    
    // 5. 构建GROUP BY子句
    if (viewModel.hasAggregations()) {
        sql.append(" GROUP BY ");
        sql.append(buildGroupByClause(viewModel.getGroupFields()));
    }
    
    return sql.toString();
}
```

## 7. BPMS工作流处理流程

### 7.1 BPMS流程启动
```
BPMS节点触发
         │
         ▼
┌─────────────────────┐
│ QanatBpmsJob        │
│ Processor           │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐    ┌─────────────────────┐
│  获取流程定义        │────┤  ProcessInstance    │
│  getBpmsCode()      │    │  Service            │
└─────────────────────┘    └─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  启动BPMS流程       │
│  startProcess()     │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐    ┌─────────────────────┐
│  流程状态监听        │────┤  BpmsNotifyListener │
│  waitForComplete()  │    │  MQ消息监听         │
└─────────────────────┘    └─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  流程结果处理        │
│  handleResult()     │
└─────────────────────┘
```

### 7.2 BPMS状态回调处理
```java
@Component
public class BpmsNotifyListener implements MessageListener {
    
    @Override
    public void receiveMessage(Message message, MessageStatus messageStatus) {
        try {
            // 1. 解析BPMS通知消息
            JSONObject notification = parseMessage(message);
            String processInstanceId = notification.getString("processInstanceId");
            String eventType = notification.getString("eventType");
            
            // 2. 查找对应的任务实例
            TaskInstance taskInstance = findTaskByProcessId(processInstanceId);
            
            // 3. 根据事件类型更新任务状态
            if ("PROC_INST_FINISH".equals(eventType)) {
                // 流程完成
                updateTaskStatus(taskInstance.getId(), DagInstStatus.SUCCESS);
            } else if ("PROCESS_INSTANCE_TERMINATE".equals(eventType)) {
                // 流程终止
                updateTaskStatus(taskInstance.getId(), DagInstStatus.FAILED);
            }
            
            // 4. 发布任务状态变更事件
            publishTaskStatusEvent(taskInstance);
            
        } catch (Exception e) {
            log.error("处理BPMS回调失败", e);
        }
    }
}
```

## 8. 异常处理流程

### 8.1 任务异常恢复流程
```
任务执行异常
         │
         ▼
┌─────────────────────┐
│  异常捕获           │
│  Exception Handler  │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  异常分类           │
│  classifyException()│
└─────────────────────┘
         │
         ▼
┌─────────────────────┐    ┌─────────────────────┐
│  可重试异常          │────┤  自动重试机制       │
│  RetryableException │    │  指数退避策略       │
└─────────────────────┘    └─────────────────────┘
         │
         ▼
┌─────────────────────┐    ┌─────────────────────┐
│  不可重试异常        │────┤  人工干预流程       │
│  FatalException     │    │  BPMS审批流程       │
└─────────────────────┘    └─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  异常信息记录        │
│  logException()     │
└─────────────────────┘
```

### 8.2 系统监控告警流程
```
监控指标采集
         │
         ▼
┌─────────────────────┐    ┌─────────────────────┐
│  指标数据分析        │────┤  Alimonitor系统     │
│  analyzeMetrics()   │    │  实时监控           │
└─────────────────────┘    └─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  阈值检查           │
│  checkThreshold()   │
└─────────────────────┘
         │
         ▼
┌─────────────────────┐    ┌─────────────────────┐
│  告警触发           │────┤  通知服务           │
│  triggerAlert()     │    │  短信/邮件/钉钉     │
└─────────────────────┘    └─────────────────────┘
         │
         ▼
┌─────────────────────┐
│  自动修复           │
│  autoRemediation()  │
└─────────────────────┘
```

## 9. 性能优化调用链

### 9.1 数据库性能优化
```java
// 连接池优化
@Configuration
public class DataSourceConfig {
    @Bean
    public DataSource dataSource() {
        HikariConfig config = new HikariConfig();
        config.setMaximumPoolSize(20);           // 最大连接数
        config.setMinimumIdle(5);                // 最小空闲连接
        config.setConnectionTimeout(30000);      // 连接超时
        config.setIdleTimeout(600000);           // 空闲超时
        config.setLeakDetectionThreshold(60000); // 连接泄露检测
        return new HikariDataSource(config);
    }
}

// SQL优化
@Select({
    "<script>",
    "SELECT * FROM datasource WHERE tenant_id = #{tenantId}",
    "<if test='dsName != null'>AND ds_name = #{dsName}</if>",
    "ORDER BY gmt_create DESC",
    "LIMIT #{pageSize} OFFSET #{offset}",
    "</script>"
})
List<Datasource> selectByPage(@Param("tenantId") String tenantId,
                              @Param("dsName") String dsName,
                              @Param("offset") int offset,
                              @Param("pageSize") int pageSize);
```

### 9.2 缓存优化调用链
```java
@Service
public class DatasourceServiceImpl {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public Datasource getDatasource(String tenantId, String dsName) {
        // 1. 尝试从缓存获取
        String cacheKey = buildCacheKey(tenantId, dsName);
        Datasource cached = (Datasource) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        // 2. 从数据库查询
        Datasource datasource = datasourceMapper.selectByName(tenantId, dsName);
        
        // 3. 写入缓存
        if (datasource != null) {
            redisTemplate.opsForValue().set(cacheKey, datasource, 
                Duration.ofMinutes(30));
        }
        
        return datasource;
    }
}
```

---

**文档版本**: v1.0  
**创建时间**: 2024-01-XX  
**维护团队**: Qanat平台开发组 