# Qanat-Aliyun-Inc-Com Blueprint - API设计

## 1. API架构设计

### 1.1 API分层架构
```
┌─────────────────────────────────────────┐
│               API Gateway               │
└─────────────────────────────────────────┘
                     │
┌─────────────────────────────────────────┐
│            Controller Layer            │
│  • DataTubeController                  │
│  • BucController                       │
│  • MainController                      │
└─────────────────────────────────────────┘
                     │
┌─────────────────────────────────────────┐
│            Service Layer               │
│  • HSF Services                        │
│  • REST Services                       │
└─────────────────────────────────────────┘
                     │
┌─────────────────────────────────────────┐
│            OpenAPI Layer               │
│  • MdpCustomViewService                │
│  • QanatSqlExecuteService              │
└─────────────────────────────────────────┘
```

### 1.2 API设计原则
- **RESTful设计**: 遵循REST API设计规范
- **统一响应格式**: 使用 `DataResult<T>` 统一响应结构
- **请求参数校验**: 完整的参数校验和错误处理
- **多协议支持**: 同时支持HTTP REST和HSF协议
- **版本管理**: 通过路径和参数进行API版本控制

## 2. REST API接口

### 2.1 视图模型管理API

| 接口路径 | HTTP方法 | 接口名称 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|---------|
| `/api/createViewModelFromObject` | POST | 从对象创建视图模型 | 基于MDP对象创建视图模型 | `tenantId, appName, objectType, objectUniqueCode, operateEmpid, objectMsg` | `DataResult<Long>` |
| `/api/createViewModelFromYaml` | POST | 从YAML创建视图模型 | 基于YAML配置创建视图模型 | `ViewModelRequest` | `DataResult<Long>` |
| `/api/modifyViewModel` | POST | 修改视图模型 | 修改现有视图模型配置 | `ViewModelRequest` | `DataResult<Long>` |
| `/api/restartModelTask` | POST | 重启模型任务 | 重启指定的视图模型任务 | `tenantId, viewModelId, operateEmpid` | `DataResult<Long>` |
| `/api/reflectObjectFieldChange` | POST | 反射对象字段变更 | 处理对象字段变更事件 | `tenantId, dsUniqueName, fieldName, isRef, operateType, tagJson` | `DataResult<Boolean>` |

### 2.2 数据同步任务API

| 接口路径 | HTTP方法 | 接口名称 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|---------|
| `/api/createTableAndFullSync` | POST | 创建表并全量同步 | 创建目标表并执行全量数据同步 | `tenantId, viewModelId` | `DataResult<Boolean>` |
| `/api/createBatchStreamTasks` | POST | 创建批流任务 | 创建批处理和流处理任务 | `tenantId, viewModelId, operateEmpid` | `DataResult<Long>` |
| `/api/runTask` | POST | 运行任务 | 启动指定任务执行 | `tenantId, operateEmpid, taskId` | `DataResult<Long>` |
| `/api/stopTask` | POST | 停止任务 | 停止正在执行的任务 | `tenantId, operateEmpid, taskId` | `DataResult<Boolean>` |
| `/api/deleteScheduleTask` | POST | 删除调度任务 | 删除定时调度任务 | `tenantId, operateEmpid, taskId` | `DataResult<Boolean>` |

### 2.3 数据源管理API

| 接口路径 | HTTP方法 | 接口名称 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|---------|
| `/api/createDatasource` | POST | 创建数据源 | 创建新的数据源配置 | `DatasourceRequest` | `DataResult<Long>` |
| `/api/modifyDatasource` | POST | 修改数据源 | 修改现有数据源配置 | `DatasourceRequest` | `DataResult<Boolean>` |
| `/api/createDsInfoAndOdsTask` | POST | 创建数据源和ODS任务 | 同时创建数据源和对应的ODS任务 | `CreateOdsRequest` | `DataResult<Map<String, Long>>` |

### 2.4 流计算管理API

| 接口路径 | HTTP方法 | 接口名称 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|---------|
| `/api/getProjectCUs` | POST | 获取项目CU资源 | 查询Blink项目的CU资源使用情况 | `tenantId, appName` | `List<Map<String, Object>>` |
| `/api/listProjectBindQueue` | POST | 查询项目绑定队列 | 查询项目绑定的执行队列 | `tenantId, appName` | `String` |

### 2.5 数据管道管理API

| 接口路径 | HTTP方法 | 接口名称 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|---------|
| `/api/pauseAllDrcTasks` | POST | 暂停所有DRC任务 | 暂停全部数据实时同步任务 | 无 | `String` |
| `/api/resumeAllDrcTasks` | POST | 恢复所有DRC任务 | 恢复全部数据实时同步任务 | 无 | `String` |
| `/api/pauseAllTasks` | POST | 暂停所有任务 | 暂停全部数据处理任务 | 无 | `String` |
| `/api/resumeAllTasks` | POST | 恢复所有任务 | 恢复全部数据处理任务 | 无 | `String` |

### 2.6 外部系统集成API

| 接口路径 | HTTP方法 | 接口名称 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|---------|
| `/api/createDrcTaskForDs` | POST | 为数据源创建DRC任务 | 为指定数据源创建实时同步任务 | `tenantId, appName, dsName, empid` | `DataResult<Map<String, Long>>` |
| `/api/send` | POST | 发送消息 | 发送MetaQ消息 | `requestId, topic, tag, key, msg` | `String` |
| `/api/deleteSchedulerx2Job` | POST | 删除SchedulerX2任务 | 删除分布式定时任务 | `tenantId, appName, jobId` | `DataResult<Boolean>` |
| `/api/reflectOdpsMetaEvent` | POST | 反射ODPS元数据事件 | 处理ODPS元数据变更事件 | XML格式元数据 | `String` |
| `/api/registerOdpsMetaEvent` | POST | 注册ODPS元数据事件 | 注册ODPS元数据监听 | `tenantId, dsName` | `String` |
| `/api/unregisterOdpsMetaEvent` | POST | 取消注册ODPS元数据事件 | 取消ODPS元数据监听 | `tenantId, dsName` | `String` |

## 3. HSF服务接口

### 3.1 数据处理服务

| 服务名称 | 服务版本 | 接口方法 | 功能描述 | 参数说明 | 返回值 |
|---------|---------|---------|---------|---------|--------|
| `DatasourceService` | `1.0.0` | `createDatasource` | 创建数据源 | `DatasourceRequest` | `DataResult<Long>` |
| `TaskService` | `1.0.0` | `runTask` | 运行任务 | `tenantId, operateEmpid, taskId` | `DataResult<Long>` |
| `BlinkService` | `1.0.0` | `buildBlinkJob` | 构建Blink任务 | `tenantId, appName, jobName, sql, folderName, packages, isBatch, planJson` | `void` |

### 3.2 流计算服务

| 服务名称 | 服务版本 | 接口方法 | 功能描述 | 参数说明 | 返回值 |
|---------|---------|---------|---------|---------|--------|
| `BlinkService` | `1.0.0` | `startJob` | 启动流计算任务 | `tenantId, appName, jobName, startTime, params` | `Long` |
| `BlinkService` | `1.0.0` | `stopJob` | 停止流计算任务 | `tenantId, appName, jobName` | `void` |
| `BlinkService` | `1.0.0` | `getJobDetail` | 获取任务详情 | `tenantId, appName, jobName` | `String` |

### 3.3 数据同步服务

| 服务名称 | 服务版本 | 接口方法 | 功能描述 | 参数说明 | 返回值 |
|---------|---------|---------|---------|---------|--------|
| `SyncDataService` | `1.0.0` | `syncData` | 数据同步 | `requestId, setting, reader, writer` | `DataResult<Map<String, String>>` |
| `DrcService` | `1.0.0` | `createDrcTaskForDs` | 创建DRC任务 | `tenantId, appName, dsName, empid` | `DataResult<Map<String, Long>>` |

## 4. OpenAPI接口

### 4.1 MDP自定义视图服务

| 接口名称 | 功能描述 | 参数类型 | 返回类型 |
|---------|---------|---------|---------|
| `buildCustomOdpsView` | 构建自定义ODPS视图 | `CustomViewRequest` | `ApiResult<String>` |
| `buildDomainOdpsView` | 构建域ODPS视图 | `DomainViewRequest` | `ApiResult<String>` |
| `getYamlFromDomain` | 从域获取YAML配置 | `DomainViewRequest` | `ApiResult<String>` |

### 4.2 SQL执行服务

| 接口名称 | 功能描述 | 参数类型 | 返回类型 |
|---------|---------|---------|---------|
| `execute` | 执行SQL查询 | `SqlExecuteRequest` | `ApiResult<List<Map<String, Object>>>` |

## 5. 请求响应格式

### 5.1 统一响应格式

```json
{
  "success": true,
  "code": "200", 
  "message": "操作成功",
  "data": {
    // 具体业务数据
  }
}
```

### 5.2 DataResult响应结构

```java
public class DataResult<T> {
    private Boolean success;    // 操作是否成功
    private String code;        // 响应码
    private String message;     // 响应消息
    private T data;            // 业务数据
}
```

### 5.3 错误响应格式

```json
{
  "success": false,
  "code": "500",
  "message": "系统内部错误",
  "data": null
}
```

## 6. API安全设计

### 6.1 认证机制
- **BUC认证**: 集成阿里内部BUC用户认证系统
- **HSF认证**: HSF服务调用的身份验证
- **Token验证**: API调用的Token验证机制

### 6.2 权限控制
- **ACL权限**: 基于ACL的细粒度权限控制
- **租户隔离**: 多租户数据隔离和权限管理
- **操作审计**: 完整的API调用审计日志

### 6.3 安全防护
- **CSRF防护**: Spring Security CSRF保护
- **XSS防护**: 输入参数XSS过滤
- **参数校验**: 严格的参数格式和范围校验

## 7. API监控和限流

### 7.1 性能监控
- **响应时间**: API响应时间监控
- **吞吐量**: API调用QPS监控  
- **错误率**: API调用错误率统计

### 7.2 限流策略
- **租户级限流**: 按租户维度的API调用限流
- **接口级限流**: 针对特定接口的流量控制
- **用户级限流**: 按用户维度的调用频次限制

---

**文档版本**: v1.0  
**最后更新**: 2024年12月  
**维护者**: Qanat团队 