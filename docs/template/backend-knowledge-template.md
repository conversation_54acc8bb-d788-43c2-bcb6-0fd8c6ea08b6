# 后端工程 Blueprint 提取模板

> **使用说明**: 此模板用于指导LLM生成具体工程的Blueprint文档。请根据此模板为每个模块生成独立的markdown文件。

## 文件生成规则

### 文件存放位置
所有Blueprint文档文件应放在工程根目录的 `blueprint/` 目录下

### 主文件
- **blueprint/[工程名称]-blueprint.md** - 项目总体概述和模块导航

### 模块文件
- **blueprint/[工程名称]-blueprint-01-project-overview.md** - 项目概述
- **blueprint/[工程名称]-blueprint-02-core-modules.md** - 核心功能模块
- **blueprint/[工程名称]-blueprint-03-architecture.md** - 工程架构
- **blueprint/[工程名称]-blueprint-04-api-design.md** - API设计
- **blueprint/[工程名称]-blueprint-05-call-flow.md** - 核心链路调用流程
- **blueprint/[工程名称]-blueprint-06-domain-entities.md** - 领域实体及关系
- **blueprint/[工程名称]-blueprint-07-external-dependencies.md** - 对外部接口的依赖
- **blueprint/[工程名称]-blueprint-08-configuration.md** - 配置管理
- **blueprint/[工程名称]-blueprint-09-monitoring.md** - 监控与运维

---

## 1. 项目概述

### 1.1 项目基本信息
- **项目名称**: `[项目名称]`
- **项目描述**: `[项目功能描述]`
- **技术栈**: 
  - 框架: `[主框架名称]` `[版本号]`
  - 构建工具: `[构建工具]`
  - 编程语言: `[编程语言]` `[版本号]`
  - 数据库: `[数据库类型]`
  - 消息队列: `[消息队列类型]`
  - 缓存: `[缓存类型]`
  - 其他中间件:  `[中间件类型以及版本]`
- **部署环境**: 
  - 开发环境: `[开发环境配置]`
  - 测试环境: `[测试环境配置]`
  - 预发环境: `[预发环境配置]`
  - 生产环境: `[生产环境配置]`

## 2. 核心功能模块
- **模块1**: `[模块名称]` - `[功能描述]` - `[模块入口]`
- **模块2**: `[模块名称]` - `[功能描述]` - `[模块入口]`
- **模块3**: `[模块名称]` - `[功能描述]` - `[模块入口]`

## 3. 工程架构

### 3.1 模块结构
```
[项目根目录]/
├── [模块1]/                  # [模块1描述]
├── [模块2]/                  # [模块2描述]
├── [模块3]/                  # [模块3描述]
├── [模块4]/                  # [模块4描述]
└── [模块5]/                  # [模块5描述]
```

### 3.2 模块依赖关系
```
[主模块]
├── [依赖模块1]
├── [依赖模块2]
└── [依赖模块3]

[依赖模块1]
└── [公共模块]

[依赖模块2]
└── [公共模块]
```

### 3.3 系统架构图
```
┌─────────────────┐    ┌─────────────────┐
│   前端/客户端    │    │   网关/代理     │
└─────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    应用服务层                                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │ 接口层      │  │  业务逻辑层  │  │  领域模型层  │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    数据访问层                                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │ 数据访问    │  │  数据映射   │  │  数据实体   │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    基础设施层                                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │   数据库     │  │   缓存      │  │   消息队列   │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
└─────────────────────────────────────────────────────────────────┘
```

## 4. API设计

### 4.1 RESTful API接口
| 接口路径 | HTTP方法 | 接口名称 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|---------|
| `[接口路径]` | `[HTTP方法]` | `[接口名称]` | `[功能描述]` | `[请求参数]` | `[响应格式]` |

### 4.2 服务接口
| 服务名称 | 服务版本 | 接口方法 | 功能描述 | 参数说明 | 返回值 |
|---------|---------|---------|---------|---------|--------|
| `[服务名]` | `[版本号]` | `[方法名]` | `[功能描述]` | `[参数列表]` | `[返回值类型]` |

### 4.3 定时任务接口
| 任务名称 | 执行频率 | 任务描述 | 入口方法 | 配置参数 |
|---------|---------|---------|---------|---------|
| `[任务名]` | `[执行频率]` | `[任务功能描述]` | `[方法名]` | `[参数说明]` |

## 5. 核心链路调用流程

### 5.1 请求处理时序图
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as 网关
    participant Controller as Controller
    participant Service as Service
    participant DomainService as DomainService
    participant Repository as Repository
    participant DB as 数据库

    Client->>Gateway: HTTP请求
    Gateway->>Controller: 路由转发
    Controller->>Controller: 参数校验
    Controller->>Service: 调用业务服务
    Service->>DomainService: 领域服务调用
    DomainService->>Repository: 数据访问
    Repository->>DB: SQL执行
    DB-->>Repository: 返回结果
    Repository-->>DomainService: 返回数据
    DomainService-->>Service: 返回处理结果
    Service-->>Controller: 返回业务结果
    Controller-->>Gateway: HTTP响应
    Gateway-->>Client: 响应数据
```

### 5.2 服务间调用时序图
```mermaid
sequenceDiagram
    participant Consumer as 服务消费者
    participant Registry as 服务注册中心
    participant Provider as 服务提供者
    participant Service as Service
    participant DomainService as DomainService
    participant Repository as Repository
    participant DB as 数据库

    Consumer->>Registry: 服务发现
    Registry-->>Consumer: 返回服务地址
    Consumer->>Provider: 远程调用
    Provider->>Service: 业务处理
    Service->>DomainService: 领域逻辑
    DomainService->>Repository: 数据操作
    Repository->>DB: 数据库操作
    DB-->>Repository: 返回数据
    Repository-->>DomainService: 返回结果
    DomainService-->>Service: 返回处理结果
    Service-->>Provider: 返回结果
    Provider-->>Consumer: 返回调用结果
```

### 5.3 定时任务执行时序图
```mermaid
sequenceDiagram
    participant Scheduler as 调度器
    participant Task as 定时任务
    participant Service as Service
    participant DomainService as DomainService
    participant Repository as Repository
    participant DB as 数据库

    Scheduler->>Task: 触发任务
    Task->>Task: 任务初始化
    Task->>Service: 执行业务逻辑
    Service->>DomainService: 领域服务调用
    DomainService->>Repository: 数据操作
    Repository->>DB: 数据库操作
    DB-->>Repository: 返回结果
    Repository-->>DomainService: 返回数据
    DomainService-->>Service: 返回处理结果
    Service-->>Task: 返回执行结果
    Task-->>Scheduler: 任务完成
```

### 5.4 消息消费时序图
```mermaid
sequenceDiagram
    participant MQ as 消息队列
    participant Consumer as 消息消费者
    participant Service as Service
    participant DomainService as DomainService
    participant Repository as Repository
    participant DB as 数据库

    MQ->>Consumer: 推送消息
    Consumer->>Consumer: 消息解析
    Consumer->>Service: 业务处理
    Service->>DomainService: 领域服务调用
    DomainService->>Repository: 数据操作
    Repository->>DB: 数据库操作
    DB-->>Repository: 返回结果
    Repository-->>DomainService: 返回数据
    DomainService-->>Service: 返回处理结果
    Service-->>Consumer: 返回处理结果
    Consumer->>Repository: 更新消费状态
    Consumer-->>MQ: 确认消费
```

## 6. 领域实体及实体之间的关系

### 6.1 核心实体
| 实体名称 | 实体描述 | 主要属性 | 关联关系 |
|---------|---------|---------|---------|
| `[实体名]` | `[实体描述]` | `[属性列表]` | `[关联关系]` |

### 6.2 实体关系图
```
[实体A] ────[关系类型]────> [实体B]
[实体B] ────[关系类型]────> [实体C]
[实体A] ────[关系类型]────> [实体D]
```

### 6.3 数据模型设计
```sql
-- 主要数据表结构
CREATE TABLE [表名] (
    [字段名] [数据类型] [约束],
    [字段名] [数据类型] [约束],
    PRIMARY KEY ([主键字段])
);
```

## 7. 对外部接口的依赖

### 7.1 外部服务依赖
| 服务名称 | 服务类型 | 版本 | 用途 | 调用方式 |
|---------|---------|------|------|---------|
| `[服务名]` | `[服务类型]` | `[版本号]` | `[用途描述]` | `[调用方式]` |

### 7.2 第三方API依赖
| API名称 | 提供商 | 版本 | 用途 | 认证方式 |
|---------|--------|------|------|---------|
| `[API名称]` | `[提供商]` | `[版本号]` | `[用途描述]` | `[认证方式]` |

### 7.3 数据库依赖
| 数据库类型 | 版本 | 用途 | 连接配置 |
|-----------|------|------|---------|
| `[数据库类型]` | `[版本号]` | `[用途描述]` | `[连接配置]` |

### 7.4 中间件依赖
| 中间件类型 | 版本 | 用途 | 配置说明 |
|-----------|------|------|---------|
| `[中间件类型]` | `[版本号]` | `[用途描述]` | `[配置说明]` |

## 8. 配置管理

### 8.1 环境配置
| 配置项 | 开发环境 | 测试环境 | 预发环境 | 生产环境 |
|--------|---------|---------|---------|---------|
| `[配置项名]` | `[开发环境值]` | `[测试环境值]` | `[预发环境值]` | `[生产环境值]` |

### 8.2 开关配置
| 开关名称 | 开关描述 | 默认值 | 影响范围 |
|---------|---------|--------|---------|
| `[开关名]` | `[开关描述]` | `[默认值]` | `[影响范围]` |

## 9. 监控与运维

### 9.1 日志配置
- **日志级别**: `[日志级别配置]`
- **日志输出**: `[日志输出配置]`
- **日志轮转**: `[日志轮转策略]`

---

## LLM生成指导

### 生成步骤
1. **分析项目结构** - 扫描项目代码，识别模块、包结构、依赖关系
2. **参考业务文件** - 分析外部提供的业务文件，提取业务背景、业务术语、功能需求等信息
3. **提取核心信息** - 从代码中提取API、实体、配置等关键信息
4. **生成独立文件** - 按照上述文件结构生成9个独立的markdown文件
5. **填充具体内容** - 将方括号`[]`中的占位符替换为实际项目信息
6. **创建导航文件** - 生成README.md作为项目文档导航

### 业务文件参考
在生成项目概述部分时，需要重点参考外部提供的业务文件，包括但不限于：
- **业务背景文档** - 了解项目的业务目标和价值
- **业务术语表** - 提取关键业务概念和术语定义
- **功能需求文档** - 明确系统功能边界和业务规则
- **业务流程文档** - 理解核心业务流程和用户场景
- **技术需求文档** - 了解技术约束和性能要求

### 注意事项
- 每个文件应该包含完整的章节内容
- 保持时序图的Mermaid格式
- 确保表格格式正确
- 根据实际项目调整章节内容
- 移除不相关的章节或添加项目特有的章节
- 在项目概述中准确反映业务背景和术语
- 确保技术实现与业务需求的一致性
- **重要：生成内容必须准确，可以没有信息，但不能虚构或推测**
- **对于不确定的信息，应明确标注为"待确认"或"未找到相关信息"**
- **优先基于代码分析和业务文档提取真实信息，避免主观推断**

**注意**: 此模板为通用模板，请根据具体项目情况进行调整和完善。方括号 `[]` 中的内容需要根据实际项目信息进行填充。 