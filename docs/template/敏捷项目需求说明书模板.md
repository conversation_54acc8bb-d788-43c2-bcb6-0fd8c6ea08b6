# 敏捷项目需求说明书模板

## 文档信息
- **需求编号**：REQ_XXXX
- **需求链接**：https://xxxx
- **项目名称**：智能客户管理系统
- **产品负责人**：张三
- **Scrum Master**：李四
- **版本号**：V1.0
- **更新日期**：2024-01-15

## 修订记录
| 版本 | 日期 | 修订内容 | 修订人 |
|------|------|----------|--------|
| V1.0 | 2024-01-15 | 初始版本 | 张三 |
| V1.1 | 2024-01-20 | 增加支付模块 | 李四 |

---

## 1. 项目概述

### 1.1 项目愿景
**愿景陈述**：为销售团队提供一个直观、高效的客户管理平台，帮助销售人员提升50%的工作效率，实现客户价值最大化。

### 1.2 项目目标
- **业务目标**：提升销售转化率25%，减少客户流失率15%
- **用户目标**：简化客户管理流程，提供移动端支持
- **技术目标**：构建可扩展的微服务架构，支持1000+并发用户

### 1.3 成功指标
- **用户采用率**：上线3个月内达到90%
- **系统响应时间**：页面加载时间<2秒
- **用户满意度**：NPS评分>8分
---

## 3. 用户故事

### 3.1 用户画像
#### 🎯 主要用户：销售代表
- **年龄**：28岁
- **经验**：3年销售经验
- **技能**：熟悉基本办公软件，移动设备重度用户
- **痛点**：客户信息分散，跟进效率低，外出时无法及时更新数据
- **期望**：操作简单，随时随地可用，数据同步及时

#### 🎯 次要用户：销售经理
- **年龄**：35岁
- **经验**：8年管理经验
- **技能**：数据分析能力强，注重团队绩效
- **痛点**：缺乏团队绩效可视化，无法实时掌握销售进度
- **期望**：全面的数据报表，团队协作功能



### 3.2 用户故事地图
```
用户目标：xxx
    │
    ├── 客户信息管理
    │   ├── 创建客户档案
    │   ├── 更新客户信息
    │   └── 查看客户历史
    │
    ├── 销售流程管理
    │   ├── 创建销售机会
    │   ├── 跟进客户进度
    │   └── 完成交易
    │
    └── 数据分析
        ├── 查看销售报表
        ├── 分析客户价值
        └── 预测销售趋势
```

### 3.3：用户故事

#### 客户信息管理
- 作为销售代表，我希望能够快速创建客户档案，以便系统化管理客户信息
```
用户故事：创建客户档案
作为：销售代表
我希望：能够快速创建客户档案
以便：系统化管理客户信息，提高工作效率

验收标准：
✓ 必填字段：客户名称、联系人、电话、邮箱
✓ 可选字段：公司规模、行业类型、地址
✓ 创建成功后自动生成客户编号
✓ 支持批量导入客户信息
✓ 创建时间<3秒
✓ 移动端同步显示
```

- 作为销售代表，我希望能够在移动端查看客户信息，以便外出时及时了解客户状况
  ...

#### 用户故事X


---

## 4. 业务流程

### 4.1 核心业务流程
#### 客户管理流程
```mermaid
graph TD
    A[销售代表登录] --> B[搜索/浏览客户]
    B --> C{客户是否存在？}
    C -->|是| D[查看客户详情]
    C -->|否| E[创建新客户]
    E --> F[填写客户信息]
    F --> G[保存客户档案]
    G --> H[客户档案创建完成]
    D --> I[更新客户信息]
    I --> J[添加跟进记录]
    J --> K[设置提醒事项]
    K --> L[客户管理完成]
```

#### 销售机会管理流程
```mermaid
graph TD
    A[识别销售机会] --> B[创建销售机会]
    B --> C[初步接触]
    C --> D[需求分析]
    D --> E[方案制定]
    E --> F[商务谈判]
    F --> G{是否成交？}
    G -->|是| H[签订合同]
    G -->|否| I[跟进或关闭]
    H --> J[交付实施]
    J --> K[售后服务]
    I --> L[记录失败原因]
    L --> M[经验总结]
```

### 4.2 异常流程
```mermaid
graph TD
    A[正常业务流程] --> B{系统异常？}
    B -->|网络异常| C[离线模式]
    B -->|数据冲突| D[冲突解决]
    B -->|权限不足| E[申请权限]
    B -->|数据错误| F[错误提示]
    C --> G[本地缓存]
    G --> H[网络恢复后同步]
    D --> I[显示冲突详情]
    I --> J[用户选择保留版本]
    E --> K[通知管理员]
    F --> L[引导用户修正]
```

---

## 5. 权限设计

### 5.1 角色
描述每个业务对象（记录）的角色

#### 客户
| 角色 | 说明 |
| 销售代表 | 某个客户的销售代表 |

#### 线索
| 角色 | 说明 |
| 销售代表 | 某个客户的销售代表 |
---

## 5. 功能需求

### 5.1 功能架构
不同功能按功能模块分组，如：

客户管理
   客户创建
   客户编辑
   客户清单
       按名称搜索
       按多个字段组合搜索


### 5.2 功能详情
#### 📋 功能：客户档案管理
**功能描述**：提供完整的客户信息管理功能，支持创建、查看、编辑、删除客户档案。

**可操作的角色**：
| 角色 |
| 角色1 |
| 角色2 |


**输入**：
- 客户基本信息（姓名、电话、邮箱等）
- 公司信息（公司名称、规模、行业等）
- 联系记录和跟进历史

**规则**：
### 规则表&规则矩阵表达（常用于描述用户判断因子多且没有流程的场景）
| 条件 | 客户类型 | 跟进频率 | 分配规则 | 权限等级 |
|------|----------|----------|----------|----------|
| 新客户 | 潜在客户 | 每周1次 | 自动分配 | 标准 |
| 意向客户 | 高价值客户 | 每天1次 | 经理分配 | 高级 |
| 成交客户 | VIP客户 | 每月1次 | 专人负责 | 最高 |
| 流失客户 | 待挽回客户 | 暂停跟进 | 主管处理 | 受限 |

### 流程表达（常用于带有流程）
```mermaid
graph TD
    A[销售机会] --> B{金额大小？}
    B -->|>10万| C[需要经理审批]
    B -->|≤10万| D[销售代表处理]
    
    C --> E{审批结果？}
    E -->|通过| F[进入商务谈判]
    E -->|拒绝| G[记录拒绝原因]
    
    D --> H{客户类型？}
    H -->|新客户| I[基础流程]
    H -->|老客户| J[简化流程]
    
    F --> K[制定合同]
    I --> L[需求调研]
    J --> M[直接报价]
```
### 数据验证规则
```
用户对输入信息验证
├── 必填字段验证
│   ├── 客户名称：不能为空，长度2-50字符
│   ├── 联系电话：必须为有效手机号或固话
│   └── 邮箱地址：必须为有效邮箱格式
├── 业务逻辑验证
│   ├── 客户编号：系统自动生成，不可重复
│   ├── 创建时间：自动设置为当前时间
│   └── 修改时间：每次更新时自动更新
└── 权限验证
    ├── 创建权限：所有销售代表
    ├── 查看权限：客户负责人+上级
    └── 删除权限：仅限管理员
```

**输出**：
- 结构化的客户档案
- 客户活动时间线
- 客户价值评分

**业务场景**：
```
场景1：创建新客户
给定：销售代表已登录系统
当：填写客户基本信息并保存
那么：系统生成客户档案并分配唯一编号

场景2：查看客户详情
给定：系统中存在客户档案
当：销售代表点击客户名称
那么：显示客户详细信息和互动历史
```

#### 📋 功能：xxxx
**功能描述**：xxxx

**输入**：
- xxx
**规则**

**输出**：
- xxx

**业务场景**：
```
场景1：xxx

场景2：xxx
```
---

## 7. 数据模型

### 7.1 核心实体关系
```mermaid
erDiagram
    USER ||--o{ CUSTOMER : manages
    CUSTOMER ||--o{ OPPORTUNITY : has
    OPPORTUNITY ||--o{ ACTIVITY : contains
    USER ||--o{ ACTIVITY : performs
    CUSTOMER ||--o{ CONTACT : includes
    
    USER {
        int id
        string name
        string email
        string role
        datetime created_at
    }
    
    CUSTOMER {
        int id
        string name
        string company
        string phone
        string email
        string industry
        int owner_id
        datetime created_at
    }
    
    OPPORTUNITY {
        int id
        string title
        decimal amount
        string stage
        int customer_id
        datetime close_date
        datetime created_at
    }
    
    ACTIVITY {
        int id
        string type
        string description
        int customer_id
        int user_id
        datetime activity_date
    }
    
    CONTACT {
        int id
        string name
        string position
        string phone
        string email
        int customer_id
    }
```

### 7.2 数据表
#### 客户表 (customers)
| 字段名| 字段编码 | 类型 | 长度 | 必填 | 默认值 | 说明 | 示例 |
|--------|------|------|------|--------|------|------|
|id | id | int | 11 | 是 | 自增 | 主键 | 1001 |
|名称 | name | varchar | 100 | 是 | - | 客户名称 | 阿里巴巴 |
|公司 | company | varchar | 200 | 否 | - | 公司名称 | 阿里巴巴集团 |

### 7.3 数据字典
#### 客户类型 (type)
| 名称| 编码 | 
| 未实名| 0 | 
| 实名| 1 | 

---

## 8. 界面设计

### 8.1 界面架构
```
应用界面结构
├── 顶部导航栏
│   ├── Logo
│   ├── 主导航菜单
│   └── 用户菜单
├── 侧边栏（可收缩）
│   ├── 客户管理
│   ├── 销售管理
│   ├── 报表中心
│   └── 系统设置
└── 主内容区
    ├── 面包屑导航
    ├── 页面标题和操作按钮
    └── 具体功能区域
```

### 8.2 关键页面设计
#### 📱 客户列表页
**页面元素**：
- 搜索框和筛选器
- 客户列表（表格或卡片视图）
- 分页控件
- 快速操作按钮

**交互流程**：
```mermaid
graph LR
    A[进入客户列表] --> B[加载客户数据]
    B --> C[显示客户列表]
    C --> D[用户操作]
    D --> E{操作类型}
    E -->|搜索| F[更新列表]
    E -->|查看| G[跳转详情页]
    E -->|编辑| H[打开编辑弹窗]
    E -->|删除| I[确认删除]
    F --> C
    I --> J[删除成功]
    J --> C
```

#### 📊 客户详情页
**页面布局**：
```
┌─────────────────────────────────────┐
│ 客户基本信息卡片                     │
├─────────────────────────────────────┤
│ 联系人信息 │ 销售机会 │ 跟进记录    │
├─────────────────────────────────────┤
│ 客户活动时间线                       │
└─────────────────────────────────────┘
```

---

## 9. 技术需求

### 9.1 技术架构
```mermaid
graph TB
    subgraph "前端层"
        A[Vue.js + Element UI]
        B[移动端 H5]
    end
    
    subgraph "API网关层"
        C[Nginx + Kong]
    end
    
    subgraph "应用层"
        D[Node.js + Express]
        E[用户服务]
        F[客户服务]
        G[销售服务]
    end
    
    subgraph "数据层"
        H[MySQL 8.0]
        I[Redis缓存]
        J[文件存储]
    end
    
    A --> C
    B --> C
    C --> D
    D --> E
    D --> F
    D --> G
    E --> H
    F --> H
    G --> H
    D --> I
    D --> J
```

### 9.2 技术选型
本次需求引入新的组件或对现有组件的变更

| 技术栈 | 选择 | 版本 | 说明 |
|--------|------|------|------|
| 前端框架 | Vue.js | 3.x | 响应式、组件化开发 |

### 9.3 外部依赖
本次需求对外部系统依赖

#### 后端依赖
| 组件 | 提供方式（hsf、http、pop...） | 版本 | 坐标 | 说明 |
| xxx | hsf | hsf:1.0.0 | maven坐标 | 最这个组件的说明 |

#### 前端依赖
| 组件 | HTTP方法 | 版本 | URL | 说明 |
| xxx | GET | 1.0.0 | URL | 最这个组件的说明 |


### 9.4 后端接口
根据功能和业务实体列出完成这个需求对后端接口的诉求

#### 服务名称（Java类名）：CustomerService
##### 接口X：（方法名）：getCustomerById
  入参描述：
  出参描述：
  规则：
##### 接口X：（方法名）：xxx
  入参描述：
  出参描述：
  规则：


---


## 10. 测试需求

### 10.1 测试策略
```mermaid
graph TD
    A[测试金字塔] --> B[单元测试 70%]
    A --> C[集成测试 20%]
    A --> D[端到端测试 10%]
    
    B --> B1[函数测试]
    B --> B2[组件测试]
    
    C --> C1[API测试]
    C --> C2[数据库测试]
    
    D --> D1[用户场景测试]
    D --> D2[浏览器兼容性测试]
```

### 10.2 测试用例示例
#### 🧪 功能测试用例：创建客户
```
测试用例：TC001-创建客户档案
前置条件：用户已登录系统
测试步骤：
1. 点击"新增客户"按钮
2. 填写客户名称："测试公司"
3. 填写联系电话："13812345678"
4. 填写邮箱："<EMAIL>"
5. 点击"保存"按钮

预期结果：
✓ 客户档案创建成功
✓ 显示成功提示信息
✓ 自动生成客户编号
✓ 跳转到客户详情页

测试数据：
- 有效客户名称：测试公司
- 有效手机号：13812345678
- 有效邮箱：<EMAIL>
```

### 10.3 自动化测试
```javascript
// 单元测试示例
describe('客户服务', () => {
  test('应该成功创建客户', async () => {
    const customerData = {
      name: '测试公司',
      phone: '13812345678',
      email: '<EMAIL>'
    };
    
    const result = await customerService.create(customerData);
    
    expect(result.success).toBe(true);
    expect(result.data.id).toBeDefined();
    expect(result.data.name).toBe('测试公司');
  });
});
```

---

## 11. 验收标准

### 11.1 完成定义 (Definition of Done)
- [ ] 功能开发完成
- [ ] 单元测试通过率≥90%
- [ ] 代码审查通过
- [ ] 集成测试通过
- [ ] 产品负责人验收通过
- [ ] 文档更新完成
- [ ] 部署到测试环境
- [ ] 无阻塞性缺陷

### 11.2 验收测试场景
#### 场景1：端到端客户管理流程
```gherkin
Feature: 客户管理
  As a 销售代表
  I want to 管理客户信息
  So that 我可以提高销售效率

  Scenario: 成功创建和管理客户
    Given 我已登录系统
    When 我创建一个新客户"阿里巴巴"
    And 我添加联系人信息
    And 我创建一个销售机会
    Then 我应该能在客户列表中看到该客户
    And 我应该能查看完整的客户详情
    And 我应该能看到相关的销售机会
```

### 11.3 发布标准
- **功能完整性**：所有计划功能已实现
- **性能达标**：满足性能需求指标
- **安全检查**：通过安全扫描
- **用户验收**：核心用户验收通过
- **文档齐全**：用户手册和技术文档完整

---

## 13. 附录

### 13.1 术语表
| 术语 | 英文 | 定义 | 示例 |
|------|------|------|------|
| 客户 | Customer | 公司的潜在或现有客户 | 阿里巴巴集团 |
| 销售机会 | Opportunity | 可能带来收入的销售机会 | 云服务器采购项目 |
| 转化率 | Conversion Rate | 从潜在客户到成交客户的比例 | 20% |
| 生命周期价值 | LTV | 客户在整个生命周期的价值 | 10万元 |
