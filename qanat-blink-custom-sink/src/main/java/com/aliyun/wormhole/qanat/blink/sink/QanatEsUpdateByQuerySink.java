package com.aliyun.wormhole.qanat.blink.sink;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.nio.entity.NStringEntity;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.client.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class QanatEsUpdateByQuerySink extends QanatEsSinkBase {

    private final static Logger LOG = LoggerFactory.getLogger(QanatEsUpdateByQuerySink.class);

    @Override
    public void onAddData(Map<String, Object> inputData) throws IOException {
        updateByQuery(index, type, mapping, inputData);
    }

    private String updateByQuery(String index, String type, String mapping, Map<String, Object> inputData) throws IOException {
        JSONObject mappingJson = JSONObject.parseObject(mapping);
        String updateFieldNames = (String)inputData.get("updateFieldName");
        String updateValues = (String)inputData.get("updateValue");
        String updateOldValues = (String)inputData.get("updateOldValue");
        String queryFieldName = (String)inputData.get("queryFieldName");
        String queryValue = (String)inputData.get("queryValue");
        
        String source = "";
        String nestedField = "";
        if (queryFieldName.indexOf(".") >= 0) {
            nestedField = queryFieldName.split("\\.")[0];
        }
        String[] updateFieldNameArray = updateFieldNames.split("`");
        String[] updateValueArray = updateValues.split("`");
        String[] updateOldValueArray = updateOldValues.split("`");
        for (int i = 0; i < updateFieldNameArray.length; i++) {
            String updateFieldName = updateFieldNameArray[i];
            String updateValue = updateValueArray[i];
            String updateOldValue = StringUtils.isNotBlank(updateOldValues) ? updateOldValueArray[i] : "";
            JSONObject fieldJson = mappingJson.getJSONObject(updateFieldName);
            if (fieldJson.containsKey("array") && updateFieldName.equals(queryFieldName)) {
                source = "for(def i=0;i<ctx._source." + updateFieldName + ".length;i++){def a=ctx._source." + updateFieldName + "[i];if(a=='" + updateOldValue + "'){ctx._source." + updateFieldName + "[i]='" + updateValue + "';break;}}";
            } if (fieldJson.containsKey("array") && !updateFieldName.equals(queryFieldName)) {
                JSONArray jsonArray = new JSONArray();
                String[] updateValueTokens = updateValue.split("\\|");
                for (String value : updateValueTokens) {
                    jsonArray.add(value);
                }
                source += "ctx._source." + updateFieldName + "=" + jsonArray.toJSONString() + ";";
            } else {
                source += "ctx._source." + updateFieldName + "='" + updateValue + "';";
            }
        }
        Map<String, Object> bodyMap = new HashMap<>();
        Map<String, Object> scriptMap = new HashMap<>();
        bodyMap.put("script", scriptMap);
        Map<String, Object> queryMap = new HashMap<>();
        bodyMap.put("query", queryMap);
        scriptMap.put("source", source);
        if (StringUtils.isNotBlank(nestedField)) {
            Map<String, Object> nestedMap = new HashMap<>();
            queryMap.put("nested", nestedMap);
            nestedMap.put("path", nestedField);
            Map<String, Object> nestedQueryMap = new HashMap<>();
            nestedMap.put("query", nestedQueryMap);
            Map<String, Object> termMap = new HashMap<>();
            nestedQueryMap.put("term", termMap);
            termMap.put(queryFieldName, queryValue);
        } else {
            Map<String, Object> termMap = new HashMap<>();
            queryMap.put("term", termMap);
            termMap.put(queryFieldName, queryValue);
            
        }
        String body = JSON.toJSONString(bodyMap);
        HttpEntity entity = new NStringEntity(body, ContentType.APPLICATION_JSON);
        Response resp = getLowLevelClient().performRequest("POST", "/" + index + "/" + type + "/_update_by_query?retry_on_conflict=100", Collections.emptyMap(), entity);
        String content = EntityUtils.toString(resp.getEntity());
        LOG.info("resp:{}", content);
        return content;
    }

    @Override
    public String getName() {
        return "QanatEsUpdateByQuerySink";
    }

    @Override
    public void onInit() {
        // TODO Auto-generated method stub
        
    }

    @Override
    public void onSync() {
        // TODO Auto-generated method stub
        
    }

    @Override
    public void onClose() {
        // TODO Auto-generated method stub
        
    }
    
    public static void main(String [] args) {
        QanatEsUpdateByQuerySink sink = new QanatEsUpdateByQuerySink();
        try {
            String mapping = "{\n" + 
                "        \"id\": {\"type\":\"long\"},\n" + 
                "        \"target_user_cid\": {\"type\":\"long\"},\n" + 
                "        \"target_user_uid\": {\"type\":\"long\"},\n" + 
                "        \"name\": {\"type\": \"text\"},\n" + 
                "        \"content\": {\"type\": \"text\"},\n" + 
                "        \"template_id\": {\"type\":\"long\"},\n" + 
                "        \"category_id\": {\"type\":\"varchar\"},\n" + 
                "        \"creator\": {\"type\": \"text\"},\n" + 
                "        \"gmt_create\": {\"type\":\"date\",\"format\": \"yyyy-MM-dd HH:mm:ss\"},\n" + 
                "        \"gmt_modified\": {\"type\":\"date\",\"format\": \"yyyy-MM-dd HH:mm:ss\"},\n" + 
                "        \"closed_time\": {\"type\":\"date\",\"format\": \"yyyy-MM-dd HH:mm:ss\"},\n" + 
                "        \"follow_up_time\": {\"type\":\"date\",\"format\": \"yyyy-MM-dd HH:mm:ss\"},\n" + 
                "        \"status\": {\"type\":\"short\"},\n" + 
                "        \"priority\": {\"type\":\"short\"},\n" + 
                "        \"photoscope_data_id\": {\"type\": \"long\"},\n" + 
                "        \"scheduled_start_date\": {\"type\": \"date\",\"format\": \"yyyy-MM-dd HH:mm:ss\"},\n" + 
                "        \"scheduled_end_date\": {\"type\": \"date\",\"format\": \"yyyy-MM-dd HH:mm:ss\"},\n" + 
                "        \"source_id\": {\"type\": \"text\"},\n" + 
                "        \"source_code\": {\"type\": \"text\"},\n" + 
                "        \"schedule_template_id\": {\"type\": \"long\"},\n" + 
                "        \"customerName\": {\"type\": \"text\"},\n" + 
                "        \"customerGc\": {\"type\": \"text\"},\n" + 
                "        \"taskCustomer\": {\n" + 
                "          \"type\": \"nested\",\n" + 
                "          \"array\": \"1\",\n" + 
                "          \"properties\": {\n" + 
                "            \"id\": {\"type\":\"long\"},\n" + 
                "            \"gmt_create\": {\"type\":\"date\",\"format\": \"yyyy-MM-dd HH:mm:ss\"},\n" + 
                "            \"gmt_modified\": {\"type\":\"date\",\"format\": \"yyyy-MM-dd HH:mm:ss\"},\n" + 
                "            \"cid\": {\"type\": \"long\"},\n" + 
                "            \"contact_person_id\": {\"type\": \"long\"},\n" + 
                "            \"address\": {\"type\": \"text\"}\n" + 
                "          }\n" + 
                "        },\n" + 
                "        \"taskScheduleType\": {\n" + 
                "          \"type\": \"nested\",\n" + 
                "          \"array\": \"1\",\n" + 
                "          \"properties\": {\n" + 
                "            \"id\": {\"type\":\"long\"},\n" + 
                "            \"gmt_create\": {\"type\":\"date\",\"format\": \"yyyy-MM-dd HH:mm:ss\"},\n" + 
                "            \"gmt_modified\": {\"type\":\"date\",\"format\": \"yyyy-MM-dd HH:mm:ss\"},\n" + 
                "            \"task_schedule_id\": {\"type\": \"long\"},\n" + 
                "            \"first_type\": {\"type\": \"text\"},\n" + 
                "            \"sec_type\": {\"type\": \"text\"}\n" + 
                "          }\n" + 
                "        },\n" + 
                "        \"taskSchedule\": {\n" + 
                "          \"type\": \"nested\",\n" + 
                "          \"array\": \"1\",\n" + 
                "          \"properties\": {\n" + 
                "            \"id\": {\"type\":\"long\"},\n" + 
                "            \"gmt_create\": {\"type\":\"date\",\"format\": \"yyyy-MM-dd HH:mm:ss\"},\n" + 
                "            \"gmt_modified\": {\"type\":\"date\",\"format\": \"yyyy-MM-dd HH:mm:ss\"},\n" + 
                "            \"set_expire_done\": {\"type\": \"integer\"},\n" + 
                "            \"remind_type\": {\"type\": \"text\"},\n" + 
                "            \"ding_remind_type\": {\"type\": \"text\"},\n" + 
                "            \"task_batch_id\": {\"type\": \"long\"}\n" + 
                "          }\n" + 
                "        },\n" + 
                "        \"taskBatch\": {\n" + 
                "          \"type\": \"nested\",\n" + 
                "          \"array\": \"1\",\n" + 
                "          \"properties\": {\n" + 
                "            \"id\": {\"type\":\"long\"},\n" + 
                "            \"gmt_create\": {\"type\":\"date\",\"format\": \"yyyy-MM-dd HH:mm:ss\"},\n" + 
                "            \"gmt_modified\": {\"type\":\"date\",\"format\": \"yyyy-MM-dd HH:mm:ss\"},\n" + 
                "            \"handle_type\": {\"type\": \"text\"},\n" + 
                "            \"success_num\": {\"type\": \"integer\"},\n" + 
                "            \"fail_num\": {\"type\": \"integer\"}\n" + 
                "          }\n" + 
                "        },\n" + 
                "        \"taskResponsiblePerson\": {\n" + 
                "          \"type\": \"nested\",\n" + 
                "          \"array\": \"1\",\n" + 
                "          \"properties\": {\n" + 
                "            \"id\": {\"type\":\"long\"},\n" + 
                "            \"gmt_create\": {\"type\":\"date\",\"format\": \"yyyy-MM-dd HH:mm:ss\"},\n" + 
                "            \"gmt_modified\": {\"type\":\"date\",\"format\": \"yyyy-MM-dd HH:mm:ss\"},\n" + 
                "            \"is_deleted\": {\"type\": \"integer\"},\n" + 
                "            \"responsible_empid\": {\"type\": \"text\"},\n" + 
                "            \"emp_name\": {\"type\": \"text\"},\n" + 
                "            \"rp_type\": {\"type\": \"short\"},\n" + 
                "            \"emp_nick_name\": {\"type\": \"text\"}\n" + 
                "          }\n" + 
                "        },\n" + 
                "        \"team_path\": {\"type\": \"text\", \"array\":\"1\"}\n" + 
                "    }";
            Map<String, Object> inputData = new HashMap<>();
            inputData.put("updateFieldName", "target_user_cid`customerName");
            inputData.put("updateValue", "1112547480`xxxxxx");
            inputData.put("updateOldValue", "");
            inputData.put("queryFieldName", "taskResponsiblePerson.responsible_empid");
            inputData.put("queryValue", "WB270346");
            sink.initEsClient("es-cn-xnr6k24253ywffsao.public.elasticsearch.aliyuncs.com", "9200", "elastic", "ESwormhole0102");
            String content = sink.updateByQuery("sop_task_1", "idx", mapping, inputData);
            System.out.println(content);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}   