package com.aliyun.wormhole.qanat.blink.sink;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.blink.streaming.connector.custom.api.CustomSinkBase;
import com.alibaba.blink.streaming.connectors.common.util.BlinkStringUtil;
import com.alibaba.blink.streaming.connectors.common.util.ConnectionPool;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatAdb3DeleteV2Sink extends CustomSinkBase {

    private final static Logger LOG = LoggerFactory.getLogger(QanatAdb3DeleteV2Sink.class);

    private String url;
    private String dbName;
    private String tableName;
    private String username;
    private String password;
    private Connection connection = null;
    private transient DruidDataSource dataSource;
	private static ConnectionPool<DruidDataSource> dataSourcePool = new ConnectionPool<>();
	private String dataSourceKey = "";
    private int retryIntervalMs = 200;
    private int maxRetryTime = 3;
    
    @Override
    public void open(int i, int i1) throws IOException {
        LOG.info(this.getName() + " open({},{})", i, i1);
        try {
        	dbName = userParamsMap.get("dbname");
            tableName = userParamsMap.get("tablename");
    		
    		if (BlinkStringUtil.isNotEmpty(dbName)) {
                String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
                JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
                url = dbMetaJson.getString("jdbcUrl");
                username = dbMetaJson.getString("username");
                password = dbMetaJson.getString("password");
    		} else {
	            url = userParamsMap.get("url");
	            username = userParamsMap.get("username");
	            password = userParamsMap.get("password");
    		}
            
            synchronized (QanatAdb3AuthV2Sink.class) {
    			dataSourceKey = url;
    			if (dataSourcePool.contains(dataSourceKey)){
    				dataSource = dataSourcePool.get(dataSourceKey);
    				if (dataSource  == null) {
        				buildDatasource();
    				}
    			} else {
    				buildDatasource();
    			}
    			connection = dataSource.getConnection();
            }
        } catch (Exception e) {
            LOG.error("sql exec failed:{}", e.getMessage(), e);
        }
    }
    
    private void buildDatasource() throws SQLException {
		dataSource = new DruidDataSource();
		dataSource.setUrl(url);
		dataSource.setUsername(username);
		dataSource.setPassword(password);
		dataSource.setDriverClassName("com.mysql.jdbc.Driver"); // com.***.***.**.driver
		dataSource.setMaxActive(5);
		dataSource.setInitialSize(1);
		dataSource.setMaxWait(60000);
		dataSource.setMinIdle(0);
		dataSource.setTestWhileIdle(true);
		dataSource.setTestOnBorrow(true);
		dataSource.setTestOnReturn(true);
		dataSource.setRemoveAbandoned(true);
		dataSource.setValidationQuery("select 1");
		dataSource.setTimeBetweenEvictionRunsMillis(90000);
		dataSource.setMinEvictableIdleTimeMillis(18000000);
		dataSource.init();

		dataSourcePool.put(dataSourceKey, dataSource);
	}

    @Override
    public void writeAddRecord(Row row) throws IOException {
        LOG.info("writeAddRecord start");
        String traceId = null;
        Statement statement = null;
        try {
            Map<String, Object> inputData = getInputData(row, rowTypeInfo);
            LOG.info("inputData={}", JSON.toJSONString(inputData));
            List<String> whereClauses = new ArrayList<>();
            for (String field : inputData.keySet()) {
            	if ("__traceId__".equalsIgnoreCase(field)) {
            		traceId = String.valueOf(inputData.get(field));
            		continue;
            	}
            	whereClauses.add(field + "='" + String.valueOf(inputData.get(field)) + "'"); 
            }
            if (CollectionUtils.isNotEmpty(whereClauses)) { 
	            String sql = "DELETE FROM " + tableName + " WHERE " + StringUtils.join(whereClauses, " AND ");
	            LOG.info("{} dbName={} sql={}", traceId, dbName, sql);
	    		int retryTime = 0;
	            long startTs = System.currentTimeMillis();
	    		while (retryTime < maxRetryTime) {
	    			try {
			            if (connection == null || connection.isClosed()) {
			                long startTsConn = System.currentTimeMillis();
			            	try {
			            		connection = dataSource.getConnection();
			            	} catch(Exception e) {
			            		buildDatasource();
			            		connection = dataSource.getConnection();
			            	}
				            LOG.info("{} retry:{} reconnect to {} cost={}", traceId, retryTime, dbName, System.currentTimeMillis()-startTsConn);
			            }
			            statement = connection.createStatement();
			            int execCnt = statement.executeUpdate(sql);
			            LOG.info("{} after dbName={}, execCnt={}, cost={}", traceId, dbName, execCnt, System.currentTimeMillis() - startTs);
			            break;
	    			} catch (SQLException e) {
	    	            LOG.error("{} dbName:{} sqlexception with retry:{} error:{}", traceId, dbName, retryTime, e.getMessage(), e);
						retryTime++;
						if (retryTime == maxRetryTime) {
							throw new RuntimeException(e);
						}
	    				try {
							Thread.sleep(retryIntervalMs * retryTime);
	    				} catch (Exception e1) {
	    					//ignore
	    				}
	    			} finally {
	    	            if (statement != null) {
	    	                try {
	    	                    statement.close();
	    	                } catch (SQLException e) {
	    	                    LOG.error("statement close failed", e);
	    	                }
	    	            }
	    	            if (connection != null) {
	    	                try {
	    	                	connection.close();
	    	                } catch (SQLException e) {
	    	                    LOG.error("connection close failed", e);
	    	                }
	    	            }
	    				
	    			}
	    		}
        	}
            LOG.info("{} writeAddRecord finish", traceId);
        } catch(Exception e) {
            LOG.error("{} writeAddRecord failed, error={}", traceId, e.getMessage(), e);
        } 
    }

    @Override
    public void writeDeleteRecord(Row row) throws IOException {
        
    }
    
    private Map<String, Object> getInputData(Row row, RowTypeInfo rowTypeInfo) {
        Map<String, Object> inputData = new HashMap<>();
        for (String fieldName : rowTypeInfo.getFieldNames()) {
            Object value = row.getField(rowTypeInfo.getFieldIndex(fieldName));
            if (value instanceof java.sql.Timestamp) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String str = df.format((java.sql.Timestamp)value);
                value = str;
            }
            if (value instanceof java.math.BigDecimal) {
                value = ((java.math.BigDecimal)value).doubleValue();
            }
            inputData.put(fieldName, value);
        }
        return inputData;
    }

    @Override
    public void sync() throws IOException {
    }

    @Override
    public void close() throws IOException {
        if (connection != null) {
            try {
            	connection.close();
            } catch (SQLException e) {
            	connection = null;
                LOG.error("conn close failed", e);
            }
        }
    }

    @Override
    public String getName() {
        return "QanatAdb3DeleteSink";
    }
}
