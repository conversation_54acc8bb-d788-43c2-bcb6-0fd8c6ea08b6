package com.aliyun.wormhole.qanat.blink.sink;

import java.io.IOException;
import java.net.InetAddress;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.blink.streaming.connector.custom.api.CustomSinkBase;
import com.alibaba.blink.streaming.connectors.common.util.BlinkStringUtil;
import com.alibaba.blink.streaming.connectors.common.util.ConnectionPool;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.blink.util.QanatCustomUtil;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.aliyun.wormhole.qanat.drc.event.DbEventInfo;
import com.aliyun.wormhole.qanat.stream.event.StreamEvent;
import com.aliyun.wormhole.qanat.stream.event.export.EventExporter;
import com.aliyun.wormhole.qanat.stream.event.export.KafkaEventExporter;
import com.aliyun.wormhole.qanat.stream.event.export.MetaqEventExporter;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatMdpAdb3Sink extends CustomSinkBase {

    private final static Logger LOG = LoggerFactory.getLogger(QanatMdpAdb3Sink.class);

    private String url;
    private String tableName;
    private String username;
    private String password;
    private Connection conn = null;
    private String streamEvent = null;
    private EventExporter exporter;
    private String eventTag;
    
    private transient DruidDataSource dataSource;
	private static ConnectionPool<DruidDataSource> dataSourcePool = new ConnectionPool<>();
	private String dataSourceKey = "";
	private String streamType = null;
    
    @Override
    public void open(int i, int i1) throws IOException {
        LOG.info(this.getName() + " open({},{})", i, i1);
        try {
        	String dbName = userParamsMap.get("dbname");
            tableName = userParamsMap.get("tablename");
    		
    		if (BlinkStringUtil.isNotEmpty(dbName)) {
                String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
                JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
                url = dbMetaJson.getString("jdbcUrl");
                username = dbMetaJson.getString("username");
                password = dbMetaJson.getString("password");
    		} else {
	            url = userParamsMap.get("url");
	            username = userParamsMap.get("username");
	            password = userParamsMap.get("password");
    		}
            
            synchronized (QanatMdpAdb3Sink.class) {
        			dataSourceKey = url + username + password + tableName;
        			if (dataSourcePool.contains(dataSourceKey)){
        				dataSource = dataSourcePool.get(dataSourceKey);
        			} else {
        				dataSource = new DruidDataSource();
        				dataSource.setUrl(url);
        				dataSource.setUsername(username);
        				dataSource.setPassword(password);
        				dataSource.setDriverClassName("com.mysql.jdbc.Driver");
        				dataSource.setMaxActive(40);
        				dataSource.setInitialSize(1);
        				dataSource.setMaxWait(15000);//默认为15s
        				dataSource.setMinIdle(0);
        				dataSource.setTestWhileIdle(true);
        				dataSource.setTestOnBorrow(false);
        				dataSource.setTestOnReturn(false);
        				dataSource.setRemoveAbandoned(false);
        				dataSource.setValidationQuery("select 1");
        				dataSource.setTimeBetweenEvictionRunsMillis(180000);
        				dataSource.setMinEvictableIdleTimeMillis(3600000);
        				dataSource.init();
    
        				dataSourcePool.put(dataSourceKey, dataSource);
        			}
        			conn = dataSource.getConnection();
            }
            
            streamEvent = userParamsMap.get("streamevent");
            streamEvent = StringUtils.isBlank(streamEvent) ? "enable" : streamEvent;
            if ("enable".equalsIgnoreCase(streamEvent)) {
            	streamType = userParamsMap.get("streamtype");
                String eventTopic = userParamsMap.get("eventtopic");
            	if ("kafka".equals(streamType)) {
                    String eventServer = userParamsMap.get("eventserver");
    	            exporter = new KafkaEventExporter(eventTopic, eventServer);
    	            exporter.init();
    	            LOG.info("KafkaEventExporter {}:{} inited", eventTopic, eventServer);
            	} else {
	                String eventUnit = userParamsMap.get("eventunit");
	                eventTag = userParamsMap.get("eventtag");
	                if (StringUtils.isBlank(eventTopic)) {
	                    eventTopic = "QANAT_BLINK_ADB3_SINK_LOG_TOPIC";
	                }
	                String eventGroup = userParamsMap.get("eventgroup");
	                if (StringUtils.isBlank(eventGroup)) {
	                    eventGroup = "PID_" + getName() + "_" + tableName + "_" + i + "_" + i1 + "_" + InetAddress.getLocalHost().getHostName() + "_" + System.currentTimeMillis();
	                }
	                exporter = new MetaqEventExporter(eventTopic, eventGroup, eventUnit);
	                exporter.init();
	                LOG.info("MetaqEventExporter {}:{}:{} inited", eventTopic, eventGroup, eventUnit);
            	}
            }
        } catch (Exception e) {
            LOG.error("sql exec failed:{}", e.getMessage(), e);
        }
    }

    @Override
    public void writeAddRecord(Row row) throws IOException {
        LOG.info("writeAddRecord start");
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            Map<String, Object> inputData = getInputData(row, rowTypeInfo);
            LOG.info("inputDat={}", JSON.toJSONString(inputData));
            String msg = String.valueOf(inputData.get("msg"));
            String traceId = String.valueOf(inputData.get("trace_id"));
            
            if (conn == null || conn.isClosed()) {
            	conn = dataSource.getConnection();
            }
            DbEventInfo event = JSON.parseObject(msg, DbEventInfo.class);
            String sql = QanatCustomUtil.buildAdb3SqlFromDbEventForMDP(tableName, event);
            LOG.info("sql={}", sql);
            statement = conn.createStatement();
            int execCnt = statement.executeUpdate(sql);
            LOG.info("after sql={}, execCnt={}", sql, execCnt);
            
            if ("enable".equalsIgnoreCase(streamEvent)) {
                //export event
                StreamEvent streamEvent = JSON.parseObject(msg, StreamEvent.class);
                streamEvent.setTs(System.currentTimeMillis());
                String tag = null;
                if (StringUtils.isBlank(eventTag)) {
                    tag = streamEvent.getDbName() + "__" + streamEvent.getTableName();
                } else {
                    tag = eventTag;
                }
                streamEvent.setTraceId(traceId);
                exporter.export(tag, streamEvent);
            }
            LOG.info("writeAddRecord finish");
        } catch(Exception e) {
            LOG.error("writeAddRecord failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                    resultSet = null;
                    LOG.error("resultSet close failed", e);
                }
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    statement = null;
                    LOG.error("statement close failed", e);
                }
            }
        }
    }

    @Override
    public void writeDeleteRecord(Row row) throws IOException {
        
    }
    
    private Map<String, Object> getInputData(Row row, RowTypeInfo rowTypeInfo) {
        Map<String, Object> inputData = new HashMap<>();
        for (String fieldName : rowTypeInfo.getFieldNames()) {
            Object value = row.getField(rowTypeInfo.getFieldIndex(fieldName));
            if (value instanceof java.sql.Timestamp) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String str = df.format((java.sql.Timestamp)value);
                value = str;
            }
            if (value instanceof java.math.BigDecimal) {
                value = ((java.math.BigDecimal)value).doubleValue();
            }
            inputData.put(fieldName, value);
        }
        return inputData;
    }

    @Override
    public void sync() throws IOException {
    }

    @Override
    public void close() throws IOException {
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                conn = null;
                LOG.error("conn close failed", e);
            }
        }
        if (exporter != null) {
            exporter.close();
        }
    }

    @Override
    public String getName() {
        return "QanatAdb3Sink";
    }
}
