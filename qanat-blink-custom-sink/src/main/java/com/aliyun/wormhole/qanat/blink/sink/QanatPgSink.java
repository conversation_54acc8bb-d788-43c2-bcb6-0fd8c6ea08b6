package com.aliyun.wormhole.qanat.blink.sink;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.blink.streaming.connector.custom.api.CustomSinkBase;
import com.alibaba.blink.streaming.connectors.common.util.BlinkStringUtil;
import com.alibaba.blink.streaming.connectors.common.util.ConnectionPool;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.aliyun.wormhole.qanat.drc.event.DataMessage;
import com.aliyun.wormhole.qanat.drc.event.DbEvent;
import com.aliyun.wormhole.qanat.drc.event.DbEventFieldInfo;
import com.aliyun.wormhole.qanat.drc.event.DbEventInfo;
import com.aliyun.wormhole.qanat.drc.event.FieldType;
import com.aliyun.wormhole.qanat.stream.event.StreamEvent;
import com.aliyun.wormhole.qanat.stream.event.export.EventExporter;
import com.aliyun.wormhole.qanat.stream.event.export.KafkaEventExporter;
import com.aliyun.wormhole.qanat.stream.event.export.MetaqEventExporter;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatPgSink extends CustomSinkBase {

    private final static Logger LOG = LoggerFactory.getLogger(QanatPgSink.class);

    private String url;
    private String tableName;
    private String username;
    private String password;
    private Connection conn = null;
    private String streamEvent = null;
    private EventExporter exporter;
    private String eventTag;
    private String streamType;
    
    private transient DruidDataSource dataSource;
	private static ConnectionPool<DruidDataSource> dataSourcePool = new ConnectionPool<>();
	private String dataSourceKey = "";
    
    @Override
    public void open(int i, int i1) throws IOException {
        LOG.info(this.getName() + " open({},{})", i, i1);
        try {
        	String dbName = userParamsMap.get("dbname");
            tableName = userParamsMap.get("tablename");
            if (BlinkStringUtil.isNotEmpty(dbName)) {
                String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
                JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
                url = dbMetaJson.getString("jdbcUrl");
                username = dbMetaJson.getString("username");
                password = dbMetaJson.getString("password");
    		} else {
	            url = userParamsMap.get("url");
	            username = userParamsMap.get("username");
	            password = userParamsMap.get("password");
    		}
            
            synchronized (QanatAdb3Sink.class) {
    			dataSourceKey = url + username + password + tableName;
    			if (dataSourcePool.contains(dataSourceKey)){
    				dataSource = dataSourcePool.get(dataSourceKey);
    			} else {
    				dataSource = new DruidDataSource();
    				dataSource.setUrl(url);
    				dataSource.setUsername(username);
    				dataSource.setPassword(password);
    				dataSource.setDriverClassName("org.postgresql.Driver");
    				dataSource.setMaxActive(40);
    				dataSource.setInitialSize(1);
    				dataSource.setMaxWait(15000);//默认为15s
    				dataSource.setMinIdle(0);
    				dataSource.setTestWhileIdle(true);
    				dataSource.setTestOnBorrow(false);
    				dataSource.setTestOnReturn(false);
    				dataSource.setRemoveAbandoned(false);
    				dataSource.setValidationQuery("select 1");
    				dataSource.setTimeBetweenEvictionRunsMillis(180000);
    				dataSource.setMinEvictableIdleTimeMillis(3600000);
    				dataSource.init();

    				dataSourcePool.put(dataSourceKey, dataSource);
    			}
    			conn = dataSource.getConnection();

                streamEvent = userParamsMap.get("streamevent");
                streamEvent = StringUtils.isBlank(streamEvent) ? "enable" : streamEvent;
                if ("enable".equalsIgnoreCase(streamEvent)) {
                	streamType = userParamsMap.get("streamtype");
                    String eventTopic = userParamsMap.get("eventtopic");
                	if ("kafka".equals(streamType)) {
                        String eventServer = userParamsMap.get("eventserver");
        	            exporter = new KafkaEventExporter(eventTopic, eventServer);
        	            exporter.init();
        	            LOG.info("KafkaEventExporter {}:{} inited", eventTopic, eventServer);
                	} else {
    	                String eventUnit = userParamsMap.get("eventunit");
    	                eventTag = userParamsMap.get("eventtag");
    	                if (StringUtils.isBlank(eventTopic)) {
    	                    eventTopic = "QANAT_BLINK_ADB3_SINK_LOG_TOPIC";
    	                }
    	                String eventGroup = userParamsMap.get("eventgroup");
    	                if (StringUtils.isBlank(eventGroup)) {
    	                    eventGroup = "PID_" + getName() + "_" + tableName + "_" + i + "_" + i1 + "_" + InetAddress.getLocalHost().getHostName() + "_" + System.currentTimeMillis();
    	                }
    	                exporter = new MetaqEventExporter(eventTopic, eventGroup, eventUnit);
    	                exporter.init();
    	                LOG.info("MetaqEventExporter {}:{}:{} inited", eventTopic, eventGroup, eventUnit);
                	}
                }
            }
        } catch (Exception e) {
            LOG.error("sql exec failed:{}", e.getMessage(), e);
        }
    }

    @Override
    public void writeAddRecord(Row row) throws IOException {
        LOG.info("writeAddRecord start");
        Statement statement = null;
        ResultSet resultSet = null;
        try {
        	Map<String, Object> inputData = getInputData(row, rowTypeInfo);
            LOG.info("inputDat={}", JSON.toJSONString(inputData));
            String msg = String.valueOf(inputData.get("msg"));
            String traceId = String.valueOf(inputData.get("trace_id"));
            
            if (conn == null || conn.isClosed()) {
            	conn = dataSource.getConnection();
            }
            DbEventInfo event = JSON.parseObject(msg, DbEventInfo.class);
            String sql = buildPgSqlFromDbEvent(tableName, event);
            LOG.info("sql={}", sql);
            statement = conn.createStatement();
            int execCnt = statement.executeUpdate(sql);
            LOG.info("after sql={}, execCnt={}", sql, execCnt);
            if (execCnt > 0 && "enable".equalsIgnoreCase(streamEvent)) {
                //export event
                StreamEvent streamEvent = JSON.parseObject(msg, StreamEvent.class);
                streamEvent.setTs(System.currentTimeMillis());
                String tag = null;
                if (StringUtils.isBlank(eventTag)) {
                    tag = streamEvent.getDbName() + "__" + streamEvent.getTableName();
                } else {
                    tag = eventTag;
                }
                streamEvent.setTraceId(traceId);
                exporter.export(tag, streamEvent);
            }
            LOG.info("writeAddRecord finish");
        } catch(Exception e) {
            LOG.error("writeAddRecord failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                    resultSet = null;
                    LOG.error("resultSet close failed", e);
                }
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    statement = null;
                    LOG.error("statement close failed", e);
                }
            }
        }
    }

    @Override
    public void writeDeleteRecord(Row row) throws IOException {
        
    }
    
    private Map<String, Object> getInputData(Row row, RowTypeInfo rowTypeInfo) {
        Map<String, Object> inputData = new HashMap<>();
        for (String fieldName : rowTypeInfo.getFieldNames()) {
            Object value = row.getField(rowTypeInfo.getFieldIndex(fieldName));
            if (value instanceof java.sql.Timestamp) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String str = df.format((java.sql.Timestamp)value);
                value = str;
            }
            if (value instanceof java.math.BigDecimal) {
                value = ((java.math.BigDecimal)value).doubleValue();
            }
            inputData.put(fieldName, value);
        }
        return inputData;
    }

    @Override
    public void sync() throws IOException {
    }

    @Override
    public void close() throws IOException {
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                conn = null;
                LOG.error("conn close failed", e);
            }
        }
    }

    @Override
    public String getName() {
        return "QanatPgSink";
    }
    
    public static String buildPgSqlFromDbEvent(String tableName, DbEventInfo record) {
    		StringBuffer strFieldName = new StringBuffer();
    		StringBuffer strFieldValue = new StringBuffer();
    		String strSQL = null;

    		if (DbEvent.INSERT == record.getEventType()) {
    			for (DbEventFieldInfo field : record.getFieldValues().values()) {
    				DataMessage.Record.Field.Type type = FieldType.parseDrcType(field.getFieldType());
    				if (strFieldName.length() > 0) {
    					strFieldName.append(",");
    				}
    				strFieldName.append("customer.crm_account_info".equalsIgnoreCase(tableName) && "aliyunID".equalsIgnoreCase(field.getFieldName()) ? "aliyun_id" : field.getFieldName());
    				try {
    					if (strFieldValue.length() > 0) {
    						strFieldValue.append(",");
    					}
    					if (null != field.getNewValue()) {
    						parseValue(strFieldValue, field, type);
    					} else {
    						strFieldValue.append("NULL");
    					}
    				} catch (UnsupportedEncodingException e) {
    					continue;
    				}
    			}
    			strSQL = String.format("INSERT INTO %s(%s) VALUES(%s)", tableName, strFieldName.toString(), strFieldValue.toString());
    		}
    		if (DbEvent.DELETE == record.getEventType()) {
    			String conditionSql = null;
    			for (DbEventFieldInfo field : record.getFieldValues().values()) {
    				try {
    					if (record.getPkField().contains(field.getFieldName())) {
    						conditionSql = field.getFieldName() + "="
    								+ field.getNewValue();
    						continue;
    					}
    				} catch (Exception e) {
    					throw new RuntimeException(e.getMessage());
    				}
    			}
    			strSQL = String.format("DELETE FROM %s WHERE %S", tableName, conditionSql);
    		}
    		if (DbEvent.UPDATE == record.getEventType()) {

    			List<String> fieldList = new ArrayList<>();
    			StringBuffer updateSQL = new StringBuffer();
    			String conditionSql = null;
    			for (DbEventFieldInfo field : record.getFieldValues().values()) {
    			    String fieldName = field.getFieldName();
    			    if ("drc_ignore".equals(fieldName)) {
    			        if (field.getNewValue() != null && "1".equals(field.getNewValue())) {
    			            return null;
    			        }
    			    }
    				DataMessage.Record.Field.Type type = FieldType.parseDrcType(field.getFieldType());
    				try {
    					if (record.getPkField().contains(field.getFieldName())) {
    						conditionSql = field.getFieldName() + "="
    								+ field.getNewValue();
    						continue;
    					}
    				} catch (Exception e) {
    					throw new RuntimeException(e.getMessage());
    				}

    				if (!fieldList.contains(field.getFieldName())) {
    					fieldList.add(field.getFieldName());
    				}
    				String filedName = field.getFieldName();
    				try {
    					if (updateSQL.length() > 0) {
    						updateSQL.append(",");
    					}
    					updateSQL.append("customer.crm_account_info".equalsIgnoreCase(tableName) && "aliyunID".equalsIgnoreCase(filedName) ? "aliyun_id" : filedName).append("=");
    					if (null != field.getNewValue()) {
    						parseValue(updateSQL, field, type);
    					} else {
    						updateSQL.append("NULL");
    					}

    				} catch (UnsupportedEncodingException e) {
    					continue;
    				}
    			}
    			if (null != conditionSql) {
    				strSQL = String.format("UPDATE %s SET %s WHERE %S", tableName, updateSQL.toString(),
    						conditionSql);
    			}
    		}
    		return strSQL;
    	}

        private static void parseValue(StringBuffer strFieldValue, DbEventFieldInfo field,
                                      DataMessage.Record.Field.Type type) throws UnsupportedEncodingException {
            String strValue = field.getNewValue();
            if (isDate(type) && strValue.matches("^\\d+$")) {
                strValue = new Timestamp(Long.parseLong(strValue) * 1000).toString();
            }else if(isDate(type) && strValue != null && strValue.matches("^\\d{4}:\\d{2}:\\d{2}$")){ 
    			try { //try parse 2006:06:12
    				DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    				DateFormat dateFormatNew = new SimpleDateFormat("yyyy:MM:dd");
    				strValue = dateFormat.format(dateFormatNew.parse(strValue));
    			}catch(Exception e) {
    				LOG.info("parse date error:" + strValue);
    			}
    		}
            if (isString(type)) {
                strFieldValue.append("'").append(strValue.replace("'", "''")).append("'");
            } else {
            	strFieldValue.append(strValue);
            }
        }

    	private static boolean isString(DataMessage.Record.Field.Type type) {
    		if (type.equals(DataMessage.Record.Field.Type.STRING)
    				|| isDate(type)) {
    			return true;
    		}
    		return false;
    	}
    	
    	private static boolean isDate(DataMessage.Record.Field.Type type) {
    	    if (type.equals(DataMessage.Record.Field.Type.DATE)
    	            || type.equals(DataMessage.Record.Field.Type.DATETIME)
    	            || type.equals(DataMessage.Record.Field.Type.TIMESTAMP)) {
    	        return true;
    	    }
    	    return false;
    	}
    	
    	public static void main(String [] args) {
    		String msg = "{\"dbName\":\"aliyun_crm_sales\",\"eventType\":2,\"fieldSet\":[\"gmt_create\",\"licensenumber\",\"to_cid\",\"country\",\"city\",\"drc_ignore\",\"origin\",\"super_biz_category\",\"key_word\",\"gmt_modified\",\"type\",\"create_empid\",\"last_modified_empid\",\"province\",\"customer_tag\",\"ka\",\"is_del\",\"biz_category\",\"licensetype\",\"s_sign\",\"src\",\"is_formal\",\"risk_level\",\"isconflict\",\"district\",\"nick_name\",\"name\",\"biz_category_sub\",\"category\",\"bid\",\"business_class\",\"cid\"],\"fieldValues\":{\"gmt_create\":{\"fieldName\":\"gmt_create\",\"fieldType\":4,\"newValue\":\"2021-07-01 11:33:21\"},\"licensenumber\":{\"fieldName\":\"licensenumber\",\"fieldType\":5},\"to_cid\":{\"fieldName\":\"to_cid\",\"fieldType\":2},\"country\":{\"fieldName\":\"country\",\"fieldType\":5,\"newValue\":\"HK\"},\"city\":{\"fieldName\":\"city\",\"fieldType\":5},\"drc_ignore\":{\"fieldName\":\"drc_ignore\",\"fieldType\":1,\"newValue\":\"0\"},\"origin\":{\"fieldName\":\"origin\",\"fieldType\":5,\"newValue\":\"account_reg\"},\"super_biz_category\":{\"fieldName\":\"super_biz_category\",\"fieldType\":5},\"key_word\":{\"fieldName\":\"key_word\",\"fieldType\":5,\"newValue\":\"\"},\"gmt_modified\":{\"fieldName\":\"gmt_modified\",\"fieldType\":4,\"newValue\":\"2021-07-01 11:33:21\"},\"type\":{\"fieldName\":\"type\",\"fieldType\":1,\"newValue\":\"0\"},\"create_empid\":{\"fieldName\":\"create_empid\",\"fieldType\":5,\"newValue\":\"task\"},\"last_modified_empid\":{\"fieldName\":\"last_modified_empid\",\"fieldType\":5,\"newValue\":\"task\"},\"province\":{\"fieldName\":\"province\",\"fieldType\":5},\"customer_tag\":{\"fieldName\":\"customer_tag\",\"fieldType\":1,\"newValue\":\"0\"},\"ka\":{\"fieldName\":\"ka\",\"fieldType\":1,\"newValue\":\"0\"},\"is_del\":{\"fieldName\":\"is_del\",\"fieldType\":2,\"newValue\":\"0\"},\"biz_category\":{\"fieldName\":\"biz_category\",\"fieldType\":5},\"licensetype\":{\"fieldName\":\"licensetype\",\"fieldType\":1,\"newValue\":\"0\"},\"s_sign\":{\"fieldName\":\"s_sign\",\"fieldType\":1,\"newValue\":\"0\"},\"src\":{\"fieldName\":\"src\",\"fieldType\":5,\"newValue\":\"website\"},\"is_formal\":{\"fieldName\":\"is_formal\",\"fieldType\":1,\"newValue\":\"0\"},\"risk_level\":{\"fieldName\":\"risk_level\",\"fieldType\":1,\"newValue\":\"0\"},\"isconflict\":{\"fieldName\":\"isconflict\",\"fieldType\":1,\"newValue\":\"0\"},\"district\":{\"fieldName\":\"district\",\"fieldType\":5},\"nick_name\":{\"fieldName\":\"nick_name\",\"fieldType\":5},\"name\":{\"fieldName\":\"name\",\"fieldType\":5,\"newValue\":\"<EMAIL>\"},\"biz_category_sub\":{\"fieldName\":\"biz_category_sub\",\"fieldType\":5},\"category\":{\"fieldName\":\"category\",\"fieldType\":5,\"newValue\":\"abroad\"},\"bid\":{\"fieldName\":\"bid\",\"fieldType\":5,\"newValue\":\"26888\"},\"business_class\":{\"fieldName\":\"business_class\",\"fieldType\":1,\"newValue\":\"0\"},\"cid\":{\"fieldName\":\"cid\",\"fieldType\":2,\"newValue\":\"***********\"}},\"pkField\":[\"cid\"],\"tableName\":\"crm_customer_base_info\",\"ts\":\"1625110401\"}";

            DbEventInfo event = JSON.parseObject(msg, DbEventInfo.class);
            String sql = buildPgSqlFromDbEvent("xxxx", event);
    		System.out.println(sql);
    	}
}
