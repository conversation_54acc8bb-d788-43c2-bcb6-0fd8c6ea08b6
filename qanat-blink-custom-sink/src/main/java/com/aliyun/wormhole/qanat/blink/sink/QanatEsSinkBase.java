package com.aliyun.wormhole.qanat.blink.sink;

import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.blink.streaming.connector.custom.api.CustomSinkBase;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.types.Row;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class QanatEsSinkBase extends CustomSinkBase {

    private final static Logger LOG = LoggerFactory.getLogger(QanatEsSinkBase.class);

    private RestHighLevelClient client = null;
    protected String index;
    protected String type;
    protected String host;
    protected String port;
    protected String mapping;
    protected String username;
    protected String password;
    protected String pk;
    protected JSONObject mappingJson;
    private String maxRetryTimeoutMillis;
    private String connectTimeout;
    private String socketTimeout;
    private String connectionRequestTimeout;
    
    @Override
    public void open(int i, int i1) throws IOException {
        LOG.info(this.getName() + " open({},{})", i, i1);
        try {
            index = userParamsMap.get("es_index");
            type = userParamsMap.get("es_type");
            host = userParamsMap.get("es_host");
            port = userParamsMap.get("es_port");
            username = userParamsMap.get("es_username");
            password = userParamsMap.get("es_password");
            mapping = userParamsMap.get("es_mapping");
            mappingJson = JSONObject.parseObject(mapping);
            if (this.primaryKeys != null && this.primaryKeys.size() > 0) {
                pk = (String)this.primaryKeys.toArray()[0];
            }
            maxRetryTimeoutMillis = userParamsMap.get("es_http_max_retry_timeout_ms");
            connectTimeout = userParamsMap.get("es_http_connect_timeout_ms");
            socketTimeout = userParamsMap.get("es_http_socket_timeout_ms");
            connectionRequestTimeout = userParamsMap.get("es_http_connect_request_timeout_ms");
            initEsClient(host, port, username, password, maxRetryTimeoutMillis, connectTimeout, socketTimeout, connectionRequestTimeout);
            onInit();
            LOG.info("QanatEsSink is inited.");
        } catch(Exception e) {
            LOG.error(this.getName() + " open failed", e);
            this.close();
        }
    }

    protected void initEsClient(String host, String port, String username, String password) {
        initEsClient(host, port, username, password, null, null, null, null);
    }

    protected void initEsClient(String host, String port, String username, String password, String maxRetryTimeoutMillis, String connectTimeout, String socketTimeout, String connectionRequestTimeout) {
        int maxRetryTimeoutMs = maxRetryTimeoutMillis == null ? 30000 : Integer.valueOf(maxRetryTimeoutMillis);
        int connectTimeoutMs = connectTimeout == null ? 30000 : Integer.valueOf(connectTimeout);
        int socketTimeoutMs = socketTimeout == null ? 30000 : Integer.valueOf(socketTimeout);
        int connectionRequestTimeoutMs = connectionRequestTimeout == null ? 10000 : Integer.valueOf(connectionRequestTimeout);
        HttpHost httpHost = new HttpHost(host, Integer.parseInt(port));
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));
        RestClientBuilder builder = RestClient.builder(httpHost);
        builder.setMaxRetryTimeoutMillis(maxRetryTimeoutMs);
        builder.setRequestConfigCallback(requestConfigBuilder -> {
            requestConfigBuilder.setConnectTimeout(connectTimeoutMs);
            requestConfigBuilder.setSocketTimeout(socketTimeoutMs);
            requestConfigBuilder.setConnectionRequestTimeout(connectionRequestTimeoutMs);
            return requestConfigBuilder;
        });
        builder.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
            @Override
            public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
                return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
            }
        }).build();
        client = new RestHighLevelClient(builder);
    }

    @Override
    public void writeAddRecord(Row row) throws IOException {
        LOG.info("writeAddRecord start");
        try {
            Map<String, Object> inputData = getInputData(row, rowTypeInfo);
            LOG.info("inputData={}", JSON.toJSONString(inputData));
            onAddData(inputData);
            LOG.info("writeAddRecord finish");
        } catch(Exception e) {
            LOG.error("writeAddRecord failed", e);
        }
    }

    @Override
    public void writeDeleteRecord(Row row) throws IOException {
        
    }
    
    private Map<String, Object> getInputData(Row row, RowTypeInfo rowTypeInfo) {
        Map<String, Object> inputData = new HashMap<>();
        for (String fieldName : rowTypeInfo.getFieldNames()) {
            Object value = row.getField(rowTypeInfo.getFieldIndex(fieldName));
            if (value instanceof java.sql.Timestamp) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String str = df.format((java.sql.Timestamp)value);
                value = str;
            }
            if (value instanceof java.math.BigDecimal) {
                value = ((java.math.BigDecimal)value).doubleValue();
            }
            inputData.put(fieldName, value);
        }
        return inputData;
    }

    @Override
    public void sync() throws IOException {
        onSync();
    }

    @Override
    public void close() throws IOException {
        onClose();
        if (client != null) {
            client.close();
        }
    }
    
    public abstract void onInit();
    
    public abstract void onAddData(Map<String, Object> inputData) throws IOException;
    
    public abstract void onSync();
    
    public abstract void onClose();
    
    public RestHighLevelClient getHighLevelClient() {
        return this.client;
    }
    
    public RestClient getLowLevelClient() {
        return this.client.getLowLevelClient();
    }
}
