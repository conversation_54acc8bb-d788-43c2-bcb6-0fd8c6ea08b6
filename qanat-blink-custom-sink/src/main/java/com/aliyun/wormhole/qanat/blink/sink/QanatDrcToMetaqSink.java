package com.aliyun.wormhole.qanat.blink.sink;

import java.io.IOException;
import java.net.InetAddress;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.blink.streaming.connector.custom.api.CustomSinkBase;
import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.producer.MessageQueueSelector;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.common.message.Message;
import com.alibaba.rocketmq.common.message.MessageQueue;
import com.aliyun.wormhole.qanat.drc.event.DbEventInfo;
import com.taobao.eagleeye.EagleEye;
import com.taobao.metaq.client.MetaProducer;
import com.taobao.metaq.trace.core.common.MetaQTraceConstants;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatDrcToMetaqSink extends CustomSinkBase {

    private final static Logger LOG = LoggerFactory.getLogger(QanatDrcToMetaqSink.class);

    private String topic;
    private String producerGroup;
    private String tag;
    private String unitName;
    private MetaProducer producer;
    
    @Override
    public void open(int taskNum, int taskCount) throws IOException {
        LOG.info(this.getName() + " open({},{})", taskNum, taskCount);
        try {
        	topic = userParamsMap.get("topic");
        	producerGroup = userParamsMap.get("producergroup");
        	tag = userParamsMap.get("tag");
        	unitName = userParamsMap.get("unitname");
            boolean success = false;
    	    for (int i = 0; i < 10; i++) {
                producer = new MetaProducer(producerGroup);
                try {
                    if (StringUtils.isNotBlank(unitName)) {
                    	LOG.info("metaq unitName:{}", unitName);
                        producer.setUnitName(unitName);
                    }
                    producer.start();
                    LOG.info("metaqProducer inited");
                    success = true;
                    break;
                } catch (Exception e) {
                	LOG.error("metaqProducer init failed", e);
                }
            }
    	    if (!success) {
                throw new RuntimeException("metaqProducer init failed");
    	    }
            LOG.info("{}:{}:{} inited", topic, producerGroup, unitName);
        } catch (Exception e) {
            LOG.error("sql exec failed:{}", e.getMessage(), e);
        }
    }

    @Override
    public void writeAddRecord(Row row) throws IOException {
        LOG.info("writeAddRecord start");
        try {
            Map<String, Object> inputData = getInputData(row, rowTypeInfo);
            LOG.info("inputDat={}", JSON.toJSONString(inputData));
            String msg = String.valueOf(inputData.get("_msg_"));
            tag = inputData.get("_tag_") == null ? tag : String.valueOf(inputData.get("_tag_"));
            
            String traceId = inputData.get("_trace_id_") == null ? null : String.valueOf(inputData.get("_trace_id_"));
            
            DbEventInfo event = JSON.parseObject(msg, DbEventInfo.class);
            this.export(traceId, tag, event);
            LOG.info("writeAddRecord finish");
        } catch(Exception e) {
            LOG.error("writeAddRecord failed", e);
        }
    }
    
	private void export(String traceId, String tag, DbEventInfo event) {
		LOG.info("export({},{})", tag, JSON.toJSONString(event));
		try {
			String pkField = "id";
			if (event.getPkField() != null && event.getPkField().size() > 0) {
				pkField = event.getPkField().iterator().next();
            }
            String pkValue = event.getFieldValues().get(pkField).getNewValue();
            if (StringUtils.isBlank(pkValue)) {
                pkValue = event.getFieldValues().get(pkField).getOldValue();
            }
            String eventJson = JSON.toJSONString(event);
            Message msg = new Message(topic, tag, pkValue, eventJson.getBytes());
            if (traceId != null) {
                msg.putUserProperty(MetaQTraceConstants.TRACE_ID_KEY, traceId);
            } else {
                msg.putUserProperty(MetaQTraceConstants.TRACE_ID_KEY, EagleEye.generateTraceId(InetAddress.getLocalHost().getHostAddress()));
            }
            SendResult sendResult = null;
            if (StringUtils.isNotBlank(pkValue) && StringUtils.isNumeric(pkValue)) {
                sendResult = producer.send(msg, new MessageQueueSelector() {
                    @Override
                    public MessageQueue select(List<MessageQueue> mqs, Message msg, Object arg) {
                        Long id = (Long)arg;
                        Long index = id % mqs.size();
                        return mqs.get(index.intValue());
                    }
                }, Long.valueOf(pkValue));
            } else {
                sendResult = producer.send(msg);
            }
            LOG.info("SendStatus={}, sendMq={}", sendResult.getSendStatus(), eventJson);
        } catch (Exception e) {
        	LOG.error("sendMq failed", e);
        }
	}

    @Override
    public void writeDeleteRecord(Row row) throws IOException {
        
    }
    
    private Map<String, Object> getInputData(Row row, RowTypeInfo rowTypeInfo) {
        Map<String, Object> inputData = new HashMap<>();
        for (String fieldName : rowTypeInfo.getFieldNames()) {
            Object value = row.getField(rowTypeInfo.getFieldIndex(fieldName));
            if (value instanceof java.sql.Timestamp) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String str = df.format((java.sql.Timestamp)value);
                value = str;
            }
            if (value instanceof java.math.BigDecimal) {
                value = ((java.math.BigDecimal)value).doubleValue();
            }
            inputData.put(fieldName, value);
        }
        return inputData;
    }

    @Override
    public void sync() throws IOException {
    }

    @Override
    public void close() throws IOException {
        if (producer != null) {
            try {
            	producer.shutdown();
            } catch (Exception e) {
            	producer = null;
                LOG.error("producer shutdow failed", e);
            }
        }
    }

    @Override
    public String getName() {
        return "QanatDrcToMetaqSink";
    }
}