package com.aliyun.wormhole.qanat.blink.sink;

import com.aliyun.wormhole.qanat.blink.util.QanatCustomUtil;
import org.elasticsearch.action.index.IndexRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

public class QanatEsIndexBulkSink extends QanatEsBulkSinkBase {

    private final static Logger LOG = LoggerFactory.getLogger(QanatEsIndexBulkSink.class);
    
    AtomicInteger count = new AtomicInteger(0);

    @Override
    public void onAddData(Map<String, Object> inputData) {
        int cnt = count.incrementAndGet();
        try {
            LOG.info("onAddData: count={}, id={}", cnt, inputData.get(pk));
            Map<String, Object> docData = QanatCustomUtil.buildDocData(mappingJson, inputData);
            getBulkProcessor().add(new IndexRequest(index, type, docData.get(pk) + "").source(docData));
        } catch(Exception e) {
            LOG.error("onAddData failed", e);
        }
    }

    @Override
    public String getName() {
        return "QanatEsIndexBulkSink";
    }
}   