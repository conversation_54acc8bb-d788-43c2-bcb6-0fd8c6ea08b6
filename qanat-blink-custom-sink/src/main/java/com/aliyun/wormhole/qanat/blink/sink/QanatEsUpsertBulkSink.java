package com.aliyun.wormhole.qanat.blink.sink;

import com.alibaba.fastjson.JSON;

import com.aliyun.wormhole.qanat.blink.util.QanatCustomUtil;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Map;

public class QanatEsUpsertBulkSink extends QanatEsBulkSinkBase {

    private final static Logger LOG = LoggerFactory.getLogger(QanatEsUpsertBulkSink.class);

    @Override
    public void onAddData(Map<String, Object> inputData) throws IOException {
        try {
            Map<String, Object> docData = QanatCustomUtil.buildDocData(mappingJson, inputData);
            LOG.info("docData={}", JSON.toJSONString(docData));
            String src = null;
            if (inputData.get("_script") != null && StringUtils.isNotBlank(inputData.get("_script").toString())) {
                src = String.valueOf(inputData.get("_script")); 
            } else {
                StringBuffer source = new StringBuffer();
                for (String key : docData.keySet()) {
                    source.append("ctx._source.").append(key).append("=").append("params.").append(key).append(";");
                }
                src = source.toString();
            }
            UpdateRequest request = new UpdateRequest(index, type, docData.get(pk) + "");
            Script script = new Script(ScriptType.INLINE, "painless", src, docData);
            request.script(script);
            request.upsert(docData);
            if (inputData.get("_routing") != null && StringUtils.isNotBlank(inputData.get("_routing").toString())) {
                request.routing(String.valueOf(inputData.get("_routing")));
            }
            request.retryOnConflict(100);
            getBulkProcessor().add(request);
        } catch(Exception e) {
            LOG.error("onAddData failed", e);
        }
    }

    @Override
    public String getName() {
        return "QanatEsUpsertBulkSink";
    }
}   