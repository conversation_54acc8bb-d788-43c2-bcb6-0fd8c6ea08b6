package com.aliyun.wormhole.qanat.blink.util;

import java.io.UnsupportedEncodingException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.drc.event.DataMessage;
import com.aliyun.wormhole.qanat.drc.event.DbEvent;
import com.aliyun.wormhole.qanat.drc.event.DbEventFieldInfo;
import com.aliyun.wormhole.qanat.drc.event.DbEventInfo;
import com.aliyun.wormhole.qanat.drc.event.FieldType;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatCustomUtil {
    
    private final static Logger log = LoggerFactory.getLogger(QanatCustomUtil.class);
    
    public static Map<String, Object> buildDocData(JSONObject mappingJson, Map<String, Object> inputData) {
        Map<String, Object> docData = new HashMap<>();
        for (String key : mappingJson.keySet()) {
            JSONObject keyJson = mappingJson.getJSONObject(key);
            if ("nested".equalsIgnoreCase(keyJson.getString("type")) || "join".equalsIgnoreCase(keyJson.getString("type")) || keyJson.containsKey("properties")) {
                List<Map<String, Object>> list = new ArrayList<>();
                if (keyJson.containsKey("dataType")) {
                    if (inputData.get(key) != null) {
                        String[] array = inputData.get(key).toString().split("\\|");
                        for (String obj : array) {
                            Map<String, Object> subDocData = new HashMap<>();
                            String[] keySet = obj.split("\\`");
                            boolean emptyObj = true;
                            for (String entry : keySet) {
                                String [] entryArray = entry.split("\\^");
                                if (entryArray.length == 2 && StringUtils.isNotBlank(entryArray[1])) {
                                    emptyObj = false;
                                    break;
                                }
                            }
                            if (emptyObj) {
                                continue;
                            }
                            for (String entry : keySet) {
                                String [] entryArray = entry.split("\\^");
                                if (entryArray.length == 2) {
                                    if (keyJson.getJSONObject("properties") != null && keyJson.getJSONObject("properties").getJSONObject(entryArray[0]) != null) {
                                        subDocData.put(entryArray[0], getValue(keyJson.getJSONObject("properties").getJSONObject(entryArray[0]).getString("dataType"),  keyJson.getJSONObject("properties").getJSONObject(entryArray[0]).getString("type"), entryArray[1]));
                                    } else {
                                        subDocData.put(entryArray[0], entryArray[1]);
                                    }
                                }
                            }
                            list.add(subDocData);
                        }
                        if ("object".equals(keyJson.getString("dataType")) && list.size() > 0) {
                            docData.put(key, list.get(0));
                        } else if ("object".equals(keyJson.getString("dataType")) && list.size() == 0) {
                            docData.put(key, new HashMap<>());
                        } else {
                            docData.put(key, list);
                        }
                    } else {
                        if ("object".equals(keyJson.getString("dataType")) && list.size() > 0) {
                            docData.put(key, new HashMap<>());
                        } else {
                            docData.put(key, new ArrayList<>());
                        }
                    }
                }
            } else {
                String dataKey = key;
                if (keyJson.containsKey("dataType") && "array".equals(keyJson.getString("dataType"))) {
                    if (inputData.get(key) != null) {
                        String[] array = inputData.get(dataKey).toString().split("\\|");
                        docData.put(key, Arrays.asList(array));
                    } else {
                        docData.put(key, new ArrayList<>());
                    }
                } else {
                    docData.put(key, inputData.get(dataKey));
                }
            }
        }
        return docData;
    }

    private static Object getValue(String dataType, String type, String value) {
        try {
        	if ("array".equalsIgnoreCase(dataType)) {
        		String[] tokens = value.split("\\.");
        		return Arrays.asList(tokens).stream().distinct().collect(Collectors.toList());
        	}	
            switch (type) {
                case "text": return value;
                case "long": return Long.valueOf(value);
                case "boolean": return Boolean.valueOf(value);
                case "float": return Float.valueOf(value);
                case "integer": return Integer.valueOf(value);
                case "date": return Long.valueOf(value);
                default: return value;
            }
        } catch (Exception e) {
            log.error("getValue({},{}) failed", type, value, e);
        }
        return null;
    }
    
    public static String buildRdsSql(String tableName, DbEventInfo event) {
    	return buildRdsSql(tableName, event, null);
    }

    public static String buildRdsSql(String srcTbName, DbEventInfo record, String cols) {
        StringBuffer strFieldName = new StringBuffer();
        StringBuffer strFieldValue = new StringBuffer();
        String strSQL = null;
        List<String> colList = null;
        if (StringUtils.isNotBlank(cols)) {
        	colList = Arrays.asList(cols.split(","));
        }

        if (DbEvent.INSERT == record.getEventType()) {
            for (DbEventFieldInfo field : record.getFieldValues().values()) {
            	if (CollectionUtils.isNotEmpty(colList) && !colList.contains(field.getFieldName())) {
            		continue;
            	}
                DataMessage.Record.Field.Type type = FieldType.parseDrcType(field.getFieldType());
                if (strFieldName.length() > 0) {
                    strFieldName.append(",");
                }
                strFieldName.append(field.getFieldName());
                try {
                    if (strFieldValue.length() > 0) {
                        strFieldValue.append(",");
                    }
                    if (null != field.getNewValue()) {
                        parseValue(strFieldValue, field, type);
                    } else {
                        strFieldValue.append("NULL");
                    }
                } catch (UnsupportedEncodingException e) {
                    continue;
                }
            }
            strSQL = String.format("INSERT INTO %s(%s) VALUES(%s)", srcTbName, strFieldName.toString(),
                strFieldValue.toString());
        }
        if (DbEvent.DELETE == record.getEventType()) {
            String conditionSql = null;
            for (DbEventFieldInfo field : record.getFieldValues().values()) {
            	if (CollectionUtils.isNotEmpty(colList) && !colList.contains(field.getFieldName())) {
            		continue;
            	}
                try {
                    if (record.getPkField().contains(field.getFieldName())) {
                        conditionSql = field.getFieldName() + "="
                            +  (field.getNewValue() != null ? field.getNewValue() : field.getOldValue());
                        continue;
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e.getMessage());
                }
            }
            strSQL = String.format("DELETE FROM %s WHERE %S", srcTbName, conditionSql);
        }
        if (DbEvent.UPDATE == record.getEventType()) {
            StringBuffer updateSQL = new StringBuffer();
            String conditionSql = null;
            for (DbEventFieldInfo field : record.getFieldValues().values()) {
            	if (CollectionUtils.isNotEmpty(colList) && !colList.contains(field.getFieldName())) {
            		continue;
            	}
                String fieldName = field.getFieldName();
                if ("drc_ignore".equals(fieldName)) {
                    if (field.getNewValue() != null && "1".equals(field.getNewValue())) {
                        return null;
                    }
                }
                DataMessage.Record.Field.Type type = FieldType.parseDrcType(field.getFieldType());
                try {
                    if (record.getPkField().contains(field.getFieldName())) {
                        conditionSql = field.getFieldName() + "="
                            + field.getNewValue();
                        continue;
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e.getMessage());
                }
                String filedName = field.getFieldName();
                try {
                    if (updateSQL.length() > 0) {
                        updateSQL.append(",");
                    }
                    updateSQL.append(filedName).append("=");
                    if (null != field.getNewValue()) {
                        parseValue(updateSQL, field, type);
                    } else {
                        updateSQL.append("NULL");
                    }

                } catch (UnsupportedEncodingException e) {
                    continue;
                }
            }
            if (null != conditionSql) {
                strSQL = String.format("UPDATE %s SET %s WHERE %S", srcTbName, updateSQL.toString(),
                    conditionSql);
            }
        }
        return strSQL;
    }

    public static String buildAdb3SqlFromDbEvent(String tableName, DbEventInfo event) {
    	return buildAdb3SqlFromDbEvent(tableName, event, null);
    }

    public static String buildAdb3SqlFromDbEvent(String tableName, DbEventInfo event, String cols) {
        StringBuffer strFieldName = new StringBuffer();
        StringBuffer strFieldValue = new StringBuffer();
        String strSQL = null;
        List<String> colList = null;
        if (StringUtils.isNotBlank(cols)) {
        	colList = Arrays.asList(cols.split(","));
        }
        if (DbEvent.INSERT == event.getEventType()) {
            for (DbEventFieldInfo field : event.getFieldValues().values()) {
            	if (CollectionUtils.isNotEmpty(colList) && !colList.contains(field.getFieldName())) {
            		continue;
            	}
                DataMessage.Record.Field.Type type = FieldType.parseDrcType(field.getFieldType());
                if (strFieldName.length() > 0) {
                    strFieldName.append(",");
                }
                strFieldName.append(field.getFieldName());
                try {
                    if (strFieldValue.length() > 0) {
                        strFieldValue.append(",");
                    }
                    if (null != field.getNewValue()) {
                        parseValue(strFieldValue, field, type);
                    } else {
                        strFieldValue.append("NULL");
                    }
                } catch (UnsupportedEncodingException e) {
                    continue;
                }
            }
            strSQL = String.format("INSERT INTO %s(%s) VALUES(%s)", tableName, strFieldName.toString(),
                strFieldValue.toString());
        }
        if (DbEvent.DELETE == event.getEventType()) {
            String conditionSql = null;
            for (DbEventFieldInfo field : event.getFieldValues().values()) {
            	if (CollectionUtils.isNotEmpty(colList) && !colList.contains(field.getFieldName())) {
            		continue;
            	}
                try {
                    if (event.getPkField().contains(field.getFieldName())) {
                        conditionSql = field.getFieldName() + "="
                            + (field.getNewValue() != null ? field.getNewValue() : field.getOldValue());
                        continue;
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e.getMessage());
                }
            }
            strSQL = String.format("DELETE FROM %s WHERE %S", tableName, conditionSql);
        }
        if (DbEvent.UPDATE == event.getEventType()) {
            for (DbEventFieldInfo field : event.getFieldValues().values()) {
            	if (CollectionUtils.isNotEmpty(colList) && !colList.contains(field.getFieldName())) {
            		continue;
            	}
                DataMessage.Record.Field.Type type = FieldType.parseDrcType(field.getFieldType());
                if (strFieldName.length() > 0) {
                    strFieldName.append(",");
                }
                strFieldName.append(field.getFieldName());
                try {
                    if (strFieldValue.length() > 0) {
                        strFieldValue.append(",");
                    }
                    if (null != field.getNewValue()) {
                        parseValue(strFieldValue, field, type);
                    } else {
                        strFieldValue.append("NULL");
                    }
                } catch (UnsupportedEncodingException e) {
                    continue;
                }
            }
            strSQL = String.format("UPDATE INTO %s(%s) VALUES(%s)", tableName, strFieldName.toString(),
                strFieldValue.toString());
        }
        return strSQL;
    }

    private static void parseValue(StringBuffer strFieldValue, DbEventFieldInfo field,
        DataMessage.Record.Field.Type type) throws UnsupportedEncodingException {
        String strValue = field.getNewValue();
        if (isDate(type) && strValue.matches("^\\d+$")) {
            strValue = new Timestamp(Long.parseLong(strValue) * 1000).toString();
        } else if (isDate(type) && strValue != null && strValue.matches("^\\d{4}:\\d{2}:\\d{2}$")) {
            try { // try parse 2006:06:12
                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                DateFormat dateFormatNew = new SimpleDateFormat("yyyy:MM:dd");
                strValue = dateFormat.format(dateFormatNew.parse(strValue));
            } catch (Exception e) {
                log.info("parse date error:" + strValue);
            }
        }
        if (isString(type)) {
            strFieldValue.append("'").append(strValue.replace("'", "''").replaceAll("\"", "\\\\\\\"")).append("'");
        } else {
            strFieldValue.append(strValue);
        }
    }

    private static boolean isString(DataMessage.Record.Field.Type type) {
        if (type.equals(DataMessage.Record.Field.Type.STRING)
            || isDate(type)) {
            return true;
        }
        return false;
    }

    private static boolean isDate(DataMessage.Record.Field.Type type) {
        if (type.equals(DataMessage.Record.Field.Type.DATE)
            || type.equals(DataMessage.Record.Field.Type.DATETIME)
            || type.equals(DataMessage.Record.Field.Type.TIMESTAMP)) {
            return true;
        }
        return false;
    }

    public static String buildAdb3SqlFromDbEventForMDP(String tableName, DbEventInfo event) {
        StringBuffer strFieldName = new StringBuffer();
        StringBuffer strFieldValue = new StringBuffer();
        String strSQL = null;

        if (DbEvent.INSERT == event.getEventType()) {
            String rowkey = 
                event.getFieldValues().get("object_biz_id").getNewValue() + "_" +
                event.getFieldValues().get("object_domain_code").getNewValue() + "_" +
                event.getFieldValues().get("object_code").getNewValue() + "_" +
                event.getFieldValues().get("tag_domain_code").getNewValue() + "_" +
                event.getFieldValues().get("tag_code").getNewValue() + "_" +
                event.getFieldValues().get("is_deleted").getNewValue();
            strFieldName.append("pk");
            strFieldValue.append("'" + rowkey + "'");
            for (DbEventFieldInfo field : event.getFieldValues().values()) {
                DataMessage.Record.Field.Type type = FieldType.parseDrcType(field.getFieldType());
                if (strFieldName.length() > 0) {
                    strFieldName.append(",");
                }
                strFieldName.append(field.getFieldName());
                try {
                    if (strFieldValue.length() > 0) {
                        strFieldValue.append(",");
                    }
                    if (null != field.getNewValue()) {
                        parseValue(strFieldValue, field, type);
                    } else {
                        strFieldValue.append("NULL");
                    }
                } catch (UnsupportedEncodingException e) {
                    continue;
                }
            }
            strSQL = String.format("INSERT INTO %s(%s) VALUES(%s)", tableName, strFieldName.toString(),
                strFieldValue.toString());
        }
        if (DbEvent.DELETE == event.getEventType()) {
            String rowkey = 
                event.getFieldValues().get("object_biz_id").getOldValue() + "_" +
                event.getFieldValues().get("object_domain_code").getOldValue() + "_" +
                event.getFieldValues().get("object_code").getOldValue() + "_" +
                event.getFieldValues().get("tag_domain_code").getOldValue() + "_" +
                event.getFieldValues().get("tag_code").getOldValue() + "_" +
                event.getFieldValues().get("is_deleted").getOldValue();
            strSQL = String.format("DELETE FROM %s WHERE %S", tableName, "pk='" + rowkey + "'");
        }
        if (DbEvent.UPDATE == event.getEventType()) {
            String rowkey = 
                event.getFieldValues().get("object_biz_id").getNewValue() + "_" +
                event.getFieldValues().get("object_domain_code").getNewValue() + "_" +
                event.getFieldValues().get("object_code").getNewValue() + "_" +
                event.getFieldValues().get("tag_domain_code").getNewValue() + "_" +
                event.getFieldValues().get("tag_code").getNewValue() + "_" +
                event.getFieldValues().get("is_deleted").getNewValue();
            strFieldName.append("pk");
            strFieldValue.append("'" + rowkey + "'");
            for (DbEventFieldInfo field : event.getFieldValues().values()) {
                DataMessage.Record.Field.Type type = FieldType.parseDrcType(field.getFieldType());
                if (strFieldName.length() > 0) {
                    strFieldName.append(",");
                }
                strFieldName.append(field.getFieldName());
                try {
                    if (strFieldValue.length() > 0) {
                        strFieldValue.append(",");
                    }
                    if (null != field.getNewValue()) {
                        parseValue(strFieldValue, field, type);
                    } else {
                        strFieldValue.append("NULL");
                    }
                } catch (UnsupportedEncodingException e) {
                    continue;
                }
            }
            strSQL = String.format("UPDATE INTO %s(%s) VALUES(%s)", tableName, strFieldName.toString(),
                strFieldValue.toString());
        }
        return strSQL;
    }
    
//    public static void main(String [] args) {
////    	JSONObject json = new JSONObject();
////    	json.put("detail", value)
////    	String strValue = json.toJSONString();
//    	String strValue = "{\"cid\":59377195,\"constructStrategyId\":1456,\"domainCode\":\"TELESALES\",\"leadBizId\":\"2f28fc89-a34f-48d0-ba43-fa273a5afaf0\",\"producerId\":89,\"recommendProduct\":\"trademark\",\"recommendProductText\":\"商标服务(trademark)\",\"recommendReason\":\"[1104006211427063-产品首购-商标服务]: \\n1) 规则名称：DT算法商机挖掘合作_正版图片推商标_电销\\n场景说明：用户当前保有正版图片|LOGO设计产品，可推荐商标产品，进行关联销售\\n推荐理由：1）正版图片_logo设计->商标服务 ： [\\\"是\\\"]\\n\\n【郑重警告】：该商机内容涉及用户敏感数据，请务必不能在客户沟通过程中透出任何相关信息，包含搜索关键词、访问内容，未支付订单等！\",\"recommendScenario\":\"产品首购\",\"status\":1,\"uid\":1104006211427063,\"uuid\":\"34fce19f-840c-415b-9344-428455760ece\"}";
//    	System.out.println(strValue);
//    	System.out.println(strValue.replaceAll("\"", "\\\\\\\""));
//    }
}
