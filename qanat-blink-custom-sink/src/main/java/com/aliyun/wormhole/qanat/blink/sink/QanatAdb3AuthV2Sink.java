package com.aliyun.wormhole.qanat.blink.sink;

import java.io.IOException;
import java.net.InetAddress;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.blink.streaming.connector.custom.api.CustomSinkBase;
import com.alibaba.blink.streaming.connector.hbase.utils.ByteSerializer;
import com.alibaba.blink.streaming.connectors.common.util.BlinkStringUtil;
import com.alibaba.blink.streaming.connectors.common.util.ConnectionPool;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.aliyun.wormhole.qanat.stream.event.FieldType;
import com.aliyun.wormhole.qanat.stream.event.StreamEvent;
import com.aliyun.wormhole.qanat.stream.event.StreamEventField;
import com.aliyun.wormhole.qanat.stream.event.export.EventExporter;
import com.aliyun.wormhole.qanat.stream.event.export.KafkaEventExporter;
import com.aliyun.wormhole.qanat.stream.event.export.MetaqEventExporter;

import lombok.Data;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatAdb3AuthV2Sink extends CustomSinkBase {

    private final static Logger LOG = LoggerFactory.getLogger(QanatAdb3AuthV2Sink.class);

    private String url;
    private String tableName;
    private String username;
    private String password;
    private Connection connection = null;
    private String streamEvent = null;
    private EventExporter exporter;
    private String eventTag;
    private String streamType;
    private int retryIntervalMs = 200;
    private int maxRetryTime = 3;
    
    private transient DruidDataSource dataSource;
	private static ConnectionPool<DruidDataSource> dataSourcePool = new ConnectionPool<>();
	private String dataSourceKey = "";
    
    @Override
    public void open(int i, int i1) throws IOException {
        LOG.info(this.getName() + " open({},{})", i, i1);
        try {
        	String dbName = userParamsMap.get("dbname");
            tableName = userParamsMap.get("tablename");
    		
    		if (BlinkStringUtil.isNotEmpty(dbName)) {
                String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
                JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
                url = dbMetaJson.getString("jdbcUrl");
                username = dbMetaJson.getString("username");
                password = dbMetaJson.getString("password");
    		} else {
	            url = userParamsMap.get("url");
	            username = userParamsMap.get("username");
	            password = userParamsMap.get("password");
    		}
            
            synchronized (QanatAdb3AuthV2Sink.class) {
    			dataSourceKey = url;
    			if (dataSourcePool.contains(dataSourceKey)){
    				dataSource = dataSourcePool.get(dataSourceKey);
    				if (dataSource  == null) {
        				buildDatasource();
    				}
    			} else {
    				buildDatasource();
    			}
    			connection = dataSource.getConnection();
            }
            
            
            streamEvent = userParamsMap.get("streamevent");
            streamEvent = StringUtils.isBlank(streamEvent) ? "enable" : streamEvent;
            if ("enable".equalsIgnoreCase(streamEvent)) {
            	streamType = userParamsMap.get("streamtype");
                String eventTopic = userParamsMap.get("eventtopic");
            	if ("kafka".equals(streamType)) {
                    String eventServer = userParamsMap.get("eventserver");
    	            exporter = new KafkaEventExporter(eventTopic, eventServer);
    	            exporter.init();
    	            LOG.info("KafkaEventExporter {}:{} inited", eventTopic, eventServer);
            	} else {
	                String eventUnit = userParamsMap.get("eventunit");
	                eventTag = userParamsMap.get("eventtag");
	                if (StringUtils.isBlank(eventTopic)) {
	                    eventTopic = "QANAT_BLINK_ADB3_SINK_LOG_TOPIC";
	                }
	                String eventGroup = userParamsMap.get("eventgroup");
	                if (StringUtils.isBlank(eventGroup)) {
	                    eventGroup = "PID_" + getName() + "_" + tableName + "_" + i + "_" + i1 + "_" + InetAddress.getLocalHost().getHostName() + "_" + System.currentTimeMillis();
	                }
	                exporter = new MetaqEventExporter(eventTopic, eventGroup, eventUnit);
	                exporter.init();
	                LOG.info("MetaqEventExporter {}:{}:{} inited", eventTopic, eventGroup, eventUnit);
            	}
            }
        } catch (Exception e) {
            LOG.error("sql exec failed:{}", e.getMessage(), e);
            throw new RuntimeException("QanatAdb3AuthV2Sink.open(" + i + "," + i1 + ") failed:" + e.getMessage(), e);
        }
    }

	private void buildDatasource() throws SQLException {
		dataSource = new DruidDataSource();
		dataSource.setUrl(url);
		dataSource.setUsername(username);
		dataSource.setPassword(password);
		dataSource.setDriverClassName("com.mysql.jdbc.Driver"); // com.***.***.**.driver
		dataSource.setMaxActive(5);
		dataSource.setInitialSize(1);
		dataSource.setMaxWait(60000);
		dataSource.setMinIdle(0);
		dataSource.setTestWhileIdle(true);
		dataSource.setTestOnBorrow(true);
		dataSource.setTestOnReturn(true);
		dataSource.setRemoveAbandoned(true);
		dataSource.setValidationQuery("select 1");
		dataSource.setTimeBetweenEvictionRunsMillis(90000);
		dataSource.setMinEvictableIdleTimeMillis(18000000);
		dataSource.init();

		dataSourcePool.put(dataSourceKey, dataSource);
	}

    @Override
    public void writeAddRecord(Row row) throws IOException {
        LOG.info("writeAddRecord start");
        Statement statement = null;
        try {
            Map<String, Object> inputData = getInputData(row, rowTypeInfo);
            LOG.info("inputDat={}", JSON.toJSONString(inputData));
            String id = inputData.get("id") == null ? null : String.valueOf(inputData.get("id"));
            String relType = inputData.get("rt") == null ? null : String.valueOf(inputData.get("rt"));
            String empid = inputData.get("e") == null ? null : String.valueOf(inputData.get("e"));
            String bizType = inputData.get("bt") == null ? null : String.valueOf(inputData.get("bt"));
            Long bizId = inputData.get("bi") == null ? null : Long.valueOf(String.valueOf(inputData.get("bi")));
            String x = inputData.get("x") == null ? null : String.valueOf(inputData.get("x"));
            String y = inputData.get("y") == null ? null : String.valueOf(inputData.get("y"));
            String z = inputData.get("z") == null ? null : String.valueOf(inputData.get("z"));
            String opType = String.valueOf(inputData.get("op"));
            String traceId = String.valueOf(inputData.get("__traceId__"));
            
            Auth auth = new Auth();
            auth.setId(id);
            auth.setBizId(bizId);
            auth.setBizType(bizType);
            auth.setEmpid(empid);
            auth.setRelType(relType);
            auth.setX(x);
            auth.setY(y);
            auth.setZ(z);
            
            String sql = getSql(opType, auth);
            LOG.info("{} dbName={} sql={}", traceId, userParamsMap.get("dbname"), sql);
            long startTs = System.currentTimeMillis();
    		int retryTime = 0;
    		while (retryTime < maxRetryTime) {
    			try {
		            if (connection == null || connection.isClosed()) {
		                long startTsConn = System.currentTimeMillis();
		            	try {
		            		connection = dataSource.getConnection();
		            	} catch(Exception e) {
		            		buildDatasource();
		            		connection = dataSource.getConnection();
		            	}
			            LOG.info("{} retry:{} reconnect to {} cost={}", traceId, retryTime, userParamsMap.get("dbname"), System.currentTimeMillis()-startTsConn);
		            }
		            statement = connection.createStatement();
		            int execCnt = statement.executeUpdate(sql);
		            LOG.info("{} after dbName={} execCnt={} cost={}", traceId, userParamsMap.get("dbname"), execCnt, System.currentTimeMillis()-startTs);
		            
		            if (execCnt > 0 && "enable".equalsIgnoreCase(streamEvent)) {
		                //export event
		            	String[] fieldNames = rowTypeInfo.getFieldNames();
		                String url = this.dataSourceKey.split("\\?")[0];
		                String[] tokens = url.split("/");
		                String dbName = tokens[tokens.length - 1];
		                String tag = null;
		                if (org.apache.commons.lang3.StringUtils.isBlank(eventTag)) {
		                    tag = dbName + "__" + tableName;
		                } else {
		                    tag = eventTag;
		                }
		                
		                StreamEvent streamEvent = new StreamEvent();
		                Map<String, StreamEventField> fieldValues = new HashMap<>();
		                streamEvent.setFieldValues(fieldValues);
		                streamEvent.setPkField(Arrays.asList("id"));
		                streamEvent.setTableName(tableName);
		                streamEvent.setEventType(Integer.valueOf(opType));
		                streamEvent.setDbName(dbName);
		                streamEvent.setTs(System.currentTimeMillis());
		                for (int i = 0; i < row.getArity(); i++) {
		                    ByteSerializer.ValueType colType = ByteSerializer.getTypeIndex(rowTypeInfo.getTypeAt(i).getTypeClass());
		                    String fieldName = fieldNames[i];
		                    if (fieldName.equalsIgnoreCase("__traceId__") || fieldName.contains("__old")) {
		                        continue;
		                    }
		                    StreamEventField eventField = new StreamEventField();
		                    eventField.setFieldName(fieldName);
		                    eventField.setNewValue(row.getField(i) + "");
		                    eventField.setOldValue(inputData.get(fieldName + "__old") == null ? null : String.valueOf(inputData.get(fieldName + "__old")));
		                    eventField.setFieldType(parseBlinkType(colType));
		                    fieldValues.put(fieldName, eventField);
		                }
		                try {
		                    streamEvent.setTraceId(traceId);
		                } catch(Exception e) {}
		                exporter.export(tag, streamEvent);
		            }
	                LOG.info("{} dbName:{} writeAddRecord finish", traceId, userParamsMap.get("dbname"));
		            break;
    			} catch (SQLException e) {
    	            LOG.error("{} dbName:{} sqlexception with retry:{} error:{}", traceId, userParamsMap.get("dbname"), retryTime, e.getMessage(), e);
					retryTime++;
					if (retryTime == maxRetryTime) {
						throw new RuntimeException(e);
					}
    				try {
						Thread.sleep(retryIntervalMs * retryTime);
    				} catch (Exception e1) {
    					//ignore
    				}
    			} finally {
    	            if (statement != null) {
    	                try {
    	                    statement.close();
    	                } catch (SQLException e) {
    	                    LOG.error("statement close failed", e);
    	                }
    	            }
    	            if (connection != null) {
    	                try {
    	                	connection.close();
    	                } catch (SQLException e) {
    	                    LOG.error("connection close failed", e);
    	                }
    	            }
    				
    			}
    		}
        } catch(Exception e) {
            LOG.error("writeAddRecord failed:{}", e.getMessage(), e);
        }
    }

	@Override
    public void writeDeleteRecord(Row row) throws IOException {
        
    }
    
    private Map<String, Object> getInputData(Row row, RowTypeInfo rowTypeInfo) {
        Map<String, Object> inputData = new HashMap<>();
        for (String fieldName : rowTypeInfo.getFieldNames()) {
            Object value = row.getField(rowTypeInfo.getFieldIndex(fieldName));
            if (value instanceof java.sql.Timestamp) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String str = df.format((java.sql.Timestamp)value);
                value = str;
            }
            if (value instanceof java.math.BigDecimal) {
                value = ((java.math.BigDecimal)value).doubleValue();
            }
            inputData.put(fieldName, value);
        }
        return inputData;
    }

    private String getSql(String opType, Auth auth) {
        List<String> fieldNameList = Arrays.asList("`id`", "`rt`", "`e`", "`bt`", "`bi`", "`x`", "`y`", "`z`");
        List<String> fieldValueList = Arrays.asList("'" + auth.getId() + "'","'" + auth.getRelType() + "'", "'" + auth.getEmpid() + "'", "'" + auth.getBizType() + "'", auth.getBizId() + "", auth.getX() == null ? "NULL" : "'" + auth.getX() + "'", auth.getY() == null ? "NULL" : "'" + auth.getY() + "'", auth.getZ() == null ? "NULL" : "'" + auth.getZ() + "'");

        if ("1".equals(opType)) {
            return String.format("INSERT INTO %s(%s) VALUES(%s)", tableName, StringUtils.join(fieldNameList, ","),  StringUtils.join(fieldValueList, ","));
        } else if ("2".equals(opType)) {
        	return String.format("UPDATE INTO %s(%s) VALUES(%s)", tableName, StringUtils.join(fieldNameList, ","),  StringUtils.join(fieldValueList, ","));
    	} else if ("3".equals(opType)) {
    		return String.format("DELETE FROM %s WHERE id='%s'", tableName, auth.getId());
    	} else {
    		return null;
        }
    }

	public static int parseBlinkType(ByteSerializer.ValueType type) {
		switch (type) {
        case V_Short:
        case V_Integer:
        case V_Long:
        case V_BigInteger:
			return FieldType.TYPE_LONG;
        case V_BigDecimal:
        case V_Float:
        case V_Double:
			return FieldType.TYPE_DOUBLE;
		case V_Timestamp:
		case V_Time:
			return FieldType.TYPE_DATE;
		case V_String:
        case V_Byte:
			return FieldType.TYPE_STRING;
		default:
			return FieldType.TYPE_NOT_SUPPORT;
		}
	}
	
    @Override
    public void sync() throws IOException {
    }

    @Override
    public void close() throws IOException {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                connection = null;
                LOG.error("conn close failed", e);
            }
        }
        if (exporter != null) {
            exporter.close();
        }
    }

    @Override
    public String getName() {
        return "QanatAdb3Sink";
    }
    
    @Data
    public static class Auth {
    	private String id;
    	private String relType;
    	private String empid;
    	private String bizType;
    	private Long bizId;
    	private String x;
    	private String y;
    	private String z;
    }
}
