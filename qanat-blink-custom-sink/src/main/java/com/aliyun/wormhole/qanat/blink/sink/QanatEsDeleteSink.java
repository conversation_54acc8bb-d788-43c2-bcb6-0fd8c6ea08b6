package com.aliyun.wormhole.qanat.blink.sink;

import org.elasticsearch.action.delete.DeleteRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class QanatEsDeleteSink extends QanatEsBulkSinkBase {

    private final static Logger log = LoggerFactory.getLogger(QanatEsDeleteSink.class);

    @Override
    public void onAddData(Map<String, Object> inputData) {
        try {
        	log.info("delData:id={}", inputData.get(pk));
            getBulkProcessor().add(new DeleteRequest(index, type, inputData.get(pk) + ""));
        } catch(Exception e) {
        	log.error("delData failed,error={}", e.getMessage(), e);
        }
    }

    @Override
    public String getName() {
        return "QanatEsDeleteBulkSink";
    }
}   