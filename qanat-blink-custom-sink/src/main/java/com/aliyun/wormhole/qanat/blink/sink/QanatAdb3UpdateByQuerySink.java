package com.aliyun.wormhole.qanat.blink.sink;

import java.io.IOException;
import java.net.InetAddress;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.alibaba.blink.streaming.connector.custom.api.CustomSinkBase;
import com.alibaba.blink.streaming.connector.hbase.utils.ByteSerializer;
import com.alibaba.blink.streaming.connectors.common.util.BlinkStringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.aliyun.wormhole.qanat.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.stream.event.StreamEvent;
import com.aliyun.wormhole.qanat.stream.event.StreamEventField;
import com.aliyun.wormhole.qanat.stream.event.export.MetaqEventExporter;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatAdb3UpdateByQuerySink extends CustomSinkBase {

    private final static Logger log = LoggerFactory.getLogger(QanatAdb3UpdateByQuerySink.class);

    private String dbName;
    private String url;
    private String tableName;
    private String username;
    private String password;
    private Connection conn = null;
    private RdsConnectionParam param;
    private String streamEvent = null;
    private MetaqEventExporter exporter;
    private String eventTag;
    
    @Override
    public void open(int i, int i1) throws IOException {
        log.info(this.getName() + " open({},{})", i, i1);
        try {
            dbName = userParamsMap.get("dbname");
            tableName = userParamsMap.get("tablename");
            if (BlinkStringUtil.isNotEmpty(dbName)) {
                String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
                JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
                url = dbMetaJson.getString("jdbcUrl");
                username = dbMetaJson.getString("username");
                password = dbMetaJson.getString("password");
    		}
            param = new RdsConnectionParam();
            param.setUrl(url)
    	        .setUserName(username)
    	        .setPassword(password);
        	conn = QanatDatasourceHandler.connectToTable(param);
        	
        	streamEvent = userParamsMap.get("streamevent");
            streamEvent = StringUtils.isBlank(streamEvent) ? "enable" : streamEvent;
            if ("enable".equalsIgnoreCase(streamEvent)) {
                String eventUnit = userParamsMap.get("eventunit");
                String eventTopic = userParamsMap.get("eventtopic");
                eventTag = userParamsMap.get("eventtag");
                if (StringUtils.isBlank(eventTopic)) {
                    eventTopic = "QANAT_BLINK_ADB3_SINK_LOG_TOPIC";
                }
                String eventGroup = userParamsMap.get("eventgroup");
                if (StringUtils.isBlank(eventGroup)) {
                    eventGroup = "PID_" + getName() + "_" + tableName + "_" + i + "_" + i1 + "_" + InetAddress.getLocalHost().getHostName() + "_" + System.currentTimeMillis();
                }
                exporter = new MetaqEventExporter(eventTopic, eventGroup, eventUnit);
                exporter.init();
                log.info("{}:{}:{} inited", eventTopic, eventGroup, eventUnit);
            }
        } catch (Exception e) {
            log.error("sql exec failed:{}", e.getMessage(), e);
        }
    }

    @Override
    public void writeAddRecord(Row row) throws IOException {
        log.info("writeAddRecord start");
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            Map<String, Object> inputData = getInputData(row, rowTypeInfo);
            log.info("inputDat={}", JSON.toJSONString(inputData));
            String traceId = String.valueOf(inputData.get("__trace_id__"));
            List<String> setClause = new ArrayList<>();
            List<String> whereClause = new ArrayList<>();
            for (String field : inputData.keySet()) {
            	if (this.primaryKeys.contains(field)) {
            		whereClause.add(field + "='" + inputData.get(field) + "'");
            	} else {
            		setClause.add(field + "='" + inputData.get(field) + "'");
            	}
            }
            String sql = "UPDATE " + tableName + " SET " + StringUtils.join(setClause, ",") + " WHERE " + StringUtils.join(setClause, " AND ") + ";";
            log.info("sql={}", sql);
            if (conn == null || conn.isClosed()) {
            	conn = QanatDatasourceHandler.connectToTable(param);
            }
            statement = conn.createStatement();
            int execCnt = statement.executeUpdate(sql);
            log.info("after sql={}, execCnt={}", sql, execCnt);
            
            if (execCnt > 0 && "enable".equalsIgnoreCase(streamEvent)) {
                //export event
                StreamEvent streamEvent = new StreamEvent();
                streamEvent.setTs(System.currentTimeMillis());
                streamEvent.setTraceId(traceId);
                streamEvent.setDbName(param.getUrl().split("/")[3].split("\\?")[0]);
                streamEvent.setTableName(tableName);
                streamEvent.setEventType(2);
                streamEvent.setPkField(this.primaryKeys.stream().collect(Collectors.toList()));
                Map<String, StreamEventField> fieldValues = new HashMap<>();
                streamEvent.setFieldValues(fieldValues);
                for (String fieldName : rowTypeInfo.getFieldNames()) {
                    Object value = row.getField(rowTypeInfo.getFieldIndex(fieldName));
	                StreamEventField field = new StreamEventField();
	                fieldValues.put(fieldName, field);
	                field.setFieldName(fieldName);
	                field.setFieldType(transformEventType(rowTypeInfo.getTypeAt(rowTypeInfo.getFieldIndex(fieldName))));
	            	field.setNewValue(String.valueOf(value));
                }
                String tag = null;
                if (StringUtils.isBlank(eventTag)) {
                    tag = streamEvent.getDbName() + "__" + streamEvent.getTableName();
                } else {
                    tag = eventTag;
                }
                exporter.export(tag, streamEvent);
            }
            log.info("writeAddRecord finish");
        } catch(Exception e) {
            log.error("writeAddRecord failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                    resultSet = null;
                    log.error("resultSet close failed", e);
                }
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    statement = null;
                    log.error("statement close failed", e);
                }
            }
        }
    }

    private Integer transformEventType(TypeInformation<Object> typeAt) {
    	ByteSerializer.ValueType colType = ByteSerializer.getTypeIndex(typeAt.getTypeClass());
            switch (colType) {
                case V_String:
                    return 5;
                case V_Integer:
                case V_Boolean:
                    return 1;
                case V_Long:
                    return 2;
                case V_BigInteger:
                case V_BigDecimal:
                case V_Float:
                case V_Double:
                    return 3;
                case V_Timestamp:
                case V_Date:
                    return 3;
                default:
                    return 5;
            }
	}

	@Override
    public void writeDeleteRecord(Row row) throws IOException {
        
    }
    
    private Map<String, Object> getInputData(Row row, RowTypeInfo rowTypeInfo) {
        Map<String, Object> inputData = new HashMap<>();
        for (String fieldName : rowTypeInfo.getFieldNames()) {
            Object value = row.getField(rowTypeInfo.getFieldIndex(fieldName));
            if (value instanceof java.sql.Timestamp) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String str = df.format((java.sql.Timestamp)value);
                value = str;
            }
            if (value instanceof java.math.BigDecimal) {
                value = ((java.math.BigDecimal)value).doubleValue();
            }
            inputData.put(fieldName, value);
        }
        return inputData;
    }

    @Override
    public void sync() throws IOException {
    }

    @Override
    public void close() throws IOException {
    	QanatDatasourceHandler.closeDataSource(param);
    }

    @Override
    public String getName() {
        return "QanatAdb3UpdateByQuerySink";
    }
}
