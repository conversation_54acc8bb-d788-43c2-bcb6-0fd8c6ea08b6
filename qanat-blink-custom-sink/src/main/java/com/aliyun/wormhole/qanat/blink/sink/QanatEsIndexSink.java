package com.aliyun.wormhole.qanat.blink.sink;

import com.alibaba.fastjson.JSON;

import com.aliyun.wormhole.qanat.blink.util.QanatCustomUtil;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.common.xcontent.XContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Map;

public class QanatEsIndexSink extends QanatEsSinkBase {

    private final static Logger LOG = LoggerFactory.getLogger(QanatEsIndexSink.class);

    @Override
    public void onAddData(Map<String, Object> inputData) throws IOException {
        Map<String, Object> docData =  QanatCustomUtil.buildDocData(mappingJson, inputData);
        IndexRequest indexRequest = new IndexRequest(index, type);
        indexRequest.source(JSON.toJSONString(docData), XContentType.JSON);
        indexRequest.id(docData.get("id") + "");
        IndexResponse indexResponse = getHighLevelClient().index(indexRequest);
        LOG.info("indexResponse={}", (indexResponse == null || indexResponse.getResult() == null) ? "" : indexResponse.getResult().toString());
    }

    @Override
    public void onSync() {

    }

    @Override
    public String getName() {
        return "QanatEsIndexSink";
    }

    @Override
    public void onInit() {

    }

    @Override
    public void onClose() {
        
    }
}