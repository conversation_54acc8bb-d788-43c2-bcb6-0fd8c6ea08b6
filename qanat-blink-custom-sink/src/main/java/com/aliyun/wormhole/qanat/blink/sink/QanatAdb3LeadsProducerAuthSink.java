package com.aliyun.wormhole.qanat.blink.sink;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.blink.streaming.connector.custom.api.CustomSinkBase;
import com.alibaba.blink.streaming.connector.hbase.utils.ByteSerializer;
import com.alibaba.blink.streaming.connectors.common.util.BlinkStringUtil;
import com.alibaba.blink.streaming.connectors.common.util.ConnectionPool;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.aliyun.wormhole.qanat.stream.event.FieldType;

import lombok.Data;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatAdb3LeadsProducerAuthSink extends CustomSinkBase {

    private final static Logger LOG = LoggerFactory.getLogger(QanatAdb3LeadsProducerAuthSink.class);

    private String url;
    private String tableName;
    private String username;
    private String password;
    private Connection connection = null;
    
    private transient DruidDataSource dataSource;
	private static ConnectionPool<DruidDataSource> dataSourcePool = new ConnectionPool<>();
	private String dataSourceKey = "";
    
    @Override
    public void open(int i, int i1) throws IOException {
        LOG.info(this.getName() + " open({},{})", i, i1);
        try {
        	String dbName = userParamsMap.get("dbname");
            tableName = userParamsMap.get("tablename");
    		
    		if (BlinkStringUtil.isNotEmpty(dbName)) {
                String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
                JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
                url = dbMetaJson.getString("jdbcUrl");
                username = dbMetaJson.getString("username");
                password = dbMetaJson.getString("password");
    		} else {
	            url = userParamsMap.get("url");
	            username = userParamsMap.get("username");
	            password = userParamsMap.get("password");
    		}
            
            synchronized (QanatAdb3LeadsProducerAuthSink.class) {
    			dataSourceKey = url;
    			if (dataSourcePool.contains(dataSourceKey)){
    				dataSource = dataSourcePool.get(dataSourceKey);
    			} else {
    				dataSource = new DruidDataSource();
    				dataSource.setUrl(url);
    				dataSource.setUsername(username);
    				dataSource.setPassword(password);
    				dataSource.setDriverClassName("com.mysql.jdbc.Driver"); // com.***.***.**.driver
    				dataSource.setMaxActive(40);
    				dataSource.setInitialSize(1);
    				dataSource.setMaxWait(15000);//默认为15s
    				dataSource.setMinIdle(0);
    				dataSource.setTestWhileIdle(true);
    				dataSource.setTestOnBorrow(false);
    				dataSource.setTestOnReturn(false);
    				dataSource.setRemoveAbandoned(false);
    				dataSource.setValidationQuery("select 1");
    				dataSource.setTimeBetweenEvictionRunsMillis(180000);
    				dataSource.setMinEvictableIdleTimeMillis(3600000);
    				dataSource.init();

    				dataSourcePool.put(dataSourceKey, dataSource);
    			}
    			connection = dataSource.getConnection();
            }
        } catch (Exception e) {
            LOG.error("sql exec failed:{}", e.getMessage(), e);
        }
    }

    @Override
    public void writeAddRecord(Row row) throws IOException {
        LOG.info("writeAddRecord start");
        Statement statement = null;
        try {
            Map<String, Object> inputData = getInputData(row, rowTypeInfo);
            LOG.info("inputDat={}", JSON.toJSONString(inputData));
            String relType = inputData.get("rt") == null ? null : String.valueOf(inputData.get("rt"));
            String owner = inputData.get("e") == null ? null : String.valueOf(inputData.get("e"));
            String bizType = inputData.get("bt") == null ? null : String.valueOf(inputData.get("bt"));
            Long bizId = inputData.get("bi") == null ? null : Long.valueOf(String.valueOf(inputData.get("bi")));
            String opType = String.valueOf(inputData.get("op"));
            
            String[] tokens = owner.split(",");
            List<Auth> authList = new ArrayList<>();
            for (String token : tokens) {
	            Auth auth = new Auth();
	            String empid = token.split("/")[0];
	            auth.setId(bizType + "-" + empid + "-" + bizId);
	            auth.setBizId(bizId);
	            auth.setBizType(bizType);
	            auth.setEmpid(empid);
	            auth.setRelType(relType);
	            authList.add(auth);
            }
            if (connection == null || connection.isClosed()) {
            	connection = dataSource.getConnection();
            }
            statement = connection.createStatement();
            if ("2".equals(opType) || "3".equals(opType)) {
	            String deleteSql = "DELETE FROM " + tableName + " WHERE bi=" + bizId + " and bt='" + bizType + "' and rt='" + relType + "'";
	            LOG.info("deleteSql={}", deleteSql);
	            int execCnt = statement.executeUpdate(deleteSql);
	            LOG.info("deleteSql execCnt={}", execCnt);
            }

            if ("1".equals(opType) || "2".equals(opType)) {
	            String insertSql = getSql(opType, authList);
	            LOG.info("insertSql={}", insertSql);
	            int execCnt = statement.executeUpdate(insertSql);
	            LOG.info("insertSql execCnt={}", execCnt);
            }
            LOG.info("writeAddRecord finish");
        } catch(Exception e) {
            LOG.error("writeAddRecord failed", e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    statement = null;
                    LOG.error("statement close failed", e);
                }
            }
        }
    }

	@Override
    public void writeDeleteRecord(Row row) throws IOException {
        
    }
    
    private Map<String, Object> getInputData(Row row, RowTypeInfo rowTypeInfo) {
        Map<String, Object> inputData = new HashMap<>();
        for (String fieldName : rowTypeInfo.getFieldNames()) {
            Object value = row.getField(rowTypeInfo.getFieldIndex(fieldName));
            if (value instanceof java.sql.Timestamp) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String str = df.format((java.sql.Timestamp)value);
                value = str;
            }
            if (value instanceof java.math.BigDecimal) {
                value = ((java.math.BigDecimal)value).doubleValue();
            }
            inputData.put(fieldName, value);
        }
        return inputData;
    }

    private String getSql(String opType, List<Auth> authList) {
        List<String> fieldNameList = Arrays.asList("`id`", "`rt`", "`e`", "`bt`", "`bi`");
        List<String> insertValues = new ArrayList<>();
        for (Auth auth : authList) {
        	List<String> fieldValueList = Arrays.asList("'" + auth.getId() + "'","'" + auth.getRelType() + "'", "'" + auth.getEmpid() + "'", "'" + auth.getBizType() + "'", auth.getBizId() + "");
        	insertValues.add("(" + StringUtils.join(fieldValueList, ",") + ")");
    	}
        return String.format("INSERT INTO %s(%s) VALUES %s", tableName, StringUtils.join(fieldNameList, ","),  StringUtils.join(insertValues, ","));
    }

	public static int parseBlinkType(ByteSerializer.ValueType type) {
		switch (type) {
        case V_Short:
        case V_Integer:
        case V_Long:
        case V_BigInteger:
			return FieldType.TYPE_LONG;
        case V_BigDecimal:
        case V_Float:
        case V_Double:
			return FieldType.TYPE_DOUBLE;
		case V_Timestamp:
		case V_Time:
			return FieldType.TYPE_DATE;
		case V_String:
        case V_Byte:
			return FieldType.TYPE_STRING;
		default:
			return FieldType.TYPE_NOT_SUPPORT;
		}
	}
	
    @Override
    public void sync() throws IOException {
    }

    @Override
    public void close() throws IOException {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                connection = null;
                LOG.error("conn close failed", e);
            }
        }
    }

    @Override
    public String getName() {
        return "QanatAdb3Sink";
    }
    
    @Data
    public static class Auth {
    	private String id;
    	private String relType;
    	private String empid;
    	private String bizType;
    	private Long bizId;
    }
}
