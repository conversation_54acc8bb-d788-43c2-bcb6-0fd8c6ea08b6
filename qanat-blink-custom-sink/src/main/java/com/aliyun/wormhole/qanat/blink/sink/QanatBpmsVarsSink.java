package com.aliyun.wormhole.qanat.blink.sink;

import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.blink.streaming.connector.custom.api.CustomSinkBase;
import com.alibaba.blink.streaming.connectors.common.util.BlinkStringUtil;
import com.alibaba.blink.streaming.connectors.common.util.ConnectionPool;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.aliyun.wormhole.qanat.drc.event.DbEventFieldInfo;
import com.aliyun.wormhole.qanat.drc.event.DbEventInfo;
import com.aliyun.wormhole.qanat.stream.event.StreamEvent;
import com.aliyun.wormhole.qanat.stream.event.export.EventExporter;
import com.aliyun.wormhole.qanat.stream.event.export.KafkaEventExporter;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatBpmsVarsSink extends CustomSinkBase {

    private final static Logger LOG = LoggerFactory.getLogger(QanatBpmsVarsSink.class);

    private String url;
    private String tableName;
    private String username;
    private String password;
    private Connection conn = null;
    private String streamEvent = null;
    private EventExporter exporter;
    private String eventTag;
    private String streamType;
    
    private transient DruidDataSource dataSource;
	private static ConnectionPool<DruidDataSource> dataSourcePool = new ConnectionPool<>();
	private String dataSourceKey = "";
    
    @Override
    public void open(int i, int i1) throws IOException {
        LOG.info(this.getName() + " open({},{})", i, i1);
        try {
        	String dbName = userParamsMap.get("dbname");
            tableName = userParamsMap.get("tablename");
    		
    		if (BlinkStringUtil.isNotEmpty(dbName)) {
                String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
                JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
                url = dbMetaJson.getString("jdbcUrl");
                username = dbMetaJson.getString("username");
                password = dbMetaJson.getString("password");
    		} else {
	            url = userParamsMap.get("url");
	            username = userParamsMap.get("username");
	            password = userParamsMap.get("password");
    		}
            
            synchronized (QanatBpmsVarsSink.class) {
    			dataSourceKey = url + username + password + tableName;
    			if (dataSourcePool.contains(dataSourceKey)){
    				dataSource = dataSourcePool.get(dataSourceKey);
    			} else {
    				dataSource = new DruidDataSource();
    				dataSource.setUrl(url);
    				dataSource.setUsername(username);
    				dataSource.setPassword(password);
    				dataSource.setDriverClassName("com.mysql.jdbc.Driver"); // com.***.***.**.driver
    				dataSource.setMaxActive(40);
    				dataSource.setInitialSize(1);
    				dataSource.setMaxWait(15000);//默认为15s
    				dataSource.setMinIdle(0);
    				dataSource.setTestWhileIdle(true);
    				dataSource.setTestOnBorrow(false);
    				dataSource.setTestOnReturn(false);
    				dataSource.setRemoveAbandoned(false);
    				dataSource.setValidationQuery("select 1");
    				dataSource.setTimeBetweenEvictionRunsMillis(180000);
    				dataSource.setMinEvictableIdleTimeMillis(3600000);
    				dataSource.init();

    				dataSourcePool.put(dataSourceKey, dataSource);
    			}
    			conn = dataSource.getConnection();
            }

            streamEvent = userParamsMap.get("streamevent");
            streamEvent = StringUtils.isBlank(streamEvent) ? "enable" : streamEvent;
            if ("enable".equalsIgnoreCase(streamEvent)) {
            	streamType = userParamsMap.get("streamtype");
                String eventTopic = userParamsMap.get("eventtopic");
            	if ("kafka".equals(streamType)) {
                    String eventServer = userParamsMap.get("eventserver");
    	            exporter = new KafkaEventExporter(eventTopic, eventServer);
    	            exporter.init();
    	            LOG.info("KafkaEventExporter {}:{} inited", eventTopic, eventServer);
            	}
            }
        } catch (Exception e) {
            LOG.error("sql exec failed:{}", e.getMessage(), e);
        }
    }

    @Override
    public void writeAddRecord(Row row) throws IOException {
        LOG.info("writeAddRecord start");
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            Map<String, Object> inputData = getInputData(row, rowTypeInfo);
            LOG.info("inputDat={}", JSON.toJSONString(inputData));
            String msg = String.valueOf(inputData.get("__msg__"));
            String traceId = String.valueOf(inputData.get("__trace_id__"));
            
            if (conn == null || conn.isClosed()) {
            	conn = dataSource.getConnection();
            }
            DbEventInfo event = JSON.parseObject(msg, DbEventInfo.class);
            DbEventFieldInfo idField = event.getFieldValues().get("bp_instance_id");
            Long id = Long.valueOf(idField.getNewValue() != null ? idField.getNewValue() : idField.getOldValue());
            DbEventFieldInfo bpProcessIdField = event.getFieldValues().get("bp_process_id");
            String bpProcessId = bpProcessIdField.getNewValue() != null ? bpProcessIdField.getNewValue() : bpProcessIdField.getOldValue();
            DbEventFieldInfo varsField = event.getFieldValues().get("bp_instance_parsed_variables");
            String varsJsonStr = varsField.getNewValue() != null ? varsField.getNewValue() : varsField.getOldValue();
            if(StringUtils.isBlank(varsJsonStr)){
            	return;
            }
            JSONObject json = null;
            JSONObject varJson = null;
			try{
				json = JSON.parseObject(varsJsonStr);
				varJson=JSON.parseObject(json.getString("quotationData"));
			} catch (Exception e) {
				String jsonStr1 = varsJsonStr.replace("{\"quotationData\":\"{", "{\"quotationData\":{").replace("}}\"", "}}");
				json = JSON.parseObject(jsonStr1);
				varJson = json.getJSONObject("quotationData");
			}
            Map<String, Object> valueMap = new HashMap<>();
            if(varJson != null) {
	            valueMap.put("quotation_estimated_cycle", wrapValue(getQuotationEstimatedConsumptionCycle(varJson)));
	            valueMap.put("quotation_estimated_cycle_val", wrapValue(getQuotationEstimatedConsumptionCycleVal(varJson)));
	            valueMap.put("comprehensive_discount", wrapValue(getComprehensiveDiscount(varJson)));
	            valueMap.put("comprehensive_discount_val", wrapValue(getComprehensiveDiscountVal(varJson)));
	            valueMap.put("quotation_id", wrapValue(JSONPath.eval(varJson, "$.quotationInfoEntity.id")));
	            valueMap.put("quotation_name", wrapValue(JSONPath.eval(varJson, "$.quotationInfoEntity.quotationName")));
	            valueMap.put("bpid", wrapValue(JSONPath.eval(varJson, "$.quotationInfoEntity.bpid")));
	            valueMap.put("cid", wrapValue(JSONPath.eval(varJson, "$.quotationInfoEntity.cid")));
	            valueMap.put("customer_name", wrapValue(JSONPath.eval(varJson, "$.quotationInfoEntity.customerName")));
	            valueMap.put("gc_level", wrapValue(JSONPath.eval(varJson, "$.quotationInfoEntity.gcLevel")));
	            valueMap.put("quotation_estimated_amount", wrapValue(JSONPath.eval(varJson, "$.quotationInfoEntity.quotationEstimatedConsumptionAmount")));
	            valueMap.put("minimum_amount", wrapValue(JSONPath.eval(varJson, "$.quotationInfoEntity.quotationDiscountEntity.minimumAmount")));
	            valueMap.put("project_name", wrapValue(JSONPath.eval(varJson, "$.quotationInfoEntity.projectName")));
            } else {
	            valueMap.put("voucher_total_amount", wrapValue(JSONPath.eval(json, "$.totalAmount")));
	            valueMap.put("voucher_highdislow_inc_flag", wrapValue(JSONPath.eval(json, "$.highDisLowIncFlag")));
	            valueMap.put("voucher_name", wrapValue(JSONPath.eval(json, "$.templateName")));
	            valueMap.put("voucher_cid", wrapValue(JSONPath.eval(json, "$.customerId")));
			    valueMap.put("voucher_cid_name", wrapValue(JSONPath.eval(json, "$.customerName")));
			    valueMap.put("opportunity_id", wrapValue(JSONPath.eval(json, "$.opportunityId")));
			    valueMap.put("opportunity_name", wrapValue(JSONPath.eval(json, "$.opportunityName")));
			    valueMap.put("total_quotation_fee", wrapValue(JSONPath.eval(json, "$.totalQuotationFee")));
			    valueMap.put("total_prod_fee", wrapValue(JSONPath.eval(json, "$.totalProdFee")));
			    valueMap.put("gross_profit", wrapValue(JSONPath.eval(json, "$.grossProfit")));
			    valueMap.put("hyb_quotation_id", wrapValue(JSONPath.eval(json, "$.quotationId")));

		    	valueMap.put("uid", wrapValue(JSONPath.eval(json, "$.uid")));
		    	valueMap.put("ecid", wrapValue(JSONPath.eval(json, "$.custId")));
		    	valueMap.put("uid_name", wrapValue(JSONPath.eval(json, "$.userName")));
		    	valueMap.put("ecid_name", wrapValue(JSONPath.eval(json, "$.custName")));
			    
			    List<String> voucherProcessIds = Arrays.asList(new String[] {"19","27","20","16","21","15","17","18","23"});
			    if (voucherProcessIds.contains(bpProcessId) && 
			    		(JSONPath.eval(json, "$.customerId") == null || JSONPath.eval(json, "$.customerName") == null)) {
			    	LOG.info("bp_instance_id[{}] is_del=1", id);
			    	valueMap.put("is_del", wrapValue("1"));
			    }
			    List<String> creditProcessIds = Arrays.asList(new String[] {"71","72","73"});
			    if (creditProcessIds.contains(bpProcessId)) {
			    	valueMap.put("forecast_amount", wrapValue(JSONPath.eval(json, "$.forecastAmount")));
			    	valueMap.put("apply_amount", wrapValue(JSONPath.eval(json, "$.applyAmount")));
			    	valueMap.put("cust_apply_amount", wrapValue(JSONPath.eval(json, "$.custApplyAmount")));
			    	valueMap.put("original_credit_limit", wrapValue(JSONPath.eval(json, "$.originalCreditLimit")));
			    	valueMap.put("original_credit_value", wrapValue(JSONPath.eval(json, "$.originalCreditValue")));
			    	
			    	if (JSONPath.eval(json, "$.dailyCycle") != null && JSONPath.eval(json, "$.clearCycle") == null) {
			    		String settleCycle = null;
			    		int dailyCycle = json.getIntValue("dailyCycle");
			    		int clearCycle = json.getIntValue("clearCycle");
			    		if (dailyCycle == 30 && clearCycle == 2) {
			    			settleCycle = "thirty_days";
			    		} else if (dailyCycle == 45 && clearCycle == 2) {
			    			settleCycle = "fortyFive_days";
			    		} else if (dailyCycle == 60 && clearCycle == 2) {
			    			settleCycle = "sixty_days";
			    		} else if (dailyCycle == 90 && clearCycle == 2) {
			    			settleCycle = "ninety_days";
			    		} else if (dailyCycle == 120 && clearCycle == 2) {
			    			settleCycle = "oneHundredAndTwenty_days";
			    		} else if (dailyCycle == 180 && clearCycle == 2) {
			    			settleCycle = "oneHundrenAndEighty_days";
			    		} else if (dailyCycle == 1 && clearCycle == 1) {
			    			settleCycle = "one_month";
			    		} else if (dailyCycle == 2 && clearCycle == 1) {
			    			settleCycle = "two_months";
			    		} else if (dailyCycle == 3 && clearCycle == 1) {
			    			settleCycle = "three_months";
			    		} else if (dailyCycle == 1 && clearCycle == 3) {
			    			settleCycle = "one_season";
			    		} else if (dailyCycle == 1 && clearCycle == 6) {
			    			settleCycle = "half_year";
			    		} else if (dailyCycle == 1 && clearCycle == 12) {
			    			settleCycle = "one_year";
			    		}
				    	valueMap.put("settle_cycle", wrapValue(settleCycle));
			    	}
			    }

			    if ("78".equals(bpProcessId)) {
			    	valueMap.put("project_name", wrapValue(JSONPath.eval(json, "$.projectName")));
			    	valueMap.put("cid", wrapValue(JSONPath.eval(json, "$.cid")));
			    	valueMap.put("gc_level", wrapValue(JSONPath.eval(json, "$.gcLevel")));
			    	valueMap.put("quotation_estimated_amount", wrapValue(JSONPath.eval(json, "$.quotationEstimatedAmount")));
			    	valueMap.put("quotation_estimated_cycle_val", wrapValue(JSONPath.eval(json, "$.quotationEstimatedCycleVal")));
			    	valueMap.put("minimum_amount", wrapValue(JSONPath.eval(json, "$.minimumAmount")));
			    	valueMap.put("quotation_id", wrapValue(JSONPath.eval(json, "$.itemId")));
			    }
            }
            if (CollectionUtils.isEmpty(valueMap.keySet())) {
            	return;
            }
            valueMap.put("id", wrapValue(id));
            
            String sql = String.format("UPDATE INTO %s(%s) VALUES(%s)", tableName, StringUtils.join(valueMap.keySet(), ","), StringUtils.join(valueMap.values(), ","));
            LOG.info("sql={}", sql);
            statement = conn.createStatement();
            int execCnt = statement.executeUpdate(sql);
            LOG.info("after sql={}, execCnt={}", sql, execCnt);
            
            if (execCnt > 0 && "enable".equalsIgnoreCase(streamEvent)) {
                //export event
                StreamEvent streamEvent = JSON.parseObject(msg, StreamEvent.class);
                streamEvent.setTs(System.currentTimeMillis());
                String tag = null;
                if (StringUtils.isBlank(eventTag)) {
                    tag = streamEvent.getDbName() + "__" + streamEvent.getTableName();
                } else {
                    tag = eventTag;
                }
                streamEvent.setTraceId(traceId);
                exporter.export(tag, streamEvent);
            }
            LOG.info("writeAddRecord finish");
        } catch(Exception e) {
            LOG.error("writeAddRecord failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                    resultSet = null;
                    LOG.error("resultSet close failed", e);
                }
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    statement = null;
                    LOG.error("statement close failed", e);
                }
            }
        }
    }
    
    private static String wrapValue(Object val) {
    	return val == null ? "NULL" : ("'" + val + "'");
    }
    
    private String getQuotationEstimatedConsumptionCycle(JSONObject varJson) {
        Integer val = varJson.getJSONObject("quotationInfoEntity").getInteger("quotationEstimatedConsumptionCycle");
    	if (val==null){return "0_1";}else if(val<12){return "0_1";}else if(val>=12&& val<24){return "1_2";}else if(val>=24 && val <36){return "2_3";}else{return "gte3";}
    }
    private Integer getQuotationEstimatedConsumptionCycleVal(JSONObject varJson) {
        return varJson.getJSONObject("quotationInfoEntity").getInteger("quotationEstimatedConsumptionCycle");
    }
    
    private String getComprehensiveDiscount(JSONObject varJson) {
    	Double val = varJson.getJSONObject("quotationInfoEntity").getDouble("comprehensiveDiscount");
    	if(val==null){return "0_4";}else if(val<0.4){return "0_4";}else if(val>=0.4 && val<0.6){return "4_6";}else if(val>=0.6 && val <0.7){return "6_7";}else if(val>=0.7 && val <0.85){return "7_85";}else{return "85_10";}
    }
    
    private Double getComprehensiveDiscountVal(JSONObject varJson) {
    	return varJson.getJSONObject("quotationInfoEntity").getDouble("comprehensiveDiscount");
    }
    	
    @Override
    public void writeDeleteRecord(Row row) throws IOException {
        
    }
    
    private Map<String, Object> getInputData(Row row, RowTypeInfo rowTypeInfo) {
        Map<String, Object> inputData = new HashMap<>();
        for (String fieldName : rowTypeInfo.getFieldNames()) {
            Object value = row.getField(rowTypeInfo.getFieldIndex(fieldName));
            if (value instanceof java.sql.Timestamp) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String str = df.format((java.sql.Timestamp)value);
                value = str;
            }
            if (value instanceof java.math.BigDecimal) {
                value = ((java.math.BigDecimal)value).doubleValue();
            }
            inputData.put(fieldName, value);
        }
        return inputData;
    }

    @Override
    public void sync() throws IOException {
    }

    @Override
    public void close() throws IOException {
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                conn = null;
                LOG.error("conn close failed", e);
            }
        }
        if (exporter != null) {
            exporter.close();
        }
    }

    @Override
    public String getName() {
        return "QanatAdb3Sink";
    }
}
