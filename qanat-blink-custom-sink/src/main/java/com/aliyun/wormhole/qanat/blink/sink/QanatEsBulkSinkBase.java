package com.aliyun.wormhole.qanat.blink.sink;

import java.util.concurrent.atomic.AtomicInteger;

import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.bulk.BackoffPolicy;
import org.elasticsearch.action.bulk.BulkProcessor;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.ByteSizeUnit;
import org.elasticsearch.common.unit.ByteSizeValue;
import org.elasticsearch.common.unit.TimeValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class QanatEsBulkSinkBase extends QanatEsSinkBase {

    private final static Logger LOG = LoggerFactory.getLogger(QanatEsBulkSinkBase.class);
    
    private BulkProcessor bulkProcessor;
    protected Long scheduleFlushInterval;
    protected Integer batchSize;
    protected Integer retries;
    protected Integer retryIntervalMs;
    protected Integer concurrentRequests;
    protected Long bulkSizeMb;

    @Override
    public void onInit() {
        if (userParamsMap != null) {
            batchSize = userParamsMap.get("es_batch_size") == null ? 200 : Integer.valueOf(userParamsMap.get("es_batch_size"));
            scheduleFlushInterval = userParamsMap.get("es_flush_interval_ms") == null ? 2500L : Long.valueOf(userParamsMap.get("es_flush_interval_ms"));
            retries = userParamsMap.get("es_retries") == null ? 3 : Integer.valueOf(userParamsMap.get("es_retries"));
            retryIntervalMs = userParamsMap.get("es_retry_interval_ms") == null ? 500 : Integer.valueOf( userParamsMap.get("es_retry_interval_ms"));
            concurrentRequests = userParamsMap.get("es_bulk_concurrent") == null ? 0 : Integer.valueOf( userParamsMap.get("es_bulk_concurrent"));
            bulkSizeMb = userParamsMap.get("es_bulk_size_mb") == null ? 5L : Long.valueOf(userParamsMap.get("es_bulk_size_mb"));
        }
        BulkProcessor.Listener listener = new RetriableBulkProcessorListener(getHighLevelClient(), retries, retryIntervalMs);
        BulkProcessor localBulkProcessor = BulkProcessor.builder(getHighLevelClient()::bulkAsync, listener)
                .setBulkActions(batchSize)
                .setBulkSize(new ByteSizeValue(bulkSizeMb, ByteSizeUnit.MB))
                .setConcurrentRequests(concurrentRequests)
                .setFlushInterval(TimeValue.timeValueMillis(scheduleFlushInterval))
                .setBackoffPolicy(BackoffPolicy.exponentialBackoff())
                .build();
        this.bulkProcessor = localBulkProcessor;
    }

    @Override
    public void onSync() {
        if (this.bulkProcessor != null) {
            this.bulkProcessor.flush();
        }
    }

    @Override
    public void onClose() {
        if (this.bulkProcessor != null) {
            this.bulkProcessor.close();
        }
    }
    
    public static class RetriableBulkProcessorListener implements BulkProcessor.Listener {
        
        private RestHighLevelClient clientRef;
        private Integer maxRetries;
        private Integer retryIntervalMs;
        
        public RetriableBulkProcessorListener(RestHighLevelClient client, Integer maxRetries, Integer retryInterval) {
            this.clientRef = client;
            this.maxRetries = maxRetries;
            this.retryIntervalMs = retryInterval;
        }

        @Override
        public void beforeBulk(long executionId, BulkRequest request) {
            int numberOfActions = request.numberOfActions();
            LOG.info("beforeBulk [{}] with {} requests", executionId, numberOfActions);
        }

        @Override
        public void afterBulk(long executionId, BulkRequest request, BulkResponse response) {
            if (response.hasFailures()) {
                LOG.error("afterBulk [{}] executed with failures,response = {}", executionId, response.buildFailureMessage());
            } else {
                LOG.info("afterBulk [{}] completed in {} milliseconds", executionId, response.getTook().getMillis());
            }
        }

        @Override
        public void afterBulk(long executionId, BulkRequest request, Throwable failure) {
            LOG.error("afterBulk [{}] Failed to execute bulk", executionId, failure);
            try {
                int numberOfActions = request.numberOfActions();
                int retries = maxRetries;
                while (retries > 0) {
                    AtomicInteger success = new AtomicInteger(0);
                    LOG.info("retryBulk [{}] with {} requests for {}st time", executionId, numberOfActions, maxRetries - retries + 1);
                    this.clientRef.bulkAsync(request, new ActionListener<BulkResponse>() {
    
                        @Override
                        public void onResponse(BulkResponse response) {
                            if (response.hasFailures()) {
                                LOG.error("retryBulk [{}] executed with failures,response = {}", executionId, response.buildFailureMessage());
                            } else {
                                LOG.info("retryBulk [{}] completed in {} milliseconds", executionId, response.getTook().getMillis());
                            }
                            success.set(1);//成功
                        }
    
                        @Override
                        public void onFailure(Exception e) {
                            LOG.error("retryBulk [{}] Failed to execute bulk", executionId, e);
                            success.set(2);
                        }
                        
                    });
                    while (success.get() == 0) {
                        Thread.sleep(100);//间隔100ms
                    }
                    if (success.get() == 1) {
                        break;
                    } else if (success.get() == 2) {
                        retries --;
                        continue;
                    }
                    Thread.sleep(retryIntervalMs);
                }
            } catch (Exception e) {
                LOG.error("afterBulk [{}] Failed to execute bulk", executionId, failure);
            }
        }
    }
    
    public BulkProcessor getBulkProcessor() {
        return this.bulkProcessor;
    }
}
