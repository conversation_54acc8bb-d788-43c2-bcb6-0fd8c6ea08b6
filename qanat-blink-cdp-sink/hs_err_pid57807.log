#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGSEGV (0xb) at pc=0x000000010b1b4c8c, pid=57807, tid=0x0000000000003e03
#
# JRE version: Java(TM) SE Runtime Environment (8.0_152-b16) (build 1.8.0_152-b16)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (25.152-b16 mixed mode bsd-amd64 compressed oops)
# Problematic frame:
# C  [libjdwp.dylib+0x21c8c]  jvmtiAllocate+0x1d
#
# Failed to write core dump. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   http://bugreport.java.com/bugreport/crash.jsp
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#

---------------  T H R E A D  ---------------

Current thread (0x00007fb5db001800):  JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=15875, stack(0x000070000ad59000,0x000070000ae59000)]

siginfo: si_signo: 11 (SIGSEGV), si_code: 1 (SEGV_MAPERR), si_addr: 0x0000000000000000

Registers:
RAX=0x0000000000000000, RBX=0x000000011782538a, RCX=0x0000000000000009, RDX=0x000070000ae58b49
RSP=0x000070000ae58b00, RBP=0x000070000ae58b30, RSI=0x000000000000f000, RDI=0x0000000000000022
R8 =0x000070000ae58b49, R9 =0x0000000000000000, R10=0x00007fb5d6c0d5e0, R11=0x00007fff6aec7992
R12=0x000000010b1badc8, R13=0x00007fb5d6e24530, R14=0x000000000000000a, R15=0x000000010b1b97b5
RIP=0x000000010b1b4c8c, EFLAGS=0x0000000000010206, ERR=0x0000000000000004
  TRAPNO=0x000000000000000e

Top of Stack: (sp=0x000070000ae58b00)
0x000070000ae58b00:   0000000000000004 ffffffffffffffe7
0x000070000ae58b10:   0000000000000000 000000000000010d
0x000070000ae58b20:   000000011782538a 000000000000000a
0x000070000ae58b30:   000070000ae58c60 0000000117824792
0x000070000ae58b40:   0000000000000020 6c69662064614268
0x000070000ae58b50:   6972637365642065 00000000726f7470
0x000070000ae58b60:   0000000000000000 0000000000000000
0x000070000ae58b70:   0000000000000000 0000000000000000
0x000070000ae58b80:   0000000000000000 0000000000000000
0x000070000ae58b90:   0000000000000000 0000000000000000
0x000070000ae58ba0:   0000000000000000 0000000000000000
0x000070000ae58bb0:   0000000000000000 0000000000000000
0x000070000ae58bc0:   0000000000000000 0000000000000000
0x000070000ae58bd0:   0000000000000000 0000000000000000
0x000070000ae58be0:   0000000000000000 0000000000000000
0x000070000ae58bf0:   0000000000000000 0000000000000000
0x000070000ae58c00:   0000000000000000 0000000000000000
0x000070000ae58c10:   0000000000000000 0000000000000000
0x000070000ae58c20:   0000000000000000 0000000000000000
0x000070000ae58c30:   0000000000000000 0000000000000000
0x000070000ae58c40:   0000000000000000 7ac1639f8890002c
0x000070000ae58c50:   000070000ae58ce0 000000010b1bade3
0x000070000ae58c60:   000070000ae58c90 00000001178242ff
0x000070000ae58c70:   000070000ae58cb0 000000010b1b593d
0x000070000ae58c80:   000070000ae58ce0 000000010b1bade3
0x000070000ae58c90:   000070000ae58cb0 000000010b1b1a95
0x000070000ae58ca0:   000000010b1badbc 000000010b1bade3
0x000070000ae58cb0:   000070000ae58d20 000000010b1a1665
0x000070000ae58cc0:   0000000000000019 000070000ae58d00
0x000070000ae58cd0:   0000000001ccf501 0000000000003e03
0x000070000ae58ce0:   000224290000002c 0000000000020100
0x000070000ae58cf0:   00007fb5d6e1f0a0 00007fb5db001800 

Instructions: (pc=0x000000010b1b4c8c)
0x000000010b1b4c6c:   5e 5d c3 55 48 89 e5 41 56 53 48 83 ec 20 85 ff
0x000000010b1b4c7c:   0f 84 85 00 00 00 48 8b 05 0f 19 01 00 48 8b 00
0x000000010b1b4c8c:   48 8b 08 48 63 f7 48 8d 55 e8 48 89 c7 ff 91 68
0x000000010b1b4c9c:   01 00 00 85 c0 74 5e 89 c3 89 df e8 42 cc fe ff 

Register to memory mapping:

RAX=0x0000000000000000 is an unknown value
RBX=0x000000011782538a: dbgsysConnect+0x2fe in /Library/Java/JavaVirtualMachines/jdk1.8.0_152.jdk/Contents/Home/jre/lib/libdt_socket.dylib at 0x0000000117823000
RCX=0x0000000000000009 is an unknown value
RDX=0x000070000ae58b49 is pointing into the stack for thread: 0x00007fb5db001800
RSP=0x000070000ae58b00 is pointing into the stack for thread: 0x00007fb5db001800
RBP=0x000070000ae58b30 is pointing into the stack for thread: 0x00007fb5db001800
RSI=0x000000000000f000 is an unknown value
RDI=0x0000000000000022 is an unknown value
R8 =0x000070000ae58b49 is pointing into the stack for thread: 0x00007fb5db001800
R9 =0x0000000000000000 is an unknown value
R10=0x00007fb5d6c0d5e0 is an unknown value
R11=0x00007fff6aec7992: pthread_getspecific+0 in /usr/lib/system/libsystem_pthread.dylib at 0x00007fff6aec6000
R12=0x000000010b1badc8: util_initialize+0x2607 in /Library/Java/JavaVirtualMachines/jdk1.8.0_152.jdk/Contents/Home/jre/lib/libjdwp.dylib at 0x000000010b193000
R13=0x00007fb5d6e24530 is an unknown value
R14=0x000000000000000a is an unknown value
R15=0x000000010b1b97b5: util_initialize+0xff4 in /Library/Java/JavaVirtualMachines/jdk1.8.0_152.jdk/Contents/Home/jre/lib/libjdwp.dylib at 0x000000010b193000


Stack: [0x000070000ad59000,0x000070000ae59000],  sp=0x000070000ae58b00,  free space=1022k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [libjdwp.dylib+0x21c8c]  jvmtiAllocate+0x1d
C  [libdt_socket.dylib+0x1792]  setLastError+0x87
C  [libdt_socket.dylib+0x12ff]  socketTransport_readPacket+0x67
C  [libjdwp.dylib+0x1ea95]  transport_receivePacket+0x1a
C  [libjdwp.dylib+0xe665]  reader+0x72
V  [libjvm.dylib+0x39c1a8]
V  [libjvm.dylib+0x56e327]
V  [libjvm.dylib+0x56fa14]
V  [libjvm.dylib+0x48d542]
C  [libsystem_pthread.dylib+0x3661]  _pthread_body+0x154
C  [libsystem_pthread.dylib+0x350d]  _pthread_body+0x0
C  [libsystem_pthread.dylib+0x2bf9]  thread_start+0xd
C  0x0000000000000000


---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
  0x00007fb5d801e800 JavaThread "Service Thread" daemon [_thread_blocked, id=22787, stack(0x000070000b268000,0x000070000b368000)]
  0x00007fb5d702b000 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=43267, stack(0x000070000b165000,0x000070000b265000)]
  0x00007fb5d7802800 JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=22275, stack(0x000070000b062000,0x000070000b162000)]
  0x00007fb5d7801800 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=21763, stack(0x000070000af5f000,0x000070000b05f000)]
  0x00007fb5d9048000 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=16387, stack(0x000070000ae5c000,0x000070000af5c000)]
=>0x00007fb5db001800 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=15875, stack(0x000070000ad59000,0x000070000ae59000)]
  0x00007fb5db001000 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=15619, stack(0x000070000ac56000,0x000070000ad56000)]
  0x00007fb5db000000 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=17415, stack(0x000070000ab53000,0x000070000ac53000)]
  0x00007fb5d9818000 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=15111, stack(0x000070000aa50000,0x000070000ab50000)]
  0x00007fb5d8037800 JavaThread "Finalizer" daemon [_thread_blocked, id=18691, stack(0x000070000a94d000,0x000070000aa4d000)]
  0x00007fb5d8002800 JavaThread "Reference Handler" daemon [_thread_blocked, id=12547, stack(0x000070000a84a000,0x000070000a94a000)]
  0x00007fb5d8001000 JavaThread "main" [_thread_blocked, id=6915, stack(0x0000700009e2c000,0x0000700009f2c000)]

Other Threads:
  0x00007fb5d8002000 VMThread [stack: 0x000070000a747000,0x000070000a847000] [id=18947]

VM state:at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fb5d6f0bd90] Threads_lock - owner thread: 0x00007fb5d8002000

Heap:
 PSYoungGen      total 410624K, used 11365K [0x000000076ab00000, 0x0000000785c00000, 0x00000007c0000000)
  eden space 379904K, 2% used [0x000000076ab00000,0x000000076b6195e0,0x0000000781e00000)
  from space 30720K, 0% used [0x0000000783e00000,0x0000000783e00000,0x0000000785c00000)
  to   space 31744K, 0% used [0x0000000781e00000,0x0000000781e00000,0x0000000783d00000)
 ParOldGen       total 131072K, used 51001K [0x00000006c0000000, 0x00000006c8000000, 0x000000076ab00000)
  object space 131072K, 38% used [0x00000006c0000000,0x00000006c31ce418,0x00000006c8000000)
 Metaspace       used 26651K, capacity 27308K, committed 28592K, reserved 1075200K
  class space    used 3063K, capacity 3269K, committed 3504K, reserved 1048576K

Card table byte_map: [0x000000010d5ac000,0x000000010ddad000] byte_map_base: 0x0000000109fac000

Marking Bits: (ParMarkBitMap*) 0x000000010caf66c0
 Begin Bits: [0x000000010e303000, 0x0000000112303000)
 End Bits:   [0x0000000112303000, 0x0000000116303000)

Polling page: 0x000000010b192000

CodeCache: size=245760Kb used=20911Kb max_used=22051Kb free=224848Kb
 bounds [0x00000001178d1000, 0x0000000118ea1000, 0x00000001268d1000]
 total_blobs=5492 nmethods=5075 adapters=333
 compilation: enabled

Compilation events (10 events):
Event: 10.686 Thread 0x00007fb5d702b000 6128       2       java.util.zip.Inflater::finalize (5 bytes)
Event: 10.687 Thread 0x00007fb5d702b000 nmethod 6128 0x0000000117c55710 code [0x0000000117c55880, 0x0000000117c55a08]
Event: 10.687 Thread 0x00007fb5d702b000 6129       2       java.util.zip.ZipFile::finalize (5 bytes)
Event: 10.687 Thread 0x00007fb5d702b000 nmethod 6129 0x00000001180e3b10 code [0x00000001180e3c80, 0x00000001180e3e08]
Event: 10.692 Thread 0x00007fb5d702b000 6130       2       java.util.concurrent.ConcurrentHashMap$ValueIterator::next (34 bytes)
Event: 10.692 Thread 0x00007fb5d702b000 nmethod 6130 0x00000001184257d0 code [0x0000000118425960, 0x0000000118425bd8]
Event: 10.692 Thread 0x00007fb5d702b000 6131       2       org.eclipse.sisu.inject.RankedSequence$Content::remove (113 bytes)
Event: 10.692 Thread 0x00007fb5d702b000 nmethod 6131 0x0000000117c05b90 code [0x0000000117c05d40, 0x0000000117c06398]
Event: 10.693 Thread 0x00007fb5d702b000 6132       1       java.util.logging.Logger::getName (5 bytes)
Event: 10.693 Thread 0x00007fb5d702b000 nmethod 6132 0x0000000118803650 code [0x00000001188037a0, 0x00000001188038b0]

GC Heap History (10 events):
Event: 8.268 GC heap before
{Heap before GC invocations=10 (full 1):
 PSYoungGen      total 266240K, used 266210K [0x000000076ab00000, 0x0000000783980000, 0x00000007c0000000)
  eden space 234496K, 100% used [0x000000076ab00000,0x0000000779000000,0x0000000779000000)
  from space 31744K, 99% used [0x000000077b400000,0x000000077d2f8900,0x000000077d300000)
  to   space 36864K, 0% used [0x0000000779000000,0x0000000779000000,0x000000077b400000)
 ParOldGen       total 131072K, used 35046K [0x00000006c0000000, 0x00000006c8000000, 0x000000076ab00000)
  object space 131072K, 26% used [0x00000006c0000000,0x00000006c22399a0,0x00000006c8000000)
 Metaspace       used 27442K, capacity 28090K, committed 28208K, reserved 1075200K
  class space    used 3173K, capacity 3339K, committed 3376K, reserved 1048576K
Event: 8.293 GC heap after
Heap after GC invocations=10 (full 1):
 PSYoungGen      total 271360K, used 16099K [0x000000076ab00000, 0x0000000781380000, 0x00000007c0000000)
  eden space 234496K, 0% used [0x000000076ab00000,0x000000076ab00000,0x0000000779000000)
  from space 36864K, 43% used [0x0000000779000000,0x0000000779fb8ee0,0x000000077b400000)
  to   space 35328K, 0% used [0x000000077f100000,0x000000077f100000,0x0000000781380000)
 ParOldGen       total 131072K, used 51836K [0x00000006c0000000, 0x00000006c8000000, 0x000000076ab00000)
  object space 131072K, 39% used [0x00000006c0000000,0x00000006c329f3e0,0x00000006c8000000)
 Metaspace       used 27442K, capacity 28090K, committed 28208K, reserved 1075200K
  class space    used 3173K, capacity 3339K, committed 3376K, reserved 1048576K
}
Event: 8.758 GC heap before
{Heap before GC invocations=11 (full 1):
 PSYoungGen      total 271360K, used 250595K [0x000000076ab00000, 0x0000000781380000, 0x00000007c0000000)
  eden space 234496K, 100% used [0x000000076ab00000,0x0000000779000000,0x0000000779000000)
  from space 36864K, 43% used [0x0000000779000000,0x0000000779fb8ee0,0x000000077b400000)
  to   space 35328K, 0% used [0x000000077f100000,0x000000077f100000,0x0000000781380000)
 ParOldGen       total 131072K, used 51836K [0x00000006c0000000, 0x00000006c8000000, 0x000000076ab00000)
  object space 131072K, 39% used [0x00000006c0000000,0x00000006c329f3e0,0x00000006c8000000)
 Metaspace       used 27446K, capacity 28090K, committed 28208K, reserved 1075200K
  class space    used 3174K, capacity 3339K, committed 3376K, reserved 1048576K
Event: 8.774 GC heap after
Heap after GC invocations=11 (full 1):
 PSYoungGen      total 334848K, used 15336K [0x000000076ab00000, 0x0000000781400000, 0x00000007c0000000)
  eden space 299520K, 0% used [0x000000076ab00000,0x000000076ab00000,0x000000077cf80000)
  from space 35328K, 43% used [0x000000077f100000,0x000000077fffa000,0x0000000781380000)
  to   space 34304K, 0% used [0x000000077cf80000,0x000000077cf80000,0x000000077f100000)
 ParOldGen       total 131072K, used 51844K [0x00000006c0000000, 0x00000006c8000000, 0x000000076ab00000)
  object space 131072K, 39% used [0x00000006c0000000,0x00000006c32a13e0,0x00000006c8000000)
 Metaspace       used 27446K, capacity 28090K, committed 28208K, reserved 1075200K
  class space    used 3174K, capacity 3339K, committed 3376K, reserved 1048576K
}
Event: 9.851 GC heap before
{Heap before GC invocations=12 (full 1):
 PSYoungGen      total 334848K, used 314856K [0x000000076ab00000, 0x0000000781400000, 0x00000007c0000000)
  eden space 299520K, 100% used [0x000000076ab00000,0x000000077cf80000,0x000000077cf80000)
  from space 35328K, 43% used [0x000000077f100000,0x000000077fffa000,0x0000000781380000)
  to   space 34304K, 0% used [0x000000077cf80000,0x000000077cf80000,0x000000077f100000)
 ParOldGen       total 131072K, used 51844K [0x00000006c0000000, 0x00000006c8000000, 0x000000076ab00000)
  object space 131072K, 39% used [0x00000006c0000000,0x00000006c32a13e0,0x00000006c8000000)
 Metaspace       used 27461K, capacity 28154K, committed 28208K, reserved 1075200K
  class space    used 3174K, capacity 3339K, committed 3376K, reserved 1048576K
Event: 9.869 GC heap after
Heap after GC invocations=12 (full 1):
 PSYoungGen      total 333824K, used 19064K [0x000000076ab00000, 0x0000000785e00000, 0x00000007c0000000)
  eden space 299520K, 0% used [0x000000076ab00000,0x000000076ab00000,0x000000077cf80000)
  from space 34304K, 55% used [0x000000077cf80000,0x000000077e21e180,0x000000077f100000)
  to   space 32768K, 0% used [0x0000000783e00000,0x0000000783e00000,0x0000000785e00000)
 ParOldGen       total 131072K, used 51852K [0x00000006c0000000, 0x00000006c8000000, 0x000000076ab00000)
  object space 131072K, 39% used [0x00000006c0000000,0x00000006c32a33e0,0x00000006c8000000)
 Metaspace       used 27461K, capacity 28154K, committed 28208K, reserved 1075200K
  class space    used 3174K, capacity 3339K, committed 3376K, reserved 1048576K
}
Event: 10.504 GC heap before
{Heap before GC invocations=13 (full 1):
 PSYoungGen      total 333824K, used 162397K [0x000000076ab00000, 0x0000000785e00000, 0x00000007c0000000)
  eden space 299520K, 47% used [0x000000076ab00000,0x00000007736f9550,0x000000077cf80000)
  from space 34304K, 55% used [0x000000077cf80000,0x000000077e21e180,0x000000077f100000)
  to   space 32768K, 0% used [0x0000000783e00000,0x0000000783e00000,0x0000000785e00000)
 ParOldGen       total 131072K, used 51852K [0x00000006c0000000, 0x00000006c8000000, 0x000000076ab00000)
  object space 131072K, 39% used [0x00000006c0000000,0x00000006c32a33e0,0x00000006c8000000)
 Metaspace       used 27695K, capacity 28434K, committed 28592K, reserved 1075200K
  class space    used 3199K, capacity 3411K, committed 3504K, reserved 1048576K
Event: 10.523 GC heap after
Heap after GC invocations=13 (full 1):
 PSYoungGen      total 410624K, used 16319K [0x000000076ab00000, 0x0000000785c00000, 0x00000007c0000000)
  eden space 379904K, 0% used [0x000000076ab00000,0x000000076ab00000,0x0000000781e00000)
  from space 30720K, 53% used [0x0000000783e00000,0x0000000784defe48,0x0000000785c00000)
  to   space 31744K, 0% used [0x0000000781e00000,0x0000000781e00000,0x0000000783d00000)
 ParOldGen       total 131072K, used 51860K [0x00000006c0000000, 0x00000006c8000000, 0x000000076ab00000)
  object space 131072K, 39% used [0x00000006c0000000,0x00000006c32a53e0,0x00000006c8000000)
 Metaspace       used 27695K, capacity 28434K, committed 28592K, reserved 1075200K
  class space    used 3199K, capacity 3411K, committed 3504K, reserved 1048576K
}
Event: 10.523 GC heap before
{Heap before GC invocations=14 (full 2):
 PSYoungGen      total 410624K, used 16319K [0x000000076ab00000, 0x0000000785c00000, 0x00000007c0000000)
  eden space 379904K, 0% used [0x000000076ab00000,0x000000076ab00000,0x0000000781e00000)
  from space 30720K, 53% used [0x0000000783e00000,0x0000000784defe48,0x0000000785c00000)
  to   space 31744K, 0% used [0x0000000781e00000,0x0000000781e00000,0x0000000783d00000)
 ParOldGen       total 131072K, used 51860K [0x00000006c0000000, 0x00000006c8000000, 0x000000076ab00000)
  object space 131072K, 39% used [0x00000006c0000000,0x00000006c32a53e0,0x00000006c8000000)
 Metaspace       used 27695K, capacity 28434K, committed 28592K, reserved 1075200K
  class space    used 3199K, capacity 3411K, committed 3504K, reserved 1048576K
Event: 10.684 GC heap after
Heap after GC invocations=14 (full 2):
 PSYoungGen      total 410624K, used 0K [0x000000076ab00000, 0x0000000785c00000, 0x00000007c0000000)
  eden space 379904K, 0% used [0x000000076ab00000,0x000000076ab00000,0x0000000781e00000)
  from space 30720K, 0% used [0x0000000783e00000,0x0000000783e00000,0x0000000785c00000)
  to   space 31744K, 0% used [0x0000000781e00000,0x0000000781e00000,0x0000000783d00000)
 ParOldGen       total 131072K, used 51001K [0x00000006c0000000, 0x00000006c8000000, 0x000000076ab00000)
  object space 131072K, 38% used [0x00000006c0000000,0x00000006c31ce418,0x00000006c8000000)
 Metaspace       used 26644K, capacity 27308K, committed 28592K, reserved 1075200K
  class space    used 3063K, capacity 3269K, committed 3504K, reserved 1048576K
}

Deoptimization events (10 events):
Event: 10.442 Thread 0x00007fb5d8001000 Uncommon trap: reason=unhandled action=none pc=0x0000000117ea029c method=java.net.URLClassLoader.findClass(Ljava/lang/String;)Ljava/lang/Class; @ 44
Event: 10.443 Thread 0x00007fb5d8001000 Uncommon trap: reason=unhandled action=none pc=0x0000000117ea029c method=java.net.URLClassLoader.findClass(Ljava/lang/String;)Ljava/lang/Class; @ 44
Event: 10.443 Thread 0x00007fb5d8001000 Uncommon trap: reason=unhandled action=none pc=0x0000000117ea029c method=java.net.URLClassLoader.findClass(Ljava/lang/String;)Ljava/lang/Class; @ 44
Event: 10.444 Thread 0x00007fb5d8001000 Uncommon trap: reason=unhandled action=none pc=0x0000000117ea029c method=java.net.URLClassLoader.findClass(Ljava/lang/String;)Ljava/lang/Class; @ 44
Event: 10.444 Thread 0x00007fb5d8001000 Uncommon trap: reason=unhandled action=none pc=0x0000000117ea029c method=java.net.URLClassLoader.findClass(Ljava/lang/String;)Ljava/lang/Class; @ 44
Event: 10.444 Thread 0x00007fb5d8001000 Uncommon trap: reason=unhandled action=none pc=0x0000000117ea029c method=java.net.URLClassLoader.findClass(Ljava/lang/String;)Ljava/lang/Class; @ 44
Event: 10.444 Thread 0x00007fb5d8001000 Uncommon trap: reason=unhandled action=none pc=0x0000000117ea029c method=java.net.URLClassLoader.findClass(Ljava/lang/String;)Ljava/lang/Class; @ 44
Event: 10.501 Thread 0x00007fb5d8001000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000001182bdad8 method=java.util.Properties$LineReader.readLine()I @ 410
Event: 10.686 Thread 0x00007fb5d8001000 Uncommon trap: reason=unhandled action=none pc=0x0000000117ea029c method=java.net.URLClassLoader.findClass(Ljava/lang/String;)Ljava/lang/Class; @ 44
Event: 10.690 Thread 0x00007fb5d8001000 Uncommon trap: reason=unhandled action=none pc=0x0000000117ea029c method=java.net.URLClassLoader.findClass(Ljava/lang/String;)Ljava/lang/Class; @ 44

Classes redefined (0 events):
No events

Internal exceptions (10 events):
Event: 10.399 Thread 0x00007fb5d8001000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException': 100> (0x0000000772f3a1e8) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u152/9742/hotspot/src/share/vm/interpreter/interpreterRuntime.cpp, line 366]
Event: 10.399 Thread 0x00007fb5d8001000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException': 111> (0x0000000772f3b9a0) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u152/9742/hotspot/src/share/vm/interpreter/interpreterRuntime.cpp, line 366]
Event: 10.399 Thread 0x00007fb5d8001000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException': 113> (0x0000000772f3d6e0) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u152/9742/hotspot/src/share/vm/interpreter/interpreterRuntime.cpp, line 366]
Event: 10.399 Thread 0x00007fb5d8001000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException': 85> (0x0000000772f3f338) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u152/9742/hotspot/src/share/vm/interpreter/interpreterRuntime.cpp, line 366]
Event: 10.399 Thread 0x00007fb5d8001000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException': 79> (0x0000000772f40930) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u152/9742/hotspot/src/share/vm/interpreter/interpreterRuntime.cpp, line 366]
Event: 10.404 Thread 0x00007fb5d8001000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException': 37> (0x0000000772fa1490) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u152/9742/hotspot/src/share/vm/interpreter/interpreterRuntime.cpp, line 366]
Event: 10.404 Thread 0x00007fb5d8001000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException': 72> (0x0000000772fa2a88) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u152/9742/hotspot/src/share/vm/interpreter/interpreterRuntime.cpp, line 366]
Event: 10.404 Thread 0x00007fb5d8001000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException': 83> (0x0000000772fa41c0) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u152/9742/hotspot/src/share/vm/interpreter/interpreterRuntime.cpp, line 366]
Event: 10.404 Thread 0x00007fb5d8001000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException': 83> (0x0000000772fa59b0) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u152/9742/hotspot/src/share/vm/interpreter/interpreterRuntime.cpp, line 366]
Event: 10.441 Thread 0x00007fb5d8001000 Implicit null exception at 0x00000001186e0cdd to 0x00000001186e1281

Events (10 events):
Event: 10.691 Thread 0x00007fb5d8001000 DEOPT UNPACKING pc=0x0000000117918633 sp=0x0000700009f295a8 mode 1
Event: 10.691 Thread 0x00007fb5d8001000 DEOPT PACKING pc=0x0000000117edd1ec sp=0x0000700009f298c0
Event: 10.691 Thread 0x00007fb5d8001000 DEOPT UNPACKING pc=0x0000000117918633 sp=0x0000700009f29600 mode 1
Event: 10.691 Thread 0x00007fb5d8001000 DEOPT PACKING pc=0x0000000117aef5a1 sp=0x0000700009f29830
Event: 10.691 Thread 0x00007fb5d8001000 DEOPT UNPACKING pc=0x0000000117918633 sp=0x0000700009f29590 mode 1
Event: 10.691 Thread 0x00007fb5d8001000 DEOPT PACKING pc=0x00000001179d1214 sp=0x0000700009f29950
Event: 10.691 Thread 0x00007fb5d8001000 DEOPT UNPACKING pc=0x0000000117918633 sp=0x0000700009f29608 mode 1
Event: 10.693 Thread 0x00007fb5db049000 Thread added: 0x00007fb5db049000
Event: 10.693 Thread 0x00007fb5db049000 Thread exited: 0x00007fb5db049000
Event: 10.694 Executing VM operation: Exit


Dynamic libraries:
0x000000001b1bb000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x000000001b1bb000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x000000001b1bb000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x000000001b1bb000 	/usr/lib/libz.1.dylib
0x000000001b1bb000 	/usr/lib/libSystem.B.dylib
0x000000001b1bb000 	/usr/lib/libobjc.A.dylib
0x000000001b1bb000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x000000001b1bb000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x000000001b1bb000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x000000001b1bb000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x000000001b1bb000 	/System/Library/PrivateFrameworks/RemoteViewServices.framework/Versions/A/RemoteViewServices
0x000000001b1bb000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x000000001b1bb000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x000000001b1bb000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x000000001b1bb000 	/System/Library/PrivateFrameworks/DesktopServicesPriv.framework/Versions/A/DesktopServicesPriv
0x000000001b1bb000 	/usr/lib/libenergytrace.dylib
0x000000001b1bb000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x000000001b1bb000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x000000001b1bb000 	/usr/lib/libScreenReader.dylib
0x000000001b1bb000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x000000001b1bb000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x000000001b1bb000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x000000001b1bb000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x000000001b1bb000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x000000001b1bb000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x000000001b1bb000 	/usr/lib/libicucore.A.dylib
0x000000001b1bb000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x000000001b1bb000 	/System/Library/PrivateFrameworks/SignpostNotification.framework/Versions/A/SignpostNotification
0x000000001b1bb000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x000000001b1bb000 	/usr/lib/libauto.dylib
0x000000001b1bb000 	/usr/lib/libxml2.2.dylib
0x000000001b1bb000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x000000001b1bb000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x000000001b1bb000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x000000001b1bb000 	/usr/lib/liblangid.dylib
0x000000001b1bb000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x000000001b1bb000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x000000001b1bb000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x000000001b1bb000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x000000001b1bb000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x000000001b1bb000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x000000001b1bb000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x000000001b1bb000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x000000001b1bb000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x000000001b1bb000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x000000001b1bb000 	/System/Library/PrivateFrameworks/Backup.framework/Versions/A/Backup
0x000000001b1bb000 	/usr/lib/libarchive.2.dylib
0x000000001b1bb000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x000000001b1bb000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x000000001b1bb000 	/usr/lib/libCRFSuite.dylib
0x000000001b1bb000 	/usr/lib/libc++.1.dylib
0x000000001b1bb000 	/usr/lib/libc++abi.dylib
0x000000001b1bb000 	/usr/lib/system/libcache.dylib
0x000000001b1bb000 	/usr/lib/system/libcommonCrypto.dylib
0x000000001b1bb000 	/usr/lib/system/libcompiler_rt.dylib
0x000000001b1bb000 	/usr/lib/system/libcopyfile.dylib
0x000000001b1bb000 	/usr/lib/system/libcorecrypto.dylib
0x000000001b1bb000 	/usr/lib/system/libdispatch.dylib
0x000000001b1bb000 	/usr/lib/system/libdyld.dylib
0x000000001b1bb000 	/usr/lib/system/libkeymgr.dylib
0x000000001b1bb000 	/usr/lib/system/liblaunch.dylib
0x000000001b1bb000 	/usr/lib/system/libmacho.dylib
0x000000001b1bb000 	/usr/lib/system/libquarantine.dylib
0x000000001b1bb000 	/usr/lib/system/libremovefile.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_asl.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_blocks.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_c.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_configuration.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_coreservices.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_darwin.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_dnssd.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_info.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_m.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_malloc.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_network.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_networkextension.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_notify.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_sandbox.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_secinit.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_kernel.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_platform.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_pthread.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_symptoms.dylib
0x000000001b1bb000 	/usr/lib/system/libsystem_trace.dylib
0x000000001b1bb000 	/usr/lib/system/libunwind.dylib
0x000000001b1bb000 	/usr/lib/system/libxpc.dylib
0x000000001b1bb000 	/usr/lib/closure/libclosured.dylib
0x000000001b1bb000 	/usr/lib/libbsm.0.dylib
0x000000001b1bb000 	/usr/lib/system/libkxld.dylib
0x000000001b1bb000 	/usr/lib/libOpenScriptingUtil.dylib
0x000000001b1bb000 	/usr/lib/libcoretls.dylib
0x000000001b1bb000 	/usr/lib/libcoretls_cfhelpers.dylib
0x000000001b1bb000 	/usr/lib/libpam.2.dylib
0x000000001b1bb000 	/usr/lib/libsqlite3.dylib
0x000000001b1bb000 	/usr/lib/libxar.1.dylib
0x000000001b1bb000 	/usr/lib/libbz2.1.0.dylib
0x000000001b1bb000 	/usr/lib/liblzma.5.dylib
0x000000001b1bb000 	/usr/lib/libnetwork.dylib
0x000000001b1bb000 	/usr/lib/libapple_nghttp2.dylib
0x000000001b1bb000 	/usr/lib/libpcap.A.dylib
0x000000001b1bb000 	/usr/lib/libboringssl.dylib
0x000000001b1bb000 	/usr/lib/libusrtcp.dylib
0x000000001b1bb000 	/usr/lib/libapple_crypto.dylib
0x000000001b1bb000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x000000001b1bb000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x000000001b1bb000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x000000001b1bb000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x000000001b1bb000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x000000001b1bb000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x000000001b1bb000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x000000001b1bb000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x000000001b1bb000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x000000001b1bb000 	/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS
0x000000001b1bb000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x000000001b1bb000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x000000001b1bb000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x000000001b1bb000 	/usr/lib/libmecabra.dylib
0x000000001b1bb000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x000000001b1bb000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x000000001b1bb000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x000000001b1bb000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/LangAnalysis.framework/Versions/A/LangAnalysis
0x000000001b1bb000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x000000001b1bb000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x000000001b1bb000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x000000001b1bb000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x000000001b1bb000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x000000001b1bb000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x000000001b1bb000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x000000001b1bb000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x000000001b1bb000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x000000001b1bb000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x000000001b1bb000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x000000001b1bb000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x000000001b1bb000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x000000001b1bb000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x000000001b1bb000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x000000001b1bb000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x000000001b1bb000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x000000001b1bb000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x000000001b1bb000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x000000001b1bb000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x000000001b1bb000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x000000001b1bb000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x000000001b1bb000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x000000001b1bb000 	/usr/lib/libFosl_dynamic.dylib
0x000000001b1bb000 	/System/Library/PrivateFrameworks/FaceCore.framework/Versions/A/FaceCore
0x000000001b1bb000 	/System/Library/Frameworks/OpenCL.framework/Versions/A/OpenCL
0x000000001b1bb000 	/usr/lib/libcompression.dylib
0x000000001b1bb000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontParser.dylib
0x000000001b1bb000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x000000001b1bb000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x000000001b1bb000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x000000001b1bb000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x000000001b1bb000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x000000001b1bb000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x000000001b1bb000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x000000001b1bb000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x000000001b1bb000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x000000001b1bb000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x000000001b1bb000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x000000001b1bb000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x000000001b1bb000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x000000001b1bb000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x000000001b1bb000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x000000001b1bb000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x000000001b1bb000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x000000001b1bb000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x000000001b1bb000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x000000001b1bb000 	/usr/lib/libcups.2.dylib
0x000000001b1bb000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x000000001b1bb000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x000000001b1bb000 	/usr/lib/libresolv.9.dylib
0x000000001b1bb000 	/usr/lib/libiconv.2.dylib
0x000000001b1bb000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x000000001b1bb000 	/usr/lib/libheimdal-asn1.dylib
0x000000001b1bb000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x000000001b1bb000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x000000001b1bb000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x000000001b1bb000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x000000001b1bb000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x000000001b1bb000 	/usr/lib/libutil.dylib
0x000000001b1bb000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x000000001b1bb000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x000000001b1bb000 	/usr/lib/libmarisa.dylib
0x000000001b1bb000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x000000001b1bb000 	/usr/lib/libChineseTokenizer.dylib
0x000000001b1bb000 	/usr/lib/libcmph.dylib
0x000000001b1bb000 	/System/Library/PrivateFrameworks/LanguageModeling.framework/Versions/A/LanguageModeling
0x000000001b1bb000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x000000001b1bb000 	/System/Library/Frameworks/ServiceManagement.framework/Versions/A/ServiceManagement
0x000000001b1bb000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x000000001b1bb000 	/usr/lib/libxslt.1.dylib
0x000000001b1bb000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/Ink.framework/Versions/A/Ink
0x000000001b1bb000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x000000001b1bb000 	/usr/lib/libate.dylib
0x000000001b1bb000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x000000001b1bb000 	/System/Library/PrivateFrameworks/Sharing.framework/Versions/A/Sharing
0x000000001b1bb000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x000000001b1bb000 	/System/Library/PrivateFrameworks/AuthKit.framework/Versions/A/AuthKit
0x000000001b1bb000 	/System/Library/PrivateFrameworks/Apple80211.framework/Versions/A/Apple80211
0x000000001b1bb000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x000000001b1bb000 	/System/Library/Frameworks/CoreWLAN.framework/Versions/A/CoreWLAN
0x000000001b1bb000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x000000001b1bb000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x000000001b1bb000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x000000001b1bb000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x000000001b1bb000 	/System/Library/PrivateFrameworks/AppleIDAuthSupport.framework/Versions/A/AppleIDAuthSupport
0x000000001b1bb000 	/System/Library/PrivateFrameworks/KeychainCircle.framework/Versions/A/KeychainCircle
0x000000001b1bb000 	/System/Library/PrivateFrameworks/CoreWiFi.framework/Versions/A/CoreWiFi
0x000000001b1bb000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x000000001b1bb000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x000000001b1bb000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x000000001b1bb000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x000000001b1bb000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x000000001b1bb000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x000000001b1bb000 	/System/Library/CoreServices/Encodings/libSimplifiedChineseConverter.dylib
0x000000010c200000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_152.jdk/Contents/Home/jre/lib/server/libjvm.dylib
0x000000001b1bb000 	/usr/lib/libstdc++.6.0.9.dylib
0x000000010b14e000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_152.jdk/Contents/Home/jre/lib/libverify.dylib
0x000000010b15c000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_152.jdk/Contents/Home/jre/lib/libjava.dylib
0x000000010b193000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_152.jdk/Contents/Home/jre/lib/libjdwp.dylib
0x000000010b1de000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_152.jdk/Contents/Home/jre/lib/libnpt.dylib
0x000000010b1eb000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_152.jdk/Contents/Home/jre/lib/libzip.dylib
0x0000000117823000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_152.jdk/Contents/Home/jre/lib/libdt_socket.dylib
0x0000000129fd3000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_152.jdk/Contents/Home/jre/lib/libnet.dylib
0x0000000129fea000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_152.jdk/Contents/Home/jre/lib/libnio.dylib

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,suspend=y,address=localhost:63063 -Dmaven.home=EMBEDDED -Dclassworlds.conf=/Users/<USER>/sales/workspace/.metadata/.plugins/org.eclipse.m2e.launching/launches/m2conf6153578937790771793.tmp -Dmaven.multiModuleProjectDirectory=/Users/<USER>/git/qanat-aliyun-inc-com/qanat-blink-cdp-sink -Dfile.encoding=UTF-8 
java_command: org.codehaus.plexus.classworlds.launcher.Launcher -B -Dmaven.test.skip=true -DskipTests -s /Users/<USER>/.m2/settings.xml clean install
java_class_path (initial): /Applications/Eclipse.app/Contents/Eclipse/plugins/org.eclipse.m2e.maven.runtime_1.8.2.20171007-0216/jars/plexus-classworlds-2.5.2.jar
Launcher Type: SUN_STANDARD

Environment Variables:
PATH=/usr/bin:/bin:/usr/sbin:/sbin
SHELL=/bin/bash

Signal Handlers:
SIGSEGV: [libjvm.dylib+0x5b494d], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_ONSTACK|SA_RESTART|SA_SIGINFO
SIGBUS: [libjvm.dylib+0x5b494d], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGFPE: [libjvm.dylib+0x48ab54], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGPIPE: [libjvm.dylib+0x48ab54], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGXFSZ: [libjvm.dylib+0x48ab54], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGILL: [libjvm.dylib+0x48ab54], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGUSR1: SIG_DFL, sa_mask[0]=00000000000000000000000000000000, sa_flags=none
SIGUSR2: [libjvm.dylib+0x48a672], sa_mask[0]=00100000000000000000000000000000, sa_flags=SA_RESTART|SA_SIGINFO
SIGHUP: [libjvm.dylib+0x488c49], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGINT: [libjvm.dylib+0x488c49], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGTERM: [libjvm.dylib+0x488c49], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGQUIT: [libjvm.dylib+0x488c49], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO


---------------  S Y S T E M  ---------------

OS:Bsduname:Darwin 17.7.0 Darwin Kernel Version 17.7.0: Thu Jun 21 22:53:14 PDT 2018; root:xnu-4570.71.2~1/RELEASE_X86_64 x86_64
rlimit: STACK 8192k, CORE 0k, NPROC 1418, NOFILE 10240, AS infinity
load average:2.95 2.88 2.73

CPU:total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 70 stepping 1, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2

Memory: 4k page, physical 16777216k(1993452k free)

/proc/meminfo:


vm_info: Java HotSpot(TM) 64-Bit Server VM (25.152-b16) for bsd-amd64 JRE (1.8.0_152-b16), built on Sep 14 2017 02:31:13 by "java_re" with gcc 4.2.1 (Based on Apple Inc. build 5658) (LLVM build 2336.11.00)

time: Wed Jan 15 15:01:19 2020
elapsed time: 10 seconds (0d 0h 0m 10s)

