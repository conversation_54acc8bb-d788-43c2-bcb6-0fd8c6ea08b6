package com.aliyun.wormhole.qanat.blink.sink;

public class OdsMdpObjectDataModelSink extends AbstractOdsMdpSink {

    @Override
    public String getName() {
        return "OdsMdpObjectDataModelSink";
    }

	@Override
	protected String getDomainCode() {
		return null;
	}

	@Override
	protected String getObjectUniqueCode() {
		return userParamsMap.get("object_unique_code");
	}

	@Override
	protected void doOpen() {
		
	}

	@Override
	protected String getTableName() {
        return "ods_mdp_am_" + getObjectUniqueCode();
	}

	@Override
	protected String getPkField() {
		return userParamsMap.get("object_pk");
	}
}
