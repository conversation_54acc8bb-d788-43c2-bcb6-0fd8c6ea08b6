package com.aliyun.wormhole.qanat.blink.sink;

import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.blink.streaming.connector.custom.api.CustomSinkBase;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.aliyun.wormhole.qanat.stream.event.StreamEvent;
import com.aliyun.wormhole.qanat.stream.event.StreamEventField;
import com.aliyun.wormhole.qanat.stream.event.export.MetaqEventExporter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CdpAdb3Sink extends CustomSinkBase {

    private final static Logger LOG = LoggerFactory.getLogger(CdpAdb3Sink.class);

    private String url;
    private String tableName;
    private String username;
    private String password;
    private Connection connnection = null;
    private String streamEventSwitch = null;
    private MetaqEventExporter exporter;
    
    @Override
    public void open(int i, int i1) throws IOException {
        LOG.info(this.getName() + " open({},{})", i, i1);
        try {
            url = userParamsMap.get("url");
            tableName = userParamsMap.get("tablename");
            username = userParamsMap.get("username");
            password = userParamsMap.get("password");
            Class.forName("com.mysql.jdbc.Driver");
            connnection = DriverManager.getConnection(url, username, password);
            
            streamEventSwitch = userParamsMap.get("streamevent");
            streamEventSwitch = StringUtils.isBlank(streamEventSwitch) ? "enable" : streamEventSwitch;
            if ("enable".equalsIgnoreCase(streamEventSwitch)) {
                String eventUnit = userParamsMap.get("eventnuit");
                String eventTopic = userParamsMap.get("eventtopic");
                if (StringUtils.isBlank(eventTopic)) {
                    eventTopic = "QANAT_BLINK_ADB3_SINK_LOG_TOPIC";
                }
                String eventGroup = userParamsMap.get("eventgroup");
                if (StringUtils.isBlank(eventGroup)) {
                    eventGroup = "PID_" + getName() + "_" + tableName;
                }
                exporter = new MetaqEventExporter(eventTopic, eventGroup, eventUnit);
                exporter.init();
            }
        } catch (Exception e) {
            LOG.error("sql exec failed:{}", e.getMessage(), e);
        }
    }

    @Override
    public void writeAddRecord(Row row) throws IOException {
        LOG.info("writeAddRecord start");
        try {
            Map<String, Object> inputData = getInputData(row, rowTypeInfo);
            LOG.info("inputDat={}", JSON.toJSONString(inputData));
            String msg = String.valueOf(inputData.get("msg"));
            
            JSONArray targetInfoJsonArray = JSON.parseArray(msg);
            if (targetInfoJsonArray == null || targetInfoJsonArray.size() == 0) {
                return;
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<String> replaceValuesList = new ArrayList<>();
            List<String> deleteSqlList = new ArrayList<>();
            List<StreamEvent> streamEventList = new ArrayList<>();
            List<Long> cidList = new ArrayList<>();
            for (int i = 0; i < targetInfoJsonArray.size(); i++) {
                JSONObject targetInfoJson = targetInfoJsonArray.getJSONObject(i);
                String targetInfoId = targetInfoJson.getString("targetInfoId");
                cidList.add(Long.valueOf(targetInfoId));
                String operate = targetInfoJson.getString("operate");
                JSONArray subjectInfoJsonArray = targetInfoJson.getJSONArray("subjectInfoList");
                if (subjectInfoJsonArray == null || subjectInfoJsonArray.size() == 0) {
                    continue;
                }
                for (int j = 0; j < subjectInfoJsonArray.size(); j++) {
                    JSONObject subjectInfoJson = subjectInfoJsonArray.getJSONObject(j);
                    String subjectCode = subjectInfoJson.getString("subjectCode");
                    String subjectDataType = subjectInfoJson.getString("subjectDataType");
                    String value = subjectInfoJson.getString("value");
                    
                    StreamEvent streamEvent = new StreamEvent();
                    Map<String, StreamEventField> fieldValues = new HashMap<>();
                    if ("PUT".equalsIgnoreCase(operate)) {
                        String gmtModified = sdf.format(new Date());
                        replaceValuesList.add(String.format("(%s,'%s','%s','%s','%s')", targetInfoId, subjectCode, subjectDataType, value, gmtModified));
                        streamEvent.setEventType(1);
                        setEventField(fieldValues, "cid", 2, targetInfoId, false);
                        setEventField(fieldValues, "tag_code", 5, subjectCode, false);
                        setEventField(fieldValues, "tag_type", 5, subjectDataType, false);
                        setEventField(fieldValues, "tag_value", 5, value, false);
                    } else {
                        deleteSqlList.add(String.format("DELETE FROM %s WHERE cid=%s and tag_code='%s';", tableName, targetInfoId, subjectCode));
                        streamEvent.setEventType(3);
                        setEventField(fieldValues, "cid", 2, targetInfoId, true);
                        setEventField(fieldValues, "tag_code", 5, subjectCode, true);
                        setEventField(fieldValues, "tag_type", 5, subjectDataType, true);
                        setEventField(fieldValues, "tag_value", 5, value, true);
                    }
                    streamEvent.setFieldValues(fieldValues);
                    String jdbcUrl = this.url.split("\\?")[0];
                    String[] tokens = jdbcUrl.split("/");
                    String dbName = tokens[tokens.length - 1];
                    streamEvent.setDbName(dbName);
                    streamEvent.setTableName(tableName);
                    streamEvent.setPkField(Arrays.asList("cid", "tag_code"));
                    streamEventList.add(streamEvent);
                }
            }
            if (CollectionUtils.isNotEmpty(replaceValuesList)) {
                String replaceIntoSql = String.format("REPLACE INTO %s(%s) values %s;", tableName, "cid,tag_code,tag_type,tag_value,gmt_modified", StringUtils.join(replaceValuesList, ","));
                executeSql(replaceIntoSql);
            }
            if (CollectionUtils.isNotEmpty(deleteSqlList)) {
                for (String sql : deleteSqlList) {
                    executeSql(sql);
                }
            }
            if ("enable".equalsIgnoreCase(streamEventSwitch)) {
                if (CollectionUtils.isNotEmpty(streamEventList)) {
                    new Thread(()->{
                        for (StreamEvent event : streamEventList) {
                            event.setTs(System.currentTimeMillis());
                            String tag = event.getDbName() + "__" + event.getTableName();
                            exporter.export(tag, event);
                        }
                    }).start();
                }
            }
            
            //更新标签多值列
            List<String> updateValuesList = new ArrayList<>();
            String cids = StringUtils.join(cidList, ",");
            String selectSql = String.format("select cid, group_concat(tag_code||'-'||replace(tag_value,',','.')) as tags from devata_cid_cdp_tags where cid in (%s) group by cid", cids);
            List<Map<String, String>> dataList = querySql(selectSql);
            if (CollectionUtils.isNotEmpty(dataList)) {
                List<StreamEvent> updStreamEventList = new ArrayList<>();
                for (Map<String, String> data : dataList) {
                    String cid = data.get("cid");
                    String tags = data.get("tags");
                    String [] tagCodeValueArray = tags.split(",");
                    List<String> tagCodeValueList = new ArrayList<>();
                    for (String tagCodeValue : tagCodeValueArray) {
                        String[] tagTokenArray = tagCodeValue.split("-", 2);
                        String tagCode = tagTokenArray[0];
                        String tagValues = tagTokenArray[1];
                        String[] tagValueArray = tagValues.split("\\.");
                        for (String tagValue : tagValueArray) {
                            tagCodeValueList.add(tagCode + "-" + tagValue);
                        }
                    }
                    String newTags = StringUtils.join(tagCodeValueList, ",");
                    updateValuesList.add(String.format("(%s,'%s')", cid, newTags));
                    
                    StreamEvent streamEvent = new StreamEvent();
                    streamEvent.setEventType(2);
                    Map<String, StreamEventField> fieldValues = new HashMap<>();
                    setEventField(fieldValues, "cid", 2, data.get("cid"), false);
                    setEventField(fieldValues, "tags", 5, data.get("tags"), false);
                    streamEvent.setFieldValues(fieldValues);
                    String jdbcUrl = this.url.split("\\?")[0];
                    String[] tokens = jdbcUrl.split("/");
                    String dbName = tokens[tokens.length - 1];
                    streamEvent.setDbName(dbName);
                    streamEvent.setTableName("dwd_devata_cid_info");
                    streamEvent.setPkField(Arrays.asList("cid"));
                    updStreamEventList.add(streamEvent);
                }
                if (CollectionUtils.isNotEmpty(updateValuesList)) {
                    String replaceIntoSql = String.format("UPDATE INTO %s(%s) values %s;", "dwd_devata_cid_info", "cid,tags", StringUtils.join(updateValuesList, ","));
                    executeSql(replaceIntoSql);
                }
                if ("enable".equalsIgnoreCase(streamEventSwitch)) {
                    if (CollectionUtils.isNotEmpty(updStreamEventList)) {
                        new Thread(()->{
                            for (StreamEvent event : updStreamEventList) {
                                event.setTs(System.currentTimeMillis());
                                String tag = event.getDbName() + "__" + event.getTableName();
                                exporter.export(tag, event);
                            }
                        }).start();
                    }
                }
            }
            LOG.info("writeAddRecord finish");
        } catch(Exception e) {
            LOG.error("writeAddRecord failed", e);
        }
    }

    private void setEventField(Map<String, StreamEventField> fieldValues, String filedName, int filedType, String fieldValue, boolean isDel) {
        StreamEventField eventFiled = new StreamEventField();
        eventFiled.setFieldName(filedName);
        eventFiled.setFieldType(filedType);
        if (isDel) {
            eventFiled.setOldValue(fieldValue);
        } else {
            eventFiled.setNewValue(fieldValue);
        }
        fieldValues.put(filedName, eventFiled);
    }

    private void executeSql(String sql) {
        LOG.info("before exec sql={}", sql);
        Statement statement = null;
        try {
            statement = connnection.createStatement();
            int execCnt = statement.executeUpdate(sql);
            LOG.info("after exec sql={}, execCnt={}", sql, execCnt);
        } catch(Exception e) {
            LOG.error("executeSql failed", e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
        }
    }

    private List<Map<String, String>> querySql(String sql) {
        LOG.info("before exec sql={}", sql);
        List<Map<String, String>> dataList = new ArrayList<>();
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = connnection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                Map<String, String> data = new HashMap<>();
                data.put("cid", resultSet.getString("cid"));
                data.put("tags", resultSet.getString("tags"));
                dataList.add(data);
            }
            LOG.info("after exec sql cnt={}", dataList.size());
        } catch(Exception e) {
            LOG.error("querySql failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                }
                resultSet = null;
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
                statement = null;
            }
        }
        return dataList;
    }

    @Override
    public void writeDeleteRecord(Row row) throws IOException {
        
    }
    
    private Map<String, Object> getInputData(Row row, RowTypeInfo rowTypeInfo) {
        Map<String, Object> inputData = new HashMap<>();
        for (String fieldName : rowTypeInfo.getFieldNames()) {
            Object value = row.getField(rowTypeInfo.getFieldIndex(fieldName));
            if (value instanceof java.sql.Timestamp) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String str = df.format((java.sql.Timestamp)value);
                value = str;
            }
            if (value instanceof java.math.BigDecimal) {
                value = ((java.math.BigDecimal)value).doubleValue();
            }
            inputData.put(fieldName, value);
        }
        return inputData;
    }

    @Override
    public void sync() throws IOException {
    }

    @Override
    public void close() throws IOException {
        if (connnection != null) {
            try {
                connnection.close();
            } catch (SQLException e) {
            }
            connnection = null;
        }
        if (exporter != null) {
            exporter.close();
        }
    }

    @Override
    public String getName() {
        return "CdpAdb3Sink";
    }
    
    public static void main(String[] args) {
        String a = "EastChinaLALabel__AnnualSalesVolume-500W-1000W";
        String[] tokens = a.split("-", 2);
        System.out.println(tokens[0]);
        System.out.println(tokens[1]);
    }
}
