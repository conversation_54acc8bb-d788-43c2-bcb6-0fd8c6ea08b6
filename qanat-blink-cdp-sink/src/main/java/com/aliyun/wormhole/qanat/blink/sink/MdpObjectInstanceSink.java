package com.aliyun.wormhole.qanat.blink.sink;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.blink.streaming.connector.custom.api.CustomSinkBase;
import com.alibaba.blink.streaming.connectors.common.util.BlinkStringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.aliyun.wormhole.qanat.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.stream.event.StreamEvent;
import com.aliyun.wormhole.qanat.stream.event.StreamEventField;
import com.aliyun.wormhole.qanat.stream.event.export.EventExporter;
import com.aliyun.wormhole.qanat.stream.event.export.KafkaEventExporter;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MdpObjectInstanceSink extends CustomSinkBase {

    private final static Logger log = LoggerFactory.getLogger(MdpObjectInstanceSink.class);

    private String dbName;
    private String withTableName;
    private Connection conn = null;
    private EventExporter exporter;
    private String streamEvent = null;
    private String eventTag;
    private RdsConnectionParam param;
    protected String pkField;
    private String streamType;
    private int retryIntervalMs = 200;
    private int maxRetryTime = 3;
    private String limitFields;
    private String distributeKey;
    
    @Override
    public void open(int i, int i1) throws IOException {
        log.info(this.getName() + " open({},{})", i, i1);
        boolean initSuccess = false;
        String lastErrorMsg = null;
        int retris = 0;
        while (retris < 10) {
	        try {
	            dbName = userParamsMap.get("dbname");
	            withTableName = userParamsMap.get("tablename");
	            pkField = userParamsMap.get("object_pk");
	            limitFields = userParamsMap.get("limit_fields");
	            distributeKey = userParamsMap.get("distribute_key");
	            
	            synchronized (MdpObjectInstanceSink.class) {
	            	String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
	                log.info("dbMetaStr={}", dbMetaStr);
	                if (StringUtils.isBlank(dbMetaStr)) {
	                    log.error("{} open({},{}) failed to get dbMeta", this.getName(), i, i1);
	                	throw new RuntimeException(this.getName() + " open(" + i + "," + i1 + ") failed to get dbMetar");
	                }
	                JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
	                param = new RdsConnectionParam();
	                param.setUrl(dbMetaJson.getString("jdbcUrl"))
	        	        .setUserName(dbMetaJson.getString("username"))
	        	        .setPassword(dbMetaJson.getString("password"));
	            	conn = QanatDatasourceHandler.connectToTable(param);
	                String dsInfoStr = QanatDataSourceUtils.getDsInfo("obj_" + userParamsMap.get("object_unique_code"), Thread.currentThread().getContextClassLoader());
	                log.info("dsInfoStr={}", dsInfoStr);
	                if (StringUtils.isBlank(dsInfoStr)) {
	                    log.error("{} open({},{}) failed to get object dsInfo", this.getName(), i, i1);
	                	throw new RuntimeException(this.getName() + " open(" + i + "," + i1 + ") failed to get object dsInfo");
	                }
	                JSONObject dsInfoJson = JSON.parseObject(dsInfoStr);
	                pkField = StringUtils.isNotBlank(userParamsMap.get("object_pk")) ? userParamsMap.get("object_pk") : dsInfoJson.getString("pk_fields");
	            }
	            streamEvent = userParamsMap.get("streamevent");
	            streamEvent = StringUtils.isBlank(streamEvent) ? "enable" : streamEvent;
	            if ("enable".equalsIgnoreCase(streamEvent)) {
	            	streamType = userParamsMap.get("streamtype");
	                String eventTopic = userParamsMap.get("eventtopic");
	            	if ("kafka".equals(streamType)) {
	                    String eventServer = userParamsMap.get("eventserver");
	            		if (BlinkStringUtil.isNotEmpty(eventServer)) {
	                        String dbMetaStr = QanatDataSourceUtils.getDbMeta(eventServer, Thread.currentThread().getContextClassLoader());
	                        JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
	                        if (dbMetaJson != null && BlinkStringUtil.isNotEmpty(dbMetaJson.getString("bootstrap.servers"))) {
	                        	eventServer = dbMetaJson.getString("bootstrap.servers");
	                        }
	            		}
	    	            exporter = new KafkaEventExporter(eventTopic, eventServer);
	    	            exporter.init();
	    	            log.info("KafkaEventExporter {}:{} inited", eventTopic, eventServer);
	            	}
	            }
	            initSuccess = true;
	            break;
	        } catch (Exception e) {
	            retris++;
	            lastErrorMsg = e.getMessage();
	            log.error("sink open failed {} times, error:{}", retris, e.getMessage(), e);
	            try {
					Thread.sleep(100);
				} catch (InterruptedException e1) {}
	        }
        }
        if (!initSuccess) {
            log.error("{} open({},{}) failed:lastErrorMsg, then try to failover", this.getName(), i, i1, lastErrorMsg);
        	throw new RuntimeException(this.getName() + " open(" + i + "," + i1 + ") failed:" + lastErrorMsg + ", then try to failover");
        }
    }

    @Override
    public void writeAddRecord(Row row) throws IOException {
        log.info("writeAddRecord start");
        Statement statement = null;
        try {
            Map<String, Object> inputData = getInputData(row, rowTypeInfo);
            log.info("inputDat={}", JSON.toJSONString(inputData));
            String msg = String.valueOf(inputData.get("__msg__"));
            String traceId = String.valueOf(inputData.get("__trace_id__"));
            JSONObject event = JSON.parseObject(msg);
            JSONObject objectInst = event.getJSONObject("extParam").getJSONObject("objectInstanceVO");
            String metaRef = userParamsMap.get("object_unique_code");
            String objectUniqueCode = objectInst.getString("objectUniqueCode");
            if (!metaRef.equalsIgnoreCase(objectUniqueCode)) {
                log.info("objectUniqueCode not eq metaRef", metaRef, objectUniqueCode);
                return;
            }
            String dstDbName = getDstDbName(objectInst.getString("useDataSourceName"));
            if (dbName.equalsIgnoreCase(dstDbName)) {
            	log.info("{} object:{} pk:{} has been writen to db:{} by mdp so ignore", traceId, objectUniqueCode, objectInst.getString("objectBizId"), dstDbName);
            	if ("enable".equalsIgnoreCase(streamEvent)) {
                    //export event
                    StreamEvent streamEvent = JSON.parseObject(msg, StreamEvent.class);
                    streamEvent.setTs(System.currentTimeMillis());
                    String tag = null;
                    if (StringUtils.isBlank(eventTag)) {
                        tag = metaRef;
                    } else {
                        tag = eventTag;
                    }
                    streamEvent.setTraceId(traceId);
                    streamEvent.setDbName(param.getUrl().split("/")[3].split("\\?")[0]);
                    streamEvent.setTableName(withTableName);
                    String pkField = userParamsMap.get("object_pk");
                    streamEvent.setPkField(Arrays.asList(pkField));
                    Map<String, StreamEventField> fieldValues = new HashMap<>();
                    streamEvent.setFieldValues(fieldValues);

                	
                    StreamEventField field = new StreamEventField();
                    fieldValues.put(pkField, field);
                    field.setFieldName(pkField);
                    field.setFieldType(5);
                    if ("DELETE".equalsIgnoreCase(event.getString("operateType"))) {
                    	field.setOldValue(objectInst.getString("objectBizId"));
                    } else {
                    	field.setNewValue(objectInst.getString("objectBizId"));
                    }
                    for (int i = 0; i < objectInst.getJSONArray("fieldVOList").size(); i++) {
                    	JSONObject fieldJson = objectInst.getJSONArray("fieldVOList").getJSONObject(i);
    	                field = new StreamEventField();
    	                fieldValues.put(fieldJson.getString("fieldCode"), field);
    	                field.setFieldName(fieldJson.getString("fieldCode"));
    	                field.setFieldType(5);
    	                if ("DELETE".equalsIgnoreCase(event.getString("operateType"))) {
    	                	field.setOldValue(fieldJson.getString("fieldValue"));
    	                } else {
    	                	field.setNewValue(fieldJson.getString("fieldValue"));
    	                }
                    }
                    if ("CREATE".equalsIgnoreCase(event.getString("operateType"))) {
                    	streamEvent.setEventType(1);
                    } else if ("UPDATE".equalsIgnoreCase(event.getString("operateType"))) {
                    	streamEvent.setEventType(2);
                    } else if ("DELETE".equalsIgnoreCase(event.getString("operateType"))) {
                    	streamEvent.setEventType(3);
                    }
                    exporter.export(tag, streamEvent);
                }
            	return;
            }
            String sql = buildAdb3SqlFromDbEvent(withTableName, pkField, objectInst.getString("objectBizId"), event.getString("operateType"), objectInst.getJSONArray("fieldVOList"), limitFields, distributeKey);
            log.info("{} dbName={} sql={}", traceId, dbName, sql);
            if (StringUtils.isBlank(sql)) {
                log.info("{} dbName={} nothing to do", traceId, dbName);
            	return;
            }
            long startTs = System.currentTimeMillis();
    		int retryTime = 0;
    		while (retryTime < maxRetryTime) {
    			try {
    	            if (conn == null || conn.isClosed()) {
		                long startTsConn = System.currentTimeMillis();
    	            	conn = QanatDatasourceHandler.connectToTable(param);
			            log.info("{} retry:{} reconnect to {} cost={}", traceId, retryTime, dbName, System.currentTimeMillis()-startTsConn);
    	            }
		            statement = conn.createStatement();
		            int execCnt = statement.executeUpdate(sql);
		            log.info("{} after db={} execCnt={}, cost={}", traceId, dbName, execCnt, System.currentTimeMillis() - startTs);
		            
		            if ("enable".equalsIgnoreCase(streamEvent)) {
		                //export event
		                StreamEvent streamEvent = JSON.parseObject(msg, StreamEvent.class);
		                streamEvent.setTs(System.currentTimeMillis());
		                String tag = null;
		                if (StringUtils.isBlank(eventTag)) {
		                    tag = metaRef;
		                } else {
		                    tag = eventTag;
		                }
		                streamEvent.setTraceId(traceId);
		                streamEvent.setDbName(param.getUrl().split("/")[3].split("\\?")[0]);
		                streamEvent.setTableName(withTableName);
		                String pkField = userParamsMap.get("object_pk");
		                streamEvent.setPkField(Arrays.asList(pkField));
		                Map<String, StreamEventField> fieldValues = new HashMap<>();
		                streamEvent.setFieldValues(fieldValues);
		
		            	
		                StreamEventField field = new StreamEventField();
		                fieldValues.put(pkField, field);
		                field.setFieldName(pkField);
		                field.setFieldType(5);
		                if ("DELETE".equalsIgnoreCase(event.getString("operateType"))) {
		                	field.setOldValue(objectInst.getString("objectBizId"));
		                } else {
		                	field.setNewValue(objectInst.getString("objectBizId"));
		                }
		                for (int i = 0; i < objectInst.getJSONArray("fieldVOList").size(); i++) {
		                	JSONObject fieldJson = objectInst.getJSONArray("fieldVOList").getJSONObject(i);
			                field = new StreamEventField();
			                fieldValues.put(fieldJson.getString("fieldCode"), field);
			                field.setFieldName(fieldJson.getString("fieldCode"));
			                field.setFieldType(5);
			                if ("DELETE".equalsIgnoreCase(event.getString("operateType"))) {
			                	field.setOldValue(fieldJson.getString("fieldValue"));
			                } else {
			                	field.setNewValue(fieldJson.getString("fieldValue"));
			                }
		                }
		                if ("CREATE".equalsIgnoreCase(event.getString("operateType"))) {
		                	streamEvent.setEventType(1);
		                } else if ("UPDATE".equalsIgnoreCase(event.getString("operateType"))) {
		                	streamEvent.setEventType(2);
		                } else if ("DELETE".equalsIgnoreCase(event.getString("operateType"))) {
		                	streamEvent.setEventType(3);
		                }
		                exporter.export(tag, streamEvent);
		            }
	                log.info("{} dbName:{} writeAddRecord finish", traceId, dbName);
		            break;
    			} catch (SQLException e) {
    				log.error("{} dbName:{} sqlexception with retry:{} error:{}", traceId, dbName, retryTime, e.getMessage(), e);
					retryTime++;
					if (retryTime == maxRetryTime) {
						throw new RuntimeException(e);
					}
    				try {
						Thread.sleep(retryIntervalMs * retryTime);
    				} catch (Exception e1) {
    					//ignore
    				}
    			} finally {
    	            if (statement != null) {
    	                try {
    	                    statement.close();
    	                } catch (SQLException e) {
    	                	log.error("statement close failed", e);
    	                }
    	            }
    	            if (conn != null) {
    	                try {
    	                	conn.close();
    	                } catch (SQLException e) {
    	                	log.error("connection close failed", e);
    	                }
    	            }
    				
    			}
    		}
        } catch(Exception e) {
            log.error("writeAddRecord failed", e);
        }
    }
    
    private String getDstDbName(String useDataSourceName) {
    	if ("ADB1".equalsIgnoreCase(useDataSourceName)) {
    		return "devata_rtdw";
    	} else if ("ADB2".equalsIgnoreCase(useDataSourceName)) {
    		return "devata_rtdw_2";
    	} else if ("ADB3".equalsIgnoreCase(useDataSourceName)) {
    		return "devata_rtdw_3";
    	}
		return null;
	}

	private String buildAdb3SqlFromDbEvent(String tableName, String pkField, String pkValue, String operateType, JSONArray fieldVOList, String limitFields, String distributeKey) {
        List<String> fieldList = new ArrayList<>();
        List<String> fieldValueList = new ArrayList<>();
        List<String> updateKVList = new ArrayList<>();
        fieldList.add("`" + pkField + "`");
        fieldValueList.add("'" + pkValue + "'");
        
        List<String> limitFieldList = new ArrayList<>();
        if (StringUtils.isNotBlank(limitFields)) {
        	limitFieldList = Arrays.asList(limitFields.split(","));
        }
        
        for (int i = 0; i < fieldVOList.size(); i++) {
        	JSONObject field = fieldVOList.getJSONObject(i);
        	if (CollectionUtils.isNotEmpty(limitFieldList) && !limitFieldList.contains(field.getString("fieldCode"))) {
        		continue;
        	}
            fieldList.add("`" + field.getString("fieldCode") + "`");
            if (field.getString("fieldValue") != null) {
            	fieldValueList.add("'" + field.getString("fieldValue") + "'");
            	if (StringUtils.isBlank(distributeKey) || !distributeKey.equalsIgnoreCase(field.getString("fieldCode"))) {
            		updateKVList.add("`" + field.getString("fieldCode") + "`='" + field.getString("fieldValue") + "'");
            	}
            } else {
            	fieldValueList.add("NULL");
            	if (StringUtils.isBlank(distributeKey) || !distributeKey.equalsIgnoreCase(field.getString("fieldCode"))) {
            		updateKVList.add("`" + field.getString("fieldCode") + "`=NULL");
            	}
            }
        }
        
        if (CollectionUtils.isEmpty(updateKVList)) {
        	return null;
        }
        
        if ("CREATE".equalsIgnoreCase(operateType) || "UPDATE".equalsIgnoreCase(operateType)) {
            return String.format("INSERT INTO %s(%s) VALUES(%s) ON DUPLICATE KEY UPDATE %s", tableName, StringUtils.join(fieldList, ","), StringUtils.join(fieldValueList, ","), StringUtils.join(updateKVList, ","));
        } else if ("DELETE".equalsIgnoreCase(operateType)) {
            return  String.format("DELETE FROM %s WHERE %s='%s'", tableName, pkField, pkValue);
        }
        return null;
    }

    @Override
    public void writeDeleteRecord(Row row) throws IOException {
        
    }
    
    private Map<String, Object> getInputData(Row row, RowTypeInfo rowTypeInfo) {
        Map<String, Object> inputData = new HashMap<>();
        for (String fieldName : rowTypeInfo.getFieldNames()) {
            Object value = row.getField(rowTypeInfo.getFieldIndex(fieldName));
            if (value instanceof java.sql.Timestamp) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String str = df.format((java.sql.Timestamp)value);
                value = str;
            }
            if (value instanceof java.math.BigDecimal) {
                value = ((java.math.BigDecimal)value).doubleValue();
            }
            inputData.put(fieldName, value);
        }
        return inputData;
    }

    @Override
    public void sync() throws IOException {
    }

    @Override
    public void close() throws IOException {
    	QanatDatasourceHandler.closeDataSource(param);
        if (exporter != null) {
            exporter.close();
        }
    }

    @Override
    public String getName() {
        return "MdpObjectInstanceSink";
    }
}