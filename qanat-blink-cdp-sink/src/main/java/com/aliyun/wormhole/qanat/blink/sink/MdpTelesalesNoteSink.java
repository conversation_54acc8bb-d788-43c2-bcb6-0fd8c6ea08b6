package com.aliyun.wormhole.qanat.blink.sink;

import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.blink.streaming.connector.custom.api.CustomSinkBase;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.aliyun.wormhole.qanat.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.stream.event.StreamEvent;
import com.aliyun.wormhole.qanat.stream.event.StreamEventField;
import com.aliyun.wormhole.qanat.stream.event.export.EventExporter;
import com.aliyun.wormhole.qanat.stream.event.export.KafkaEventExporter;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

public class MdpTelesalesNoteSink extends CustomSinkBase {

    private final static Logger log = LoggerFactory.getLogger(MdpTelesalesNoteSink.class);

    private String dbName;
    private String withTableName;
    private Connection conn = null;
    private EventExporter exporter;
    private String streamEvent = null;
    private String eventTag;
    private RdsConnectionParam param;
    protected String pkField;
    private String streamType;
    
    @Override
    public void open(int i, int i1) throws IOException {
        log.info(this.getName() + " open({},{})", i, i1);
        try {
            dbName = userParamsMap.get("dbname");
            withTableName = userParamsMap.get("tablename");
            
            synchronized (MdpTelesalesNoteSink.class) {
            	String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
                log.info("dbMetaStr={}", dbMetaStr);
                if (StringUtils.isBlank(dbMetaStr)) {
                	return;
                }
                JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
                param = new RdsConnectionParam();
                param.setUrl(dbMetaJson.getString("jdbcUrl"))
        	        .setUserName(dbMetaJson.getString("username"))
        	        .setPassword(dbMetaJson.getString("password"));
            	conn = QanatDatasourceHandler.connectToTable(param);
                String dsInfoStr = QanatDataSourceUtils.getDsInfo("obj_" + userParamsMap.get("object_unique_code"), Thread.currentThread().getContextClassLoader());
                log.info("dsInfoStr={}", dsInfoStr);
                if (StringUtils.isBlank(dsInfoStr)) {
                	return;
                }
                JSONObject dsInfoJson = JSON.parseObject(dsInfoStr);
                pkField = StringUtils.isNotBlank(userParamsMap.get("object_pk")) ? userParamsMap.get("object_pk") : dsInfoJson.getString("pk_fields");
            }
            streamEvent = userParamsMap.get("streamevent");
            streamEvent = StringUtils.isBlank(streamEvent) ? "enable" : streamEvent;
            if ("enable".equalsIgnoreCase(streamEvent)) {
            	streamType = userParamsMap.get("streamtype");
                String eventTopic = userParamsMap.get("eventtopic");
            	if ("kafka".equals(streamType)) {
                    String eventServer = userParamsMap.get("eventserver");
    	            exporter = new KafkaEventExporter(eventTopic, eventServer);
    	            exporter.init();
    	            log.info("KafkaEventExporter {}:{} inited", eventTopic, eventServer);
            	}
            }
        } catch (Exception e) {
            log.error("sql exec failed:{}", e.getMessage(), e);
        }
    }

    @Override
    public void writeAddRecord(Row row) throws IOException {
        log.info("writeAddRecord start");
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            Map<String, Object> inputData = getInputData(row, rowTypeInfo);
            log.info("inputDat={}", JSON.toJSONString(inputData));
            String msg = String.valueOf(inputData.get("__msg__"));
            String traceId = String.valueOf(inputData.get("__trace_id__"));
            
            if (conn == null || conn.isClosed()) {
            	conn = QanatDatasourceHandler.connectToTable(param);
            }
            JSONObject event = JSON.parseObject(msg);
            JSONObject objectInst = event.getJSONObject("extParam").getJSONObject("objectInstanceVO");
            String metaRef = userParamsMap.get("object_unique_code");
            String objectUniqueCode = objectInst.getString("objectUniqueCode");
            if (!metaRef.equalsIgnoreCase(objectUniqueCode)) {
                log.info("objectUniqueCode not eq metaRef", metaRef, objectUniqueCode);
                return;
            }
            String sql = buildAdb3SqlFromDbEvent(withTableName, pkField, objectInst.getString("objectBizId"), event.getString("operateType"), objectInst.getJSONArray("fieldVOList"));
            log.info("sql={}", sql);
            if (sql == null) {
            	return;
            }
            statement = conn.createStatement();
            int execCnt = statement.executeUpdate(sql);
            log.info("after sql={}, execCnt={}", sql, execCnt);
            
            if ("enable".equalsIgnoreCase(streamEvent)) {
                //export event
                StreamEvent streamEvent = JSON.parseObject(msg, StreamEvent.class);
                streamEvent.setTs(System.currentTimeMillis());
                String tag = null;
                if (StringUtils.isBlank(eventTag)) {
                    tag = metaRef;
                } else {
                    tag = eventTag;
                }
                streamEvent.setTraceId(traceId);
                streamEvent.setDbName(param.getUrl().split("/")[3].split("\\?")[0]);
                streamEvent.setTableName(withTableName);
                String pkField = userParamsMap.get("object_pk");
                streamEvent.setPkField(Arrays.asList(pkField));
                Map<String, StreamEventField> fieldValues = new HashMap<>();
                streamEvent.setFieldValues(fieldValues);

            	
                StreamEventField field = new StreamEventField();
                fieldValues.put(pkField, field);
                field.setFieldName(pkField);
                field.setFieldType(5);
                if ("DELETE".equalsIgnoreCase(event.getString("operateType"))) {
                	field.setOldValue(objectInst.getString("objectBizId"));
                } else {
                	field.setNewValue(objectInst.getString("objectBizId"));
                }
                for (int i = 0; i < objectInst.getJSONArray("fieldVOList").size(); i++) {
                	JSONObject fieldJson = objectInst.getJSONArray("fieldVOList").getJSONObject(i);
	                field = new StreamEventField();
	                fieldValues.put(fieldJson.getString("fieldCode"), field);
	                field.setFieldName(fieldJson.getString("fieldCode"));
	                field.setFieldType(5);
	                if ("DELETE".equalsIgnoreCase(event.getString("operateType"))) {
	                	field.setOldValue(fieldJson.getString("fieldValue"));
	                } else {
	                	field.setNewValue(fieldJson.getString("fieldValue"));
	                }
                }
                if ("CREATE".equalsIgnoreCase(event.getString("operateType"))) {
                	streamEvent.setEventType(1);
                } else if ("UPDATE".equalsIgnoreCase(event.getString("operateType"))) {
                	streamEvent.setEventType(2);
                } else if ("DELETE".equalsIgnoreCase(event.getString("operateType"))) {
                	streamEvent.setEventType(3);
                }
                exporter.export(tag, streamEvent);
            }
            log.info("writeAddRecord finish");
        } catch(Exception e) {
            log.error("writeAddRecord failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                    resultSet = null;
                    log.error("resultSet close failed", e);
                }
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    statement = null;
                    log.error("statement close failed", e);
                }
            }
        }
    }
    
    private String buildAdb3SqlFromDbEvent(String tableName, String pkField, String pkValue, String operateType, JSONArray fieldVOList) {
        List<String> fieldList = new ArrayList<>();
        List<String> fieldValueList = new ArrayList<>();
        List<String> fieldUpdValueList = new ArrayList<>();
        for (int i = 0; i < fieldVOList.size(); i++) {
        	JSONObject field = fieldVOList.getJSONObject(i);
        	if ("connect_xof3x1".equalsIgnoreCase(field.getString("fieldCode"))) {
                if (field.getString("fieldValue") != null) {
                    fieldList.add("cid_connection_result");
                	fieldValueList.add("'" + field.getString("fieldValue") + "'");
                	fieldUpdValueList.add("cid_connection_result='" + field.getString("fieldValue") + "'");
                }
        	} else if ("customerSize_xof3x1".equalsIgnoreCase(field.getString("fieldCode"))) {
                if (field.getString("fieldValue") != null) {
                    fieldList.add("cid_prediction_gaap");
                	fieldValueList.add("'" + field.getString("fieldValue") + "'");
                	fieldUpdValueList.add("cid_prediction_gaap='" + field.getString("fieldValue") + "'");
                }
        	} else if ("intention_xof3x1".equalsIgnoreCase(field.getString("fieldCode"))) {
                if (field.getString("fieldValue") != null) {
                    fieldList.add("cid_desire_level");
                	fieldValueList.add("'" + field.getString("fieldValue") + "'");
                	fieldUpdValueList.add("cid_desire_level='" + field.getString("fieldValue") + "'");
                }
        	} else if ("cidMdp2_xof3x1".equalsIgnoreCase(field.getString("fieldCode"))) {
                if (field.getString("fieldValue") != null) {
                    fieldList.add("cid");
                	fieldValueList.add("'" + field.getString("fieldValue") + "'");
                }
        	}
        }
        if (CollectionUtils.isEmpty(fieldList) || fieldList.size() == 1) {
        	return null;
        }
        return String.format("INSERT INTO %s(%s) VALUES(%s) ON DUPLICATE KEY UPDATE %s", tableName, StringUtils.join(fieldList, ","), StringUtils.join(fieldValueList, ","), StringUtils.join(fieldUpdValueList, ","));
    }

    @Override
    public void writeDeleteRecord(Row row) throws IOException {
        
    }
    
    private Map<String, Object> getInputData(Row row, RowTypeInfo rowTypeInfo) {
        Map<String, Object> inputData = new HashMap<>();
        for (String fieldName : rowTypeInfo.getFieldNames()) {
            Object value = row.getField(rowTypeInfo.getFieldIndex(fieldName));
            if (value instanceof java.sql.Timestamp) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String str = df.format((java.sql.Timestamp)value);
                value = str;
            }
            if (value instanceof java.math.BigDecimal) {
                value = ((java.math.BigDecimal)value).doubleValue();
            }
            inputData.put(fieldName, value);
        }
        return inputData;
    }

    @Override
    public void sync() throws IOException {
    }

    @Override
    public void close() throws IOException {
    	QanatDatasourceHandler.closeDataSource(param);
        if (exporter != null) {
            exporter.close();
        }
    }

    @Override
    public String getName() {
        return "MdpObjectInstanceSink";
    }
}