package com.aliyun.wormhole.qanat.blink.sink;

import org.yaml.snakeyaml.Yaml;

import com.aliyun.wormhole.qanat.blink.sink.ViewModel.DataObject;

public class OdsMdpYamlDataModelSink extends AbstractOdsMdpSink {

    private DataObject object;

    @Override
    public String getName() {
        return "OdsMdpYamlDataModelSink";
    }

	@Override
	protected String getDomainCode() {
		return null;
	}

	@Override
	protected String getObjectUniqueCode() {
		return object.getRef();
	}

	@Override
	protected void doOpen() {
        String yaml = userParamsMap.get("datamodel");
        object =  new Yaml().loadAs(yaml, ViewModel.DataObject.class);
	}

	@Override
	protected String getTableName() {
		return object.getCode();
	}

	@Override
	protected String getPkField() {
		return userParamsMap.get("object_pk");
	}
}
