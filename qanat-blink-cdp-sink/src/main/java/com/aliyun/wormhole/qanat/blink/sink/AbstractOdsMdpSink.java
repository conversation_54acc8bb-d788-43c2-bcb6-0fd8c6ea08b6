package com.aliyun.wormhole.qanat.blink.sink;

import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.blink.streaming.connector.custom.api.CustomSinkBase;
import com.alibaba.blink.streaming.connectors.common.util.BlinkStringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.aliyun.wormhole.qanat.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.datasource.RdsConnectionParam;
import com.aliyun.wormhole.qanat.drc.event.DbEvent;
import com.aliyun.wormhole.qanat.drc.event.DbEventInfo;
import com.aliyun.wormhole.qanat.stream.event.StreamEvent;
import com.aliyun.wormhole.qanat.stream.event.StreamEventField;
import com.aliyun.wormhole.qanat.stream.event.export.EventExporter;
import com.aliyun.wormhole.qanat.stream.event.export.KafkaEventExporter;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class AbstractOdsMdpSink extends CustomSinkBase {

    private final static Logger log = LoggerFactory.getLogger(AbstractOdsMdpSink.class);

    private String dbName;
    private String withTableName;
    private Connection conn = null;
    private EventExporter exporter;
    private String streamEvent = null;
    private String eventTag;
    private RdsConnectionParam param;
    private List<String> acceptDomainCodes = null;
    private List<String> exceptDomainCodes = null;
    protected String pkField;
    private String streamType;
    
    @Override
    public void open(int i, int i1) throws IOException {
        log.info(this.getName() + " open({},{})", i, i1);
        try {
            dbName = userParamsMap.get("dbname");
            withTableName = userParamsMap.get("tablename");
            String acceptDomainCode = userParamsMap.get("domain_code");
            if (StringUtils.isNotBlank(acceptDomainCode)) {
            	acceptDomainCodes = Arrays.asList(acceptDomainCode.split(","));
            }
            String exceptDomainCode = userParamsMap.get("except_domain_code");
            if (StringUtils.isNotBlank(exceptDomainCode)) {
            	exceptDomainCodes = Arrays.asList(exceptDomainCode.split(","));
            }
            
            synchronized (AbstractOdsMdpSink.class) {
            	String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
                log.info("dbMetaStr={}", dbMetaStr);
                if (StringUtils.isBlank(dbMetaStr)) {
                	return;
                }
                JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
                param = new RdsConnectionParam();
                param.setUrl(dbMetaJson.getString("jdbcUrl"))
        	        .setUserName(dbMetaJson.getString("username"))
        	        .setPassword(dbMetaJson.getString("password"));
            	conn = QanatDatasourceHandler.connectToTable(param);
                String dsInfoStr = QanatDataSourceUtils.getDsInfo("obj_" + getObjectUniqueCode(), Thread.currentThread().getContextClassLoader());
                log.info("dsInfoStr={}", dsInfoStr);
                if (StringUtils.isBlank(dsInfoStr)) {
                	return;
                }
                JSONObject dsInfoJson = JSON.parseObject(dsInfoStr);
                pkField = StringUtils.isNotBlank(this.getPkField()) ? this.getPkField() : dsInfoJson.getString("pk_fields");
            }
            streamEvent = userParamsMap.get("streamevent");
            streamEvent = StringUtils.isBlank(streamEvent) ? "enable" : streamEvent;
            if ("enable".equalsIgnoreCase(streamEvent)) {
            	streamType = userParamsMap.get("streamtype");
                String eventTopic = userParamsMap.get("eventtopic");
            	if ("kafka".equals(streamType)) {
                    String eventServer = userParamsMap.get("eventserver");
            		if (BlinkStringUtil.isNotEmpty(eventServer)) {
                        String dbMetaStr = QanatDataSourceUtils.getDbMeta(eventServer, Thread.currentThread().getContextClassLoader());
                        JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
                        if (dbMetaJson != null && BlinkStringUtil.isNotEmpty(dbMetaJson.getString("bootstrap.servers"))) {
                        	eventServer = dbMetaJson.getString("bootstrap.servers");
                        }
            		}
    	            exporter = new KafkaEventExporter(eventTopic, eventServer);
    	            exporter.init();
    	            log.info("KafkaEventExporter {}:{} inited", eventTopic, eventServer);
            	}
            }
            doOpen();
        } catch (Exception e) {
            log.error("sql exec failed:{}", e.getMessage(), e);
        }
    }

	protected abstract String getDomainCode();

	protected abstract String getObjectUniqueCode();

	protected abstract void doOpen();

	protected abstract String getTableName();

	protected abstract String getPkField();

    @Override
    public void writeAddRecord(Row row) throws IOException {
        log.info("writeAddRecord start");
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            Map<String, Object> inputData = getInputData(row, rowTypeInfo);
            log.info("inputDat={}", JSON.toJSONString(inputData));
            String msg = String.valueOf(inputData.get("__msg__"));
            String traceId = String.valueOf(inputData.get("__trace_id__"));
            
            if (conn == null || conn.isClosed()) {
            	conn = QanatDatasourceHandler.connectToTable(param);
            }
            DbEventInfo event = JSON.parseObject(msg, DbEventInfo.class);
            String tableName = StringUtils.isNotBlank(withTableName) ? withTableName : getTableName();
            String metaRef = getObjectUniqueCode();
            String objectUniqueCode = getValue(event, "object_unique_code");
            if (!metaRef.equalsIgnoreCase(objectUniqueCode)) {
                log.info("objectUniqueCode not eq metaRef", metaRef, objectUniqueCode);
                return;
            }
            String domainCode = getDomainCode();
            String operateDomainCode = getValue(event, "operate_domain_code");
            if (domainCode != null && !domainCode.equalsIgnoreCase(operateDomainCode)) {
                log.info("operateDomainCode not eq domain", domainCode, operateDomainCode);
                return;
            }
            if (CollectionUtils.isNotEmpty(exceptDomainCodes) && exceptDomainCodes.contains(operateDomainCode)) {
                log.info("operateDomainCode[{}] not match", operateDomainCode);
                return;
            }
            if (CollectionUtils.isNotEmpty(acceptDomainCodes) && !acceptDomainCodes.contains(operateDomainCode)) {
                log.info("operateDomainCode[{}] not match", operateDomainCode);
                return;
            }
            String sql = buildAdb3SqlFromDbEvent(event, metaRef, tableName);
            log.info("sql={}", sql);
            statement = conn.createStatement();
            int execCnt = statement.executeUpdate(sql);
            log.info("after sql={}, execCnt={}", sql, execCnt);
            
            if ("enable".equalsIgnoreCase(streamEvent)) {
                //export event
                StreamEvent streamEvent = JSON.parseObject(msg, StreamEvent.class);
                streamEvent.setTs(System.currentTimeMillis());
                String tag = null;
                if (StringUtils.isBlank(eventTag)) {
                    tag = metaRef;
                } else {
                    tag = eventTag;
                }
                streamEvent.setTraceId(traceId);
                streamEvent.setDbName(param.getUrl().split("/")[3].split("\\?")[0]);
                streamEvent.setTableName(tableName);
                String pkField = getPkField();
                streamEvent.setPkField(Arrays.asList(pkField));
                Map<String, StreamEventField> fieldValues = new HashMap<>();
                streamEvent.setFieldValues(fieldValues);
                
                StreamEventField field = new StreamEventField();
                fieldValues.put(getValue(event, "tag_code"), field);
                field.setFieldName(getValue(event, "tag_code"));
                field.setFieldType(transformMdpTypeToAdbType(getValue(event, "tag_data_type")));
            	field.setNewValue(event.getFieldValues().get("tag_value").getNewValue());
            	field.setOldValue(event.getFieldValues().get("tag_value").getOldValue());
            	
                field = new StreamEventField();
                fieldValues.put(pkField, field);
                field.setFieldName(pkField);
                field.setFieldType(transformMdpTypeToAdbType(getValue(event, "tag_data_type")));
            	field.setNewValue(event.getFieldValues().get("object_biz_id").getNewValue());
            	field.setOldValue(event.getFieldValues().get("object_biz_id").getOldValue());
            	
                int eventType = event.getEventType();
                if (event.getEventType() == 3) {
                	eventType = 2;
                }
                streamEvent.setEventType(eventType);
                exporter.export(tag, streamEvent);
            }
            log.info("writeAddRecord finish");
        } catch(Exception e) {
            log.error("writeAddRecord failed", e);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                    resultSet = null;
                    log.error("resultSet close failed", e);
                }
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    statement = null;
                    log.error("statement close failed", e);
                }
            }
        }
    }
    
    private String buildAdb3SqlFromDbEvent(DbEventInfo event, String metaRef, String tableName) {
        String strSQL = null;
        
        String pkValue = "'" + getValue(event, "object_biz_id") + "'";
        String fieldName = getValue(event, "tag_code");
        String fieldValue = "'" + getValue(event, "tag_value") + "'";
        
        String isDeleted = getValue(event, "is_deleted");

        if (DbEvent.INSERT == event.getEventType()
        		|| DbEvent.UPDATE == event.getEventType()) {
            List<String> fieldList = new ArrayList<>();
            List<String> fieldValueList = new ArrayList<>();
            
            fieldList.add(pkField);
            fieldValueList.add(pkValue);
            fieldList.add(fieldName);
            fieldValueList.add("0".equals(isDeleted) ? fieldValue : "NULL");

            strSQL = String.format("INSERT INTO %s(%s) VALUES(%s) ON DUPLICATE KEY UPDATE %s=%s", tableName, StringUtils.join(fieldList, ","), StringUtils.join(fieldValueList, ","), fieldName, "0".equals(isDeleted) ? fieldValue : "NULL");
        }
        if (DbEvent.DELETE == event.getEventType()) {
            List<String> fieldList = new ArrayList<>();
            List<String> fieldValueList = new ArrayList<>();

            fieldList.add(pkField);
            fieldValueList.add(pkValue);
            fieldList.add(fieldName);
            fieldValueList.add("NULL");
            
            strSQL = String.format("UPDATE INTO %s(%s) VALUES(%s)", tableName, StringUtils.join(fieldList, ","), StringUtils.join(fieldValueList, ","));
        }
        return strSQL;
    }

    private static String getValue(DbEventInfo event, String column) {
        String newValue = event.getFieldValues().get(column).getNewValue();
        String oldValue = event.getFieldValues().get(column).getOldValue();
        return StringUtils.isBlank(newValue) ? oldValue : newValue;
    }

    @Override
    public void writeDeleteRecord(Row row) throws IOException {
        
    }
    
    private Map<String, Object> getInputData(Row row, RowTypeInfo rowTypeInfo) {
        Map<String, Object> inputData = new HashMap<>();
        for (String fieldName : rowTypeInfo.getFieldNames()) {
            Object value = row.getField(rowTypeInfo.getFieldIndex(fieldName));
            if (value instanceof java.sql.Timestamp) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String str = df.format((java.sql.Timestamp)value);
                value = str;
            }
            if (value instanceof java.math.BigDecimal) {
                value = ((java.math.BigDecimal)value).doubleValue();
            }
            inputData.put(fieldName, value);
        }
        return inputData;
    }

    @Override
    public void sync() throws IOException {
    }

    @Override
    public void close() throws IOException {
    	QanatDatasourceHandler.closeDataSource(param);
        if (exporter != null) {
            exporter.close();
        }
    }

    @Override
    public String getName() {
        return "AbstractOdsMdpSink";
    }
    
    private int transformMdpTypeToAdbType(String type) {
        int dbType = 5;
        switch (type) {
            case "DATETIME":
                dbType = 4;
                break;
            case "ENUM":
                dbType = 5;
                break;
            case "BOOLEAN":
                dbType = 1;
                break;
            case "DECIMAL":
                dbType = 3;
                break;
            case "STRING":
                dbType = 5;
                break;
            case "ENUMS":
                dbType = 5;
                break;
            case "BIGINT":
                dbType = 2;
                break;
            default:
                dbType = 5;
                break;
        }
        return dbType;
    }
}
