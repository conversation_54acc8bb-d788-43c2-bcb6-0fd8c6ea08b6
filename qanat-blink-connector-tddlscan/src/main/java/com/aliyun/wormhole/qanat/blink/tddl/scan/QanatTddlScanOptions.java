package com.aliyun.wormhole.qanat.blink.tddl.scan;

import java.util.Arrays;
import java.util.List;

import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.configuration.ConfigOptions;

public class QanatTddlScanOptions {

    public static final ConfigOption<String> APPNAME = ConfigOptions.key("appName".toLowerCase()).noDefaultValue();
    public static final ConfigOption<String> TABLENAME = ConfigOptions.key("tableName".toLowerCase()).noDefaultValue();
    public static final ConfigOption<String> WHERE_CLAUSE = ConfigOptions.key("whereClause".toLowerCase()).noDefaultValue();
    public static final ConfigOption<Integer> BATCH_SIZE = ConfigOptions.key("batchSize".toLowerCase()).defaultValue(100);
    public static final ConfigOption<String> DBNAME = ConfigOptions.key("dbName".toLowerCase()).noDefaultValue();
    
    public static final String PARAMS_HELP_MSG = String.format(
        "required params:%s,%s,%s\n",
        APPNAME,
        TABLENAME,
        DBNAME);

    public static final List<String> SUPPORTED_KEYS = Arrays.asList(
        APPNAME.key(),
        TABLENAME.key(),
        WHERE_CLAUSE.key(),
        BATCH_SIZE.key());
}