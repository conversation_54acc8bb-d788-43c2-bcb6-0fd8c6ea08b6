/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.blink.tddl.scan.source;

import com.alibaba.blink.streaming.connectors.common.datatype.DataType;
import com.alibaba.blink.streaming.connectors.common.reader.Interruptible;
import com.alibaba.blink.streaming.connectors.common.reader.MonotonyIncreaseProgress;
import com.alibaba.blink.streaming.connectors.common.reader.RecordReader;
import com.aliyun.wormhole.qanat.blink.tddl.scan.io.TddlScanInputConfig;
import com.taobao.tddl.client.jdbc.TDataSource;

import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.core.io.InputSplit;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

public class TddlScanRecordReader implements RecordReader<Row, Long>, Interruptible {

    private static final Logger LOGGER = LoggerFactory.getLogger(TddlScanRecordReader.class);

    private TddlScanReaderConfig tddlScanConfig;
    private ResultSet resultSet;
    private Connection connection = null;
    private Statement statement = null;
    private long currentId = 0;
    private volatile boolean interrupted = false;
    private RichTableSchema richSchema;
    private Long startId = null;
    private Long endId = null;
    private int batchSize = 0;
    
    public TddlScanRecordReader(RichTableSchema richSchema) {
        this.richSchema = richSchema;
    }

    @Override
    public void open(InputSplit inputSplit, RuntimeContext context) throws IOException {
        tddlScanConfig = (TddlScanReaderConfig)inputSplit;
        batchSize = tddlScanConfig.getTddlScanInputConfig().getBatchSize();
        createTableHandle(tddlScanConfig.getTddlScanInputConfig());
    }

    @Override
    public Long getProgress() {
        return currentId;
    }

    @Override
    public MonotonyIncreaseProgress getMonotonyIncreaseProgress() {
        MonotonyIncreaseProgress monotonyIncreaseProgress = new MonotonyIncreaseProgress();
        monotonyIncreaseProgress.add("", currentId);
        return monotonyIncreaseProgress;
    }

    @Override
    public void seek(Long id) throws IOException {
        LOGGER.info("TddlSource: get page from startId[{}]", id);

        if (id + 1 > endId) {
            LOGGER.error("TddlSource: page startId[{}] isn't less than maxId[{}]", id, endId);
            throw new RuntimeException("page idx isn't less than maxPageCnt");
        } else {
            TddlScanInputConfig tddlConfig = tddlScanConfig.getTddlScanInputConfig();
            LOGGER.info("TddlSource: seek from startId[{}]", id);
            fetchData(tddlConfig, richSchema.getPrimaryKeys().get(0), id);
        }
    }

    @Override
    public boolean next() throws IOException, InterruptedException {
        if (interrupted) {
            LOGGER.info("TddlSource: received interrupt command, finish this consumer.");
            return false;
        }

        try {
            if (resultSet.next()) {
                return true;
            } else {
                return moveToNextPage();
            }
        } catch (SQLException e) {
            throw new RuntimeException("Failed to get next, err info: " + e.getMessage());
        }
    }

    @Override
    public Row getMessage() {
        if (resultSet == null) {
            LOGGER.warn("conversion error, resultSet object is null");
            return null;
        }
        int fieldLength = richSchema.getResultTypeInfo().getArity();
        Row row = new Row(fieldLength);
        try {
            DataType[] fieldTypes = new DataType[fieldLength];
            for (int i = 0; i < fieldLength; i++) {
                fieldTypes[i] = DataType.getType(richSchema.getResultTypeInfo().getTypeAt(i));
            }
            for (int i = 0; i < fieldLength; i++) {
                Object val = resultSetToObject(fieldTypes[i], this.resultSet, i + 1, "");
                row.setField(i, val);
            }
            LOGGER.info("resultSet={}, row={}", this.resultSet, row.getField(richSchema.getResultTypeInfo().getFieldIndex(richSchema.getPrimaryKeys().get(0))));
        } catch (Exception e) {
            LOGGER.warn("TddlSource: get error info: {}", e.getMessage());
        }
        return row;
    }
    
    private Object resultSetToObject(DataType type, ResultSet resultSet, int fieldCount, String timeZone) {
        try {
            switch (type) {
                case ByteArray:
                    return new String(resultSet.getBytes(fieldCount));
                case String:
                    return resultSet.getString(fieldCount);
                case Byte:
                    return String.valueOf(resultSet.getByte(fieldCount));
                case Short:
                    return resultSet.getShort(fieldCount);
                case Integer:
                    return resultSet.getInt(fieldCount);
                case Long:
                case BigInteger:
                	Long val = resultSet.getLong(fieldCount);
                	if (resultSet.wasNull()) {
                		val = null;
                	}
                    return val;
                case Float:
                    return resultSet.getFloat(fieldCount);
                case Double:
                    return resultSet.getDouble(fieldCount);
                case Boolean:
                    return Boolean.toString(resultSet.getBoolean(fieldCount));
                case Timestamp:
                    return resultSet.getTimestamp(fieldCount);
                case Time:
                    return resultSet.getTime(fieldCount);
                case Date:
                    return resultSet.getDate(fieldCount);
                case BigDecimal:
                    return resultSet.getBigDecimal(fieldCount);
                default:
                    return String.valueOf(resultSet.getString(fieldCount));
            }
        } catch (Exception e) {
            throw new RuntimeException(String.format("Parse recordSet failed, DataType=%s, fieldCount=%s, timeZone=%s",
                type.toString(), fieldCount, timeZone), e);
        }
    }

    @Override
    public void close() {
        LOGGER.info("TddlSource: begin to close.");
        closeTableHandle();
    }

    @Override
    public long getWatermark() {
        return System.currentTimeMillis();
    }

    @Override
    public long getDelay() {
        return 0;
    }

    @Override
    public boolean isHeartBeat() {
        return false;
    }

    @Override
    public void interrupt() {
        LOGGER.info("TddlSource: get interrupt info.");
        interrupted = true;
    }

    @Override
    public long getFetchedDelay() {
        return 0;
    }

    private boolean moveToNextPage() throws SQLException {
        while ((currentId+batchSize) <= endId) {
            TddlScanInputConfig tddlConfig = tddlScanConfig.getTddlScanInputConfig();
            LOGGER.info("TddlSource: currentId={}", currentId);

            currentId += batchSize;
            LOGGER.info("TddlSource: begin to scan from id[{}]", currentId);
            fetchData(tddlConfig, richSchema.getPrimaryKeys().get(0), currentId);

            if (resultSet.next()) {
                return true;
            }
        }

        LOGGER.info("TddlSource: finish to process tabale scan, currentId[{}] maxId[{}]", currentId, endId);
        return false;
    }

    private void closeTableHandle() {
        LOGGER.info("TddlSource: begin to close table");

        if (null != connection) {
            LOGGER.info("TddlSource: closing connection ...");
            try {
                connection.close();
            } catch (SQLException e) {
                throw new RuntimeException("Failed to close connection, error info: " + e.getMessage());
            }
            connection = null;
        }

        if (null != statement) {
            LOGGER.info("TddlSource: closing statement ...");
            try {
                statement.close();
            } catch (SQLException e) {
                throw new RuntimeException("Failed to close statement, error info: " + e.getMessage());
            }
            statement = null;
        }
    }

    private void createTableHandle(TddlScanInputConfig tddlConfig) {
        LOGGER.info("TddlSource: begin to create table");

        if (null != connection || null != statement) {
            throw new RuntimeException("pls close last connection && statement at first.");
        }

        try {
    		System.setProperty("tddl.version.check", "false");
        	TDataSource ds = new TDataSource();
            ds.setAppName(tddlConfig.getAppName());
            ds.setDynamicRule(true);
            ds.init();
            connection = ds.getConnection();
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);

        	Map<String, Object> tableMetaMap = getMaxId(tddlConfig);
        	startId = (Long)tableMetaMap.get("minId");
        	currentId = (Long)tableMetaMap.get("minId");
        	endId = (Long)tableMetaMap.get("maxId");
        	LOGGER.info("startId:{} endId:{} count:{}", startId, endId, tableMetaMap.get("count"));
            fetchData(tddlConfig, richSchema.getPrimaryKeys().get(0), currentId);
            
        } catch (SQLException e) {
            throw new RuntimeException("error info: " + e.getMessage());
        }

        LOGGER.info("TddlSource: create table successfully");
    }
    
    private void fetchData(TddlScanInputConfig tddlConfig, String pkField, long startId) {
        try {
            String inputSqlStr = genSQL(tddlConfig, pkField, startId);
            LOGGER.info("TddlSource: gen sql string: {}", inputSqlStr);
            resultSet = statement.executeQuery(inputSqlStr);
            LOGGER.info("start:{},resultSet1={}", startId, resultSet);
        } catch (SQLException e) {
            throw new RuntimeException("error info: " + e.getMessage());
        }
    }

    private Map<String, Object> getMaxId(TddlScanInputConfig tddlConfig) {
    	Map<String, Object> tableMetaMap = new HashMap<>();
        StringBuilder inputSql = new StringBuilder();
        inputSql.append("/*+TDDL({'type':'direct','vtab':'" + tddlConfig.getTableName() + "','dbid':'" + tddlConfig.getGroupKey() + "','realtabs':['" + tddlConfig.getRealTableName() +"']})*//*+TDDL_GROUP({groupIndex:1})*/")
        		.append("SELECT ")
				.append("MIN(" + richSchema.getPrimaryKeys().get(0) + ")")
				.append(",")
        		.append("MAX(" + richSchema.getPrimaryKeys().get(0) + ")")
				.append(",")
				.append("COUNT(*)")
		        .append(" FROM ")
		        .append(tddlConfig.getTableName());

        if (null != tddlConfig.getWhereClause() && !tddlConfig.getWhereClause().isEmpty()) {
            inputSql.append(" WHERE ").append(tddlConfig.getWhereClause());
        }
        try {
            ResultSet rs= statement.executeQuery(inputSql.toString());
            if(rs.next()) {
            	tableMetaMap.put("minId", rs.getLong(1));
            	tableMetaMap.put("maxId", rs.getLong(2));
            	tableMetaMap.put("count", rs.getLong(3));
            }
        } catch (SQLException e) {
            throw new RuntimeException("error info: " + e.getMessage());
        }
        return tableMetaMap;
    }

    private String genSQL(TddlScanInputConfig tddlConfig, String pkField, long startId) {
        StringBuilder inputSql = new StringBuilder();
        inputSql.append("/*+TDDL({'type':'direct','vtab':'" + tddlConfig.getTableName() + "','dbid':'" + tddlConfig.getGroupKey() + "','realtabs':['" + tddlConfig.getRealTableName() +"']})*//*+TDDL_GROUP({groupIndex:1})*/")
        		.append("SELECT ");
        for (int i = 0; i < tddlConfig.getFields().size(); ++i) {
            inputSql.append(tddlConfig.getFields().get(i));

            if (tddlConfig.getFields().size() != i + 1) {
                inputSql.append(", ");
            } else {
                inputSql.append(" ");
            }
        }
        inputSql.append(" FROM ").append(tddlConfig.getTableName());
        inputSql.append(" WHERE ").append(pkField).append(">=").append(startId).append(" AND ").append(pkField).append("<").append((startId+batchSize) > endId ? (endId+1) : (startId+batchSize));

        if (null != tddlConfig.getWhereClause() && !tddlConfig.getWhereClause().isEmpty()) {
            inputSql.append(" AND ").append(tddlConfig.getWhereClause());
        }
        LOGGER.info("sql={}", inputSql.toString());
        return inputSql.toString();
    }
}
