/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.blink.tddl.scan.source;

import com.alibaba.blink.streaming.connectors.common.source.SourceFunctionTableSource;
import com.aliyun.wormhole.qanat.blink.tddl.scan.io.TddlScanInputConfig;

import java.util.List;

import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.types.Row;

public class TddlScanTableSource extends SourceFunctionTableSource<Row> {

	private String tableName;
	private List<TddlScanInputConfig> inputList;
	private RichTableSchema richSchema;

	public TddlScanTableSource(RichTableSchema richSchema, String tableName, List<TddlScanInputConfig> inputList) {
		this.tableName = tableName;
		this.inputList = inputList;
		this.richSchema = richSchema;
	}

	@Override
	public SourceFunction getSourceFunction() {
		TddlScanSource source = new TddlScanSource(richSchema, tableName, inputList);
		source.setTableName(tableName);
		return source;
	}

	@Override
	public String explainSource() {
		return String.format("TddlScanTableSource-%s", tableName);
	}
}
