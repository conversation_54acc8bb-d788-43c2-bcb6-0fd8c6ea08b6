package com.aliyun.wormhole.qanat.blink.tddl.scan;

import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.api.TableSourceParser;
import org.apache.flink.table.factories.BatchTableSourceFactory;
import org.apache.flink.table.factories.StreamTableSourceFactory;
import org.apache.flink.table.factories.TableSourceParserFactory;
import org.apache.flink.table.sources.BatchTableSource;
import org.apache.flink.table.sources.StreamTableSource;
import org.apache.flink.table.types.InternalType;
import org.apache.flink.table.typeutils.TypeUtils;
import org.apache.flink.table.util.TableProperties;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.blink.streaming.connector.hbase.utils.ByteSerializer;
import com.alibaba.blink.streaming.connectors.common.source.SourceCollectorTableFunction;
import com.alibaba.blink.streaming.connectors.common.source.SourceUtils;
import com.alibaba.blink.streaming.connectors.common.util.BlinkStringUtil;
import com.alibaba.blink.table.connectors.conf.BlinkOptions;
import com.alibaba.blink.table.factories.BlinkTableFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.blink.tddl.scan.io.TddlScanInputConfig;
import com.aliyun.wormhole.qanat.blink.tddl.scan.parser.TddlSourceParser;
import com.aliyun.wormhole.qanat.blink.tddl.scan.source.TddlScanTableSource;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.taobao.tddl.client.jdbc.TDataSource;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_PROPERTY_VERSION;
import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_TYPE;

public class TddlScanTableFactory extends BlinkTableFactory implements
		TableSourceParserFactory,
		BatchTableSourceFactory<Row>,
		StreamTableSourceFactory<Row> {

    private final static Logger log = LoggerFactory.getLogger(TddlScanTableFactory.class);
    
	@Override
	public TableSourceParser createParser(
			String tableName, RichTableSchema tableSchema, TableProperties properties) {
	    log.info("tableSchema={}", JSON.toJSONString(tableSchema));
		TableSourceParser tableSourceParser = SourceUtils.createParserFromDDL(tableSchema, properties, classLoader);
		if (tableSourceParser == null) {
		    TddlSourceParser parser = new TddlSourceParser(tableSchema);
		    tableSourceParser = new TableSourceParser(
                new SourceCollectorTableFunction<>(parser),
                Collections.singletonList("f0"));
		}
		return tableSourceParser;
	}

	private TddlScanTableSource createSource(Map<String, String> props) {
		TableProperties tableProperties = new TableProperties();
		tableProperties.putProperties(props);
		RichTableSchema richTableSchema = tableProperties.readSchemaFromProperties(classLoader);

		for (InternalType t : richTableSchema.getColumnTypes()) {
			Class<?> cls = TypeUtils.getExternalClassForType((t));
			if (!ByteSerializer.isSupportedType(cls)) {
				throw new IllegalArgumentException("Unsupported column type: " + cls);
			}
		}

		List<String> fieldList = new ArrayList<>();
		fieldList.addAll(Arrays.asList(richTableSchema.getColumnNames()));

		String appName = tableProperties.getString(QanatTddlScanOptions.APPNAME);
        String tableName = tableProperties.getString(QanatTddlScanOptions.TABLENAME);
        Integer batchSize = tableProperties.getInteger(QanatTddlScanOptions.BATCH_SIZE);
        String whereClause = tableProperties.getString(QanatTddlScanOptions.WHERE_CLAUSE);
        String dbName = tableProperties.getString(QanatTddlScanOptions.DBNAME);
        
        if (BlinkStringUtil.isNotEmpty(dbName)) {
            String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
            JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
            appName = dbMetaJson.getString("appName");
		}
        
        List<TddlScanInputConfig> inputList = new ArrayList<>();
        // 初始化tddl数据源
		System.setProperty("tddl.version.check", "false");
        TDataSource ds = new TDataSource();
        ds.setAppName(appName);
        ds.setDynamicRule(true);
        ds.init();

        Map<String, Set<String>> topology = ds.getTableTopology(tableName);
        for (Map.Entry<String, Set<String>> entry : topology.entrySet()) {
            String groupKey = entry.getKey();
            for (String realTableName : entry.getValue()) {
    			TddlScanInputConfig inputSplit = new TddlScanInputConfig();
    	        inputSplit.setFields(fieldList);
    	        inputSplit.setAppName(appName);
    	        inputSplit.setGroupKey(groupKey);
    	        inputSplit.setTableName(tableName);
    	        inputSplit.setRealTableName(realTableName);
    	        inputSplit.setBatchSize(batchSize == null ? 100000 : batchSize);
    	        inputSplit.setWhereClause(whereClause);
    	        inputList.add(inputSplit);
            }
        }
        log.info("inputList={}", JSON.toJSONString(inputList));
		return new TddlScanTableSource(richTableSchema, tableName, inputList);
	}

	@Override
	protected List<String> supportedSpecificProperties() {
		return BlinkOptions.MYSQLSCAN.SUPPORTED_KEYS;
	}

	@Override
	protected Map<String, String> requiredContextSpecific() {
		Map<String, String> context = new HashMap<>();
		context.put(CONNECTOR_TYPE, "qanat_tddl_scan"); // 
		context.put(CONNECTOR_PROPERTY_VERSION, "1"); // backwards compatibility
		return context;
	}

	@Override
	public BatchTableSource<Row> createBatchTableSource(Map<String, String> properties) {
		return createSource(properties);
	}

	@Override
	public StreamTableSource<Row> createStreamTableSource(Map<String, String> properties) {
		return createSource(properties);
	}
}
