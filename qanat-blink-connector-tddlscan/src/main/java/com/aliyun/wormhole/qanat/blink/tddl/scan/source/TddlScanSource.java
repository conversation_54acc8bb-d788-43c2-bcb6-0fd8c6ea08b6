/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.blink.tddl.scan.source;

import com.alibaba.blink.table.connectors.conf.BlinkOptions;
import com.aliyun.wormhole.qanat.blink.tddl.scan.io.TddlScanInputConfig;
import com.alibaba.blink.streaming.connectors.common.reader.RecordReader;
import com.alibaba.blink.streaming.connectors.common.source.AbstractParallelSource;
import com.alibaba.blink.streaming.connectors.common.source.SourceUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.core.io.InputSplit;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class TddlScanSource extends AbstractParallelSource<Row, Long> {
	private static final Logger LOGGER = LoggerFactory.getLogger(TddlScanSource.class);
	private String logicTable;
	private int totalPartition;
	private List<String> partitionList;
	private List<TddlScanInputConfig> tddlReaderConfigList;
	private RichTableSchema richSchema;

	@Override
	public void initOperator(Configuration config) throws IOException {
		config.setString(BlinkOptions.INNER_SAMPLE_TABLE_NAME, tableName);
	}

	@Override
	public RecordReader<Row, Long> createReader(Configuration configuration) throws IOException {
		LOGGER.info("TddlSource: begin to createReader.");
		return new TddlScanRecordReader(richSchema);
	}


	@Override
	public InputSplit[] createInputSplitsForCurrentSubTask(int numberOfParallelSubTasks, int indexOfThisSubTask) throws IOException {
		LOGGER.info("TddlSource: begin to create input splits for current subTask");
		LOGGER.info("TddlSource: get numberOfParallelSubTasks: {}, indexOfThisSubTask: {}", numberOfParallelSubTasks, indexOfThisSubTask);
		LOGGER.info("TddlSource: get totalPartition: {}", totalPartition);

		List<Integer> subscribedGroups = SourceUtils.modAssign(this.toString(), numberOfParallelSubTasks, indexOfThisSubTask, totalPartition);
		LOGGER.info("TddlSource: get subscribedGroups: {}", subscribedGroups);

		TddlScanReaderConfig[] inputSplits = new TddlScanReaderConfig[subscribedGroups.size()];
		int i = 0;
		for (Integer partitionNumber : subscribedGroups) {
			TddlScanReaderConfig tddlScanReaderConfig = new TddlScanReaderConfig(partitionNumber);
			tddlScanReaderConfig.setTddlScanInputConfig(tddlReaderConfigList.get(partitionNumber));
			LOGGER.info("TddlSource: partitionNumber: {}, add config to current parallel: {}", partitionNumber, tddlScanReaderConfig);

			inputSplits[i++] = tddlScanReaderConfig;
		}

		return inputSplits;
	}


	@Override
	public List<String> getPartitionList() {
		LOGGER.info("TddlSource: get getPartitionList: {}", partitionList);
		return partitionList;
	}


	@Override
	public String toString() {
		String name = String.format("%s:%s", getClass().getSimpleName(), logicTable);
		LOGGER.info("TddlSource: get name: {}", name);
		return name;
	}


	public TddlScanSource(RichTableSchema richSchema, String logicTable, List<TddlScanInputConfig> inputList) {
	    this.richSchema = richSchema;
	    partitionList = new ArrayList<>();
		tddlReaderConfigList = new ArrayList<>();
		
        for (TddlScanInputConfig input : inputList) {
			partitionList.add(input.getRealTableName());
			tddlReaderConfigList.add(input);
        }
		totalPartition = tddlReaderConfigList.size();
		LOGGER.info("TddlSource: get tddlReaderConfigList: {}, totalPartition: {}", tddlReaderConfigList.size(), totalPartition);

		this.logicTable = logicTable;
		LOGGER.info("TddlSource: get logicTable: {}", logicTable);
	}

	public TddlScanSource setTableName(String tableName) {
		this.tableName = tableName;
		return this;
	}
}
