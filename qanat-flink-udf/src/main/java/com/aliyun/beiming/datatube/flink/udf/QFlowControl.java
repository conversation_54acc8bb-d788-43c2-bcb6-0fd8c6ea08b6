package com.aliyun.beiming.datatube.flink.udf;

import java.util.Arrays;
import java.util.concurrent.Executor;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.tair.tairstring.TairStringCluster;
import com.aliyun.tair.tairstring.params.ExincrbyFloatParams;
import com.taobao.diamond.client.impl.DiamondUnitSite;
import com.taobao.diamond.manager.ManagerListener;

import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPoolConfig;

public class QFlowControl extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QFlowControl.class);
	
	public Limiter limiter = null;
	private TairStringCluster tairStringCluster;
    
    @Override
    public void open(FunctionContext context) {
    	try {
    		JSONObject redisConfig = new JSONObject();
    		DiamondUnitSite.getDiamondUnitEnv("pre").addListeners("DATATUBE-FLINK-CONFIG", "DATATUBE-V3", Arrays.asList(new ManagerListener() {
				@Override
				public void receiveConfigInfo(String conf) {
					try {
						if (StringUtils.isNotBlank(conf) && JSONObject.parseObject(conf) != null) {
							redisConfig.put("redisHost", JSONObject.parseObject(conf).getString("redisHost"));
							redisConfig.put("redisPort", JSONObject.parseObject(conf).getInteger("redisPort"));
						}
					} catch (Exception e) {}
				}
				@Override
				public Executor getExecutor() {
					return null;
				}
			}));
			String conf = DiamondUnitSite.getDiamondUnitEnv("pre").getConfig("DATATUBE-FLINK-CONFIG", "DATATUBE-V3", 100000);
			if (StringUtils.isNotBlank(conf) && JSONObject.parseObject(conf) != null) {
				redisConfig.put("redisHost", JSONObject.parseObject(conf).getString("redisHost"));
				redisConfig.put("redisPort", JSONObject.parseObject(conf).getInteger("redisPort"));
			}
    		
			JedisCluster jedisCluster = new JedisCluster(new HostAndPort(redisConfig.getString("redisHost"), redisConfig.getInteger("redisPort")), 3000, 3000, 50, new JedisPoolConfig());
        	tairStringCluster = new TairStringCluster(jedisCluster);
        	log.info("Redis init finish");
        } catch (Exception e) {
        	log.error("JedisCluster init faield, error={}", e.getMessage(), e);
        	throw new RuntimeException("JedisCluster init faield");
        }
    }

	private synchronized void initLimiter(String limitConf) {
		JSONObject limitConfJson = JSON.parseObject(limitConf);
		if (limitConfJson != null && limitConfJson.getJSONArray("rules") != null && limitConfJson.getJSONArray("rules").size() > 0) {
			JSONArray rulesJsonArray = limitConfJson.getJSONArray("rules");
			if (rulesJsonArray != null && rulesJsonArray.size() > 0) {
				JSONObject ruleJson =  rulesJsonArray.getJSONObject(0);
				limiter =  new Limiter(ruleJson.getString("name"), ruleJson.getDouble("limit"), ruleJson.getInteger("interval"));
			}
		}
	}

	private Boolean checkPassLimit(Object pk) {
		log.info("key:{} 命中规则:{}", pk, limiter.getName());
	    try {
	    	ExincrbyFloatParams params = new ExincrbyFloatParams();
	    	params.ex(limiter.getInterval());
	    	params.max(limiter.getRate());
	    	Double result = tairStringCluster.exincrByFloat(limiter.getName(), 1.0, params);
	    	log.info("key:{} 命中规则:{} result:{}", pk, limiter.getName(), result);
			return true;
	    } catch (Exception e) {
	        if(e.getMessage().contains("increment or decrement would overflow")){// 检查返回结果中是否包含错误信息。
	            return false;
	        }
	        throw e;
	    }
	}

    public String eval(String traceId, String limiterId, Object key) {
    	log.info("{} eval({},{},{})", traceId, traceId, limiterId, key);
		long startTs = System.currentTimeMillis();
    	if (limiter == null) {
			init(traceId, limiterId, startTs);
		}
		log.info("{} init limitConf use:{}ms", traceId, System.currentTimeMillis() - startTs);
    	try {
			while (true) {
				if (limiter == null) {
					log.info("{} limiterId[{}] key:{} ts:{} 不需限流", traceId, limiterId, key, System.currentTimeMillis());
					break;
				} else if (checkPassLimit(key)) {
					log.info("{} limiterId[{}] key:{} ts:{} 通过限流", traceId, limiterId, key, System.currentTimeMillis());
					break;
				} else {
					log.info("{} limiterId[{}] key:{} ts:{} 触发限流", traceId, limiterId, key, System.currentTimeMillis());
					try {
	                    Thread.sleep(500);
	                } catch (InterruptedException ignore) {}
				}
			}
	    	log.info("datatube_stream_trace {} {} {} {} {}", traceId, key, System.currentTimeMillis(), limiterId, "flowcontrol");
		} catch(Exception e) {
			log.error("{} limiterId[{}] key:{} ts:{} 限流判断异常:{},跳过限流", traceId, limiterId, key, System.currentTimeMillis(), e.getMessage(), e);
		}
    	return traceId;
    }

	private void init(String traceId, String limiterId, long startTs) {
		try {
			DiamondUnitSite.getDiamondUnitEnv("pre").addListeners(limiterId, "DATATUBE-FLOW-V3", Arrays.asList(new ManagerListener() {
				@Override
				public void receiveConfigInfo(String conf) {
					try {
						initLimiter(conf);
					} catch (Exception e) {}
				}
				@Override
				public Executor getExecutor() {
					return null;
				}
			}));
			String limitConf = DiamondUnitSite.getDiamondUnitEnv("pre").getConfig(limiterId, "DATATUBE-FLOW-V3", 100000);
			log.info("{} limitConf={}", traceId, limitConf);
			initLimiter(limitConf);
		} catch(Exception e) {
			log.error("{} get limit conf from diamond faild, error={}", traceId, e.getMessage(), e);
		}
	}

    @Override
    public void close() {}
    
    static class Limiter {
    	private String name;
    	private Double rate;
    	private Integer interval;
    	
    	public Limiter(String name, Double rate, Integer interval) {
    		this.name = name;
    		this.rate = rate;
    		this.interval = interval;
    	}
    	
    	public String getName() {
    		return name;
    	}
    	
    	public Double getRate() {
    		return rate;
    	}
    	
    	public Integer getInterval() {
    		return interval;
    	}
    }
}