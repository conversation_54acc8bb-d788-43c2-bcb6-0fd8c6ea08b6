package com.aliyun.beiming.datatube.flink.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatContainsKeyUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatContainsKeyUdf.class);
    
    @Override
    public void open(FunctionContext context) {}

    public boolean eval(String type, String jsonStr, String key) {
    	if (StringUtils.isBlank(jsonStr) || StringUtils.isBlank(key)) {
    		return false;
    	}
    	try {
	        JSONObject json = JSON.parseObject(jsonStr);
	        if (json == null) {
	        	return false;
	        }
	        if ("drc".equalsIgnoreCase(type)) {
	        	return json.getJSONObject("fieldValues").contains<PERSON>ey(key);
	        } else if ("obj".equalsIgnoreCase(type)) {
	        	return json.getJSONObject("extParam").getJSONObject("objectInstanceVO").getJSONObject("fieldMap").containsKey(key);
	        } else {
	        	return json.containsKey(key);
	        }
    	} catch(Exception e) {
    		log.error("eval({},{},{}) failed={}", type, jsonStr, key ,e.getMessage(), e);
    		return false;
    	}
    }

    @Override
    public void close() {}
}
