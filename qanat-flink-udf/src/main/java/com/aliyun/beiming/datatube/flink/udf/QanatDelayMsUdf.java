package com.aliyun.beiming.datatube.flink.udf;

import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatDelayMsUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatDelayMsUdf.class);
    
    @Override
    public void open(FunctionContext context) {}

    public Integer eval(Integer delayMs, Long object) {
    	log.info("before sleep eval({},{},{})", delayMs, object, Thread.currentThread().getId());
        try {
            Thread.sleep(delayMs);
        } catch (Exception e) {
            log.error("eval failed:{}", e.getMessage(), e);
        }
        log.info("after sleep eval({},{},{})", delayMs, object, Thread.currentThread().getId());
        return delayMs;
    }

    public String eval(Integer delayMs, String object) {
    	log.info("before sleep eval({},{},{})", delayMs, object, Thread.currentThread().getId());
        try {
            Thread.sleep(delayMs);
        } catch (Exception e) {
            log.error("eval failed:{}", e.getMessage(), e);
        }
        log.info("after sleep eval({},{},{})", delayMs, object, Thread.currentThread().getId());
        return object;
    }

	@Override
    public void close() {}
}