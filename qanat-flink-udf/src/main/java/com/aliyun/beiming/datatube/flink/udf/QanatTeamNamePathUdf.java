package com.aliyun.beiming.datatube.flink.udf;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatTeamNamePathUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatTeamNamePathUdf.class);
    
    @Override
    public void open(FunctionContext context) {}

    public String eval(String teampath, String teamIdNamePair) {
    	log.info("eval({},{})", teampath, teamIdNamePair);
        try {
            if (StringUtils.isBlank(teampath) && StringUtils.isBlank(teamIdNamePair)) {
                return null;
            }
            if (StringUtils.isBlank(teampath) && StringUtils.isNotBlank(teamIdNamePair)) {
                return null;
            }
            if (StringUtils.isNotBlank(teampath) && StringUtils.isBlank(teamIdNamePair)) {
                return teampath;
            }
            List<String> list1 = Arrays.asList(teampath.split("\\."));
            List<String> list2 = Arrays.asList(teamIdNamePair.split(";"));
            Map<String, String> teamIdNameMap = new HashMap<>();
            for (String data : list2) {
                String[] tokens = data.split("\\:");
                if (tokens.length == 2) {
                    teamIdNameMap.put(tokens[0], tokens[1]);
                }
            }
            List<String> result = new ArrayList<>();
            for (String data : list1) {
                if (StringUtils.isNotBlank(data) && StringUtils.isNotBlank(teamIdNameMap.get(data))) {
                    result.add(teamIdNameMap.get(data));
                }
            }
            return StringUtils.join(result, '-');
        } catch(Exception e) {
            log.error("eval failed", e);
        }
        return null;
    }

    @Override
    public void close() {}

    public static void main(String[] args) {
        QanatTeamNamePathUdf obj = new QanatTeamNamePathUdf();
        System.out.println(obj.eval("0.1.2.3.", "1:部门一;2:部门二;3:部门三"));
    }
}