package com.aliyun.beiming.datatube.flink.udf;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatArrayCompareUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatArrayCompareUdf.class);
    
    @Override
    public void open(FunctionContext context) {}

    public boolean eval(String array1, String array2) {
    	log.info("eval({},{})", array1, array2);
        try {
            if (StringUtils.isBlank(array2) && StringUtils.isBlank(array1)) {
                return true;
            }
            if (StringUtils.isBlank(array2) && StringUtils.isNotBlank(array1)) {
                return false;
            }
            if (StringUtils.isNotBlank(array2) && StringUtils.isBlank(array1)) {
                return false;
            }
            List<String> list1 = Arrays.asList(array1.split(","));
            List<String> list2 = Arrays.asList(array2.split(","));
            if (list1.containsAll(list2) && list2.containsAll(list1)) {
                return true;
            } else {
                return false;
            }
        } catch(Exception e) {
            log.error("eval failed", e);
        }
        return false;
    }

    @Override
    public void close() {}
}