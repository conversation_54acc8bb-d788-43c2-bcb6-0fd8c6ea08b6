package com.aliyun.beiming.datatube.flink.udf;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatConcatUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatConcatUdf.class);
    
    @Override
    public void open(FunctionContext context) {}

    public String eval(String separator, Object ... values) {
        try {
            List<String> valueList = new ArrayList<>();
            for (int i=0; i < values.length; i++) {
                if (values[i] == null) {
                    valueList.add("");
                } else {
                    valueList.add(values[i].toString());
                }
            }
            return StringUtils.join(valueList, separator);
        } catch(Exception e) {
            log.error("eval failed", e);
        }
        return "";
    }

    @Override
    public void close() {}
}