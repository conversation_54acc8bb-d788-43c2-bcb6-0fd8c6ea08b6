package com.aliyun.beiming.datatube.flink.udf;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatHoloLogUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatHoloLogUdf.class);
    
    @Override
    public void open(FunctionContext context) {}

    public String eval(String traceId, Long hg_binlog_lsn, Long hg_binlog_event_type, Long hg_binlog_timestamp_us, String type, Object ... values) {
    	log.info("{} hg_binlog_lsn={} hg_binlog_event_type={} hg_binlog_timestamp_us={} type={} row={}", traceId, hg_binlog_lsn, hg_binlog_event_type, hg_binlog_timestamp_us, type, StringUtils.join(values, "`"));
        return traceId;
    }

	@Override
    public void close() {}
}