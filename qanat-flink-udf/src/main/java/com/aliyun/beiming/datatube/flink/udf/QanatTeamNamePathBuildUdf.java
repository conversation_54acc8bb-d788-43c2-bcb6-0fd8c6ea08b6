package com.aliyun.beiming.datatube.flink.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;
import com.aliyun.wormhole.qanat.datasource.QanatDatasourceHandler;
import com.aliyun.wormhole.qanat.datasource.RdsConnectionParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class QanatTeamNamePathBuildUdf extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QanatTeamNamePathBuildUdf.class);
    
    @Override
    public void open(FunctionContext context) {}

    public String eval(String dbName, String teamPath) {
        log.info("eval({},{})", dbName, teamPath);
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
            log.info("dbMetaStr={}", dbMetaStr);
            if (StringUtils.isBlank(dbMetaStr)) {
                return null;
            }
            JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
            RdsConnectionParam param = new RdsConnectionParam();
            param.setUrl(dbMetaJson.getString("jdbcUrl"))
                    .setUserName(dbMetaJson.getString("username"))
                    .setPassword(dbMetaJson.getString("password"));
            conn = QanatDatasourceHandler.connectToTable(param);
            String teamIds = teamPath.replaceAll("\\.", ",").substring(0, teamPath.length() - 1);
            String sql = "select id,team_cname from mariana_metadata.sales_team_info where id in (" + teamIds + ") and is_deleted=0";
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            Map<String, String> data = new HashMap<>();
            while (rs.next()) {
                data.put(rs.getLong("id") + "", rs.getString("team_cname"));
            }
            String teamNamePath = null;
            String [] teamIdArray = teamPath.split("\\.");
            List<String> teamNameArray = new ArrayList<>();
            for (int i=0; i < teamIdArray.length; i++) {
                if ("0".equals(teamIdArray[i]) || StringUtils.isBlank(teamIdArray[i])) {
                    continue;
                } else {
                    if (StringUtils.isNotBlank(data.get(teamIdArray[i]))) {
                        teamNameArray.add(data.get(teamIdArray[i]));
                    }
                }
            }
            teamNamePath = StringUtils.join(teamNameArray, "-");
            log.info("teamNamePath={}", teamNamePath);
            return teamNamePath;
        } catch (Exception e) {
            log.error("sql exec failed:{}", e.getMessage(), e);
        } finally {
            if (ps != null) {
                try {
                    ps.close();
                } catch(Exception e) {}
                ps = null;
            }
            if (conn != null) {
                try {
                    conn.close();
                } catch(Exception e) {}
                conn = null;
            }
        }
        return null;
    }

    @Override
    public void close() {}

    public static void main(String[] args) {
        QanatTeamNamePathBuildUdf obj = new QanatTeamNamePathBuildUdf();
        System.out.println(obj.eval("0.1.2.3.", "1:部门一;2:部门二;3:部门三"));
    }
}