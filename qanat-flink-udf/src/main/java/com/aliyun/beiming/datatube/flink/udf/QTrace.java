package com.aliyun.beiming.datatube.flink.udf;

import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QTrace extends ScalarFunction {
    
    private final static Logger log = LoggerFactory.getLogger(QTrace.class);

    public String eval(String traceId, Object pk, String source, String type) {
    	log.info("datatube_stream_trace {} pk={} ts={} src={} type={}", traceId, pk, System.currentTimeMillis(), source, type);
        return traceId;
    }

    public String eval(String traceId, Long hg_binlog_event_type, Object pk, String source, String type) {
    	log.info("datatube_stream_trace {} hg_binlog_event_type={} pk={} ts={} src={} type={}", traceId, hg_binlog_event_type, pk, System.currentTimeMillis(), source, type);
        return traceId;
    }

    public String eval(String traceId, Long hg_binlog_lsn, Long hg_binlog_event_type, Long hg_binlog_timestamp_us, Object pk, String source, String type) {
    	log.info("datatube_stream_trace {} hg_binlog_lsn={} hg_binlog_event_type={} hg_binlog_timestamp_us={} pk={} ts={} src={} type={}", traceId, hg_binlog_lsn, hg_binlog_event_type, hg_binlog_timestamp_us, pk, System.currentTimeMillis(), source, type);
        return traceId;
    }

	@Override
    public void close() {}
}