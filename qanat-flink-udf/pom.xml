<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<!-- <parent>
		<groupId>com.aliyun.wormhole</groupId>
		<artifactId>qanat-aliyun-inc-com</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</parent> -->
	<groupId>com.aliyun.beiming.datatube</groupId>
	<artifactId>qanat-flink-udf</artifactId>
	<version>1.0.0</version>
	<name>qanat-flink-udf</name>
	<url>http://maven.apache.org</url>
	<properties>
        <maven.build.timestamp.format>yyyyMMdd_hhmm</maven.build.timestamp.format>
	</properties>

	<dependencies>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.68.noneautotype</version>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-streaming-java</artifactId>
            <version>1.15.0</version>
            <!--<scope>provided</scope>-->
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table</artifactId>
            <version>1.15.0</version>
            <type>pom</type>
            <!--<scope>provided</scope>-->
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-core</artifactId>
            <version>1.15.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-common</artifactId>
            <version>1.15.0</version>
        </dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.1</version>
		</dependency>
    	<dependency>
    		<groupId>com.taobao.diamond</groupId>
    		<artifactId>diamond-client</artifactId>
    		<version>3.8.11</version>
    	</dependency>
        <dependency>
			<groupId>com.aliyun.tair</groupId>
			<artifactId>alibabacloud-tairjedis-sdk</artifactId>
			<version>3.0.4</version>
		</dependency>
        <dependency>
            <groupId>com.aliyun.wormhole</groupId>
            <artifactId>qanat-datasource</artifactId>
            <version>1.0.0</version>
        </dependency>
	</dependencies>
	
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.3</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<verbose>true</verbose>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
                <executions>
                    <execution>
                        <id>jar-with-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <shadedArtifactAttached>true</shadedArtifactAttached>
                            <shadedClassifierName>${maven.build.timestamp}</shadedClassifierName>
                            <createDependencyReducedPom>true</createDependencyReducedPom>
                            <dependencyReducedPomLocation>${project.basedir}/target/dependency-reduced-pom.xml
                            </dependencyReducedPomLocation>
                            <filters>
                                <!-- Globally exclude log4j.properties from our JAR files. -->
                                <filter>
                                    <artifact>*</artifact>
                                    <excludes>
                                        <exclude>log4j.properties</exclude>
                                        <exclude>log4j-test.properties</exclude>
                                        <exclude>META-INF/*.SF</exclude>  
                                        <exclude>META-INF/*.DSA</exclude>  
                                        <exclude>META-INF/*.RSA</exclude>
                                        <exclude>online/*.properties</exclude>
                                        <exclude>test/*.properties</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                            <artifactSet>
                                <includes>
                                    <!-- Unfortunately, the next line is necessary for now to force 
                                        the execution of the Shade plugin upon all sub modules. This will generate 
                                        effective poms, i.e. poms which do not contain properties which are derived 
                                        from this root pom. In particular, the Scala version properties are defined 
                                        in the root pom and without shading, the root pom would have to be Scala 
                                        suffixed and thereby all other modules. -->

                                    <include>*</include>
                                </includes>
                            </artifactSet>
                            <relocations>
                                <relocation>
                                    <pattern>com.alibaba.druid</pattern>
                                    <shadedPattern>com.alibaba.blink.shaded.tddl.com.alibaba.druid</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>com.google</pattern>
                                    <shadedPattern>com.alibaba.blink.shaded.com.google</shadedPattern>
                                </relocation>
                            </relocations>
                            <!--<finalName>${project.artifactId}-${project.version}-jar-with-dependencies</finalName> -->
                        </configuration>
                    </execution>
                </executions>
			</plugin>

		</plugins>
	</build>
</project>
