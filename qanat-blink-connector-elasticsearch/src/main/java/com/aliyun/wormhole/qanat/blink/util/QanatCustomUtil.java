package com.aliyun.wormhole.qanat.blink.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QanatCustomUtil {
    
    private final static Logger log = LoggerFactory.getLogger(QanatCustomUtil.class);
    
    public static Map<String, Object> buildDocData(JSONObject mappingJson, Map<String, Object> inputData) {
        Map<String, Object> docData = new HashMap<>();
        for (String key : mappingJson.keySet()) {
            JSONObject keyJson = mappingJson.getJSONObject(key);
            if ("nested".equalsIgnoreCase(keyJson.getString("type")) || "join".equalsIgnoreCase(keyJson.getString("type")) || keyJson.containsKey("properties")) {
                List<Map<String, Object>> list = new ArrayList<>();
                if (keyJson.containsKey("dataType")) {
                    if (inputData.get(key) != null) {
                        String[] array = inputData.get(key).toString().split("\\|");
                        for (String obj : array) {
                            Map<String, Object> subDocData = new HashMap<>();
                            String[] keySet = obj.split("\\`");
                            boolean emptyObj = true;
                            for (String entry : keySet) {
                                String [] entryArray = entry.split("\\^");
                                if (entryArray.length == 2 && StringUtils.isNotBlank(entryArray[1])) {
                                    emptyObj = false;
                                    break;
                                }
                            }
                            if (emptyObj) {
                                continue;
                            }
                            for (String entry : keySet) {
                                String [] entryArray = entry.split("\\^");
                                if (entryArray.length == 2) {
                                    if (keyJson.getJSONObject("properties") != null && keyJson.getJSONObject("properties").getJSONObject(entryArray[0]) != null) {
                                        subDocData.put(entryArray[0], getValue(keyJson.getJSONObject("properties").getJSONObject(entryArray[0]).getString("dataType"), keyJson.getJSONObject("properties").getJSONObject(entryArray[0]).getString("type"), entryArray[1]));
                                    } else {
                                        subDocData.put(entryArray[0], entryArray[1]);
                                    }
                                }
                            }
                            list.add(subDocData);
                        }
                        if ("object".equals(keyJson.getString("dataType")) && list.size() > 0) {
                            docData.put(key, list.get(0));
                        } else if ("object".equals(keyJson.getString("dataType")) && list.size() == 0) {
                            docData.put(key, new HashMap<>());
                        } else {
                            docData.put(key, list);
                        }
                    } else {
                        if ("object".equals(keyJson.getString("dataType")) && list.size() > 0) {
                            docData.put(key, new HashMap<>());
                        } else {
                            docData.put(key, new ArrayList<>());
                        }
                    }
                }
            } else {
                String dataKey = key;
                if (keyJson.containsKey("dataType") && "array".equals(keyJson.getString("dataType"))) {
                    if (inputData.get(key) != null) {
                        String[] array = inputData.get(dataKey).toString().split("\\|");
                        docData.put(key, Arrays.asList(array));
                    } else {
                        docData.put(key, new ArrayList<>());
                    }
                } else {
                    docData.put(key, inputData.get(dataKey));
                }
            }
        }
        return docData;
    }
    
    public static Map<String, Object> buildFlatMappingDocData(JSONObject mappingJson, Map<String, Object> inputData) {
        Map<String, Object> docData = new HashMap<>();
        for (String key : mappingJson.keySet()) {
            JSONObject keyJson = mappingJson.getJSONObject(key);
            if ("object".equalsIgnoreCase(keyJson.getString("dataType"))) {
                if (inputData.get(key) != null) {
                    docData.put(key, JSON.parseObject(inputData.get(key).toString(), Map.class));
                } else {
                    docData.put(key, new HashMap<>());
                }
            } else if ("array".equalsIgnoreCase(keyJson.getString("dataType"))) {
                if (inputData.get(key) != null) {
                    docData.put(key, Arrays.asList(inputData.get(key).toString().split(",")));
                } else {
                    docData.put(key, new ArrayList<>());
                }
            } else if ("jsonarray".equalsIgnoreCase(keyJson.getString("dataType"))) {
                if (inputData.get(key) != null) {
                    docData.put(key, JSON.parseArray(inputData.get(key).toString()));
                } else {
                    docData.put(key, new ArrayList<>());
                }
            } else if ("nested".equalsIgnoreCase(keyJson.getString("type"))) {
                if (inputData.get(key) != null) {
                    docData.put(key, Arrays.asList(JSON.parseObject(inputData.get(key).toString(), Map.class)));
                } else {
                    docData.put(key, new ArrayList<>());
                }
            } else {
            	if (inputData.get(key) != null && inputData.get(key) instanceof String) {
            		docData.put(key, StringEscapeUtils.unescapeJavaScript(inputData.get(key).toString()));
            	} else {
            		docData.put(key, inputData.get(key));
            	}
            }
        }
        return docData;
    }

    private static Object getValue(String dataType, String type, String value) {
        try {
        	if ("array".equalsIgnoreCase(dataType)) {
        		String[] tokens = value.split("\\.");
        		return Arrays.asList(tokens).stream().distinct().collect(Collectors.toList());
        	}
            switch (type) {
                case "text": return value;
                case "long": return Long.valueOf(value);
                case "boolean": return Boolean.valueOf(value);
                case "float": return Float.valueOf(value);
                case "integer": return Integer.valueOf(value);
                case "date": return Long.valueOf(value);
                default: return value;
            }
        } catch (Exception e) {
            log.error("getValue({},{}) failed", type, value, e);
        }
        return null;
    }
    
    public static void main(String[] args) {
    	String str = "e[w]r_e,wr-ew\"rew''r{ew}r";
    	System.out.println(str);
    	System.out.println(StringEscapeUtils.unescapeJavaScript(str));
    }
}
