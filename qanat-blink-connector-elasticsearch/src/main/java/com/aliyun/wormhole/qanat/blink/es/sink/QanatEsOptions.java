package com.aliyun.wormhole.qanat.blink.es.sink;

import java.util.Arrays;
import java.util.List;

import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.configuration.ConfigOptions;

public class QanatEsOptions {
    
    public static class QanatEsSink {

        public static final ConfigOption<String> Index = ConfigOptions.key("index".toLowerCase()).noDefaultValue();
        public static final ConfigOption<String> Type = ConfigOptions.key("es_type".toLowerCase()).noDefaultValue();
        public static final ConfigOption<String> Host = ConfigOptions.key("host".toLowerCase()).noDefaultValue();
        public static final ConfigOption<String> Port = ConfigOptions.key("port".toLowerCase()).noDefaultValue();
        public static final ConfigOption<String> Mapping = ConfigOptions.key("mapping".toLowerCase()).noDefaultValue();
        public static final ConfigOption<String> MappingMode = ConfigOptions.key("mappingMode".toLowerCase()).noDefaultValue();
        public static final ConfigOption<String> Username = ConfigOptions.key("username".toLowerCase()).noDefaultValue();
        public static final ConfigOption<String> Password = ConfigOptions.key("password".toLowerCase()).noDefaultValue();
        public static final ConfigOption<Integer> MaxRetryTimeoutMs = ConfigOptions.key("maxRetryTimeoutMs".toLowerCase()).defaultValue(30000);
        public static final ConfigOption<Integer> ConnectTimeoutMs = ConfigOptions.key("connectTimeoutMs".toLowerCase()).defaultValue(30000);
        public static final ConfigOption<Integer> SocketTimeoutMs = ConfigOptions.key("socketTimeoutMs".toLowerCase()).defaultValue(30000);
        public static final ConfigOption<Integer> ConnectionRequestTimeoutMs = ConfigOptions.key("connectionRequestTimeoutMs".toLowerCase()).defaultValue(10000);
        public static final ConfigOption<Long> ScheduleFlushInterval = ConfigOptions.key("scheduleFlushInterval".toLowerCase()).defaultValue(2500L);
        public static final ConfigOption<Integer> BatchSize = ConfigOptions.key("batchSize".toLowerCase()).defaultValue(200);
        public static final ConfigOption<Integer> Retries = ConfigOptions.key("retries".toLowerCase()).defaultValue(3);
        public static final ConfigOption<Integer> RetryIntervalMs = ConfigOptions.key("retryIntervalMs".toLowerCase()).defaultValue(500);
        public static final ConfigOption<Integer> ConcurrentRequests = ConfigOptions.key("concurrentRequests".toLowerCase()).defaultValue(0);
        public static final ConfigOption<Long> BulkSizeMb = ConfigOptions.key("bulkSizeMb".toLowerCase()).defaultValue(5L);
        
        public static final String PARAMS_HELP_MSG = String.format(
            "required params:%s,%s,%s,%s,%s,%s,%s\n",
            Host,
            Port,
            Username,
            Password,
            Index,
            Type,
            Mapping);
    
        public static final List<String> SUPPORTED_KEYS = Arrays.asList(Host.key(),
            Port.key(),
            Username.key(),
            Password.key(),
            Index.key(),
            Type.key(),
            Mapping.key(),
            MaxRetryTimeoutMs.key(),
            ConnectTimeoutMs.key(),
            SocketTimeoutMs.key(),
            ConnectionRequestTimeoutMs.key(),
            ScheduleFlushInterval.key(),
            BatchSize.key(),
            Retries.key(),
            RetryIntervalMs.key(),
            ConcurrentRequests.key(),
            BulkSizeMb.key());
        }
}