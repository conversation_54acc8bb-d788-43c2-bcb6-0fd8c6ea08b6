/*
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 * 	http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.alibaba.blink.streaming.connectors.elasticsearch.util;

import org.apache.flink.util.Preconditions;

import com.google.gson.JsonObject;
import io.searchbox.client.JestClient;
import io.searchbox.client.JestClientFactory;
import io.searchbox.client.JestResult;
import io.searchbox.client.config.HttpClientConfig;
import io.searchbox.client.config.HttpClientConfig.Builder;
import io.searchbox.core.Bulk;
import io.searchbox.core.Get;
import io.searchbox.indices.CreateIndex;
import io.searchbox.indices.IndicesExists;
import io.searchbox.indices.aliases.AddAliasMapping;
import io.searchbox.indices.aliases.ModifyAliases;
import io.searchbox.indices.mapping.PutMapping;
import org.apache.http.HttpHost;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

public class ESClient {
	private static final Logger log = LoggerFactory.getLogger(ESClient.class);

	private JestClient jestClient;
	private JestClientFactory factory;

	public ESClient(
			String endpoint,
			String user,
			String passwd,
			boolean multiThread,
			int readTimeout,
			boolean compression,
			boolean discovery) {

		factory = new JestClientFactory();
		Builder httpClientConfig = new HttpClientConfig
				.Builder(endpoint)
				.multiThreaded(multiThread)
				.connTimeout(30000)
				.readTimeout(readTimeout)
				.maxTotalConnection(200)
				.requestCompressionEnabled(compression)
				.discoveryEnabled(discovery)
				.discoveryFrequency(5l, TimeUnit.MINUTES);

		if (!("".equals(user) || "".equals(passwd))) {
			httpClientConfig.setPreemptiveAuth(new HttpHost(endpoint)).defaultCredentials(user, passwd);
		}

		factory.setHttpClientConfig(httpClientConfig.build());
	}

	public void openJestClient(){
		if (null == jestClient) {
			jestClient = factory.getObject();
		}
	}

	public boolean indicesExists(String indexName) throws Exception {
		boolean isIndicesExists = false;
		JestResult rst = jestClient.execute(new IndicesExists.Builder(indexName).build());
		if (rst.isSucceeded()) {
			isIndicesExists = true;
		} else {
			switch (rst.getResponseCode()) {
				case 404:
					isIndicesExists = false;
					break;
				case 401:
					// 无权访问
				default:
					log.warn(rst.getErrorMessage());
					break;
			}
		}
		return isIndicesExists;
	}

	public JestResult get(String indexName, String typeName, String docId, int retryTimes) {
		int retryCnt = retryTimes;
		Get get = new Get.Builder(indexName, docId).type(typeName).build();
		try {
			return jestClient.execute(get);
		} catch (IOException e) {
			if (retryCnt > 0) {
				retryCnt -= 1;
				log.error("Fetch ElasticSearch server with " +
						"index {}, type {}, docID {} failed, " +
						"will retry for time {}", indexName, typeName, docId, retryCnt);
				try {
					Thread.sleep(1000L);
				} catch (InterruptedException e1) {
					log.error("Interrupted while waiting for retry.");
					throw new RuntimeException(e1);
				}
				return get(indexName, typeName, docId, retryCnt);
			} else {
				throw new RuntimeException(e);
			}
		}
	}

	public boolean createIndex(
			String indexName, String typeName,
			Object mappings, String settings, boolean dynamic) throws Exception {
		JestResult rst = null;
		if (!indicesExists(indexName)) {
			log.info("create index " + indexName);
			rst = jestClient.execute(
					new CreateIndex.Builder(indexName)
							.settings(settings)
							.setParameter("master_timeout", "5m")
							.build()
			);
			//index_already_exists_exception
			if (!rst.isSucceeded()) {
				if (getStatus(rst) == 400) {
					log.info(String.format("index [%s] already exists", indexName));
					return true;
				} else {
					log.error(rst.getErrorMessage());
					return false;
				}
			} else {
				log.info(String.format("create [%s] index success", indexName));
			}
		}

		int idx = 0;
		while (idx < 5) {
			if (indicesExists(indexName)) {
				break;
			}
			Thread.sleep(2000);
			idx++;
		}
		if (idx >= 5) {
			return false;
		}

		if (dynamic) {
			log.info("ignore mappings");
			return true;
		}
		log.info("create mappings for " + indexName + "  " + mappings);
		rst = jestClient.execute(new PutMapping.Builder(indexName, typeName, mappings)
										.setParameter("master_timeout", "5m").build());
		if (!rst.isSucceeded()) {
			if (getStatus(rst) == 400) {
				log.info(String.format("index [%s] mappings already exists", indexName));
			} else {
				log.error(rst.getErrorMessage());
				return false;
			}
		} else {
			log.info(String.format("index [%s] put mappings success", indexName));
		}
		return true;
	}

	/**
	 * Add binding for alias and index.
	 * @param indexName index name
	 * @param alias alias for index
	 * @return true if success
	 * @throws Exception
	 */
	public boolean addAliasForIndex(String indexName, String alias) throws Exception {
		JestResult rst = null;
		ModifyAliases modifyAliases = new ModifyAliases.Builder(
				new AddAliasMapping.Builder(indexName, alias).build()).build();
		log.info("Create alias {} for index {}.", alias, indexName);
		rst = jestClient.execute(modifyAliases);
		Preconditions.checkState(rst != null,
				"Unexpected exception happened when create alias " +
						alias + " for index " + indexName);
		if (!rst.isSucceeded()) {
			if (getStatus(rst) == 400) {
				log.info("Alias {}  for index {} already exists.", alias, indexName);
				return true;
			} else {
				log.error(rst.getErrorMessage());
				return false;
			}
		} else {
			log.info("Create alias {} for index {} successfully.", alias, indexName);
		}
		return true;
	}

	public Integer getStatus(JestResult rst) {
		JsonObject jsonObject = rst.getJsonObject();
		if (jsonObject.has("status")) {
			return jsonObject.get("status").getAsInt();
		}
		return 600;
	}

	public boolean isBulkResult(JestResult rst) {
		JsonObject jsonObject = rst.getJsonObject();
		return jsonObject.has("items");
	}

	public JestResult bulkInsert(Bulk.Builder bulk, int trySize) throws Exception {
		JestResult rst = null;
		rst = jestClient.execute(bulk.build());
		if (!rst.isSucceeded()) {
			log.warn(rst.getErrorMessage());
		}
		return rst;
	}

	/**
	 * 关闭JestClient客户端
	 */
	public void closeJestClient() {
		if (jestClient != null) {
			jestClient.shutdownClient();
			jestClient = null;
		}
	}
}
