/*
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 * 	http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.alibaba.blink.streaming.connectors.elasticsearch;

import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.table.factories.BatchCompatibleTableSinkFactory;
import org.apache.flink.table.factories.BatchTableSourceFactory;
import org.apache.flink.table.factories.StreamTableSinkFactory;
import org.apache.flink.table.factories.StreamTableSourceFactory;
import org.apache.flink.table.sinks.BatchCompatibleStreamTableSink;
import org.apache.flink.table.sinks.StreamTableSink;
import org.apache.flink.table.sources.BatchTableSource;
import org.apache.flink.table.sources.LookupConfig;
import org.apache.flink.table.sources.StreamTableSource;
import org.apache.flink.table.util.TableProperties;
import org.apache.flink.types.Row;
import org.apache.flink.util.StringUtils;

import com.alibaba.blink.streaming.connectors.common.exception.NotEnoughParamsException;
import com.alibaba.blink.streaming.connectors.elasticsearch.dim.ElasticSearchTableSource;
import com.alibaba.blink.streaming.connectors.elasticsearch.sink.ElasticSearchTableSink;
import com.alibaba.blink.streaming.connectors.elasticsearch.sink.QanatEsOutputFormat;
import com.alibaba.blink.table.cache.CacheConfig;
import com.alibaba.blink.table.cache.CacheStrategy;
import com.alibaba.blink.table.connectors.conf.BlinkOptions;
import com.alibaba.blink.table.factories.BlinkTableFactory;

import com.aliyun.wormhole.qanat.blink.es.sink.QanatEsOptions;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_PROPERTY_VERSION;
import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_TYPE;

public class QanatEsTableFactory extends BlinkTableFactory implements
		StreamTableSinkFactory<Tuple2<Boolean, Row>>,
		BatchCompatibleTableSinkFactory<Tuple2<Boolean, Row>>,
		BatchTableSourceFactory<BaseRow>,
		StreamTableSourceFactory<BaseRow> {

	private ElasticSearchTableSink createSink(Map<String, String> props) {
		TableProperties properties = new TableProperties();
		properties.putProperties(props);
		RichTableSchema schema = properties.readSchemaFromProperties(classLoader);

		String index = properties.getString(QanatEsOptions.QanatEsSink.Index);
        String type = properties.getString(QanatEsOptions.QanatEsSink.Type);
        String host = properties.getString(QanatEsOptions.QanatEsSink.Host);
        String port = properties.getString(QanatEsOptions.QanatEsSink.Port);
        String mapping = properties.getString(QanatEsOptions.QanatEsSink.Mapping);
        String mappingMode = properties.getString(QanatEsOptions.QanatEsSink.MappingMode);
        String username = properties.getString(QanatEsOptions.QanatEsSink.Username);
        String password = properties.getString(QanatEsOptions.QanatEsSink.Password);
        int maxRetryTimeoutMs = properties.getInteger(QanatEsOptions.QanatEsSink.MaxRetryTimeoutMs);
        int connectTimeoutMs = properties.getInteger(QanatEsOptions.QanatEsSink.ConnectTimeoutMs);
        int socketTimeoutMs = properties.getInteger(QanatEsOptions.QanatEsSink.SocketTimeoutMs);
        int connectionRequestTimeoutMs = properties.getInteger(QanatEsOptions.QanatEsSink.ConnectionRequestTimeoutMs);
        long scheduleFlushInterval = properties.getLong(QanatEsOptions.QanatEsSink.ScheduleFlushInterval);
        int batchSize = properties.getInteger(QanatEsOptions.QanatEsSink.BatchSize);
        int retries = properties.getInteger(QanatEsOptions.QanatEsSink.Retries);
        int retryIntervalMs = properties.getInteger(QanatEsOptions.QanatEsSink.RetryIntervalMs);
        int concurrentRequests = properties.getInteger(QanatEsOptions.QanatEsSink.ConcurrentRequests);
        long bulkSizeMb = properties.getLong(QanatEsOptions.QanatEsSink.BulkSizeMb);
        if (StringUtils.isNullOrWhitespaceOnly(index) || StringUtils.isNullOrWhitespaceOnly(type)
            || StringUtils.isNullOrWhitespaceOnly(host) || StringUtils.isNullOrWhitespaceOnly(port)
            || StringUtils.isNullOrWhitespaceOnly(username) || StringUtils.isNullOrWhitespaceOnly(password)) {
            throw new NotEnoughParamsException(QanatEsOptions.QanatEsSink.PARAMS_HELP_MSG);
        }
        QanatEsOutputFormat.Builder builder = new QanatEsOutputFormat.Builder();
        builder.setIndex(index)
                .setType(type)
                .setHost(host)
                .setPort(port)
                .setMapping(mapping)
                .setMappingMode(mappingMode)
                .setUsername(username)
                .setPassword(password)
                .setMaxRetryTimeoutMs(maxRetryTimeoutMs)
                .setConnectTimeoutMs(connectTimeoutMs)
                .setSocketTimeoutMs(socketTimeoutMs)
                .setConnectionRequestTimeoutMs(connectionRequestTimeoutMs)
                .setScheduleFlushInterval(scheduleFlushInterval)
                .setBatchSize(batchSize)
                .setRetries(retries)
                .setRetryIntervalMs(retryIntervalMs)
                .setConcurrentRequests(concurrentRequests)
                .setBulkSizeMb(bulkSizeMb);
        if (schema.getPrimaryKeys().size() == 1){
            builder.setPk(schema.getPrimaryKeys().get(0));
        }
		return new ElasticSearchTableSink(builder, schema);
	}

	@Override
	protected List<String> supportedSpecificProperties() {
		return mergeProperties(BlinkOptions.ELASTICSEARCH.SUPPORTED_KEYS,
				BlinkOptions.DIM.SUPPORTED_KEYS);
	}

	@Override
	protected Map<String, String> requiredContextSpecific() {
		Map<String, String> context = new HashMap<>();
		context.put(CONNECTOR_TYPE, "qanat_es"); // ELASTICSEARCH
		context.put(CONNECTOR_PROPERTY_VERSION, "1"); // backwards compatibility
		return context;
	}

	@Override
	public BatchCompatibleStreamTableSink<Tuple2<Boolean, Row>> createBatchCompatibleTableSink(
			Map<String, String> properties) {
		return createSink(properties);
	}

	@Override
	public StreamTableSink<Tuple2<Boolean, Row>> createStreamTableSink(
			Map<String, String> properties) {
		return createSink(properties);
	}

	@Override
	public StreamTableSource<BaseRow> createStreamTableSource(Map<String, String> props) {
		TableProperties properties = new TableProperties();
		properties.putProperties(props);
		String sqlTableName = properties.readTableNameFromProperties();
		RichTableSchema schema = properties.readSchemaFromProperties(classLoader);

		String endPoint = properties.getString(BlinkOptions.ELASTICSEARCH.END_POINT);
		String accessId = properties.getString(BlinkOptions.ELASTICSEARCH.ACCESS_ID);
		String accessKey = properties.getString(BlinkOptions.ELASTICSEARCH.ACCESSS_KEY);
		String index = properties.getString(BlinkOptions.ELASTICSEARCH.INDEX);
		String type = properties.getString(BlinkOptions.ELASTICSEARCH.TYPE);
		int maxRetryTimes = properties.getInteger(BlinkOptions.ELASTICSEARCH.OPTIONAL_MAX_RETRY_TIMES);
		int timeOut = properties.getInteger(BlinkOptions.ELASTICSEARCH.OPTIONAL_TIMEOUT);
		boolean discovery = properties.getBoolean(BlinkOptions.ELASTICSEARCH.OPTIONAL_DISCOVERY);
		boolean compression = properties.getBoolean(BlinkOptions.ELASTICSEARCH.OPTIONAL_COMPRESSION);
		boolean multiThread = properties.getBoolean(BlinkOptions.ELASTICSEARCH.OPTIONAL_MULTI_THREAD);
		// support settings.
		// String settings = properties.getString(BlinkOptions.ELASTICSEARCH.OPTIONAL_SETTINGS);
		if (StringUtils.isNullOrWhitespaceOnly(endPoint) || StringUtils.isNullOrWhitespaceOnly(index)
				|| StringUtils.isNullOrWhitespaceOnly(type)) {
			throw new NotEnoughParamsException(BlinkOptions.ELASTICSEARCH.PARAMS_HELP_MSG);
		}

		// create cacheConfig
		CacheStrategy cacheStrategy = BlinkOptions.DIM.getCacheStrategy(properties, true);
		String cacheReloadTimeBlacklist = properties.getString(BlinkOptions.DIM.OPTIONAL_CACHE_RELOAD_TIME_BLACKLIST);
		List<Tuple2<Long, Long>> timeRangeBlacklist = BlinkOptions.DIM.parseTimeRangeBlacklist(cacheReloadTimeBlacklist);
		int scanLimit = properties.getInteger(BlinkOptions.DIM.OPTIONAL_CACHE_SCAN_LIMIT);
		CacheConfig cacheConfig = new CacheConfig(cacheStrategy, timeRangeBlacklist, scanLimit);

		// reuse getLookupConfig
		properties.setBoolean(BlinkOptions.DIM.OPTIONAL_ASYNC, false); // do not support sync lookup now.
		LookupConfig lookupConfig = BlinkOptions.DIM.getLookupConfig(properties);
		return new ElasticSearchTableSource(sqlTableName,
				schema,
				endPoint,
				accessId,
				accessKey,
				index,
				type,
				maxRetryTimes,
				lookupConfig,
				cacheConfig,
				multiThread,
				timeOut, // http client read timeout.
				compression,
				discovery);
	}

	@Override
	public BatchTableSource<BaseRow> createBatchTableSource(Map<String, String> properties) {
		return (BatchTableSource<BaseRow>) createStreamTableSource(properties);
	}
}
