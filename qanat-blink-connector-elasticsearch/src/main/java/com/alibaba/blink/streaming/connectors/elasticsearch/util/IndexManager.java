/*
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.You may obtain a copy of the License at
 *
 * 	http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.alibaba.blink.streaming.connectors.elasticsearch.util;

import org.apache.commons.lang3.time.FastDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Date;
import java.sql.Timestamp;
import java.util.PriorityQueue;
import java.util.TimeZone;

/**
 * This class is used to manage the current active index, it will also cache the past 10 indices if
 * dynamic indexing is set to be true.
 *
 * <p>This class is not thread safe.
 */
public class IndexManager {
	private static final Logger LOG = LoggerFactory.getLogger(IndexManager.class);
	
	private PriorityQueue<String> cache;

	private FastDateFormat dfDate;
	private String alias;
	private ESClient esClient;

	public IndexManager(String alias, ESClient esClient, String timeZone, String indexInterval) {
		this.cache = new PriorityQueue<>(10);
		this.esClient = esClient;
		this.alias = alias;
		this.dfDate = getDateFormat(timeZone, getFormatString(indexInterval));
	}
	
	/**
	* Get the index name for a field value, will create new index if check exists failed.
	* For checking index existence, we first look up the cache then the ES server.
	*
	* @param indexField index field value to compute index name
 	* @return index name for the field.
	*/
	public String getAndBook(Object indexField, String typeName, Object mappings, 
						String settings, boolean dynamic) {
		if (indexField == null) {
			return null;
		}

		try {
			String index = alias + getFormatField(indexField);
			if (checkIndexExists(index)) {
				return index;
			} else {
			// 1. look up ES server.
			if (!esClient.indicesExists(index)) {
				// 2. create index if it does not exist.
				synchronized (this) {
					try {
						// 2.1 create index in ES server.
						esClient.createIndex(index, typeName, mappings, settings, dynamic);
						// 2.2 add alias for this created index.
						esClient.addAliasForIndex(index, alias);
					} catch (Exception ex) {
						LOG.error("Exception when create index {} with type {}", index, typeName);
						throw new RuntimeException(ex.getMessage());
					}
				}
			}
			// 3. book up the index
			addIntoCache(index);
			return index;
			}
		} catch (Exception e) {
			return null;
		}
	}

	private String getFormatString(String indexInterval) {
		switch (indexInterval) {
		case "d":
			return "yyyyMMdd";
		case "m":
			return "yyyyMM";
		case "w":
			return "yyyyMMW";
		default:
			throw new RuntimeException("Supported index interval: " + "d(day), m(month), " +
					"w(week).");
		}
	}

	private FastDateFormat getDateFormat(String timeZone, String format) {
		if (null == timeZone || timeZone.isEmpty()) {
			return FastDateFormat.getInstance(format);
		}
		return FastDateFormat.getInstance(format, TimeZone.getTimeZone(timeZone));
	}

	private String getFormatField(Object indexField) {
		String ret;
		if (indexField instanceof Timestamp) {
			ret = this.dfDate.format((Timestamp) indexField);
		} else if (indexField instanceof Date) {
			ret = this.dfDate.format((Date) indexField);
		} else if (indexField instanceof Long) {
			Long tmp = (Long)indexField;
			if (tmp < 1000000000000L) {
				tmp = tmp * 1000; // transformSchema it to million seconds.
			}
			ret = this.dfDate.format(new Date(tmp));
		} else {
			LOG.error("Unsupported index field type {} for dynamic indexing.", indexField.getClass().getCanonicalName());
			throw new RuntimeException("Unsupported index field type or dynamic indexing.");
		}
		return ret;
	}

	private void addIntoCache(String indexName) {
		if (cache.size() > 10) {
		cache.poll(); // remove the earliest time.
	}
		cache.add(indexName);
	}

	private boolean checkIndexExists(String indexName) {
		return this.cache.contains(indexName);
	}

	public static void main(String[] args) {
		FastDateFormat dfDate = FastDateFormat.getInstance("yyyyMMW");
		System.out.print(dfDate.format(new Timestamp(1234523451123L)));
		PriorityQueue<String> q = new PriorityQueue<>(10);
		q.add("aaa" + 20100213);
		q.add("aaa" + 20100110);
		System.out.print(q.poll());
	}
}
