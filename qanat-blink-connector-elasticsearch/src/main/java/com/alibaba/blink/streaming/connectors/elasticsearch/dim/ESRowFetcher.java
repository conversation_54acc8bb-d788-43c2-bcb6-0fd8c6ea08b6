/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.streaming.connectors.elasticsearch.dim;

import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.table.dataformat.BinaryString;
import org.apache.flink.table.dataformat.GenericRow;
import org.apache.flink.table.plan.schema.IndexKey;
import org.apache.flink.table.runtime.conversion.DataStructureConverters;
import org.apache.flink.table.types.TypeInfoWrappedDataType;
import org.apache.flink.util.Collector;

import com.alibaba.blink.streaming.connectors.common.datatype.DataType;
import com.alibaba.blink.streaming.connectors.common.errcode.ConnectorErrors;
import com.alibaba.blink.streaming.connectors.common.exception.ErrorUtils;
import com.alibaba.blink.streaming.connectors.common.util.BaseRowGetter;
import com.alibaba.blink.table.cache.CacheStrategy;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import io.searchbox.client.JestResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.List;

public class ESRowFetcher extends AbstractESRowFetcher
        implements FlatMapFunction<BaseRow, BaseRow> {
    private static final long serialVersionUID = 6941593589582551911L;
    private static final Logger LOG = LoggerFactory.getLogger(ESRowFetcher.class);

    private final int fieldLength;
    private final DataType[] fieldTypes;
    private final String[] fieldNames;
    private final DataStructureConverters.DataStructureConverter[] converters;

    private final String esIndex;
    private final String esType;

    private final int maxRetryTimes;

    private final TypeInformation<?> keyType;
    private final TypeSerializer<?> keySerilizer;

    public ESRowFetcher(String sqlTableName,
                        RichTableSchema schema,
                        IndexKey index,
                        CacheStrategy cacheStrategy,
                        String esIndex,
                        String esType,
                        int maxRetryTimes,
                        String endpoint,
                        String accessId,
                        String accessKey,
                        boolean multiThread,
                        int readTimeout,
                        boolean compression,
                        boolean discovery) {
        super(sqlTableName, schema, index, cacheStrategy, endpoint, accessId, accessKey,
                multiThread, readTimeout, compression, discovery);
        List<Integer> indexCols = this.index.getDefinedColumns();
        int targetKeyIndex =  indexCols.get(0);
        assert targetKeyIndex != -1 : "Index column must be declared for ES Dim Source.";

        RowTypeInfo rowTypeInfo = schema.getResultTypeInfo();
        this.fieldLength = rowTypeInfo.getArity();
        this.fieldTypes = new DataType[fieldLength];
        this.converters = new DataStructureConverters.DataStructureConverter[fieldLength];
        for (int i = 0; i < fieldLength; i++) {
            fieldTypes[i] = DataType.getType(rowTypeInfo.getTypeAt(i));
            converters[i] = DataStructureConverters.getConverterForType(new TypeInfoWrappedDataType(rowTypeInfo.getTypeAt(i)));
        }

        this.fieldNames = rowTypeInfo.getFieldNames();

        this.esIndex = esIndex;
        this.esType = esType;
        keyType = rowTypeInfo.getTypeAt(targetKeyIndex);
        keySerilizer = keyType.createSerializer(new ExecutionConfig());

        this.maxRetryTimes = maxRetryTimes;
    }

    @Override
    public void flatMap(BaseRow baseRow, Collector<BaseRow> collector) {
        fetchQPS.markEvent();
        Object key = getCacheKey(baseRow);
        if (key == null) {
            LOG.warn("Join ES on empty key of row: {}", baseRow);
            emptyKeyCounter.incrementAndGet();
            return;
        }

        // we do not do uniqueness checking for index key,
        // but the implementation now only supports unique attribute.
        BaseRow cachedRow = one2oneCache.get(key);
        if (cachedRow != null) {
            cacheHitQPS.markEvent();
            if (cachedRow.getArity() > 0) {
                collector.collect(cachedRow);
            }
            return;
        }

        long start = System.nanoTime();
        JsonObject value = fetchResultAsJson(key);
        long end = System.nanoTime();
        fetchLatency.update(end - start);

        if (null == value) {
            // if no match result in ES, put empty row in cache
            if (cacheStrategy.isCacheEmpty()) {
                one2oneCache.put(key, NULL_BASEROW);
            }
        } else {
            BaseRow resultRow = toResultRow(value);

            one2oneCache.put(key, resultRow);
            collector.collect(resultRow);
            // make sure mark once even if there are many result
            fetchHitQPS.markEvent();
        }
    }

    private GenericRow toResultRow(JsonObject result) {
        GenericRow resultRow = new GenericRow(fieldLength);
        // parse to row
        for (int i = 0; i < fieldLength; i++) {
            resultRow.update(i, deserializeToInnerObject(result, fieldTypes[i], converters[i], fieldNames[i]));
        }
        return resultRow;
    }

    private JsonObject fetchResultAsJson(Object key) {
        // Get result using key as document ID.
        JestResult jestResult = this.esClient.get(esIndex, esType, key.toString(), this.maxRetryTimes);
        if (jestResult == null) {
            return null;
        }

        if (jestResult.isSucceeded()) {
            // TODO: support oneToMany mapping.
            try {
                return jestResult.getJsonObject().getAsJsonObject("_source");
            } catch (NullPointerException npe) {
                return null;
            }
        } else {
            Integer status = this.esClient.getStatus(jestResult);
            if (status != 400) {
                throw ErrorUtils.getException(String.format(
                        "status:[%d], error: %s",
                        status,
                        jestResult.getErrorMessage()));
            } else {
                throw ErrorUtils.getException(String.format(
                    "status:[%d], error: %s, config not ignoreParseError so throw this error",
                    status, jestResult.getErrorMessage()));
            }

        }
    }

    private static Object deserializeToInnerObject(
        JsonObject result,
        DataType type,
        DataStructureConverters.DataStructureConverter converter,
        String name) {
        JsonElement jsonElement = result.get(name);
        if (jsonElement == null) {
            return null;
        }
        switch (type) {
            case ByteArray:
                return jsonElement.getAsString().getBytes();
            case String:
                return converter.toInternal(BinaryString.fromString(jsonElement.getAsString()));
            case Byte:
                return jsonElement.getAsByte();
            case Short:
                return jsonElement.getAsShort();
            case Integer:
                return jsonElement.getAsInt();
            case Long:
                return jsonElement.getAsLong();
            case Float:
                return jsonElement.getAsFloat();
            case Double:
                return jsonElement.getAsDouble();
            case Boolean:
                return jsonElement.getAsBoolean();
            case Time:
                try {
                    return converter.toInternal(Time.valueOf(jsonElement.getAsString()));
                } catch (Exception e) {
                    LOG.error("Supported date format is not matched for parsed str {}",
                        jsonElement.getAsString());
                    throw new RuntimeException(e);
                }
            case Timestamp:
                try {
                    return converter.toInternal(Timestamp.valueOf(jsonElement.getAsString()));
                } catch (Exception e) {
                    LOG.error("Supported date format is not matched for parsed str {}",
                        jsonElement.getAsString());
                    throw new RuntimeException(e);
                }
            case Date:
                try {
                    return converter.toInternal(Date.valueOf(jsonElement.getAsString()));
                } catch (Exception e) {
                    LOG.error("Supported date format is not matched for parsed str {}",
                        jsonElement.getAsString());
                    throw new RuntimeException(e);
                }
            case BigDecimal:
                return converter.toInternal(jsonElement.getAsBigDecimal());
            // not support BigInteger
            default:
                throw new IllegalArgumentException(ConnectorErrors.INST.dataTypeError(String.valueOf(type)));
        }
    }

    private Object getCacheKey(BaseRow input) {
        return BaseRowGetter.safeGet(input, 0, keyType, keySerilizer);
    }
}
