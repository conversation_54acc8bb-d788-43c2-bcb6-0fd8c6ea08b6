package com.alibaba.blink.streaming.connectors.elasticsearch.sink;

import com.alibaba.blink.streaming.connectors.common.MetricUtils;
import com.alibaba.blink.streaming.connectors.common.output.Syncable;
import com.alibaba.blink.streaming.connectors.common.output.TupleRichOutputFormat;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.aliyun.wormhole.qanat.blink.util.QanatCustomUtil;
import com.taobao.metaq.client.MetaProducer;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Meter;
import org.apache.flink.types.Row;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.bulk.BackoffPolicy;
import org.elasticsearch.action.bulk.BulkProcessor;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.ByteSizeUnit;
import org.elasticsearch.common.unit.ByteSizeValue;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

public class QanatEsOutputFormat extends TupleRichOutputFormat implements Syncable {
    
	private static final Logger log = LoggerFactory.getLogger(QanatEsOutputFormat.class);
	
	private RowTypeInfo rowTypeInfo;

    private RestHighLevelClient client;
    private BulkProcessor bulkProcessor;
    private JSONObject mappingJson;
    private MetaProducer producer;
    
    private String index;
    private String type;
    private String host;
    private String port;
    private String mapping;
    private String mappingMode;
    private String username;
    private String password;
    private String pk;
    private Integer maxRetryTimeoutMs = 30000;
    private Integer connectTimeoutMs = 30000;
    private Integer socketTimeoutMs = 30000;
    private Integer connectionRequestTimeoutMs = 10000;
    private Long scheduleFlushInterval = 2500L;
    private Integer batchSize = 200;
    private Integer retries = 3;
    private Integer retryIntervalMs = 500;
    private Integer concurrentRequests = 0;
    private Long bulkSizeMb = 5L;

	private Meter outTps;
	private Meter outBps;
	private MetricUtils.LatencyGauge latencyGauge;

	public QanatEsOutputFormat(
			String host,
            String port,
			String username,
			String password,
			String index,
			String type,
			String mapping,
			String mappingMode,
            String pk,
            Integer maxRetryTimeoutMs,
	        Integer connectTimeoutMs,
	        Integer socketTimeoutMs,
	        Integer connectionRequestTimeoutMs,
	        Long scheduleFlushInterval,
	        Integer batchSize,
	        Integer retries,
	        Integer retryIntervalMs,
	        Integer concurrentRequests,
	        Long bulkSizeMb,
	        RowTypeInfo rowTypeInfo) {
		this.host = host;
        this.port = port;
		this.username = username;
		this.password = password;
		this.index = index;
		this.type = type;
		this.mapping = mapping;
		this.mappingMode = mappingMode;
        this.pk = pk;
        this.maxRetryTimeoutMs = maxRetryTimeoutMs;
        this.connectTimeoutMs = connectTimeoutMs;
        this.socketTimeoutMs = socketTimeoutMs;
        this.connectionRequestTimeoutMs = connectionRequestTimeoutMs;
        this.scheduleFlushInterval = scheduleFlushInterval;
        this.batchSize = batchSize;
        this.retries = retries;
        this.retryIntervalMs = retryIntervalMs;
        this.concurrentRequests = concurrentRequests;
        this.bulkSizeMb = bulkSizeMb;
        this.rowTypeInfo = rowTypeInfo;
	}

	@Override
	public void open(int taskNumber, int numTasks) throws IOException {
        super.open(taskNumber, numTasks);
        mappingJson = JSONObject.parseObject(mapping);
        HttpHost httpHost = new HttpHost(host, Integer.parseInt(port));
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));
        RestClientBuilder builder = RestClient.builder(httpHost);
        builder.setMaxRetryTimeoutMillis(maxRetryTimeoutMs);
        builder.setRequestConfigCallback(requestConfigBuilder -> {
            requestConfigBuilder.setConnectTimeout(connectTimeoutMs);
            requestConfigBuilder.setSocketTimeout(socketTimeoutMs);
            requestConfigBuilder.setConnectionRequestTimeout(connectionRequestTimeoutMs);
            return requestConfigBuilder;
        });
        builder.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
            @Override
            public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
                return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
            }
        }).build();
        client = new RestHighLevelClient(builder);

        
        outTps = MetricUtils.registerOutTps(getRuntimeContext());
        outBps = MetricUtils.registerOutBps(getRuntimeContext(), "elasticsearch");
        latencyGauge = MetricUtils.registerOutLatency(getRuntimeContext());
        
        BulkProcessor.Listener listener = new RetryBulkProcessorListener(client, retries, retryIntervalMs, outTps, outBps, latencyGauge, producer);
        BulkProcessor localBulkProcessor = BulkProcessor.builder(client::bulkAsync, listener)
                .setBulkActions(batchSize)
                .setBulkSize(new ByteSizeValue(bulkSizeMb, ByteSizeUnit.MB))
                .setConcurrentRequests(concurrentRequests)
                .setFlushInterval(TimeValue.timeValueMillis(scheduleFlushInterval))
                .setBackoffPolicy(BackoffPolicy.exponentialBackoff())
                .build();
        this.bulkProcessor = localBulkProcessor;
	}

	@Override
	public void close() throws IOException {
	    if (this.bulkProcessor != null) {
            this.bulkProcessor.close();
        }
	    if (client != null) {
            client.close();
        }
	    if (producer != null) {
	        producer.shutdown();
	    }
	}

	@Override
	public void writeAddRecord(Row row) throws IOException {
	    log.info("writeAddRecord start");
        try {
            Map<String, Object> inputData = getInputData(row, rowTypeInfo);
            log.info("inputData={}", JSON.toJSONString(inputData));
            onAddData(inputData);
            log.info("writeAddRecord finish");
        } catch(Exception e) {
            log.error("writeAddRecord failed", e);
        }
	}
    
    private Map<String, Object> getInputData(Row row, RowTypeInfo rowTypeInfo) {
        Map<String, Object> inputData = new HashMap<>();
        for (String fieldName : rowTypeInfo.getFieldNames()) {
            Object value = row.getField(rowTypeInfo.getFieldIndex(fieldName));
            if (value instanceof java.sql.Timestamp) {
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String str = df.format((java.sql.Timestamp)value);
                value = str;
            }
            if (value instanceof java.math.BigDecimal) {
                value = ((java.math.BigDecimal)value).doubleValue();
            }
            inputData.put(fieldName, value);
        }
        return inputData;
    }
    
    private void onAddData(Map<String, Object> inputData) throws IOException {
        try {
        	Map<String, Object> docData = null;
        	if (mappingJson == null) {
        		log.info("no mapping mode");
        		docData = new HashMap<>();
        		for (String key : inputData.keySet()) {
        			if (key.equalsIgnoreCase(pk)) {
        				docData.put(key, inputData.get(key));
        			} else if ("_script".equalsIgnoreCase(key)) {
        				continue;
        			} else {
        				docData.put(key, Arrays.asList(JSON.parseObject(inputData.get(key).toString(), Map.class)));
        			}
        		}
        	} else if ("flat".equals(mappingMode)) {
        		log.info("flat mapping mode");
        		docData = QanatCustomUtil.buildFlatMappingDocData(mappingJson, inputData);
        	} else {
        		docData = QanatCustomUtil.buildDocData(mappingJson, inputData);
        	}
            
            log.info("docData={}", JSON.toJSONString(docData));
            String src = null;
            if (inputData.get("_script") != null && StringUtils.isNotBlank(inputData.get("_script").toString())) {
                src = String.valueOf(inputData.get("_script")); 
            } else {
                StringBuffer source = new StringBuffer();
                for (String key : docData.keySet()) {
                    source.append("ctx._source.").append(key).append("=").append("params.").append(key).append(";");
                }
                src = source.toString();
            }
            UpdateRequest request = new UpdateRequest(index, type, docData.get(pk) + "");
            Script script = new Script(ScriptType.INLINE, "painless", src, docData);
            request.script(script);
            request.upsert(docData);
            if (inputData.get("_routing") != null && StringUtils.isNotBlank(inputData.get("_routing").toString())) {
                request.routing(String.valueOf(inputData.get("_routing")));
            }
            request.retryOnConflict(100);
            this.bulkProcessor.add(request);
        } catch(Exception e) {
            log.error("onAddData failed", e);
        }
    }

	@Override
	public String getName() {
		return String.format("ES: [%s]", index);
	}
	
	public static class Builder {
	    private String index;
	    private String type;
	    private String host;
	    private String port;
	    private String mapping;
	    private String mappingMode;
	    private String username;
	    private String password;
	    private String pk;
	    private Integer maxRetryTimeoutMs = 30000;
	    private Integer connectTimeoutMs = 30000;
	    private Integer socketTimeoutMs = 30000;
	    private Integer connectionRequestTimeoutMs = 10000;
	    private Long scheduleFlushInterval = 2500L;
	    private Integer batchSize = 200;
	    private Integer retries = 3;
	    private Integer retryIntervalMs = 500;
	    private Integer concurrentRequests = 0;
	    private Long bulkSizeMb = 5L;
	    private RowTypeInfo rowTypeInfo;
	    
	    public Builder() {
	    }
	    
	    public Builder(
            String host,
            String port,
            String username,
            String password,
            String index,
            String type,
            String mapping,
            String mappingMode,
            String pk,
            Integer maxRetryTimeoutMs,
            Integer connectTimeoutMs,
            Integer socketTimeoutMs,
            Integer connectionRequestTimeoutMs,
            Long scheduleFlushInterval,
            Integer batchSize,
            Integer retries,
            Integer retryIntervalMs,
            Integer concurrentRequests,
            Long bulkSizeMb) {
        this.host = host;
        this.port = port;
        this.username = username;
        this.password = password;
        this.index = index;
        this.type = type;
        this.mapping = mapping;
        this.mappingMode = mappingMode;
        this.pk = pk;
        this.maxRetryTimeoutMs = maxRetryTimeoutMs;
        this.connectTimeoutMs = connectTimeoutMs;
        this.socketTimeoutMs = socketTimeoutMs;
        this.connectionRequestTimeoutMs = connectionRequestTimeoutMs;
        this.scheduleFlushInterval = scheduleFlushInterval;
        this.batchSize = batchSize;
        this.retries = retries;
        this.retryIntervalMs = retryIntervalMs;
        this.concurrentRequests = concurrentRequests;
        this.bulkSizeMb = bulkSizeMb;}

        public String getIndex() {
            return index;
        }

        public Builder setIndex(String index) {
            this.index = index;
            return this;
        }

        public Builder setType(String type) {
            this.type = type;
            return this;
        }

        public Builder setHost(String host) {
            this.host = host;
            return this;
        }

        public Builder setPort(String port) {
            this.port = port;
            return this;
        }

        public Builder setMapping(String mapping) {
            this.mapping = mapping;
            return this;
        }

        public Builder setMappingMode(String mappingMode) {
            this.mappingMode = mappingMode;
            return this;
        }

        public Builder setUsername(String username) {
            this.username = username;
            return this;
        }

        public Builder setPassword(String password) {
            this.password = password;
            return this;
        }

        public Builder setPk(String pk) {
            this.pk = pk;
            return this;
        }

        public Builder setMaxRetryTimeoutMs(Integer maxRetryTimeoutMs) {
            this.maxRetryTimeoutMs = maxRetryTimeoutMs;
            return this;
        }

        public Builder setConnectTimeoutMs(Integer connectTimeoutMs) {
            this.connectTimeoutMs = connectTimeoutMs;
            return this;
        }

        public Builder setSocketTimeoutMs(Integer socketTimeoutMs) {
            this.socketTimeoutMs = socketTimeoutMs;
            return this;
        }

        public Builder setConnectionRequestTimeoutMs(Integer connectionRequestTimeoutMs) {
            this.connectionRequestTimeoutMs = connectionRequestTimeoutMs;
            return this;
        }

        public Builder setScheduleFlushInterval(Long scheduleFlushInterval) {
            this.scheduleFlushInterval = scheduleFlushInterval;
            return this;
        }

        public Builder setBatchSize(Integer batchSize) {
            this.batchSize = batchSize;
            return this;
        }

        public Builder setRetries(Integer retries) {
            this.retries = retries;
            return this;
        }

        public Builder setRetryIntervalMs(Integer retryIntervalMs) {
            this.retryIntervalMs = retryIntervalMs;
            return this;
        }

        public Builder setConcurrentRequests(Integer concurrentRequests) {
            this.concurrentRequests = concurrentRequests;
            return this;
        }

        public Builder setBulkSizeMb(Long bulkSizeMb) {
            this.bulkSizeMb = bulkSizeMb;
            return this;
        }

        public Builder setRowTypeInfo(RowTypeInfo rowTypeInfo) {
            this.rowTypeInfo = rowTypeInfo;
            return this;
        }

        QanatEsOutputFormat build() {
            QanatEsOutputFormat elasticSearchOutputFormat = new QanatEsOutputFormat(
                 host,
                 port,
                 username,
                 password,
                 index,
                 type,
                 mapping,
                 mappingMode,
                 pk,
                 maxRetryTimeoutMs,
                 connectTimeoutMs,
                 socketTimeoutMs,
                 connectionRequestTimeoutMs,
                 scheduleFlushInterval,
                 batchSize,
                 retries,
                 retryIntervalMs,
                 concurrentRequests,
                 bulkSizeMb,
                 rowTypeInfo);
            return elasticSearchOutputFormat;
        }
    }
    
    public static class RetryBulkProcessorListener implements BulkProcessor.Listener {
        
        private RestHighLevelClient clientRef;
        private Integer maxRetries;
        private Integer retryIntervalMs;
        private Meter outTps;
        private Meter outBps;
        private MetricUtils.LatencyGauge latencyGauge;
        private MetaProducer producer;
        
        public RetryBulkProcessorListener(RestHighLevelClient client, Integer maxRetries, Integer retryInterval, Meter outTps, Meter outBps, MetricUtils.LatencyGauge latencyGauge, MetaProducer producer) {
            this.clientRef = client;
            this.maxRetries = maxRetries;
            this.retryIntervalMs = retryInterval;
            this.outTps = outTps;
            this.outBps = outBps;
            this.latencyGauge = latencyGauge;
            this.producer = producer;
        }

        @Override
        public void beforeBulk(long executionId, BulkRequest request) {
            int numberOfActions = request.numberOfActions();
            log.info("beforeBulk [{}] with {} requests", executionId, numberOfActions);
        }

        @Override
        public void afterBulk(long executionId, BulkRequest request, BulkResponse response) {
            if (response.getItems() != null) {
                for (org.elasticsearch.action.bulk.BulkItemResponse item : response.getItems()) {
                	log.info("{} index={} id={} result={} failMsg={}", executionId, item.getIndex(), item.getResponse().getId(), item.getResponse().getResult(), item.getFailureMessage());             
                }
            }
            if (response.hasFailures()) {
                log.error("afterBulk [{}] executed with failures,response = {}", executionId, response.buildFailureMessage());
                afterBulk(executionId, request, new RuntimeException(response.buildFailureMessage()));
            } else {
                log.info("afterBulk [{}] completed in {} milliseconds", executionId, response.getTook().getMillis());
                latencyGauge.report(response.getTook() == null ? 0 : response.getTook().getMillis());
                outTps.markEvent(response.getItems() != null ? response.getItems().length : 0);
                // rough estimate each row 1000 bytes
                outBps.markEvent(response.getItems() != null ? response.getItems().length * 1000 : 0);
            }
        }

        @Override
        public void afterBulk(long executionId, BulkRequest request, Throwable failure) {
            log.error("afterBulk [{}] executed with exception, error = {}", executionId, failure.getMessage(), failure);
            try {
                int numberOfActions = request.numberOfActions();
                int retries = maxRetries;
                while (retries > 0) {
                    AtomicInteger success = new AtomicInteger(0);
                    log.info("retryBulk [{}] with {} requests for {}st time", executionId, numberOfActions, maxRetries - retries + 1);
                    this.clientRef.bulkAsync(request, new ActionListener<BulkResponse>() {
    
                        @Override
                        public void onResponse(BulkResponse response) {
                            if (response.hasFailures()) {
                                log.error("retryBulk [{}] executed with failures,response = {}", executionId, response.buildFailureMessage());
                                success.set(2);
                            } else {
                                log.info("retryBulk [{}] completed in {} milliseconds", executionId, response.getTook().getMillis());
                                latencyGauge.report(response.getTook().getMillis());
                                outTps.markEvent(response.getItems() != null ? response.getItems().length : 0);
                                // rough estimate each row 1000 bytes
                                outBps.markEvent(response.getItems() != null ? response.getItems().length * 1000 : 0);
                                success.set(1);//成功
                            }
                        }
    
                        @Override
                        public void onFailure(Exception e) {
                            log.error("retryBulk [{}] Failed to execute bulk {} ", executionId, e.getMessage(), e);
                            success.set(2);
                        }
                        
                    });
                    while (success.get() == 0) {
                        Thread.sleep(100);//间隔100ms
                    }
                    if (success.get() == 1) {
                        break;
                    } else if (success.get() == 2) {
                        retries --;
                        continue;
                    }
                    Thread.sleep(retryIntervalMs);
                }
            } catch (Exception e) {
                log.error("afterBulk [{}] Failed to execute bulk", executionId, failure);
            }
        }
    }

    @Override
    public void sync() throws IOException {
        // TODO Auto-generated method stub
        
    }

    @Override
    public void configure(Configuration arg0) {
        // TODO Auto-generated method stub
        
    }

    @Override
    public void writeDeleteRecord(Row arg0) throws IOException {
        // TODO Auto-generated method stub
        
    }
}