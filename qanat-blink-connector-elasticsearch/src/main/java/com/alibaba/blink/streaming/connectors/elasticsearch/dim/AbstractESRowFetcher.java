/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.streaming.connectors.elasticsearch.dim;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.ResultTypeQueryable;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.table.plan.schema.IndexKey;
import org.apache.flink.table.types.TypeConverters;

import com.alibaba.blink.streaming.connectors.common.DimJoinFetcher;
import com.alibaba.blink.streaming.connectors.elasticsearch.util.ESClient;
import com.alibaba.blink.table.cache.CacheStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AbstractESRowFetcher extends DimJoinFetcher implements ResultTypeQueryable<BaseRow> {
    private static final long serialVersionUID = 6941593589582551911L;
    private static Logger LOG = LoggerFactory.getLogger(AbstractESRowFetcher.class);

    protected ESClient esClient;
    private RichTableSchema schema;
    private String endpoint;
    private String accessId;
    private String accessKey;
    private boolean multiThread;
    private int readTimeout;
    private boolean compression;
    private boolean discovery;


    AbstractESRowFetcher(
            String sqlTableName,
            RichTableSchema schema,
            IndexKey index,
            CacheStrategy cacheStrategy,
            String endpoint,
            String accessId,
            String accessKey,
            boolean multiThread,
            int readTimeout,
            boolean compression,
            boolean discovery) {
        super(sqlTableName, index, cacheStrategy);
        this.schema = schema;
        this.endpoint = endpoint;
        this.accessId = accessId;
        this.accessKey = accessKey;
        this.multiThread = multiThread;
        this.readTimeout = readTimeout;
        this.compression = compression;
        this.discovery = discovery;
    }

    @Override
    public void openConnection(Configuration parameters) {
        this.esClient = new ESClient(
            endpoint,
            accessId,
            accessKey,
            multiThread,
            readTimeout,
            compression,
            discovery);
        esClient.openJestClient();
        LOG.info("ElasticSearch client opened successfully.");
    }

    @Override
    public void closeConnection() {
        if (esClient != null) {
            esClient.closeJestClient();
        }
        LOG.info("ElasticSearch client closed successfully.");
    }

    @Override
    public TypeInformation<BaseRow> getProducedType() {
        return TypeConverters.toBaseRowTypeInfo(schema.getResultType());
    }
}
