/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.streaming.connectors.elasticsearch.dim;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.api.functions.AsyncTableFunction;
import org.apache.flink.table.connector.DefinedDistribution;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.plan.schema.IndexKey;
import org.apache.flink.table.plan.stats.TableStats;
import org.apache.flink.table.sources.BatchTableSource;
import org.apache.flink.table.sources.LookupConfig;
import org.apache.flink.table.sources.LookupableTableSource;
import org.apache.flink.table.sources.StreamTableSource;
import org.apache.flink.table.types.DataType;

import com.alibaba.blink.streaming.connectors.common.LookupFunctionWrapper;
import com.alibaba.blink.streaming.connectors.common.source.SourceUtils;
import com.alibaba.blink.table.cache.CacheConfig;
import com.alibaba.blink.table.cache.CacheStrategy;

public class ElasticSearchTableSource
        implements StreamTableSource<BaseRow>,
        BatchTableSource<BaseRow>,
        LookupableTableSource<BaseRow>,
        DefinedDistribution {

    private final String sqlTableName;
    private final RichTableSchema schema;
    private final String endPoint;
    private final String accessId;
    private final String accesskey;
    private final String index;
    private final String type;

    private final int maxRetryTimes;

    private String partitionedField;
    private boolean shuffleEmptyKey = false;

    private final LookupConfig lookupConfig;
    private final CacheStrategy cacheStrategy;

    private final boolean multiThread;
    private final int readTimeout;
    private final boolean compression;
    private final boolean discovery;

    public ElasticSearchTableSource(String sqlTableName,
                                    RichTableSchema schema,
                                    String endPoint,
                                    String accessId,
                                    String accesskey,
                                    String index,
                                    String type,
                                    int maxRetryTimes,
                                    LookupConfig lookupConfig,
                                    CacheConfig cacheConfig,
                                    boolean multiThread,
                                    int readTimeout,
                                    boolean compression,
                                    boolean discovery) {
        this.sqlTableName = sqlTableName;
        this.schema = schema;
        this.endPoint = endPoint;
        this.accessId = accessId;
        this.accesskey = accesskey;
        this.index = index;
        this.type = type;
        this.maxRetryTimes = maxRetryTimes;
        this.lookupConfig = lookupConfig;
        this.cacheStrategy = cacheConfig.getCacheStrategy();
        this.multiThread = multiThread;
        this.readTimeout = readTimeout;
        this.compression = compression;
        this.discovery = discovery;
    }

    public void enablePartition() {
        this.partitionedField = "";
    }

    public void setShuffleEmptyKey(boolean shuffleEmptyKey) {
        this.shuffleEmptyKey = shuffleEmptyKey;
    }

    @Override
    public String[] getPartitionFields() {
        return this.partitionedField == null ? null : new String[] { this.partitionedField };
    }

    @Override
    public boolean shuffleEmptyKey() {
        return this.shuffleEmptyKey;
    }

    @Override
    public TableFunction<BaseRow> getLookupFunction(int[] lookupKeys) {
        IndexKey indexKey = SourceUtils.getSelectedIndexKey(schema, lookupKeys);
        if (cacheStrategy.isAllCache()) {
            throw new UnsupportedOperationException("ES do not support cache all strategy now.");
        } else {
            return new LookupFunctionWrapper(new ESRowFetcher(sqlTableName, schema, indexKey,
                cacheStrategy, index, type, maxRetryTimes, endPoint, accessId, accesskey,
                multiThread, readTimeout, compression, discovery));
        }
    }

    @Override
    public AsyncTableFunction<BaseRow> getAsyncLookupFunction(int[] lookupKeys) {
        throw new UnsupportedOperationException("ES do not support async lookup.");
    }

    @Override
    public LookupConfig getLookupConfig() {
        return lookupConfig;
    }

    @Override
    public DataStream<BaseRow> getDataStream(StreamExecutionEnvironment execEnv) {
        throw new UnsupportedOperationException("ElasticSearchTableSource does not support consume as DataStream.");
    }

    @Override
    public DataType getReturnType() {
        return SourceUtils.toDataType(schema);
    }

    @Override
    public TableSchema getTableSchema() {
        return SourceUtils.toTableSchema(schema);
    }

    @Override
    public String explainSource() {
        return "ElasticSearch: [" + sqlTableName + "]";
    }

    @Override
    public TableStats getTableStats() {
        return null;
    }

    @Override
    public boolean sortLocalPartition() {
        return false;
    }

    @Override
    public DataStream<BaseRow> getBoundedStream(StreamExecutionEnvironment streamEnv) {
        throw new UnsupportedOperationException("ElasticSearchTableSource does not support consume as BoundedStream.");
    }
}
