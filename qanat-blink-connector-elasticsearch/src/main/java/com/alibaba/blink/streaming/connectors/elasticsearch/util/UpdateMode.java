/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.streaming.connectors.elasticsearch.util;

/** Update mode for records with primary key. **/
public enum UpdateMode {
	FULL("full"), INC("inc");
	private String str;

	UpdateMode(String modeStr) {
		this.str = modeStr;
	}

	/** Decide which update mode is specified. **/
	public static UpdateMode apply(String modeStr) {
		if (modeStr == null) {
			return FULL;
		}
		switch (modeStr.toLowerCase()) {
			case "full": return FULL;
			case "inc": return INC;
			case "incremental": return INC;
			default:
				throw new RuntimeException("Unsupported update modes for ELASTICSEARCH, " +
					"available modes: full, inc");
		}
	}
}
