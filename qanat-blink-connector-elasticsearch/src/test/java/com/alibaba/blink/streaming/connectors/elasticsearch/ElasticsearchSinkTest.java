/*
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 * 	http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.alibaba.blink.streaming.connectors.elasticsearch;

import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.TableEnvironment;
import org.apache.flink.table.api.java.StreamTableEnvironment;
import org.apache.flink.table.factories.TableFactoryUtil;
import org.apache.flink.table.sinks.TableSink;
import org.apache.flink.table.types.DataTypes;
import org.apache.flink.table.types.InternalType;
import org.apache.flink.table.util.TableProperties;
import org.apache.flink.test.util.MultipleProgramsTestBase;
import org.apache.flink.util.InstantiationUtil;

import com.alibaba.blink.streaming.connectors.elasticsearch.testutils.SourceSinkDataTestKit;
import com.alibaba.blink.table.connectors.conf.BlinkOptions;
import com.alibaba.blink.table.util.BlinkTableFactoryUtil;
import org.elasticsearch.client.Client;
import org.elasticsearch.client.transport.TransportClient;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.ClassRule;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.rules.TemporaryFolder;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;

@Ignore
@RunWith(Parameterized.class)
public class ElasticsearchSinkTest extends MultipleProgramsTestBase {

	public ElasticsearchSinkTest(TestExecutionMode mode) {
		super(mode);
	}

	protected static final String CLUSTER_NAME = "test-cluster";

	protected static EmbeddedElasticsearchNodeEnvironment embeddedNodeEnv;

	@ClassRule
	public static TemporaryFolder tempFolder = new TemporaryFolder();

	@BeforeClass
	public static void prepare() throws Exception {

		System.out.println("-------------------------------------------------------------------------");
		System.out.println("    Starting embedded Elasticsearch node ");
		System.out.println("-------------------------------------------------------------------------");

		// dynamically load version-specific implementation of the Elasticsearch embedded node environment
		Class<?> clazz = Class.forName(
				"com.alibaba.blink.streaming.connectors.elasticsearch.EmbeddedElasticsearchNodeEnvironmentImpl");
		embeddedNodeEnv = (EmbeddedElasticsearchNodeEnvironment) InstantiationUtil.instantiate(clazz);

		embeddedNodeEnv.start(tempFolder.newFolder(), CLUSTER_NAME);

	}

	@AfterClass
	public static void shutdown() throws Exception {

		System.out.println("-------------------------------------------------------------------------");
		System.out.println("    Shutting down embedded Elasticsearch node ");
		System.out.println("-------------------------------------------------------------------------");

		embeddedNodeEnv.close();

	}

	/**
	 * Tests that the Elasticsearch sink works properly using a {@link TransportClient}.
	 */
	@Test
	public void runTransportClientTest() throws Exception {
		final String index = "transport-client-test-index";

		final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
		env.setParallelism(1);

		StreamTableEnvironment tEnv = TableEnvironment.getTableEnvironment(env);
		Table in = SourceSinkDataTestKit.getTestSourceTable(env, tEnv);

		RichTableSchema schema = new RichTableSchema(
				new String[]{"indexField", "len", "id", "content"},
				new InternalType[]{
						DataTypes.TIMESTAMP,
						DataTypes.INT,
						DataTypes.STRING,
						DataTypes.STRING});
		schema.setPrimaryKey("len");
		TableProperties properties = new TableProperties();
		properties.setString(BlinkOptions.ELASTICSEARCH.END_POINT, "http://127.0.0.1:9211");
		properties.setString(BlinkOptions.ELASTICSEARCH.INDEX, index);
		properties.setString(BlinkOptions.ELASTICSEARCH.TYPE, "flink-es-test-type");
		properties.setString(BlinkOptions.ELASTICSEARCH.ACCESS_ID, "");
		properties.setString(BlinkOptions.ELASTICSEARCH.ACCESSS_KEY, "");
		properties.putTableNameIntoProperties("elasticsearch");
		properties.putSchemaIntoProperties(schema);

		TableSink sink = TableFactoryUtil.findAndCreateTableSink(tEnv,
				BlinkTableFactoryUtil.getSimpleDescriptor("ELASTICSEARCH", properties), null);
		in.select("a,b,c,d").writeToSink(sink);

		env.execute("Elasticsearch Sink Test");

		// verify the results
		Client client = embeddedNodeEnv.getClient();
		SourceSinkDataTestKit.verifyProducedSinkData(client, index);

		client.close();
	}

	/**
	 * Tests that the Elasticsearch sink works properly using a {@link TransportClient}.
	 */
	@Test
	public void testDynamicIndex() throws Exception {
		final String index = "idx";

		final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
		env.setParallelism(1);

		StreamTableEnvironment tEnv = TableEnvironment.getTableEnvironment(env);
		Table in = SourceSinkDataTestKit.getTestSourceTable(env, tEnv);

		RichTableSchema schema = new RichTableSchema(
				new String[]{"indexField", "len", "id", "content"},
				new InternalType[]{
						DataTypes.TIMESTAMP,
						DataTypes.INT,
						DataTypes.STRING,
						DataTypes.STRING});
		schema.setPrimaryKey("len");
		TableProperties properties = new TableProperties();
		properties.setString(BlinkOptions.ELASTICSEARCH.END_POINT, "http://127.0.0.1:9211");
		properties.setString(BlinkOptions.ELASTICSEARCH.INDEX, index);
		properties.setString(BlinkOptions.ELASTICSEARCH.TYPE, "flink-es-test-type");
		properties.setString(BlinkOptions.ELASTICSEARCH.ACCESS_ID, "");
		properties.setString(BlinkOptions.ELASTICSEARCH.ACCESSS_KEY, "");
		properties.setBoolean(BlinkOptions.ELASTICSEARCH.OPTIONAL_DYNAMIC_INDEX, true);
		properties.setString(BlinkOptions.ELASTICSEARCH.OPTIONAL_INDEX_FIELD, "indexField");
		properties.setString(BlinkOptions.ELASTICSEARCH.OPTIONAL_INDEX_INTERVAL, "d");
		properties.putTableNameIntoProperties("elasticsearch");
		properties.putSchemaIntoProperties(schema);

		TableSink sink = TableFactoryUtil.findAndCreateTableSink(tEnv,
				BlinkTableFactoryUtil.getSimpleDescriptor("ELASTICSEARCH", properties));
		in.select("a,b,c,d").writeToSink(sink);

		env.execute("Elasticsearch Sink Test");

		// verify the results
		Client client = embeddedNodeEnv.getClient();
		SourceSinkDataTestKit.verifyProducedSinkData2(client, index);

		client.close();
	}

	/**
	 * Tests that the Elasticsearch sink works properly using a {@link TransportClient}.
	 *
	 * Notes: ignore cause the sequence is not stable.
	 */
	@Test
	public void testPrimaryKey() throws Exception {
		final String index = "idx";

		final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
		env.setParallelism(1);

		StreamTableEnvironment tEnv = TableEnvironment.getTableEnvironment(env);
		Table in = SourceSinkDataTestKit.getTestSourceTable2(env, tEnv);

		RichTableSchema schema = new RichTableSchema(
				new String[]{"indexField", "len", "id", "content"},
				new InternalType[]{
						DataTypes.TIMESTAMP,
						DataTypes.INT,
						DataTypes.STRING,
						DataTypes.STRING});
		schema.setPrimaryKey("len");
		TableProperties properties = new TableProperties();
		properties.setString(BlinkOptions.ELASTICSEARCH.END_POINT, "http://127.0.0.1:9211");
		properties.setString(BlinkOptions.ELASTICSEARCH.INDEX, index);
		properties.setString(BlinkOptions.ELASTICSEARCH.TYPE, "flink-es-test-type");
		properties.setString(BlinkOptions.ELASTICSEARCH.ACCESS_ID, "");
		properties.setString(BlinkOptions.ELASTICSEARCH.ACCESSS_KEY, "");
		properties.putTableNameIntoProperties("elasticsearch");
		properties.putSchemaIntoProperties(schema);

		TableSink sink = TableFactoryUtil.findAndCreateTableSink(tEnv,
				BlinkTableFactoryUtil.getSimpleDescriptor("ELASTICSEARCH", properties));
		in.select("a,b,c,d").writeToSink(sink);

		env.execute("Elasticsearch Sink Test");

		// verify the results
		Client client = embeddedNodeEnv.getClient();
		SourceSinkDataTestKit.verifyProducedSinkData3(client, index);

		client.close();
	}

	/**
	 * Tests that the Elasticsearch sink works properly using a {@link TransportClient}.
	 */
	@Test
	public void testIncrementalUpdate() throws Exception {
		final String index = "idx";

		final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
		env.setParallelism(1);

		StreamTableEnvironment tEnv = TableEnvironment.getTableEnvironment(env);
		Table in = SourceSinkDataTestKit.getTestSourceTable3(env, tEnv);

		RichTableSchema schema = new RichTableSchema(
				new String[]{"indexField", "len", "id", "content"},
				new InternalType[]{
						DataTypes.TIMESTAMP,
						DataTypes.INT,
						DataTypes.STRING,
						DataTypes.STRING});
		schema.setPrimaryKey("len");
		TableProperties properties = new TableProperties();
		properties.setString(BlinkOptions.ELASTICSEARCH.END_POINT, "http://127.0.0.1:9211");
		properties.setString(BlinkOptions.ELASTICSEARCH.INDEX, index);
		properties.setString(BlinkOptions.ELASTICSEARCH.TYPE, "flink-es-test-type");
		properties.setString(BlinkOptions.ELASTICSEARCH.ACCESS_ID, "");
		properties.setString(BlinkOptions.ELASTICSEARCH.ACCESSS_KEY, "");
		properties.setString(BlinkOptions.ELASTICSEARCH.OPTIONAL_UPDATE_MODE, "inc");
		properties.putTableNameIntoProperties("elasticsearch");
		properties.putSchemaIntoProperties(schema);

		TableSink sink = TableFactoryUtil.findAndCreateTableSink(tEnv,
				BlinkTableFactoryUtil.getSimpleDescriptor("ELASTICSEARCH", properties));
		in.select("a,b,c,d").writeToSink(sink);

		env.execute("Elasticsearch Sink Test");

		// verify the results
		Client client = embeddedNodeEnv.getClient();
		SourceSinkDataTestKit.verifyProducedSinkData4(client, index);

		client.close();
	}

}
