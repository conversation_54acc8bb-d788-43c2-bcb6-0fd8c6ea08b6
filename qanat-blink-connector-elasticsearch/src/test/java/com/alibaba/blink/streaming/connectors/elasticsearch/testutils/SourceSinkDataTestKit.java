/*
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 * 	http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.alibaba.blink.streaming.connectors.elasticsearch.testutils;

import org.apache.flink.api.java.tuple.Tuple4;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.Types;
import org.apache.flink.table.api.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.client.Client;
import org.junit.Assert;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * This class contains utilities and a pre-defined source function and
 * Elasticsearch Sink function used to simulate and verify data used in tests.
 */
public class SourceSinkDataTestKit {

	private static final int NUM_ELEMENTS = 3;

	private static final String ID_FIELD_NAME = "id";

	private static final String TYPE_NAME = "flink-es-test-type";

	private static final int FIXED_ID = 1;

	public static DataStream<Tuple4<Timestamp, Integer, String, String>> getSmall4TupleDataSet(StreamExecutionEnvironment env) {
		List<Tuple4<Timestamp, Integer, String, String>> data = new ArrayList<>();
		data.add(new Tuple4<>(Timestamp.valueOf("2018-11-18 00:00:00"), 0, "0", "hi"));
		data.add(new Tuple4<>(Timestamp.valueOf("2018-11-19 00:00:00"), 1, "1", "hello"));
		data.add(new Tuple4<>(Timestamp.valueOf("2018-11-20 00:00:00"), 2, "2", "world"));
		return env.fromCollection(data);
	}

	/** Data with same primary key. **/
	public static DataStream<Tuple4<Timestamp, Integer, String, String>> getSmall4TupleDataSet2(StreamExecutionEnvironment env) {
		List<Tuple4<Timestamp, Integer, String, String>> data = new ArrayList<>();
		data.add(new Tuple4<>(Timestamp.valueOf("2018-11-18 00:00:00"), FIXED_ID, "0", "hi"));
		data.add(new Tuple4<>(Timestamp.valueOf("2018-11-19 00:00:00"), FIXED_ID, "1", "hello"));
		data.add(new Tuple4<>(Timestamp.valueOf("2018-11-20 00:00:00"), FIXED_ID, "2", "world"));
		return env.fromCollection(data);
	}

	/** Data with same primary key and partial fields. **/
	public static DataStream<Row> getSmall4TupleDataSet3(StreamExecutionEnvironment env) {
		List<Row> data = new ArrayList<>();
		data.add(Row.of(Timestamp.valueOf("2018-11-18 00:00:00"), FIXED_ID, null, null));
		data.add(Row.of(null, FIXED_ID, "1", null));
		data.add(Row.of(null, FIXED_ID, null, "world"));
		return env.fromCollection(data,
				new RowTypeInfo(Types.SQL_TIMESTAMP(),
						Types.INT(),
						Types.STRING(),
						Types.STRING()));
	}

	/**
	 * A {@link SourceFunction} that generates the elements (id, "message #" + id) with id being 0 - 20.
	 */
	public static Table getTestSourceTable(StreamExecutionEnvironment env, StreamTableEnvironment tEnv) {
		DataStream<Tuple4<Timestamp, Integer, String, String>> ds = getSmall4TupleDataSet(env);
		Table in = tEnv.fromDataStream(ds, "a,b,c,d");
		return in;
	}

	/**
	 * A {@link SourceFunction} that generates the elements (id, "message #" + id) with id being 0 - 20.
	 */
	public static Table getTestSourceTable2(StreamExecutionEnvironment env, StreamTableEnvironment tEnv) {
		DataStream<Tuple4<Timestamp, Integer, String, String>> ds = getSmall4TupleDataSet2(env);
		Table in = tEnv.fromDataStream(ds, "a,b,c,d");
		return in;
	}

	public static Table getTestSourceTable3(StreamExecutionEnvironment env, StreamTableEnvironment tEnv) {
		DataStream<Row> ds = getSmall4TupleDataSet3(env);
		Table in = tEnv.fromDataStream(ds, "a,b,c,d");
		return in;
	}

	public static void verifyProducedSinkData(Client client, String index) {
		for (int i = 0; i < NUM_ELEMENTS; i++) {
			GetResponse response = client.get(new GetRequest(index, TYPE_NAME, Integer.toString(i))).actionGet();
			Assert.assertEquals(String.valueOf(i), response.getSource().get(ID_FIELD_NAME));
		}
	}

	public static void verifyProducedSinkData2(Client client, String index) {
		int startSuffix = 20181118;
		for (int i = 0; i < NUM_ELEMENTS; i++) {
			int suffix = startSuffix + i;
			GetResponse response = client.get(new GetRequest(index + suffix, TYPE_NAME, Integer.toString(i))).actionGet();
			Assert.assertEquals(String.valueOf(i), response.getSource().get(ID_FIELD_NAME));
		}
	}

	public static void verifyProducedSinkData3(Client client, String index) {
		for (int i = 0; i < NUM_ELEMENTS; i++) {
			GetResponse response = client.get(new GetRequest(index, TYPE_NAME, FIXED_ID + "")).actionGet();
			Assert.assertEquals("2", response.getSource().get(ID_FIELD_NAME));
		}
	}

	// Verify single data.
	public static void verifyProducedSinkData4(Client client, String index) {
		GetResponse response = client.get(new GetRequest(index, TYPE_NAME, FIXED_ID + "")).actionGet();
		Assert.assertEquals("1", response.getSource().get("id"));
		Assert.assertEquals("world", response.getSource().get("content"));
		Assert.assertEquals("2018-11-18 00:00:00", response.getSource().get("indexField"));
	}
}
