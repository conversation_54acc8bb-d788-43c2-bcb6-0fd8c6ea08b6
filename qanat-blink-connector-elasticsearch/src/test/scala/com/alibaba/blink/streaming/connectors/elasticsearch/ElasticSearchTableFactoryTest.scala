/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.streaming.connectors.elasticsearch

import org.apache.flink.api.scala._
import org.apache.flink.streaming.api.datastream
import org.apache.flink.streaming.api.datastream.DataStreamSink
import org.apache.flink.streaming.api.functions.sink.SinkFunction
import org.apache.flink.streaming.api.scala.{DataStream, StreamExecutionEnvironment}
import org.apache.flink.table.api.scala._
import org.apache.flink.table.api.{RichTableSchema, TableEnvironment}
import org.apache.flink.table.dataformat.BaseRow
import org.apache.flink.table.sinks.{AppendStreamTableSink, TableSink}
import org.apache.flink.table.sources.TableSource
import org.apache.flink.table.types.{DataType, DataTypes}
import org.apache.flink.table.util.TableProperties
import org.apache.flink.util.InstantiationUtil

import com.alibaba.blink.streaming.connectors.elasticsearch.ElasticSearchTableFactoryTest.results
import com.alibaba.blink.table.connectors.conf.BlinkOptions
import com.alibaba.blink.table.util.BlinkTableFactoryUtil
import org.elasticsearch.action.index.IndexRequest
import org.junit.rules.TemporaryFolder
import org.junit.{After, Before, Ignore, Rule, Test}

import java.sql.Timestamp

import scala.collection.JavaConversions._

@Ignore // We should open this when we solve the log4j error.
class ElasticSearchTableFactoryTest {

  protected val CLUSTER_NAME = "test-cluster"

  private[this] val testIndex = "test_index"

  private[this] val testType = "test_type"

  protected var embeddedNodeEnv: EmbeddedElasticsearchNodeEnvironment = _

  val _tempFolder = new TemporaryFolder
  @Rule def tempFolder: TemporaryFolder = _tempFolder

  private[this] def getIndexRequest(id: Int, age: Int, name: String, nTime: Long): IndexRequest = {

    new IndexRequest()
      .source("id", id,
        "age", age,
        "name", name,
        "nTime", new Timestamp(nTime))
      .id(id.toString)
      .index(testIndex)
      .`type`(testType)
  }

  @Before
  @throws[Exception]
  def prepare(): Unit = {
    System.out.println("-------------------------------------------------------------------------")
    System.out.println("    Starting embedded Elasticsearch node ")
    System.out.println("-------------------------------------------------------------------------")
    // dynamically load version-specific implementation of the Elasticsearch
    // embedded node environment
    val clazz = Class.forName("com.alibaba.blink.streaming.connectors.elasticsearch" +
      ".EmbeddedElasticsearchNodeEnvironmentImpl")
    embeddedNodeEnv = InstantiationUtil.instantiate(clazz)
      .asInstanceOf[EmbeddedElasticsearchNodeEnvironment]
    embeddedNodeEnv.start(tempFolder.newFolder, CLUSTER_NAME)
    // import records into the es cluster
    val client = embeddedNodeEnv.getClient
    client.index(getIndexRequest(1, 30, "Julian", 1551406044679L)).get()
    client.index(getIndexRequest(2, 20, "Fabian", 1551406045338L)).get()
    client.index(getIndexRequest(3, 25, "Danny", 1551406045343L)).get()
  }

  @After
  @throws[Exception]
  def shutdown(): Unit = {
    System.out.println("-------------------------------------------------------------------------")
    System.out.println("    Shutting down embedded Elasticsearch node ")
    System.out.println("-------------------------------------------------------------------------")
    embeddedNodeEnv.close()
  }

  @Test
  def testCreateBlinkStoreDimSource(): Unit = {
    val schema = new RichTableSchema(
      Array("id", "age", "name", "nTime"),
      Array(
        DataTypes.INT,
        DataTypes.INT,
        DataTypes.STRING,
        DataTypes.TIMESTAMP))

    schema.setPrimaryKey("id")

    val kvParams: TableProperties = new TableProperties()
    kvParams.setString("endPoint", "http://127.0.0.1:9211")
    kvParams.setString("accessId", "test-cluster")
    kvParams.setString("accessKey", "1234")
    kvParams.setString("index", testIndex)
    kvParams.setString("typeName", testType)
    kvParams.putTableNameIntoProperties("esTable")
    kvParams.putSchemaIntoProperties(schema)
    kvParams.setString(BlinkOptions.CONNECTOR_GROUP, BlinkOptions.CONNECTOR_GROUP_VALUE_BLINK)

    val data = List(
      (1, 2, "Hi"),
      (2, 5, "Hello"),
      (3, 6, "Hello!"))

    val env = StreamExecutionEnvironment.getExecutionEnvironment
    val tEnv = TableEnvironment.getTableEnvironment(env)
    val stream: DataStream[(Int, Int, String)] = env.fromCollection(data)
    val streamTable = stream.toTable(tEnv , 'id, 'len, 'content, 'proctime.proctime)
    tEnv.registerTable("source", streamTable)

    val blinkStore = BlinkTableFactoryUtil.findAndCreateTableSource(tEnv,
      BlinkTableFactoryUtil.getSimpleDescriptor("ELASTICSEARCH", kvParams))
    tEnv.registerTableSource("dim1", blinkStore.asInstanceOf[TableSource])

    tEnv.sqlQuery(
      "select t1.id, len, content, t2.age, t2.name, t2.nTime from source t1 join lateral " +
        "dim1 for system_time as of t1.proctime as t2 on t1.id = t2.id")
      .writeToSink(new TestCollectTableSink)
    tEnv.execute()

    // compare result
    val expect = List("1,2,Hi,30,Julian,2019-03-01 10:07:24.679",
      "2,5,Hello,20,Fabian,2019-03-01 10:07:25.338",
      "3,6,Hello!,25,Danny,2019-03-01 10:07:25.343").sorted

    assert(expect.length == results.size, s"Expect result " +
      s"size ${expect.length} while actually is ${results.size}.")
    expect zip results.toList.sorted foreach { p =>
      assert(p._1 == p._2, s"Expect result ${p._1} while actually is ${p._2}.")
    }
  }

}

class TestCollectTableSink extends AppendStreamTableSink[BaseRow] {
  override def getOutputType: DataType = DataTypes.createRowTypeV2(getFieldTypes, getFieldNames)

  override def getFieldNames: Array[String] = Array("id", "len", "content", "age", "name")

  override def emitDataStream(dataStream: datastream.DataStream[BaseRow]): DataStreamSink[_] = {
    dataStream.addSink(new SinkFunction[BaseRow] {
      override def invoke(value: BaseRow): Unit = {
        val strRow = Array(value.getInt(0),
          value.getInt(1),
          value.getString(2),
          value.getInt(3),
          value.getString(4),
          new Timestamp(value.getLong(5))).mkString(",")
        ElasticSearchTableFactoryTest.results.add(strRow)
      }
    }).name("TestCollectSink")
  }

  override def getFieldTypes: Array[DataType] = Array(DataTypes.INT,
    DataTypes.INT, DataTypes.STRING, DataTypes.INT, DataTypes.STRING)

  override def configure(
      fieldNames: Array[String],
      fieldTypes: Array[DataType]): TableSink[BaseRow] = {
    this
  }
}

object ElasticSearchTableFactoryTest {
  val results: java.util.ArrayList[String] = new java.util.ArrayList[String]()
}
