<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>blink-connectors</artifactId>
        <groupId>com.alibaba.blink</groupId>
        <version>blink-3.5-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.aliyun.wormhole</groupId>
    <artifactId>qanat-blink-connector-elasticsearch</artifactId>

    <packaging>jar</packaging>

    <!-- Allow users to pass custom connector versions -->

    <dependencies>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.67</version>
            </dependency>
        <!-- blink table -->
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>blink-table</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- core dependencies -->

        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-streaming-java_${scala.binary.version}</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>io.searchbox</groupId>
            <artifactId>jest-common</artifactId>
            <version>2.4.0</version>
        </dependency>
        <dependency>
            <groupId>io.searchbox</groupId>
            <artifactId>jest</artifactId>
            <version>2.4.0</version>
        </dependency>

        <!-- test dependencies -->

        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-test-utils_${scala.binary.version}</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-runtime_${scala.binary.version}</artifactId>
            <version>${project.version}</version>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-streaming-java_${scala.binary.version}</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
            <type>test-jar</type>
        </dependency>

        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-table_${scala.binary.version}</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.blink</groupId>
                    <artifactId>flink-shaded-hive</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-core</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
            <!-- Projects depending on this project,
            won't depend on flink-table. -->
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-java</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>jersey-client</artifactId>
                    <groupId>com.sun.jersey</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>flink-shaded-hadoop2</artifactId>
                    <groupId>com.alibaba.blink</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>flink-shaded-hadoop3</artifactId>
                    <groupId>com.alibaba.blink</groupId>
                </exclusion>
            </exclusions>
            <!-- Projects depending on this project,
            won't depend on flink-table. -->
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>blink-connector-common</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-streaming-java_${scala.binary.version}</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-table_${scala.binary.version}</artifactId>
            <version>${blink.version}</version>
            <classifier>tests</classifier>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.blink</groupId>
                    <artifactId>flink-shaded-hive</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-streaming-scala_${scala.binary.version}</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-metrics-dropwizard</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-hadoop-compatibility_${scala.binary.version}</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>jersey-core</artifactId>
                    <groupId>com.sun.jersey</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-test-utils-junit</artifactId>
            <version>${blink.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.blink</groupId>
            <artifactId>flink-test-utils_${scala.binary.version}</artifactId>
            <version>${blink.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- test dependencies -->
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_2.11</artifactId>
            <version>2.2.6</version>
            <scope>test</scope>
        </dependency>

        <!-- <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>5.1.2</version>
            
            FLINK-7133: Excluding all org.ow2.asm from elasticsearch dependencies because
            1. from the POV of client they are optional,
            2. the version configured by default at the time of writing this comment (1.7.1) depends on asm 4.1
               and when it is shaded into elasticsearch-base artifact it conflicts with newer shaded versions of asm
               resulting in errors at the runtime when application is executed locally, e.g. from IDE.
           
            <exclusions>
                <exclusion>
                    <groupId>org.ow2.asm</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency> -->
        <!-- Dependency for Elasticsearch 5.x Java Client -->
       <!--  <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>transport</artifactId>
            <version>5.1.2</version>
            <scope>test</scope>
        </dependency> -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>2.7</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>6.3.2</version>
        </dependency>
        
        <dependency>
            <groupId>com.taobao.metaq.final</groupId>
            <artifactId>metaq-client</artifactId>
            <version>4.3.2-INSTANCE-SNAPSHOT</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.0</version>
                <configuration>
                    <classpathDependencyExcludes>
                        <classpathDependencyExclude>log4j:log4j</classpathDependencyExclude>
                    </classpathDependencyExcludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <executions>
                    <execution>
                        <id>shade-flink</id>
                        <configuration>
                            <relocations combine.children="append">
                                <relocation>
                                    <pattern>com.taobao.metaq</pattern>
                                    <shadedPattern>com.alibaba.blink.shaded.metaq.com.taobao.metaq</shadedPattern>
                                </relocation>

                                <relocation>
                                    <pattern>com.alibaba.rocketmq.client</pattern>
                                    <shadedPattern>com.alibaba.blink.shaded.metaq.com.alibaba.rocketmq.client</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>com.taobao.diamond</pattern>
                                    <shadedPattern>com.alibaba.blink.shaded.metaq.com.taobao.diamond</shadedPattern>
                                </relocation>
                            </relocations>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- Scala Compiler -->
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <executions>
                    <!-- Run scala compiler in the process-resources phase, so that dependencies on
                        scala classes can be resolved later in the (Java) compile phase -->
                    <execution>
                        <id>scala-compile-first</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>add-source</goal>
                            <goal>compile</goal>
                        </goals>
                    </execution>

                    <!-- Run scala compiler in the process-test-resources phase, so that dependencies on
                         scala classes can be resolved later in the (Java) test-compile phase -->
                    <execution>
                        <id>scala-test-compile</id>
                        <phase>process-test-resources</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Scala Code Style, most of the configuration done via plugin management -->
            <plugin>
                <groupId>org.scalastyle</groupId>
                <artifactId>scalastyle-maven-plugin</artifactId>
                <configuration>
                    <configLocation>${project.basedir}/../tools/maven/scalastyle-config.xml
                    </configLocation>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>test-jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>
