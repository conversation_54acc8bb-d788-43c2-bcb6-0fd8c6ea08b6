/**
 * TDD测试运行器
 * 验证基于技术方案生成的代码是否符合预期
 */
public class TDDTestRunner {
    
    public static void main(String[] args) {
        System.out.println("🧪 开始TDD测试验证...\n");
        
        TDDTestRunner runner = new TDDTestRunner();
        int totalTests = 0;
        int passedTests = 0;
        
        // 测试FlinkSqlBuilder核心功能
        totalTests += runner.testFlinkSqlBuilder();
        passedTests += runner.getPassedCount();
        
        // 测试FlinkSyncTemplate功能
        totalTests += runner.testFlinkSyncTemplate();
        passedTests += runner.getPassedCount();
        
        // 输出测试结果
        System.out.println("\n📊 TDD测试结果总结:");
        System.out.println("总测试数: " + totalTests);
        System.out.println("通过测试: " + passedTests);
        System.out.println("失败测试: " + (totalTests - passedTests));
        System.out.println("通过率: " + String.format("%.1f%%", (double)passedTests / totalTests * 100));
        
        if (passedTests == totalTests) {
            System.out.println("\n✅ 所有TDD测试通过！代码实现符合技术方案要求。");
        } else {
            System.out.println("\n❌ 部分测试失败，需要修复实现。");
        }
    }
    
    private int passedCount = 0;
    
    private int getPassedCount() {
        int current = passedCount;
        passedCount = 0; // 重置计数器
        return current;
    }
    
    /**
     * 测试FlinkSqlBuilder功能
     */
    private int testFlinkSqlBuilder() {
        System.out.println("🔧 测试FlinkSqlBuilder...");
        
        FlinkSqlBuilderSimple builder = new FlinkSqlBuilderSimple();
        int testCount = 0;
        
        // 测试1: 数据类型映射
        testCount++;
        try {
            assert "STRING".equals(builder.mapToFlinkType("varchar"));
            assert "INT".equals(builder.mapToFlinkType("int"));
            assert "BIGINT".equals(builder.mapToFlinkType("bigint"));
            assert "TIMESTAMP(3)".equals(builder.mapToFlinkType("datetime"));
            assert "DECIMAL(18,2)".equals(builder.mapToFlinkType("decimal"));
            System.out.println("  ✅ 数据类型映射测试通过");
            passedCount++;
        } catch (AssertionError e) {
            System.out.println("  ❌ 数据类型映射测试失败: " + e.getMessage());
        }
        
        // 测试2: Flink语法适配
        testCount++;
        try {
            String originalSql = "SELECT DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') FROM test_table";
            String result = builder.adaptToFlinkSyntax(originalSql);
            assert result.contains("DATE_FORMAT(create_time, 'yyyy-MM-dd HH:mm:ss')");
            assert result.contains("PROCTIME() as proc_time");
            System.out.println("  ✅ Flink语法适配测试通过");
            passedCount++;
        } catch (AssertionError e) {
            System.out.println("  ❌ Flink语法适配测试失败: " + e.getMessage());
        }
        
        // 测试3: Hologres Binlog源表生成
        testCount++;
        try {
            String result = builder.generateHologresBinlogSource("test_table", "physical_table", "test_endpoint");
            assert result.contains("CREATE TABLE test_table");
            assert result.contains("'connector' = 'hologres'");
            assert result.contains("'binlog' = 'true'");
            assert result.contains("'tablename' = 'physical_table'");
            System.out.println("  ✅ Hologres Binlog源表生成测试通过");
            passedCount++;
        } catch (AssertionError e) {
            System.out.println("  ❌ Hologres Binlog源表生成测试失败: " + e.getMessage());
        }
        
        // 测试4: INSERT语句生成
        testCount++;
        try {
            String result = builder.generateFlinkInsertSql("target_table", "SELECT id, name FROM source_table");
            assert result.startsWith("INSERT INTO target_table");
            assert result.contains("SELECT id, name FROM source_table");
            System.out.println("  ✅ INSERT语句生成测试通过");
            passedCount++;
        } catch (AssertionError e) {
            System.out.println("  ❌ INSERT语句生成测试失败: " + e.getMessage());
        }
        
        // 测试5: SQL头部注释生成
        testCount++;
        try {
            String result = builder.generateSqlHeader("test_job", "test_model");
            assert result.contains("--Author: Datatube Flink Generator");
            assert result.contains("--JobName: test_job");
            assert result.contains("--ModelCode: test_model");
            assert result.contains("--Architecture: Flink + Hologres");
            System.out.println("  ✅ SQL头部注释生成测试通过");
            passedCount++;
        } catch (AssertionError e) {
            System.out.println("  ❌ SQL头部注释生成测试失败: " + e.getMessage());
        }
        
        // 测试6: UDF函数定义生成
        testCount++;
        try {
            String result = builder.generateUdfDefinitions();
            assert result.contains("qanatConcat");
            assert result.contains("qanatNvl");
            assert result.contains("qanatDateFormat");
            assert result.contains("CREATE TEMPORARY FUNCTION");
            System.out.println("  ✅ UDF函数定义生成测试通过");
            passedCount++;
        } catch (AssertionError e) {
            System.out.println("  ❌ UDF函数定义生成测试失败: " + e.getMessage());
        }
        
        return testCount;
    }
    
    /**
     * 测试FlinkSyncTemplate功能
     */
    private int testFlinkSyncTemplate() {
        System.out.println("\n📝 测试FlinkSyncTemplate...");
        
        FlinkSyncTemplateSimple template = new FlinkSyncTemplateSimple();
        int testCount = 0;
        
        // 测试1: 模板参数替换
        testCount++;
        try {
            String templateStr = "CREATE TABLE ${tableName} WITH ('connector' = '${connector}')";
            java.util.Map<String, Object> params = new java.util.HashMap<>();
            params.put("tableName", "test_table");
            params.put("connector", "hologres");
            
            String result = template.formatTemplate(templateStr, params);
            assert result.contains("CREATE TABLE test_table");
            assert result.contains("'connector' = 'hologres'");
            System.out.println("  ✅ 模板参数替换测试通过");
            passedCount++;
        } catch (AssertionError e) {
            System.out.println("  ❌ 模板参数替换测试失败: " + e.getMessage());
        }
        
        // 测试2: 未替换占位符清理
        testCount++;
        try {
            String templateStr = "CREATE TABLE ${tableName} WITH ('connector' = '${unknownParam}')";
            java.util.Map<String, Object> params = new java.util.HashMap<>();
            params.put("tableName", "test_table");
            
            String result = template.formatTemplate(templateStr, params);
            assert result.contains("CREATE TABLE test_table");
            assert !result.contains("${unknownParam}"); // 应该被清理掉
            System.out.println("  ✅ 未替换占位符清理测试通过");
            passedCount++;
        } catch (AssertionError e) {
            System.out.println("  ❌ 未替换占位符清理测试失败: " + e.getMessage());
        }
        
        // 测试3: 模板参数验证
        testCount++;
        try {
            String templateStr = "CREATE TABLE ${tableName} WITH ('connector' = '${connector}')";
            java.util.Map<String, Object> params = new java.util.HashMap<>();
            params.put("tableName", "test_table");
            params.put("connector", "hologres");
            
            boolean isValid = template.validateTemplateParams(templateStr, params);
            assert isValid;
            
            params.remove("connector");
            isValid = template.validateTemplateParams(templateStr, params);
            assert !isValid;
            
            System.out.println("  ✅ 模板参数验证测试通过");
            passedCount++;
        } catch (AssertionError e) {
            System.out.println("  ❌ 模板参数验证测试失败: " + e.getMessage());
        }
        
        return testCount;
    }
}

/**
 * 简化的FlinkSyncTemplate实现
 */
class FlinkSyncTemplateSimple {
    
    public String formatTemplate(String template, java.util.Map<String, Object> params) {
        String result = template;
        
        if (params != null) {
            for (java.util.Map.Entry<String, Object> entry : params.entrySet()) {
                String placeholder = "${" + entry.getKey() + "}";
                String value = entry.getValue() != null ? entry.getValue().toString() : "";
                result = result.replace(placeholder, value);
            }
        }
        
        // 清理未替换的占位符
        result = result.replaceAll("\\$\\{[^}]+\\}", "");
        
        return result;
    }
    
    public boolean validateTemplateParams(String template, java.util.Map<String, Object> params) {
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\$\\{([^}]+)\\}");
        java.util.regex.Matcher matcher = pattern.matcher(template);
        
        while (matcher.find()) {
            String paramName = matcher.group(1);
            if (params == null || !params.containsKey(paramName)) {
                return false;
            }
        }
        
        return true;
    }
}

/**
 * 简化的FlinkSqlBuilder实现（复用）
 */
class FlinkSqlBuilderSimple {
    
    public String mapToFlinkType(String originalType) {
        switch (originalType.toLowerCase()) {
            case "varchar":
            case "string":
                return "STRING";
            case "int":
            case "integer":
                return "INT";
            case "bigint":
            case "long":
                return "BIGINT";
            case "decimal":
                return "DECIMAL(18,2)";
            case "datetime":
            case "timestamp":
                return "TIMESTAMP(3)";
            case "date":
                return "DATE";
            case "boolean":
                return "BOOLEAN";
            case "double":
                return "DOUBLE";
            case "float":
                return "FLOAT";
            default:
                return "STRING";
        }
    }
    
    public String adaptToFlinkSyntax(String originalSql) {
        String adaptedSql = originalSql;
        
        // 处理时间函数
        adaptedSql = adaptedSql.replaceAll("DATE_FORMAT\\(([^,]+),'%Y-%m-%d %H:%i:%s'\\)", 
                                          "DATE_FORMAT($1, 'yyyy-MM-dd HH:mm:ss')");
        
        // 添加处理时间字段
        if (!adaptedSql.contains("PROCTIME()")) {
            adaptedSql = adaptedSql.replaceFirst("SELECT", "SELECT PROCTIME() as proc_time,");
        }
        
        return adaptedSql;
    }
    
    public String generateHologresBinlogSource(String tableName, String physicalTableName, String endpoint) {
        return String.format(
            "CREATE TABLE %s (\n" +
            "    id BIGINT,\n" +
            "    name STRING,\n" +
            "    PRIMARY KEY (id) NOT ENFORCED\n" +
            ") WITH (\n" +
            "    'connector' = 'hologres',\n" +
            "    'tablename' = '%s',\n" +
            "    'endpoint' = '%s',\n" +
            "    'binlog' = 'true',\n" +
            "    'binlog.scan.startup.mode' = 'latest'\n" +
            ");\n",
            tableName, physicalTableName, endpoint
        );
    }
    
    public String generateFlinkInsertSql(String tableName, String selectSql) {
        return String.format("INSERT INTO %s\n%s", tableName, selectSql);
    }
    
    public String generateSqlHeader(String jobName, String modelCode) {
        return String.format(
            "--SQL\n" +
            "--********************************************************************--\n" +
            "--Author: Datatube Flink Generator\n" +
            "--CreateTime: %s\n" +
            "--JobName: %s\n" +
            "--ModelCode: %s\n" +
            "--Architecture: Flink + Hologres\n" +
            "--********************************************************************--\n\n",
            new java.util.Date().toString(), jobName, modelCode
        );
    }
    
    public String generateUdfDefinitions() {
        return "-- UDF函数定义\n" +
               "CREATE TEMPORARY FUNCTION IF NOT EXISTS qanatConcat AS 'com.aliyun.wormhole.qanat.flink.udf.QanatConcatUdf';\n" +
               "CREATE TEMPORARY FUNCTION IF NOT EXISTS qanatNvl AS 'com.aliyun.wormhole.qanat.flink.udf.QanatNvlUdf';\n" +
               "CREATE TEMPORARY FUNCTION IF NOT EXISTS qanatDateFormat AS 'com.aliyun.wormhole.qanat.flink.udf.QanatDateFormatUdf';\n\n";
    }
}
