package com.aliyun.wormhole.qanat.blink.http.parser;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.blink.streaming.connectors.common.TableInfoAware;
import com.alibaba.blink.streaming.connectors.common.source.SourceCollector;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.table.dataformat.BaseRow;
import org.apache.flink.table.dataformat.BinaryString;
import org.apache.flink.table.dataformat.GenericRow;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.typeutils.BaseRowTypeInfo;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HttpSourceParser
    extends Object
    implements TableInfoAware, SourceCollector<String, BaseRow> {
    private static final long serialVersionUID = 1L;
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpSourceParser.class);

    private Map<String, String> config = new HashMap();
    private List<String> fieldList = new ArrayList();

    private RowTypeInfo rowTypeInfo;
    private String url;
    private long lastPrintTS = 0L;

    public TableInfoAware setUserParamsMap(Map<String, String> map) {
        if (null == map || map.isEmpty()) {
            throw new IllegalArgumentException("Failed to get map info.");
        }

        this.config.putAll(map);
        LOGGER.info("HttpSourceParser setUserParamsMap: {}", this.config);

        return this;
    }

    public TableInfoAware setPrimaryKeys(List<String> list) {
        return this;
    }

    public TableInfoAware setHeaderFields(List<String> list) {
        return this;
    }

    public TableInfoAware setRowTypeInfo(RowTypeInfo rowTypeInfo) {
        this.rowTypeInfo = rowTypeInfo;

        this.fieldList.addAll(Arrays.asList(rowTypeInfo.getFieldNames()));
        LOGGER.info("HttpSourceParser get fieldList: {}", this.fieldList);

        return this;
    }

    public TableInfoAware setRowTypeInfo(DataType dataType) {
        return this;
    }

    public void open(FunctionContext context) {
        LOGGER.info("HttpSourceParser begin to open.");
        this.url = (String)this.config.get("url".toLowerCase());
        if (StringUtils.isBlank(this.url)) {
            throw new IllegalArgumentException("failed to get url from parameter list.");
        }
        LOGGER.info("HttpSourceParser: get parameter url : {}", this.url);
    }

    public void parseAndCollect(String result, Collector<BaseRow> collector) {
        if (null == result) {
            return;
        }
        GenericRow genericRow = new GenericRow(this.fieldList.size());
        for (int idx = 0; idx < this.fieldList.size(); idx++) {
            genericRow.update(idx, BinaryString.fromString(result));
        }
        timingPrinter(genericRow);
        collector.collect(genericRow);
    }

    private void timingPrinter(GenericRow genericRow) {
        if (System.currentTimeMillis() - this.lastPrintTS > 100000L) {

            Map<String, String> debugInfo = new HashMap<String, String>();
            for (int idx = 0; idx < this.fieldList.size(); idx++) {
                debugInfo.put(this.fieldList.get(idx), genericRow.getBinaryString(idx).toString());
            }

            this.lastPrintTS = System.currentTimeMillis();
            LOGGER.info("HttpSourceParser get record: {}", debugInfo);
        }
    }

    public void close() {
        LOGGER.info("HttpSourceParser begin to close");
    }

    public TypeInformation<BaseRow> getProducedType() {
        return new BaseRowTypeInfo(GenericRow.class, this.rowTypeInfo
            .getFieldTypes(), this.rowTypeInfo.getFieldNames());
    }
}
