package com.aliyun.wormhole.qanat.blink.http;

import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.api.TableSourceParser;
import org.apache.flink.table.factories.BatchTableSourceFactory;
import org.apache.flink.table.factories.StreamTableSourceFactory;
import org.apache.flink.table.factories.TableSourceParserFactory;
import org.apache.flink.table.sources.BatchTableSource;
import org.apache.flink.table.sources.StreamTableSource;
import org.apache.flink.table.types.InternalType;
import org.apache.flink.table.typeutils.TypeUtils;
import org.apache.flink.table.util.TableProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.blink.streaming.connector.hbase.utils.ByteSerializer;
import com.alibaba.blink.streaming.connectors.common.source.SourceUtils;
import com.alibaba.blink.table.factories.BlinkTableFactory;
import com.alibaba.fastjson.JSON;

import com.aliyun.wormhole.qanat.blink.http.io.HttpInputConfig;
import com.aliyun.wormhole.qanat.blink.http.source.HttpTableSource;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_PROPERTY_VERSION;
import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_TYPE;

public class HttpTableFactory extends BlinkTableFactory implements
		TableSourceParserFactory,
		BatchTableSourceFactory<String>,
		StreamTableSourceFactory<String> {

    private final static Logger log = LoggerFactory.getLogger(HttpTableFactory.class);
    
	@Override
	public TableSourceParser createParser(
			String tableName, RichTableSchema tableSchema, TableProperties properties) {
	    log.info("tableSchema={}", JSON.toJSONString(tableSchema));
		TableSourceParser parser = SourceUtils.createParserFromDDL(tableSchema, properties, classLoader);
		if (parser == null) {
			throw new IllegalArgumentException("Failed to get source collector params info.");
		}
		return parser;
	}

	private HttpTableSource createSource(Map<String, String> props) {
		TableProperties tableProperties = new TableProperties();
		tableProperties.putProperties(props);
		RichTableSchema richTableSchema = tableProperties.readSchemaFromProperties(classLoader);

		for (InternalType t : richTableSchema.getColumnTypes()) {
			Class cls = TypeUtils.getExternalClassForType((t));
			if (!ByteSerializer.isSupportedType(cls)) {
				throw new IllegalArgumentException("Unsupported column type: " + cls);
			}
		}

		List<String> fieldList = new ArrayList<>();
		fieldList.addAll(Arrays.asList(richTableSchema.getColumnNames()));

		HttpInputConfig httpInputConfig = new HttpInputConfig();
		httpInputConfig.setUrl(tableProperties.getString(QanatHttpOptions.Source.URL));
		httpInputConfig.setContentType(tableProperties.getString(QanatHttpOptions.Source.ContentType));
		httpInputConfig.setMethod(tableProperties.getString(QanatHttpOptions.Source.Method));
		httpInputConfig.setData(tableProperties.getString(QanatHttpOptions.Source.Data));

		return new HttpTableSource(httpInputConfig);
	}

	@Override
	protected List<String> supportedSpecificProperties() {
		return QanatHttpOptions.Source.SUPPORTED_KEYS;
	}

	@Override
	protected Map<String, String> requiredContextSpecific() {
		Map<String, String> context = new HashMap<>();
		context.put(CONNECTOR_TYPE, "qanat_http"); //
		context.put(CONNECTOR_PROPERTY_VERSION, "1"); // backwards compatibility
		return context;
	}

	@Override
	public BatchTableSource<String> createBatchTableSource(Map<String, String> properties) {
		return createSource(properties);
	}

	@Override
	public StreamTableSource<String> createStreamTableSource(Map<String, String> properties) {
		return createSource(properties);
	}
}
