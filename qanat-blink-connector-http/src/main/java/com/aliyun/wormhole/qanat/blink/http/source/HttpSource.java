/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.blink.http.source;

import com.alibaba.blink.table.connectors.conf.BlinkOptions;

import com.aliyun.wormhole.qanat.blink.http.io.HttpInputConfig;

import com.alibaba.blink.streaming.connectors.common.reader.RecordReader;
import com.alibaba.blink.streaming.connectors.common.source.AbstractParallelSource;
import com.alibaba.blink.streaming.connectors.common.source.SourceUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.core.io.InputSplit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HttpSource extends AbstractParallelSource<String, Integer> {
	private static final Logger LOGGER = LoggerFactory.getLogger(HttpSource.class);
	
	private String url;
	private int totalPartition;
	private List<String> partitionList;
	private List<HttpInputConfig> httpReaderConfigList;

	@Override
	public void initOperator(Configuration config) throws IOException {
		config.setString(BlinkOptions.INNER_SAMPLE_TABLE_NAME, tableName);
	}

	@Override
	public RecordReader<String, Integer> createReader(Configuration configuration) throws IOException {
		LOGGER.info("HttpSource: begin to createReader.");
		return new HttpRecordReader();
	}


	@Override
	public InputSplit[] createInputSplitsForCurrentSubTask(int numberOfParallelSubTasks, int indexOfThisSubTask) throws IOException {
		LOGGER.info("HttpSource: begin to create input splits for current subTask");
		LOGGER.info("HttpSource: get numberOfParallelSubTasks: {}, indexOfThisSubTask: {}", numberOfParallelSubTasks, indexOfThisSubTask);
		LOGGER.info("HttpSource: get totalPartition: {}", totalPartition);

		List<Integer> subscribedGroups = SourceUtils.modAssign(this.toString(), numberOfParallelSubTasks, indexOfThisSubTask, totalPartition);
		LOGGER.info("HttpSource: get subscribedGroups: {}", subscribedGroups);

		HttpReaderConfig[] inputSplits = new HttpReaderConfig[subscribedGroups.size()];
		int i = 0;
		for (Integer partitionNumber : subscribedGroups) {
			HttpReaderConfig httpReaderConfig = new HttpReaderConfig(partitionNumber);
			httpReaderConfig.setHttpInputConfig(httpReaderConfigList.get(partitionNumber));
			LOGGER.info("HttpSource: partitionNumber: {}, add config to current parallel: {}", partitionNumber, httpReaderConfig);

			inputSplits[i++] = httpReaderConfig;
		}

		return inputSplits;
	}


	@Override
	public List<String> getPartitionList() {
		LOGGER.info("HttpSource: get getPartitionList: {}", partitionList);
		return partitionList;
	}


	@Override
	public String toString() {
		String name = String.format("%s:%s", getClass().getSimpleName(), url);
		LOGGER.info("HttpSource: get name: {}", name);
		return name;
	}


	public HttpSource(HttpInputConfig httpInputConfig) {

		// get depart by db name, cause db can be supported by only one connection at each time
		Map<String, HttpInputConfig> httpInputConfigMap = new HashMap<>();
		String url = httpInputConfig.getUrl();
		if (!httpInputConfigMap.containsKey(url)) {
			httpInputConfigMap.put(url, httpInputConfig);
		}
		partitionList = new ArrayList<>();
		httpReaderConfigList = new ArrayList<>();
		for (Map.Entry<String, HttpInputConfig> entry : httpInputConfigMap.entrySet()) {
			partitionList.add(entry.getKey());
			httpReaderConfigList.add(httpInputConfig);
		}
		totalPartition = httpReaderConfigList.size();
		LOGGER.info("HttpSource: get httpReaderConfigList: {}, totalPartition: {}", httpReaderConfigList, totalPartition);
		LOGGER.info("HttpSource: get partitionList: {}", partitionList);

		LOGGER.info("HttpSource: url: {}", httpInputConfig.getUrl());
	}

	public HttpSource setTableName(String tableName) {
		this.tableName = tableName;
		return this;
	}
}
