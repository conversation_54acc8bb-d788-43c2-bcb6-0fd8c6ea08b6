package com.aliyun.wormhole.qanat.blink.http.source;

import com.alibaba.blink.streaming.connectors.common.reader.Interruptible;
import com.alibaba.blink.streaming.connectors.common.reader.MonotonyIncreaseProgress;
import com.alibaba.blink.streaming.connectors.common.reader.RecordReader;

import com.aliyun.wormhole.qanat.blink.http.io.HttpInputConfig;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.core.io.InputSplit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

public class HttpRecordReader implements RecordReader<String, Integer>, Interruptible {

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpRecordReader.class);

    private HttpReaderConfig httpReaderConfig;
    private volatile boolean interrupted = false;
    private String resp;

    @Override
    public void open(InputSplit inputSplit, RuntimeContext context) throws IOException {
        httpReaderConfig = (HttpReaderConfig)inputSplit;

        HttpInputConfig config = httpReaderConfig.getHttpInputConfig();
        resp = doHttpInvoke(config);
    }

    private String doHttpInvoke(HttpInputConfig config) {
        String resp = null;
        Request request = null;
        if ("post".equalsIgnoreCase(config.getMethod())) {
            MediaType mediaType = MediaType.parse(config.getContentType());
            request = new Request.Builder()
                    .url(config.getUrl())
                    .post(RequestBody.create(mediaType, config.getData()))
                    .build();
        } else if ("get".equalsIgnoreCase(config.getMethod())) {
            request = new Request.Builder()
                    .url(config.getUrl())
                    .get()
                    .build();
        }
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
        		.connectTimeout(30, TimeUnit.SECONDS)
        		.readTimeout(30, TimeUnit.SECONDS)
        		.build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                resp = response.body().string();  
            }
        } catch (IOException e) {
            LOGGER.error("http request failed", e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return resp;
    }

    @Override
    public Integer getProgress() {
        return 0;
    }

    @Override
    public MonotonyIncreaseProgress getMonotonyIncreaseProgress() {
        MonotonyIncreaseProgress monotonyIncreaseProgress = new MonotonyIncreaseProgress();
        monotonyIncreaseProgress.add("", 1L);
        return monotonyIncreaseProgress;
    }

    @Override
    public void seek(Integer idx) throws IOException {
    }

    @Override
    public boolean next() throws IOException, InterruptedException {
        if (interrupted) {
            LOGGER.info("HttpSource: received interrupt command, finish this consumer.");
            return false;
        }
        return true;
    }

    @Override
    public String getMessage() {
        interrupt();
        return resp;
    }

    @Override
    public void close() {
    }

    @Override
    public long getWatermark() {
        return System.currentTimeMillis();
    }

    @Override
    public long getDelay() {
        return 0;
    }

    @Override
    public boolean isHeartBeat() {
        return false;
    }

    @Override
    public void interrupt() {
        interrupted = true;
    }

    @Override
    public long getFetchedDelay() {
        return 0;
    }
    
    public static void main(String[] args) {
        HttpRecordReader test = new HttpRecordReader();
        HttpInputConfig config = new HttpInputConfig();
        config.setContentType("application/json;charset=UTF-8");
        config.setData("{\"operation\":\"dashboardInfo:\\n  action: sop.cbmDashboard.getDashboardDataYaml\\n\\npermissionCodes:\\n  action: sop.cbmDashboard.permission\"}");
        config.setMethod("POST");
        config.setUrl("https://query.aliyun.com/yamlql");
        System.out.print(test.doHttpInvoke(config));
    }
}
