package com.aliyun.wormhole.qanat.blink.http;

import java.util.Arrays;
import java.util.List;

import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.configuration.ConfigOptions;

public class QanatHttpOptions {
    
    public static class Source {

        public static final ConfigOption<String> URL = ConfigOptions.key("url".toLowerCase()).noDefaultValue();
        public static final ConfigOption<String> ContentType = ConfigOptions.key("content_type".toLowerCase()).noDefaultValue();
        public static final ConfigOption<String> Method = ConfigOptions.key("method".toLowerCase()).defaultValue("POST");
        public static final ConfigOption<String> Data = ConfigOptions.key("data".toLowerCase()).noDefaultValue();
        
        public static final String PARAMS_HELP_MSG = String.format(
            "required params:%s\n",
            URL);
    
        public static final List<String> SUPPORTED_KEYS = Arrays.asList(
            URL.key(),
            ContentType.key(),
            Method.key(),
            Data.key());
        }
}