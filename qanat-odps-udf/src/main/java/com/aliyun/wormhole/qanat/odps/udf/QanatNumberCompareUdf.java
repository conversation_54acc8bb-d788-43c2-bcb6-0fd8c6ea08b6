package com.aliyun.wormhole.qanat.odps.udf;

import java.math.BigDecimal;

import com.aliyun.odps.udf.UDF;


public class QanatNumberCompareUdf extends UDF {
	
	public Boolean evaluate(String obj1, String obj2) {
        try {
            if (obj1 == null && isBlank(obj1)) {
                return true;
            }
            if (isBlank(obj2) && !isBlank(obj1)) {
                return false;
            }
            if (!isBlank(obj2) && isBlank(obj1)) {
                return false;
            }
            if (obj1.equals(obj2)) {
                return true;
            } else {
            	BigDecimal d1 = new BigDecimal(obj1);
            	BigDecimal d2 = new BigDecimal(obj2);
            	if (d1.compareTo(d2) == 0) {
            		return true;
            	} else {
            		return false;
            	}
            }
        } catch(Exception e) {
        	e.printStackTrace();
        }
        return false;
	}
	
	private boolean isBlank(String obj) {
		if (obj == null || "".equals(obj.trim())) {
			return true;
		} else {
			return false;
		}
	}
}