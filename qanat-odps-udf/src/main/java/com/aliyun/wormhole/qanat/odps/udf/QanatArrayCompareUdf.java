package com.aliyun.wormhole.qanat.odps.udf;

import java.util.Arrays;
import java.util.List;

import com.aliyun.odps.udf.UDF;


public class QanatArrayCompareUdf extends UDF {
	
	public Boolean evaluate(String array1, String array2) {
        try {
            if (isBlank(array2) && isBlank(array1)) {
                return true;
            }
            if (isBlank(array2) && !isBlank(array1)) {
                return false;
            }
            if (!isBlank(array2) && isBlank(array1)) {
                return false;
            }
            List<String> list1 = Arrays.asList(array1.split(","));
            List<String> list2 = Arrays.asList(array2.split(","));
            if (list1.containsAll(list2) && list2.containsAll(list1)) {
                return true;
            } else {
                return false;
            }
        } catch(Exception e) {
        	e.printStackTrace();
        }
        return false;
	}
	
	private boolean isBlank(String str) {
		if (str == null || "".equals(str.trim())) {
			return true;
		} else {
			return false;
		}
	}
}