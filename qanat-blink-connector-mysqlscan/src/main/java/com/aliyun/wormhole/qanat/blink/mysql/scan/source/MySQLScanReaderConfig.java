/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.blink.mysql.scan.source;

import org.apache.flink.core.io.InputSplit;

import java.io.Serializable;

import com.aliyun.wormhole.qanat.blink.mysql.scan.io.MySQLScanInputConfig;

public class MySQLScanReaderConfig implements InputSplit, Serializable {

	private final int partitionNumber;

	private MySQLScanInputConfig mySQLScanInputConfig;

	public MySQLScanReaderConfig(int partitionNumber) {
		this.partitionNumber = partitionNumber;
	}

	@Override
	public int getSplitNumber() {
		return partitionNumber;
	}

	public MySQLScanInputConfig getMySQLScanInputConfig() {
		return mySQLScanInputConfig;
	}

	public void setMySQLScanInputConfig(MySQLScanInputConfig mySQLScanInputConfig) {
		this.mySQLScanInputConfig = mySQLScanInputConfig;
	}

	@Override
	public String toString() {
		return "MySQLReaderConfig{" +
				"partitionNumber=" + partitionNumber +
				", mySQLInputConfig=" + mySQLScanInputConfig +
				'}';
	}
}
