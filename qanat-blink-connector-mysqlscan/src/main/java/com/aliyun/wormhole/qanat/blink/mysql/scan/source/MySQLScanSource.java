/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.blink.mysql.scan.source;

import com.alibaba.blink.table.connectors.conf.BlinkOptions;

import com.aliyun.wormhole.qanat.blink.mysql.scan.io.MySQLScanInputConfig;

import com.alibaba.blink.streaming.connectors.common.reader.RecordReader;
import com.alibaba.blink.streaming.connectors.common.source.AbstractParallelSource;
import com.alibaba.blink.streaming.connectors.common.source.SourceUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.core.io.InputSplit;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MySQLScanSource extends AbstractParallelSource<Row, Long> {
	private static final Logger LOGGER = LoggerFactory.getLogger(MySQLScanSource.class);
	private String logicTable;
	private int totalPartition;
	private List<String> partitionList;
	private List<MySQLScanInputConfig> mySQLReaderConfigList;
	private RichTableSchema richSchema;

	@Override
	public void initOperator(Configuration config) throws IOException {
		config.setString(BlinkOptions.INNER_SAMPLE_TABLE_NAME, tableName);
	}

	@Override
	public RecordReader<Row, Long> createReader(Configuration configuration) throws IOException {
		LOGGER.info("MySQLSource: begin to createReader.");
		return new MySQLScanRecordReader(richSchema);
	}


	@Override
	public InputSplit[] createInputSplitsForCurrentSubTask(int numberOfParallelSubTasks, int indexOfThisSubTask) throws IOException {
		LOGGER.info("MySQLSource: begin to create input splits for current subTask");
		LOGGER.info("MySQLSource: get numberOfParallelSubTasks: {}, indexOfThisSubTask: {}", numberOfParallelSubTasks, indexOfThisSubTask);
		LOGGER.info("MySQLSource: get totalPartition: {}", totalPartition);

		List<Integer> subscribedGroups = SourceUtils.modAssign(this.toString(), numberOfParallelSubTasks, indexOfThisSubTask, totalPartition);
		LOGGER.info("MySQLSource: get subscribedGroups: {}", subscribedGroups);

		MySQLScanReaderConfig[] inputSplits = new MySQLScanReaderConfig[subscribedGroups.size()];
		int i = 0;
		for (Integer partitionNumber : subscribedGroups) {
			MySQLScanReaderConfig mySQLScanReaderConfig = new MySQLScanReaderConfig(partitionNumber);
			mySQLScanReaderConfig.setMySQLScanInputConfig(mySQLReaderConfigList.get(partitionNumber));
			LOGGER.info("MySQLSource: partitionNumber: {}, add config to current parallel: {}", partitionNumber, mySQLScanReaderConfig);

			inputSplits[i++] = mySQLScanReaderConfig;
		}

		return inputSplits;
	}


	@Override
	public List<String> getPartitionList() {
		LOGGER.info("MySQLSource: get getPartitionList: {}", partitionList);
		return partitionList;
	}


	@Override
	public String toString() {
		String name = String.format("%s:%s", getClass().getSimpleName(), logicTable);
		LOGGER.info("MySQLSource: get name: {}", name);
		return name;
	}


	public MySQLScanSource(RichTableSchema richSchema, String logicTable, MySQLScanInputConfig mySQLScanInputConfig) {
	    this.richSchema = richSchema;

		// get depart by db name, cause db can be supported by only one connection at each time
		Map<String, MySQLScanInputConfig> mySQLDBMapConfig = new HashMap<>();
		String url = mySQLScanInputConfig.getUrl();
		if (!mySQLDBMapConfig.containsKey(url)) {
			mySQLDBMapConfig.put(url, mySQLScanInputConfig);
		}
		partitionList = new ArrayList<>();
		mySQLReaderConfigList = new ArrayList<>();
		for (Map.Entry<String, MySQLScanInputConfig> entry : mySQLDBMapConfig.entrySet()) {
			partitionList.add(entry.getKey());
			mySQLReaderConfigList.add(entry.getValue());
		}
		totalPartition = mySQLReaderConfigList.size();
		LOGGER.info("MySQLSource: get mySQLReaderConfigList: {}, totalPartition: {}", mySQLReaderConfigList, totalPartition);
		LOGGER.info("MySQLSource: get partitionList: {}", partitionList);

		this.logicTable = logicTable;
		LOGGER.info("MySQLSource: get logicTable: {}", logicTable);
	}

	public MySQLScanSource setTableName(String tableName) {
		this.tableName = tableName;
		return this;
	}
}
