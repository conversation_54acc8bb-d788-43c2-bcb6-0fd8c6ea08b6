package com.aliyun.wormhole.qanat.blink.mysql.scan.parser;

import java.util.List;
import java.util.Map;

import com.alibaba.blink.streaming.connectors.common.TableInfoAware;
import com.alibaba.blink.streaming.connectors.common.source.SourceCollector;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.types.DataType;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MySQLSourceParser
    extends Object
    implements TableInfoAware, SourceCollector<Row, Row> {
    private static final long serialVersionUID = 1L;
    private static final Logger LOGGER = LoggerFactory.getLogger(MySQLSourceParser.class);

    private RichTableSchema tableSchema;
    
    public MySQLSourceParser(RichTableSchema tableSchema) {
        this.tableSchema = tableSchema;
    }

    public TableInfoAware setUserParamsMap(Map<String, String> map) {
        return this;
    }

    public TableInfoAware setPrimaryKeys(List<String> list) {
        return this;
    }

    public TableInfoAware setHeaderFields(List<String> list) {
        return this;
    }

    public TableInfoAware setRowTypeInfo(RowTypeInfo rowTypeInfo) {
        return this;
    }

    public TableInfoAware setRowTypeInfo(DataType dataType) {
        return this;
    }

    public void open(FunctionContext context) {
        LOGGER.info("MySQLSourceParser begin to open.");
    }

    public void parseAndCollect(Row row, Collector<Row> collector) {
        collector.collect(row);
    }

    public void close() {
        LOGGER.info("MySQLSourceParser begin to close");
    }

    public TypeInformation<Row> getProducedType() {
        return tableSchema.getResultTypeInfo();
    }
}
