package com.aliyun.wormhole.qanat.blink.mysql.scan;

import java.util.Arrays;
import java.util.List;

import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.configuration.ConfigOptions;

public class QanatMySQLScanOptions {

    public static final ConfigOption<String> URL = ConfigOptions.key("url".toLowerCase()).noDefaultValue();
    public static final ConfigOption<String> USERNAME = ConfigOptions.key("userName".toLowerCase()).noDefaultValue();
    public static final ConfigOption<String> PASSWORD = ConfigOptions.key("password".toLowerCase()).noDefaultValue();
    public static final ConfigOption<String> TABLENAME = ConfigOptions.key("tableName".toLowerCase()).noDefaultValue();
    public static final ConfigOption<String> WHERE_CLAUSE = ConfigOptions.key("whereClause".toLowerCase()).noDefaultValue();
    public static final ConfigOption<Integer> BATCH_SIZE = ConfigOptions.key("batchSize".toLowerCase()).defaultValue(100);
    public static final ConfigOption<String> DBNAME = ConfigOptions.key("dbName".toLowerCase()).noDefaultValue();
    
    public static final String PARAMS_HELP_MSG = String.format(
        "required params:%s,%s,%s,%s\n",
        URL,
        USERNAME,
        PASSWORD,
        TABLENAME,
        DBNAME);

    public static final List<String> SUPPORTED_KEYS = Arrays.asList(
        URL.key(),
        USERNAME.key(),
        PASSWORD.key(),
        TABLENAME.key(),
        WHERE_CLAUSE.key(),
        BATCH_SIZE.key());
}