/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.blink.mysql.scan.source;

import com.alibaba.blink.streaming.connectors.common.datatype.DataType;
import com.alibaba.blink.streaming.connectors.common.reader.Interruptible;
import com.alibaba.blink.streaming.connectors.common.reader.MonotonyIncreaseProgress;
import com.alibaba.blink.streaming.connectors.common.reader.RecordReader;
import com.aliyun.wormhole.qanat.blink.mysql.scan.io.MySQLScanInputConfig;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.core.io.InputSplit;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

public class MySQLScanRecordReader implements RecordReader<Row, Long>, Interruptible {

    private static final Logger LOGGER = LoggerFactory.getLogger(MySQLScanRecordReader.class);

    private MySQLScanReaderConfig mySQLScanReaderConfig;
    private ResultSet resultSet;
    private Connection connection = null;
    private Statement statement = null;
    private long currentId = 0;
    private volatile boolean interrupted = false;
    private RichTableSchema richSchema;
    private Long startId = null;
    private Long endId = null;
    private int batchSize = 0;
    
    public MySQLScanRecordReader(RichTableSchema richSchema) {
        this.richSchema = richSchema;
    }

    @Override
    public void open(InputSplit inputSplit, RuntimeContext context) throws IOException {
        mySQLScanReaderConfig = (MySQLScanReaderConfig)inputSplit;
        batchSize = mySQLScanReaderConfig.getMySQLScanInputConfig().getBatchSize();
        createTableHandle(mySQLScanReaderConfig.getMySQLScanInputConfig());
    }

    @Override
    public Long getProgress() {
        return currentId;
    }

    @Override
    public MonotonyIncreaseProgress getMonotonyIncreaseProgress() {
        MonotonyIncreaseProgress monotonyIncreaseProgress = new MonotonyIncreaseProgress();
        monotonyIncreaseProgress.add("", currentId);
        return monotonyIncreaseProgress;
    }

    @Override
    public void seek(Long id) throws IOException {
        LOGGER.info("MySQLSource: get page from startId[{}]", id);

        if (id + 1 > endId) {
            LOGGER.error("MySQLSource: page startId[{}] isn't less than maxId[{}]", id, endId);
            throw new RuntimeException("page idx isn't less than maxPageCnt");
        } else {
            MySQLScanInputConfig mySQLScanInputConfig = mySQLScanReaderConfig.getMySQLScanInputConfig();
            LOGGER.info("MySQLSource: seek from startId[{}]", id);
            fetchData(mySQLScanInputConfig, richSchema.getPrimaryKeys().get(0), id);
        }
    }

    @Override
    public boolean next() throws IOException, InterruptedException {
        if (interrupted) {
            LOGGER.info("MySQLSource: received interrupt command, finish this consumer.");
            return false;
        }

        try {
            if (resultSet.next()) {
                return true;
            } else {
                return moveToNextPage();
            }
        } catch (SQLException e) {
            throw new RuntimeException("Failed to get next, err info: " + e.getMessage());
        }
    }

    @Override
    public Row getMessage() {
        if (resultSet == null) {
            LOGGER.warn("conversion error, resultSet object is null");
            return null;
        }
        int fieldLength = richSchema.getResultTypeInfo().getArity();
        Row row = new Row(fieldLength);
        try {
            DataType[] fieldTypes = new DataType[fieldLength];
            for (int i = 0; i < fieldLength; i++) {
                fieldTypes[i] = DataType.getType(richSchema.getResultTypeInfo().getTypeAt(i));
            }
            for (int i = 0; i < fieldLength; i++) {
                Object val = resultSetToObject(fieldTypes[i], this.resultSet, i + 1, "");
                row.setField(i, val);
            }
            LOGGER.info("resultSet={}, row={}", this.resultSet, row.getField(richSchema.getResultTypeInfo().getFieldIndex(richSchema.getPrimaryKeys().get(0))));
        } catch (Exception e) {
            LOGGER.warn("MySQLSource: get error info: {}", e.getMessage());
        }
        return row;
    }
    
    private Object resultSetToObject(DataType type, ResultSet resultSet, int fieldCount, String timeZone) {
        try {
            switch (type) {
                case ByteArray:
                    return new String(resultSet.getBytes(fieldCount));
                case String:
                    return resultSet.getString(fieldCount);
                case Byte:
                    return String.valueOf(resultSet.getByte(fieldCount));
                case Short:
                    return resultSet.getShort(fieldCount);
                case Integer:
                    Integer intVal = resultSet.getInt(fieldCount);
                	if (resultSet.wasNull()) {
                		intVal = null;
                	}
                    return intVal;
                case Long:
                case BigInteger:
                	Long val = resultSet.getLong(fieldCount);
                	if (resultSet.wasNull()) {
                		val = null;
                	}
                    return val;
                case Float:
                    return resultSet.getFloat(fieldCount);
                case Double:
                    Double doubleVal = resultSet.getDouble(fieldCount);
                	if (resultSet.wasNull()) {
                		doubleVal = null;
                	}
                    return doubleVal;
                case Boolean:
                    return Boolean.toString(resultSet.getBoolean(fieldCount));
                case Timestamp:
                    return resultSet.getTimestamp(fieldCount);
                case Time:
                    return resultSet.getTime(fieldCount);
                case Date:
                    return resultSet.getDate(fieldCount);
                case BigDecimal:
                    return resultSet.getBigDecimal(fieldCount);
                default:
                    return String.valueOf(resultSet.getString(fieldCount));
            }
        } catch (Exception e) {
            throw new RuntimeException(String.format("Parse recordSet failed, DataType=%s, fieldCount=%s, timeZone=%s",
                type.toString(), fieldCount, timeZone), e);
        }
    }

    @Override
    public void close() {
        LOGGER.info("MySQLSource: begin to close.");
        closeTableHandle();
    }

    @Override
    public long getWatermark() {
        return System.currentTimeMillis();
    }

    @Override
    public long getDelay() {
        return 0;
    }

    @Override
    public boolean isHeartBeat() {
        return false;
    }

    @Override
    public void interrupt() {
        LOGGER.info("MySQLSource: get interrupt info.");
        interrupted = true;
    }

    @Override
    public long getFetchedDelay() {
        return 0;
    }

    private boolean moveToNextPage() throws SQLException {
        while ((currentId+batchSize) <= endId) {
            MySQLScanInputConfig mySQLScanInputConfig = mySQLScanReaderConfig.getMySQLScanInputConfig();
            LOGGER.info("MySQLSource: currentId={}", currentId);

            currentId += batchSize;
            LOGGER.info("MySQLSource: begin to scan from id[{}]", currentId);
            fetchData(mySQLScanInputConfig, richSchema.getPrimaryKeys().get(0), currentId);

            if (resultSet.next()) {
                return true;
            }
        }

        LOGGER.info("MySQLSource: finish to process tabale scan, currentId[{}] maxId[{}]", currentId, endId);
        return false;
    }

    private void closeTableHandle() {
        LOGGER.info("MySQLSource: begin to close table");

        if (null != connection) {
            LOGGER.info("MySQLSource: closing connection ...");
            try {
                connection.close();
            } catch (SQLException e) {
                throw new RuntimeException("Failed to close connection, error info: " + e.getMessage());
            }
            connection = null;
        }

        if (null != statement) {
            LOGGER.info("MySQLSource: closing statement ...");
            try {
                statement.close();
            } catch (SQLException e) {
                throw new RuntimeException("Failed to close statement, error info: " + e.getMessage());
            }
            statement = null;
        }
    }

    private void createTableHandle(MySQLScanInputConfig mySQLScanInputConfig) {
        LOGGER.info("MySQLSource: begin to create table");

        if (null != connection || null != statement) {
            throw new RuntimeException("pls close last connection && statement at first.");
        }

        try {
            if (mySQLScanInputConfig.getUrl().startsWith("jdbc:mysql://")) {
                Class.forName("com.mysql.jdbc.Driver");
            } else if (mySQLScanInputConfig.getUrl().startsWith("jdbc:postgresql://")) {
                Class.forName("org.postgresql.Driver");
            }
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("MySQLSource: driver not found: " + e.getMessage());
        }
        try {
            connection = DriverManager.getConnection(mySQLScanInputConfig.getUrl(), mySQLScanInputConfig.getUserName(), mySQLScanInputConfig.getPassword());
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);

        	Map<String, Object> tableMetaMap = getMaxId(mySQLScanInputConfig);
        	startId = (Long)tableMetaMap.get("minId");
        	currentId = (Long)tableMetaMap.get("minId");
        	endId = (Long)tableMetaMap.get("maxId");
        	LOGGER.info("startId:{} endId:{} count:{}", startId, endId, tableMetaMap.get("count"));
            fetchData(mySQLScanInputConfig, richSchema.getPrimaryKeys().get(0), currentId);
            
        } catch (SQLException e) {
            throw new RuntimeException("error info: " + e.getMessage());
        }

        LOGGER.info("MySQLSource: create table successfully");
    }
    
    private void fetchData(MySQLScanInputConfig mySQLScanInputConfig, String pkField, long startId) {
        try {
            String inputSqlStr = genSQL(mySQLScanInputConfig, pkField, startId);
            resultSet = statement.executeQuery(inputSqlStr);
            LOGGER.info("start:{},resultSet1={}", startId, resultSet);
        } catch (SQLException e) {
            throw new RuntimeException("error info: " + e.getMessage());
        }
    }

    private Map<String, Object> getMaxId(MySQLScanInputConfig mySQLScanInputConfig) {
    	Map<String, Object> tableMetaMap = new HashMap<>();
        StringBuilder inputSql = new StringBuilder();
        inputSql.append("SELECT ")
				.append("MIN(" + richSchema.getPrimaryKeys().get(0) + ")")
				.append(",")
        		.append("MAX(" + richSchema.getPrimaryKeys().get(0) + ")")
				.append(",")
				.append("COUNT(*)")
		        .append(" FROM ")
		        .append(mySQLScanInputConfig.getTableName());

        if (null != mySQLScanInputConfig.getWhereClause() && !mySQLScanInputConfig.getWhereClause().isEmpty()) {
            inputSql.append(" WHERE ").append(mySQLScanInputConfig.getWhereClause());
        }
        try {
            ResultSet rs= statement.executeQuery(inputSql.toString());
            if(rs.next()) {
            	tableMetaMap.put("minId", rs.getLong(1));
            	tableMetaMap.put("maxId", rs.getLong(2));
            	tableMetaMap.put("count", rs.getLong(3));
            }
        } catch (SQLException e) {
            throw new RuntimeException("error info: " + e.getMessage());
        }
        return tableMetaMap;
    }

    private String genSQL(MySQLScanInputConfig mySQLScanInputConfig, String pkField, long startId) {
        StringBuilder inputSql = new StringBuilder();
        inputSql.append("SELECT ");
        for (int i = 0; i < mySQLScanInputConfig.getFields().size(); ++i) {
            inputSql.append(mySQLScanInputConfig.getFields().get(i));

            if (mySQLScanInputConfig.getFields().size() != i + 1) {
                inputSql.append(", ");
            } else {
                inputSql.append(" ");
            }
        }
        inputSql.append(" FROM ").append(mySQLScanInputConfig.getTableName());
        inputSql.append(" WHERE ").append(pkField).append(">=").append(startId).append(" AND ").append(pkField).append("<").append((startId+batchSize) > endId ? (endId+1) : (startId+batchSize));

        if (null != mySQLScanInputConfig.getWhereClause() && !mySQLScanInputConfig.getWhereClause().isEmpty()) {
            inputSql.append(" AND ").append(mySQLScanInputConfig.getWhereClause());
        }
        LOGGER.info("dbName={} sql={}", mySQLScanInputConfig.getDbName(), inputSql.toString());
        return inputSql.toString();
    }
}
