/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.aliyun.wormhole.qanat.blink.mysql.scan.source;

import com.alibaba.blink.streaming.connectors.common.source.SourceFunctionTableSource;

import com.aliyun.wormhole.qanat.blink.mysql.scan.io.MySQLScanInputConfig;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.types.Row;

public class MySQLScanTableSource extends SourceFunctionTableSource<Row> {

	private String tableName;
	private MySQLScanInputConfig mySQLScanInputConfig;
	private RichTableSchema richSchema;

	public MySQLScanTableSource(RichTableSchema richSchema, String tableName, MySQLScanInputConfig mySQLScanInputConfig) {
		this.tableName = tableName;
		this.mySQLScanInputConfig = mySQLScanInputConfig;
		this.richSchema = richSchema;
	}

	@Override
	public SourceFunction getSourceFunction() {
		MySQLScanSource source = new MySQLScanSource(richSchema, tableName, mySQLScanInputConfig);
		source.setTableName(tableName);
		return source;
	}

	@Override
	public String explainSource() {
		return String.format("MySQLScanTableSource-%s", tableName);
	}
}
