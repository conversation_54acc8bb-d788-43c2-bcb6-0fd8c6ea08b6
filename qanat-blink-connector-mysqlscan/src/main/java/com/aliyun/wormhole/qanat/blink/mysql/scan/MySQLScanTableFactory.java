package com.aliyun.wormhole.qanat.blink.mysql.scan;

import org.apache.flink.table.api.RichTableSchema;
import org.apache.flink.table.api.TableSourceParser;
import org.apache.flink.table.factories.BatchTableSourceFactory;
import org.apache.flink.table.factories.StreamTableSourceFactory;
import org.apache.flink.table.factories.TableSourceParserFactory;
import org.apache.flink.table.sources.BatchTableSource;
import org.apache.flink.table.sources.StreamTableSource;
import org.apache.flink.table.types.InternalType;
import org.apache.flink.table.typeutils.TypeUtils;
import org.apache.flink.table.util.TableProperties;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.blink.streaming.connector.hbase.utils.ByteSerializer;
import com.alibaba.blink.streaming.connectors.common.source.SourceCollectorTableFunction;
import com.alibaba.blink.streaming.connectors.common.source.SourceUtils;
import com.alibaba.blink.streaming.connectors.common.util.BlinkStringUtil;
import com.alibaba.blink.table.connectors.conf.BlinkOptions;
import com.alibaba.blink.table.factories.BlinkTableFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.wormhole.qanat.blink.mysql.scan.io.MySQLScanInputConfig;
import com.aliyun.wormhole.qanat.blink.mysql.scan.parser.MySQLSourceParser;
import com.aliyun.wormhole.qanat.blink.mysql.scan.source.MySQLScanTableSource;
import com.aliyun.wormhole.qanat.datasource.QanatDataSourceUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_PROPERTY_VERSION;
import static org.apache.flink.table.descriptors.ConnectorDescriptorValidator.CONNECTOR_TYPE;

public class MySQLScanTableFactory extends BlinkTableFactory implements
		TableSourceParserFactory,
		BatchTableSourceFactory<Row>,
		StreamTableSourceFactory<Row> {

    private final static Logger log = LoggerFactory.getLogger(MySQLScanTableFactory.class);
    
	@Override
	public TableSourceParser createParser(
			String tableName, RichTableSchema tableSchema, TableProperties properties) {
	    log.info("tableSchema={}", JSON.toJSONString(tableSchema));
		TableSourceParser tableSourceParser = SourceUtils.createParserFromDDL(tableSchema, properties, classLoader);
		if (tableSourceParser == null) {
		    MySQLSourceParser parser = new MySQLSourceParser(tableSchema);
		    tableSourceParser = new TableSourceParser(
                new SourceCollectorTableFunction<>(parser),
                Collections.singletonList("f0"));
		}
		return tableSourceParser;
	}

	private MySQLScanTableSource createSource(Map<String, String> props) {
		TableProperties tableProperties = new TableProperties();
		tableProperties.putProperties(props);
		RichTableSchema richTableSchema = tableProperties.readSchemaFromProperties(classLoader);

		for (InternalType t : richTableSchema.getColumnTypes()) {
			Class<?> cls = TypeUtils.getExternalClassForType((t));
			if (!ByteSerializer.isSupportedType(cls)) {
				throw new IllegalArgumentException("Unsupported column type: " + cls);
			}
		}

		List<String> fieldList = new ArrayList<>();
		fieldList.addAll(Arrays.asList(richTableSchema.getColumnNames()));
		MySQLScanInputConfig mySQLScanInputConfig = new MySQLScanInputConfig();

		String username = tableProperties.getString(QanatMySQLScanOptions.USERNAME);
        String password = tableProperties.getString(QanatMySQLScanOptions.PASSWORD);
        String tableName = tableProperties.getString(QanatMySQLScanOptions.TABLENAME);
        String url = tableProperties.getString(QanatMySQLScanOptions.URL);
        Integer batchSize = tableProperties.getInteger(QanatMySQLScanOptions.BATCH_SIZE);
        String whereClause = tableProperties.getString(QanatMySQLScanOptions.WHERE_CLAUSE);
        String dbName = tableProperties.getString(QanatMySQLScanOptions.DBNAME);
        
        if (BlinkStringUtil.isNotEmpty(dbName)) {
            String dbMetaStr = QanatDataSourceUtils.getDbMeta(dbName, Thread.currentThread().getContextClassLoader());
            JSONObject dbMetaJson = JSON.parseObject(dbMetaStr);
            url = dbMetaJson.getString("jdbcUrl");
            username = dbMetaJson.getString("username");
            password = dbMetaJson.getString("password");
		}

        mySQLScanInputConfig.setFields(fieldList);
        mySQLScanInputConfig.setUrl(url);
        mySQLScanInputConfig.setUserName(username);
        mySQLScanInputConfig.setPassword(password);
        mySQLScanInputConfig.setTableName(tableName);
        mySQLScanInputConfig.setBatchSize(batchSize == null ? 100000 : batchSize);
        mySQLScanInputConfig.setWhereClause(whereClause);
        mySQLScanInputConfig.setDbName(dbName);

		return new MySQLScanTableSource(richTableSchema, tableName, mySQLScanInputConfig);
	}

	@Override
	protected List<String> supportedSpecificProperties() {
		return BlinkOptions.MYSQLSCAN.SUPPORTED_KEYS;
	}

	@Override
	protected Map<String, String> requiredContextSpecific() {
		Map<String, String> context = new HashMap<>();
		context.put(CONNECTOR_TYPE, "qanat_scan"); // MYSQLSCAN
		context.put(CONNECTOR_PROPERTY_VERSION, "1"); // backwards compatibility
		return context;
	}

	@Override
	public BatchTableSource<Row> createBatchTableSource(Map<String, String> properties) {
		return createSource(properties);
	}

	@Override
	public StreamTableSource<Row> createStreamTableSource(Map<String, String> properties) {
		return createSource(properties);
	}
}
