<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.wormhole.qanat.dal.mapper.DsFieldInfoMapper" >
  <resultMap id="BaseResultMap" type="com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    <result column="field_name" property="fieldName" jdbcType="VARCHAR" />
    <result column="field_unique_name" property="fieldUniqueName" jdbcType="VARCHAR" />
    <result column="ds_name" property="dsName" jdbcType="VARCHAR" />
    <result column="ds_unique_name" property="dsUniqueName" jdbcType="VARCHAR" />
    <result column="db_name" property="dbName" jdbcType="VARCHAR" />
    <result column="field_type" property="fieldType" jdbcType="VARCHAR" />
    <result column="field_desc" property="fieldDesc" jdbcType="VARCHAR" />
    <result column="is_pk" property="isPk" jdbcType="TINYINT" />
    <result column="default_value" property="defaultValue" jdbcType="VARCHAR" />
    <result column="is_not_null" property="isNotNull" jdbcType="TINYINT" />
    <result column="is_deleted" property="isDeleted" jdbcType="BIGINT" />
    <result column="create_empid" property="createEmpid" jdbcType="VARCHAR" />
    <result column="modify_empid" property="modifyEmpid" jdbcType="VARCHAR" />
    <result column="sys_type" property="sysType" jdbcType="VARCHAR" />
    <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
    <result column="is_fk" property="isFk" jdbcType="INTEGER" />
    <result column="fk_object_type" property="fkObjectType" jdbcType="VARCHAR" />
    <result column="ext_info" property="extInfo" jdbcType="VARCHAR" />
    <result column="data_type" property="dataType" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gmt_create, gmt_modified, field_name, field_unique_name, ds_name, ds_unique_name, 
    db_name, field_type, field_desc, is_pk, default_value, is_not_null, is_deleted, create_empid, 
    modify_empid, sys_type, tenant_id, is_fk, fk_object_type, ext_info, data_type
  </sql>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ds_field_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleAndPage" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ds_field_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample" >
    delete from ds_field_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <select id="countByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample" resultType="java.lang.Integer" >
    select count(*) from ds_field_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update ds_field_info
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null" >
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.fieldName != null" >
        field_name = #{record.fieldName,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldUniqueName != null" >
        field_unique_name = #{record.fieldUniqueName,jdbcType=VARCHAR},
      </if>
      <if test="record.dsName != null" >
        ds_name = #{record.dsName,jdbcType=VARCHAR},
      </if>
      <if test="record.dsUniqueName != null" >
        ds_unique_name = #{record.dsUniqueName,jdbcType=VARCHAR},
      </if>
      <if test="record.dbName != null" >
        db_name = #{record.dbName,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldType != null" >
        field_type = #{record.fieldType,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldDesc != null" >
        field_desc = #{record.fieldDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.isPk != null" >
        is_pk = #{record.isPk,jdbcType=TINYINT},
      </if>
      <if test="record.defaultValue != null" >
        default_value = #{record.defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="record.isNotNull != null" >
        is_not_null = #{record.isNotNull,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null" >
        is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      </if>
      <if test="record.createEmpid != null" >
        create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.modifyEmpid != null" >
        modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.sysType != null" >
        sys_type = #{record.sysType,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null" >
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.isFk != null" >
        is_fk = #{record.isFk,jdbcType=INTEGER},
      </if>
      <if test="record.fkObjectType != null" >
        fk_object_type = #{record.fkObjectType,jdbcType=VARCHAR},
      </if>
      <if test="record.extInfo != null" >
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.dataType != null" >
        data_type = #{record.dataType,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update ds_field_info
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      field_name = #{record.fieldName,jdbcType=VARCHAR},
      field_unique_name = #{record.fieldUniqueName,jdbcType=VARCHAR},
      ds_name = #{record.dsName,jdbcType=VARCHAR},
      ds_unique_name = #{record.dsUniqueName,jdbcType=VARCHAR},
      db_name = #{record.dbName,jdbcType=VARCHAR},
      field_type = #{record.fieldType,jdbcType=VARCHAR},
      field_desc = #{record.fieldDesc,jdbcType=VARCHAR},
      is_pk = #{record.isPk,jdbcType=TINYINT},
      default_value = #{record.defaultValue,jdbcType=VARCHAR},
      is_not_null = #{record.isNotNull,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      sys_type = #{record.sysType,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      is_fk = #{record.isFk,jdbcType=INTEGER},
      fk_object_type = #{record.fkObjectType,jdbcType=VARCHAR},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      data_type = #{record.dataType,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ds_field_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ds_field_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo" useGeneratedKeys="true" keyProperty="id" >
    insert into ds_field_info (gmt_create, gmt_modified, field_name, 
      field_unique_name, ds_name, ds_unique_name, 
      db_name, field_type, field_desc, 
      is_pk, default_value, is_not_null, 
      is_deleted, create_empid, modify_empid, 
      sys_type, tenant_id, is_fk, 
      fk_object_type, ext_info, data_type
      )
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{fieldName,jdbcType=VARCHAR}, 
      #{fieldUniqueName,jdbcType=VARCHAR}, #{dsName,jdbcType=VARCHAR}, #{dsUniqueName,jdbcType=VARCHAR}, 
      #{dbName,jdbcType=VARCHAR}, #{fieldType,jdbcType=VARCHAR}, #{fieldDesc,jdbcType=VARCHAR}, 
      #{isPk,jdbcType=TINYINT}, #{defaultValue,jdbcType=VARCHAR}, #{isNotNull,jdbcType=TINYINT}, 
      #{isDeleted,jdbcType=BIGINT}, #{createEmpid,jdbcType=VARCHAR}, #{modifyEmpid,jdbcType=VARCHAR}, 
      #{sysType,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{isFk,jdbcType=INTEGER}, 
      #{fkObjectType,jdbcType=VARCHAR}, #{extInfo,jdbcType=VARCHAR}, #{dataType,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo" useGeneratedKeys="true" keyProperty="id" >
    insert into ds_field_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      gmt_create,
      gmt_modified,
      field_name,
      field_unique_name,
      ds_name,
      ds_unique_name,
      db_name,
      field_type,
      field_desc,
      is_pk,
      default_value,
      is_not_null,
      is_deleted,
      create_empid,
      modify_empid,
      sys_type,
      tenant_id,
      is_fk,
      fk_object_type,
      ext_info,
      data_type,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{fieldName,jdbcType=VARCHAR},
      #{fieldUniqueName,jdbcType=VARCHAR},
      #{dsName,jdbcType=VARCHAR},
      #{dsUniqueName,jdbcType=VARCHAR},
      #{dbName,jdbcType=VARCHAR},
      #{fieldType,jdbcType=VARCHAR},
      #{fieldDesc,jdbcType=VARCHAR},
      #{isPk,jdbcType=TINYINT},
      #{defaultValue,jdbcType=VARCHAR},
      #{isNotNull,jdbcType=TINYINT},
      #{isDeleted,jdbcType=BIGINT},
      #{createEmpid,jdbcType=VARCHAR},
      #{modifyEmpid,jdbcType=VARCHAR},
      #{sysType,jdbcType=VARCHAR},
      #{tenantId,jdbcType=VARCHAR},
      #{isFk,jdbcType=INTEGER},
      #{fkObjectType,jdbcType=VARCHAR},
      #{extInfo,jdbcType=VARCHAR},
      #{dataType,jdbcType=VARCHAR},
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo" >
    update ds_field_info
    <set >
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null" >
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="fieldName != null" >
        field_name = #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="fieldUniqueName != null" >
        field_unique_name = #{fieldUniqueName,jdbcType=VARCHAR},
      </if>
      <if test="dsName != null" >
        ds_name = #{dsName,jdbcType=VARCHAR},
      </if>
      <if test="dsUniqueName != null" >
        ds_unique_name = #{dsUniqueName,jdbcType=VARCHAR},
      </if>
      <if test="dbName != null" >
        db_name = #{dbName,jdbcType=VARCHAR},
      </if>
      <if test="fieldType != null" >
        field_type = #{fieldType,jdbcType=VARCHAR},
      </if>
      <if test="fieldDesc != null" >
        field_desc = #{fieldDesc,jdbcType=VARCHAR},
      </if>
      <if test="isPk != null" >
        is_pk = #{isPk,jdbcType=TINYINT},
      </if>
      <if test="defaultValue != null" >
        default_value = #{defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="isNotNull != null" >
        is_not_null = #{isNotNull,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createEmpid != null" >
        create_empid = #{createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="modifyEmpid != null" >
        modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="sysType != null" >
        sys_type = #{sysType,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="isFk != null" >
        is_fk = #{isFk,jdbcType=INTEGER},
      </if>
      <if test="fkObjectType != null" >
        fk_object_type = #{fkObjectType,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null" >
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null" >
        data_type = #{dataType,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo" >
    update ds_field_info
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      field_name = #{fieldName,jdbcType=VARCHAR},
      field_unique_name = #{fieldUniqueName,jdbcType=VARCHAR},
      ds_name = #{dsName,jdbcType=VARCHAR},
      ds_unique_name = #{dsUniqueName,jdbcType=VARCHAR},
      db_name = #{dbName,jdbcType=VARCHAR},
      field_type = #{fieldType,jdbcType=VARCHAR},
      field_desc = #{fieldDesc,jdbcType=VARCHAR},
      is_pk = #{isPk,jdbcType=TINYINT},
      default_value = #{defaultValue,jdbcType=VARCHAR},
      is_not_null = #{isNotNull,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      sys_type = #{sysType,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      is_fk = #{isFk,jdbcType=INTEGER},
      fk_object_type = #{fkObjectType,jdbcType=VARCHAR},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      data_type = #{dataType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>