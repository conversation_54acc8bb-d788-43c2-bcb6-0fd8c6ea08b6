<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.wormhole.qanat.dal.mapper.ExtensionMapper" >
  <resultMap id="BaseResultMap" type="com.aliyun.wormhole.qanat.dal.domain.Extension" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="code" property="code" jdbcType="VARCHAR" />
    <result column="plugin" property="plugin" jdbcType="VARCHAR" />
    <result column="is_deleted" property="isDeleted" jdbcType="BIGINT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
    <result column="object_type" property="objectType" jdbcType="VARCHAR" />
    <result column="version" property="version" jdbcType="VARCHAR" />
    <result column="db_name" property="dbName" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.aliyun.wormhole.qanat.dal.domain.Extension" extends="BaseResultMap" >
    <result column="script" property="script" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gmt_create, gmt_modified, `type`, code, plugin, is_deleted, remark, tenant_id, 
    object_type, version, db_name
  </sql>
  <sql id="Blob_Column_List" >
    script
  </sql>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.ExtensionExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from extension
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.ExtensionExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from extension
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithBLOBsAndPage" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.ExtensionExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from extension
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleAndPage" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.ExtensionExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from extension
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.ExtensionExample" >
    delete from extension
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <select id="countByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.ExtensionExample" resultType="java.lang.Integer" >
    select count(*) from extension
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update extension
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null" >
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null" >
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.plugin != null" >
        plugin = #{record.plugin,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null" >
        is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null" >
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.objectType != null" >
        object_type = #{record.objectType,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null" >
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.dbName != null" >
        db_name = #{record.dbName,jdbcType=VARCHAR},
      </if>
      <if test="record.script != null" >
        script = #{record.script,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update extension
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      `type` = #{record.type,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      plugin = #{record.plugin,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      object_type = #{record.objectType,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=VARCHAR},
      db_name = #{record.dbName,jdbcType=VARCHAR},
      script = #{record.script,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update extension
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      `type` = #{record.type,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      plugin = #{record.plugin,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      object_type = #{record.objectType,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=VARCHAR},
      db_name = #{record.dbName,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from extension
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from extension
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.aliyun.wormhole.qanat.dal.domain.Extension" useGeneratedKeys="true" keyProperty="id" >
    insert into extension (gmt_create, gmt_modified, `type`, 
      code, plugin, is_deleted, 
      remark, tenant_id, object_type, 
      version, db_name, script
      )
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{type,jdbcType=VARCHAR}, 
      #{code,jdbcType=VARCHAR}, #{plugin,jdbcType=VARCHAR}, #{isDeleted,jdbcType=BIGINT}, 
      #{remark,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{objectType,jdbcType=VARCHAR}, 
      #{version,jdbcType=VARCHAR}, #{dbName,jdbcType=VARCHAR}, #{script,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.Extension" useGeneratedKeys="true" keyProperty="id" >
    insert into extension
    <trim prefix="(" suffix=")" suffixOverrides="," >
      gmt_create,
      gmt_modified,
      `type`,
      code,
      plugin,
      is_deleted,
      remark,
      tenant_id,
      object_type,
      version,
      db_name,
      script,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{type,jdbcType=VARCHAR},
      #{code,jdbcType=VARCHAR},
      #{plugin,jdbcType=VARCHAR},
      #{isDeleted,jdbcType=BIGINT},
      #{remark,jdbcType=VARCHAR},
      #{tenantId,jdbcType=VARCHAR},
      #{objectType,jdbcType=VARCHAR},
      #{version,jdbcType=VARCHAR},
      #{dbName,jdbcType=VARCHAR},
      #{script,jdbcType=LONGVARCHAR},
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.Extension" >
    update extension
    <set >
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null" >
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="code != null" >
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="plugin != null" >
        plugin = #{plugin,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="objectType != null" >
        object_type = #{objectType,jdbcType=VARCHAR},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="dbName != null" >
        db_name = #{dbName,jdbcType=VARCHAR},
      </if>
      <if test="script != null" >
        script = #{script,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.Extension" >
    update extension
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      `type` = #{type,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      plugin = #{plugin,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      object_type = #{objectType,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      db_name = #{dbName,jdbcType=VARCHAR},
      script = #{script,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.wormhole.qanat.dal.domain.Extension" >
    update extension
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      `type` = #{type,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      plugin = #{plugin,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      object_type = #{objectType,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      db_name = #{dbName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>