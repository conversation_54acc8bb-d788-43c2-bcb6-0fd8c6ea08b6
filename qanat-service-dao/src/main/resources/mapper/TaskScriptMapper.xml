<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.wormhole.qanat.dal.mapper.TaskScriptMapper" >
  <resultMap id="BaseResultMap" type="com.aliyun.wormhole.qanat.dal.domain.TaskScript" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
    <result column="app_name" property="appName" jdbcType="VARCHAR" />
    <result column="create_empid" property="createEmpid" jdbcType="VARCHAR" />
    <result column="modify_empid" property="modifyEmpid" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.aliyun.wormhole.qanat.dal.domain.TaskScript" extends="BaseResultMap" >
    <result column="script" property="script" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gmt_create, gmt_modified, `type`, tenant_id, app_name, create_empid, modify_empid
  </sql>
  <sql id="Blob_Column_List" >
    script
  </sql>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskScriptExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from task_script
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskScriptExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from task_script
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithBLOBsAndPage" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskScriptExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from task_script
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleAndPage" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskScriptExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from task_script
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskScriptExample" >
    delete from task_script
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <select id="countByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskScriptExample" resultType="java.lang.Integer" >
    select count(*) from task_script
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update task_script
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null" >
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null" >
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.appName != null" >
        app_name = #{record.appName,jdbcType=VARCHAR},
      </if>
      <if test="record.createEmpid != null" >
        create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.modifyEmpid != null" >
        modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.script != null" >
        script = #{record.script,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update task_script
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      `type` = #{record.type,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      app_name = #{record.appName,jdbcType=VARCHAR},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      script = #{record.script,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update task_script
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      `type` = #{record.type,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      app_name = #{record.appName,jdbcType=VARCHAR},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from task_script
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from task_script
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskScript" useGeneratedKeys="true" keyProperty="id" >
    insert into task_script (gmt_create, gmt_modified, `type`, 
      tenant_id, app_name, create_empid, 
      modify_empid, script)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{type,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=VARCHAR}, #{appName,jdbcType=VARCHAR}, #{createEmpid,jdbcType=VARCHAR}, 
      #{modifyEmpid,jdbcType=VARCHAR}, #{script,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskScript" useGeneratedKeys="true" keyProperty="id" >
    insert into task_script
    <trim prefix="(" suffix=")" suffixOverrides="," >
      gmt_create,
      gmt_modified,
      `type`,
      tenant_id,
      app_name,
      create_empid,
      modify_empid,
      script,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{type,jdbcType=VARCHAR},
      #{tenantId,jdbcType=VARCHAR},
      #{appName,jdbcType=VARCHAR},
      #{createEmpid,jdbcType=VARCHAR},
      #{modifyEmpid,jdbcType=VARCHAR},
      #{script,jdbcType=LONGVARCHAR},
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskScript" >
    update task_script
    <set >
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null" >
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="appName != null" >
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="createEmpid != null" >
        create_empid = #{createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="modifyEmpid != null" >
        modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="script != null" >
        script = #{script,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskScript" >
    update task_script
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      `type` = #{type,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      app_name = #{appName,jdbcType=VARCHAR},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      script = #{script,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskScript" >
    update task_script
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      `type` = #{type,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      app_name = #{appName,jdbcType=VARCHAR},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>