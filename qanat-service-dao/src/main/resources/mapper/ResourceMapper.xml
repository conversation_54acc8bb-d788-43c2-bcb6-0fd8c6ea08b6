<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.wormhole.qanat.dal.mapper.ResourceMapper" >
  <resultMap id="BaseResultMap" type="com.aliyun.wormhole.qanat.dal.domain.Resource" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    <result column="resource_name" property="resourceName" jdbcType="VARCHAR" />
    <result column="resource_type" property="resourceType" jdbcType="VARCHAR" />
    <result column="resource_desc" property="resourceDesc" jdbcType="VARCHAR" />
    <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
    <result column="is_deleted" property="isDeleted" jdbcType="BIGINT" />
    <result column="create_empid" property="createEmpid" jdbcType="VARCHAR" />
    <result column="modify_empid" property="modifyEmpid" jdbcType="VARCHAR" />
    <result column="owner" property="owner" jdbcType="VARCHAR" />
    <result column="parent_resource_id" property="parentResourceId" jdbcType="BIGINT" />
    <result column="db_name" property="dbName" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.aliyun.wormhole.qanat.dal.domain.Resource" extends="BaseResultMap" >
    <result column="meta" property="meta" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gmt_create, gmt_modified, resource_name, resource_type, resource_desc, tenant_id, 
    is_deleted, create_empid, modify_empid, `owner`, parent_resource_id, db_name
  </sql>
  <sql id="Blob_Column_List" >
    meta
  </sql>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.ResourceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from `resource`
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.ResourceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from `resource`
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithBLOBsAndPage" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.ResourceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from `resource`
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleAndPage" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.ResourceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from `resource`
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.ResourceExample" >
    delete from `resource`
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <select id="countByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.ResourceExample" resultType="java.lang.Integer" >
    select count(*) from `resource`
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update `resource`
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null" >
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.resourceName != null" >
        resource_name = #{record.resourceName,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceType != null" >
        resource_type = #{record.resourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceDesc != null" >
        resource_desc = #{record.resourceDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null" >
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null" >
        is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      </if>
      <if test="record.createEmpid != null" >
        create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.modifyEmpid != null" >
        modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.owner != null" >
        `owner` = #{record.owner,jdbcType=VARCHAR},
      </if>
      <if test="record.parentResourceId != null" >
        parent_resource_id = #{record.parentResourceId,jdbcType=BIGINT},
      </if>
      <if test="record.dbName != null" >
        db_name = #{record.dbName,jdbcType=VARCHAR},
      </if>
      <if test="record.meta != null" >
        meta = #{record.meta,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update `resource`
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      resource_name = #{record.resourceName,jdbcType=VARCHAR},
      resource_type = #{record.resourceType,jdbcType=VARCHAR},
      resource_desc = #{record.resourceDesc,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      `owner` = #{record.owner,jdbcType=VARCHAR},
      parent_resource_id = #{record.parentResourceId,jdbcType=BIGINT},
      db_name = #{record.dbName,jdbcType=VARCHAR},
      meta = #{record.meta,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update `resource`
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      resource_name = #{record.resourceName,jdbcType=VARCHAR},
      resource_type = #{record.resourceType,jdbcType=VARCHAR},
      resource_desc = #{record.resourceDesc,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      `owner` = #{record.owner,jdbcType=VARCHAR},
      parent_resource_id = #{record.parentResourceId,jdbcType=BIGINT},
      db_name = #{record.dbName,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from `resource`
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from `resource`
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.aliyun.wormhole.qanat.dal.domain.Resource" useGeneratedKeys="true" keyProperty="id" >
    insert into `resource` (gmt_create, gmt_modified, resource_name, 
      resource_type, resource_desc, tenant_id, 
      is_deleted, create_empid, modify_empid, 
      `owner`, parent_resource_id, db_name, 
      meta)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{resourceName,jdbcType=VARCHAR}, 
      #{resourceType,jdbcType=VARCHAR}, #{resourceDesc,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=BIGINT}, #{createEmpid,jdbcType=VARCHAR}, #{modifyEmpid,jdbcType=VARCHAR}, 
      #{owner,jdbcType=VARCHAR}, #{parentResourceId,jdbcType=BIGINT}, #{dbName,jdbcType=VARCHAR}, 
      #{meta,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.Resource" useGeneratedKeys="true" keyProperty="id" >
    insert into `resource`
    <trim prefix="(" suffix=")" suffixOverrides="," >
      gmt_create,
      gmt_modified,
      resource_name,
      resource_type,
      resource_desc,
      tenant_id,
      is_deleted,
      create_empid,
      modify_empid,
      `owner`,
      parent_resource_id,
      db_name,
      meta,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{resourceName,jdbcType=VARCHAR},
      #{resourceType,jdbcType=VARCHAR},
      #{resourceDesc,jdbcType=VARCHAR},
      #{tenantId,jdbcType=VARCHAR},
      #{isDeleted,jdbcType=BIGINT},
      #{createEmpid,jdbcType=VARCHAR},
      #{modifyEmpid,jdbcType=VARCHAR},
      #{owner,jdbcType=VARCHAR},
      #{parentResourceId,jdbcType=BIGINT},
      #{dbName,jdbcType=VARCHAR},
      #{meta,jdbcType=LONGVARCHAR},
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.Resource" >
    update `resource`
    <set >
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null" >
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="resourceName != null" >
        resource_name = #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null" >
        resource_type = #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="resourceDesc != null" >
        resource_desc = #{resourceDesc,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createEmpid != null" >
        create_empid = #{createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="modifyEmpid != null" >
        modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="owner != null" >
        `owner` = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="parentResourceId != null" >
        parent_resource_id = #{parentResourceId,jdbcType=BIGINT},
      </if>
      <if test="dbName != null" >
        db_name = #{dbName,jdbcType=VARCHAR},
      </if>
      <if test="meta != null" >
        meta = #{meta,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.Resource" >
    update `resource`
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      resource_name = #{resourceName,jdbcType=VARCHAR},
      resource_type = #{resourceType,jdbcType=VARCHAR},
      resource_desc = #{resourceDesc,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      `owner` = #{owner,jdbcType=VARCHAR},
      parent_resource_id = #{parentResourceId,jdbcType=BIGINT},
      db_name = #{dbName,jdbcType=VARCHAR},
      meta = #{meta,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.wormhole.qanat.dal.domain.Resource" >
    update `resource`
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      resource_name = #{resourceName,jdbcType=VARCHAR},
      resource_type = #{resourceType,jdbcType=VARCHAR},
      resource_desc = #{resourceDesc,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      `owner` = #{owner,jdbcType=VARCHAR},
      parent_resource_id = #{parentResourceId,jdbcType=BIGINT},
      db_name = #{dbName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>