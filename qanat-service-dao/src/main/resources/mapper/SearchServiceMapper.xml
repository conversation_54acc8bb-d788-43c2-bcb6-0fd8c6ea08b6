<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.wormhole.qanat.dal.mapper.SearchServiceMapper" >
  <resultMap id="BaseResultMap" type="com.aliyun.wormhole.qanat.dal.domain.SearchService" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    <result column="svc_name" property="svcName" jdbcType="VARCHAR" />
    <result column="svc_desc" property="svcDesc" jdbcType="VARCHAR" />
    <result column="biz_line" property="bizLine" jdbcType="VARCHAR" />
    <result column="app_key" property="appKey" jdbcType="VARCHAR" />
    <result column="app_id" property="appId" jdbcType="VARCHAR" />
    <result column="create_empid" property="createEmpid" jdbcType="VARCHAR" />
    <result column="modify_empid" property="modifyEmpid" jdbcType="VARCHAR" />
    <result column="is_deleted" property="isDeleted" jdbcType="BIGINT" />
    <result column="svc_version" property="svcVersion" jdbcType="BIGINT" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.aliyun.wormhole.qanat.dal.domain.SearchServiceWithBLOBs" extends="BaseResultMap" >
    <result column="meta" property="meta" jdbcType="LONGVARCHAR" />
    <result column="ref_ds_list" property="refDsList" jdbcType="LONGVARCHAR" />
    <result column="dag_script" property="dagScript" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gmt_create, gmt_modified, svc_name, svc_desc, biz_line, app_key, app_id, create_empid, 
    modify_empid, is_deleted, svc_version
  </sql>
  <sql id="Blob_Column_List" >
    meta, ref_ds_list, dag_script
  </sql>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.SearchServiceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from search_service
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.SearchServiceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from search_service
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithBLOBsAndPage" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.SearchServiceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from search_service
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleAndPage" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.SearchServiceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from search_service
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.SearchServiceExample" >
    delete from search_service
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <select id="countByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.SearchServiceExample" resultType="java.lang.Integer" >
    select count(*) from search_service
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update search_service
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null" >
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.svcName != null" >
        svc_name = #{record.svcName,jdbcType=VARCHAR},
      </if>
      <if test="record.svcDesc != null" >
        svc_desc = #{record.svcDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.bizLine != null" >
        biz_line = #{record.bizLine,jdbcType=VARCHAR},
      </if>
      <if test="record.appKey != null" >
        app_key = #{record.appKey,jdbcType=VARCHAR},
      </if>
      <if test="record.appId != null" >
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.createEmpid != null" >
        create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.modifyEmpid != null" >
        modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null" >
        is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      </if>
      <if test="record.svcVersion != null" >
        svc_version = #{record.svcVersion,jdbcType=BIGINT},
      </if>
      <if test="record.meta != null" >
        meta = #{record.meta,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.refDsList != null" >
        ref_ds_list = #{record.refDsList,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.dagScript != null" >
        dag_script = #{record.dagScript,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update search_service
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      svc_name = #{record.svcName,jdbcType=VARCHAR},
      svc_desc = #{record.svcDesc,jdbcType=VARCHAR},
      biz_line = #{record.bizLine,jdbcType=VARCHAR},
      app_key = #{record.appKey,jdbcType=VARCHAR},
      app_id = #{record.appId,jdbcType=VARCHAR},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      svc_version = #{record.svcVersion,jdbcType=BIGINT},
      meta = #{record.meta,jdbcType=LONGVARCHAR},
      ref_ds_list = #{record.refDsList,jdbcType=LONGVARCHAR},
      dag_script = #{record.dagScript,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update search_service
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      svc_name = #{record.svcName,jdbcType=VARCHAR},
      svc_desc = #{record.svcDesc,jdbcType=VARCHAR},
      biz_line = #{record.bizLine,jdbcType=VARCHAR},
      app_key = #{record.appKey,jdbcType=VARCHAR},
      app_id = #{record.appId,jdbcType=VARCHAR},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      svc_version = #{record.svcVersion,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from search_service
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from search_service
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.aliyun.wormhole.qanat.dal.domain.SearchServiceWithBLOBs" useGeneratedKeys="true" keyProperty="id" >
    insert into search_service (gmt_create, gmt_modified, svc_name, 
      svc_desc, biz_line, app_key, 
      app_id, create_empid, modify_empid, 
      is_deleted, svc_version, meta, 
      ref_ds_list, dag_script)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{svcName,jdbcType=VARCHAR}, 
      #{svcDesc,jdbcType=VARCHAR}, #{bizLine,jdbcType=VARCHAR}, #{appKey,jdbcType=VARCHAR}, 
      #{appId,jdbcType=VARCHAR}, #{createEmpid,jdbcType=VARCHAR}, #{modifyEmpid,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=BIGINT}, #{svcVersion,jdbcType=BIGINT}, #{meta,jdbcType=LONGVARCHAR}, 
      #{refDsList,jdbcType=LONGVARCHAR}, #{dagScript,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.SearchServiceWithBLOBs" useGeneratedKeys="true" keyProperty="id" >
    insert into search_service
    <trim prefix="(" suffix=")" suffixOverrides="," >
      gmt_create,
      gmt_modified,
      svc_name,
      svc_desc,
      biz_line,
      app_key,
      app_id,
      create_empid,
      modify_empid,
      is_deleted,
      svc_version,
      meta,
      ref_ds_list,
      dag_script,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{svcName,jdbcType=VARCHAR},
      #{svcDesc,jdbcType=VARCHAR},
      #{bizLine,jdbcType=VARCHAR},
      #{appKey,jdbcType=VARCHAR},
      #{appId,jdbcType=VARCHAR},
      #{createEmpid,jdbcType=VARCHAR},
      #{modifyEmpid,jdbcType=VARCHAR},
      #{isDeleted,jdbcType=BIGINT},
      #{svcVersion,jdbcType=BIGINT},
      #{meta,jdbcType=LONGVARCHAR},
      #{refDsList,jdbcType=LONGVARCHAR},
      #{dagScript,jdbcType=LONGVARCHAR},
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.SearchServiceWithBLOBs" >
    update search_service
    <set >
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null" >
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="svcName != null" >
        svc_name = #{svcName,jdbcType=VARCHAR},
      </if>
      <if test="svcDesc != null" >
        svc_desc = #{svcDesc,jdbcType=VARCHAR},
      </if>
      <if test="bizLine != null" >
        biz_line = #{bizLine,jdbcType=VARCHAR},
      </if>
      <if test="appKey != null" >
        app_key = #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="appId != null" >
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="createEmpid != null" >
        create_empid = #{createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="modifyEmpid != null" >
        modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="svcVersion != null" >
        svc_version = #{svcVersion,jdbcType=BIGINT},
      </if>
      <if test="meta != null" >
        meta = #{meta,jdbcType=LONGVARCHAR},
      </if>
      <if test="refDsList != null" >
        ref_ds_list = #{refDsList,jdbcType=LONGVARCHAR},
      </if>
      <if test="dagScript != null" >
        dag_script = #{dagScript,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.SearchServiceWithBLOBs" >
    update search_service
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      svc_name = #{svcName,jdbcType=VARCHAR},
      svc_desc = #{svcDesc,jdbcType=VARCHAR},
      biz_line = #{bizLine,jdbcType=VARCHAR},
      app_key = #{appKey,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=VARCHAR},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      svc_version = #{svcVersion,jdbcType=BIGINT},
      meta = #{meta,jdbcType=LONGVARCHAR},
      ref_ds_list = #{refDsList,jdbcType=LONGVARCHAR},
      dag_script = #{dagScript,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.wormhole.qanat.dal.domain.SearchService" >
    update search_service
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      svc_name = #{svcName,jdbcType=VARCHAR},
      svc_desc = #{svcDesc,jdbcType=VARCHAR},
      biz_line = #{bizLine,jdbcType=VARCHAR},
      app_key = #{appKey,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=VARCHAR},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      svc_version = #{svcVersion,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>