<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.wormhole.qanat.dal.mapper.CommResouceAclMapper" >
  <resultMap id="BaseResultMap" type="com.aliyun.wormhole.qanat.dal.domain.CommResouceAcl" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    <result column="resource_id" property="resourceId" jdbcType="BIGINT" />
    <result column="resource_type" property="resourceType" jdbcType="VARCHAR" />
    <result column="resouce_name" property="resouceName" jdbcType="VARCHAR" />
    <result column="acl_token" property="aclToken" jdbcType="VARCHAR" />
    <result column="create_empid" property="createEmpid" jdbcType="VARCHAR" />
    <result column="modify_empid" property="modifyEmpid" jdbcType="VARCHAR" />
    <result column="is_deleted" property="isDeleted" jdbcType="BIGINT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="op_type" property="opType" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gmt_create, gmt_modified, resource_id, resource_type, resouce_name, acl_token, 
    create_empid, modify_empid, is_deleted, remark, op_type
  </sql>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.CommResouceAclExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from comm_resouce_acl
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleAndPage" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.CommResouceAclExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from comm_resouce_acl
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.CommResouceAclExample" >
    delete from comm_resouce_acl
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <select id="countByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.CommResouceAclExample" resultType="java.lang.Integer" >
    select count(*) from comm_resouce_acl
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update comm_resouce_acl
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null" >
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.resourceId != null" >
        resource_id = #{record.resourceId,jdbcType=BIGINT},
      </if>
      <if test="record.resourceType != null" >
        resource_type = #{record.resourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.resouceName != null" >
        resouce_name = #{record.resouceName,jdbcType=VARCHAR},
      </if>
      <if test="record.aclToken != null" >
        acl_token = #{record.aclToken,jdbcType=VARCHAR},
      </if>
      <if test="record.createEmpid != null" >
        create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.modifyEmpid != null" >
        modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null" >
        is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.opType != null" >
        op_type = #{record.opType,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update comm_resouce_acl
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      resource_id = #{record.resourceId,jdbcType=BIGINT},
      resource_type = #{record.resourceType,jdbcType=VARCHAR},
      resouce_name = #{record.resouceName,jdbcType=VARCHAR},
      acl_token = #{record.aclToken,jdbcType=VARCHAR},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      op_type = #{record.opType,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from comm_resouce_acl
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from comm_resouce_acl
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.aliyun.wormhole.qanat.dal.domain.CommResouceAcl" useGeneratedKeys="true" keyProperty="id" >
    insert into comm_resouce_acl (gmt_create, gmt_modified, resource_id, 
      resource_type, resouce_name, acl_token, 
      create_empid, modify_empid, is_deleted, 
      remark, op_type)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{resourceId,jdbcType=BIGINT}, 
      #{resourceType,jdbcType=VARCHAR}, #{resouceName,jdbcType=VARCHAR}, #{aclToken,jdbcType=VARCHAR}, 
      #{createEmpid,jdbcType=VARCHAR}, #{modifyEmpid,jdbcType=VARCHAR}, #{isDeleted,jdbcType=BIGINT}, 
      #{remark,jdbcType=VARCHAR}, #{opType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.CommResouceAcl" useGeneratedKeys="true" keyProperty="id" >
    insert into comm_resouce_acl
    <trim prefix="(" suffix=")" suffixOverrides="," >
      gmt_create,
      gmt_modified,
      resource_id,
      resource_type,
      resouce_name,
      acl_token,
      create_empid,
      modify_empid,
      is_deleted,
      remark,
      op_type,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{resourceId,jdbcType=BIGINT},
      #{resourceType,jdbcType=VARCHAR},
      #{resouceName,jdbcType=VARCHAR},
      #{aclToken,jdbcType=VARCHAR},
      #{createEmpid,jdbcType=VARCHAR},
      #{modifyEmpid,jdbcType=VARCHAR},
      #{isDeleted,jdbcType=BIGINT},
      #{remark,jdbcType=VARCHAR},
      #{opType,jdbcType=VARCHAR},
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.CommResouceAcl" >
    update comm_resouce_acl
    <set >
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null" >
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="resourceId != null" >
        resource_id = #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="resourceType != null" >
        resource_type = #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="resouceName != null" >
        resouce_name = #{resouceName,jdbcType=VARCHAR},
      </if>
      <if test="aclToken != null" >
        acl_token = #{aclToken,jdbcType=VARCHAR},
      </if>
      <if test="createEmpid != null" >
        create_empid = #{createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="modifyEmpid != null" >
        modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="opType != null" >
        op_type = #{opType,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.wormhole.qanat.dal.domain.CommResouceAcl" >
    update comm_resouce_acl
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      resource_id = #{resourceId,jdbcType=BIGINT},
      resource_type = #{resourceType,jdbcType=VARCHAR},
      resouce_name = #{resouceName,jdbcType=VARCHAR},
      acl_token = #{aclToken,jdbcType=VARCHAR},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      op_type = #{opType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>