<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceModelFieldMapper" >
  <resultMap id="BaseResultMap" type="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelField" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    <result column="create_empid" property="createEmpid" jdbcType="VARCHAR" />
    <result column="modify_empid" property="modifyEmpid" jdbcType="VARCHAR" />
    <result column="field_name" property="fieldName" jdbcType="VARCHAR" />
    <result column="ref_field_name" property="refFieldName" jdbcType="VARCHAR" />
    <result column="field_type" property="fieldType" jdbcType="VARCHAR" />
    <result column="field_desc" property="fieldDesc" jdbcType="VARCHAR" />
    <result column="is_multivalue" property="isMultivalue" jdbcType="INTEGER" />
    <result column="is_pk" property="isPk" jdbcType="INTEGER" />
    <result column="is_fk" property="isFk" jdbcType="INTEGER" />
    <result column="mv_token" property="mvToken" jdbcType="VARCHAR" />
    <result column="is_func" property="isFunc" jdbcType="INTEGER" />
    <result column="ext_ref_fields" property="extRefFields" jdbcType="VARCHAR" />
    <result column="datatube_inst_id" property="datatubeInstId" jdbcType="BIGINT" />
    <result column="model_object_code" property="modelObjectCode" jdbcType="VARCHAR" />
    <result column="ref_ds_name" property="refDsName" jdbcType="VARCHAR" />
    <result column="is_deleted" property="isDeleted" jdbcType="BIGINT" />
    <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gmt_create, gmt_modified, create_empid, modify_empid, field_name, ref_field_name, 
    field_type, field_desc, is_multivalue, is_pk, is_fk, mv_token, is_func, ext_ref_fields, 
    datatube_inst_id, model_object_code, ref_ds_name, is_deleted, tenant_id
  </sql>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelFieldExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from datatube_instance_model_field
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleAndPage" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelFieldExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from datatube_instance_model_field
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelFieldExample" >
    delete from datatube_instance_model_field
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <select id="countByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelFieldExample" resultType="java.lang.Integer" >
    select count(*) from datatube_instance_model_field
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update datatube_instance_model_field
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null" >
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createEmpid != null" >
        create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.modifyEmpid != null" >
        modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldName != null" >
        field_name = #{record.fieldName,jdbcType=VARCHAR},
      </if>
      <if test="record.refFieldName != null" >
        ref_field_name = #{record.refFieldName,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldType != null" >
        field_type = #{record.fieldType,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldDesc != null" >
        field_desc = #{record.fieldDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.isMultivalue != null" >
        is_multivalue = #{record.isMultivalue,jdbcType=INTEGER},
      </if>
      <if test="record.isPk != null" >
        is_pk = #{record.isPk,jdbcType=INTEGER},
      </if>
      <if test="record.isFk != null" >
        is_fk = #{record.isFk,jdbcType=INTEGER},
      </if>
      <if test="record.mvToken != null" >
        mv_token = #{record.mvToken,jdbcType=VARCHAR},
      </if>
      <if test="record.isFunc != null" >
        is_func = #{record.isFunc,jdbcType=INTEGER},
      </if>
      <if test="record.extRefFields != null" >
        ext_ref_fields = #{record.extRefFields,jdbcType=VARCHAR},
      </if>
      <if test="record.datatubeInstId != null" >
        datatube_inst_id = #{record.datatubeInstId,jdbcType=BIGINT},
      </if>
      <if test="record.modelObjectCode != null" >
        model_object_code = #{record.modelObjectCode,jdbcType=VARCHAR},
      </if>
      <if test="record.refDsName != null" >
        ref_ds_name = #{record.refDsName,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null" >
        is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null" >
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update datatube_instance_model_field
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      field_name = #{record.fieldName,jdbcType=VARCHAR},
      ref_field_name = #{record.refFieldName,jdbcType=VARCHAR},
      field_type = #{record.fieldType,jdbcType=VARCHAR},
      field_desc = #{record.fieldDesc,jdbcType=VARCHAR},
      is_multivalue = #{record.isMultivalue,jdbcType=INTEGER},
      is_pk = #{record.isPk,jdbcType=INTEGER},
      is_fk = #{record.isFk,jdbcType=INTEGER},
      mv_token = #{record.mvToken,jdbcType=VARCHAR},
      is_func = #{record.isFunc,jdbcType=INTEGER},
      ext_ref_fields = #{record.extRefFields,jdbcType=VARCHAR},
      datatube_inst_id = #{record.datatubeInstId,jdbcType=BIGINT},
      model_object_code = #{record.modelObjectCode,jdbcType=VARCHAR},
      ref_ds_name = #{record.refDsName,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from datatube_instance_model_field
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from datatube_instance_model_field
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelField" useGeneratedKeys="true" keyProperty="id" >
    insert into datatube_instance_model_field (gmt_create, gmt_modified, create_empid, 
      modify_empid, field_name, ref_field_name, 
      field_type, field_desc, is_multivalue, 
      is_pk, is_fk, mv_token, 
      is_func, ext_ref_fields, datatube_inst_id, 
      model_object_code, ref_ds_name, is_deleted, 
      tenant_id)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{createEmpid,jdbcType=VARCHAR}, 
      #{modifyEmpid,jdbcType=VARCHAR}, #{fieldName,jdbcType=VARCHAR}, #{refFieldName,jdbcType=VARCHAR}, 
      #{fieldType,jdbcType=VARCHAR}, #{fieldDesc,jdbcType=VARCHAR}, #{isMultivalue,jdbcType=INTEGER}, 
      #{isPk,jdbcType=INTEGER}, #{isFk,jdbcType=INTEGER}, #{mvToken,jdbcType=VARCHAR}, 
      #{isFunc,jdbcType=INTEGER}, #{extRefFields,jdbcType=VARCHAR}, #{datatubeInstId,jdbcType=BIGINT}, 
      #{modelObjectCode,jdbcType=VARCHAR}, #{refDsName,jdbcType=VARCHAR}, #{isDeleted,jdbcType=BIGINT}, 
      #{tenantId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelField" useGeneratedKeys="true" keyProperty="id" >
    insert into datatube_instance_model_field
    <trim prefix="(" suffix=")" suffixOverrides="," >
      gmt_create,
      gmt_modified,
      create_empid,
      modify_empid,
      field_name,
      ref_field_name,
      field_type,
      field_desc,
      is_multivalue,
      is_pk,
      is_fk,
      mv_token,
      is_func,
      ext_ref_fields,
      datatube_inst_id,
      model_object_code,
      ref_ds_name,
      is_deleted,
      tenant_id,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{createEmpid,jdbcType=VARCHAR},
      #{modifyEmpid,jdbcType=VARCHAR},
      #{fieldName,jdbcType=VARCHAR},
      #{refFieldName,jdbcType=VARCHAR},
      #{fieldType,jdbcType=VARCHAR},
      #{fieldDesc,jdbcType=VARCHAR},
      #{isMultivalue,jdbcType=INTEGER},
      #{isPk,jdbcType=INTEGER},
      #{isFk,jdbcType=INTEGER},
      #{mvToken,jdbcType=VARCHAR},
      #{isFunc,jdbcType=INTEGER},
      #{extRefFields,jdbcType=VARCHAR},
      #{datatubeInstId,jdbcType=BIGINT},
      #{modelObjectCode,jdbcType=VARCHAR},
      #{refDsName,jdbcType=VARCHAR},
      #{isDeleted,jdbcType=BIGINT},
      #{tenantId,jdbcType=VARCHAR},
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelField" >
    update datatube_instance_model_field
    <set >
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null" >
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="createEmpid != null" >
        create_empid = #{createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="modifyEmpid != null" >
        modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="fieldName != null" >
        field_name = #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="refFieldName != null" >
        ref_field_name = #{refFieldName,jdbcType=VARCHAR},
      </if>
      <if test="fieldType != null" >
        field_type = #{fieldType,jdbcType=VARCHAR},
      </if>
      <if test="fieldDesc != null" >
        field_desc = #{fieldDesc,jdbcType=VARCHAR},
      </if>
      <if test="isMultivalue != null" >
        is_multivalue = #{isMultivalue,jdbcType=INTEGER},
      </if>
      <if test="isPk != null" >
        is_pk = #{isPk,jdbcType=INTEGER},
      </if>
      <if test="isFk != null" >
        is_fk = #{isFk,jdbcType=INTEGER},
      </if>
      <if test="mvToken != null" >
        mv_token = #{mvToken,jdbcType=VARCHAR},
      </if>
      <if test="isFunc != null" >
        is_func = #{isFunc,jdbcType=INTEGER},
      </if>
      <if test="extRefFields != null" >
        ext_ref_fields = #{extRefFields,jdbcType=VARCHAR},
      </if>
      <if test="datatubeInstId != null" >
        datatube_inst_id = #{datatubeInstId,jdbcType=BIGINT},
      </if>
      <if test="modelObjectCode != null" >
        model_object_code = #{modelObjectCode,jdbcType=VARCHAR},
      </if>
      <if test="refDsName != null" >
        ref_ds_name = #{refDsName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelField" >
    update datatube_instance_model_field
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      field_name = #{fieldName,jdbcType=VARCHAR},
      ref_field_name = #{refFieldName,jdbcType=VARCHAR},
      field_type = #{fieldType,jdbcType=VARCHAR},
      field_desc = #{fieldDesc,jdbcType=VARCHAR},
      is_multivalue = #{isMultivalue,jdbcType=INTEGER},
      is_pk = #{isPk,jdbcType=INTEGER},
      is_fk = #{isFk,jdbcType=INTEGER},
      mv_token = #{mvToken,jdbcType=VARCHAR},
      is_func = #{isFunc,jdbcType=INTEGER},
      ext_ref_fields = #{extRefFields,jdbcType=VARCHAR},
      datatube_inst_id = #{datatubeInstId,jdbcType=BIGINT},
      model_object_code = #{modelObjectCode,jdbcType=VARCHAR},
      ref_ds_name = #{refDsName,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>