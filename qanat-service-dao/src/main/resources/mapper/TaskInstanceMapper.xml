<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.wormhole.qanat.dal.mapper.TaskInstanceMapper" >
  <resultMap id="BaseResultMap" type="com.aliyun.wormhole.qanat.dal.domain.TaskInstance" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    <result column="task_id" property="taskId" jdbcType="BIGINT" />
    <result column="operator" property="operator" jdbcType="VARCHAR" />
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="create_empid" property="createEmpid" jdbcType="VARCHAR" />
    <result column="modify_empid" property="modifyEmpid" jdbcType="VARCHAR" />
    <result column="parent_instance_id" property="parentInstanceId" jdbcType="BIGINT" />
    <result column="external_inst_id" property="externalInstId" jdbcType="VARCHAR" />
    <result column="task_name" property="taskName" jdbcType="VARCHAR" />
    <result column="external_id" property="externalId" jdbcType="VARCHAR" />
    <result column="task_command" property="taskCommand" jdbcType="VARCHAR" />
    <result column="host_addr" property="hostAddr" jdbcType="VARCHAR" />
    <result column="node_action" property="nodeAction" jdbcType="VARCHAR" />
    <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
    <result column="app_name" property="appName" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.aliyun.wormhole.qanat.dal.domain.TaskInstance" extends="BaseResultMap" >
    <result column="exec_param" property="execParam" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gmt_create, gmt_modified, task_id, `operator`, start_time, end_time, `status`, 
    create_empid, modify_empid, parent_instance_id, external_inst_id, task_name, external_id, 
    task_command, host_addr, node_action, tenant_id, app_name
  </sql>
  <sql id="Blob_Column_List" >
    exec_param
  </sql>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInstanceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from task_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInstanceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from task_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithBLOBsAndPage" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInstanceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from task_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleAndPage" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInstanceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from task_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInstanceExample" >
    delete from task_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <select id="countByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInstanceExample" resultType="java.lang.Integer" >
    select count(*) from task_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update task_instance
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null" >
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskId != null" >
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.operator != null" >
        `operator` = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.startTime != null" >
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null" >
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.createEmpid != null" >
        create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.modifyEmpid != null" >
        modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.parentInstanceId != null" >
        parent_instance_id = #{record.parentInstanceId,jdbcType=BIGINT},
      </if>
      <if test="record.externalInstId != null" >
        external_inst_id = #{record.externalInstId,jdbcType=VARCHAR},
      </if>
      <if test="record.taskName != null" >
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.externalId != null" >
        external_id = #{record.externalId,jdbcType=VARCHAR},
      </if>
      <if test="record.taskCommand != null" >
        task_command = #{record.taskCommand,jdbcType=VARCHAR},
      </if>
      <if test="record.hostAddr != null" >
        host_addr = #{record.hostAddr,jdbcType=VARCHAR},
      </if>
      <if test="record.nodeAction != null" >
        node_action = #{record.nodeAction,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null" >
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.appName != null" >
        app_name = #{record.appName,jdbcType=VARCHAR},
      </if>
      <if test="record.execParam != null" >
        exec_param = #{record.execParam,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update task_instance
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      task_id = #{record.taskId,jdbcType=BIGINT},
      `operator` = #{record.operator,jdbcType=VARCHAR},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      parent_instance_id = #{record.parentInstanceId,jdbcType=BIGINT},
      external_inst_id = #{record.externalInstId,jdbcType=VARCHAR},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      external_id = #{record.externalId,jdbcType=VARCHAR},
      task_command = #{record.taskCommand,jdbcType=VARCHAR},
      host_addr = #{record.hostAddr,jdbcType=VARCHAR},
      node_action = #{record.nodeAction,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      app_name = #{record.appName,jdbcType=VARCHAR},
      exec_param = #{record.execParam,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update task_instance
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      task_id = #{record.taskId,jdbcType=BIGINT},
      `operator` = #{record.operator,jdbcType=VARCHAR},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      parent_instance_id = #{record.parentInstanceId,jdbcType=BIGINT},
      external_inst_id = #{record.externalInstId,jdbcType=VARCHAR},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      external_id = #{record.externalId,jdbcType=VARCHAR},
      task_command = #{record.taskCommand,jdbcType=VARCHAR},
      host_addr = #{record.hostAddr,jdbcType=VARCHAR},
      node_action = #{record.nodeAction,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      app_name = #{record.appName,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from task_instance
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from task_instance
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInstance" useGeneratedKeys="true" keyProperty="id" >
    insert into task_instance (gmt_create, gmt_modified, task_id, 
      `operator`, start_time, end_time, 
      `status`, create_empid, modify_empid, 
      parent_instance_id, external_inst_id, task_name, 
      external_id, task_command, host_addr, 
      node_action, tenant_id, app_name, 
      exec_param)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{taskId,jdbcType=BIGINT}, 
      #{operator,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=TINYINT}, #{createEmpid,jdbcType=VARCHAR}, #{modifyEmpid,jdbcType=VARCHAR}, 
      #{parentInstanceId,jdbcType=BIGINT}, #{externalInstId,jdbcType=VARCHAR}, #{taskName,jdbcType=VARCHAR}, 
      #{externalId,jdbcType=VARCHAR}, #{taskCommand,jdbcType=VARCHAR}, #{hostAddr,jdbcType=VARCHAR}, 
      #{nodeAction,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{appName,jdbcType=VARCHAR}, 
      #{execParam,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInstance" useGeneratedKeys="true" keyProperty="id" >
    insert into task_instance
    <trim prefix="(" suffix=")" suffixOverrides="," >
      gmt_create,
      gmt_modified,
      task_id,
      `operator`,
      start_time,
      end_time,
      `status`,
      create_empid,
      modify_empid,
      parent_instance_id,
      external_inst_id,
      task_name,
      external_id,
      task_command,
      host_addr,
      node_action,
      tenant_id,
      app_name,
      exec_param,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{taskId,jdbcType=BIGINT},
      #{operator,jdbcType=VARCHAR},
      #{startTime,jdbcType=TIMESTAMP},
      #{endTime,jdbcType=TIMESTAMP},
      #{status,jdbcType=TINYINT},
      #{createEmpid,jdbcType=VARCHAR},
      #{modifyEmpid,jdbcType=VARCHAR},
      #{parentInstanceId,jdbcType=BIGINT},
      #{externalInstId,jdbcType=VARCHAR},
      #{taskName,jdbcType=VARCHAR},
      #{externalId,jdbcType=VARCHAR},
      #{taskCommand,jdbcType=VARCHAR},
      #{hostAddr,jdbcType=VARCHAR},
      #{nodeAction,jdbcType=VARCHAR},
      #{tenantId,jdbcType=VARCHAR},
      #{appName,jdbcType=VARCHAR},
      #{execParam,jdbcType=LONGVARCHAR},
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInstance" >
    update task_instance
    <set >
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null" >
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="taskId != null" >
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="operator != null" >
        `operator` = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="createEmpid != null" >
        create_empid = #{createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="modifyEmpid != null" >
        modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="parentInstanceId != null" >
        parent_instance_id = #{parentInstanceId,jdbcType=BIGINT},
      </if>
      <if test="externalInstId != null" >
        external_inst_id = #{externalInstId,jdbcType=VARCHAR},
      </if>
      <if test="taskName != null" >
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="externalId != null" >
        external_id = #{externalId,jdbcType=VARCHAR},
      </if>
      <if test="taskCommand != null" >
        task_command = #{taskCommand,jdbcType=VARCHAR},
      </if>
      <if test="hostAddr != null" >
        host_addr = #{hostAddr,jdbcType=VARCHAR},
      </if>
      <if test="nodeAction != null" >
        node_action = #{nodeAction,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="appName != null" >
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="execParam != null" >
        exec_param = #{execParam,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInstance" >
    update task_instance
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      task_id = #{taskId,jdbcType=BIGINT},
      `operator` = #{operator,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      parent_instance_id = #{parentInstanceId,jdbcType=BIGINT},
      external_inst_id = #{externalInstId,jdbcType=VARCHAR},
      task_name = #{taskName,jdbcType=VARCHAR},
      external_id = #{externalId,jdbcType=VARCHAR},
      task_command = #{taskCommand,jdbcType=VARCHAR},
      host_addr = #{hostAddr,jdbcType=VARCHAR},
      node_action = #{nodeAction,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      app_name = #{appName,jdbcType=VARCHAR},
      exec_param = #{execParam,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInstance" >
    update task_instance
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      task_id = #{taskId,jdbcType=BIGINT},
      `operator` = #{operator,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      parent_instance_id = #{parentInstanceId,jdbcType=BIGINT},
      external_inst_id = #{externalInstId,jdbcType=VARCHAR},
      task_name = #{taskName,jdbcType=VARCHAR},
      external_id = #{externalId,jdbcType=VARCHAR},
      task_command = #{taskCommand,jdbcType=VARCHAR},
      host_addr = #{hostAddr,jdbcType=VARCHAR},
      node_action = #{nodeAction,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      app_name = #{appName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>