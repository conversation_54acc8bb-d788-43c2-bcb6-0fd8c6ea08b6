<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceMapper" >
  <resultMap id="BaseResultMap" type="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    <result column="create_empid" property="createEmpid" jdbcType="VARCHAR" />
    <result column="modify_empid" property="modifyEmpid" jdbcType="VARCHAR" />
    <result column="code" property="code" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="provider" property="provider" jdbcType="VARCHAR" />
    <result column="level" property="level" jdbcType="VARCHAR" />
    <result column="is_deleted" property="isDeleted" jdbcType="BIGINT" />
    <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
    <result column="object_type" property="objectType" jdbcType="VARCHAR" />
    <result column="app_name" property="appName" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="is_test" property="isTest" jdbcType="BIGINT" />
    <result column="provider_id" property="providerId" jdbcType="BIGINT" />
    <result column="compute_cost" property="computeCost" jdbcType="DECIMAL" />
    <result column="store_cost" property="storeCost" jdbcType="DECIMAL" />
    <result column="consistent_rate" property="consistentRate" jdbcType="DECIMAL" />
    <result column="delay_ms" property="delayMs" jdbcType="INTEGER" />
    <result column="db_name" property="dbName" jdbcType="VARCHAR" />
    <result column="version" property="version" jdbcType="VARCHAR" />
    <result column="is_dynamic" property="isDynamic" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gmt_create, gmt_modified, create_empid, modify_empid, code, `name`, remark, provider, 
    `level`, is_deleted, tenant_id, object_type, app_name, `type`, is_test, provider_id, 
    compute_cost, store_cost, consistent_rate, delay_ms, db_name, version, is_dynamic
  </sql>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from datatube_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleAndPage" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from datatube_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample" >
    delete from datatube_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <select id="countByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample" resultType="java.lang.Integer" >
    select count(*) from datatube_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update datatube_instance
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null" >
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createEmpid != null" >
        create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.modifyEmpid != null" >
        modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null" >
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null" >
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.provider != null" >
        provider = #{record.provider,jdbcType=VARCHAR},
      </if>
      <if test="record.level != null" >
        `level` = #{record.level,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null" >
        is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null" >
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.objectType != null" >
        object_type = #{record.objectType,jdbcType=VARCHAR},
      </if>
      <if test="record.appName != null" >
        app_name = #{record.appName,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.isTest != null" >
        is_test = #{record.isTest,jdbcType=BIGINT},
      </if>
      <if test="record.providerId != null" >
        provider_id = #{record.providerId,jdbcType=BIGINT},
      </if>
      <if test="record.computeCost != null" >
        compute_cost = #{record.computeCost,jdbcType=DECIMAL},
      </if>
      <if test="record.storeCost != null" >
        store_cost = #{record.storeCost,jdbcType=DECIMAL},
      </if>
      <if test="record.consistentRate != null" >
        consistent_rate = #{record.consistentRate,jdbcType=DECIMAL},
      </if>
      <if test="record.delayMs != null" >
        delay_ms = #{record.delayMs,jdbcType=INTEGER},
      </if>
      <if test="record.dbName != null" >
        db_name = #{record.dbName,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null" >
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.isDynamic != null" >
        is_dynamic = #{record.isDynamic,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update datatube_instance
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      provider = #{record.provider,jdbcType=VARCHAR},
      `level` = #{record.level,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      object_type = #{record.objectType,jdbcType=VARCHAR},
      app_name = #{record.appName,jdbcType=VARCHAR},
      `type` = #{record.type,jdbcType=VARCHAR},
      is_test = #{record.isTest,jdbcType=BIGINT},
      provider_id = #{record.providerId,jdbcType=BIGINT},
      compute_cost = #{record.computeCost,jdbcType=DECIMAL},
      store_cost = #{record.storeCost,jdbcType=DECIMAL},
      consistent_rate = #{record.consistentRate,jdbcType=DECIMAL},
      delay_ms = #{record.delayMs,jdbcType=INTEGER},
      db_name = #{record.dbName,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=VARCHAR},
      is_dynamic = #{record.isDynamic,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from datatube_instance
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from datatube_instance
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance" useGeneratedKeys="true" keyProperty="id" >
    insert into datatube_instance (gmt_create, gmt_modified, create_empid, 
      modify_empid, code, `name`, 
      remark, provider, `level`, 
      is_deleted, tenant_id, object_type, 
      app_name, `type`, is_test, 
      provider_id, compute_cost, store_cost, 
      consistent_rate, delay_ms, db_name, 
      version, is_dynamic)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{createEmpid,jdbcType=VARCHAR}, 
      #{modifyEmpid,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{provider,jdbcType=VARCHAR}, #{level,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{objectType,jdbcType=VARCHAR}, 
      #{appName,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{isTest,jdbcType=BIGINT}, 
      #{providerId,jdbcType=BIGINT}, #{computeCost,jdbcType=DECIMAL}, #{storeCost,jdbcType=DECIMAL}, 
      #{consistentRate,jdbcType=DECIMAL}, #{delayMs,jdbcType=INTEGER}, #{dbName,jdbcType=VARCHAR}, 
      #{version,jdbcType=VARCHAR}, #{isDynamic,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance" useGeneratedKeys="true" keyProperty="id" >
    insert into datatube_instance
    <trim prefix="(" suffix=")" suffixOverrides="," >
      gmt_create,
      gmt_modified,
      create_empid,
      modify_empid,
      code,
      `name`,
      remark,
      provider,
      `level`,
      is_deleted,
      tenant_id,
      object_type,
      app_name,
      `type`,
      is_test,
      provider_id,
      compute_cost,
      store_cost,
      consistent_rate,
      delay_ms,
      db_name,
      version,
      is_dynamic,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{createEmpid,jdbcType=VARCHAR},
      #{modifyEmpid,jdbcType=VARCHAR},
      #{code,jdbcType=VARCHAR},
      #{name,jdbcType=VARCHAR},
      #{remark,jdbcType=VARCHAR},
      #{provider,jdbcType=VARCHAR},
      #{level,jdbcType=VARCHAR},
      #{isDeleted,jdbcType=BIGINT},
      #{tenantId,jdbcType=VARCHAR},
      #{objectType,jdbcType=VARCHAR},
      #{appName,jdbcType=VARCHAR},
      #{type,jdbcType=VARCHAR},
      #{isTest,jdbcType=BIGINT},
      #{providerId,jdbcType=BIGINT},
      #{computeCost,jdbcType=DECIMAL},
      #{storeCost,jdbcType=DECIMAL},
      #{consistentRate,jdbcType=DECIMAL},
      #{delayMs,jdbcType=INTEGER},
      #{dbName,jdbcType=VARCHAR},
      #{version,jdbcType=VARCHAR},
      #{isDynamic,jdbcType=INTEGER},
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance" >
    update datatube_instance
    <set >
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null" >
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="createEmpid != null" >
        create_empid = #{createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="modifyEmpid != null" >
        modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="code != null" >
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="provider != null" >
        provider = #{provider,jdbcType=VARCHAR},
      </if>
      <if test="level != null" >
        `level` = #{level,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="objectType != null" >
        object_type = #{objectType,jdbcType=VARCHAR},
      </if>
      <if test="appName != null" >
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="isTest != null" >
        is_test = #{isTest,jdbcType=BIGINT},
      </if>
      <if test="providerId != null" >
        provider_id = #{providerId,jdbcType=BIGINT},
      </if>
      <if test="computeCost != null" >
        compute_cost = #{computeCost,jdbcType=DECIMAL},
      </if>
      <if test="storeCost != null" >
        store_cost = #{storeCost,jdbcType=DECIMAL},
      </if>
      <if test="consistentRate != null" >
        consistent_rate = #{consistentRate,jdbcType=DECIMAL},
      </if>
      <if test="delayMs != null" >
        delay_ms = #{delayMs,jdbcType=INTEGER},
      </if>
      <if test="dbName != null" >
        db_name = #{dbName,jdbcType=VARCHAR},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="isDynamic != null" >
        is_dynamic = #{isDynamic,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance" >
    update datatube_instance
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      provider = #{provider,jdbcType=VARCHAR},
      `level` = #{level,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      object_type = #{objectType,jdbcType=VARCHAR},
      app_name = #{appName,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      is_test = #{isTest,jdbcType=BIGINT},
      provider_id = #{providerId,jdbcType=BIGINT},
      compute_cost = #{computeCost,jdbcType=DECIMAL},
      store_cost = #{storeCost,jdbcType=DECIMAL},
      consistent_rate = #{consistentRate,jdbcType=DECIMAL},
      delay_ms = #{delayMs,jdbcType=INTEGER},
      db_name = #{dbName,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      is_dynamic = #{isDynamic,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>