<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceTaskMapper" >
  <resultMap id="BaseResultMap" type="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    <result column="create_empid" property="createEmpid" jdbcType="VARCHAR" />
    <result column="modify_empid" property="modifyEmpid" jdbcType="VARCHAR" />
    <result column="datatube_inst_id" property="datatubeInstId" jdbcType="BIGINT" />
    <result column="task_name" property="taskName" jdbcType="VARCHAR" />
    <result column="task_type" property="taskType" jdbcType="VARCHAR" />
    <result column="task_cu" property="taskCu" jdbcType="DECIMAL" />
    <result column="is_deleted" property="isDeleted" jdbcType="BIGINT" />
    <result column="version" property="version" jdbcType="INTEGER" />
    <result column="parallel" property="parallel" jdbcType="INTEGER" />
    <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask" extends="BaseResultMap" >
    <result column="task_script" property="taskScript" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gmt_create, gmt_modified, create_empid, modify_empid, datatube_inst_id, task_name, 
    task_type, task_cu, is_deleted, version, parallel, tenant_id
  </sql>
  <sql id="Blob_Column_List" >
    task_script
  </sql>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTaskExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from datatube_instance_task
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTaskExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from datatube_instance_task
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithBLOBsAndPage" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTaskExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from datatube_instance_task
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleAndPage" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTaskExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from datatube_instance_task
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTaskExample" >
    delete from datatube_instance_task
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <select id="countByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTaskExample" resultType="java.lang.Integer" >
    select count(*) from datatube_instance_task
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update datatube_instance_task
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null" >
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createEmpid != null" >
        create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.modifyEmpid != null" >
        modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.datatubeInstId != null" >
        datatube_inst_id = #{record.datatubeInstId,jdbcType=BIGINT},
      </if>
      <if test="record.taskName != null" >
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.taskType != null" >
        task_type = #{record.taskType,jdbcType=VARCHAR},
      </if>
      <if test="record.taskCu != null" >
        task_cu = #{record.taskCu,jdbcType=DECIMAL},
      </if>
      <if test="record.isDeleted != null" >
        is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      </if>
      <if test="record.version != null" >
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.parallel != null" >
        parallel = #{record.parallel,jdbcType=INTEGER},
      </if>
      <if test="record.tenantId != null" >
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.taskScript != null" >
        task_script = #{record.taskScript,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update datatube_instance_task
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      datatube_inst_id = #{record.datatubeInstId,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      task_type = #{record.taskType,jdbcType=VARCHAR},
      task_cu = #{record.taskCu,jdbcType=DECIMAL},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      version = #{record.version,jdbcType=INTEGER},
      parallel = #{record.parallel,jdbcType=INTEGER},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      task_script = #{record.taskScript,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update datatube_instance_task
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      datatube_inst_id = #{record.datatubeInstId,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      task_type = #{record.taskType,jdbcType=VARCHAR},
      task_cu = #{record.taskCu,jdbcType=DECIMAL},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      version = #{record.version,jdbcType=INTEGER},
      parallel = #{record.parallel,jdbcType=INTEGER},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from datatube_instance_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from datatube_instance_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask" useGeneratedKeys="true" keyProperty="id" >
    insert into datatube_instance_task (gmt_create, gmt_modified, create_empid, 
      modify_empid, datatube_inst_id, task_name, 
      task_type, task_cu, is_deleted, 
      version, parallel, tenant_id, 
      task_script)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{createEmpid,jdbcType=VARCHAR}, 
      #{modifyEmpid,jdbcType=VARCHAR}, #{datatubeInstId,jdbcType=BIGINT}, #{taskName,jdbcType=VARCHAR}, 
      #{taskType,jdbcType=VARCHAR}, #{taskCu,jdbcType=DECIMAL}, #{isDeleted,jdbcType=BIGINT}, 
      #{version,jdbcType=INTEGER}, #{parallel,jdbcType=INTEGER}, #{tenantId,jdbcType=VARCHAR}, 
      #{taskScript,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask" useGeneratedKeys="true" keyProperty="id" >
    insert into datatube_instance_task
    <trim prefix="(" suffix=")" suffixOverrides="," >
      gmt_create,
      gmt_modified,
      create_empid,
      modify_empid,
      datatube_inst_id,
      task_name,
      task_type,
      task_cu,
      is_deleted,
      version,
      parallel,
      tenant_id,
      task_script,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{createEmpid,jdbcType=VARCHAR},
      #{modifyEmpid,jdbcType=VARCHAR},
      #{datatubeInstId,jdbcType=BIGINT},
      #{taskName,jdbcType=VARCHAR},
      #{taskType,jdbcType=VARCHAR},
      #{taskCu,jdbcType=DECIMAL},
      #{isDeleted,jdbcType=BIGINT},
      #{version,jdbcType=INTEGER},
      #{parallel,jdbcType=INTEGER},
      #{tenantId,jdbcType=VARCHAR},
      #{taskScript,jdbcType=LONGVARCHAR},
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask" >
    update datatube_instance_task
    <set >
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null" >
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="createEmpid != null" >
        create_empid = #{createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="modifyEmpid != null" >
        modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="datatubeInstId != null" >
        datatube_inst_id = #{datatubeInstId,jdbcType=BIGINT},
      </if>
      <if test="taskName != null" >
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null" >
        task_type = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="taskCu != null" >
        task_cu = #{taskCu,jdbcType=DECIMAL},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="parallel != null" >
        parallel = #{parallel,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="taskScript != null" >
        task_script = #{taskScript,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask" >
    update datatube_instance_task
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      datatube_inst_id = #{datatubeInstId,jdbcType=BIGINT},
      task_name = #{taskName,jdbcType=VARCHAR},
      task_type = #{taskType,jdbcType=VARCHAR},
      task_cu = #{taskCu,jdbcType=DECIMAL},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      version = #{version,jdbcType=INTEGER},
      parallel = #{parallel,jdbcType=INTEGER},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      task_script = #{taskScript,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask" >
    update datatube_instance_task
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      datatube_inst_id = #{datatubeInstId,jdbcType=BIGINT},
      task_name = #{taskName,jdbcType=VARCHAR},
      task_type = #{taskType,jdbcType=VARCHAR},
      task_cu = #{taskCu,jdbcType=DECIMAL},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      version = #{version,jdbcType=INTEGER},
      parallel = #{parallel,jdbcType=INTEGER},
      tenant_id = #{tenantId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>