<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.wormhole.qanat.dal.mapper.TaskInfoMapper" >
  <resultMap id="BaseResultMap" type="com.aliyun.wormhole.qanat.dal.domain.TaskInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    <result column="create_empid" property="createEmpid" jdbcType="VARCHAR" />
    <result column="modify_empid" property="modifyEmpid" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="task_desc" property="taskDesc" jdbcType="VARCHAR" />
    <result column="policy" property="policy" jdbcType="VARCHAR" />
    <result column="engine_type" property="engineType" jdbcType="VARCHAR" />
    <result column="is_deleted" property="isDeleted" jdbcType="BIGINT" />
    <result column="external_id" property="externalId" jdbcType="VARCHAR" />
    <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
    <result column="app_name" property="appName" jdbcType="VARCHAR" />
    <result column="time_expression" property="timeExpression" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.aliyun.wormhole.qanat.dal.domain.TaskInfoWithBLOBs" extends="BaseResultMap" >
    <result column="meta" property="meta" jdbcType="LONGVARCHAR" />
    <result column="dag" property="dag" jdbcType="LONGVARCHAR" />
    <result column="sub_tasks" property="subTasks" jdbcType="LONGVARCHAR" />
    <result column="dag_script" property="dagScript" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gmt_create, gmt_modified, create_empid, modify_empid, `name`, task_desc, policy, 
    engine_type, is_deleted, external_id, tenant_id, app_name, time_expression
  </sql>
  <sql id="Blob_Column_List" >
    meta, dag, sub_tasks, dag_script
  </sql>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from task_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from task_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithBLOBsAndPage" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from task_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleAndPage" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from task_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInfoExample" >
    delete from task_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <select id="countByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInfoExample" resultType="java.lang.Integer" >
    select count(*) from task_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update task_info
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null" >
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createEmpid != null" >
        create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.modifyEmpid != null" >
        modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null" >
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.taskDesc != null" >
        task_desc = #{record.taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.policy != null" >
        policy = #{record.policy,jdbcType=VARCHAR},
      </if>
      <if test="record.engineType != null" >
        engine_type = #{record.engineType,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null" >
        is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      </if>
      <if test="record.externalId != null" >
        external_id = #{record.externalId,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null" >
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.appName != null" >
        app_name = #{record.appName,jdbcType=VARCHAR},
      </if>
      <if test="record.timeExpression != null" >
        time_expression = #{record.timeExpression,jdbcType=VARCHAR},
      </if>
      <if test="record.meta != null" >
        meta = #{record.meta,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.dag != null" >
        dag = #{record.dag,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.subTasks != null" >
        sub_tasks = #{record.subTasks,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.dagScript != null" >
        dag_script = #{record.dagScript,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update task_info
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      task_desc = #{record.taskDesc,jdbcType=VARCHAR},
      policy = #{record.policy,jdbcType=VARCHAR},
      engine_type = #{record.engineType,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      external_id = #{record.externalId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      app_name = #{record.appName,jdbcType=VARCHAR},
      time_expression = #{record.timeExpression,jdbcType=VARCHAR},
      meta = #{record.meta,jdbcType=LONGVARCHAR},
      dag = #{record.dag,jdbcType=LONGVARCHAR},
      sub_tasks = #{record.subTasks,jdbcType=LONGVARCHAR},
      dag_script = #{record.dagScript,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update task_info
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      task_desc = #{record.taskDesc,jdbcType=VARCHAR},
      policy = #{record.policy,jdbcType=VARCHAR},
      engine_type = #{record.engineType,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      external_id = #{record.externalId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      app_name = #{record.appName,jdbcType=VARCHAR},
      time_expression = #{record.timeExpression,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from task_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from task_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInfoWithBLOBs" useGeneratedKeys="true" keyProperty="id" >
    insert into task_info (gmt_create, gmt_modified, create_empid, 
      modify_empid, `name`, task_desc, 
      policy, engine_type, is_deleted, 
      external_id, tenant_id, app_name, 
      time_expression, meta, dag, 
      sub_tasks, dag_script)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{createEmpid,jdbcType=VARCHAR}, 
      #{modifyEmpid,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{taskDesc,jdbcType=VARCHAR}, 
      #{policy,jdbcType=VARCHAR}, #{engineType,jdbcType=VARCHAR}, #{isDeleted,jdbcType=BIGINT}, 
      #{externalId,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{appName,jdbcType=VARCHAR}, 
      #{timeExpression,jdbcType=VARCHAR}, #{meta,jdbcType=LONGVARCHAR}, #{dag,jdbcType=LONGVARCHAR}, 
      #{subTasks,jdbcType=LONGVARCHAR}, #{dagScript,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInfoWithBLOBs" useGeneratedKeys="true" keyProperty="id" >
    insert into task_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      gmt_create,
      gmt_modified,
      create_empid,
      modify_empid,
      `name`,
      task_desc,
      policy,
      engine_type,
      is_deleted,
      external_id,
      tenant_id,
      app_name,
      time_expression,
      meta,
      dag,
      sub_tasks,
      dag_script,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{createEmpid,jdbcType=VARCHAR},
      #{modifyEmpid,jdbcType=VARCHAR},
      #{name,jdbcType=VARCHAR},
      #{taskDesc,jdbcType=VARCHAR},
      #{policy,jdbcType=VARCHAR},
      #{engineType,jdbcType=VARCHAR},
      #{isDeleted,jdbcType=BIGINT},
      #{externalId,jdbcType=VARCHAR},
      #{tenantId,jdbcType=VARCHAR},
      #{appName,jdbcType=VARCHAR},
      #{timeExpression,jdbcType=VARCHAR},
      #{meta,jdbcType=LONGVARCHAR},
      #{dag,jdbcType=LONGVARCHAR},
      #{subTasks,jdbcType=LONGVARCHAR},
      #{dagScript,jdbcType=LONGVARCHAR},
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInfoWithBLOBs" >
    update task_info
    <set >
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null" >
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="createEmpid != null" >
        create_empid = #{createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="modifyEmpid != null" >
        modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="taskDesc != null" >
        task_desc = #{taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="policy != null" >
        policy = #{policy,jdbcType=VARCHAR},
      </if>
      <if test="engineType != null" >
        engine_type = #{engineType,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="externalId != null" >
        external_id = #{externalId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="appName != null" >
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="timeExpression != null" >
        time_expression = #{timeExpression,jdbcType=VARCHAR},
      </if>
      <if test="meta != null" >
        meta = #{meta,jdbcType=LONGVARCHAR},
      </if>
      <if test="dag != null" >
        dag = #{dag,jdbcType=LONGVARCHAR},
      </if>
      <if test="subTasks != null" >
        sub_tasks = #{subTasks,jdbcType=LONGVARCHAR},
      </if>
      <if test="dagScript != null" >
        dag_script = #{dagScript,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInfoWithBLOBs" >
    update task_info
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      task_desc = #{taskDesc,jdbcType=VARCHAR},
      policy = #{policy,jdbcType=VARCHAR},
      engine_type = #{engineType,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      external_id = #{externalId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      app_name = #{appName,jdbcType=VARCHAR},
      time_expression = #{timeExpression,jdbcType=VARCHAR},
      meta = #{meta,jdbcType=LONGVARCHAR},
      dag = #{dag,jdbcType=LONGVARCHAR},
      sub_tasks = #{subTasks,jdbcType=LONGVARCHAR},
      dag_script = #{dagScript,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.wormhole.qanat.dal.domain.TaskInfo" >
    update task_info
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      task_desc = #{taskDesc,jdbcType=VARCHAR},
      policy = #{policy,jdbcType=VARCHAR},
      engine_type = #{engineType,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      external_id = #{externalId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      app_name = #{appName,jdbcType=VARCHAR},
      time_expression = #{timeExpression,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>