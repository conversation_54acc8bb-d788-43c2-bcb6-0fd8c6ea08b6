<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.wormhole.qanat.dal.mapper.DatasourceMapper" >
  <resultMap id="BaseResultMap" type="com.aliyun.wormhole.qanat.dal.domain.Datasource" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    <result column="ds_name" property="dsName" jdbcType="VARCHAR" />
    <result column="ds_desc" property="dsDesc" jdbcType="VARCHAR" />
    <result column="ds_type" property="dsType" jdbcType="VARCHAR" />
    <result column="create_empid" property="createEmpid" jdbcType="VARCHAR" />
    <result column="modify_empid" property="modifyEmpid" jdbcType="VARCHAR" />
    <result column="is_deleted" property="isDeleted" jdbcType="BIGINT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="db_name" property="dbName" jdbcType="VARCHAR" />
    <result column="object_type" property="objectType" jdbcType="VARCHAR" />
    <result column="ds_unique_name" property="dsUniqueName" jdbcType="VARCHAR" />
    <result column="pk_fields" property="pkFields" jdbcType="VARCHAR" />
    <result column="owner" property="owner" jdbcType="VARCHAR" />
    <result column="data_status" property="dataStatus" jdbcType="VARCHAR" />
    <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
    <result column="table_name" property="tableName" jdbcType="VARCHAR" />
    <result column="sys_type" property="sysType" jdbcType="VARCHAR" />
    <result column="catelog_name" property="catelogName" jdbcType="VARCHAR" />
    <result column="predict_size" property="predictSize" jdbcType="BIGINT" />
    <result column="size" property="size" jdbcType="BIGINT" />
    <result column="predict_qph" property="predictQph" jdbcType="INTEGER" />
    <result column="qph" property="qph" jdbcType="INTEGER" />
    <result column="work_time" property="workTime" jdbcType="VARCHAR" />
    <result column="data_size" property="dataSize" jdbcType="DECIMAL" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.aliyun.wormhole.qanat.dal.domain.Datasource" extends="BaseResultMap" >
    <result column="meta" property="meta" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gmt_create, gmt_modified, ds_name, ds_desc, ds_type, create_empid, modify_empid, 
    is_deleted, remark, db_name, object_type, ds_unique_name, pk_fields, `owner`, data_status, 
    tenant_id, `table_name`, sys_type, catelog_name, predict_size, `size`, predict_qph, 
    qph, work_time, data_size
  </sql>
  <sql id="Blob_Column_List" >
    meta
  </sql>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatasourceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from datasource
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatasourceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from datasource
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithBLOBsAndPage" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatasourceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from datasource
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleAndPage" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatasourceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from datasource
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatasourceExample" >
    delete from datasource
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <select id="countByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatasourceExample" resultType="java.lang.Integer" >
    select count(*) from datasource
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update datasource
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null" >
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dsName != null" >
        ds_name = #{record.dsName,jdbcType=VARCHAR},
      </if>
      <if test="record.dsDesc != null" >
        ds_desc = #{record.dsDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.dsType != null" >
        ds_type = #{record.dsType,jdbcType=VARCHAR},
      </if>
      <if test="record.createEmpid != null" >
        create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.modifyEmpid != null" >
        modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null" >
        is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.dbName != null" >
        db_name = #{record.dbName,jdbcType=VARCHAR},
      </if>
      <if test="record.objectType != null" >
        object_type = #{record.objectType,jdbcType=VARCHAR},
      </if>
      <if test="record.dsUniqueName != null" >
        ds_unique_name = #{record.dsUniqueName,jdbcType=VARCHAR},
      </if>
      <if test="record.pkFields != null" >
        pk_fields = #{record.pkFields,jdbcType=VARCHAR},
      </if>
      <if test="record.owner != null" >
        `owner` = #{record.owner,jdbcType=VARCHAR},
      </if>
      <if test="record.dataStatus != null" >
        data_status = #{record.dataStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null" >
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.tableName != null" >
        `table_name` = #{record.tableName,jdbcType=VARCHAR},
      </if>
      <if test="record.sysType != null" >
        sys_type = #{record.sysType,jdbcType=VARCHAR},
      </if>
      <if test="record.catelogName != null" >
        catelog_name = #{record.catelogName,jdbcType=VARCHAR},
      </if>
      <if test="record.predictSize != null" >
        predict_size = #{record.predictSize,jdbcType=BIGINT},
      </if>
      <if test="record.size != null" >
        `size` = #{record.size,jdbcType=BIGINT},
      </if>
      <if test="record.predictQph != null" >
        predict_qph = #{record.predictQph,jdbcType=INTEGER},
      </if>
      <if test="record.qph != null" >
        qph = #{record.qph,jdbcType=INTEGER},
      </if>
      <if test="record.workTime != null" >
        work_time = #{record.workTime,jdbcType=VARCHAR},
      </if>
      <if test="record.dataSize != null" >
        data_size = #{record.dataSize,jdbcType=DECIMAL},
      </if>
      <if test="record.meta != null" >
        meta = #{record.meta,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update datasource
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      ds_name = #{record.dsName,jdbcType=VARCHAR},
      ds_desc = #{record.dsDesc,jdbcType=VARCHAR},
      ds_type = #{record.dsType,jdbcType=VARCHAR},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      db_name = #{record.dbName,jdbcType=VARCHAR},
      object_type = #{record.objectType,jdbcType=VARCHAR},
      ds_unique_name = #{record.dsUniqueName,jdbcType=VARCHAR},
      pk_fields = #{record.pkFields,jdbcType=VARCHAR},
      `owner` = #{record.owner,jdbcType=VARCHAR},
      data_status = #{record.dataStatus,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      `table_name` = #{record.tableName,jdbcType=VARCHAR},
      sys_type = #{record.sysType,jdbcType=VARCHAR},
      catelog_name = #{record.catelogName,jdbcType=VARCHAR},
      predict_size = #{record.predictSize,jdbcType=BIGINT},
      `size` = #{record.size,jdbcType=BIGINT},
      predict_qph = #{record.predictQph,jdbcType=INTEGER},
      qph = #{record.qph,jdbcType=INTEGER},
      work_time = #{record.workTime,jdbcType=VARCHAR},
      data_size = #{record.dataSize,jdbcType=DECIMAL},
      meta = #{record.meta,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update datasource
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      ds_name = #{record.dsName,jdbcType=VARCHAR},
      ds_desc = #{record.dsDesc,jdbcType=VARCHAR},
      ds_type = #{record.dsType,jdbcType=VARCHAR},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      db_name = #{record.dbName,jdbcType=VARCHAR},
      object_type = #{record.objectType,jdbcType=VARCHAR},
      ds_unique_name = #{record.dsUniqueName,jdbcType=VARCHAR},
      pk_fields = #{record.pkFields,jdbcType=VARCHAR},
      `owner` = #{record.owner,jdbcType=VARCHAR},
      data_status = #{record.dataStatus,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      `table_name` = #{record.tableName,jdbcType=VARCHAR},
      sys_type = #{record.sysType,jdbcType=VARCHAR},
      catelog_name = #{record.catelogName,jdbcType=VARCHAR},
      predict_size = #{record.predictSize,jdbcType=BIGINT},
      `size` = #{record.size,jdbcType=BIGINT},
      predict_qph = #{record.predictQph,jdbcType=INTEGER},
      qph = #{record.qph,jdbcType=INTEGER},
      work_time = #{record.workTime,jdbcType=VARCHAR},
      data_size = #{record.dataSize,jdbcType=DECIMAL}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from datasource
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from datasource
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.aliyun.wormhole.qanat.dal.domain.Datasource" useGeneratedKeys="true" keyProperty="id" >
    insert into datasource (gmt_create, gmt_modified, ds_name, 
      ds_desc, ds_type, create_empid, 
      modify_empid, is_deleted, remark, 
      db_name, object_type, ds_unique_name, 
      pk_fields, `owner`, data_status, 
      tenant_id, `table_name`, sys_type, 
      catelog_name, predict_size, `size`, 
      predict_qph, qph, work_time, 
      data_size, meta)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{dsName,jdbcType=VARCHAR}, 
      #{dsDesc,jdbcType=VARCHAR}, #{dsType,jdbcType=VARCHAR}, #{createEmpid,jdbcType=VARCHAR}, 
      #{modifyEmpid,jdbcType=VARCHAR}, #{isDeleted,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR}, 
      #{dbName,jdbcType=VARCHAR}, #{objectType,jdbcType=VARCHAR}, #{dsUniqueName,jdbcType=VARCHAR}, 
      #{pkFields,jdbcType=VARCHAR}, #{owner,jdbcType=VARCHAR}, #{dataStatus,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=VARCHAR}, #{tableName,jdbcType=VARCHAR}, #{sysType,jdbcType=VARCHAR}, 
      #{catelogName,jdbcType=VARCHAR}, #{predictSize,jdbcType=BIGINT}, #{size,jdbcType=BIGINT}, 
      #{predictQph,jdbcType=INTEGER}, #{qph,jdbcType=INTEGER}, #{workTime,jdbcType=VARCHAR}, 
      #{dataSize,jdbcType=DECIMAL}, #{meta,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.Datasource" useGeneratedKeys="true" keyProperty="id" >
    insert into datasource
    <trim prefix="(" suffix=")" suffixOverrides="," >
      gmt_create,
      gmt_modified,
      ds_name,
      ds_desc,
      ds_type,
      create_empid,
      modify_empid,
      is_deleted,
      remark,
      db_name,
      object_type,
      ds_unique_name,
      pk_fields,
      `owner`,
      data_status,
      tenant_id,
      `table_name`,
      sys_type,
      catelog_name,
      predict_size,
      `size`,
      predict_qph,
      qph,
      work_time,
      data_size,
      meta,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{dsName,jdbcType=VARCHAR},
      #{dsDesc,jdbcType=VARCHAR},
      #{dsType,jdbcType=VARCHAR},
      #{createEmpid,jdbcType=VARCHAR},
      #{modifyEmpid,jdbcType=VARCHAR},
      #{isDeleted,jdbcType=BIGINT},
      #{remark,jdbcType=VARCHAR},
      #{dbName,jdbcType=VARCHAR},
      #{objectType,jdbcType=VARCHAR},
      #{dsUniqueName,jdbcType=VARCHAR},
      #{pkFields,jdbcType=VARCHAR},
      #{owner,jdbcType=VARCHAR},
      #{dataStatus,jdbcType=VARCHAR},
      #{tenantId,jdbcType=VARCHAR},
      #{tableName,jdbcType=VARCHAR},
      #{sysType,jdbcType=VARCHAR},
      #{catelogName,jdbcType=VARCHAR},
      #{predictSize,jdbcType=BIGINT},
      #{size,jdbcType=BIGINT},
      #{predictQph,jdbcType=INTEGER},
      #{qph,jdbcType=INTEGER},
      #{workTime,jdbcType=VARCHAR},
      #{dataSize,jdbcType=DECIMAL},
      #{meta,jdbcType=LONGVARCHAR},
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.Datasource" >
    update datasource
    <set >
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null" >
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="dsName != null" >
        ds_name = #{dsName,jdbcType=VARCHAR},
      </if>
      <if test="dsDesc != null" >
        ds_desc = #{dsDesc,jdbcType=VARCHAR},
      </if>
      <if test="dsType != null" >
        ds_type = #{dsType,jdbcType=VARCHAR},
      </if>
      <if test="createEmpid != null" >
        create_empid = #{createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="modifyEmpid != null" >
        modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="dbName != null" >
        db_name = #{dbName,jdbcType=VARCHAR},
      </if>
      <if test="objectType != null" >
        object_type = #{objectType,jdbcType=VARCHAR},
      </if>
      <if test="dsUniqueName != null" >
        ds_unique_name = #{dsUniqueName,jdbcType=VARCHAR},
      </if>
      <if test="pkFields != null" >
        pk_fields = #{pkFields,jdbcType=VARCHAR},
      </if>
      <if test="owner != null" >
        `owner` = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="dataStatus != null" >
        data_status = #{dataStatus,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="tableName != null" >
        `table_name` = #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="sysType != null" >
        sys_type = #{sysType,jdbcType=VARCHAR},
      </if>
      <if test="catelogName != null" >
        catelog_name = #{catelogName,jdbcType=VARCHAR},
      </if>
      <if test="predictSize != null" >
        predict_size = #{predictSize,jdbcType=BIGINT},
      </if>
      <if test="size != null" >
        `size` = #{size,jdbcType=BIGINT},
      </if>
      <if test="predictQph != null" >
        predict_qph = #{predictQph,jdbcType=INTEGER},
      </if>
      <if test="qph != null" >
        qph = #{qph,jdbcType=INTEGER},
      </if>
      <if test="workTime != null" >
        work_time = #{workTime,jdbcType=VARCHAR},
      </if>
      <if test="dataSize != null" >
        data_size = #{dataSize,jdbcType=DECIMAL},
      </if>
      <if test="meta != null" >
        meta = #{meta,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.Datasource" >
    update datasource
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      ds_name = #{dsName,jdbcType=VARCHAR},
      ds_desc = #{dsDesc,jdbcType=VARCHAR},
      ds_type = #{dsType,jdbcType=VARCHAR},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      db_name = #{dbName,jdbcType=VARCHAR},
      object_type = #{objectType,jdbcType=VARCHAR},
      ds_unique_name = #{dsUniqueName,jdbcType=VARCHAR},
      pk_fields = #{pkFields,jdbcType=VARCHAR},
      `owner` = #{owner,jdbcType=VARCHAR},
      data_status = #{dataStatus,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      `table_name` = #{tableName,jdbcType=VARCHAR},
      sys_type = #{sysType,jdbcType=VARCHAR},
      catelog_name = #{catelogName,jdbcType=VARCHAR},
      predict_size = #{predictSize,jdbcType=BIGINT},
      `size` = #{size,jdbcType=BIGINT},
      predict_qph = #{predictQph,jdbcType=INTEGER},
      qph = #{qph,jdbcType=INTEGER},
      work_time = #{workTime,jdbcType=VARCHAR},
      data_size = #{dataSize,jdbcType=DECIMAL},
      meta = #{meta,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.wormhole.qanat.dal.domain.Datasource" >
    update datasource
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      ds_name = #{dsName,jdbcType=VARCHAR},
      ds_desc = #{dsDesc,jdbcType=VARCHAR},
      ds_type = #{dsType,jdbcType=VARCHAR},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      db_name = #{dbName,jdbcType=VARCHAR},
      object_type = #{objectType,jdbcType=VARCHAR},
      ds_unique_name = #{dsUniqueName,jdbcType=VARCHAR},
      pk_fields = #{pkFields,jdbcType=VARCHAR},
      `owner` = #{owner,jdbcType=VARCHAR},
      data_status = #{dataStatus,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      `table_name` = #{tableName,jdbcType=VARCHAR},
      sys_type = #{sysType,jdbcType=VARCHAR},
      catelog_name = #{catelogName,jdbcType=VARCHAR},
      predict_size = #{predictSize,jdbcType=BIGINT},
      `size` = #{size,jdbcType=BIGINT},
      predict_qph = #{predictQph,jdbcType=INTEGER},
      qph = #{qph,jdbcType=INTEGER},
      work_time = #{workTime,jdbcType=VARCHAR},
      data_size = #{dataSize,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>