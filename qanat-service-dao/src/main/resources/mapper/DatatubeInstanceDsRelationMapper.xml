<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceDsRelationMapper" >
  <resultMap id="BaseResultMap" type="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceDsRelation" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    <result column="create_empid" property="createEmpid" jdbcType="VARCHAR" />
    <result column="modify_empid" property="modifyEmpid" jdbcType="VARCHAR" />
    <result column="datatube_inst_id" property="datatubeInstId" jdbcType="BIGINT" />
    <result column="ds_name" property="dsName" jdbcType="VARCHAR" />
    <result column="ds_type" property="dsType" jdbcType="VARCHAR" />
    <result column="relation_type" property="relationType" jdbcType="VARCHAR" />
    <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
    <result column="is_deleted" property="isDeleted" jdbcType="BIGINT" />
    <result column="version" property="version" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gmt_create, gmt_modified, create_empid, modify_empid, datatube_inst_id, ds_name, 
    ds_type, relation_type, tenant_id, is_deleted, version
  </sql>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceDsRelationExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from datatube_instance_ds_relation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleAndPage" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceDsRelationExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from datatube_instance_ds_relation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceDsRelationExample" >
    delete from datatube_instance_ds_relation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <select id="countByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceDsRelationExample" resultType="java.lang.Integer" >
    select count(*) from datatube_instance_ds_relation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update datatube_instance_ds_relation
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null" >
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createEmpid != null" >
        create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.modifyEmpid != null" >
        modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.datatubeInstId != null" >
        datatube_inst_id = #{record.datatubeInstId,jdbcType=BIGINT},
      </if>
      <if test="record.dsName != null" >
        ds_name = #{record.dsName,jdbcType=VARCHAR},
      </if>
      <if test="record.dsType != null" >
        ds_type = #{record.dsType,jdbcType=VARCHAR},
      </if>
      <if test="record.relationType != null" >
        relation_type = #{record.relationType,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null" >
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null" >
        is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      </if>
      <if test="record.version != null" >
        version = #{record.version,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update datatube_instance_ds_relation
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      datatube_inst_id = #{record.datatubeInstId,jdbcType=BIGINT},
      ds_name = #{record.dsName,jdbcType=VARCHAR},
      ds_type = #{record.dsType,jdbcType=VARCHAR},
      relation_type = #{record.relationType,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      version = #{record.version,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from datatube_instance_ds_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from datatube_instance_ds_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceDsRelation" useGeneratedKeys="true" keyProperty="id" >
    insert into datatube_instance_ds_relation (gmt_create, gmt_modified, create_empid, 
      modify_empid, datatube_inst_id, ds_name, 
      ds_type, relation_type, tenant_id, 
      is_deleted, version)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{createEmpid,jdbcType=VARCHAR}, 
      #{modifyEmpid,jdbcType=VARCHAR}, #{datatubeInstId,jdbcType=BIGINT}, #{dsName,jdbcType=VARCHAR}, 
      #{dsType,jdbcType=VARCHAR}, #{relationType,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=BIGINT}, #{version,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceDsRelation" useGeneratedKeys="true" keyProperty="id" >
    insert into datatube_instance_ds_relation
    <trim prefix="(" suffix=")" suffixOverrides="," >
      gmt_create,
      gmt_modified,
      create_empid,
      modify_empid,
      datatube_inst_id,
      ds_name,
      ds_type,
      relation_type,
      tenant_id,
      is_deleted,
      version,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{createEmpid,jdbcType=VARCHAR},
      #{modifyEmpid,jdbcType=VARCHAR},
      #{datatubeInstId,jdbcType=BIGINT},
      #{dsName,jdbcType=VARCHAR},
      #{dsType,jdbcType=VARCHAR},
      #{relationType,jdbcType=VARCHAR},
      #{tenantId,jdbcType=VARCHAR},
      #{isDeleted,jdbcType=BIGINT},
      #{version,jdbcType=INTEGER},
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceDsRelation" >
    update datatube_instance_ds_relation
    <set >
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null" >
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="createEmpid != null" >
        create_empid = #{createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="modifyEmpid != null" >
        modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="datatubeInstId != null" >
        datatube_inst_id = #{datatubeInstId,jdbcType=BIGINT},
      </if>
      <if test="dsName != null" >
        ds_name = #{dsName,jdbcType=VARCHAR},
      </if>
      <if test="dsType != null" >
        ds_type = #{dsType,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null" >
        relation_type = #{relationType,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceDsRelation" >
    update datatube_instance_ds_relation
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      datatube_inst_id = #{datatubeInstId,jdbcType=BIGINT},
      ds_name = #{dsName,jdbcType=VARCHAR},
      ds_type = #{dsType,jdbcType=VARCHAR},
      relation_type = #{relationType,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      version = #{version,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>