<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.wormhole.qanat.dal.mapper.ComponentDsRelationMapper" >
  <resultMap id="BaseResultMap" type="com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelation" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="component_name" property="componentName" jdbcType="VARCHAR" />
    <result column="ds_name" property="dsName" jdbcType="VARCHAR" />
    <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
    <result column="relation_type" property="relationType" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gmt_create, component_name, ds_name, tenant_id, relation_type
  </sql>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelationExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from component_ds_relation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleAndPage" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelationExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from component_ds_relation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelationExample" >
    delete from component_ds_relation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <select id="countByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelationExample" resultType="java.lang.Integer" >
    select count(*) from component_ds_relation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update component_ds_relation
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.componentName != null" >
        component_name = #{record.componentName,jdbcType=VARCHAR},
      </if>
      <if test="record.dsName != null" >
        ds_name = #{record.dsName,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null" >
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.relationType != null" >
        relation_type = #{record.relationType,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update component_ds_relation
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      component_name = #{record.componentName,jdbcType=VARCHAR},
      ds_name = #{record.dsName,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      relation_type = #{record.relationType,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from component_ds_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from component_ds_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelation" useGeneratedKeys="true" keyProperty="id" >
    insert into component_ds_relation (gmt_create, component_name, ds_name, 
      tenant_id, relation_type)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{componentName,jdbcType=VARCHAR}, #{dsName,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=VARCHAR}, #{relationType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelation" useGeneratedKeys="true" keyProperty="id" >
    insert into component_ds_relation
    <trim prefix="(" suffix=")" suffixOverrides="," >
      gmt_create,
      component_name,
      ds_name,
      tenant_id,
      relation_type,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{componentName,jdbcType=VARCHAR},
      #{dsName,jdbcType=VARCHAR},
      #{tenantId,jdbcType=VARCHAR},
      #{relationType,jdbcType=VARCHAR},
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelation" >
    update component_ds_relation
    <set >
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="componentName != null" >
        component_name = #{componentName,jdbcType=VARCHAR},
      </if>
      <if test="dsName != null" >
        ds_name = #{dsName,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null" >
        relation_type = #{relationType,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelation" >
    update component_ds_relation
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      component_name = #{componentName,jdbcType=VARCHAR},
      ds_name = #{dsName,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      relation_type = #{relationType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>