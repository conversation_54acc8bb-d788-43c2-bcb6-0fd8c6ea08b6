<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.wormhole.qanat.dal.mapper.DatatubeInstanceModelObjMapper" >
  <resultMap id="BaseResultMap" type="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelObj" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    <result column="create_empid" property="createEmpid" jdbcType="VARCHAR" />
    <result column="modify_empid" property="modifyEmpid" jdbcType="VARCHAR" />
    <result column="datatube_inst_id" property="datatubeInstId" jdbcType="BIGINT" />
    <result column="model_object_code" property="modelObjectCode" jdbcType="VARCHAR" />
    <result column="model_object_type" property="modelObjectType" jdbcType="VARCHAR" />
    <result column="ref_ds_name" property="refDsName" jdbcType="VARCHAR" />
    <result column="filter" property="filter" jdbcType="VARCHAR" />
    <result column="is_main" property="isMain" jdbcType="INTEGER" />
    <result column="is_lookup" property="isLookup" jdbcType="INTEGER" />
    <result column="lookup_from" property="lookupFrom" jdbcType="VARCHAR" />
    <result column="rel_type" property="relType" jdbcType="VARCHAR" />
    <result column="tpm" property="tpm" jdbcType="INTEGER" />
    <result column="sla" property="sla" jdbcType="INTEGER" />
    <result column="is_deleted" property="isDeleted" jdbcType="BIGINT" />
    <result column="flow_limit" property="flowLimit" jdbcType="DECIMAL" />
    <result column="lookup_flow_limit" property="lookupFlowLimit" jdbcType="DECIMAL" />
    <result column="lookup_sla" property="lookupSla" jdbcType="INTEGER" />
    <result column="fields" property="fields" jdbcType="VARCHAR" />
    <result column="ref_fields" property="refFields" jdbcType="VARCHAR" />
    <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gmt_create, gmt_modified, create_empid, modify_empid, datatube_inst_id, model_object_code, 
    model_object_type, ref_ds_name, `filter`, is_main, is_lookup, lookup_from, rel_type, 
    tpm, sla, is_deleted, flow_limit, lookup_flow_limit, lookup_sla, `fields`, ref_fields, 
    tenant_id
  </sql>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelObjExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from datatube_instance_model_obj
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleAndPage" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelObjExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from datatube_instance_model_obj
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelObjExample" >
    delete from datatube_instance_model_obj
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <select id="countByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelObjExample" resultType="java.lang.Integer" >
    select count(*) from datatube_instance_model_obj
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update datatube_instance_model_obj
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null" >
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createEmpid != null" >
        create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.modifyEmpid != null" >
        modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.datatubeInstId != null" >
        datatube_inst_id = #{record.datatubeInstId,jdbcType=BIGINT},
      </if>
      <if test="record.modelObjectCode != null" >
        model_object_code = #{record.modelObjectCode,jdbcType=VARCHAR},
      </if>
      <if test="record.modelObjectType != null" >
        model_object_type = #{record.modelObjectType,jdbcType=VARCHAR},
      </if>
      <if test="record.refDsName != null" >
        ref_ds_name = #{record.refDsName,jdbcType=VARCHAR},
      </if>
      <if test="record.filter != null" >
        `filter` = #{record.filter,jdbcType=VARCHAR},
      </if>
      <if test="record.isMain != null" >
        is_main = #{record.isMain,jdbcType=INTEGER},
      </if>
      <if test="record.isLookup != null" >
        is_lookup = #{record.isLookup,jdbcType=INTEGER},
      </if>
      <if test="record.lookupFrom != null" >
        lookup_from = #{record.lookupFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.relType != null" >
        rel_type = #{record.relType,jdbcType=VARCHAR},
      </if>
      <if test="record.tpm != null" >
        tpm = #{record.tpm,jdbcType=INTEGER},
      </if>
      <if test="record.sla != null" >
        sla = #{record.sla,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null" >
        is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      </if>
      <if test="record.flowLimit != null" >
        flow_limit = #{record.flowLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.lookupFlowLimit != null" >
        lookup_flow_limit = #{record.lookupFlowLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.lookupSla != null" >
        lookup_sla = #{record.lookupSla,jdbcType=INTEGER},
      </if>
      <if test="record.fields != null" >
        `fields` = #{record.fields,jdbcType=VARCHAR},
      </if>
      <if test="record.refFields != null" >
        ref_fields = #{record.refFields,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null" >
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update datatube_instance_model_obj
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      datatube_inst_id = #{record.datatubeInstId,jdbcType=BIGINT},
      model_object_code = #{record.modelObjectCode,jdbcType=VARCHAR},
      model_object_type = #{record.modelObjectType,jdbcType=VARCHAR},
      ref_ds_name = #{record.refDsName,jdbcType=VARCHAR},
      `filter` = #{record.filter,jdbcType=VARCHAR},
      is_main = #{record.isMain,jdbcType=INTEGER},
      is_lookup = #{record.isLookup,jdbcType=INTEGER},
      lookup_from = #{record.lookupFrom,jdbcType=VARCHAR},
      rel_type = #{record.relType,jdbcType=VARCHAR},
      tpm = #{record.tpm,jdbcType=INTEGER},
      sla = #{record.sla,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=BIGINT},
      flow_limit = #{record.flowLimit,jdbcType=DECIMAL},
      lookup_flow_limit = #{record.lookupFlowLimit,jdbcType=DECIMAL},
      lookup_sla = #{record.lookupSla,jdbcType=INTEGER},
      `fields` = #{record.fields,jdbcType=VARCHAR},
      ref_fields = #{record.refFields,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from datatube_instance_model_obj
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from datatube_instance_model_obj
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelObj" useGeneratedKeys="true" keyProperty="id" >
    insert into datatube_instance_model_obj (gmt_create, gmt_modified, create_empid, 
      modify_empid, datatube_inst_id, model_object_code, 
      model_object_type, ref_ds_name, `filter`, 
      is_main, is_lookup, lookup_from, 
      rel_type, tpm, sla, 
      is_deleted, flow_limit, lookup_flow_limit, 
      lookup_sla, `fields`, ref_fields, 
      tenant_id)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{createEmpid,jdbcType=VARCHAR}, 
      #{modifyEmpid,jdbcType=VARCHAR}, #{datatubeInstId,jdbcType=BIGINT}, #{modelObjectCode,jdbcType=VARCHAR}, 
      #{modelObjectType,jdbcType=VARCHAR}, #{refDsName,jdbcType=VARCHAR}, #{filter,jdbcType=VARCHAR}, 
      #{isMain,jdbcType=INTEGER}, #{isLookup,jdbcType=INTEGER}, #{lookupFrom,jdbcType=VARCHAR}, 
      #{relType,jdbcType=VARCHAR}, #{tpm,jdbcType=INTEGER}, #{sla,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=BIGINT}, #{flowLimit,jdbcType=DECIMAL}, #{lookupFlowLimit,jdbcType=DECIMAL}, 
      #{lookupSla,jdbcType=INTEGER}, #{fields,jdbcType=VARCHAR}, #{refFields,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelObj" useGeneratedKeys="true" keyProperty="id" >
    insert into datatube_instance_model_obj
    <trim prefix="(" suffix=")" suffixOverrides="," >
      gmt_create,
      gmt_modified,
      create_empid,
      modify_empid,
      datatube_inst_id,
      model_object_code,
      model_object_type,
      ref_ds_name,
      `filter`,
      is_main,
      is_lookup,
      lookup_from,
      rel_type,
      tpm,
      sla,
      is_deleted,
      flow_limit,
      lookup_flow_limit,
      lookup_sla,
      `fields`,
      ref_fields,
      tenant_id,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{createEmpid,jdbcType=VARCHAR},
      #{modifyEmpid,jdbcType=VARCHAR},
      #{datatubeInstId,jdbcType=BIGINT},
      #{modelObjectCode,jdbcType=VARCHAR},
      #{modelObjectType,jdbcType=VARCHAR},
      #{refDsName,jdbcType=VARCHAR},
      #{filter,jdbcType=VARCHAR},
      #{isMain,jdbcType=INTEGER},
      #{isLookup,jdbcType=INTEGER},
      #{lookupFrom,jdbcType=VARCHAR},
      #{relType,jdbcType=VARCHAR},
      #{tpm,jdbcType=INTEGER},
      #{sla,jdbcType=INTEGER},
      #{isDeleted,jdbcType=BIGINT},
      #{flowLimit,jdbcType=DECIMAL},
      #{lookupFlowLimit,jdbcType=DECIMAL},
      #{lookupSla,jdbcType=INTEGER},
      #{fields,jdbcType=VARCHAR},
      #{refFields,jdbcType=VARCHAR},
      #{tenantId,jdbcType=VARCHAR},
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelObj" >
    update datatube_instance_model_obj
    <set >
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null" >
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="createEmpid != null" >
        create_empid = #{createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="modifyEmpid != null" >
        modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="datatubeInstId != null" >
        datatube_inst_id = #{datatubeInstId,jdbcType=BIGINT},
      </if>
      <if test="modelObjectCode != null" >
        model_object_code = #{modelObjectCode,jdbcType=VARCHAR},
      </if>
      <if test="modelObjectType != null" >
        model_object_type = #{modelObjectType,jdbcType=VARCHAR},
      </if>
      <if test="refDsName != null" >
        ref_ds_name = #{refDsName,jdbcType=VARCHAR},
      </if>
      <if test="filter != null" >
        `filter` = #{filter,jdbcType=VARCHAR},
      </if>
      <if test="isMain != null" >
        is_main = #{isMain,jdbcType=INTEGER},
      </if>
      <if test="isLookup != null" >
        is_lookup = #{isLookup,jdbcType=INTEGER},
      </if>
      <if test="lookupFrom != null" >
        lookup_from = #{lookupFrom,jdbcType=VARCHAR},
      </if>
      <if test="relType != null" >
        rel_type = #{relType,jdbcType=VARCHAR},
      </if>
      <if test="tpm != null" >
        tpm = #{tpm,jdbcType=INTEGER},
      </if>
      <if test="sla != null" >
        sla = #{sla,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="flowLimit != null" >
        flow_limit = #{flowLimit,jdbcType=DECIMAL},
      </if>
      <if test="lookupFlowLimit != null" >
        lookup_flow_limit = #{lookupFlowLimit,jdbcType=DECIMAL},
      </if>
      <if test="lookupSla != null" >
        lookup_sla = #{lookupSla,jdbcType=INTEGER},
      </if>
      <if test="fields != null" >
        `fields` = #{fields,jdbcType=VARCHAR},
      </if>
      <if test="refFields != null" >
        ref_fields = #{refFields,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelObj" >
    update datatube_instance_model_obj
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      datatube_inst_id = #{datatubeInstId,jdbcType=BIGINT},
      model_object_code = #{modelObjectCode,jdbcType=VARCHAR},
      model_object_type = #{modelObjectType,jdbcType=VARCHAR},
      ref_ds_name = #{refDsName,jdbcType=VARCHAR},
      `filter` = #{filter,jdbcType=VARCHAR},
      is_main = #{isMain,jdbcType=INTEGER},
      is_lookup = #{isLookup,jdbcType=INTEGER},
      lookup_from = #{lookupFrom,jdbcType=VARCHAR},
      rel_type = #{relType,jdbcType=VARCHAR},
      tpm = #{tpm,jdbcType=INTEGER},
      sla = #{sla,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      flow_limit = #{flowLimit,jdbcType=DECIMAL},
      lookup_flow_limit = #{lookupFlowLimit,jdbcType=DECIMAL},
      lookup_sla = #{lookupSla,jdbcType=INTEGER},
      `fields` = #{fields,jdbcType=VARCHAR},
      ref_fields = #{refFields,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>