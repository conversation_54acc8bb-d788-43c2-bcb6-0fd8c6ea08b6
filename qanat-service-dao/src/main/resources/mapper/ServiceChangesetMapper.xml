<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "mybatis-3-mapper.dtd" >
<mapper namespace="com.aliyun.wormhole.qanat.dal.mapper.ServiceChangesetMapper" >
  <resultMap id="BaseResultMap" type="com.aliyun.wormhole.qanat.dal.domain.ServiceChangeset" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    <result column="cs_type" property="csType" jdbcType="VARCHAR" />
    <result column="cs_desc" property="csDesc" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_empid" property="createEmpid" jdbcType="VARCHAR" />
    <result column="modify_empid" property="modifyEmpid" jdbcType="VARCHAR" />
    <result column="service_id" property="serviceId" jdbcType="BIGINT" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.aliyun.wormhole.qanat.dal.domain.ServiceChangesetWithBLOBs" extends="BaseResultMap" >
    <result column="participants" property="participants" jdbcType="LONGVARCHAR" />
    <result column="dag_script" property="dagScript" jdbcType="LONGVARCHAR" />
    <result column="ref_ds_list" property="refDsList" jdbcType="LONGVARCHAR" />
    <result column="meta" property="meta" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gmt_create, gmt_modified, cs_type, cs_desc, remark, `status`, create_empid, modify_empid, 
    service_id
  </sql>
  <sql id="Blob_Column_List" >
    participants, dag_script, ref_ds_list, meta
  </sql>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.ServiceChangesetExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from service_changeset
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.ServiceChangesetExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from service_changeset
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithBLOBsAndPage" resultMap="ResultMapWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.ServiceChangesetExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from service_changeset
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleAndPage" resultMap="BaseResultMap" parameterType="com.aliyun.wormhole.qanat.dal.domain.ServiceChangesetExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from service_changeset
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.ServiceChangesetExample" >
    delete from service_changeset
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <select id="countByExample" parameterType="com.aliyun.wormhole.qanat.dal.domain.ServiceChangesetExample" resultType="java.lang.Integer" >
    select count(*) from service_changeset
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update service_changeset
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null" >
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.csType != null" >
        cs_type = #{record.csType,jdbcType=VARCHAR},
      </if>
      <if test="record.csDesc != null" >
        cs_desc = #{record.csDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createEmpid != null" >
        create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.modifyEmpid != null" >
        modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceId != null" >
        service_id = #{record.serviceId,jdbcType=BIGINT},
      </if>
      <if test="record.participants != null" >
        participants = #{record.participants,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.dagScript != null" >
        dag_script = #{record.dagScript,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.refDsList != null" >
        ref_ds_list = #{record.refDsList,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.meta != null" >
        meta = #{record.meta,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update service_changeset
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      cs_type = #{record.csType,jdbcType=VARCHAR},
      cs_desc = #{record.csDesc,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=INTEGER},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      service_id = #{record.serviceId,jdbcType=BIGINT},
      participants = #{record.participants,jdbcType=LONGVARCHAR},
      dag_script = #{record.dagScript,jdbcType=LONGVARCHAR},
      ref_ds_list = #{record.refDsList,jdbcType=LONGVARCHAR},
      meta = #{record.meta,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update service_changeset
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      cs_type = #{record.csType,jdbcType=VARCHAR},
      cs_desc = #{record.csDesc,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=INTEGER},
      create_empid = #{record.createEmpid,jdbcType=VARCHAR},
      modify_empid = #{record.modifyEmpid,jdbcType=VARCHAR},
      service_id = #{record.serviceId,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from service_changeset
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from service_changeset
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.aliyun.wormhole.qanat.dal.domain.ServiceChangesetWithBLOBs" useGeneratedKeys="true" keyProperty="id" >
    insert into service_changeset (gmt_create, gmt_modified, cs_type, 
      cs_desc, remark, `status`, 
      create_empid, modify_empid, service_id, 
      participants, dag_script, ref_ds_list, 
      meta)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{csType,jdbcType=VARCHAR}, 
      #{csDesc,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{createEmpid,jdbcType=VARCHAR}, #{modifyEmpid,jdbcType=VARCHAR}, #{serviceId,jdbcType=BIGINT}, 
      #{participants,jdbcType=LONGVARCHAR}, #{dagScript,jdbcType=LONGVARCHAR}, #{refDsList,jdbcType=LONGVARCHAR}, 
      #{meta,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.ServiceChangesetWithBLOBs" useGeneratedKeys="true" keyProperty="id" >
    insert into service_changeset
    <trim prefix="(" suffix=")" suffixOverrides="," >
      gmt_create,
      gmt_modified,
      cs_type,
      cs_desc,
      remark,
      `status`,
      create_empid,
      modify_empid,
      service_id,
      participants,
      dag_script,
      ref_ds_list,
      meta,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{csType,jdbcType=VARCHAR},
      #{csDesc,jdbcType=VARCHAR},
      #{remark,jdbcType=VARCHAR},
      #{status,jdbcType=INTEGER},
      #{createEmpid,jdbcType=VARCHAR},
      #{modifyEmpid,jdbcType=VARCHAR},
      #{serviceId,jdbcType=BIGINT},
      #{participants,jdbcType=LONGVARCHAR},
      #{dagScript,jdbcType=LONGVARCHAR},
      #{refDsList,jdbcType=LONGVARCHAR},
      #{meta,jdbcType=LONGVARCHAR},
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.wormhole.qanat.dal.domain.ServiceChangesetWithBLOBs" >
    update service_changeset
    <set >
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null" >
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="csType != null" >
        cs_type = #{csType,jdbcType=VARCHAR},
      </if>
      <if test="csDesc != null" >
        cs_desc = #{csDesc,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createEmpid != null" >
        create_empid = #{createEmpid,jdbcType=VARCHAR},
      </if>
      <if test="modifyEmpid != null" >
        modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      </if>
      <if test="serviceId != null" >
        service_id = #{serviceId,jdbcType=BIGINT},
      </if>
      <if test="participants != null" >
        participants = #{participants,jdbcType=LONGVARCHAR},
      </if>
      <if test="dagScript != null" >
        dag_script = #{dagScript,jdbcType=LONGVARCHAR},
      </if>
      <if test="refDsList != null" >
        ref_ds_list = #{refDsList,jdbcType=LONGVARCHAR},
      </if>
      <if test="meta != null" >
        meta = #{meta,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aliyun.wormhole.qanat.dal.domain.ServiceChangesetWithBLOBs" >
    update service_changeset
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      cs_type = #{csType,jdbcType=VARCHAR},
      cs_desc = #{csDesc,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      service_id = #{serviceId,jdbcType=BIGINT},
      participants = #{participants,jdbcType=LONGVARCHAR},
      dag_script = #{dagScript,jdbcType=LONGVARCHAR},
      ref_ds_list = #{refDsList,jdbcType=LONGVARCHAR},
      meta = #{meta,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.wormhole.qanat.dal.domain.ServiceChangeset" >
    update service_changeset
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      cs_type = #{csType,jdbcType=VARCHAR},
      cs_desc = #{csDesc,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      create_empid = #{createEmpid,jdbcType=VARCHAR},
      modify_empid = #{modifyEmpid,jdbcType=VARCHAR},
      service_id = #{serviceId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>