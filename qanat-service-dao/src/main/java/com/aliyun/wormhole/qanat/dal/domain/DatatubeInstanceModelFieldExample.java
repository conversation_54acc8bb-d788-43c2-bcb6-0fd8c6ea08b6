package com.aliyun.wormhole.qanat.dal.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DatatubeInstanceModelFieldExample {
    protected String pk_name = "id";

    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DatatubeInstanceModelFieldExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setPk_name(String pk_name) {
        this.pk_name = pk_name;
    }

    public String getPk_name() {
        return pk_name;
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIsNull() {
            addCriterion("create_empid is null");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIsNotNull() {
            addCriterion("create_empid is not null");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidEqualTo(String value) {
            addCriterion("create_empid =", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotEqualTo(String value) {
            addCriterion("create_empid <>", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidGreaterThan(String value) {
            addCriterion("create_empid >", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidGreaterThanOrEqualTo(String value) {
            addCriterion("create_empid >=", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLessThan(String value) {
            addCriterion("create_empid <", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLessThanOrEqualTo(String value) {
            addCriterion("create_empid <=", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLike(String value) {
            addCriterion("create_empid like", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotLike(String value) {
            addCriterion("create_empid not like", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIn(List<String> values) {
            addCriterion("create_empid in", values, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotIn(List<String> values) {
            addCriterion("create_empid not in", values, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidBetween(String value1, String value2) {
            addCriterion("create_empid between", value1, value2, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotBetween(String value1, String value2) {
            addCriterion("create_empid not between", value1, value2, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIsNull() {
            addCriterion("modify_empid is null");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIsNotNull() {
            addCriterion("modify_empid is not null");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidEqualTo(String value) {
            addCriterion("modify_empid =", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotEqualTo(String value) {
            addCriterion("modify_empid <>", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidGreaterThan(String value) {
            addCriterion("modify_empid >", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidGreaterThanOrEqualTo(String value) {
            addCriterion("modify_empid >=", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLessThan(String value) {
            addCriterion("modify_empid <", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLessThanOrEqualTo(String value) {
            addCriterion("modify_empid <=", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLike(String value) {
            addCriterion("modify_empid like", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotLike(String value) {
            addCriterion("modify_empid not like", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIn(List<String> values) {
            addCriterion("modify_empid in", values, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotIn(List<String> values) {
            addCriterion("modify_empid not in", values, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidBetween(String value1, String value2) {
            addCriterion("modify_empid between", value1, value2, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotBetween(String value1, String value2) {
            addCriterion("modify_empid not between", value1, value2, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andFieldNameIsNull() {
            addCriterion("field_name is null");
            return (Criteria) this;
        }

        public Criteria andFieldNameIsNotNull() {
            addCriterion("field_name is not null");
            return (Criteria) this;
        }

        public Criteria andFieldNameEqualTo(String value) {
            addCriterion("field_name =", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameNotEqualTo(String value) {
            addCriterion("field_name <>", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameGreaterThan(String value) {
            addCriterion("field_name >", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameGreaterThanOrEqualTo(String value) {
            addCriterion("field_name >=", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameLessThan(String value) {
            addCriterion("field_name <", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameLessThanOrEqualTo(String value) {
            addCriterion("field_name <=", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameLike(String value) {
            addCriterion("field_name like", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameNotLike(String value) {
            addCriterion("field_name not like", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameIn(List<String> values) {
            addCriterion("field_name in", values, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameNotIn(List<String> values) {
            addCriterion("field_name not in", values, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameBetween(String value1, String value2) {
            addCriterion("field_name between", value1, value2, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameNotBetween(String value1, String value2) {
            addCriterion("field_name not between", value1, value2, "fieldName");
            return (Criteria) this;
        }

        public Criteria andRefFieldNameIsNull() {
            addCriterion("ref_field_name is null");
            return (Criteria) this;
        }

        public Criteria andRefFieldNameIsNotNull() {
            addCriterion("ref_field_name is not null");
            return (Criteria) this;
        }

        public Criteria andRefFieldNameEqualTo(String value) {
            addCriterion("ref_field_name =", value, "refFieldName");
            return (Criteria) this;
        }

        public Criteria andRefFieldNameNotEqualTo(String value) {
            addCriterion("ref_field_name <>", value, "refFieldName");
            return (Criteria) this;
        }

        public Criteria andRefFieldNameGreaterThan(String value) {
            addCriterion("ref_field_name >", value, "refFieldName");
            return (Criteria) this;
        }

        public Criteria andRefFieldNameGreaterThanOrEqualTo(String value) {
            addCriterion("ref_field_name >=", value, "refFieldName");
            return (Criteria) this;
        }

        public Criteria andRefFieldNameLessThan(String value) {
            addCriterion("ref_field_name <", value, "refFieldName");
            return (Criteria) this;
        }

        public Criteria andRefFieldNameLessThanOrEqualTo(String value) {
            addCriterion("ref_field_name <=", value, "refFieldName");
            return (Criteria) this;
        }

        public Criteria andRefFieldNameLike(String value) {
            addCriterion("ref_field_name like", value, "refFieldName");
            return (Criteria) this;
        }

        public Criteria andRefFieldNameNotLike(String value) {
            addCriterion("ref_field_name not like", value, "refFieldName");
            return (Criteria) this;
        }

        public Criteria andRefFieldNameIn(List<String> values) {
            addCriterion("ref_field_name in", values, "refFieldName");
            return (Criteria) this;
        }

        public Criteria andRefFieldNameNotIn(List<String> values) {
            addCriterion("ref_field_name not in", values, "refFieldName");
            return (Criteria) this;
        }

        public Criteria andRefFieldNameBetween(String value1, String value2) {
            addCriterion("ref_field_name between", value1, value2, "refFieldName");
            return (Criteria) this;
        }

        public Criteria andRefFieldNameNotBetween(String value1, String value2) {
            addCriterion("ref_field_name not between", value1, value2, "refFieldName");
            return (Criteria) this;
        }

        public Criteria andFieldTypeIsNull() {
            addCriterion("field_type is null");
            return (Criteria) this;
        }

        public Criteria andFieldTypeIsNotNull() {
            addCriterion("field_type is not null");
            return (Criteria) this;
        }

        public Criteria andFieldTypeEqualTo(String value) {
            addCriterion("field_type =", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeNotEqualTo(String value) {
            addCriterion("field_type <>", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeGreaterThan(String value) {
            addCriterion("field_type >", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeGreaterThanOrEqualTo(String value) {
            addCriterion("field_type >=", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeLessThan(String value) {
            addCriterion("field_type <", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeLessThanOrEqualTo(String value) {
            addCriterion("field_type <=", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeLike(String value) {
            addCriterion("field_type like", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeNotLike(String value) {
            addCriterion("field_type not like", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeIn(List<String> values) {
            addCriterion("field_type in", values, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeNotIn(List<String> values) {
            addCriterion("field_type not in", values, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeBetween(String value1, String value2) {
            addCriterion("field_type between", value1, value2, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeNotBetween(String value1, String value2) {
            addCriterion("field_type not between", value1, value2, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldDescIsNull() {
            addCriterion("field_desc is null");
            return (Criteria) this;
        }

        public Criteria andFieldDescIsNotNull() {
            addCriterion("field_desc is not null");
            return (Criteria) this;
        }

        public Criteria andFieldDescEqualTo(String value) {
            addCriterion("field_desc =", value, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescNotEqualTo(String value) {
            addCriterion("field_desc <>", value, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescGreaterThan(String value) {
            addCriterion("field_desc >", value, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescGreaterThanOrEqualTo(String value) {
            addCriterion("field_desc >=", value, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescLessThan(String value) {
            addCriterion("field_desc <", value, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescLessThanOrEqualTo(String value) {
            addCriterion("field_desc <=", value, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescLike(String value) {
            addCriterion("field_desc like", value, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescNotLike(String value) {
            addCriterion("field_desc not like", value, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescIn(List<String> values) {
            addCriterion("field_desc in", values, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescNotIn(List<String> values) {
            addCriterion("field_desc not in", values, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescBetween(String value1, String value2) {
            addCriterion("field_desc between", value1, value2, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescNotBetween(String value1, String value2) {
            addCriterion("field_desc not between", value1, value2, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andIsMultivalueIsNull() {
            addCriterion("is_multivalue is null");
            return (Criteria) this;
        }

        public Criteria andIsMultivalueIsNotNull() {
            addCriterion("is_multivalue is not null");
            return (Criteria) this;
        }

        public Criteria andIsMultivalueEqualTo(Integer value) {
            addCriterion("is_multivalue =", value, "isMultivalue");
            return (Criteria) this;
        }

        public Criteria andIsMultivalueNotEqualTo(Integer value) {
            addCriterion("is_multivalue <>", value, "isMultivalue");
            return (Criteria) this;
        }

        public Criteria andIsMultivalueGreaterThan(Integer value) {
            addCriterion("is_multivalue >", value, "isMultivalue");
            return (Criteria) this;
        }

        public Criteria andIsMultivalueGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_multivalue >=", value, "isMultivalue");
            return (Criteria) this;
        }

        public Criteria andIsMultivalueLessThan(Integer value) {
            addCriterion("is_multivalue <", value, "isMultivalue");
            return (Criteria) this;
        }

        public Criteria andIsMultivalueLessThanOrEqualTo(Integer value) {
            addCriterion("is_multivalue <=", value, "isMultivalue");
            return (Criteria) this;
        }

        public Criteria andIsMultivalueIn(List<Integer> values) {
            addCriterion("is_multivalue in", values, "isMultivalue");
            return (Criteria) this;
        }

        public Criteria andIsMultivalueNotIn(List<Integer> values) {
            addCriterion("is_multivalue not in", values, "isMultivalue");
            return (Criteria) this;
        }

        public Criteria andIsMultivalueBetween(Integer value1, Integer value2) {
            addCriterion("is_multivalue between", value1, value2, "isMultivalue");
            return (Criteria) this;
        }

        public Criteria andIsMultivalueNotBetween(Integer value1, Integer value2) {
            addCriterion("is_multivalue not between", value1, value2, "isMultivalue");
            return (Criteria) this;
        }

        public Criteria andIsPkIsNull() {
            addCriterion("is_pk is null");
            return (Criteria) this;
        }

        public Criteria andIsPkIsNotNull() {
            addCriterion("is_pk is not null");
            return (Criteria) this;
        }

        public Criteria andIsPkEqualTo(Integer value) {
            addCriterion("is_pk =", value, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkNotEqualTo(Integer value) {
            addCriterion("is_pk <>", value, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkGreaterThan(Integer value) {
            addCriterion("is_pk >", value, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_pk >=", value, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkLessThan(Integer value) {
            addCriterion("is_pk <", value, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkLessThanOrEqualTo(Integer value) {
            addCriterion("is_pk <=", value, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkIn(List<Integer> values) {
            addCriterion("is_pk in", values, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkNotIn(List<Integer> values) {
            addCriterion("is_pk not in", values, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkBetween(Integer value1, Integer value2) {
            addCriterion("is_pk between", value1, value2, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkNotBetween(Integer value1, Integer value2) {
            addCriterion("is_pk not between", value1, value2, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsFkIsNull() {
            addCriterion("is_fk is null");
            return (Criteria) this;
        }

        public Criteria andIsFkIsNotNull() {
            addCriterion("is_fk is not null");
            return (Criteria) this;
        }

        public Criteria andIsFkEqualTo(Integer value) {
            addCriterion("is_fk =", value, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkNotEqualTo(Integer value) {
            addCriterion("is_fk <>", value, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkGreaterThan(Integer value) {
            addCriterion("is_fk >", value, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_fk >=", value, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkLessThan(Integer value) {
            addCriterion("is_fk <", value, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkLessThanOrEqualTo(Integer value) {
            addCriterion("is_fk <=", value, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkIn(List<Integer> values) {
            addCriterion("is_fk in", values, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkNotIn(List<Integer> values) {
            addCriterion("is_fk not in", values, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkBetween(Integer value1, Integer value2) {
            addCriterion("is_fk between", value1, value2, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkNotBetween(Integer value1, Integer value2) {
            addCriterion("is_fk not between", value1, value2, "isFk");
            return (Criteria) this;
        }

        public Criteria andMvTokenIsNull() {
            addCriterion("mv_token is null");
            return (Criteria) this;
        }

        public Criteria andMvTokenIsNotNull() {
            addCriterion("mv_token is not null");
            return (Criteria) this;
        }

        public Criteria andMvTokenEqualTo(String value) {
            addCriterion("mv_token =", value, "mvToken");
            return (Criteria) this;
        }

        public Criteria andMvTokenNotEqualTo(String value) {
            addCriterion("mv_token <>", value, "mvToken");
            return (Criteria) this;
        }

        public Criteria andMvTokenGreaterThan(String value) {
            addCriterion("mv_token >", value, "mvToken");
            return (Criteria) this;
        }

        public Criteria andMvTokenGreaterThanOrEqualTo(String value) {
            addCriterion("mv_token >=", value, "mvToken");
            return (Criteria) this;
        }

        public Criteria andMvTokenLessThan(String value) {
            addCriterion("mv_token <", value, "mvToken");
            return (Criteria) this;
        }

        public Criteria andMvTokenLessThanOrEqualTo(String value) {
            addCriterion("mv_token <=", value, "mvToken");
            return (Criteria) this;
        }

        public Criteria andMvTokenLike(String value) {
            addCriterion("mv_token like", value, "mvToken");
            return (Criteria) this;
        }

        public Criteria andMvTokenNotLike(String value) {
            addCriterion("mv_token not like", value, "mvToken");
            return (Criteria) this;
        }

        public Criteria andMvTokenIn(List<String> values) {
            addCriterion("mv_token in", values, "mvToken");
            return (Criteria) this;
        }

        public Criteria andMvTokenNotIn(List<String> values) {
            addCriterion("mv_token not in", values, "mvToken");
            return (Criteria) this;
        }

        public Criteria andMvTokenBetween(String value1, String value2) {
            addCriterion("mv_token between", value1, value2, "mvToken");
            return (Criteria) this;
        }

        public Criteria andMvTokenNotBetween(String value1, String value2) {
            addCriterion("mv_token not between", value1, value2, "mvToken");
            return (Criteria) this;
        }

        public Criteria andIsFuncIsNull() {
            addCriterion("is_func is null");
            return (Criteria) this;
        }

        public Criteria andIsFuncIsNotNull() {
            addCriterion("is_func is not null");
            return (Criteria) this;
        }

        public Criteria andIsFuncEqualTo(Integer value) {
            addCriterion("is_func =", value, "isFunc");
            return (Criteria) this;
        }

        public Criteria andIsFuncNotEqualTo(Integer value) {
            addCriterion("is_func <>", value, "isFunc");
            return (Criteria) this;
        }

        public Criteria andIsFuncGreaterThan(Integer value) {
            addCriterion("is_func >", value, "isFunc");
            return (Criteria) this;
        }

        public Criteria andIsFuncGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_func >=", value, "isFunc");
            return (Criteria) this;
        }

        public Criteria andIsFuncLessThan(Integer value) {
            addCriterion("is_func <", value, "isFunc");
            return (Criteria) this;
        }

        public Criteria andIsFuncLessThanOrEqualTo(Integer value) {
            addCriterion("is_func <=", value, "isFunc");
            return (Criteria) this;
        }

        public Criteria andIsFuncIn(List<Integer> values) {
            addCriterion("is_func in", values, "isFunc");
            return (Criteria) this;
        }

        public Criteria andIsFuncNotIn(List<Integer> values) {
            addCriterion("is_func not in", values, "isFunc");
            return (Criteria) this;
        }

        public Criteria andIsFuncBetween(Integer value1, Integer value2) {
            addCriterion("is_func between", value1, value2, "isFunc");
            return (Criteria) this;
        }

        public Criteria andIsFuncNotBetween(Integer value1, Integer value2) {
            addCriterion("is_func not between", value1, value2, "isFunc");
            return (Criteria) this;
        }

        public Criteria andExtRefFieldsIsNull() {
            addCriterion("ext_ref_fields is null");
            return (Criteria) this;
        }

        public Criteria andExtRefFieldsIsNotNull() {
            addCriterion("ext_ref_fields is not null");
            return (Criteria) this;
        }

        public Criteria andExtRefFieldsEqualTo(String value) {
            addCriterion("ext_ref_fields =", value, "extRefFields");
            return (Criteria) this;
        }

        public Criteria andExtRefFieldsNotEqualTo(String value) {
            addCriterion("ext_ref_fields <>", value, "extRefFields");
            return (Criteria) this;
        }

        public Criteria andExtRefFieldsGreaterThan(String value) {
            addCriterion("ext_ref_fields >", value, "extRefFields");
            return (Criteria) this;
        }

        public Criteria andExtRefFieldsGreaterThanOrEqualTo(String value) {
            addCriterion("ext_ref_fields >=", value, "extRefFields");
            return (Criteria) this;
        }

        public Criteria andExtRefFieldsLessThan(String value) {
            addCriterion("ext_ref_fields <", value, "extRefFields");
            return (Criteria) this;
        }

        public Criteria andExtRefFieldsLessThanOrEqualTo(String value) {
            addCriterion("ext_ref_fields <=", value, "extRefFields");
            return (Criteria) this;
        }

        public Criteria andExtRefFieldsLike(String value) {
            addCriterion("ext_ref_fields like", value, "extRefFields");
            return (Criteria) this;
        }

        public Criteria andExtRefFieldsNotLike(String value) {
            addCriterion("ext_ref_fields not like", value, "extRefFields");
            return (Criteria) this;
        }

        public Criteria andExtRefFieldsIn(List<String> values) {
            addCriterion("ext_ref_fields in", values, "extRefFields");
            return (Criteria) this;
        }

        public Criteria andExtRefFieldsNotIn(List<String> values) {
            addCriterion("ext_ref_fields not in", values, "extRefFields");
            return (Criteria) this;
        }

        public Criteria andExtRefFieldsBetween(String value1, String value2) {
            addCriterion("ext_ref_fields between", value1, value2, "extRefFields");
            return (Criteria) this;
        }

        public Criteria andExtRefFieldsNotBetween(String value1, String value2) {
            addCriterion("ext_ref_fields not between", value1, value2, "extRefFields");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdIsNull() {
            addCriterion("datatube_inst_id is null");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdIsNotNull() {
            addCriterion("datatube_inst_id is not null");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdEqualTo(Long value) {
            addCriterion("datatube_inst_id =", value, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdNotEqualTo(Long value) {
            addCriterion("datatube_inst_id <>", value, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdGreaterThan(Long value) {
            addCriterion("datatube_inst_id >", value, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdGreaterThanOrEqualTo(Long value) {
            addCriterion("datatube_inst_id >=", value, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdLessThan(Long value) {
            addCriterion("datatube_inst_id <", value, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdLessThanOrEqualTo(Long value) {
            addCriterion("datatube_inst_id <=", value, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdIn(List<Long> values) {
            addCriterion("datatube_inst_id in", values, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdNotIn(List<Long> values) {
            addCriterion("datatube_inst_id not in", values, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdBetween(Long value1, Long value2) {
            addCriterion("datatube_inst_id between", value1, value2, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdNotBetween(Long value1, Long value2) {
            addCriterion("datatube_inst_id not between", value1, value2, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeIsNull() {
            addCriterion("model_object_code is null");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeIsNotNull() {
            addCriterion("model_object_code is not null");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeEqualTo(String value) {
            addCriterion("model_object_code =", value, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeNotEqualTo(String value) {
            addCriterion("model_object_code <>", value, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeGreaterThan(String value) {
            addCriterion("model_object_code >", value, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("model_object_code >=", value, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeLessThan(String value) {
            addCriterion("model_object_code <", value, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeLessThanOrEqualTo(String value) {
            addCriterion("model_object_code <=", value, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeLike(String value) {
            addCriterion("model_object_code like", value, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeNotLike(String value) {
            addCriterion("model_object_code not like", value, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeIn(List<String> values) {
            addCriterion("model_object_code in", values, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeNotIn(List<String> values) {
            addCriterion("model_object_code not in", values, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeBetween(String value1, String value2) {
            addCriterion("model_object_code between", value1, value2, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeNotBetween(String value1, String value2) {
            addCriterion("model_object_code not between", value1, value2, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andRefDsNameIsNull() {
            addCriterion("ref_ds_name is null");
            return (Criteria) this;
        }

        public Criteria andRefDsNameIsNotNull() {
            addCriterion("ref_ds_name is not null");
            return (Criteria) this;
        }

        public Criteria andRefDsNameEqualTo(String value) {
            addCriterion("ref_ds_name =", value, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameNotEqualTo(String value) {
            addCriterion("ref_ds_name <>", value, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameGreaterThan(String value) {
            addCriterion("ref_ds_name >", value, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameGreaterThanOrEqualTo(String value) {
            addCriterion("ref_ds_name >=", value, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameLessThan(String value) {
            addCriterion("ref_ds_name <", value, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameLessThanOrEqualTo(String value) {
            addCriterion("ref_ds_name <=", value, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameLike(String value) {
            addCriterion("ref_ds_name like", value, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameNotLike(String value) {
            addCriterion("ref_ds_name not like", value, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameIn(List<String> values) {
            addCriterion("ref_ds_name in", values, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameNotIn(List<String> values) {
            addCriterion("ref_ds_name not in", values, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameBetween(String value1, String value2) {
            addCriterion("ref_ds_name between", value1, value2, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameNotBetween(String value1, String value2) {
            addCriterion("ref_ds_name not between", value1, value2, "refDsName");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Long value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Long value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Long value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Long value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Long value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Long value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Long> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Long> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Long value1, Long value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Long value1, Long value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}