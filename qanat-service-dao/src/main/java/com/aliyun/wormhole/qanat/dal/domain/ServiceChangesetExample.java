package com.aliyun.wormhole.qanat.dal.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ServiceChangesetExample {
    /**
     * 主键字段
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    protected String pk_name = "id";

    /**
     * 排序字段
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    protected String orderByClause;

    /**
     * 去重复
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    protected boolean distinct;

    /**
     * 条件集
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    protected List<Criteria> oredCriteria;

    public ServiceChangesetExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setPk_name(String pk_name) {
        this.pk_name = pk_name;
    }

    public String getPk_name() {
        return pk_name;
    }

    /**
     * 排序字段
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * 设置去重复
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * 条件查询要先创建Criteria
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * 
     * 内类部，系统内部调用1
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andCsTypeIsNull() {
            addCriterion("cs_type is null");
            return (Criteria) this;
        }

        public Criteria andCsTypeIsNotNull() {
            addCriterion("cs_type is not null");
            return (Criteria) this;
        }

        public Criteria andCsTypeEqualTo(String value) {
            addCriterion("cs_type =", value, "csType");
            return (Criteria) this;
        }

        public Criteria andCsTypeNotEqualTo(String value) {
            addCriterion("cs_type <>", value, "csType");
            return (Criteria) this;
        }

        public Criteria andCsTypeGreaterThan(String value) {
            addCriterion("cs_type >", value, "csType");
            return (Criteria) this;
        }

        public Criteria andCsTypeGreaterThanOrEqualTo(String value) {
            addCriterion("cs_type >=", value, "csType");
            return (Criteria) this;
        }

        public Criteria andCsTypeLessThan(String value) {
            addCriterion("cs_type <", value, "csType");
            return (Criteria) this;
        }

        public Criteria andCsTypeLessThanOrEqualTo(String value) {
            addCriterion("cs_type <=", value, "csType");
            return (Criteria) this;
        }

        public Criteria andCsTypeLike(String value) {
            addCriterion("cs_type like", value, "csType");
            return (Criteria) this;
        }

        public Criteria andCsTypeNotLike(String value) {
            addCriterion("cs_type not like", value, "csType");
            return (Criteria) this;
        }

        public Criteria andCsTypeIn(List<String> values) {
            addCriterion("cs_type in", values, "csType");
            return (Criteria) this;
        }

        public Criteria andCsTypeNotIn(List<String> values) {
            addCriterion("cs_type not in", values, "csType");
            return (Criteria) this;
        }

        public Criteria andCsTypeBetween(String value1, String value2) {
            addCriterion("cs_type between", value1, value2, "csType");
            return (Criteria) this;
        }

        public Criteria andCsTypeNotBetween(String value1, String value2) {
            addCriterion("cs_type not between", value1, value2, "csType");
            return (Criteria) this;
        }

        public Criteria andCsDescIsNull() {
            addCriterion("cs_desc is null");
            return (Criteria) this;
        }

        public Criteria andCsDescIsNotNull() {
            addCriterion("cs_desc is not null");
            return (Criteria) this;
        }

        public Criteria andCsDescEqualTo(String value) {
            addCriterion("cs_desc =", value, "csDesc");
            return (Criteria) this;
        }

        public Criteria andCsDescNotEqualTo(String value) {
            addCriterion("cs_desc <>", value, "csDesc");
            return (Criteria) this;
        }

        public Criteria andCsDescGreaterThan(String value) {
            addCriterion("cs_desc >", value, "csDesc");
            return (Criteria) this;
        }

        public Criteria andCsDescGreaterThanOrEqualTo(String value) {
            addCriterion("cs_desc >=", value, "csDesc");
            return (Criteria) this;
        }

        public Criteria andCsDescLessThan(String value) {
            addCriterion("cs_desc <", value, "csDesc");
            return (Criteria) this;
        }

        public Criteria andCsDescLessThanOrEqualTo(String value) {
            addCriterion("cs_desc <=", value, "csDesc");
            return (Criteria) this;
        }

        public Criteria andCsDescLike(String value) {
            addCriterion("cs_desc like", value, "csDesc");
            return (Criteria) this;
        }

        public Criteria andCsDescNotLike(String value) {
            addCriterion("cs_desc not like", value, "csDesc");
            return (Criteria) this;
        }

        public Criteria andCsDescIn(List<String> values) {
            addCriterion("cs_desc in", values, "csDesc");
            return (Criteria) this;
        }

        public Criteria andCsDescNotIn(List<String> values) {
            addCriterion("cs_desc not in", values, "csDesc");
            return (Criteria) this;
        }

        public Criteria andCsDescBetween(String value1, String value2) {
            addCriterion("cs_desc between", value1, value2, "csDesc");
            return (Criteria) this;
        }

        public Criteria andCsDescNotBetween(String value1, String value2) {
            addCriterion("cs_desc not between", value1, value2, "csDesc");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIsNull() {
            addCriterion("create_empid is null");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIsNotNull() {
            addCriterion("create_empid is not null");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidEqualTo(String value) {
            addCriterion("create_empid =", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotEqualTo(String value) {
            addCriterion("create_empid <>", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidGreaterThan(String value) {
            addCriterion("create_empid >", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidGreaterThanOrEqualTo(String value) {
            addCriterion("create_empid >=", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLessThan(String value) {
            addCriterion("create_empid <", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLessThanOrEqualTo(String value) {
            addCriterion("create_empid <=", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLike(String value) {
            addCriterion("create_empid like", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotLike(String value) {
            addCriterion("create_empid not like", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIn(List<String> values) {
            addCriterion("create_empid in", values, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotIn(List<String> values) {
            addCriterion("create_empid not in", values, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidBetween(String value1, String value2) {
            addCriterion("create_empid between", value1, value2, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotBetween(String value1, String value2) {
            addCriterion("create_empid not between", value1, value2, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIsNull() {
            addCriterion("modify_empid is null");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIsNotNull() {
            addCriterion("modify_empid is not null");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidEqualTo(String value) {
            addCriterion("modify_empid =", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotEqualTo(String value) {
            addCriterion("modify_empid <>", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidGreaterThan(String value) {
            addCriterion("modify_empid >", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidGreaterThanOrEqualTo(String value) {
            addCriterion("modify_empid >=", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLessThan(String value) {
            addCriterion("modify_empid <", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLessThanOrEqualTo(String value) {
            addCriterion("modify_empid <=", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLike(String value) {
            addCriterion("modify_empid like", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotLike(String value) {
            addCriterion("modify_empid not like", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIn(List<String> values) {
            addCriterion("modify_empid in", values, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotIn(List<String> values) {
            addCriterion("modify_empid not in", values, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidBetween(String value1, String value2) {
            addCriterion("modify_empid between", value1, value2, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotBetween(String value1, String value2) {
            addCriterion("modify_empid not between", value1, value2, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andServiceIdIsNull() {
            addCriterion("service_id is null");
            return (Criteria) this;
        }

        public Criteria andServiceIdIsNotNull() {
            addCriterion("service_id is not null");
            return (Criteria) this;
        }

        public Criteria andServiceIdEqualTo(Long value) {
            addCriterion("service_id =", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdNotEqualTo(Long value) {
            addCriterion("service_id <>", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdGreaterThan(Long value) {
            addCriterion("service_id >", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdGreaterThanOrEqualTo(Long value) {
            addCriterion("service_id >=", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdLessThan(Long value) {
            addCriterion("service_id <", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdLessThanOrEqualTo(Long value) {
            addCriterion("service_id <=", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdIn(List<Long> values) {
            addCriterion("service_id in", values, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdNotIn(List<Long> values) {
            addCriterion("service_id not in", values, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdBetween(Long value1, Long value2) {
            addCriterion("service_id between", value1, value2, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdNotBetween(Long value1, Long value2) {
            addCriterion("service_id not between", value1, value2, "serviceId");
            return (Criteria) this;
        }
    }

    /**
     * service_changeset
     * @ibatorgenerated do_not_delete_during_merge 2019-08-19 16:15:41
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * 
     * 内类部，系统内部调用1
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}