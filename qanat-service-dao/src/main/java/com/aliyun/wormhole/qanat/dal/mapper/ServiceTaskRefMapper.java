package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.ServiceTaskRef;
import com.aliyun.wormhole.qanat.dal.domain.ServiceTaskRefExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface ServiceTaskRefMapper {
    /**
     * 条件统计
     * 参数:查询条件,null为整张表
     * 返回:查询个数
     * @ibatorgenerated 2019-09-26 11:05:53
     */
    int countByExample(ServiceTaskRefExample example);

    /**
     * 批量条件删除
     * 参数:删除条件,null为整张表
     * 返回:删除个数
     * @ibatorgenerated 2019-09-26 11:05:53
     */
    int deleteByExample(ServiceTaskRefExample example);

    /**
     * 批量条件查询
     * 参数:查询条件,null查整张表
     * 返回:对象集合
     * @ibatorgenerated 2019-09-26 11:05:53
     */
    List<ServiceTaskRef> selectByExample(ServiceTaskRefExample example);

    /**
     * 批量条件修改，空值条件不修改
     * 参数:1.要修改成的值，2.要修改条件
     * 返回:成功修改个数
     * @ibatorgenerated 2019-09-26 11:05:53
     */
    int updateByExampleSelective(@Param("record") ServiceTaskRef record, @Param("example") ServiceTaskRefExample example);

    /**
     * 批量条件修改，空值条件会修改成null
     * 参数:1.要修改成的值，2.要修改条件
     * 返回:成功修改个数
     * @ibatorgenerated 2019-09-26 11:05:53
     */
    int updateByExample(@Param("record") ServiceTaskRef record, @Param("example") ServiceTaskRefExample example);

    /**
     * 物理分页条件查询
     * 参数:1.查询条件 2.分页条件 new RowBounds(2,3) 
            从第2条开始显示，显示3条(从0开始编号)
     * 返回:成功修改个数
     * @ibatorgenerated 2019-09-26 11:05:53
     */
    List<ServiceTaskRef> selectByExampleAndPage(ServiceTaskRefExample example, RowBounds rowBound);

    /**
     * 根据主键删除
     * 参数:主键
     * 返回:删除个数
     * @ibatorgenerated 2019-09-26 11:05:53
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入，空属性也会插入
     * 参数:pojo对象
     * 返回:删除个数
     * @ibatorgenerated 2019-09-26 11:05:53
     */
    int insert(ServiceTaskRef record);

    /**
     * 插入，空属性不会插入
     * 参数:pojo对象
     * 返回:删除个数
     * @ibatorgenerated 2019-09-26 11:05:53
     */
    int insertSelective(ServiceTaskRef record);

    /**
     * 根据主键查询
     * 参数:查询条件,主键值
     * 返回:对象
     * @ibatorgenerated 2019-09-26 11:05:53
     */
    ServiceTaskRef selectByPrimaryKey(Long id);

    /**
     * 根据主键修改，空值条件不会修改成null
     * 参数:1.要修改成的值
     * 返回:成功修改个数
     * @ibatorgenerated 2019-09-26 11:05:53
     */
    int updateByPrimaryKeySelective(ServiceTaskRef record);

    /**
     * 根据主键修改，空值条件会修改成null
     * 参数:1.要修改成的值
     * 返回:成功修改个数
     * @ibatorgenerated 2019-09-26 11:05:53
     */
    int updateByPrimaryKey(ServiceTaskRef record);
}