package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.SearchServiceVersion;
import com.aliyun.wormhole.qanat.dal.domain.SearchServiceVersionExample;
import com.aliyun.wormhole.qanat.dal.domain.SearchServiceVersionWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface SearchServiceVersionMapper {
    /**
     * 条件统计
     * 参数:查询条件,null为整张表
     * 返回:查询个数
     * @ibatorgenerated 2019-08-19 16:15:30
     */
    int countByExample(SearchServiceVersionExample example);

    /**
     * 批量条件删除
     * 参数:删除条件,null为整张表
     * 返回:删除个数
     * @ibatorgenerated 2019-08-19 16:15:30
     */
    int deleteByExample(SearchServiceVersionExample example);

    /**
     * 批量条件查询,支持大字段类型
     * 参数:查询条件,null查整张表
     * 返回:对象集合
     * @ibatorgenerated 2019-08-19 16:15:30
     */
    List<SearchServiceVersionWithBLOBs> selectByExampleWithBLOBs(SearchServiceVersionExample example);

    /**
     * 批量条件查询
     * 参数:查询条件,null查整张表
     * 返回:对象集合
     * @ibatorgenerated 2019-08-19 16:15:30
     */
    List<SearchServiceVersion> selectByExample(SearchServiceVersionExample example);

    /**
     * 批量条件修改，空值条件不修改
     * 参数:1.要修改成的值，2.要修改条件
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:30
     */
    int updateByExampleSelective(@Param("record") SearchServiceVersionWithBLOBs record, @Param("example") SearchServiceVersionExample example);

    /**
     * 批量条件修改，空值条件会修改成null,支持大字段类型
     * 参数:1.要修改成的值，2.要修改条件
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:30
     */
    int updateByExampleWithBLOBs(@Param("record") SearchServiceVersionWithBLOBs record, @Param("example") SearchServiceVersionExample example);

    /**
     * 批量条件修改，空值条件会修改成null
     * 参数:1.要修改成的值，2.要修改条件
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:30
     */
    int updateByExample(@Param("record") SearchServiceVersion record, @Param("example") SearchServiceVersionExample example);

    /**
     * 物理分页条件查询,支持大字段
     * 参数:1.查询条件 2.分页条件 new RowBounds(2,3) 
            从第2条开始显示，显示3条(从0开始编号)
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:30
     */
    List<SearchServiceVersionWithBLOBs> selectByExampleWithBLOBsAndPage(SearchServiceVersionExample example, RowBounds rowBound);

    /**
     * 物理分页条件查询
     * 参数:1.查询条件 2.分页条件 new RowBounds(2,3) 
            从第2条开始显示，显示3条(从0开始编号)
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:30
     */
    List<SearchServiceVersion> selectByExampleAndPage(SearchServiceVersionExample example, RowBounds rowBound);

    /**
     * 根据主键删除
     * 参数:主键
     * 返回:删除个数
     * @ibatorgenerated 2019-08-19 16:15:30
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入，空属性也会插入
     * 参数:pojo对象
     * 返回:删除个数
     * @ibatorgenerated 2019-08-19 16:15:30
     */
    int insert(SearchServiceVersionWithBLOBs record);

    /**
     * 插入，空属性不会插入
     * 参数:pojo对象
     * 返回:删除个数
     * @ibatorgenerated 2019-08-19 16:15:30
     */
    int insertSelective(SearchServiceVersionWithBLOBs record);

    /**
     * 根据主键查询
     * 参数:查询条件,主键值
     * 返回:对象
     * @ibatorgenerated 2019-08-19 16:15:30
     */
    SearchServiceVersionWithBLOBs selectByPrimaryKey(Long id);

    /**
     * 根据主键修改，空值条件不会修改成null
     * 参数:1.要修改成的值
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:30
     */
    int updateByPrimaryKeySelective(SearchServiceVersionWithBLOBs record);

    /**
     * 根据主键修改，空值条件会修改成null,支持大字段类型
     * 参数:1.要修改成的值
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:30
     */
    int updateByPrimaryKeyWithBLOBs(SearchServiceVersionWithBLOBs record);

    /**
     * 根据主键修改，空值条件会修改成null
     * 参数:1.要修改成的值
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:30
     */
    int updateByPrimaryKey(SearchServiceVersion record);
}