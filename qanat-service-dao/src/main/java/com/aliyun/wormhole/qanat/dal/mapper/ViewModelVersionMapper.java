package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.ViewModelVersion;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelVersionExample;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelVersionWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface ViewModelVersionMapper {
    int countByExample(ViewModelVersionExample example);

    int deleteByExample(ViewModelVersionExample example);

    List<ViewModelVersionWithBLOBs> selectByExampleWithBLOBs(ViewModelVersionExample example);

    List<ViewModelVersion> selectByExample(ViewModelVersionExample example);

    int updateByExampleSelective(@Param("record") ViewModelVersionWithBLOBs record, @Param("example") ViewModelVersionExample example);

    int updateByExampleWithBLOBs(@Param("record") ViewModelVersionWithBLOBs record, @Param("example") ViewModelVersionExample example);

    int updateByExample(@Param("record") ViewModelVersion record, @Param("example") ViewModelVersionExample example);

    List<ViewModelVersionWithBLOBs> selectByExampleWithBLOBsAndPage(ViewModelVersionExample example, RowBounds rowBound);

    List<ViewModelVersion> selectByExampleAndPage(ViewModelVersionExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(ViewModelVersionWithBLOBs record);

    int insertSelective(ViewModelVersionWithBLOBs record);

    ViewModelVersionWithBLOBs selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ViewModelVersionWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(ViewModelVersionWithBLOBs record);

    int updateByPrimaryKey(ViewModelVersion record);
}