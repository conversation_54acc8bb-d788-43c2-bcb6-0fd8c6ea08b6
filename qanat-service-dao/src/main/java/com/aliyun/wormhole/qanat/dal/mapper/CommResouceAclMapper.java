package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.CommResouceAcl;
import com.aliyun.wormhole.qanat.dal.domain.CommResouceAclExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface CommResouceAclMapper {
    /**
     * 条件统计
     * 参数:查询条件,null为整张表
     * 返回:查询个数
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    int countByExample(CommResouceAclExample example);

    /**
     * 批量条件删除
     * 参数:删除条件,null为整张表
     * 返回:删除个数
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    int deleteByExample(CommResouceAclExample example);

    /**
     * 批量条件查询
     * 参数:查询条件,null查整张表
     * 返回:对象集合
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    List<CommResouceAcl> selectByExample(CommResouceAclExample example);

    /**
     * 批量条件修改，空值条件不修改
     * 参数:1.要修改成的值，2.要修改条件
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    int updateByExampleSelective(@Param("record") CommResouceAcl record, @Param("example") CommResouceAclExample example);

    /**
     * 批量条件修改，空值条件会修改成null
     * 参数:1.要修改成的值，2.要修改条件
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    int updateByExample(@Param("record") CommResouceAcl record, @Param("example") CommResouceAclExample example);

    /**
     * 物理分页条件查询
     * 参数:1.查询条件 2.分页条件 new RowBounds(2,3) 
            从第2条开始显示，显示3条(从0开始编号)
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    List<CommResouceAcl> selectByExampleAndPage(CommResouceAclExample example, RowBounds rowBound);

    /**
     * 根据主键删除
     * 参数:主键
     * 返回:删除个数
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入，空属性也会插入
     * 参数:pojo对象
     * 返回:删除个数
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    int insert(CommResouceAcl record);

    /**
     * 插入，空属性不会插入
     * 参数:pojo对象
     * 返回:删除个数
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    int insertSelective(CommResouceAcl record);

    /**
     * 根据主键查询
     * 参数:查询条件,主键值
     * 返回:对象
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    CommResouceAcl selectByPrimaryKey(Long id);

    /**
     * 根据主键修改，空值条件不会修改成null
     * 参数:1.要修改成的值
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    int updateByPrimaryKeySelective(CommResouceAcl record);

    /**
     * 根据主键修改，空值条件会修改成null
     * 参数:1.要修改成的值
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    int updateByPrimaryKey(CommResouceAcl record);
}