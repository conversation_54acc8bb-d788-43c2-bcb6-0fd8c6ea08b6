package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelation;
import com.aliyun.wormhole.qanat.dal.domain.ComponentDsRelationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface ComponentDsRelationMapper {
    int countByExample(ComponentDsRelationExample example);

    int deleteByExample(ComponentDsRelationExample example);

    List<ComponentDsRelation> selectByExample(ComponentDsRelationExample example);

    int updateByExampleSelective(@Param("record") ComponentDsRelation record, @Param("example") ComponentDsRelationExample example);

    int updateByExample(@Param("record") ComponentDsRelation record, @Param("example") ComponentDsRelationExample example);

    List<ComponentDsRelation> selectByExampleAndPage(ComponentDsRelationExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(ComponentDsRelation record);

    int insertSelective(ComponentDsRelation record);

    ComponentDsRelation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ComponentDsRelation record);

    int updateByPrimaryKey(ComponentDsRelation record);
}