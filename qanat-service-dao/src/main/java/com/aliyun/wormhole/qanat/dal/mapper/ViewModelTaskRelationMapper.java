package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.ViewModelTaskRelation;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelTaskRelationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface ViewModelTaskRelationMapper {
    int countByExample(ViewModelTaskRelationExample example);

    int deleteByExample(ViewModelTaskRelationExample example);

    List<ViewModelTaskRelation> selectByExample(ViewModelTaskRelationExample example);

    int updateByExampleSelective(@Param("record") ViewModelTaskRelation record, @Param("example") ViewModelTaskRelationExample example);

    int updateByExample(@Param("record") ViewModelTaskRelation record, @Param("example") ViewModelTaskRelationExample example);

    List<ViewModelTaskRelation> selectByExampleAndPage(ViewModelTaskRelationExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(ViewModelTaskRelation record);

    int insertSelective(ViewModelTaskRelation record);

    ViewModelTaskRelation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ViewModelTaskRelation record);

    int updateByPrimaryKey(ViewModelTaskRelation record);
}