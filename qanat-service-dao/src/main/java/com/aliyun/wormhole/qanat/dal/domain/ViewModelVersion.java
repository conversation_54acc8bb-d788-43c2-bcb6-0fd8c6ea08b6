package com.aliyun.wormhole.qanat.dal.domain;

import java.io.Serializable;
import java.util.Date;

public class ViewModelVersion implements Serializable {
    private Long id;

    private Date gmtCreate;

    private Date gmtModified;

    private String tenantId;

    private Long viewModelId;

    private String viewModelName;

    private String remark;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Long getViewModelId() {
        return viewModelId;
    }

    public void setViewModelId(Long viewModelId) {
        this.viewModelId = viewModelId;
    }

    public String getViewModelName() {
        return viewModelName;
    }

    public void setViewModelName(String viewModelName) {
        this.viewModelName = viewModelName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}