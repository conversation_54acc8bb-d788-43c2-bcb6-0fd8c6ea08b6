package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.Resource;
import com.aliyun.wormhole.qanat.dal.domain.ResourceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface ResourceMapper {
    int countByExample(ResourceExample example);

    int deleteByExample(ResourceExample example);

    List<Resource> selectByExampleWithBLOBs(ResourceExample example);

    List<Resource> selectByExample(ResourceExample example);

    int updateByExampleSelective(@Param("record") Resource record, @Param("example") ResourceExample example);

    int updateByExampleWithBLOBs(@Param("record") Resource record, @Param("example") ResourceExample example);

    int updateByExample(@Param("record") Resource record, @Param("example") ResourceExample example);

    List<Resource> selectByExampleWithBLOBsAndPage(ResourceExample example, RowBounds rowBound);

    List<Resource> selectByExampleAndPage(ResourceExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(Resource record);

    int insertSelective(Resource record);

    Resource selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Resource record);

    int updateByPrimaryKeyWithBLOBs(Resource record);

    int updateByPrimaryKey(Resource record);
}