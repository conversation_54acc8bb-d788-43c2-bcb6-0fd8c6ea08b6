package com.aliyun.wormhole.qanat.dal.domain;

import java.io.Serializable;
import java.util.Date;

public class DatatubeInstanceModelField implements Serializable {
    private Long id;

    private Date gmtCreate;

    private Date gmtModified;

    private String createEmpid;

    private String modifyEmpid;

    private String fieldName;

    private String refFieldName;

    private String fieldType;

    private String fieldDesc;

    private Integer isMultivalue;

    private Integer isPk;

    private Integer isFk;

    private String mvToken;

    private Integer isFunc;

    private String extRefFields;

    private Long datatubeInstId;

    private String modelObjectCode;

    private String refDsName;

    private Long isDeleted;

    private String tenantId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getCreateEmpid() {
        return createEmpid;
    }

    public void setCreateEmpid(String createEmpid) {
        this.createEmpid = createEmpid;
    }

    public String getModifyEmpid() {
        return modifyEmpid;
    }

    public void setModifyEmpid(String modifyEmpid) {
        this.modifyEmpid = modifyEmpid;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getRefFieldName() {
        return refFieldName;
    }

    public void setRefFieldName(String refFieldName) {
        this.refFieldName = refFieldName;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public String getFieldDesc() {
        return fieldDesc;
    }

    public void setFieldDesc(String fieldDesc) {
        this.fieldDesc = fieldDesc;
    }

    public Integer getIsMultivalue() {
        return isMultivalue;
    }

    public void setIsMultivalue(Integer isMultivalue) {
        this.isMultivalue = isMultivalue;
    }

    public Integer getIsPk() {
        return isPk;
    }

    public void setIsPk(Integer isPk) {
        this.isPk = isPk;
    }

    public Integer getIsFk() {
        return isFk;
    }

    public void setIsFk(Integer isFk) {
        this.isFk = isFk;
    }

    public String getMvToken() {
        return mvToken;
    }

    public void setMvToken(String mvToken) {
        this.mvToken = mvToken;
    }

    public Integer getIsFunc() {
        return isFunc;
    }

    public void setIsFunc(Integer isFunc) {
        this.isFunc = isFunc;
    }

    public String getExtRefFields() {
        return extRefFields;
    }

    public void setExtRefFields(String extRefFields) {
        this.extRefFields = extRefFields;
    }

    public Long getDatatubeInstId() {
        return datatubeInstId;
    }

    public void setDatatubeInstId(Long datatubeInstId) {
        this.datatubeInstId = datatubeInstId;
    }

    public String getModelObjectCode() {
        return modelObjectCode;
    }

    public void setModelObjectCode(String modelObjectCode) {
        this.modelObjectCode = modelObjectCode;
    }

    public String getRefDsName() {
        return refDsName;
    }

    public void setRefDsName(String refDsName) {
        this.refDsName = refDsName;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}