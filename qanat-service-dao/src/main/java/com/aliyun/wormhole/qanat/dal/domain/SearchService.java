package com.aliyun.wormhole.qanat.dal.domain;

import java.io.Serializable;
import java.util.Date;

public class SearchService implements Serializable {
    /**
     * search_service.id (主键)
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    private Long id;

    /**
     * search_service.gmt_create (创建时间)
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    private Date gmtCreate;

    /**
     * search_service.gmt_modified (修改时间)
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    private Date gmtModified;

    /**
     * search_service.svc_name (服务名称)
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    private String svcName;

    /**
     * search_service.svc_desc (服务描述)
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    private String svcDesc;

    /**
     * search_service.biz_line (业务线)
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    private String bizLine;

    /**
     * search_service.app_key (应用AK)
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    private String appKey;

    /**
     * search_service.app_id (应用id)
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    private String appId;

    /**
     * search_service.create_empid (创建人工号)
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    private String createEmpid;

    /**
     * search_service.modify_empid (修改人工号)
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    private String modifyEmpid;

    /**
     * search_service.is_deleted (是否逻辑删除)
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    private Long isDeleted;

    /**
     * search_service.svc_version (版本id)
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    private Long svcVersion;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getSvcName() {
        return svcName;
    }

    public void setSvcName(String svcName) {
        this.svcName = svcName;
    }

    public String getSvcDesc() {
        return svcDesc;
    }

    public void setSvcDesc(String svcDesc) {
        this.svcDesc = svcDesc;
    }

    public String getBizLine() {
        return bizLine;
    }

    public void setBizLine(String bizLine) {
        this.bizLine = bizLine;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getCreateEmpid() {
        return createEmpid;
    }

    public void setCreateEmpid(String createEmpid) {
        this.createEmpid = createEmpid;
    }

    public String getModifyEmpid() {
        return modifyEmpid;
    }

    public void setModifyEmpid(String modifyEmpid) {
        this.modifyEmpid = modifyEmpid;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Long getSvcVersion() {
        return svcVersion;
    }

    public void setSvcVersion(Long svcVersion) {
        this.svcVersion = svcVersion;
    }
}