package com.aliyun.wormhole.qanat.dal.domain;

import java.io.Serializable;
import java.util.Date;

public class TenantInfo implements Serializable {
    private Long id;

    private Date gmtCreate;

    private Date gmtModified;

    private String createEmpid;

    private String modifyEmpid;

    private String tenantId;

    private String name;

    private String unit;

    private String defaultDw;

    private String etlDw;

    private String backupDw;

    private String mdpMeta;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getCreateEmpid() {
        return createEmpid;
    }

    public void setCreateEmpid(String createEmpid) {
        this.createEmpid = createEmpid;
    }

    public String getModifyEmpid() {
        return modifyEmpid;
    }

    public void setModifyEmpid(String modifyEmpid) {
        this.modifyEmpid = modifyEmpid;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getDefaultDw() {
        return defaultDw;
    }

    public void setDefaultDw(String defaultDw) {
        this.defaultDw = defaultDw;
    }

    public String getEtlDw() {
        return etlDw;
    }

    public void setEtlDw(String etlDw) {
        this.etlDw = etlDw;
    }

    public String getBackupDw() {
        return backupDw;
    }

    public void setBackupDw(String backupDw) {
        this.backupDw = backupDw;
    }

    public String getMdpMeta() {
        return mdpMeta;
    }

    public void setMdpMeta(String mdpMeta) {
        this.mdpMeta = mdpMeta;
    }
}