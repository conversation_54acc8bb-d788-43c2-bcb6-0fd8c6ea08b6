package com.aliyun.wormhole.qanat.dal.domain;

import java.io.Serializable;
import java.util.Date;

public class DsFieldRelation implements Serializable {
    private Long id;

    private Date gmtCreate;

    private Date gmtModified;

    private String srcFieldName;

    private String srcDsName;

    private String dstFieldName;

    private String dstDsName;

    private String relationType;

    private String computeFunc;

    private String metricName;

    private Long isDeleted;

    private String tenantId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getSrcFieldName() {
        return srcFieldName;
    }

    public void setSrcFieldName(String srcFieldName) {
        this.srcFieldName = srcFieldName;
    }

    public String getSrcDsName() {
        return srcDsName;
    }

    public void setSrcDsName(String srcDsName) {
        this.srcDsName = srcDsName;
    }

    public String getDstFieldName() {
        return dstFieldName;
    }

    public void setDstFieldName(String dstFieldName) {
        this.dstFieldName = dstFieldName;
    }

    public String getDstDsName() {
        return dstDsName;
    }

    public void setDstDsName(String dstDsName) {
        this.dstDsName = dstDsName;
    }

    public String getRelationType() {
        return relationType;
    }

    public void setRelationType(String relationType) {
        this.relationType = relationType;
    }

    public String getComputeFunc() {
        return computeFunc;
    }

    public void setComputeFunc(String computeFunc) {
        this.computeFunc = computeFunc;
    }

    public String getMetricName() {
        return metricName;
    }

    public void setMetricName(String metricName) {
        this.metricName = metricName;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}