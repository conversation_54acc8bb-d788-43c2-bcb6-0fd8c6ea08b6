package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.ViewModelDsRelation;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelDsRelationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface ViewModelDsRelationMapper {
    int countByExample(ViewModelDsRelationExample example);

    int deleteByExample(ViewModelDsRelationExample example);

    List<ViewModelDsRelation> selectByExample(ViewModelDsRelationExample example);

    int updateByExampleSelective(@Param("record") ViewModelDsRelation record, @Param("example") ViewModelDsRelationExample example);

    int updateByExample(@Param("record") ViewModelDsRelation record, @Param("example") ViewModelDsRelationExample example);

    List<ViewModelDsRelation> selectByExampleAndPage(ViewModelDsRelationExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(ViewModelDsRelation record);

    int insertSelective(ViewModelDsRelation record);

    ViewModelDsRelation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ViewModelDsRelation record);

    int updateByPrimaryKey(ViewModelDsRelation record);
}