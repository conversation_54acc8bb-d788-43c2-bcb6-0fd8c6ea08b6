package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelField;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelFieldExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DatatubeInstanceModelFieldMapper {
    int countByExample(DatatubeInstanceModelFieldExample example);

    int deleteByExample(DatatubeInstanceModelFieldExample example);

    List<DatatubeInstanceModelField> selectByExample(DatatubeInstanceModelFieldExample example);

    int updateByExampleSelective(@Param("record") DatatubeInstanceModelField record, @Param("example") DatatubeInstanceModelFieldExample example);

    int updateByExample(@Param("record") DatatubeInstanceModelField record, @Param("example") DatatubeInstanceModelFieldExample example);

    List<DatatubeInstanceModelField> selectByExampleAndPage(DatatubeInstanceModelFieldExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(DatatubeInstanceModelField record);

    int insertSelective(DatatubeInstanceModelField record);

    DatatubeInstanceModelField selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DatatubeInstanceModelField record);

    int updateByPrimaryKey(DatatubeInstanceModelField record);
}