package com.aliyun.wormhole.qanat.dal.domain;

import java.io.Serializable;
import java.util.Date;

public class AppInfo implements Serializable {
    private Long id;

    private Date gmtCreate;

    private Date gmtModified;

    private String appName;

    private String appDesc;

    private String owner;

    private String createEmpid;

    private String modifyEmpid;

    private Long isDeleted;

    private String tenantId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppDesc() {
        return appDesc;
    }

    public void setAppDesc(String appDesc) {
        this.appDesc = appDesc;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getCreateEmpid() {
        return createEmpid;
    }

    public void setCreateEmpid(String createEmpid) {
        this.createEmpid = createEmpid;
    }

    public String getModifyEmpid() {
        return modifyEmpid;
    }

    public void setModifyEmpid(String modifyEmpid) {
        this.modifyEmpid = modifyEmpid;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}