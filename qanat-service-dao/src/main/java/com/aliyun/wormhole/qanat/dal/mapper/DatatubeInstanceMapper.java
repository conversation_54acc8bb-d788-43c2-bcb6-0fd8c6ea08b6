package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstance;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DatatubeInstanceMapper {
    int countByExample(DatatubeInstanceExample example);

    int deleteByExample(DatatubeInstanceExample example);

    List<DatatubeInstance> selectByExample(DatatubeInstanceExample example);

    int updateByExampleSelective(@Param("record") DatatubeInstance record, @Param("example") DatatubeInstanceExample example);

    int updateByExample(@Param("record") DatatubeInstance record, @Param("example") DatatubeInstanceExample example);

    List<DatatubeInstance> selectByExampleAndPage(DatatubeInstanceExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(DatatubeInstance record);

    int insertSelective(DatatubeInstance record);

    DatatubeInstance selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DatatubeInstance record);

    int updateByPrimaryKey(DatatubeInstance record);
}