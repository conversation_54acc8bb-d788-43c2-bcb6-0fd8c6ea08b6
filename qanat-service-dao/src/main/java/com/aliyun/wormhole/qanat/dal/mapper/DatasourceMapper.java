package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.Datasource;
import com.aliyun.wormhole.qanat.dal.domain.DatasourceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DatasourceMapper {
    int countByExample(DatasourceExample example);

    int deleteByExample(DatasourceExample example);

    List<Datasource> selectByExampleWithBLOBs(DatasourceExample example);

    List<Datasource> selectByExample(DatasourceExample example);

    int updateByExampleSelective(@Param("record") Datasource record, @Param("example") DatasourceExample example);

    int updateByExampleWithBLOBs(@Param("record") Datasource record, @Param("example") DatasourceExample example);

    int updateByExample(@Param("record") Datasource record, @Param("example") DatasourceExample example);

    List<Datasource> selectByExampleWithBLOBsAndPage(DatasourceExample example, RowBounds rowBound);

    List<Datasource> selectByExampleAndPage(DatasourceExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(Datasource record);

    int insertSelective(Datasource record);

    Datasource selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Datasource record);

    int updateByPrimaryKeyWithBLOBs(Datasource record);

    int updateByPrimaryKey(Datasource record);
}