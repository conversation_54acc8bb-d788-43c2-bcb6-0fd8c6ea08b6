package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.DbInfo;
import com.aliyun.wormhole.qanat.dal.domain.DbInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DbInfoMapper {
    int countByExample(DbInfoExample example);

    int deleteByExample(DbInfoExample example);

    List<DbInfo> selectByExampleWithBLOBs(DbInfoExample example);

    List<DbInfo> selectByExample(DbInfoExample example);

    int updateByExampleSelective(@Param("record") DbInfo record, @Param("example") DbInfoExample example);

    int updateByExampleWithBLOBs(@Param("record") DbInfo record, @Param("example") DbInfoExample example);

    int updateByExample(@Param("record") DbInfo record, @Param("example") DbInfoExample example);

    List<DbInfo> selectByExampleWithBLOBsAndPage(DbInfoExample example, RowBounds rowBound);

    List<DbInfo> selectByExampleAndPage(DbInfoExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(DbInfo record);

    int insertSelective(DbInfo record);

    DbInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DbInfo record);

    int updateByPrimaryKeyWithBLOBs(DbInfo record);

    int updateByPrimaryKey(DbInfo record);
}