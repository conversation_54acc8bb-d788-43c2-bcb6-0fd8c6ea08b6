package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.TaskInfo;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfoExample;
import com.aliyun.wormhole.qanat.dal.domain.TaskInfoWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface TaskInfoMapper {
    int countByExample(TaskInfoExample example);

    int deleteByExample(TaskInfoExample example);

    List<TaskInfoWithBLOBs> selectByExampleWithBLOBs(TaskInfoExample example);

    List<TaskInfo> selectByExample(TaskInfoExample example);

    int updateByExampleSelective(@Param("record") TaskInfoWithBLOBs record, @Param("example") TaskInfoExample example);

    int updateByExampleWithBLOBs(@Param("record") TaskInfoWithBLOBs record, @Param("example") TaskInfoExample example);

    int updateByExample(@Param("record") TaskInfo record, @Param("example") TaskInfoExample example);

    List<TaskInfoWithBLOBs> selectByExampleWithBLOBsAndPage(TaskInfoExample example, RowBounds rowBound);

    List<TaskInfo> selectByExampleAndPage(TaskInfoExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(TaskInfoWithBLOBs record);

    int insertSelective(TaskInfoWithBLOBs record);

    TaskInfoWithBLOBs selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TaskInfoWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(TaskInfoWithBLOBs record);

    int updateByPrimaryKey(TaskInfo record);
}