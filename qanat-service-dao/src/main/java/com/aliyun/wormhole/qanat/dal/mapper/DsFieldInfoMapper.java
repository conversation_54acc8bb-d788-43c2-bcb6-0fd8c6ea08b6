package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfo;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DsFieldInfoMapper {
    int countByExample(DsFieldInfoExample example);

    int deleteByExample(DsFieldInfoExample example);

    List<DsFieldInfo> selectByExample(DsFieldInfoExample example);

    int updateByExampleSelective(@Param("record") DsFieldInfo record, @Param("example") DsFieldInfoExample example);

    int updateByExample(@Param("record") DsFieldInfo record, @Param("example") DsFieldInfoExample example);

    List<DsFieldInfo> selectByExampleAndPage(DsFieldInfoExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(DsFieldInfo record);

    int insertSelective(DsFieldInfo record);

    DsFieldInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DsFieldInfo record);

    int updateByPrimaryKey(DsFieldInfo record);
}