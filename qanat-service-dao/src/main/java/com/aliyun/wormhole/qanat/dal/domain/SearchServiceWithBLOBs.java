package com.aliyun.wormhole.qanat.dal.domain;

public class SearchServiceWithBLOBs extends SearchService {
    /**
     * search_service.meta (元信息)
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    private String meta;

    /**
     * search_service.ref_ds_list (引用数据源列表)
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    private String refDsList;

    /**
     * search_service.dag_script (DAG脚本)
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    private String dagScript;

    public String getMeta() {
        return meta;
    }

    public void setMeta(String meta) {
        this.meta = meta;
    }

    public String getRefDsList() {
        return refDsList;
    }

    public void setRefDsList(String refDsList) {
        this.refDsList = refDsList;
    }

    public String getDagScript() {
        return dagScript;
    }

    public void setDagScript(String dagScript) {
        this.dagScript = dagScript;
    }
}