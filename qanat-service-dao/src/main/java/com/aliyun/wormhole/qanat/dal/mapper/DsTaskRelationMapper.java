package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.DsTaskRelation;
import com.aliyun.wormhole.qanat.dal.domain.DsTaskRelationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DsTaskRelationMapper {
    int countByExample(DsTaskRelationExample example);

    int deleteByExample(DsTaskRelationExample example);

    List<DsTaskRelation> selectByExample(DsTaskRelationExample example);

    int updateByExampleSelective(@Param("record") DsTaskRelation record, @Param("example") DsTaskRelationExample example);

    int updateByExample(@Param("record") DsTaskRelation record, @Param("example") DsTaskRelationExample example);

    List<DsTaskRelation> selectByExampleAndPage(DsTaskRelationExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(DsTaskRelation record);

    int insertSelective(DsTaskRelation record);

    DsTaskRelation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DsTaskRelation record);

    int updateByPrimaryKey(DsTaskRelation record);
}