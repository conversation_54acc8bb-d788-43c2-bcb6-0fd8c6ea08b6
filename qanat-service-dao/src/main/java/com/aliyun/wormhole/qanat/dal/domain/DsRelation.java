package com.aliyun.wormhole.qanat.dal.domain;

import java.io.Serializable;
import java.util.Date;

public class DsRelation implements Serializable {
    private Long id;

    private Date gmtCreate;

    private Date gmtModified;

    private String tenantId;

    private String srcDsName;

    private String dstDsName;

    private String relationType;

    private Long isDeleted;

    private String createEmpid;

    private String modifyEmpid;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getSrcDsName() {
        return srcDsName;
    }

    public void setSrcDsName(String srcDsName) {
        this.srcDsName = srcDsName;
    }

    public String getDstDsName() {
        return dstDsName;
    }

    public void setDstDsName(String dstDsName) {
        this.dstDsName = dstDsName;
    }

    public String getRelationType() {
        return relationType;
    }

    public void setRelationType(String relationType) {
        this.relationType = relationType;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getCreateEmpid() {
        return createEmpid;
    }

    public void setCreateEmpid(String createEmpid) {
        this.createEmpid = createEmpid;
    }

    public String getModifyEmpid() {
        return modifyEmpid;
    }

    public void setModifyEmpid(String modifyEmpid) {
        this.modifyEmpid = modifyEmpid;
    }
}