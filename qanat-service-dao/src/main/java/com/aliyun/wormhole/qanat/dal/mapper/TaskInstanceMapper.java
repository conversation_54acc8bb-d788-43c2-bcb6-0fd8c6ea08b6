package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.TaskInstance;
import com.aliyun.wormhole.qanat.dal.domain.TaskInstanceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface TaskInstanceMapper {
    int countByExample(TaskInstanceExample example);

    int deleteByExample(TaskInstanceExample example);

    List<TaskInstance> selectByExampleWithBLOBs(TaskInstanceExample example);

    List<TaskInstance> selectByExample(TaskInstanceExample example);

    int updateByExampleSelective(@Param("record") TaskInstance record, @Param("example") TaskInstanceExample example);

    int updateByExampleWithBLOBs(@Param("record") TaskInstance record, @Param("example") TaskInstanceExample example);

    int updateByExample(@Param("record") TaskInstance record, @Param("example") TaskInstanceExample example);

    List<TaskInstance> selectByExampleWithBLOBsAndPage(TaskInstanceExample example, RowBounds rowBound);

    List<TaskInstance> selectByExampleAndPage(TaskInstanceExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(TaskInstance record);

    int insertSelective(TaskInstance record);

    TaskInstance selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TaskInstance record);

    int updateByPrimaryKeyWithBLOBs(TaskInstance record);

    int updateByPrimaryKey(TaskInstance record);
}