package com.aliyun.wormhole.qanat.dal.domain;

import java.io.Serializable;
import java.util.Date;

public class DsCatelog implements Serializable {
    private Long id;

    private Date gmtCreate;

    private Date gmtModified;

    private String catelogName;

    private String catelogDesc;

    private Long isDeleted;

    private String createEmpid;

    private String modifyMepid;

    private String tenantId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getCatelogName() {
        return catelogName;
    }

    public void setCatelogName(String catelogName) {
        this.catelogName = catelogName;
    }

    public String getCatelogDesc() {
        return catelogDesc;
    }

    public void setCatelogDesc(String catelogDesc) {
        this.catelogDesc = catelogDesc;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getCreateEmpid() {
        return createEmpid;
    }

    public void setCreateEmpid(String createEmpid) {
        this.createEmpid = createEmpid;
    }

    public String getModifyMepid() {
        return modifyMepid;
    }

    public void setModifyMepid(String modifyMepid) {
        this.modifyMepid = modifyMepid;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}