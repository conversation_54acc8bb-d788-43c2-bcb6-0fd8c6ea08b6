package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelObj;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceModelObjExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DatatubeInstanceModelObjMapper {
    int countByExample(DatatubeInstanceModelObjExample example);

    int deleteByExample(DatatubeInstanceModelObjExample example);

    List<DatatubeInstanceModelObj> selectByExample(DatatubeInstanceModelObjExample example);

    int updateByExampleSelective(@Param("record") DatatubeInstanceModelObj record, @Param("example") DatatubeInstanceModelObjExample example);

    int updateByExample(@Param("record") DatatubeInstanceModelObj record, @Param("example") DatatubeInstanceModelObjExample example);

    List<DatatubeInstanceModelObj> selectByExampleAndPage(DatatubeInstanceModelObjExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(DatatubeInstanceModelObj record);

    int insertSelective(DatatubeInstanceModelObj record);

    DatatubeInstanceModelObj selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DatatubeInstanceModelObj record);

    int updateByPrimaryKey(DatatubeInstanceModelObj record);
}