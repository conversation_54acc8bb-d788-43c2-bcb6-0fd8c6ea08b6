package com.aliyun.wormhole.qanat.dal.domain;

import java.io.Serializable;
import java.util.Date;

public class SearchServiceVersion implements Serializable {
    /**
     * search_service_version.id (主键)
     * @ibatorgenerated 2019-08-19 16:15:29
     */
    private Long id;

    /**
     * search_service_version.gmt_create (创建时间)
     * @ibatorgenerated 2019-08-19 16:15:29
     */
    private Date gmtCreate;

    /**
     * search_service_version.create_empid (创建人工号)
     * @ibatorgenerated 2019-08-19 16:15:29
     */
    private String createEmpid;

    /**
     * search_service_version.cs_id (关联变更id)
     * @ibatorgenerated 2019-08-19 16:15:29
     */
    private Long csId;

    /**
     * search_service_version.version (版本号)
     * @ibatorgenerated 2019-08-19 16:15:29
     */
    private String version;

    /**
     * search_service_version.is_deleted (是否逻辑删除)
     * @ibatorgenerated 2019-08-19 16:15:29
     */
    private Long isDeleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getCreateEmpid() {
        return createEmpid;
    }

    public void setCreateEmpid(String createEmpid) {
        this.createEmpid = createEmpid;
    }

    public Long getCsId() {
        return csId;
    }

    public void setCsId(Long csId) {
        this.csId = csId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }
}