package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.DsCatelog;
import com.aliyun.wormhole.qanat.dal.domain.DsCatelogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DsCatelogMapper {
    int countByExample(DsCatelogExample example);

    int deleteByExample(DsCatelogExample example);

    List<DsCatelog> selectByExample(DsCatelogExample example);

    int updateByExampleSelective(@Param("record") DsCatelog record, @Param("example") DsCatelogExample example);

    int updateByExample(@Param("record") DsCatelog record, @Param("example") DsCatelogExample example);

    List<DsCatelog> selectByExampleAndPage(DsCatelogExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(DsCatelog record);

    int insertSelective(DsCatelog record);

    DsCatelog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DsCatelog record);

    int updateByPrimaryKey(DsCatelog record);
}