package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.DsRelation;
import com.aliyun.wormhole.qanat.dal.domain.DsRelationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DsRelationMapper {
    int countByExample(DsRelationExample example);

    int deleteByExample(DsRelationExample example);

    List<DsRelation> selectByExample(DsRelationExample example);

    int updateByExampleSelective(@Param("record") DsRelation record, @Param("example") DsRelationExample example);

    int updateByExample(@Param("record") DsRelation record, @Param("example") DsRelationExample example);

    List<DsRelation> selectByExampleAndPage(DsRelationExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(DsRelation record);

    int insertSelective(DsRelation record);

    DsRelation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DsRelation record);

    int updateByPrimaryKey(DsRelation record);
}