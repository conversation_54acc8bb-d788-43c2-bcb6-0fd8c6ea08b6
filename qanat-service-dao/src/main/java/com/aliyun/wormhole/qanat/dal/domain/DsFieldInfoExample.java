package com.aliyun.wormhole.qanat.dal.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DsFieldInfoExample {
    protected String pk_name = "id";

    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DsFieldInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setPk_name(String pk_name) {
        this.pk_name = pk_name;
    }

    public String getPk_name() {
        return pk_name;
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andFieldNameIsNull() {
            addCriterion("field_name is null");
            return (Criteria) this;
        }

        public Criteria andFieldNameIsNotNull() {
            addCriterion("field_name is not null");
            return (Criteria) this;
        }

        public Criteria andFieldNameEqualTo(String value) {
            addCriterion("field_name =", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameNotEqualTo(String value) {
            addCriterion("field_name <>", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameGreaterThan(String value) {
            addCriterion("field_name >", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameGreaterThanOrEqualTo(String value) {
            addCriterion("field_name >=", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameLessThan(String value) {
            addCriterion("field_name <", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameLessThanOrEqualTo(String value) {
            addCriterion("field_name <=", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameLike(String value) {
            addCriterion("field_name like", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameNotLike(String value) {
            addCriterion("field_name not like", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameIn(List<String> values) {
            addCriterion("field_name in", values, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameNotIn(List<String> values) {
            addCriterion("field_name not in", values, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameBetween(String value1, String value2) {
            addCriterion("field_name between", value1, value2, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameNotBetween(String value1, String value2) {
            addCriterion("field_name not between", value1, value2, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldUniqueNameIsNull() {
            addCriterion("field_unique_name is null");
            return (Criteria) this;
        }

        public Criteria andFieldUniqueNameIsNotNull() {
            addCriterion("field_unique_name is not null");
            return (Criteria) this;
        }

        public Criteria andFieldUniqueNameEqualTo(String value) {
            addCriterion("field_unique_name =", value, "fieldUniqueName");
            return (Criteria) this;
        }

        public Criteria andFieldUniqueNameNotEqualTo(String value) {
            addCriterion("field_unique_name <>", value, "fieldUniqueName");
            return (Criteria) this;
        }

        public Criteria andFieldUniqueNameGreaterThan(String value) {
            addCriterion("field_unique_name >", value, "fieldUniqueName");
            return (Criteria) this;
        }

        public Criteria andFieldUniqueNameGreaterThanOrEqualTo(String value) {
            addCriterion("field_unique_name >=", value, "fieldUniqueName");
            return (Criteria) this;
        }

        public Criteria andFieldUniqueNameLessThan(String value) {
            addCriterion("field_unique_name <", value, "fieldUniqueName");
            return (Criteria) this;
        }

        public Criteria andFieldUniqueNameLessThanOrEqualTo(String value) {
            addCriterion("field_unique_name <=", value, "fieldUniqueName");
            return (Criteria) this;
        }

        public Criteria andFieldUniqueNameLike(String value) {
            addCriterion("field_unique_name like", value, "fieldUniqueName");
            return (Criteria) this;
        }

        public Criteria andFieldUniqueNameNotLike(String value) {
            addCriterion("field_unique_name not like", value, "fieldUniqueName");
            return (Criteria) this;
        }

        public Criteria andFieldUniqueNameIn(List<String> values) {
            addCriterion("field_unique_name in", values, "fieldUniqueName");
            return (Criteria) this;
        }

        public Criteria andFieldUniqueNameNotIn(List<String> values) {
            addCriterion("field_unique_name not in", values, "fieldUniqueName");
            return (Criteria) this;
        }

        public Criteria andFieldUniqueNameBetween(String value1, String value2) {
            addCriterion("field_unique_name between", value1, value2, "fieldUniqueName");
            return (Criteria) this;
        }

        public Criteria andFieldUniqueNameNotBetween(String value1, String value2) {
            addCriterion("field_unique_name not between", value1, value2, "fieldUniqueName");
            return (Criteria) this;
        }

        public Criteria andDsNameIsNull() {
            addCriterion("ds_name is null");
            return (Criteria) this;
        }

        public Criteria andDsNameIsNotNull() {
            addCriterion("ds_name is not null");
            return (Criteria) this;
        }

        public Criteria andDsNameEqualTo(String value) {
            addCriterion("ds_name =", value, "dsName");
            return (Criteria) this;
        }

        public Criteria andDsNameNotEqualTo(String value) {
            addCriterion("ds_name <>", value, "dsName");
            return (Criteria) this;
        }

        public Criteria andDsNameGreaterThan(String value) {
            addCriterion("ds_name >", value, "dsName");
            return (Criteria) this;
        }

        public Criteria andDsNameGreaterThanOrEqualTo(String value) {
            addCriterion("ds_name >=", value, "dsName");
            return (Criteria) this;
        }

        public Criteria andDsNameLessThan(String value) {
            addCriterion("ds_name <", value, "dsName");
            return (Criteria) this;
        }

        public Criteria andDsNameLessThanOrEqualTo(String value) {
            addCriterion("ds_name <=", value, "dsName");
            return (Criteria) this;
        }

        public Criteria andDsNameLike(String value) {
            addCriterion("ds_name like", value, "dsName");
            return (Criteria) this;
        }

        public Criteria andDsNameNotLike(String value) {
            addCriterion("ds_name not like", value, "dsName");
            return (Criteria) this;
        }

        public Criteria andDsNameIn(List<String> values) {
            addCriterion("ds_name in", values, "dsName");
            return (Criteria) this;
        }

        public Criteria andDsNameNotIn(List<String> values) {
            addCriterion("ds_name not in", values, "dsName");
            return (Criteria) this;
        }

        public Criteria andDsNameBetween(String value1, String value2) {
            addCriterion("ds_name between", value1, value2, "dsName");
            return (Criteria) this;
        }

        public Criteria andDsNameNotBetween(String value1, String value2) {
            addCriterion("ds_name not between", value1, value2, "dsName");
            return (Criteria) this;
        }

        public Criteria andDsUniqueNameIsNull() {
            addCriterion("ds_unique_name is null");
            return (Criteria) this;
        }

        public Criteria andDsUniqueNameIsNotNull() {
            addCriterion("ds_unique_name is not null");
            return (Criteria) this;
        }

        public Criteria andDsUniqueNameEqualTo(String value) {
            addCriterion("ds_unique_name =", value, "dsUniqueName");
            return (Criteria) this;
        }

        public Criteria andDsUniqueNameNotEqualTo(String value) {
            addCriterion("ds_unique_name <>", value, "dsUniqueName");
            return (Criteria) this;
        }

        public Criteria andDsUniqueNameGreaterThan(String value) {
            addCriterion("ds_unique_name >", value, "dsUniqueName");
            return (Criteria) this;
        }

        public Criteria andDsUniqueNameGreaterThanOrEqualTo(String value) {
            addCriterion("ds_unique_name >=", value, "dsUniqueName");
            return (Criteria) this;
        }

        public Criteria andDsUniqueNameLessThan(String value) {
            addCriterion("ds_unique_name <", value, "dsUniqueName");
            return (Criteria) this;
        }

        public Criteria andDsUniqueNameLessThanOrEqualTo(String value) {
            addCriterion("ds_unique_name <=", value, "dsUniqueName");
            return (Criteria) this;
        }

        public Criteria andDsUniqueNameLike(String value) {
            addCriterion("ds_unique_name like", value, "dsUniqueName");
            return (Criteria) this;
        }

        public Criteria andDsUniqueNameNotLike(String value) {
            addCriterion("ds_unique_name not like", value, "dsUniqueName");
            return (Criteria) this;
        }

        public Criteria andDsUniqueNameIn(List<String> values) {
            addCriterion("ds_unique_name in", values, "dsUniqueName");
            return (Criteria) this;
        }

        public Criteria andDsUniqueNameNotIn(List<String> values) {
            addCriterion("ds_unique_name not in", values, "dsUniqueName");
            return (Criteria) this;
        }

        public Criteria andDsUniqueNameBetween(String value1, String value2) {
            addCriterion("ds_unique_name between", value1, value2, "dsUniqueName");
            return (Criteria) this;
        }

        public Criteria andDsUniqueNameNotBetween(String value1, String value2) {
            addCriterion("ds_unique_name not between", value1, value2, "dsUniqueName");
            return (Criteria) this;
        }

        public Criteria andDbNameIsNull() {
            addCriterion("db_name is null");
            return (Criteria) this;
        }

        public Criteria andDbNameIsNotNull() {
            addCriterion("db_name is not null");
            return (Criteria) this;
        }

        public Criteria andDbNameEqualTo(String value) {
            addCriterion("db_name =", value, "dbName");
            return (Criteria) this;
        }

        public Criteria andDbNameNotEqualTo(String value) {
            addCriterion("db_name <>", value, "dbName");
            return (Criteria) this;
        }

        public Criteria andDbNameGreaterThan(String value) {
            addCriterion("db_name >", value, "dbName");
            return (Criteria) this;
        }

        public Criteria andDbNameGreaterThanOrEqualTo(String value) {
            addCriterion("db_name >=", value, "dbName");
            return (Criteria) this;
        }

        public Criteria andDbNameLessThan(String value) {
            addCriterion("db_name <", value, "dbName");
            return (Criteria) this;
        }

        public Criteria andDbNameLessThanOrEqualTo(String value) {
            addCriterion("db_name <=", value, "dbName");
            return (Criteria) this;
        }

        public Criteria andDbNameLike(String value) {
            addCriterion("db_name like", value, "dbName");
            return (Criteria) this;
        }

        public Criteria andDbNameNotLike(String value) {
            addCriterion("db_name not like", value, "dbName");
            return (Criteria) this;
        }

        public Criteria andDbNameIn(List<String> values) {
            addCriterion("db_name in", values, "dbName");
            return (Criteria) this;
        }

        public Criteria andDbNameNotIn(List<String> values) {
            addCriterion("db_name not in", values, "dbName");
            return (Criteria) this;
        }

        public Criteria andDbNameBetween(String value1, String value2) {
            addCriterion("db_name between", value1, value2, "dbName");
            return (Criteria) this;
        }

        public Criteria andDbNameNotBetween(String value1, String value2) {
            addCriterion("db_name not between", value1, value2, "dbName");
            return (Criteria) this;
        }

        public Criteria andFieldTypeIsNull() {
            addCriterion("field_type is null");
            return (Criteria) this;
        }

        public Criteria andFieldTypeIsNotNull() {
            addCriterion("field_type is not null");
            return (Criteria) this;
        }

        public Criteria andFieldTypeEqualTo(String value) {
            addCriterion("field_type =", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeNotEqualTo(String value) {
            addCriterion("field_type <>", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeGreaterThan(String value) {
            addCriterion("field_type >", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeGreaterThanOrEqualTo(String value) {
            addCriterion("field_type >=", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeLessThan(String value) {
            addCriterion("field_type <", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeLessThanOrEqualTo(String value) {
            addCriterion("field_type <=", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeLike(String value) {
            addCriterion("field_type like", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeNotLike(String value) {
            addCriterion("field_type not like", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeIn(List<String> values) {
            addCriterion("field_type in", values, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeNotIn(List<String> values) {
            addCriterion("field_type not in", values, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeBetween(String value1, String value2) {
            addCriterion("field_type between", value1, value2, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeNotBetween(String value1, String value2) {
            addCriterion("field_type not between", value1, value2, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldDescIsNull() {
            addCriterion("field_desc is null");
            return (Criteria) this;
        }

        public Criteria andFieldDescIsNotNull() {
            addCriterion("field_desc is not null");
            return (Criteria) this;
        }

        public Criteria andFieldDescEqualTo(String value) {
            addCriterion("field_desc =", value, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescNotEqualTo(String value) {
            addCriterion("field_desc <>", value, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescGreaterThan(String value) {
            addCriterion("field_desc >", value, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescGreaterThanOrEqualTo(String value) {
            addCriterion("field_desc >=", value, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescLessThan(String value) {
            addCriterion("field_desc <", value, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescLessThanOrEqualTo(String value) {
            addCriterion("field_desc <=", value, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescLike(String value) {
            addCriterion("field_desc like", value, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescNotLike(String value) {
            addCriterion("field_desc not like", value, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescIn(List<String> values) {
            addCriterion("field_desc in", values, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescNotIn(List<String> values) {
            addCriterion("field_desc not in", values, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescBetween(String value1, String value2) {
            addCriterion("field_desc between", value1, value2, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andFieldDescNotBetween(String value1, String value2) {
            addCriterion("field_desc not between", value1, value2, "fieldDesc");
            return (Criteria) this;
        }

        public Criteria andIsPkIsNull() {
            addCriterion("is_pk is null");
            return (Criteria) this;
        }

        public Criteria andIsPkIsNotNull() {
            addCriterion("is_pk is not null");
            return (Criteria) this;
        }

        public Criteria andIsPkEqualTo(Byte value) {
            addCriterion("is_pk =", value, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkNotEqualTo(Byte value) {
            addCriterion("is_pk <>", value, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkGreaterThan(Byte value) {
            addCriterion("is_pk >", value, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_pk >=", value, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkLessThan(Byte value) {
            addCriterion("is_pk <", value, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkLessThanOrEqualTo(Byte value) {
            addCriterion("is_pk <=", value, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkIn(List<Byte> values) {
            addCriterion("is_pk in", values, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkNotIn(List<Byte> values) {
            addCriterion("is_pk not in", values, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkBetween(Byte value1, Byte value2) {
            addCriterion("is_pk between", value1, value2, "isPk");
            return (Criteria) this;
        }

        public Criteria andIsPkNotBetween(Byte value1, Byte value2) {
            addCriterion("is_pk not between", value1, value2, "isPk");
            return (Criteria) this;
        }

        public Criteria andDefaultValueIsNull() {
            addCriterion("default_value is null");
            return (Criteria) this;
        }

        public Criteria andDefaultValueIsNotNull() {
            addCriterion("default_value is not null");
            return (Criteria) this;
        }

        public Criteria andDefaultValueEqualTo(String value) {
            addCriterion("default_value =", value, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueNotEqualTo(String value) {
            addCriterion("default_value <>", value, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueGreaterThan(String value) {
            addCriterion("default_value >", value, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueGreaterThanOrEqualTo(String value) {
            addCriterion("default_value >=", value, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueLessThan(String value) {
            addCriterion("default_value <", value, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueLessThanOrEqualTo(String value) {
            addCriterion("default_value <=", value, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueLike(String value) {
            addCriterion("default_value like", value, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueNotLike(String value) {
            addCriterion("default_value not like", value, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueIn(List<String> values) {
            addCriterion("default_value in", values, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueNotIn(List<String> values) {
            addCriterion("default_value not in", values, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueBetween(String value1, String value2) {
            addCriterion("default_value between", value1, value2, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueNotBetween(String value1, String value2) {
            addCriterion("default_value not between", value1, value2, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andIsNotNullIsNull() {
            addCriterion("is_not_null is null");
            return (Criteria) this;
        }

        public Criteria andIsNotNullIsNotNull() {
            addCriterion("is_not_null is not null");
            return (Criteria) this;
        }

        public Criteria andIsNotNullEqualTo(Byte value) {
            addCriterion("is_not_null =", value, "isNotNull");
            return (Criteria) this;
        }

        public Criteria andIsNotNullNotEqualTo(Byte value) {
            addCriterion("is_not_null <>", value, "isNotNull");
            return (Criteria) this;
        }

        public Criteria andIsNotNullGreaterThan(Byte value) {
            addCriterion("is_not_null >", value, "isNotNull");
            return (Criteria) this;
        }

        public Criteria andIsNotNullGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_not_null >=", value, "isNotNull");
            return (Criteria) this;
        }

        public Criteria andIsNotNullLessThan(Byte value) {
            addCriterion("is_not_null <", value, "isNotNull");
            return (Criteria) this;
        }

        public Criteria andIsNotNullLessThanOrEqualTo(Byte value) {
            addCriterion("is_not_null <=", value, "isNotNull");
            return (Criteria) this;
        }

        public Criteria andIsNotNullIn(List<Byte> values) {
            addCriterion("is_not_null in", values, "isNotNull");
            return (Criteria) this;
        }

        public Criteria andIsNotNullNotIn(List<Byte> values) {
            addCriterion("is_not_null not in", values, "isNotNull");
            return (Criteria) this;
        }

        public Criteria andIsNotNullBetween(Byte value1, Byte value2) {
            addCriterion("is_not_null between", value1, value2, "isNotNull");
            return (Criteria) this;
        }

        public Criteria andIsNotNullNotBetween(Byte value1, Byte value2) {
            addCriterion("is_not_null not between", value1, value2, "isNotNull");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Long value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Long value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Long value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Long value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Long value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Long value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Long> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Long> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Long value1, Long value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Long value1, Long value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIsNull() {
            addCriterion("create_empid is null");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIsNotNull() {
            addCriterion("create_empid is not null");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidEqualTo(String value) {
            addCriterion("create_empid =", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotEqualTo(String value) {
            addCriterion("create_empid <>", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidGreaterThan(String value) {
            addCriterion("create_empid >", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidGreaterThanOrEqualTo(String value) {
            addCriterion("create_empid >=", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLessThan(String value) {
            addCriterion("create_empid <", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLessThanOrEqualTo(String value) {
            addCriterion("create_empid <=", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLike(String value) {
            addCriterion("create_empid like", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotLike(String value) {
            addCriterion("create_empid not like", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIn(List<String> values) {
            addCriterion("create_empid in", values, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotIn(List<String> values) {
            addCriterion("create_empid not in", values, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidBetween(String value1, String value2) {
            addCriterion("create_empid between", value1, value2, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotBetween(String value1, String value2) {
            addCriterion("create_empid not between", value1, value2, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIsNull() {
            addCriterion("modify_empid is null");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIsNotNull() {
            addCriterion("modify_empid is not null");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidEqualTo(String value) {
            addCriterion("modify_empid =", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotEqualTo(String value) {
            addCriterion("modify_empid <>", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidGreaterThan(String value) {
            addCriterion("modify_empid >", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidGreaterThanOrEqualTo(String value) {
            addCriterion("modify_empid >=", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLessThan(String value) {
            addCriterion("modify_empid <", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLessThanOrEqualTo(String value) {
            addCriterion("modify_empid <=", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLike(String value) {
            addCriterion("modify_empid like", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotLike(String value) {
            addCriterion("modify_empid not like", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIn(List<String> values) {
            addCriterion("modify_empid in", values, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotIn(List<String> values) {
            addCriterion("modify_empid not in", values, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidBetween(String value1, String value2) {
            addCriterion("modify_empid between", value1, value2, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotBetween(String value1, String value2) {
            addCriterion("modify_empid not between", value1, value2, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andSysTypeIsNull() {
            addCriterion("sys_type is null");
            return (Criteria) this;
        }

        public Criteria andSysTypeIsNotNull() {
            addCriterion("sys_type is not null");
            return (Criteria) this;
        }

        public Criteria andSysTypeEqualTo(String value) {
            addCriterion("sys_type =", value, "sysType");
            return (Criteria) this;
        }

        public Criteria andSysTypeNotEqualTo(String value) {
            addCriterion("sys_type <>", value, "sysType");
            return (Criteria) this;
        }

        public Criteria andSysTypeGreaterThan(String value) {
            addCriterion("sys_type >", value, "sysType");
            return (Criteria) this;
        }

        public Criteria andSysTypeGreaterThanOrEqualTo(String value) {
            addCriterion("sys_type >=", value, "sysType");
            return (Criteria) this;
        }

        public Criteria andSysTypeLessThan(String value) {
            addCriterion("sys_type <", value, "sysType");
            return (Criteria) this;
        }

        public Criteria andSysTypeLessThanOrEqualTo(String value) {
            addCriterion("sys_type <=", value, "sysType");
            return (Criteria) this;
        }

        public Criteria andSysTypeLike(String value) {
            addCriterion("sys_type like", value, "sysType");
            return (Criteria) this;
        }

        public Criteria andSysTypeNotLike(String value) {
            addCriterion("sys_type not like", value, "sysType");
            return (Criteria) this;
        }

        public Criteria andSysTypeIn(List<String> values) {
            addCriterion("sys_type in", values, "sysType");
            return (Criteria) this;
        }

        public Criteria andSysTypeNotIn(List<String> values) {
            addCriterion("sys_type not in", values, "sysType");
            return (Criteria) this;
        }

        public Criteria andSysTypeBetween(String value1, String value2) {
            addCriterion("sys_type between", value1, value2, "sysType");
            return (Criteria) this;
        }

        public Criteria andSysTypeNotBetween(String value1, String value2) {
            addCriterion("sys_type not between", value1, value2, "sysType");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andIsFkIsNull() {
            addCriterion("is_fk is null");
            return (Criteria) this;
        }

        public Criteria andIsFkIsNotNull() {
            addCriterion("is_fk is not null");
            return (Criteria) this;
        }

        public Criteria andIsFkEqualTo(Integer value) {
            addCriterion("is_fk =", value, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkNotEqualTo(Integer value) {
            addCriterion("is_fk <>", value, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkGreaterThan(Integer value) {
            addCriterion("is_fk >", value, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_fk >=", value, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkLessThan(Integer value) {
            addCriterion("is_fk <", value, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkLessThanOrEqualTo(Integer value) {
            addCriterion("is_fk <=", value, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkIn(List<Integer> values) {
            addCriterion("is_fk in", values, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkNotIn(List<Integer> values) {
            addCriterion("is_fk not in", values, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkBetween(Integer value1, Integer value2) {
            addCriterion("is_fk between", value1, value2, "isFk");
            return (Criteria) this;
        }

        public Criteria andIsFkNotBetween(Integer value1, Integer value2) {
            addCriterion("is_fk not between", value1, value2, "isFk");
            return (Criteria) this;
        }

        public Criteria andFkObjectTypeIsNull() {
            addCriterion("fk_object_type is null");
            return (Criteria) this;
        }

        public Criteria andFkObjectTypeIsNotNull() {
            addCriterion("fk_object_type is not null");
            return (Criteria) this;
        }

        public Criteria andFkObjectTypeEqualTo(String value) {
            addCriterion("fk_object_type =", value, "fkObjectType");
            return (Criteria) this;
        }

        public Criteria andFkObjectTypeNotEqualTo(String value) {
            addCriterion("fk_object_type <>", value, "fkObjectType");
            return (Criteria) this;
        }

        public Criteria andFkObjectTypeGreaterThan(String value) {
            addCriterion("fk_object_type >", value, "fkObjectType");
            return (Criteria) this;
        }

        public Criteria andFkObjectTypeGreaterThanOrEqualTo(String value) {
            addCriterion("fk_object_type >=", value, "fkObjectType");
            return (Criteria) this;
        }

        public Criteria andFkObjectTypeLessThan(String value) {
            addCriterion("fk_object_type <", value, "fkObjectType");
            return (Criteria) this;
        }

        public Criteria andFkObjectTypeLessThanOrEqualTo(String value) {
            addCriterion("fk_object_type <=", value, "fkObjectType");
            return (Criteria) this;
        }

        public Criteria andFkObjectTypeLike(String value) {
            addCriterion("fk_object_type like", value, "fkObjectType");
            return (Criteria) this;
        }

        public Criteria andFkObjectTypeNotLike(String value) {
            addCriterion("fk_object_type not like", value, "fkObjectType");
            return (Criteria) this;
        }

        public Criteria andFkObjectTypeIn(List<String> values) {
            addCriterion("fk_object_type in", values, "fkObjectType");
            return (Criteria) this;
        }

        public Criteria andFkObjectTypeNotIn(List<String> values) {
            addCriterion("fk_object_type not in", values, "fkObjectType");
            return (Criteria) this;
        }

        public Criteria andFkObjectTypeBetween(String value1, String value2) {
            addCriterion("fk_object_type between", value1, value2, "fkObjectType");
            return (Criteria) this;
        }

        public Criteria andFkObjectTypeNotBetween(String value1, String value2) {
            addCriterion("fk_object_type not between", value1, value2, "fkObjectType");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLike(String value) {
            addCriterion("ext_info like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLike(String value) {
            addCriterion("ext_info not like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andDataTypeIsNull() {
            addCriterion("data_type is null");
            return (Criteria) this;
        }

        public Criteria andDataTypeIsNotNull() {
            addCriterion("data_type is not null");
            return (Criteria) this;
        }

        public Criteria andDataTypeEqualTo(String value) {
            addCriterion("data_type =", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotEqualTo(String value) {
            addCriterion("data_type <>", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeGreaterThan(String value) {
            addCriterion("data_type >", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeGreaterThanOrEqualTo(String value) {
            addCriterion("data_type >=", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeLessThan(String value) {
            addCriterion("data_type <", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeLessThanOrEqualTo(String value) {
            addCriterion("data_type <=", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeLike(String value) {
            addCriterion("data_type like", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotLike(String value) {
            addCriterion("data_type not like", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeIn(List<String> values) {
            addCriterion("data_type in", values, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotIn(List<String> values) {
            addCriterion("data_type not in", values, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeBetween(String value1, String value2) {
            addCriterion("data_type between", value1, value2, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotBetween(String value1, String value2) {
            addCriterion("data_type not between", value1, value2, "dataType");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}