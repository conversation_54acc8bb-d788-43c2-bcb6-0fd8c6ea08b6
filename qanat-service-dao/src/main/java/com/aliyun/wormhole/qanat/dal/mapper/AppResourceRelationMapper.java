package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelation;
import com.aliyun.wormhole.qanat.dal.domain.AppResourceRelationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface AppResourceRelationMapper {
    int countByExample(AppResourceRelationExample example);

    int deleteByExample(AppResourceRelationExample example);

    List<AppResourceRelation> selectByExample(AppResourceRelationExample example);

    int updateByExampleSelective(@Param("record") AppResourceRelation record, @Param("example") AppResourceRelationExample example);

    int updateByExample(@Param("record") AppResourceRelation record, @Param("example") AppResourceRelationExample example);

    List<AppResourceRelation> selectByExampleAndPage(AppResourceRelationExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(AppResourceRelation record);

    int insertSelective(AppResourceRelation record);

    AppResourceRelation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AppResourceRelation record);

    int updateByPrimaryKey(AppResourceRelation record);
}