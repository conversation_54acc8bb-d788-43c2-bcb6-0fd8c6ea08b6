package com.aliyun.wormhole.qanat.dal.domain;

import java.io.Serializable;
import java.util.Date;

public class ServiceChangeset implements Serializable {
    /**
     * service_changeset.id (主键)
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    private Long id;

    /**
     * service_changeset.gmt_create (创建时间)
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    private Date gmtCreate;

    /**
     * service_changeset.gmt_modified (修改时间)
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    private Date gmtModified;

    /**
     * service_changeset.cs_type (变更类型)
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    private String csType;

    /**
     * service_changeset.cs_desc (表更内容描述)
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    private String csDesc;

    /**
     * service_changeset.remark (备注)
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    private String remark;

    /**
     * service_changeset.status (状态)
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    private Integer status;

    /**
     * service_changeset.create_empid (创建人工号)
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    private String createEmpid;

    /**
     * service_changeset.modify_empid (修改人工号)
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    private String modifyEmpid;

    /**
     * service_changeset.service_id (服务id)
     * @ibatorgenerated 2019-08-19 16:15:41
     */
    private Long serviceId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getCsType() {
        return csType;
    }

    public void setCsType(String csType) {
        this.csType = csType;
    }

    public String getCsDesc() {
        return csDesc;
    }

    public void setCsDesc(String csDesc) {
        this.csDesc = csDesc;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreateEmpid() {
        return createEmpid;
    }

    public void setCreateEmpid(String createEmpid) {
        this.createEmpid = createEmpid;
    }

    public String getModifyEmpid() {
        return modifyEmpid;
    }

    public void setModifyEmpid(String modifyEmpid) {
        this.modifyEmpid = modifyEmpid;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }
}