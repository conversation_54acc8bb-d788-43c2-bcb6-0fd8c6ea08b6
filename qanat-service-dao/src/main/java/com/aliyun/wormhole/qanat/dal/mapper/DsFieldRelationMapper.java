package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.DsFieldRelation;
import com.aliyun.wormhole.qanat.dal.domain.DsFieldRelationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DsFieldRelationMapper {
    int countByExample(DsFieldRelationExample example);

    int deleteByExample(DsFieldRelationExample example);

    List<DsFieldRelation> selectByExample(DsFieldRelationExample example);

    int updateByExampleSelective(@Param("record") DsFieldRelation record, @Param("example") DsFieldRelationExample example);

    int updateByExample(@Param("record") DsFieldRelation record, @Param("example") DsFieldRelationExample example);

    List<DsFieldRelation> selectByExampleAndPage(DsFieldRelationExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(DsFieldRelation record);

    int insertSelective(DsFieldRelation record);

    DsFieldRelation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DsFieldRelation record);

    int updateByPrimaryKey(DsFieldRelation record);
}