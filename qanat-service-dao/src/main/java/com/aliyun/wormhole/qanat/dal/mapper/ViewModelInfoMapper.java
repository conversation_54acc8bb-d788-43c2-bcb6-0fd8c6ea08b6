package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfo;
import com.aliyun.wormhole.qanat.dal.domain.ViewModelInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface ViewModelInfoMapper {
    int countByExample(ViewModelInfoExample example);

    int deleteByExample(ViewModelInfoExample example);

    List<ViewModelInfo> selectByExample(ViewModelInfoExample example);

    int updateByExampleSelective(@Param("record") ViewModelInfo record, @Param("example") ViewModelInfoExample example);

    int updateByExample(@Param("record") ViewModelInfo record, @Param("example") ViewModelInfoExample example);

    List<ViewModelInfo> selectByExampleAndPage(ViewModelInfoExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(ViewModelInfo record);

    int insertSelective(ViewModelInfo record);

    ViewModelInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ViewModelInfo record);

    int updateByPrimaryKey(ViewModelInfo record);
}