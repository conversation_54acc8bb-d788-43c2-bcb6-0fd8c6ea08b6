package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.Extension;
import com.aliyun.wormhole.qanat.dal.domain.ExtensionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface ExtensionMapper {
    int countByExample(ExtensionExample example);

    int deleteByExample(ExtensionExample example);

    List<Extension> selectByExampleWithBLOBs(ExtensionExample example);

    List<Extension> selectByExample(ExtensionExample example);

    int updateByExampleSelective(@Param("record") Extension record, @Param("example") ExtensionExample example);

    int updateByExampleWithBLOBs(@Param("record") Extension record, @Param("example") ExtensionExample example);

    int updateByExample(@Param("record") Extension record, @Param("example") ExtensionExample example);

    List<Extension> selectByExampleWithBLOBsAndPage(ExtensionExample example, RowBounds rowBound);

    List<Extension> selectByExampleAndPage(ExtensionExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(Extension record);

    int insertSelective(Extension record);

    Extension selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Extension record);

    int updateByPrimaryKeyWithBLOBs(Extension record);

    int updateByPrimaryKey(Extension record);
}