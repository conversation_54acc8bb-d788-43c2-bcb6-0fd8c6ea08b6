package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceDsRelation;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceDsRelationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DatatubeInstanceDsRelationMapper {
    int countByExample(DatatubeInstanceDsRelationExample example);

    int deleteByExample(DatatubeInstanceDsRelationExample example);

    List<DatatubeInstanceDsRelation> selectByExample(DatatubeInstanceDsRelationExample example);

    int updateByExampleSelective(@Param("record") DatatubeInstanceDsRelation record, @Param("example") DatatubeInstanceDsRelationExample example);

    int updateByExample(@Param("record") DatatubeInstanceDsRelation record, @Param("example") DatatubeInstanceDsRelationExample example);

    List<DatatubeInstanceDsRelation> selectByExampleAndPage(DatatubeInstanceDsRelationExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(DatatubeInstanceDsRelation record);

    int insertSelective(DatatubeInstanceDsRelation record);

    DatatubeInstanceDsRelation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DatatubeInstanceDsRelation record);

    int updateByPrimaryKey(DatatubeInstanceDsRelation record);
}