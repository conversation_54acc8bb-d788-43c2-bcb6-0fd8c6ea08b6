package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.AppInfo;
import com.aliyun.wormhole.qanat.dal.domain.AppInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface AppInfoMapper {
    int countByExample(AppInfoExample example);

    int deleteByExample(AppInfoExample example);

    List<AppInfo> selectByExample(AppInfoExample example);

    int updateByExampleSelective(@Param("record") AppInfo record, @Param("example") AppInfoExample example);

    int updateByExample(@Param("record") AppInfo record, @Param("example") AppInfoExample example);

    List<AppInfo> selectByExampleAndPage(AppInfoExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(AppInfo record);

    int insertSelective(AppInfo record);

    AppInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AppInfo record);

    int updateByPrimaryKey(AppInfo record);
}