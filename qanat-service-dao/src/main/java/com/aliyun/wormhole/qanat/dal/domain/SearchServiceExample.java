package com.aliyun.wormhole.qanat.dal.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SearchServiceExample {
    /**
     * 主键字段
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    protected String pk_name = "id";

    /**
     * 排序字段
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    protected String orderByClause;

    /**
     * 去重复
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    protected boolean distinct;

    /**
     * 条件集
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    protected List<Criteria> oredCriteria;

    public SearchServiceExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setPk_name(String pk_name) {
        this.pk_name = pk_name;
    }

    public String getPk_name() {
        return pk_name;
    }

    /**
     * 排序字段
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * 设置去重复
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * 条件查询要先创建Criteria
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * 
     * 内类部，系统内部调用1
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andSvcNameIsNull() {
            addCriterion("svc_name is null");
            return (Criteria) this;
        }

        public Criteria andSvcNameIsNotNull() {
            addCriterion("svc_name is not null");
            return (Criteria) this;
        }

        public Criteria andSvcNameEqualTo(String value) {
            addCriterion("svc_name =", value, "svcName");
            return (Criteria) this;
        }

        public Criteria andSvcNameNotEqualTo(String value) {
            addCriterion("svc_name <>", value, "svcName");
            return (Criteria) this;
        }

        public Criteria andSvcNameGreaterThan(String value) {
            addCriterion("svc_name >", value, "svcName");
            return (Criteria) this;
        }

        public Criteria andSvcNameGreaterThanOrEqualTo(String value) {
            addCriterion("svc_name >=", value, "svcName");
            return (Criteria) this;
        }

        public Criteria andSvcNameLessThan(String value) {
            addCriterion("svc_name <", value, "svcName");
            return (Criteria) this;
        }

        public Criteria andSvcNameLessThanOrEqualTo(String value) {
            addCriterion("svc_name <=", value, "svcName");
            return (Criteria) this;
        }

        public Criteria andSvcNameLike(String value) {
            addCriterion("svc_name like", value, "svcName");
            return (Criteria) this;
        }

        public Criteria andSvcNameNotLike(String value) {
            addCriterion("svc_name not like", value, "svcName");
            return (Criteria) this;
        }

        public Criteria andSvcNameIn(List<String> values) {
            addCriterion("svc_name in", values, "svcName");
            return (Criteria) this;
        }

        public Criteria andSvcNameNotIn(List<String> values) {
            addCriterion("svc_name not in", values, "svcName");
            return (Criteria) this;
        }

        public Criteria andSvcNameBetween(String value1, String value2) {
            addCriterion("svc_name between", value1, value2, "svcName");
            return (Criteria) this;
        }

        public Criteria andSvcNameNotBetween(String value1, String value2) {
            addCriterion("svc_name not between", value1, value2, "svcName");
            return (Criteria) this;
        }

        public Criteria andSvcDescIsNull() {
            addCriterion("svc_desc is null");
            return (Criteria) this;
        }

        public Criteria andSvcDescIsNotNull() {
            addCriterion("svc_desc is not null");
            return (Criteria) this;
        }

        public Criteria andSvcDescEqualTo(String value) {
            addCriterion("svc_desc =", value, "svcDesc");
            return (Criteria) this;
        }

        public Criteria andSvcDescNotEqualTo(String value) {
            addCriterion("svc_desc <>", value, "svcDesc");
            return (Criteria) this;
        }

        public Criteria andSvcDescGreaterThan(String value) {
            addCriterion("svc_desc >", value, "svcDesc");
            return (Criteria) this;
        }

        public Criteria andSvcDescGreaterThanOrEqualTo(String value) {
            addCriterion("svc_desc >=", value, "svcDesc");
            return (Criteria) this;
        }

        public Criteria andSvcDescLessThan(String value) {
            addCriterion("svc_desc <", value, "svcDesc");
            return (Criteria) this;
        }

        public Criteria andSvcDescLessThanOrEqualTo(String value) {
            addCriterion("svc_desc <=", value, "svcDesc");
            return (Criteria) this;
        }

        public Criteria andSvcDescLike(String value) {
            addCriterion("svc_desc like", value, "svcDesc");
            return (Criteria) this;
        }

        public Criteria andSvcDescNotLike(String value) {
            addCriterion("svc_desc not like", value, "svcDesc");
            return (Criteria) this;
        }

        public Criteria andSvcDescIn(List<String> values) {
            addCriterion("svc_desc in", values, "svcDesc");
            return (Criteria) this;
        }

        public Criteria andSvcDescNotIn(List<String> values) {
            addCriterion("svc_desc not in", values, "svcDesc");
            return (Criteria) this;
        }

        public Criteria andSvcDescBetween(String value1, String value2) {
            addCriterion("svc_desc between", value1, value2, "svcDesc");
            return (Criteria) this;
        }

        public Criteria andSvcDescNotBetween(String value1, String value2) {
            addCriterion("svc_desc not between", value1, value2, "svcDesc");
            return (Criteria) this;
        }

        public Criteria andBizLineIsNull() {
            addCriterion("biz_line is null");
            return (Criteria) this;
        }

        public Criteria andBizLineIsNotNull() {
            addCriterion("biz_line is not null");
            return (Criteria) this;
        }

        public Criteria andBizLineEqualTo(String value) {
            addCriterion("biz_line =", value, "bizLine");
            return (Criteria) this;
        }

        public Criteria andBizLineNotEqualTo(String value) {
            addCriterion("biz_line <>", value, "bizLine");
            return (Criteria) this;
        }

        public Criteria andBizLineGreaterThan(String value) {
            addCriterion("biz_line >", value, "bizLine");
            return (Criteria) this;
        }

        public Criteria andBizLineGreaterThanOrEqualTo(String value) {
            addCriterion("biz_line >=", value, "bizLine");
            return (Criteria) this;
        }

        public Criteria andBizLineLessThan(String value) {
            addCriterion("biz_line <", value, "bizLine");
            return (Criteria) this;
        }

        public Criteria andBizLineLessThanOrEqualTo(String value) {
            addCriterion("biz_line <=", value, "bizLine");
            return (Criteria) this;
        }

        public Criteria andBizLineLike(String value) {
            addCriterion("biz_line like", value, "bizLine");
            return (Criteria) this;
        }

        public Criteria andBizLineNotLike(String value) {
            addCriterion("biz_line not like", value, "bizLine");
            return (Criteria) this;
        }

        public Criteria andBizLineIn(List<String> values) {
            addCriterion("biz_line in", values, "bizLine");
            return (Criteria) this;
        }

        public Criteria andBizLineNotIn(List<String> values) {
            addCriterion("biz_line not in", values, "bizLine");
            return (Criteria) this;
        }

        public Criteria andBizLineBetween(String value1, String value2) {
            addCriterion("biz_line between", value1, value2, "bizLine");
            return (Criteria) this;
        }

        public Criteria andBizLineNotBetween(String value1, String value2) {
            addCriterion("biz_line not between", value1, value2, "bizLine");
            return (Criteria) this;
        }

        public Criteria andAppKeyIsNull() {
            addCriterion("app_key is null");
            return (Criteria) this;
        }

        public Criteria andAppKeyIsNotNull() {
            addCriterion("app_key is not null");
            return (Criteria) this;
        }

        public Criteria andAppKeyEqualTo(String value) {
            addCriterion("app_key =", value, "appKey");
            return (Criteria) this;
        }

        public Criteria andAppKeyNotEqualTo(String value) {
            addCriterion("app_key <>", value, "appKey");
            return (Criteria) this;
        }

        public Criteria andAppKeyGreaterThan(String value) {
            addCriterion("app_key >", value, "appKey");
            return (Criteria) this;
        }

        public Criteria andAppKeyGreaterThanOrEqualTo(String value) {
            addCriterion("app_key >=", value, "appKey");
            return (Criteria) this;
        }

        public Criteria andAppKeyLessThan(String value) {
            addCriterion("app_key <", value, "appKey");
            return (Criteria) this;
        }

        public Criteria andAppKeyLessThanOrEqualTo(String value) {
            addCriterion("app_key <=", value, "appKey");
            return (Criteria) this;
        }

        public Criteria andAppKeyLike(String value) {
            addCriterion("app_key like", value, "appKey");
            return (Criteria) this;
        }

        public Criteria andAppKeyNotLike(String value) {
            addCriterion("app_key not like", value, "appKey");
            return (Criteria) this;
        }

        public Criteria andAppKeyIn(List<String> values) {
            addCriterion("app_key in", values, "appKey");
            return (Criteria) this;
        }

        public Criteria andAppKeyNotIn(List<String> values) {
            addCriterion("app_key not in", values, "appKey");
            return (Criteria) this;
        }

        public Criteria andAppKeyBetween(String value1, String value2) {
            addCriterion("app_key between", value1, value2, "appKey");
            return (Criteria) this;
        }

        public Criteria andAppKeyNotBetween(String value1, String value2) {
            addCriterion("app_key not between", value1, value2, "appKey");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(String value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(String value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(String value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(String value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(String value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(String value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLike(String value) {
            addCriterion("app_id like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotLike(String value) {
            addCriterion("app_id not like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<String> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<String> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(String value1, String value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(String value1, String value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIsNull() {
            addCriterion("create_empid is null");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIsNotNull() {
            addCriterion("create_empid is not null");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidEqualTo(String value) {
            addCriterion("create_empid =", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotEqualTo(String value) {
            addCriterion("create_empid <>", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidGreaterThan(String value) {
            addCriterion("create_empid >", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidGreaterThanOrEqualTo(String value) {
            addCriterion("create_empid >=", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLessThan(String value) {
            addCriterion("create_empid <", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLessThanOrEqualTo(String value) {
            addCriterion("create_empid <=", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLike(String value) {
            addCriterion("create_empid like", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotLike(String value) {
            addCriterion("create_empid not like", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIn(List<String> values) {
            addCriterion("create_empid in", values, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotIn(List<String> values) {
            addCriterion("create_empid not in", values, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidBetween(String value1, String value2) {
            addCriterion("create_empid between", value1, value2, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotBetween(String value1, String value2) {
            addCriterion("create_empid not between", value1, value2, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIsNull() {
            addCriterion("modify_empid is null");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIsNotNull() {
            addCriterion("modify_empid is not null");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidEqualTo(String value) {
            addCriterion("modify_empid =", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotEqualTo(String value) {
            addCriterion("modify_empid <>", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidGreaterThan(String value) {
            addCriterion("modify_empid >", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidGreaterThanOrEqualTo(String value) {
            addCriterion("modify_empid >=", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLessThan(String value) {
            addCriterion("modify_empid <", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLessThanOrEqualTo(String value) {
            addCriterion("modify_empid <=", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLike(String value) {
            addCriterion("modify_empid like", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotLike(String value) {
            addCriterion("modify_empid not like", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIn(List<String> values) {
            addCriterion("modify_empid in", values, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotIn(List<String> values) {
            addCriterion("modify_empid not in", values, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidBetween(String value1, String value2) {
            addCriterion("modify_empid between", value1, value2, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotBetween(String value1, String value2) {
            addCriterion("modify_empid not between", value1, value2, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Long value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Long value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Long value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Long value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Long value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Long value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Long> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Long> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Long value1, Long value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Long value1, Long value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andSvcVersionIsNull() {
            addCriterion("svc_version is null");
            return (Criteria) this;
        }

        public Criteria andSvcVersionIsNotNull() {
            addCriterion("svc_version is not null");
            return (Criteria) this;
        }

        public Criteria andSvcVersionEqualTo(Long value) {
            addCriterion("svc_version =", value, "svcVersion");
            return (Criteria) this;
        }

        public Criteria andSvcVersionNotEqualTo(Long value) {
            addCriterion("svc_version <>", value, "svcVersion");
            return (Criteria) this;
        }

        public Criteria andSvcVersionGreaterThan(Long value) {
            addCriterion("svc_version >", value, "svcVersion");
            return (Criteria) this;
        }

        public Criteria andSvcVersionGreaterThanOrEqualTo(Long value) {
            addCriterion("svc_version >=", value, "svcVersion");
            return (Criteria) this;
        }

        public Criteria andSvcVersionLessThan(Long value) {
            addCriterion("svc_version <", value, "svcVersion");
            return (Criteria) this;
        }

        public Criteria andSvcVersionLessThanOrEqualTo(Long value) {
            addCriterion("svc_version <=", value, "svcVersion");
            return (Criteria) this;
        }

        public Criteria andSvcVersionIn(List<Long> values) {
            addCriterion("svc_version in", values, "svcVersion");
            return (Criteria) this;
        }

        public Criteria andSvcVersionNotIn(List<Long> values) {
            addCriterion("svc_version not in", values, "svcVersion");
            return (Criteria) this;
        }

        public Criteria andSvcVersionBetween(Long value1, Long value2) {
            addCriterion("svc_version between", value1, value2, "svcVersion");
            return (Criteria) this;
        }

        public Criteria andSvcVersionNotBetween(Long value1, Long value2) {
            addCriterion("svc_version not between", value1, value2, "svcVersion");
            return (Criteria) this;
        }
    }

    /**
     * search_service
     * @ibatorgenerated do_not_delete_during_merge 2019-08-19 16:15:47
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * 
     * 内类部，系统内部调用1
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}