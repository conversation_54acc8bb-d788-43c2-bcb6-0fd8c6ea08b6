package com.aliyun.wormhole.qanat.dal.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TenantInfoExample {
    protected String pk_name = "id";

    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TenantInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setPk_name(String pk_name) {
        this.pk_name = pk_name;
    }

    public String getPk_name() {
        return pk_name;
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIsNull() {
            addCriterion("create_empid is null");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIsNotNull() {
            addCriterion("create_empid is not null");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidEqualTo(String value) {
            addCriterion("create_empid =", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotEqualTo(String value) {
            addCriterion("create_empid <>", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidGreaterThan(String value) {
            addCriterion("create_empid >", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidGreaterThanOrEqualTo(String value) {
            addCriterion("create_empid >=", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLessThan(String value) {
            addCriterion("create_empid <", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLessThanOrEqualTo(String value) {
            addCriterion("create_empid <=", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLike(String value) {
            addCriterion("create_empid like", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotLike(String value) {
            addCriterion("create_empid not like", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIn(List<String> values) {
            addCriterion("create_empid in", values, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotIn(List<String> values) {
            addCriterion("create_empid not in", values, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidBetween(String value1, String value2) {
            addCriterion("create_empid between", value1, value2, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotBetween(String value1, String value2) {
            addCriterion("create_empid not between", value1, value2, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIsNull() {
            addCriterion("modify_empid is null");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIsNotNull() {
            addCriterion("modify_empid is not null");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidEqualTo(String value) {
            addCriterion("modify_empid =", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotEqualTo(String value) {
            addCriterion("modify_empid <>", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidGreaterThan(String value) {
            addCriterion("modify_empid >", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidGreaterThanOrEqualTo(String value) {
            addCriterion("modify_empid >=", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLessThan(String value) {
            addCriterion("modify_empid <", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLessThanOrEqualTo(String value) {
            addCriterion("modify_empid <=", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLike(String value) {
            addCriterion("modify_empid like", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotLike(String value) {
            addCriterion("modify_empid not like", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIn(List<String> values) {
            addCriterion("modify_empid in", values, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotIn(List<String> values) {
            addCriterion("modify_empid not in", values, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidBetween(String value1, String value2) {
            addCriterion("modify_empid between", value1, value2, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotBetween(String value1, String value2) {
            addCriterion("modify_empid not between", value1, value2, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("`name` is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("`name` is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("`name` =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("`name` <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("`name` >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("`name` >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("`name` <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("`name` <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("`name` like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("`name` not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("`name` in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("`name` not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("`name` between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("`name` not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andDefaultDwIsNull() {
            addCriterion("default_dw is null");
            return (Criteria) this;
        }

        public Criteria andDefaultDwIsNotNull() {
            addCriterion("default_dw is not null");
            return (Criteria) this;
        }

        public Criteria andDefaultDwEqualTo(String value) {
            addCriterion("default_dw =", value, "defaultDw");
            return (Criteria) this;
        }

        public Criteria andDefaultDwNotEqualTo(String value) {
            addCriterion("default_dw <>", value, "defaultDw");
            return (Criteria) this;
        }

        public Criteria andDefaultDwGreaterThan(String value) {
            addCriterion("default_dw >", value, "defaultDw");
            return (Criteria) this;
        }

        public Criteria andDefaultDwGreaterThanOrEqualTo(String value) {
            addCriterion("default_dw >=", value, "defaultDw");
            return (Criteria) this;
        }

        public Criteria andDefaultDwLessThan(String value) {
            addCriterion("default_dw <", value, "defaultDw");
            return (Criteria) this;
        }

        public Criteria andDefaultDwLessThanOrEqualTo(String value) {
            addCriterion("default_dw <=", value, "defaultDw");
            return (Criteria) this;
        }

        public Criteria andDefaultDwLike(String value) {
            addCriterion("default_dw like", value, "defaultDw");
            return (Criteria) this;
        }

        public Criteria andDefaultDwNotLike(String value) {
            addCriterion("default_dw not like", value, "defaultDw");
            return (Criteria) this;
        }

        public Criteria andDefaultDwIn(List<String> values) {
            addCriterion("default_dw in", values, "defaultDw");
            return (Criteria) this;
        }

        public Criteria andDefaultDwNotIn(List<String> values) {
            addCriterion("default_dw not in", values, "defaultDw");
            return (Criteria) this;
        }

        public Criteria andDefaultDwBetween(String value1, String value2) {
            addCriterion("default_dw between", value1, value2, "defaultDw");
            return (Criteria) this;
        }

        public Criteria andDefaultDwNotBetween(String value1, String value2) {
            addCriterion("default_dw not between", value1, value2, "defaultDw");
            return (Criteria) this;
        }

        public Criteria andEtlDwIsNull() {
            addCriterion("etl_dw is null");
            return (Criteria) this;
        }

        public Criteria andEtlDwIsNotNull() {
            addCriterion("etl_dw is not null");
            return (Criteria) this;
        }

        public Criteria andEtlDwEqualTo(String value) {
            addCriterion("etl_dw =", value, "etlDw");
            return (Criteria) this;
        }

        public Criteria andEtlDwNotEqualTo(String value) {
            addCriterion("etl_dw <>", value, "etlDw");
            return (Criteria) this;
        }

        public Criteria andEtlDwGreaterThan(String value) {
            addCriterion("etl_dw >", value, "etlDw");
            return (Criteria) this;
        }

        public Criteria andEtlDwGreaterThanOrEqualTo(String value) {
            addCriterion("etl_dw >=", value, "etlDw");
            return (Criteria) this;
        }

        public Criteria andEtlDwLessThan(String value) {
            addCriterion("etl_dw <", value, "etlDw");
            return (Criteria) this;
        }

        public Criteria andEtlDwLessThanOrEqualTo(String value) {
            addCriterion("etl_dw <=", value, "etlDw");
            return (Criteria) this;
        }

        public Criteria andEtlDwLike(String value) {
            addCriterion("etl_dw like", value, "etlDw");
            return (Criteria) this;
        }

        public Criteria andEtlDwNotLike(String value) {
            addCriterion("etl_dw not like", value, "etlDw");
            return (Criteria) this;
        }

        public Criteria andEtlDwIn(List<String> values) {
            addCriterion("etl_dw in", values, "etlDw");
            return (Criteria) this;
        }

        public Criteria andEtlDwNotIn(List<String> values) {
            addCriterion("etl_dw not in", values, "etlDw");
            return (Criteria) this;
        }

        public Criteria andEtlDwBetween(String value1, String value2) {
            addCriterion("etl_dw between", value1, value2, "etlDw");
            return (Criteria) this;
        }

        public Criteria andEtlDwNotBetween(String value1, String value2) {
            addCriterion("etl_dw not between", value1, value2, "etlDw");
            return (Criteria) this;
        }

        public Criteria andBackupDwIsNull() {
            addCriterion("backup_dw is null");
            return (Criteria) this;
        }

        public Criteria andBackupDwIsNotNull() {
            addCriterion("backup_dw is not null");
            return (Criteria) this;
        }

        public Criteria andBackupDwEqualTo(String value) {
            addCriterion("backup_dw =", value, "backupDw");
            return (Criteria) this;
        }

        public Criteria andBackupDwNotEqualTo(String value) {
            addCriterion("backup_dw <>", value, "backupDw");
            return (Criteria) this;
        }

        public Criteria andBackupDwGreaterThan(String value) {
            addCriterion("backup_dw >", value, "backupDw");
            return (Criteria) this;
        }

        public Criteria andBackupDwGreaterThanOrEqualTo(String value) {
            addCriterion("backup_dw >=", value, "backupDw");
            return (Criteria) this;
        }

        public Criteria andBackupDwLessThan(String value) {
            addCriterion("backup_dw <", value, "backupDw");
            return (Criteria) this;
        }

        public Criteria andBackupDwLessThanOrEqualTo(String value) {
            addCriterion("backup_dw <=", value, "backupDw");
            return (Criteria) this;
        }

        public Criteria andBackupDwLike(String value) {
            addCriterion("backup_dw like", value, "backupDw");
            return (Criteria) this;
        }

        public Criteria andBackupDwNotLike(String value) {
            addCriterion("backup_dw not like", value, "backupDw");
            return (Criteria) this;
        }

        public Criteria andBackupDwIn(List<String> values) {
            addCriterion("backup_dw in", values, "backupDw");
            return (Criteria) this;
        }

        public Criteria andBackupDwNotIn(List<String> values) {
            addCriterion("backup_dw not in", values, "backupDw");
            return (Criteria) this;
        }

        public Criteria andBackupDwBetween(String value1, String value2) {
            addCriterion("backup_dw between", value1, value2, "backupDw");
            return (Criteria) this;
        }

        public Criteria andBackupDwNotBetween(String value1, String value2) {
            addCriterion("backup_dw not between", value1, value2, "backupDw");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}