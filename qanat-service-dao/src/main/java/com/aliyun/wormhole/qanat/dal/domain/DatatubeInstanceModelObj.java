package com.aliyun.wormhole.qanat.dal.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class DatatubeInstanceModelObj implements Serializable {
    private Long id;

    private Date gmtCreate;

    private Date gmtModified;

    private String createEmpid;

    private String modifyEmpid;

    private Long datatubeInstId;

    private String modelObjectCode;

    private String modelObjectType;

    private String refDsName;

    private String filter;

    private Integer isMain;

    private Integer isLookup;

    private String lookupFrom;

    private String relType;

    private Integer tpm;

    private Integer sla;

    private Long isDeleted;

    private BigDecimal flowLimit;

    private BigDecimal lookupFlowLimit;

    private Integer lookupSla;

    private String fields;

    private String refFields;

    private String tenantId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getCreateEmpid() {
        return createEmpid;
    }

    public void setCreateEmpid(String createEmpid) {
        this.createEmpid = createEmpid;
    }

    public String getModifyEmpid() {
        return modifyEmpid;
    }

    public void setModifyEmpid(String modifyEmpid) {
        this.modifyEmpid = modifyEmpid;
    }

    public Long getDatatubeInstId() {
        return datatubeInstId;
    }

    public void setDatatubeInstId(Long datatubeInstId) {
        this.datatubeInstId = datatubeInstId;
    }

    public String getModelObjectCode() {
        return modelObjectCode;
    }

    public void setModelObjectCode(String modelObjectCode) {
        this.modelObjectCode = modelObjectCode;
    }

    public String getModelObjectType() {
        return modelObjectType;
    }

    public void setModelObjectType(String modelObjectType) {
        this.modelObjectType = modelObjectType;
    }

    public String getRefDsName() {
        return refDsName;
    }

    public void setRefDsName(String refDsName) {
        this.refDsName = refDsName;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    public Integer getIsMain() {
        return isMain;
    }

    public void setIsMain(Integer isMain) {
        this.isMain = isMain;
    }

    public Integer getIsLookup() {
        return isLookup;
    }

    public void setIsLookup(Integer isLookup) {
        this.isLookup = isLookup;
    }

    public String getLookupFrom() {
        return lookupFrom;
    }

    public void setLookupFrom(String lookupFrom) {
        this.lookupFrom = lookupFrom;
    }

    public String getRelType() {
        return relType;
    }

    public void setRelType(String relType) {
        this.relType = relType;
    }

    public Integer getTpm() {
        return tpm;
    }

    public void setTpm(Integer tpm) {
        this.tpm = tpm;
    }

    public Integer getSla() {
        return sla;
    }

    public void setSla(Integer sla) {
        this.sla = sla;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public BigDecimal getFlowLimit() {
        return flowLimit;
    }

    public void setFlowLimit(BigDecimal flowLimit) {
        this.flowLimit = flowLimit;
    }

    public BigDecimal getLookupFlowLimit() {
        return lookupFlowLimit;
    }

    public void setLookupFlowLimit(BigDecimal lookupFlowLimit) {
        this.lookupFlowLimit = lookupFlowLimit;
    }

    public Integer getLookupSla() {
        return lookupSla;
    }

    public void setLookupSla(Integer lookupSla) {
        this.lookupSla = lookupSla;
    }

    public String getFields() {
        return fields;
    }

    public void setFields(String fields) {
        this.fields = fields;
    }

    public String getRefFields() {
        return refFields;
    }

    public void setRefFields(String refFields) {
        this.refFields = refFields;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}