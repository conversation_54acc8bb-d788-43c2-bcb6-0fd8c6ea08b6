package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTask;
import com.aliyun.wormhole.qanat.dal.domain.DatatubeInstanceTaskExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface DatatubeInstanceTaskMapper {
    int countByExample(DatatubeInstanceTaskExample example);

    int deleteByExample(DatatubeInstanceTaskExample example);

    List<DatatubeInstanceTask> selectByExampleWithBLOBs(DatatubeInstanceTaskExample example);

    List<DatatubeInstanceTask> selectByExample(DatatubeInstanceTaskExample example);

    int updateByExampleSelective(@Param("record") DatatubeInstanceTask record, @Param("example") DatatubeInstanceTaskExample example);

    int updateByExampleWithBLOBs(@Param("record") DatatubeInstanceTask record, @Param("example") DatatubeInstanceTaskExample example);

    int updateByExample(@Param("record") DatatubeInstanceTask record, @Param("example") DatatubeInstanceTaskExample example);

    List<DatatubeInstanceTask> selectByExampleWithBLOBsAndPage(DatatubeInstanceTaskExample example, RowBounds rowBound);

    List<DatatubeInstanceTask> selectByExampleAndPage(DatatubeInstanceTaskExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(DatatubeInstanceTask record);

    int insertSelective(DatatubeInstanceTask record);

    DatatubeInstanceTask selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DatatubeInstanceTask record);

    int updateByPrimaryKeyWithBLOBs(DatatubeInstanceTask record);

    int updateByPrimaryKey(DatatubeInstanceTask record);
}