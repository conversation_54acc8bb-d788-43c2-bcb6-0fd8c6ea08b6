package com.aliyun.wormhole.qanat.dal.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class Datasource implements Serializable {
    private Long id;

    private Date gmtCreate;

    private Date gmtModified;

    private String dsName;

    private String dsDesc;

    private String dsType;

    private String createEmpid;

    private String modifyEmpid;

    private Long isDeleted;

    private String remark;

    private String dbName;

    private String objectType;

    private String dsUniqueName;

    private String pkFields;

    private String owner;

    private String dataStatus;

    private String tenantId;

    private String tableName;

    private String sysType;

    private String catelogName;

    private Long predictSize;

    private Long size;

    private Integer predictQph;

    private Integer qph;

    private String workTime;

    private BigDecimal dataSize;

    private String meta;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getDsName() {
        return dsName;
    }

    public void setDsName(String dsName) {
        this.dsName = dsName;
    }

    public String getDsDesc() {
        return dsDesc;
    }

    public void setDsDesc(String dsDesc) {
        this.dsDesc = dsDesc;
    }

    public String getDsType() {
        return dsType;
    }

    public void setDsType(String dsType) {
        this.dsType = dsType;
    }

    public String getCreateEmpid() {
        return createEmpid;
    }

    public void setCreateEmpid(String createEmpid) {
        this.createEmpid = createEmpid;
    }

    public String getModifyEmpid() {
        return modifyEmpid;
    }

    public void setModifyEmpid(String modifyEmpid) {
        this.modifyEmpid = modifyEmpid;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getObjectType() {
        return objectType;
    }

    public void setObjectType(String objectType) {
        this.objectType = objectType;
    }

    public String getDsUniqueName() {
        return dsUniqueName;
    }

    public void setDsUniqueName(String dsUniqueName) {
        this.dsUniqueName = dsUniqueName;
    }

    public String getPkFields() {
        return pkFields;
    }

    public void setPkFields(String pkFields) {
        this.pkFields = pkFields;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getDataStatus() {
        return dataStatus;
    }

    public void setDataStatus(String dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getSysType() {
        return sysType;
    }

    public void setSysType(String sysType) {
        this.sysType = sysType;
    }

    public String getCatelogName() {
        return catelogName;
    }

    public void setCatelogName(String catelogName) {
        this.catelogName = catelogName;
    }

    public Long getPredictSize() {
        return predictSize;
    }

    public void setPredictSize(Long predictSize) {
        this.predictSize = predictSize;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Integer getPredictQph() {
        return predictQph;
    }

    public void setPredictQph(Integer predictQph) {
        this.predictQph = predictQph;
    }

    public Integer getQph() {
        return qph;
    }

    public void setQph(Integer qph) {
        this.qph = qph;
    }

    public String getWorkTime() {
        return workTime;
    }

    public void setWorkTime(String workTime) {
        this.workTime = workTime;
    }

    public BigDecimal getDataSize() {
        return dataSize;
    }

    public void setDataSize(BigDecimal dataSize) {
        this.dataSize = dataSize;
    }

    public String getMeta() {
        return meta;
    }

    public void setMeta(String meta) {
        this.meta = meta;
    }
}