package com.aliyun.wormhole.qanat.dal.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class DatatubeInstance implements Serializable {
    private Long id;

    private Date gmtCreate;

    private Date gmtModified;

    private String createEmpid;

    private String modifyEmpid;

    private String code;

    private String name;

    private String remark;

    private String provider;

    private String level;

    private Long isDeleted;

    private String tenantId;

    private String objectType;

    private String appName;

    private String type;

    private Long isTest;

    private Long providerId;

    private BigDecimal computeCost;

    private BigDecimal storeCost;

    private BigDecimal consistentRate;

    private Integer delayMs;

    private String dbName;

    private String version;

    private Integer isDynamic;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getCreateEmpid() {
        return createEmpid;
    }

    public void setCreateEmpid(String createEmpid) {
        this.createEmpid = createEmpid;
    }

    public String getModifyEmpid() {
        return modifyEmpid;
    }

    public void setModifyEmpid(String modifyEmpid) {
        this.modifyEmpid = modifyEmpid;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getObjectType() {
        return objectType;
    }

    public void setObjectType(String objectType) {
        this.objectType = objectType;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getIsTest() {
        return isTest;
    }

    public void setIsTest(Long isTest) {
        this.isTest = isTest;
    }

    public Long getProviderId() {
        return providerId;
    }

    public void setProviderId(Long providerId) {
        this.providerId = providerId;
    }

    public BigDecimal getComputeCost() {
        return computeCost;
    }

    public void setComputeCost(BigDecimal computeCost) {
        this.computeCost = computeCost;
    }

    public BigDecimal getStoreCost() {
        return storeCost;
    }

    public void setStoreCost(BigDecimal storeCost) {
        this.storeCost = storeCost;
    }

    public BigDecimal getConsistentRate() {
        return consistentRate;
    }

    public void setConsistentRate(BigDecimal consistentRate) {
        this.consistentRate = consistentRate;
    }

    public Integer getDelayMs() {
        return delayMs;
    }

    public void setDelayMs(Integer delayMs) {
        this.delayMs = delayMs;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getIsDynamic() {
        return isDynamic;
    }

    public void setIsDynamic(Integer isDynamic) {
        this.isDynamic = isDynamic;
    }
}