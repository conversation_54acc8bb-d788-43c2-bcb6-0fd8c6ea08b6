package com.aliyun.wormhole.qanat.dal.domain;

import java.io.Serializable;
import java.util.Date;

public class CommResouceAcl implements Serializable {
    /**
     * comm_resouce_acl.id (主键)
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    private Long id;

    /**
     * comm_resouce_acl.gmt_create (创建时间)
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    private Date gmtCreate;

    /**
     * comm_resouce_acl.gmt_modified (修改时间)
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    private Date gmtModified;

    /**
     * comm_resouce_acl.resource_id (资源标识)
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    private Long resourceId;

    /**
     * comm_resouce_acl.resource_type (资源类型)
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    private String resourceType;

    /**
     * comm_resouce_acl.resouce_name (资源名称)
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    private String resouceName;

    /**
     * comm_resouce_acl.acl_token (acl权限点)
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    private String aclToken;

    /**
     * comm_resouce_acl.create_empid (创建人工号)
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    private String createEmpid;

    /**
     * comm_resouce_acl.modify_empid (修改人工号)
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    private String modifyEmpid;

    /**
     * comm_resouce_acl.is_deleted (是否逻辑删除)
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    private Long isDeleted;

    /**
     * comm_resouce_acl.remark (备注)
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    private String remark;

    /**
     * comm_resouce_acl.op_type (操作类型)
     * @ibatorgenerated 2019-08-19 16:15:26
     */
    private String opType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getResourceId() {
        return resourceId;
    }

    public void setResourceId(Long resourceId) {
        this.resourceId = resourceId;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getResouceName() {
        return resouceName;
    }

    public void setResouceName(String resouceName) {
        this.resouceName = resouceName;
    }

    public String getAclToken() {
        return aclToken;
    }

    public void setAclToken(String aclToken) {
        this.aclToken = aclToken;
    }

    public String getCreateEmpid() {
        return createEmpid;
    }

    public void setCreateEmpid(String createEmpid) {
        this.createEmpid = createEmpid;
    }

    public String getModifyEmpid() {
        return modifyEmpid;
    }

    public void setModifyEmpid(String modifyEmpid) {
        this.modifyEmpid = modifyEmpid;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOpType() {
        return opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }
}