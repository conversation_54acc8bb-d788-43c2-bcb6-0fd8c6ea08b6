package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.TenantInfo;
import com.aliyun.wormhole.qanat.dal.domain.TenantInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface TenantInfoMapper {
    int countByExample(TenantInfoExample example);

    int deleteByExample(TenantInfoExample example);

    List<TenantInfo> selectByExampleWithBLOBs(TenantInfoExample example);

    List<TenantInfo> selectByExample(TenantInfoExample example);

    int updateByExampleSelective(@Param("record") TenantInfo record, @Param("example") TenantInfoExample example);

    int updateByExampleWithBLOBs(@Param("record") TenantInfo record, @Param("example") TenantInfoExample example);

    int updateByExample(@Param("record") TenantInfo record, @Param("example") TenantInfoExample example);

    List<TenantInfo> selectByExampleWithBLOBsAndPage(TenantInfoExample example, RowBounds rowBound);

    List<TenantInfo> selectByExampleAndPage(TenantInfoExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(TenantInfo record);

    int insertSelective(TenantInfo record);

    TenantInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TenantInfo record);

    int updateByPrimaryKeyWithBLOBs(TenantInfo record);

    int updateByPrimaryKey(TenantInfo record);
}