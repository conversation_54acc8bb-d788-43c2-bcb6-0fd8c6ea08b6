package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.SearchService;
import com.aliyun.wormhole.qanat.dal.domain.SearchServiceExample;
import com.aliyun.wormhole.qanat.dal.domain.SearchServiceWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface SearchServiceMapper {
    /**
     * 条件统计
     * 参数:查询条件,null为整张表
     * 返回:查询个数
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    int countByExample(SearchServiceExample example);

    /**
     * 批量条件删除
     * 参数:删除条件,null为整张表
     * 返回:删除个数
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    int deleteByExample(SearchServiceExample example);

    /**
     * 批量条件查询,支持大字段类型
     * 参数:查询条件,null查整张表
     * 返回:对象集合
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    List<SearchServiceWithBLOBs> selectByExampleWithBLOBs(SearchServiceExample example);

    /**
     * 批量条件查询
     * 参数:查询条件,null查整张表
     * 返回:对象集合
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    List<SearchService> selectByExample(SearchServiceExample example);

    /**
     * 批量条件修改，空值条件不修改
     * 参数:1.要修改成的值，2.要修改条件
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    int updateByExampleSelective(@Param("record") SearchServiceWithBLOBs record, @Param("example") SearchServiceExample example);

    /**
     * 批量条件修改，空值条件会修改成null,支持大字段类型
     * 参数:1.要修改成的值，2.要修改条件
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    int updateByExampleWithBLOBs(@Param("record") SearchServiceWithBLOBs record, @Param("example") SearchServiceExample example);

    /**
     * 批量条件修改，空值条件会修改成null
     * 参数:1.要修改成的值，2.要修改条件
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    int updateByExample(@Param("record") SearchService record, @Param("example") SearchServiceExample example);

    /**
     * 物理分页条件查询,支持大字段
     * 参数:1.查询条件 2.分页条件 new RowBounds(2,3) 
            从第2条开始显示，显示3条(从0开始编号)
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    List<SearchServiceWithBLOBs> selectByExampleWithBLOBsAndPage(SearchServiceExample example, RowBounds rowBound);

    /**
     * 物理分页条件查询
     * 参数:1.查询条件 2.分页条件 new RowBounds(2,3) 
            从第2条开始显示，显示3条(从0开始编号)
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    List<SearchService> selectByExampleAndPage(SearchServiceExample example, RowBounds rowBound);

    /**
     * 根据主键删除
     * 参数:主键
     * 返回:删除个数
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入，空属性也会插入
     * 参数:pojo对象
     * 返回:删除个数
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    int insert(SearchServiceWithBLOBs record);

    /**
     * 插入，空属性不会插入
     * 参数:pojo对象
     * 返回:删除个数
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    int insertSelective(SearchServiceWithBLOBs record);

    /**
     * 根据主键查询
     * 参数:查询条件,主键值
     * 返回:对象
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    SearchServiceWithBLOBs selectByPrimaryKey(Long id);

    /**
     * 根据主键修改，空值条件不会修改成null
     * 参数:1.要修改成的值
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    int updateByPrimaryKeySelective(SearchServiceWithBLOBs record);

    /**
     * 根据主键修改，空值条件会修改成null,支持大字段类型
     * 参数:1.要修改成的值
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    int updateByPrimaryKeyWithBLOBs(SearchServiceWithBLOBs record);

    /**
     * 根据主键修改，空值条件会修改成null
     * 参数:1.要修改成的值
     * 返回:成功修改个数
     * @ibatorgenerated 2019-08-19 16:15:47
     */
    int updateByPrimaryKey(SearchService record);
}