package com.aliyun.wormhole.qanat.dal.domain;

public class ServiceChangesetWithBLOBs extends ServiceChangeset {
    /**
     * service_changeset.participants (参与人)
     * @ibatorgenerated 2019-08-19 16:15:42
     */
    private String participants;

    /**
     * service_changeset.dag_script (Dag脚本)
     * @ibatorgenerated 2019-08-19 16:15:42
     */
    private String dagScript;

    /**
     * service_changeset.ref_ds_list (引用数据源列表)
     * @ibatorgenerated 2019-08-19 16:15:42
     */
    private String refDsList;

    /**
     * service_changeset.meta (变更元数据)
     * @ibatorgenerated 2019-08-19 16:15:42
     */
    private String meta;

    public String getParticipants() {
        return participants;
    }

    public void setParticipants(String participants) {
        this.participants = participants;
    }

    public String getDagScript() {
        return dagScript;
    }

    public void setDagScript(String dagScript) {
        this.dagScript = dagScript;
    }

    public String getRefDsList() {
        return refDsList;
    }

    public void setRefDsList(String refDsList) {
        this.refDsList = refDsList;
    }

    public String getMeta() {
        return meta;
    }

    public void setMeta(String meta) {
        this.meta = meta;
    }
}