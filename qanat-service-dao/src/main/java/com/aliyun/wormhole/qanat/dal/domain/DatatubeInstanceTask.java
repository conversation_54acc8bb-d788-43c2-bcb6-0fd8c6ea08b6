package com.aliyun.wormhole.qanat.dal.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class DatatubeInstanceTask implements Serializable {
    private Long id;

    private Date gmtCreate;

    private Date gmtModified;

    private String createEmpid;

    private String modifyEmpid;

    private Long datatubeInstId;

    private String taskName;

    private String taskType;

    private BigDecimal taskCu;

    private Long isDeleted;

    private Integer version;

    private Integer parallel;

    private String tenantId;

    private String taskScript;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getCreateEmpid() {
        return createEmpid;
    }

    public void setCreateEmpid(String createEmpid) {
        this.createEmpid = createEmpid;
    }

    public String getModifyEmpid() {
        return modifyEmpid;
    }

    public void setModifyEmpid(String modifyEmpid) {
        this.modifyEmpid = modifyEmpid;
    }

    public Long getDatatubeInstId() {
        return datatubeInstId;
    }

    public void setDatatubeInstId(Long datatubeInstId) {
        this.datatubeInstId = datatubeInstId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public BigDecimal getTaskCu() {
        return taskCu;
    }

    public void setTaskCu(BigDecimal taskCu) {
        this.taskCu = taskCu;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getParallel() {
        return parallel;
    }

    public void setParallel(Integer parallel) {
        this.parallel = parallel;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTaskScript() {
        return taskScript;
    }

    public void setTaskScript(String taskScript) {
        this.taskScript = taskScript;
    }
}