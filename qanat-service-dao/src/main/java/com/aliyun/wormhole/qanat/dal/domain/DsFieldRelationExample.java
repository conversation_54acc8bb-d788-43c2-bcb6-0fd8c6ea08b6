package com.aliyun.wormhole.qanat.dal.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DsFieldRelationExample {
    protected String pk_name = "id";

    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DsFieldRelationExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setPk_name(String pk_name) {
        this.pk_name = pk_name;
    }

    public String getPk_name() {
        return pk_name;
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andSrcFieldNameIsNull() {
            addCriterion("src_field_name is null");
            return (Criteria) this;
        }

        public Criteria andSrcFieldNameIsNotNull() {
            addCriterion("src_field_name is not null");
            return (Criteria) this;
        }

        public Criteria andSrcFieldNameEqualTo(String value) {
            addCriterion("src_field_name =", value, "srcFieldName");
            return (Criteria) this;
        }

        public Criteria andSrcFieldNameNotEqualTo(String value) {
            addCriterion("src_field_name <>", value, "srcFieldName");
            return (Criteria) this;
        }

        public Criteria andSrcFieldNameGreaterThan(String value) {
            addCriterion("src_field_name >", value, "srcFieldName");
            return (Criteria) this;
        }

        public Criteria andSrcFieldNameGreaterThanOrEqualTo(String value) {
            addCriterion("src_field_name >=", value, "srcFieldName");
            return (Criteria) this;
        }

        public Criteria andSrcFieldNameLessThan(String value) {
            addCriterion("src_field_name <", value, "srcFieldName");
            return (Criteria) this;
        }

        public Criteria andSrcFieldNameLessThanOrEqualTo(String value) {
            addCriterion("src_field_name <=", value, "srcFieldName");
            return (Criteria) this;
        }

        public Criteria andSrcFieldNameLike(String value) {
            addCriterion("src_field_name like", value, "srcFieldName");
            return (Criteria) this;
        }

        public Criteria andSrcFieldNameNotLike(String value) {
            addCriterion("src_field_name not like", value, "srcFieldName");
            return (Criteria) this;
        }

        public Criteria andSrcFieldNameIn(List<String> values) {
            addCriterion("src_field_name in", values, "srcFieldName");
            return (Criteria) this;
        }

        public Criteria andSrcFieldNameNotIn(List<String> values) {
            addCriterion("src_field_name not in", values, "srcFieldName");
            return (Criteria) this;
        }

        public Criteria andSrcFieldNameBetween(String value1, String value2) {
            addCriterion("src_field_name between", value1, value2, "srcFieldName");
            return (Criteria) this;
        }

        public Criteria andSrcFieldNameNotBetween(String value1, String value2) {
            addCriterion("src_field_name not between", value1, value2, "srcFieldName");
            return (Criteria) this;
        }

        public Criteria andSrcDsNameIsNull() {
            addCriterion("src_ds_name is null");
            return (Criteria) this;
        }

        public Criteria andSrcDsNameIsNotNull() {
            addCriterion("src_ds_name is not null");
            return (Criteria) this;
        }

        public Criteria andSrcDsNameEqualTo(String value) {
            addCriterion("src_ds_name =", value, "srcDsName");
            return (Criteria) this;
        }

        public Criteria andSrcDsNameNotEqualTo(String value) {
            addCriterion("src_ds_name <>", value, "srcDsName");
            return (Criteria) this;
        }

        public Criteria andSrcDsNameGreaterThan(String value) {
            addCriterion("src_ds_name >", value, "srcDsName");
            return (Criteria) this;
        }

        public Criteria andSrcDsNameGreaterThanOrEqualTo(String value) {
            addCriterion("src_ds_name >=", value, "srcDsName");
            return (Criteria) this;
        }

        public Criteria andSrcDsNameLessThan(String value) {
            addCriterion("src_ds_name <", value, "srcDsName");
            return (Criteria) this;
        }

        public Criteria andSrcDsNameLessThanOrEqualTo(String value) {
            addCriterion("src_ds_name <=", value, "srcDsName");
            return (Criteria) this;
        }

        public Criteria andSrcDsNameLike(String value) {
            addCriterion("src_ds_name like", value, "srcDsName");
            return (Criteria) this;
        }

        public Criteria andSrcDsNameNotLike(String value) {
            addCriterion("src_ds_name not like", value, "srcDsName");
            return (Criteria) this;
        }

        public Criteria andSrcDsNameIn(List<String> values) {
            addCriterion("src_ds_name in", values, "srcDsName");
            return (Criteria) this;
        }

        public Criteria andSrcDsNameNotIn(List<String> values) {
            addCriterion("src_ds_name not in", values, "srcDsName");
            return (Criteria) this;
        }

        public Criteria andSrcDsNameBetween(String value1, String value2) {
            addCriterion("src_ds_name between", value1, value2, "srcDsName");
            return (Criteria) this;
        }

        public Criteria andSrcDsNameNotBetween(String value1, String value2) {
            addCriterion("src_ds_name not between", value1, value2, "srcDsName");
            return (Criteria) this;
        }

        public Criteria andDstFieldNameIsNull() {
            addCriterion("dst_field_name is null");
            return (Criteria) this;
        }

        public Criteria andDstFieldNameIsNotNull() {
            addCriterion("dst_field_name is not null");
            return (Criteria) this;
        }

        public Criteria andDstFieldNameEqualTo(String value) {
            addCriterion("dst_field_name =", value, "dstFieldName");
            return (Criteria) this;
        }

        public Criteria andDstFieldNameNotEqualTo(String value) {
            addCriterion("dst_field_name <>", value, "dstFieldName");
            return (Criteria) this;
        }

        public Criteria andDstFieldNameGreaterThan(String value) {
            addCriterion("dst_field_name >", value, "dstFieldName");
            return (Criteria) this;
        }

        public Criteria andDstFieldNameGreaterThanOrEqualTo(String value) {
            addCriterion("dst_field_name >=", value, "dstFieldName");
            return (Criteria) this;
        }

        public Criteria andDstFieldNameLessThan(String value) {
            addCriterion("dst_field_name <", value, "dstFieldName");
            return (Criteria) this;
        }

        public Criteria andDstFieldNameLessThanOrEqualTo(String value) {
            addCriterion("dst_field_name <=", value, "dstFieldName");
            return (Criteria) this;
        }

        public Criteria andDstFieldNameLike(String value) {
            addCriterion("dst_field_name like", value, "dstFieldName");
            return (Criteria) this;
        }

        public Criteria andDstFieldNameNotLike(String value) {
            addCriterion("dst_field_name not like", value, "dstFieldName");
            return (Criteria) this;
        }

        public Criteria andDstFieldNameIn(List<String> values) {
            addCriterion("dst_field_name in", values, "dstFieldName");
            return (Criteria) this;
        }

        public Criteria andDstFieldNameNotIn(List<String> values) {
            addCriterion("dst_field_name not in", values, "dstFieldName");
            return (Criteria) this;
        }

        public Criteria andDstFieldNameBetween(String value1, String value2) {
            addCriterion("dst_field_name between", value1, value2, "dstFieldName");
            return (Criteria) this;
        }

        public Criteria andDstFieldNameNotBetween(String value1, String value2) {
            addCriterion("dst_field_name not between", value1, value2, "dstFieldName");
            return (Criteria) this;
        }

        public Criteria andDstDsNameIsNull() {
            addCriterion("dst_ds_name is null");
            return (Criteria) this;
        }

        public Criteria andDstDsNameIsNotNull() {
            addCriterion("dst_ds_name is not null");
            return (Criteria) this;
        }

        public Criteria andDstDsNameEqualTo(String value) {
            addCriterion("dst_ds_name =", value, "dstDsName");
            return (Criteria) this;
        }

        public Criteria andDstDsNameNotEqualTo(String value) {
            addCriterion("dst_ds_name <>", value, "dstDsName");
            return (Criteria) this;
        }

        public Criteria andDstDsNameGreaterThan(String value) {
            addCriterion("dst_ds_name >", value, "dstDsName");
            return (Criteria) this;
        }

        public Criteria andDstDsNameGreaterThanOrEqualTo(String value) {
            addCriterion("dst_ds_name >=", value, "dstDsName");
            return (Criteria) this;
        }

        public Criteria andDstDsNameLessThan(String value) {
            addCriterion("dst_ds_name <", value, "dstDsName");
            return (Criteria) this;
        }

        public Criteria andDstDsNameLessThanOrEqualTo(String value) {
            addCriterion("dst_ds_name <=", value, "dstDsName");
            return (Criteria) this;
        }

        public Criteria andDstDsNameLike(String value) {
            addCriterion("dst_ds_name like", value, "dstDsName");
            return (Criteria) this;
        }

        public Criteria andDstDsNameNotLike(String value) {
            addCriterion("dst_ds_name not like", value, "dstDsName");
            return (Criteria) this;
        }

        public Criteria andDstDsNameIn(List<String> values) {
            addCriterion("dst_ds_name in", values, "dstDsName");
            return (Criteria) this;
        }

        public Criteria andDstDsNameNotIn(List<String> values) {
            addCriterion("dst_ds_name not in", values, "dstDsName");
            return (Criteria) this;
        }

        public Criteria andDstDsNameBetween(String value1, String value2) {
            addCriterion("dst_ds_name between", value1, value2, "dstDsName");
            return (Criteria) this;
        }

        public Criteria andDstDsNameNotBetween(String value1, String value2) {
            addCriterion("dst_ds_name not between", value1, value2, "dstDsName");
            return (Criteria) this;
        }

        public Criteria andRelationTypeIsNull() {
            addCriterion("relation_type is null");
            return (Criteria) this;
        }

        public Criteria andRelationTypeIsNotNull() {
            addCriterion("relation_type is not null");
            return (Criteria) this;
        }

        public Criteria andRelationTypeEqualTo(String value) {
            addCriterion("relation_type =", value, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeNotEqualTo(String value) {
            addCriterion("relation_type <>", value, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeGreaterThan(String value) {
            addCriterion("relation_type >", value, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeGreaterThanOrEqualTo(String value) {
            addCriterion("relation_type >=", value, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeLessThan(String value) {
            addCriterion("relation_type <", value, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeLessThanOrEqualTo(String value) {
            addCriterion("relation_type <=", value, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeLike(String value) {
            addCriterion("relation_type like", value, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeNotLike(String value) {
            addCriterion("relation_type not like", value, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeIn(List<String> values) {
            addCriterion("relation_type in", values, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeNotIn(List<String> values) {
            addCriterion("relation_type not in", values, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeBetween(String value1, String value2) {
            addCriterion("relation_type between", value1, value2, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeNotBetween(String value1, String value2) {
            addCriterion("relation_type not between", value1, value2, "relationType");
            return (Criteria) this;
        }

        public Criteria andComputeFuncIsNull() {
            addCriterion("compute_func is null");
            return (Criteria) this;
        }

        public Criteria andComputeFuncIsNotNull() {
            addCriterion("compute_func is not null");
            return (Criteria) this;
        }

        public Criteria andComputeFuncEqualTo(String value) {
            addCriterion("compute_func =", value, "computeFunc");
            return (Criteria) this;
        }

        public Criteria andComputeFuncNotEqualTo(String value) {
            addCriterion("compute_func <>", value, "computeFunc");
            return (Criteria) this;
        }

        public Criteria andComputeFuncGreaterThan(String value) {
            addCriterion("compute_func >", value, "computeFunc");
            return (Criteria) this;
        }

        public Criteria andComputeFuncGreaterThanOrEqualTo(String value) {
            addCriterion("compute_func >=", value, "computeFunc");
            return (Criteria) this;
        }

        public Criteria andComputeFuncLessThan(String value) {
            addCriterion("compute_func <", value, "computeFunc");
            return (Criteria) this;
        }

        public Criteria andComputeFuncLessThanOrEqualTo(String value) {
            addCriterion("compute_func <=", value, "computeFunc");
            return (Criteria) this;
        }

        public Criteria andComputeFuncLike(String value) {
            addCriterion("compute_func like", value, "computeFunc");
            return (Criteria) this;
        }

        public Criteria andComputeFuncNotLike(String value) {
            addCriterion("compute_func not like", value, "computeFunc");
            return (Criteria) this;
        }

        public Criteria andComputeFuncIn(List<String> values) {
            addCriterion("compute_func in", values, "computeFunc");
            return (Criteria) this;
        }

        public Criteria andComputeFuncNotIn(List<String> values) {
            addCriterion("compute_func not in", values, "computeFunc");
            return (Criteria) this;
        }

        public Criteria andComputeFuncBetween(String value1, String value2) {
            addCriterion("compute_func between", value1, value2, "computeFunc");
            return (Criteria) this;
        }

        public Criteria andComputeFuncNotBetween(String value1, String value2) {
            addCriterion("compute_func not between", value1, value2, "computeFunc");
            return (Criteria) this;
        }

        public Criteria andMetricNameIsNull() {
            addCriterion("metric_name is null");
            return (Criteria) this;
        }

        public Criteria andMetricNameIsNotNull() {
            addCriterion("metric_name is not null");
            return (Criteria) this;
        }

        public Criteria andMetricNameEqualTo(String value) {
            addCriterion("metric_name =", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameNotEqualTo(String value) {
            addCriterion("metric_name <>", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameGreaterThan(String value) {
            addCriterion("metric_name >", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameGreaterThanOrEqualTo(String value) {
            addCriterion("metric_name >=", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameLessThan(String value) {
            addCriterion("metric_name <", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameLessThanOrEqualTo(String value) {
            addCriterion("metric_name <=", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameLike(String value) {
            addCriterion("metric_name like", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameNotLike(String value) {
            addCriterion("metric_name not like", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameIn(List<String> values) {
            addCriterion("metric_name in", values, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameNotIn(List<String> values) {
            addCriterion("metric_name not in", values, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameBetween(String value1, String value2) {
            addCriterion("metric_name between", value1, value2, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameNotBetween(String value1, String value2) {
            addCriterion("metric_name not between", value1, value2, "metricName");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Long value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Long value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Long value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Long value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Long value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Long value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Long> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Long> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Long value1, Long value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Long value1, Long value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}