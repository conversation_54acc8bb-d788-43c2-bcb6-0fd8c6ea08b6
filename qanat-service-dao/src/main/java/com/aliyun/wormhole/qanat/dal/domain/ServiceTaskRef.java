package com.aliyun.wormhole.qanat.dal.domain;

import java.io.Serializable;
import java.util.Date;

public class ServiceTaskRef implements Serializable {
    /**
     * service_task_ref.id (主键)
     * @ibatorgenerated 2019-09-26 11:05:52
     */
    private Long id;

    /**
     * service_task_ref.gmt_create (创建时间)
     * @ibatorgenerated 2019-09-26 11:05:52
     */
    private Date gmtCreate;

    /**
     * service_task_ref.create_empid (创建人)
     * @ibatorgenerated 2019-09-26 11:05:52
     */
    private String createEmpid;

    /**
     * service_task_ref.svc_id (服务id)
     * @ibatorgenerated 2019-09-26 11:05:52
     */
    private Long svcId;

    /**
     * service_task_ref.task_id (任务id)
     * @ibatorgenerated 2019-09-26 11:05:52
     */
    private Long taskId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getCreateEmpid() {
        return createEmpid;
    }

    public void setCreateEmpid(String createEmpid) {
        this.createEmpid = createEmpid;
    }

    public Long getSvcId() {
        return svcId;
    }

    public void setSvcId(Long svcId) {
        this.svcId = svcId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }
}