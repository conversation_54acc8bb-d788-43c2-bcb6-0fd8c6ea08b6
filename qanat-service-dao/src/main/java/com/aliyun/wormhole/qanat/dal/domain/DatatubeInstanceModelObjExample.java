package com.aliyun.wormhole.qanat.dal.domain;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DatatubeInstanceModelObjExample {
    protected String pk_name = "id";

    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DatatubeInstanceModelObjExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setPk_name(String pk_name) {
        this.pk_name = pk_name;
    }

    public String getPk_name() {
        return pk_name;
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIsNull() {
            addCriterion("create_empid is null");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIsNotNull() {
            addCriterion("create_empid is not null");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidEqualTo(String value) {
            addCriterion("create_empid =", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotEqualTo(String value) {
            addCriterion("create_empid <>", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidGreaterThan(String value) {
            addCriterion("create_empid >", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidGreaterThanOrEqualTo(String value) {
            addCriterion("create_empid >=", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLessThan(String value) {
            addCriterion("create_empid <", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLessThanOrEqualTo(String value) {
            addCriterion("create_empid <=", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidLike(String value) {
            addCriterion("create_empid like", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotLike(String value) {
            addCriterion("create_empid not like", value, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidIn(List<String> values) {
            addCriterion("create_empid in", values, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotIn(List<String> values) {
            addCriterion("create_empid not in", values, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidBetween(String value1, String value2) {
            addCriterion("create_empid between", value1, value2, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andCreateEmpidNotBetween(String value1, String value2) {
            addCriterion("create_empid not between", value1, value2, "createEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIsNull() {
            addCriterion("modify_empid is null");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIsNotNull() {
            addCriterion("modify_empid is not null");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidEqualTo(String value) {
            addCriterion("modify_empid =", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotEqualTo(String value) {
            addCriterion("modify_empid <>", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidGreaterThan(String value) {
            addCriterion("modify_empid >", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidGreaterThanOrEqualTo(String value) {
            addCriterion("modify_empid >=", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLessThan(String value) {
            addCriterion("modify_empid <", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLessThanOrEqualTo(String value) {
            addCriterion("modify_empid <=", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidLike(String value) {
            addCriterion("modify_empid like", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotLike(String value) {
            addCriterion("modify_empid not like", value, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidIn(List<String> values) {
            addCriterion("modify_empid in", values, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotIn(List<String> values) {
            addCriterion("modify_empid not in", values, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidBetween(String value1, String value2) {
            addCriterion("modify_empid between", value1, value2, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andModifyEmpidNotBetween(String value1, String value2) {
            addCriterion("modify_empid not between", value1, value2, "modifyEmpid");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdIsNull() {
            addCriterion("datatube_inst_id is null");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdIsNotNull() {
            addCriterion("datatube_inst_id is not null");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdEqualTo(Long value) {
            addCriterion("datatube_inst_id =", value, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdNotEqualTo(Long value) {
            addCriterion("datatube_inst_id <>", value, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdGreaterThan(Long value) {
            addCriterion("datatube_inst_id >", value, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdGreaterThanOrEqualTo(Long value) {
            addCriterion("datatube_inst_id >=", value, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdLessThan(Long value) {
            addCriterion("datatube_inst_id <", value, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdLessThanOrEqualTo(Long value) {
            addCriterion("datatube_inst_id <=", value, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdIn(List<Long> values) {
            addCriterion("datatube_inst_id in", values, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdNotIn(List<Long> values) {
            addCriterion("datatube_inst_id not in", values, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdBetween(Long value1, Long value2) {
            addCriterion("datatube_inst_id between", value1, value2, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andDatatubeInstIdNotBetween(Long value1, Long value2) {
            addCriterion("datatube_inst_id not between", value1, value2, "datatubeInstId");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeIsNull() {
            addCriterion("model_object_code is null");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeIsNotNull() {
            addCriterion("model_object_code is not null");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeEqualTo(String value) {
            addCriterion("model_object_code =", value, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeNotEqualTo(String value) {
            addCriterion("model_object_code <>", value, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeGreaterThan(String value) {
            addCriterion("model_object_code >", value, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("model_object_code >=", value, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeLessThan(String value) {
            addCriterion("model_object_code <", value, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeLessThanOrEqualTo(String value) {
            addCriterion("model_object_code <=", value, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeLike(String value) {
            addCriterion("model_object_code like", value, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeNotLike(String value) {
            addCriterion("model_object_code not like", value, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeIn(List<String> values) {
            addCriterion("model_object_code in", values, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeNotIn(List<String> values) {
            addCriterion("model_object_code not in", values, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeBetween(String value1, String value2) {
            addCriterion("model_object_code between", value1, value2, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectCodeNotBetween(String value1, String value2) {
            addCriterion("model_object_code not between", value1, value2, "modelObjectCode");
            return (Criteria) this;
        }

        public Criteria andModelObjectTypeIsNull() {
            addCriterion("model_object_type is null");
            return (Criteria) this;
        }

        public Criteria andModelObjectTypeIsNotNull() {
            addCriterion("model_object_type is not null");
            return (Criteria) this;
        }

        public Criteria andModelObjectTypeEqualTo(String value) {
            addCriterion("model_object_type =", value, "modelObjectType");
            return (Criteria) this;
        }

        public Criteria andModelObjectTypeNotEqualTo(String value) {
            addCriterion("model_object_type <>", value, "modelObjectType");
            return (Criteria) this;
        }

        public Criteria andModelObjectTypeGreaterThan(String value) {
            addCriterion("model_object_type >", value, "modelObjectType");
            return (Criteria) this;
        }

        public Criteria andModelObjectTypeGreaterThanOrEqualTo(String value) {
            addCriterion("model_object_type >=", value, "modelObjectType");
            return (Criteria) this;
        }

        public Criteria andModelObjectTypeLessThan(String value) {
            addCriterion("model_object_type <", value, "modelObjectType");
            return (Criteria) this;
        }

        public Criteria andModelObjectTypeLessThanOrEqualTo(String value) {
            addCriterion("model_object_type <=", value, "modelObjectType");
            return (Criteria) this;
        }

        public Criteria andModelObjectTypeLike(String value) {
            addCriterion("model_object_type like", value, "modelObjectType");
            return (Criteria) this;
        }

        public Criteria andModelObjectTypeNotLike(String value) {
            addCriterion("model_object_type not like", value, "modelObjectType");
            return (Criteria) this;
        }

        public Criteria andModelObjectTypeIn(List<String> values) {
            addCriterion("model_object_type in", values, "modelObjectType");
            return (Criteria) this;
        }

        public Criteria andModelObjectTypeNotIn(List<String> values) {
            addCriterion("model_object_type not in", values, "modelObjectType");
            return (Criteria) this;
        }

        public Criteria andModelObjectTypeBetween(String value1, String value2) {
            addCriterion("model_object_type between", value1, value2, "modelObjectType");
            return (Criteria) this;
        }

        public Criteria andModelObjectTypeNotBetween(String value1, String value2) {
            addCriterion("model_object_type not between", value1, value2, "modelObjectType");
            return (Criteria) this;
        }

        public Criteria andRefDsNameIsNull() {
            addCriterion("ref_ds_name is null");
            return (Criteria) this;
        }

        public Criteria andRefDsNameIsNotNull() {
            addCriterion("ref_ds_name is not null");
            return (Criteria) this;
        }

        public Criteria andRefDsNameEqualTo(String value) {
            addCriterion("ref_ds_name =", value, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameNotEqualTo(String value) {
            addCriterion("ref_ds_name <>", value, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameGreaterThan(String value) {
            addCriterion("ref_ds_name >", value, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameGreaterThanOrEqualTo(String value) {
            addCriterion("ref_ds_name >=", value, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameLessThan(String value) {
            addCriterion("ref_ds_name <", value, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameLessThanOrEqualTo(String value) {
            addCriterion("ref_ds_name <=", value, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameLike(String value) {
            addCriterion("ref_ds_name like", value, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameNotLike(String value) {
            addCriterion("ref_ds_name not like", value, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameIn(List<String> values) {
            addCriterion("ref_ds_name in", values, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameNotIn(List<String> values) {
            addCriterion("ref_ds_name not in", values, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameBetween(String value1, String value2) {
            addCriterion("ref_ds_name between", value1, value2, "refDsName");
            return (Criteria) this;
        }

        public Criteria andRefDsNameNotBetween(String value1, String value2) {
            addCriterion("ref_ds_name not between", value1, value2, "refDsName");
            return (Criteria) this;
        }

        public Criteria andFilterIsNull() {
            addCriterion("`filter` is null");
            return (Criteria) this;
        }

        public Criteria andFilterIsNotNull() {
            addCriterion("`filter` is not null");
            return (Criteria) this;
        }

        public Criteria andFilterEqualTo(String value) {
            addCriterion("`filter` =", value, "filter");
            return (Criteria) this;
        }

        public Criteria andFilterNotEqualTo(String value) {
            addCriterion("`filter` <>", value, "filter");
            return (Criteria) this;
        }

        public Criteria andFilterGreaterThan(String value) {
            addCriterion("`filter` >", value, "filter");
            return (Criteria) this;
        }

        public Criteria andFilterGreaterThanOrEqualTo(String value) {
            addCriterion("`filter` >=", value, "filter");
            return (Criteria) this;
        }

        public Criteria andFilterLessThan(String value) {
            addCriterion("`filter` <", value, "filter");
            return (Criteria) this;
        }

        public Criteria andFilterLessThanOrEqualTo(String value) {
            addCriterion("`filter` <=", value, "filter");
            return (Criteria) this;
        }

        public Criteria andFilterLike(String value) {
            addCriterion("`filter` like", value, "filter");
            return (Criteria) this;
        }

        public Criteria andFilterNotLike(String value) {
            addCriterion("`filter` not like", value, "filter");
            return (Criteria) this;
        }

        public Criteria andFilterIn(List<String> values) {
            addCriterion("`filter` in", values, "filter");
            return (Criteria) this;
        }

        public Criteria andFilterNotIn(List<String> values) {
            addCriterion("`filter` not in", values, "filter");
            return (Criteria) this;
        }

        public Criteria andFilterBetween(String value1, String value2) {
            addCriterion("`filter` between", value1, value2, "filter");
            return (Criteria) this;
        }

        public Criteria andFilterNotBetween(String value1, String value2) {
            addCriterion("`filter` not between", value1, value2, "filter");
            return (Criteria) this;
        }

        public Criteria andIsMainIsNull() {
            addCriterion("is_main is null");
            return (Criteria) this;
        }

        public Criteria andIsMainIsNotNull() {
            addCriterion("is_main is not null");
            return (Criteria) this;
        }

        public Criteria andIsMainEqualTo(Integer value) {
            addCriterion("is_main =", value, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainNotEqualTo(Integer value) {
            addCriterion("is_main <>", value, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainGreaterThan(Integer value) {
            addCriterion("is_main >", value, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_main >=", value, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainLessThan(Integer value) {
            addCriterion("is_main <", value, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainLessThanOrEqualTo(Integer value) {
            addCriterion("is_main <=", value, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainIn(List<Integer> values) {
            addCriterion("is_main in", values, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainNotIn(List<Integer> values) {
            addCriterion("is_main not in", values, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainBetween(Integer value1, Integer value2) {
            addCriterion("is_main between", value1, value2, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainNotBetween(Integer value1, Integer value2) {
            addCriterion("is_main not between", value1, value2, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsLookupIsNull() {
            addCriterion("is_lookup is null");
            return (Criteria) this;
        }

        public Criteria andIsLookupIsNotNull() {
            addCriterion("is_lookup is not null");
            return (Criteria) this;
        }

        public Criteria andIsLookupEqualTo(Integer value) {
            addCriterion("is_lookup =", value, "isLookup");
            return (Criteria) this;
        }

        public Criteria andIsLookupNotEqualTo(Integer value) {
            addCriterion("is_lookup <>", value, "isLookup");
            return (Criteria) this;
        }

        public Criteria andIsLookupGreaterThan(Integer value) {
            addCriterion("is_lookup >", value, "isLookup");
            return (Criteria) this;
        }

        public Criteria andIsLookupGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_lookup >=", value, "isLookup");
            return (Criteria) this;
        }

        public Criteria andIsLookupLessThan(Integer value) {
            addCriterion("is_lookup <", value, "isLookup");
            return (Criteria) this;
        }

        public Criteria andIsLookupLessThanOrEqualTo(Integer value) {
            addCriterion("is_lookup <=", value, "isLookup");
            return (Criteria) this;
        }

        public Criteria andIsLookupIn(List<Integer> values) {
            addCriterion("is_lookup in", values, "isLookup");
            return (Criteria) this;
        }

        public Criteria andIsLookupNotIn(List<Integer> values) {
            addCriterion("is_lookup not in", values, "isLookup");
            return (Criteria) this;
        }

        public Criteria andIsLookupBetween(Integer value1, Integer value2) {
            addCriterion("is_lookup between", value1, value2, "isLookup");
            return (Criteria) this;
        }

        public Criteria andIsLookupNotBetween(Integer value1, Integer value2) {
            addCriterion("is_lookup not between", value1, value2, "isLookup");
            return (Criteria) this;
        }

        public Criteria andLookupFromIsNull() {
            addCriterion("lookup_from is null");
            return (Criteria) this;
        }

        public Criteria andLookupFromIsNotNull() {
            addCriterion("lookup_from is not null");
            return (Criteria) this;
        }

        public Criteria andLookupFromEqualTo(String value) {
            addCriterion("lookup_from =", value, "lookupFrom");
            return (Criteria) this;
        }

        public Criteria andLookupFromNotEqualTo(String value) {
            addCriterion("lookup_from <>", value, "lookupFrom");
            return (Criteria) this;
        }

        public Criteria andLookupFromGreaterThan(String value) {
            addCriterion("lookup_from >", value, "lookupFrom");
            return (Criteria) this;
        }

        public Criteria andLookupFromGreaterThanOrEqualTo(String value) {
            addCriterion("lookup_from >=", value, "lookupFrom");
            return (Criteria) this;
        }

        public Criteria andLookupFromLessThan(String value) {
            addCriterion("lookup_from <", value, "lookupFrom");
            return (Criteria) this;
        }

        public Criteria andLookupFromLessThanOrEqualTo(String value) {
            addCriterion("lookup_from <=", value, "lookupFrom");
            return (Criteria) this;
        }

        public Criteria andLookupFromLike(String value) {
            addCriterion("lookup_from like", value, "lookupFrom");
            return (Criteria) this;
        }

        public Criteria andLookupFromNotLike(String value) {
            addCriterion("lookup_from not like", value, "lookupFrom");
            return (Criteria) this;
        }

        public Criteria andLookupFromIn(List<String> values) {
            addCriterion("lookup_from in", values, "lookupFrom");
            return (Criteria) this;
        }

        public Criteria andLookupFromNotIn(List<String> values) {
            addCriterion("lookup_from not in", values, "lookupFrom");
            return (Criteria) this;
        }

        public Criteria andLookupFromBetween(String value1, String value2) {
            addCriterion("lookup_from between", value1, value2, "lookupFrom");
            return (Criteria) this;
        }

        public Criteria andLookupFromNotBetween(String value1, String value2) {
            addCriterion("lookup_from not between", value1, value2, "lookupFrom");
            return (Criteria) this;
        }

        public Criteria andRelTypeIsNull() {
            addCriterion("rel_type is null");
            return (Criteria) this;
        }

        public Criteria andRelTypeIsNotNull() {
            addCriterion("rel_type is not null");
            return (Criteria) this;
        }

        public Criteria andRelTypeEqualTo(String value) {
            addCriterion("rel_type =", value, "relType");
            return (Criteria) this;
        }

        public Criteria andRelTypeNotEqualTo(String value) {
            addCriterion("rel_type <>", value, "relType");
            return (Criteria) this;
        }

        public Criteria andRelTypeGreaterThan(String value) {
            addCriterion("rel_type >", value, "relType");
            return (Criteria) this;
        }

        public Criteria andRelTypeGreaterThanOrEqualTo(String value) {
            addCriterion("rel_type >=", value, "relType");
            return (Criteria) this;
        }

        public Criteria andRelTypeLessThan(String value) {
            addCriterion("rel_type <", value, "relType");
            return (Criteria) this;
        }

        public Criteria andRelTypeLessThanOrEqualTo(String value) {
            addCriterion("rel_type <=", value, "relType");
            return (Criteria) this;
        }

        public Criteria andRelTypeLike(String value) {
            addCriterion("rel_type like", value, "relType");
            return (Criteria) this;
        }

        public Criteria andRelTypeNotLike(String value) {
            addCriterion("rel_type not like", value, "relType");
            return (Criteria) this;
        }

        public Criteria andRelTypeIn(List<String> values) {
            addCriterion("rel_type in", values, "relType");
            return (Criteria) this;
        }

        public Criteria andRelTypeNotIn(List<String> values) {
            addCriterion("rel_type not in", values, "relType");
            return (Criteria) this;
        }

        public Criteria andRelTypeBetween(String value1, String value2) {
            addCriterion("rel_type between", value1, value2, "relType");
            return (Criteria) this;
        }

        public Criteria andRelTypeNotBetween(String value1, String value2) {
            addCriterion("rel_type not between", value1, value2, "relType");
            return (Criteria) this;
        }

        public Criteria andTpmIsNull() {
            addCriterion("tpm is null");
            return (Criteria) this;
        }

        public Criteria andTpmIsNotNull() {
            addCriterion("tpm is not null");
            return (Criteria) this;
        }

        public Criteria andTpmEqualTo(Integer value) {
            addCriterion("tpm =", value, "tpm");
            return (Criteria) this;
        }

        public Criteria andTpmNotEqualTo(Integer value) {
            addCriterion("tpm <>", value, "tpm");
            return (Criteria) this;
        }

        public Criteria andTpmGreaterThan(Integer value) {
            addCriterion("tpm >", value, "tpm");
            return (Criteria) this;
        }

        public Criteria andTpmGreaterThanOrEqualTo(Integer value) {
            addCriterion("tpm >=", value, "tpm");
            return (Criteria) this;
        }

        public Criteria andTpmLessThan(Integer value) {
            addCriterion("tpm <", value, "tpm");
            return (Criteria) this;
        }

        public Criteria andTpmLessThanOrEqualTo(Integer value) {
            addCriterion("tpm <=", value, "tpm");
            return (Criteria) this;
        }

        public Criteria andTpmIn(List<Integer> values) {
            addCriterion("tpm in", values, "tpm");
            return (Criteria) this;
        }

        public Criteria andTpmNotIn(List<Integer> values) {
            addCriterion("tpm not in", values, "tpm");
            return (Criteria) this;
        }

        public Criteria andTpmBetween(Integer value1, Integer value2) {
            addCriterion("tpm between", value1, value2, "tpm");
            return (Criteria) this;
        }

        public Criteria andTpmNotBetween(Integer value1, Integer value2) {
            addCriterion("tpm not between", value1, value2, "tpm");
            return (Criteria) this;
        }

        public Criteria andSlaIsNull() {
            addCriterion("sla is null");
            return (Criteria) this;
        }

        public Criteria andSlaIsNotNull() {
            addCriterion("sla is not null");
            return (Criteria) this;
        }

        public Criteria andSlaEqualTo(Integer value) {
            addCriterion("sla =", value, "sla");
            return (Criteria) this;
        }

        public Criteria andSlaNotEqualTo(Integer value) {
            addCriterion("sla <>", value, "sla");
            return (Criteria) this;
        }

        public Criteria andSlaGreaterThan(Integer value) {
            addCriterion("sla >", value, "sla");
            return (Criteria) this;
        }

        public Criteria andSlaGreaterThanOrEqualTo(Integer value) {
            addCriterion("sla >=", value, "sla");
            return (Criteria) this;
        }

        public Criteria andSlaLessThan(Integer value) {
            addCriterion("sla <", value, "sla");
            return (Criteria) this;
        }

        public Criteria andSlaLessThanOrEqualTo(Integer value) {
            addCriterion("sla <=", value, "sla");
            return (Criteria) this;
        }

        public Criteria andSlaIn(List<Integer> values) {
            addCriterion("sla in", values, "sla");
            return (Criteria) this;
        }

        public Criteria andSlaNotIn(List<Integer> values) {
            addCriterion("sla not in", values, "sla");
            return (Criteria) this;
        }

        public Criteria andSlaBetween(Integer value1, Integer value2) {
            addCriterion("sla between", value1, value2, "sla");
            return (Criteria) this;
        }

        public Criteria andSlaNotBetween(Integer value1, Integer value2) {
            addCriterion("sla not between", value1, value2, "sla");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Long value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Long value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Long value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Long value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Long value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Long value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Long> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Long> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Long value1, Long value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Long value1, Long value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andFlowLimitIsNull() {
            addCriterion("flow_limit is null");
            return (Criteria) this;
        }

        public Criteria andFlowLimitIsNotNull() {
            addCriterion("flow_limit is not null");
            return (Criteria) this;
        }

        public Criteria andFlowLimitEqualTo(BigDecimal value) {
            addCriterion("flow_limit =", value, "flowLimit");
            return (Criteria) this;
        }

        public Criteria andFlowLimitNotEqualTo(BigDecimal value) {
            addCriterion("flow_limit <>", value, "flowLimit");
            return (Criteria) this;
        }

        public Criteria andFlowLimitGreaterThan(BigDecimal value) {
            addCriterion("flow_limit >", value, "flowLimit");
            return (Criteria) this;
        }

        public Criteria andFlowLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("flow_limit >=", value, "flowLimit");
            return (Criteria) this;
        }

        public Criteria andFlowLimitLessThan(BigDecimal value) {
            addCriterion("flow_limit <", value, "flowLimit");
            return (Criteria) this;
        }

        public Criteria andFlowLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("flow_limit <=", value, "flowLimit");
            return (Criteria) this;
        }

        public Criteria andFlowLimitIn(List<BigDecimal> values) {
            addCriterion("flow_limit in", values, "flowLimit");
            return (Criteria) this;
        }

        public Criteria andFlowLimitNotIn(List<BigDecimal> values) {
            addCriterion("flow_limit not in", values, "flowLimit");
            return (Criteria) this;
        }

        public Criteria andFlowLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("flow_limit between", value1, value2, "flowLimit");
            return (Criteria) this;
        }

        public Criteria andFlowLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("flow_limit not between", value1, value2, "flowLimit");
            return (Criteria) this;
        }

        public Criteria andLookupFlowLimitIsNull() {
            addCriterion("lookup_flow_limit is null");
            return (Criteria) this;
        }

        public Criteria andLookupFlowLimitIsNotNull() {
            addCriterion("lookup_flow_limit is not null");
            return (Criteria) this;
        }

        public Criteria andLookupFlowLimitEqualTo(BigDecimal value) {
            addCriterion("lookup_flow_limit =", value, "lookupFlowLimit");
            return (Criteria) this;
        }

        public Criteria andLookupFlowLimitNotEqualTo(BigDecimal value) {
            addCriterion("lookup_flow_limit <>", value, "lookupFlowLimit");
            return (Criteria) this;
        }

        public Criteria andLookupFlowLimitGreaterThan(BigDecimal value) {
            addCriterion("lookup_flow_limit >", value, "lookupFlowLimit");
            return (Criteria) this;
        }

        public Criteria andLookupFlowLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("lookup_flow_limit >=", value, "lookupFlowLimit");
            return (Criteria) this;
        }

        public Criteria andLookupFlowLimitLessThan(BigDecimal value) {
            addCriterion("lookup_flow_limit <", value, "lookupFlowLimit");
            return (Criteria) this;
        }

        public Criteria andLookupFlowLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("lookup_flow_limit <=", value, "lookupFlowLimit");
            return (Criteria) this;
        }

        public Criteria andLookupFlowLimitIn(List<BigDecimal> values) {
            addCriterion("lookup_flow_limit in", values, "lookupFlowLimit");
            return (Criteria) this;
        }

        public Criteria andLookupFlowLimitNotIn(List<BigDecimal> values) {
            addCriterion("lookup_flow_limit not in", values, "lookupFlowLimit");
            return (Criteria) this;
        }

        public Criteria andLookupFlowLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("lookup_flow_limit between", value1, value2, "lookupFlowLimit");
            return (Criteria) this;
        }

        public Criteria andLookupFlowLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("lookup_flow_limit not between", value1, value2, "lookupFlowLimit");
            return (Criteria) this;
        }

        public Criteria andLookupSlaIsNull() {
            addCriterion("lookup_sla is null");
            return (Criteria) this;
        }

        public Criteria andLookupSlaIsNotNull() {
            addCriterion("lookup_sla is not null");
            return (Criteria) this;
        }

        public Criteria andLookupSlaEqualTo(Integer value) {
            addCriterion("lookup_sla =", value, "lookupSla");
            return (Criteria) this;
        }

        public Criteria andLookupSlaNotEqualTo(Integer value) {
            addCriterion("lookup_sla <>", value, "lookupSla");
            return (Criteria) this;
        }

        public Criteria andLookupSlaGreaterThan(Integer value) {
            addCriterion("lookup_sla >", value, "lookupSla");
            return (Criteria) this;
        }

        public Criteria andLookupSlaGreaterThanOrEqualTo(Integer value) {
            addCriterion("lookup_sla >=", value, "lookupSla");
            return (Criteria) this;
        }

        public Criteria andLookupSlaLessThan(Integer value) {
            addCriterion("lookup_sla <", value, "lookupSla");
            return (Criteria) this;
        }

        public Criteria andLookupSlaLessThanOrEqualTo(Integer value) {
            addCriterion("lookup_sla <=", value, "lookupSla");
            return (Criteria) this;
        }

        public Criteria andLookupSlaIn(List<Integer> values) {
            addCriterion("lookup_sla in", values, "lookupSla");
            return (Criteria) this;
        }

        public Criteria andLookupSlaNotIn(List<Integer> values) {
            addCriterion("lookup_sla not in", values, "lookupSla");
            return (Criteria) this;
        }

        public Criteria andLookupSlaBetween(Integer value1, Integer value2) {
            addCriterion("lookup_sla between", value1, value2, "lookupSla");
            return (Criteria) this;
        }

        public Criteria andLookupSlaNotBetween(Integer value1, Integer value2) {
            addCriterion("lookup_sla not between", value1, value2, "lookupSla");
            return (Criteria) this;
        }

        public Criteria andFieldsIsNull() {
            addCriterion("`fields` is null");
            return (Criteria) this;
        }

        public Criteria andFieldsIsNotNull() {
            addCriterion("`fields` is not null");
            return (Criteria) this;
        }

        public Criteria andFieldsEqualTo(String value) {
            addCriterion("`fields` =", value, "fields");
            return (Criteria) this;
        }

        public Criteria andFieldsNotEqualTo(String value) {
            addCriterion("`fields` <>", value, "fields");
            return (Criteria) this;
        }

        public Criteria andFieldsGreaterThan(String value) {
            addCriterion("`fields` >", value, "fields");
            return (Criteria) this;
        }

        public Criteria andFieldsGreaterThanOrEqualTo(String value) {
            addCriterion("`fields` >=", value, "fields");
            return (Criteria) this;
        }

        public Criteria andFieldsLessThan(String value) {
            addCriterion("`fields` <", value, "fields");
            return (Criteria) this;
        }

        public Criteria andFieldsLessThanOrEqualTo(String value) {
            addCriterion("`fields` <=", value, "fields");
            return (Criteria) this;
        }

        public Criteria andFieldsLike(String value) {
            addCriterion("`fields` like", value, "fields");
            return (Criteria) this;
        }

        public Criteria andFieldsNotLike(String value) {
            addCriterion("`fields` not like", value, "fields");
            return (Criteria) this;
        }

        public Criteria andFieldsIn(List<String> values) {
            addCriterion("`fields` in", values, "fields");
            return (Criteria) this;
        }

        public Criteria andFieldsNotIn(List<String> values) {
            addCriterion("`fields` not in", values, "fields");
            return (Criteria) this;
        }

        public Criteria andFieldsBetween(String value1, String value2) {
            addCriterion("`fields` between", value1, value2, "fields");
            return (Criteria) this;
        }

        public Criteria andFieldsNotBetween(String value1, String value2) {
            addCriterion("`fields` not between", value1, value2, "fields");
            return (Criteria) this;
        }

        public Criteria andRefFieldsIsNull() {
            addCriterion("ref_fields is null");
            return (Criteria) this;
        }

        public Criteria andRefFieldsIsNotNull() {
            addCriterion("ref_fields is not null");
            return (Criteria) this;
        }

        public Criteria andRefFieldsEqualTo(String value) {
            addCriterion("ref_fields =", value, "refFields");
            return (Criteria) this;
        }

        public Criteria andRefFieldsNotEqualTo(String value) {
            addCriterion("ref_fields <>", value, "refFields");
            return (Criteria) this;
        }

        public Criteria andRefFieldsGreaterThan(String value) {
            addCriterion("ref_fields >", value, "refFields");
            return (Criteria) this;
        }

        public Criteria andRefFieldsGreaterThanOrEqualTo(String value) {
            addCriterion("ref_fields >=", value, "refFields");
            return (Criteria) this;
        }

        public Criteria andRefFieldsLessThan(String value) {
            addCriterion("ref_fields <", value, "refFields");
            return (Criteria) this;
        }

        public Criteria andRefFieldsLessThanOrEqualTo(String value) {
            addCriterion("ref_fields <=", value, "refFields");
            return (Criteria) this;
        }

        public Criteria andRefFieldsLike(String value) {
            addCriterion("ref_fields like", value, "refFields");
            return (Criteria) this;
        }

        public Criteria andRefFieldsNotLike(String value) {
            addCriterion("ref_fields not like", value, "refFields");
            return (Criteria) this;
        }

        public Criteria andRefFieldsIn(List<String> values) {
            addCriterion("ref_fields in", values, "refFields");
            return (Criteria) this;
        }

        public Criteria andRefFieldsNotIn(List<String> values) {
            addCriterion("ref_fields not in", values, "refFields");
            return (Criteria) this;
        }

        public Criteria andRefFieldsBetween(String value1, String value2) {
            addCriterion("ref_fields between", value1, value2, "refFields");
            return (Criteria) this;
        }

        public Criteria andRefFieldsNotBetween(String value1, String value2) {
            addCriterion("ref_fields not between", value1, value2, "refFields");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}