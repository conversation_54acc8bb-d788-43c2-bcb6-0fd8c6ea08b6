package com.aliyun.wormhole.qanat.dal.domain;

import java.io.Serializable;
import java.util.Date;

public class DsFieldInfo implements Serializable {
    private Long id;

    private Date gmtCreate;

    private Date gmtModified;

    private String fieldName;

    private String fieldUniqueName;

    private String dsName;

    private String dsUniqueName;

    private String dbName;

    private String fieldType;

    private String fieldDesc;

    private Byte isPk;

    private String defaultValue;

    private Byte isNotNull;

    private Long isDeleted;

    private String createEmpid;

    private String modifyEmpid;

    private String sysType;

    private String tenantId;

    private Integer isFk;

    private String fkObjectType;

    private String extInfo;

    private String dataType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldUniqueName() {
        return fieldUniqueName;
    }

    public void setFieldUniqueName(String fieldUniqueName) {
        this.fieldUniqueName = fieldUniqueName;
    }

    public String getDsName() {
        return dsName;
    }

    public void setDsName(String dsName) {
        this.dsName = dsName;
    }

    public String getDsUniqueName() {
        return dsUniqueName;
    }

    public void setDsUniqueName(String dsUniqueName) {
        this.dsUniqueName = dsUniqueName;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public String getFieldDesc() {
        return fieldDesc;
    }

    public void setFieldDesc(String fieldDesc) {
        this.fieldDesc = fieldDesc;
    }

    public Byte getIsPk() {
        return isPk;
    }

    public void setIsPk(Byte isPk) {
        this.isPk = isPk;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public Byte getIsNotNull() {
        return isNotNull;
    }

    public void setIsNotNull(Byte isNotNull) {
        this.isNotNull = isNotNull;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getCreateEmpid() {
        return createEmpid;
    }

    public void setCreateEmpid(String createEmpid) {
        this.createEmpid = createEmpid;
    }

    public String getModifyEmpid() {
        return modifyEmpid;
    }

    public void setModifyEmpid(String modifyEmpid) {
        this.modifyEmpid = modifyEmpid;
    }

    public String getSysType() {
        return sysType;
    }

    public void setSysType(String sysType) {
        this.sysType = sysType;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getIsFk() {
        return isFk;
    }

    public void setIsFk(Integer isFk) {
        this.isFk = isFk;
    }

    public String getFkObjectType() {
        return fkObjectType;
    }

    public void setFkObjectType(String fkObjectType) {
        this.fkObjectType = fkObjectType;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
}