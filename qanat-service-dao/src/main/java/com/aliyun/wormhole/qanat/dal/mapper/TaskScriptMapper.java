package com.aliyun.wormhole.qanat.dal.mapper;

import com.aliyun.wormhole.qanat.dal.domain.TaskScript;
import com.aliyun.wormhole.qanat.dal.domain.TaskScriptExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface TaskScriptMapper {
    int countByExample(TaskScriptExample example);

    int deleteByExample(TaskScriptExample example);

    List<TaskScript> selectByExampleWithBLOBs(TaskScriptExample example);

    List<TaskScript> selectByExample(TaskScriptExample example);

    int updateByExampleSelective(@Param("record") TaskScript record, @Param("example") TaskScriptExample example);

    int updateByExampleWithBLOBs(@Param("record") TaskScript record, @Param("example") TaskScriptExample example);

    int updateByExample(@Param("record") TaskScript record, @Param("example") TaskScriptExample example);

    List<TaskScript> selectByExampleWithBLOBsAndPage(TaskScriptExample example, RowBounds rowBound);

    List<TaskScript> selectByExampleAndPage(TaskScriptExample example, RowBounds rowBound);

    int deleteByPrimaryKey(Long id);

    int insert(TaskScript record);

    int insertSelective(TaskScript record);

    TaskScript selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TaskScript record);

    int updateByPrimaryKeyWithBLOBs(TaskScript record);

    int updateByPrimaryKey(TaskScript record);
}